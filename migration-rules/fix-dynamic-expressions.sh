#!/bin/bash

# Script to fix problematic dynamic expressions that conflict with strongly-typed views
# Usage: ./fix-dynamic-expressions.sh [--dry-run]

DRY_RUN=false
if [[ "$1" == "--dry-run" ]]; then
    DRY_RUN=true
    echo "=== DRY RUN MODE - No files will be modified ==="
    echo
fi

echo "=== Fixing dynamic expressions in strongly-typed views ==="
echo

fixed_count=0
files_processed=0

# Find files that have both @inherits BaseViewPage<T> and problematic dynamic expressions
while IFS= read -r -d '' file; do
    if grep -q "@inherits.*BaseViewPage<" "$file"; then
        ((files_processed++))
        
        # Check for problematic patterns
        has_issues=false
        
        # Pattern 1: System.Func<dynamic, object> in LabelFor, DisplayFor, etc.
        if grep -q "System\.Func<dynamic, object>" "$file"; then
            has_issues=true
            echo "Found System.Func<dynamic, object> in: $file"
        fi
        
        # Pattern 2: Cast<dynamic>() expressions
        if grep -q "\.Cast<dynamic>()" "$file"; then
            has_issues=true
            echo "Found .Cast<dynamic>() in: $file"
        fi
        
        # Pattern 3: (System.Func<dynamic, bool>) casting
        if grep -q "System\.Func<dynamic, bool>" "$file"; then
            has_issues=true
            echo "Found System.Func<dynamic, bool> in: $file"
        fi
        
        if [[ "$has_issues" == "true" ]]; then
            if [[ "$DRY_RUN" == "true" ]]; then
                echo "  Would fix dynamic expressions in this file"
                ((fixed_count++))
            else
                # Create backup
                cp "$file" "${file}.backup"
                temp_file=$(mktemp)
                
                # Fix the patterns
                sed \
                    -e 's/(System\.Func<dynamic, object>)(\([^)]*\))/\1/g' \
                    -e 's/(System\.Func<dynamic, bool>)(\([^)]*\))/\1/g' \
                    -e 's/\.Cast<dynamic>()\.Where((System\.Func<dynamic, bool>)(\([^)]*\)))/.Where(\1)/g' \
                    -e 's/\.Cast<dynamic>()\.Where(\([^)]*\))/.Where(\1)/g' \
                    -e 's/\.Cast<dynamic>()\.Any((\([^)]*\)))/.Any(\1)/g' \
                    -e 's/\.Cast<dynamic>()\.Any()/.Any()/g' \
                    -e 's/\.Cast<dynamic>()\.Select((System\.Func<dynamic, object>)(\([^)]*\)))/.Select(\1)/g' \
                    -e 's/\.OrderBy((System\.Func<[^,>]*,\s*[^>]*>))(\([^)]*\))/.OrderBy(\2)/g' \
                    -e 's/\.ThenBy((System\.Func<[^,>]*,\s*[^>]*>))(\([^)]*\))/.ThenBy(\2)/g' \
                    -e 's/\.GroupBy((Func<[^,>]*,\s*[^>]*>))(\([^)]*\))/.GroupBy(\2)/g' \
                    "$file" > "$temp_file"
                
                # Verify the changes didn't break the file
                if [[ -s "$temp_file" ]]; then
                    mv "$temp_file" "$file"
                    echo "  ✅ Fixed dynamic expressions"
                    ((fixed_count++))
                else
                    # Restore backup if something went wrong
                    mv "${file}.backup" "$file"
                    rm -f "$temp_file"
                    echo "  ⚠️  Skipped: sed processing failed"
                fi
                
                # Clean up backup if successful
                if [[ -f "${file}.backup" && -f "$file" ]]; then
                    rm "${file}.backup"
                fi
            fi
            echo
        fi
    fi
done < <(find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum30" -name "*.cshtml" -type f -print0)

echo "=== Summary ==="
echo "Files processed: $files_processed"
if [[ "$DRY_RUN" == "true" ]]; then
    echo "Would fix: $fixed_count files"
    echo
    echo "Run without --dry-run to apply changes"
else
    echo "Fixed: $fixed_count files"
    echo
    if [[ $fixed_count -gt 0 ]]; then
        echo "✅ Fixed dynamic expressions in strongly-typed views!"
        echo "Next step: Run 'dotnet build' to verify compilation issues are resolved"
    else
        echo "ℹ️  No problematic dynamic expressions found"
    fi
fi