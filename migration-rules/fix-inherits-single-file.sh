#!/bin/bash

# Safe script to fix @inherits for a single file
# Usage: ./fix-inherits-single-file.sh <file_path>

if [ $# -ne 1 ]; then
    echo "Usage: $0 <file_path>"
    echo "Example: $0 /path/to/file.cshtml"
    exit 1
fi

file_path="$1"

if [ ! -f "$file_path" ]; then
    echo "Error: File does not exist: $file_path"
    exit 1
fi

# Check if file has @model directive
if ! grep -q "^@model " "$file_path"; then
    echo "File does not have @model directive: $file_path"
    exit 0
fi

# Check if it already has proper @inherits
if grep -q "@inherits.*BaseViewPage<.*>" "$file_path"; then
    echo "File already has proper @inherits: $file_path"
    exit 0
fi

# Extract the model type
model_type=$(grep "^@model " "$file_path" | head -n1 | sed 's/^@model //' | sed 's/^[ \t]*//' | sed 's/[ \t]*$//')

if [ -z "$model_type" ]; then
    echo "Could not extract model type from: $file_path"
    exit 1
fi

echo "Processing: $file_path"
echo "Model type: $model_type"

# Create backup
cp "$file_path" "$file_path.backup"

# Use awk to add @inherits after @model line
awk -v model_type="$model_type" '
/^@model / {
    print $0
    print "@inherits Cerebrum30.BaseViewPage<" model_type ">"
    next
}
{ print }
' "$file_path.backup" > "$file_path"

# Verify the change was made correctly
if grep -q "@inherits.*BaseViewPage<.*>" "$file_path"; then
    echo "Successfully added @inherits declaration"
    rm "$file_path.backup"
else
    echo "Failed to add @inherits declaration, restoring backup"
    mv "$file_path.backup" "$file_path"
    exit 1
fi