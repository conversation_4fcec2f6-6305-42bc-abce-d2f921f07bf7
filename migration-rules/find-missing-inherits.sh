#!/bin/bash

# Script to find views with @model but missing strongly-typed @inherits BaseViewPage<T>
# Usage: ./find-missing-inherits.sh

echo "=== Views with @model but missing strongly-typed @inherits BaseViewPage<T> ==="
echo

# Find all .cshtml files in the target-source directory
find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source" -name "*.cshtml" -type f | while read -r file; do
    # Check if file has @model directive
    if grep -q "^@model " "$file"; then
        # Check if file has strongly-typed @inherits BaseViewPage<
        if ! grep -q "@inherits.*BaseViewPage<" "$file"; then
            # Extract the model type from @model directive
            model_line=$(grep "^@model " "$file" | head -1)
            
            echo "File: $file"
            echo "  Current: $model_line"
            
            # Extract model type and suggest the @inherits line
            model_type=$(echo "$model_line" | sed 's/@model //' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            echo "  Missing: @inherits Cerebrum30.BaseViewPage<$model_type>"
            echo
        fi
    fi
done

echo "=== Summary ==="
echo

# Count totals
total_model_files=$(find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source" -name "*.cshtml" -type f -exec grep -l "^@model " {} \; | wc -l)
fixed_files=$(find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source" -name "*.cshtml" -type f -exec grep -l "@inherits.*BaseViewPage<" {} \; | wc -l)
missing_files=$(find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source" -name "*.cshtml" -type f | while read -r file; do
    if grep -q "^@model " "$file" && ! grep -q "@inherits.*BaseViewPage<" "$file"; then
        echo "$file"
    fi
done | wc -l)

echo "Total views with @model: $total_model_files"
echo "Views with strongly-typed @inherits: $fixed_files"
echo "Views missing strongly-typed @inherits: $missing_files"

if [ "$missing_files" -gt 0 ]; then
    echo
    echo "❌ Found $missing_files views that need fixing!"
    echo "Run this script to see which files need the @inherits directive added."
else
    echo
    echo "✅ All views with @model have strongly-typed @inherits directives!"
fi