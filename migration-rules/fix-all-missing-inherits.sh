#!/bin/bash

# Script to add @inherits BaseViewPage<TModel> declarations to all files missing them
# This processes the output from identify-missing-inherits-safe.sh

echo "Adding @inherits declarations to all files with @model but missing @inherits..."
echo

# Get list of files that need fixing
files_needing_fixes=$(find /home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum30 -name "*.cshtml" -type f | while read -r file; do
    # Check if file has @model directive and is missing proper @inherits
    if grep -q "^@model " "$file" && ! grep -q "@inherits.*BaseViewPage<.*>" "$file"; then
        echo "$file"
    fi
done)

total_files=0
fixed_files=0
failed_files=0

for file_path in $files_needing_fixes; do
    if [ ! -f "$file_path" ]; then
        continue
    fi
    
    total_files=$((total_files + 1))
    
    # Extract the model type
    model_type=$(grep "^@model " "$file_path" | head -n1 | sed 's/^@model //' | sed 's/^[ \t]*//' | sed 's/[ \t]*$//')
    
    if [ -z "$model_type" ]; then
        echo "Failed to extract model type from: $file_path"
        failed_files=$((failed_files + 1))
        continue
    fi
    
    echo "Processing: $file_path"
    echo "  Model: $model_type"
    
    # Create backup
    cp "$file_path" "$file_path.backup"
    
    # Use awk to add @inherits after @model line
    awk -v model_type="$model_type" '
    /^@model / {
        print $0
        print "@inherits Cerebrum30.BaseViewPage<" model_type ">"
        next
    }
    { print }
    ' "$file_path.backup" > "$file_path"
    
    # Verify the change was made correctly
    if grep -q "@inherits.*BaseViewPage<.*>" "$file_path"; then
        echo "  ✓ Successfully added @inherits declaration"
        rm "$file_path.backup"
        fixed_files=$((fixed_files + 1))
    else
        echo "  ✗ Failed to add @inherits declaration, restoring backup"
        mv "$file_path.backup" "$file_path"
        failed_files=$((failed_files + 1))
    fi
    echo
done

echo "=== Summary ==="
echo "Total files processed: $total_files"
echo "Successfully fixed: $fixed_files"
echo "Failed: $failed_files"
echo

if [ $failed_files -eq 0 ]; then
    echo "✓ All files were successfully processed!"
else
    echo "⚠ Some files failed to process. Check the output above for details."
fi