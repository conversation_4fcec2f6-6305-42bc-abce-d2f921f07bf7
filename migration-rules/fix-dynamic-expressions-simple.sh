#!/bin/bash

# Simple script to fix the most common dynamic expression issues
# Usage: ./fix-dynamic-expressions-simple.sh [--dry-run]

DRY_RUN=false
if [[ "$1" == "--dry-run" ]]; then
    DRY_RUN=true
    echo "=== DRY RUN MODE - No files will be modified ==="
    echo
fi

echo "=== Fixing common dynamic expressions in strongly-typed views ==="
echo

fixed_count=0
files_processed=0

# Find files that have both @inherits BaseViewPage<T> and problematic dynamic expressions
find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum30" -name "*.cshtml" -type f | while read file; do
    if grep -q "@inherits.*BaseViewPage<" "$file"; then
        ((files_processed++))
        
        # Check for the most common problematic patterns
        needs_fix=false
        
        # Pattern 1: System.Func<dynamic, object> in LabelFor, DisplayFor, etc.
        if grep -q "System\.Func<dynamic, object>" "$file"; then
            needs_fix=true
        fi
        
        # Pattern 2: (object) casts in HtmlHelpers
        if grep -q "=> (object)" "$file"; then
            needs_fix=true
        fi
        
        if [[ "$needs_fix" == "true" ]]; then
            echo "Processing: $file"
            
            if [[ "$DRY_RUN" == "true" ]]; then
                echo "  Would fix dynamic expressions in this file"
                ((fixed_count++))
            else
                # Create backup
                cp "$file" "${file}.backup"
                
                # Fix the patterns using simple sed
                # Fix Pattern 1: Remove System.Func<dynamic, object> casts
                sed -i 's/(System\.Func<dynamic, object>)(\([^)]*\))/\1/g' "$file"
                
                # Fix Pattern 2: Remove (object) casts in lambda expressions
                sed -i 's/=> (object)/=>/g' "$file"
                
                # Verify the file is still valid
                if [[ -s "$file" ]]; then
                    echo "  ✅ Fixed dynamic expressions"
                    ((fixed_count++))
                    # Remove backup if successful
                    rm "${file}.backup"
                else
                    # Restore backup if something went wrong
                    mv "${file}.backup" "$file"
                    echo "  ⚠️  Skipped: processing failed"
                fi
            fi
            echo
        fi
    fi
done

echo "=== Summary ==="
echo "Files processed: $files_processed"
if [[ "$DRY_RUN" == "true" ]]; then
    echo "Would fix: $fixed_count files"
    echo "Run without --dry-run to apply changes"
else
    echo "Fixed: $fixed_count files"
    if [[ $fixed_count -gt 0 ]]; then
        echo "✅ Fixed common dynamic expressions!"
        echo "Next step: Run 'dotnet build' to verify compilation issues are resolved"
    else
        echo "ℹ️  No problematic dynamic expressions found"
    fi
fi