#!/bin/bash

# Script to find views with @model but missing strongly-typed @inherits BaseViewPage<T>
# Focused on Cerebrum30 main application only
# Usage: ./find-missing-inherits-cerebrum30.sh

echo "=== Cerebrum30 Views with @model but missing strongly-typed @inherits BaseViewPage<T> ==="
echo

# Find all .cshtml files in the Cerebrum30 directory only
find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum30" -name "*.cshtml" -type f | while read -r file; do
    # Check if file has @model directive
    if grep -q "^@model " "$file"; then
        # Check if file has strongly-typed @inherits BaseViewPage<
        if ! grep -q "@inherits.*BaseViewPage<" "$file"; then
            # Extract the model type from @model directive
            model_line=$(grep "^@model " "$file" | head -1)
            
            echo "File: $file"
            echo "  Current: $model_line"
            
            # Extract model type and suggest the @inherits line
            model_type=$(echo "$model_line" | sed 's/@model //' | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            echo "  Missing: @inherits Cerebrum30.BaseViewPage<$model_type>"
            echo
        fi
    fi
done

echo "=== Summary for Cerebrum30 ==="
echo

# Count totals for Cerebrum30 only
total_model_files=$(find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum30" -name "*.cshtml" -type f -exec grep -l "^@model " {} \; | wc -l)
fixed_files=$(find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum30" -name "*.cshtml" -type f -exec grep -l "@inherits.*BaseViewPage<" {} \; | wc -l)
missing_files=$(find "/home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum30" -name "*.cshtml" -type f | while read -r file; do
    if grep -q "^@model " "$file" && ! grep -q "@inherits.*BaseViewPage<" "$file"; then
        echo "$file"
    fi
done | wc -l)

echo "Total Cerebrum30 views with @model: $total_model_files"
echo "Views with strongly-typed @inherits: $fixed_files"
echo "Views missing strongly-typed @inherits: $missing_files"

if [ "$missing_files" -gt 0 ]; then
    echo
    echo "❌ Found $missing_files Cerebrum30 views that need fixing!"
    echo
    echo "Priority areas based on common usage:"
    echo "1. Schedule area views (appointments, daysheet)"
    echo "2. Shared partial views and editor templates"
    echo "3. Admin area views"
    echo "4. Other functional areas"
else
    echo
    echo "✅ All Cerebrum30 views with @model have strongly-typed @inherits directives!"
fi