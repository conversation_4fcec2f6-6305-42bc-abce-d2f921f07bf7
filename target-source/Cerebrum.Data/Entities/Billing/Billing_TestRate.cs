﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Test Rate
    /// </summary>
    [TrackChanges]
    [Table("Billing_TestRate")]
    public class Billing_TestRate
    {
        [Key]
        public int id { get; set; }
        public int testId { get; set; }
        public int? fee { get; set; }
        [StringLength(50)]
        public string name { get; set; }
        [StringLength(255)]
        public string note { get; set; }
        public int billingTypeId { get; set; }
        [StringLength(10)]
        public string claimCode { get; set; }
        public Int16 numberOfServices { get; set; }
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public bool IHFbilling { get; set; } = false;
        public int billToPermission { get; set; } = 0; //permission id
        public DateTime createdDate { get; set; } = DateTime.Now;        

        //[ForeignKey("testId")]
        //public virtual Test test { get; set; }
    }
}