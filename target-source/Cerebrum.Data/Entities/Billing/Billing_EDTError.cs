﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Billing EDT error
    /// </summary>
    [TrackChanges]
    [Table("Billing_EDTError")]
    public class Billing_EDTError
    {
        [Key]
        public int id { get; set; }
        [StringLength(256)]
        public string fileName { get; set; }

        public int officeId { get; set; }
        [StringLength(20)]
        public string edtGroupId { get; set; }
        public int externalDoctorId { get; set; }
        public int status { get; set; }     //1  --- fixed;   0 --- not fixed
        public DateTime DateDownloaded { get; set; }
    }
}
