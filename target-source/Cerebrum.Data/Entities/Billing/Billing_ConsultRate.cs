﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Consult Rate
    /// </summary>
    [TrackChanges]
    [Table("Billing_ConsultRate")]
    public class Billing_ConsultRate
    {
        [Key]
        public int id { get; set; }
        public int fee { get; set; }
        public int specialty { get; set; }
        [StringLength(100)]
        public string name { get; set; }
        [StringLength(128)]
        public string note { get; set; }
        public int billingTypeId { get; set; }
        [StringLength(10)]
        public string claimCode { get; set; }
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
    }
}