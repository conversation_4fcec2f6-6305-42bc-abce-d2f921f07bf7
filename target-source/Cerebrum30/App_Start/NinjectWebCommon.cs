[assembly: WebActivatorEx.PreApplicationStartMethod(
    typeof(Cerebrum30.App_Start.NinjectWebCommon),
    "Start"
)]
[assembly: WebActivatorEx.ApplicationShutdownMethodAttribute(
    typeof(Cerebrum30.App_Start.NinjectWebCommon),
    "Stop"
)]

namespace Cerebrum30.App_Start
{
    using System;
    using System.Net.Http;
    using System.Web;
    using System.Web.Http;
    using System.Web.Mvc;
    using Areas.Admin.DataAccess;
    using Areas.AdminUser.DataAccess;
    using Areas.ExternalDocument.DataAccess;
    using Areas.Labs.DataAccess;
    using Areas.Measurements.DataAccess;
    using Areas.Medications.DataAccess;
    using Areas.VirtualVisit.DataAccess;
    using Areas.VP.DataAccess;
    using AwareMD.Eforms.Service;
    using Cerebrum.BLL;
    using Cerebrum.BLL.Admin;
    using Cerebrum.BLL.AdminUao;
    using Cerebrum.BLL.AdminUser;
    using Cerebrum.BLL.Cohort;
    using Cerebrum.BLL.Common;
    using Cerebrum.BLL.ConnectingOntario;
    using Cerebrum.BLL.Consultation;
    using Cerebrum.BLL.Documents;
    using Cerebrum.BLL.Econsult;
    using Cerebrum.BLL.Factory;
    using Cerebrum.BLL.HCV;
    using Cerebrum.BLL.HL7;
    using Cerebrum.BLL.Inventory;
    using Cerebrum.BLL.OLIS;
    using Cerebrum.BLL.Patient;
    using Cerebrum.BLL.Practice;
    using Cerebrum.BLL.RadAdapter;
    using Cerebrum.BLL.RadDicom;
    using Cerebrum.BLL.ReportLetterQueue;
    using Cerebrum.BLL.Requisition;
    using Cerebrum.BLL.Schedule;
    using Cerebrum.BLL.Schedule.Util;
    using Cerebrum.BLL.Study;
    using Cerebrum.BLL.Timesheet;
    using Cerebrum.BLL.User;
    using Cerebrum.BLL.Utility;
    using Cerebrum.BLL.VP;
    using Cerebrum.DHDR.Services;
    using Cerebrum.Doctors;
    using Cerebrum.ExternalDocument;
    using Cerebrum.Labs.LabScc;
    using Cerebrum.Labs.OLIS.services;
    using Cerebrum.Labs.OLISAPILayer;
    using Cerebrum.Reminder;
    using Cerebrum.VirtualVisit;
    using Cerebrum.VirtualVisit.Report;
    using Cerebrum.VirtualVisit.Seedwork;
    using Cerebrum3.DataAccess;
    using Cerebrum3.Infrastructure;
    using Cerebrum30.Areas.Requisition.DataAccess;
    using Cerebrum30.DAL.DataAccessGB;
    using Cerebrum30.DAL.Infrastructure;
    using Cerebrum30.Infrastructure;
    using DAL.DataAccess.Infrastructure;
    using DAL.DataAccess.Repositories;
    using log4net;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Web.Infrastructure.DynamicModuleHelper;
    using Ninject;
    using Ninject.Web.Common;
    using Ninject.Web.Mvc.FilterBindingSyntax;
    using System.Configuration;
    using WebApiContrib.IoC.Ninject;
    using Cerebrum.ContactManager;
    using Cerebrum.BLL.ExternalDoctors;
    using Cerebrum30.Utility;
    using Cerebrum.Labs.Service;

    public static class NinjectWebCommon
    {
        private static readonly Bootstrapper bootstrapper = new Bootstrapper();

        /// <summary>
        /// Starts the application
        /// </summary>
        public static void Start()
        {
            DynamicModuleUtility.RegisterModule(typeof(OnePerRequestHttpModule));
            DynamicModuleUtility.RegisterModule(typeof(NinjectHttpModule));
            bootstrapper.Initialize(CreateKernel);
        }

        /// <summary>
        /// Stops the application.
        /// </summary>
        public static void Stop()
        {
            bootstrapper.ShutDown();
        }

        /// <summary>
        /// Creates the kernel that will manage your application.
        /// </summary>
        /// <returns>The created kernel.</returns>
        private static IKernel CreateKernel()
        {
            var kernel = new StandardKernel();
            try
            {
                kernel.Bind<Func<IKernel>>().ToMethod(ctx => () => new Bootstrapper().Kernel);
                kernel.Bind<IHttpModule>().To<HttpApplicationInitializationHttpModule>();

                RegisterServices(kernel);
                EncapsulateDataService.Initialize();
                GlobalConfiguration.Configuration.DependencyResolver = new NinjectResolver(kernel);

                return kernel;
            }
            catch
            {
                kernel.Dispose();
                throw;
            }
        }

        /// <summary>
        /// Load your modules or register your services here!
        /// </summary>
        /// <param name="kernel">The kernel.</param>
        private static void RegisterServices(IKernel kernel)
        {
            kernel
                .Bind<ILog>()
                .ToMethod(context =>
                    LogManager.GetLogger(context.Request.Target.Member.ReflectedType)
                )
                .InThreadScope();
            kernel.Bind<RepositoryForAdminUser>().To<RepositoryForAdminUser>();
            kernel.Bind<IVPRepository>().To<VPRepository>();
            kernel.Bind<IMeasurementRepository>().To<MeasurementRepository>();
            kernel.Bind<IUserRepository>().To<UserRepository>();

            kernel.Bind<IUnitOfWorkDemographics>().To<UnitOfWorkDemographicsForms>();
            kernel.Bind<IPracticeRepository>().To<PracticeRepository>();
            kernel.Bind<IUnitOfWorkPatientRepository>().To<UnitOfWorkPatient>();

            kernel
                .Bind<Cerebrum3.Infrastructure.IAppointmentRepository>()
                .To<Cerebrum3.DataAccess.AppointmentRepository>();
            kernel
                .Bind<Cerebrum3.Infrastructure.IAppointmentTypeRepository>()
                .To<Cerebrum3.DataAccess.AppointmentTypeRepository>();
            kernel.Bind<IUnitOfWorkAppointmentTypeRepository>().To<UnitOfWorkAppointmentType>();

            kernel
                .Bind<Cerebrum3.Infrastructure.IPracticeTestRepository>()
                .To<Cerebrum3.DataAccess.PracticeTestRepository>();
            kernel.Bind<IUnitOfWorkPracticeTestRepository>().To<UnitOfWorkPracticeTestRepository>();
            kernel
                .Bind<Cerebrum3.Infrastructure.ITestRepository>()
                .To<Cerebrum3.DataAccess.TestRepository>();
            kernel
                .Bind<Cerebrum3.Infrastructure.IOfficeRepository>()
                .To<Cerebrum3.DataAccess.OfficeRepository>();

            kernel.Bind<IUnitOfWorkHL7>().To<UnitOfWorkHL7>();
            kernel.Bind<ICodeMappingBLL>().To<CodeMappingBLL>();
            kernel.Bind<IHL7BLL>().To<HL7BLL>();

            kernel
                .Bind<IOLISTestRequestSubCategoryRepository>()
                .To<OLISTestRequestSubCategoryRepository>();
            kernel
                .Bind<IOLISTestRequestCategoryRepository>()
                .To<OLISTestRequestCategoryRepository>();
            kernel.Bind<IOLISTestResultCategoryBLL>().To<OLISTestResultCategoryBLL>();
            kernel.Bind<IOLISTestResultNomenclatureBLL>().To<OLISTestResultNomenclatureBLL>();

            kernel.Bind<IOLISTestReportCategoryRepository>().To<OLISTestReportCategoryRepository>();
            kernel.Bind<IUnitOfWorkOLISTestRepository>().To<UnitOfWorkOLISTestRepository>();
            kernel.Bind<IOLISBuildQuery>().To<OLISBuildQuery>();
            kernel.Bind<IUnitOfWorkAppointmentRepository>().To<UnitOfWorkAppointment>();

            kernel.Bind<IPracticeDoctorRepository>().To<PracticeDoctorRepository>();
            kernel.Bind<IExternalDoctorRepository>().To<ExternalDoctorRepository>();
            kernel.Bind<IDemographicRepository>().To<Cerebrum3.DataAccess.DemographicRepository>();
            kernel
                .Bind<Cerebrum3.Infrastructure.IDemographicsHealthCardRepository>()
                .To<DemographicsHealthCardRepository>();

            kernel
                .Bind<IUnitOfWorkTriageDispositionRepository>()
                .To<UnitOfWorkTriageDispositionRepository>();

            kernel.Bind<IUnitOfWorkHealthCardService>().To<UnitOfWorkHealthCardService>();
            //External Document
            kernel.Bind<IUnitOfWorkExternalDocument>().To<UnitOfWorkExternalDocument>();
            //Requisition
            kernel.Bind<IUnitOfWorkRequisition>().To<UnitOfWorkRequisition>();
            //Billing
            kernel.Bind<IHL7CodingRepository>().To<HL7CodingRepository>();
            kernel.Bind<IHL7LostReportBLL>().To<HL7LostReportBLL>();
            //Schedule
            kernel.Bind<UnitOfWorkWaitListRepository>().To<UnitOfWorkWaitListRepository>();
            //medications
            kernel.Bind<IUnitOfWorkPatientAllergy>().To<UnitOfWorkPatientAllergy>();
            kernel.Bind<IUnitOfWorkPatientMedication>().To<UnitOfWorkPatientMedication>();
            kernel.Bind<IUnitOfWorkMedication>().To<UnitOfWorkMedication>();
            kernel.Bind<IUnitOfWorkMedicationTemp>().To<UnitOfWorkMedicationTemp>();

            kernel.Bind<IUnitOfWorkTestRepository>().To<UnitOfWorkTestRepository>();
            //medications end
            //Inventory
            kernel.Bind<IUnitOfWorkInventory>().To<UnitOfWorkInventory>();
            kernel.Bind<IInventoryBLL>().To<InventoryBLL>();

            //Cds Import & Export
            kernel.Bind<IUnitOfWorkImportCsd>().To<UnitOfWorkImportCsd>();
            kernel.Bind<IUnitOfWorkPatientMerge>().To<UnitOfWorkPatientMerge>();

            kernel
                .Bind<Cerebrum.Labs.Service.iHL7ReportService>()
                .To<Cerebrum.Labs.Service.HL7ReportService>();

            kernel.Bind<IOLISCommunicationLogService>().To<OLISCommunicationLogService>();
            kernel.Bind<IPracticeBLL>().To<PracticeBLL>();
            kernel.Bind<ILabBLL>().To<LabBLL>();
            kernel.Bind<IOLISTestBLL>().To<OLISTestBLL>();
            kernel.Bind<PracticeAdminBLL>().ToSelf();
            kernel.Bind<IUserTimesheetBLL>().To<UserTimesheetBLL>();

            kernel.Bind<IOfficeRoomTypeBLL>().To<OfficeRoomTypeBLL>();
            kernel.Bind<IOfficeRoomBLL>().To<OfficeRoomBLL>();
            kernel.Bind<IOfficeEmailBLL>().To<OfficeEmailBLL>();
            kernel.Bind<IOfficeDailyRegisterBLL>().To<OfficeDailyRegisterBLL>();
            kernel.Bind<IPracticeAppointmentTypeBLL>().To<PracticeAppointmentTypeBLL>();
            kernel.Bind<IRadBLL>().To<RadBLL>();
            kernel.Bind<IRadDicomStudyBLL>().To<RadDicomStudyBLL>();

            kernel.Bind<IConnectingOntarioBLL>().To<ConnectingOntarioBLL>();
            kernel.Bind<IConnectingOntarioConfigBLL>().To<ConnectingOntarioConfigBLL>();
            kernel
                .Bind<IAppointmentHealthCardValidationBLL>()
                .To<AppointmentHealthCardValidationBLL>();
            kernel.Bind<IHealthCardValidationBLL>().To<HealthCardValidationBLL>();
            kernel.Bind<IPatientDemographicBLL>().To<PatientDemographicBLL>();
            kernel.Bind<UserBLL>().ToSelf();
            kernel.Bind<IUserCredentialBLL>().To<UserCredentialBLL>();
            kernel.Bind<IUserPasswordService>().To<UserPasswordService>();
            kernel.Bind<ICommonBLL>().To<CommonBLL>();
            kernel.Bind<ICohortBLL>().To<CohortBLL>();
            kernel.Bind<IExternalDoctorBLL>().To<ExternalDoctorBLL>();
            kernel.Bind<VisitBLL>().ToSelf();
            kernel.Bind<IBookingConfirmationBLL>().To<BookingConfirmationBLL>();
            kernel.Bind<MeasurementBLL>().ToSelf();
            kernel.Bind<MeasurementRepository>().ToSelf();
            kernel.Bind<ExternalDocumentBLL>().ToSelf();
            kernel.Bind<IDocumentsBLL>().To<DocumentsBLL>();
            kernel.Bind<IPDFBuilder>().To<PDFBuilder>();
            kernel.Bind<UOWFormBuilder>().ToSelf();

            kernel.Bind<IAdminReportQueueBLL>().To<AdminReportQueueBLL>();
            // background of why are we using IHttpClientFactory: https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests
            // ticket #12143, #12448
            var hostBuilder = new HostBuilder();
            hostBuilder.ConfigureServices(
                (IServiceCollection services) =>
                {
                    // Profile "download" and "upload" - for any requests that are NOT setting BaseAddress, Timeout and MaxResponseContentBufferSize
                    // Profile "olis-*" - for Web.config setting "OLISAPIurl", see OLISAPILayer.OlisApiWrapper
                    // Profile "dashboard-*" - for Web.config setting "DashboardAPIUrl", see DashboardData
                    if (
                        CerebrumHTTPClient.HttpClientTimeoutSecondsDownload.HasValue
                        || CerebrumHTTPClient.HttpClientTimeoutSecondsUpload.HasValue
                    )
                    {
                        if (CerebrumHTTPClient.HttpClientTimeoutSecondsDownload.HasValue)
                        {
                            services.AddHttpClient(
                                "download",
                                client =>
                                    client.Timeout = CerebrumHTTPClient
                                        .HttpClientTimeoutSecondsDownload
                                        .Value
                            );
                            services.AddHttpClient(
                                "olis-download",
                                client =>
                                    client.Timeout = CerebrumHTTPClient
                                        .HttpClientTimeoutSecondsDownload
                                        .Value
                            );
                            services.AddHttpClient(
                                "dashboard-download",
                                client =>
                                    client.Timeout = CerebrumHTTPClient
                                        .HttpClientTimeoutSecondsDownload
                                        .Value
                            );
                        }
                        if (CerebrumHTTPClient.HttpClientTimeoutSecondsUpload.HasValue)
                        {
                            services.AddHttpClient(
                                "upload",
                                client =>
                                    client.Timeout = CerebrumHTTPClient
                                        .HttpClientTimeoutSecondsUpload
                                        .Value
                            );
                            services.AddHttpClient(
                                "olis-upload",
                                client =>
                                    client.Timeout = CerebrumHTTPClient
                                        .HttpClientTimeoutSecondsUpload
                                        .Value
                            );
                        }
                    }
                    else
                    {
                        services.AddHttpClient("upload");
                        services.AddHttpClient("download");
                        // Notes: olis and dashboard have their own specified BaseAddress, this property cannot be changed once the first request has been made, so they need separated configuration profiles.
                        services.AddHttpClient("olis-upload");
                        services.AddHttpClient("olis-download");
                        services.AddHttpClient("dashboard-download");
                    }
                    services.AddHttpClient("eConsult");
                    services.AddHttpClient("eReferral");
                    services.AddHttpClient("DHDR");
                    Services.WorkSheet.WorkSheetConfig.RegisterServices(services);
                }
            );
            kernel
                .Bind<IHttpClientFactory>()
                .ToConstant(hostBuilder.Build().Services.GetService<IHttpClientFactory>());
            kernel.Bind<IOlisApiWrapper>().To<OlisApiWrapper>();
            kernel.Bind<IPatientChartBLL>().To<PatientChartBLL>();
            kernel.Bind<IReportBLL>().To<ReportBLL>();
            kernel.Bind<IPracticeDoctorBLL>().To<PracticeDoctorBLL>();
            #region Virtual Visit
            kernel
                .Bind<Cerebrum3.Infrastructure.IGenericRepository<Cerebrum.Data.VirtualVisitRoom>>()
                .To<VirtualVisitRoomRepository>();
            kernel
                .Bind<Cerebrum3.Infrastructure.IGenericRepository<Cerebrum.Data.VirtualVisitInviteGuest>>()
                .To<VirtualVisitInvitationRepository>();

            kernel
                .Bind<Cerebrum.VirtualVisit.Data.IUOWVirtualVisit<
                    VirtualVisitRoomRepository,
                    VirtualVisitInvitationRepository,
                    VirtualVisitLogRepository
                >>()
                .To<
                    UOWVirtualVisit<
                        VirtualVisitRoomRepository,
                        VirtualVisitInvitationRepository,
                        VirtualVisitLogRepository
                    >
                >();
            kernel
                .Bind<Cerebrum.VirtualVisit.Seedwork.IVirtualVisitBLL>()
                .To<
                    VirtualVisitBLL<
                        VirtualVisitRoomRepository,
                        VirtualVisitInvitationRepository,
                        VirtualVisitLogRepository
                    >
                >();
            #endregion Virtual Visit
            kernel.Bind<IStudy>().To<Study>();

            kernel.Bind<PatientBLL>().ToSelf();
            kernel.Bind<IPatientBLL>().To<PatientBLL>();
            kernel.Bind<IOfficeBLL>().To<OfficeBLL>();
            kernel.Bind<TriageBLL>().ToSelf();
            kernel.Bind<AppointmentsBLL>().ToSelf();
            kernel.Bind<Scheduler>().ToSelf();

            // Filters binding
            kernel
                .BindFilter<Filters.BaseOnActionFilter>(System.Web.Mvc.FilterScope.Controller, 1)
                .WhenControllerHas<Filters.BaseOnActionFilter>();

            kernel
                .Bind<Areas.Schedule.DataAccess.IUOWDaysheet>()
                .To<Areas.Schedule.DataAccess.UOWDaysheet>();
            kernel
                .Bind<Areas.Schedule.DataAccess.IUOWAppointments>()
                .To<Areas.Schedule.DataAccess.UOWAppointments>();
            kernel
                .Bind<Areas.Schedule.DataAccess.IUOWSchedule>()
                .To<Areas.Schedule.DataAccess.UOWSchedule>();

            kernel
                .Bind<Cerebrum30.Services.WorkSheet.IWorkSheetApiToggle>()
                .To<Cerebrum30.Services.WorkSheet.WorkSheetApiToggleDatabase>();
            kernel.Bind<IApplicationSettingBLL>().To<ApplicationSettingBLL>();

            kernel.Bind<IDHDRService>().To<DHDRService>();
            kernel.Bind<IEFormsService>().To<EFormsService>();
            kernel.Bind<EconsultBLL>().ToSelf();
            kernel.Bind<IVirtualVisitReportBLL>().To<VirtualVisitReportBLL>();
            kernel.Bind<IAdminUaoBLL>().To<AdminUaoBLL>();
            kernel.Bind<IReminderBLL>().To<ReminderBLL>();
            kernel.Bind<ISSRSReportRepository>().To<SSRSReportRepository>();

            kernel.Bind<TimePeriodRelationFactory>().ToSelf().InSingletonScope();
            kernel.Bind<ITimePeriodRelationService>().To<TimePeriodRelationService>();
            kernel.Bind<iStaffScheduleBLL>().To<StaffScheduleBLL>();

            kernel.Bind<DaysheetBLL>().ToSelf();
            kernel
                .Bind<IAppointmentPriorityConfigurationBLL>()
                .To<AppointmentPriorityConfigurationBLL>();
            kernel.Bind<IAppointmentPriorityRepository>().To<AppointmentPriorityRepository>();
            kernel
                .Bind<IPracticeDefaultAppointmentPriorityRepository>()
                .To<PracticeDefaultAppointmentPriorityRepository>();
            kernel.Bind<IRequisitionBLL>().To<RequisitionBLL>();
            string blacklistedPasswordFilePath = AppDomain.CurrentDomain.BaseDirectory + ConfigurationManager.AppSettings["BlacklistedPasswordsFilePath"]?.ToString();
            kernel.Bind<BlacklistedPasswordFileService>().ToConstructor(c => new BlacklistedPasswordFileService(blacklistedPasswordFilePath));
            kernel.Bind<IPracticeService>().To<PracticeService>();

            kernel.Bind<ConsultationFormService>().ToSelf();
            kernel.Bind<RequisitionPatientService>().ToSelf();
            kernel.Bind<RequisitionService>().ToSelf();
            kernel.Bind<RequisitionStatusService>().ToSelf();
            kernel.Bind<RequisitionPatientRepository>().ToSelf();
            kernel.Bind<RequisitionRepository>().ToSelf();
            kernel.Bind<RequisitionStatusRepository>().ToSelf();

            kernel.Bind<IContactManagerService>().To<ContactManagerService>();
            kernel.Bind<IContactManagerEmailService>().To<ContactManagerEmailService>();
            kernel.Bind<IPatientLetterService>().To<PatientLetterService>();
            kernel.Bind<ITokenHelper>().To<TokenHelper>().InRequestScope();
        }
    }
}
