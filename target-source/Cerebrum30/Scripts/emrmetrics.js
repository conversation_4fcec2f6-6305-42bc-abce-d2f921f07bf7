﻿$(document).ready(function () {
    $("#startDate").datepicker({
        numberOfMonths: 1,
        changeYear: true,
        dateFormat: "yy/mm/dd",
        yearRange: "c-7:c+7"
    });

    $("#endDate").datepicker({
        numberOfMonths: 1,
        changeYear: true,
        dateFormat: "yy/mm/dd",
        yearRange: "c-7:c+7"
    });

    $("#startDate_s").datepicker({
        numberOfMonths: 1,
        changeYear: true,
        dateFormat: "yy/mm/dd",
        yearRange: "c-7:c+7"
    });

    $("#endDate_s").datepicker({
        numberOfMonths: 1,
        changeYear: true,
        dateFormat: "yy/mm/dd",
        yearRange: "c-7:c+7"
    });

    $(document).on('click', '#lnk', function (event) {//
        if ($("#docIdHidd").val() == 0 || $("#docIdHidd").val() == undefined) {
            alert("Select Provider befor Print!")
            return false;
        }
        if ($("#startDate").val() == "" || $("#startDate").val() == undefined) {
            alert("Select Start Date befor Print!")
            return false;
        }
        if ($("#endDate").val() == "" || $("#endDate").val() == undefined) {
            alert("Select End Dat befor Print!")
            return false;
        }

        var fakedUri = $("#lnk").prop("href");
        var uri = fakedUri.replace("docIdHiddXXXXX", $("#docIdHidd").val());
        var uri1 = uri.replace("startDateXXXXX", $("#startDate").val());
        var uri2 = uri1.replace("endDateXXXXX", $("#endDate").val());
        var uri3 = uri2.replace("docNameXXXXX", $("#docName").val());
        $("#lnk").prop("href", uri3);
    });

    $(document).on('click', '#lnk_s', function (event) {//
        if ($("#doctorIdHid").val() == 0 || $("#doctorIdHid").val() == undefined) {
            alert("Select Provider befor Print!")
            return false;
        }
        if ($("#startDate_s").val() == "" || $("#startDate_s").val() == undefined) {
            alert("Select Start Date befor Print!")
            return false;
        }
        if ($("#endDate_s").val() == "" || $("#endDate_s").val() == undefined) {
            alert("Select End Dat befor Print!")
            return false;
        }

        var fakedUri = $("#lnk_s").prop("href");
        var uri = fakedUri.replace("docIdHiddXXXXX", $("#doctorIdHid").val());
        var uri1 = uri.replace("startDateXXXXX", $("#startDate_s").val());
        var uri2 = uri1.replace("endDateXXXXX", $("#endDate_s").val());
        var uri3 = uri2.replace("docNameXXXXX", $("#docName_s").val());
        $("#lnk_s").prop("href", uri3);
    });
});

$(document).ready(function () {
    $("#docName").autocomplete({
        source: function (request, response) {
            $.ajax({
                type: "POST",
                headers: { "RequestVerificationToken": requestVerificationToken },
                url: "/AdminUser/EMRMetrics/SearchPracticeDocs/?term_=" + request.term,
                contentType: "application/json ; charset=utf-8",
                dataType: "json",
                success: function (msg) {
                    response($.map(msg, function (item) {
                        return {
                            "label": item.label,
                            "value": item.value
                        }
                    }))
                },
                error: function (msg) {
                    //
                }
            });
        },
        select: function (event, ui) {
            var label_ = ui.item.label;
            var val_ = ui.item.value;
            ui.item.value = label_;
            $("#docIdHidd").attr("value", val_);
            $(this).attr("title", label_);
        },
        minLength: 1
    });
});

$(document).ready(function () {
    $("#docName_s").autocomplete({
        source: function (request, response) {
            $.ajax({
                type: "POST",
                headers: { "RequestVerificationToken": requestVerificationToken },
                url: "/AdminUser/EMRMetrics/SearchPracticeDocs/?term_=" + request.term,
                contentType: "application/json ; charset=utf-8",
                dataType: "json",
                success: function (msg) {
                    response($.map(msg, function (item) {
                        return {
                            "label": item.label,
                            "value": item.value
                        }
                    }))
                },
                error: function (msg) {
                    //
                }
            });
        },
        select: function (event, ui) {
            var label_ = ui.item.label;
            var val_ = ui.item.value;
            ui.item.value = label_;
            $("#doctorIdHid").attr("value", val_);
            $(this).attr("title", label_);
        },
        minLength: 1
    });
});