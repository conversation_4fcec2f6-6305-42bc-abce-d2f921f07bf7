using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
// TODO: Replace System.Web.Helpers with ASP.NET Core equivalent
// using System.Web.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
// TODO: Replace System.Web.UI.WebControls with ASP.NET Core equivalent
// using System.Web.UI.WebControls;

using Newtonsoft.Json;

using Cerebrum.BLL.Bill;
using Cerebrum.ViewModels.Bill;
using Cerebrum30.Controllers;
using Cerebrum30.Filters;
using Cerebrum30.Utility;
using Cerebrum.Data;

namespace Cerebrum30.Areas.Bill.Controllers
{
    /// <summary>
    /// Bill Report's Controller
    /// </summary>
    [Area("Bill")]
    public class ReportController : BaseController, ILogGeneric
    {
        private const string PERMISSION_BILLINGADMIN_OFFICE = "Billing Admin (Office)";
        private const string PERMISSION_BILLINGADMIN_HOSPITAL = "Billing Admin (Hospital)";
        private const string PERMISSION_RAGROUP_BILLING = "RA Group Billing";
        private const string PERMISSION_RASOLO_BILLING = "RA Solo Billing";

        private bool isBillingAdmin = false;
        private int practiceId = 0;
        private List<int> practiceDoctorIds = new List<int>();
        private ReportBLL bll;
        private readonly CerebrumContext _context;
        private readonly Microsoft.Extensions.Configuration.IConfiguration _configuration;

        public ReportController(CerebrumContext context, Microsoft.Extensions.Configuration.IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
        }

        // GET: Bill/Report/Index
        public ActionResult Index()
        {
            Cerebrum.ViewModels.Bill.IndexResponse response = new Cerebrum.ViewModels.Bill.IndexResponse();
            try
            {
                //if (!(IsOfficeBillingAdmin() || IsDoctorAssistant(-1)))
                //    response.errorMessage = "You don't have permission to view bill history. Please Contact Administrator! ";
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "Index");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View("Report", response);
        }

        // GET: Bill/Report/Performeter
        public ActionResult Performeter()
        {
            PerformeterResponse response = new PerformeterResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("performeter", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.Performeter(practiceId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "Performeter");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("Performeter", response);
        }

        // GET: Bill/Report/EdtFile
        public ActionResult EdtFile()
        {
            EdtFileResponse response = new EdtFileResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("Edt File", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.EdtFile(practiceId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "EdtFile");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("EdtFile", response);
        }

        // GET: Bill/Report/EdtFileSearch
        [ValidateHeaderAntiForgeryToken]
        public JsonResult EdtFileSearch(EdtFileSearchRequest request)
        {
            EdtFileSearchResponse response = new EdtFileSearchResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("Edt File", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.EdtFileSearch(isBillingAdmin, practiceId, practiceDoctorIds, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "EdtFileSearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        [ValidateHeaderAntiForgeryToken]
        public ActionResult EdtFileContent(int edtFileId)
        {
            TextFileResponse response = new TextFileResponse();
            try
            {
                var permission = CheckBillingPermission("Edt File Content");
                response.errorMessage = permission.errorMessage;
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.EdtFileContent(edtFileId, permission);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "EdtFileContent");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View("TextFile", response);
        }

        [ValidateHeaderAntiForgeryToken]
        public ActionResult ConfirmedFileContent(int edtFileId)
        {
            TextFileResponse response = new TextFileResponse();
            try
            {
                var permission = CheckBillingPermission("Confirmed File Content");
                response.errorMessage = permission.errorMessage;
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.ConfirmedFileContent(edtFileId, permission);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ConfirmedFileContent");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View("TextFile", response);
        }

        [ValidateHeaderAntiForgeryToken]
        public ActionResult RemittanceOriginalContent(int billingFileId)
        {
            TextFileResponse response = new TextFileResponse();
            try
            {
                var permission = CheckBillingPermission("remittance advice file");
                response.errorMessage = permission.errorMessage;
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.RemittanceOriginalContent(billingFileId, permission);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "RemittanceOriginalContent");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View("TextFile", response);
        }

        // GET: Bill/Report/EdtError
        public ActionResult EdtError()
        {
            EdtErrorResponse response = new EdtErrorResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("Claim Error", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.EdtError(practiceId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "EdtErrors");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("EdtError", response);
        }

        // GET: Bill/Report/EdtErrorSearch
        [ValidateHeaderAntiForgeryToken]
        public JsonResult EdtErrorSearch(EdtErrorSearchRequest request)
        {
            EdtErrorSearchResponse response = new EdtErrorSearchResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("Claim Error", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.EdtErrorSearch(isBillingAdmin, practiceId, practiceDoctorIds, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "EdtErrorSearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        // GET: Bill/Report/ToBeBilled
        public ActionResult ToBeBilled()
        {
            ToBeBilledResponse response = new ToBeBilledResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("ToBeBilled page", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.ToBeBilled(practiceId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ToBeBilled");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("ToBeBilled", response);
        }

        // GET: Bill/Report/ToBeBilledSearch
        private JsonResult ToBeBilledSearch(ToBeBilledSearchRequest request)
        {
            ToBeBilledSearchResponse response = new ToBeBilledSearchResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("ToBeBilled page", request.billingType);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.ToBeBilledSearch(isBillingAdmin, practiceId, practiceDoctorIds, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ToBeBilledSearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        [ValidateHeaderAntiForgeryToken]
        public JsonResult ToBeBilledSearchOffice(ToBeBilledSearchRequest request)
        {
            return ToBeBilledSearch(request);
        }

        [ValidateHeaderAntiForgeryToken]
        public JsonResult ToBeBilledSearchHospital(ToBeBilledSearchRequest request)
        {
            return ToBeBilledSearch(request);
        }

        // GET: Bill/Report/ClaimSearch
        public ActionResult ClaimSearch()
        {
            ClaimSearchResponse response = new ClaimSearchResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("ClaimSearch page", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.ClaimSearch(practiceId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ClaimSearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View("ClaimSearch", response);
        }

        // GET: Bill/Report/ToBeBilledSearch
        private JsonResult ClaimSearchData(ClaimSearchDataRequest request)
        {
            ClaimSearchDataResponse response = new ClaimSearchDataResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("ClaimSearch page", request.billingType);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.ClaimSearchData(isBillingAdmin, practiceId, practiceDoctorIds, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ClaimSearchData");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        [ValidateHeaderAntiForgeryToken]
        public JsonResult ClaimSearchDataOffice(ClaimSearchDataRequest request)
        {
            return ClaimSearchData(request);
        }

        [ValidateHeaderAntiForgeryToken]
        public JsonResult ClaimSearchDataHospital(ClaimSearchDataRequest request)
        {
            return ClaimSearchData(request);
        }

        // GET: Bill/Report/Remittance
        public ActionResult Remittance()
        {
            RemittanceResponse response = new RemittanceResponse();
            try
            {
                var permission = CheckBillingPermission("remittance advice file");
                response.errorMessage = permission.errorMessage;
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "Remittance");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("Remittance", response);
        }

        // GET: Bill/Report/RemittanceSearch
        [ValidateHeaderAntiForgeryToken]
        public JsonResult RemittanceSearch(RemittanceFileSearchRequest request)
        {
            RemittanceFileSearchResponse response = new RemittanceFileSearchResponse();
            try
            {
                var permission = CheckBillingPermission("remittance advice file");
                response.errorMessage = permission.errorMessage;
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.RemittanceSearch(isBillingAdmin, practiceId, permission, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "RemittanceSearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        // GET: Bill/Report/RemittanceDetail
        [ValidateHeaderAntiForgeryToken]
        public ActionResult RemittanceDetail(int billingFileId)
        {
            RemittanceDetailResponse response = new RemittanceDetailResponse();
            try
            {
                var permission = CheckBillingPermission("remittance advice file");
                response.errorMessage = permission.errorMessage;
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.RemittanceDetail(billingFileId, permission);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "RemittanceDetail");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View(response);
        }

        // GET: Bill/Report/BillingHistory
        public ActionResult BillingHistory(int patientRecordId)
        {
            BillingHistoryResponse response = new BillingHistoryResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("billing history", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.BillingHistory(patientRecordId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "BillingHistory");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("BillingHistory", response);
        }

        // GET: Bill/Report/BillingHistorySearch
        private JsonResult BillingHistorySearch(BillingHistorySearchRequest request)
        {
            BillingHistorySearchResponse response = new BillingHistorySearchResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("billing history", request.billingType);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.BillingHistorySearch(isBillingAdmin, practiceId, practiceDoctorIds, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "BillingHistorySearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        [ValidateHeaderAntiForgeryToken]
        public JsonResult BillingHistorySearchOffice(BillingHistorySearchRequest request)
        {
            return BillingHistorySearch(request);
        }

        [ValidateHeaderAntiForgeryToken]
        public JsonResult BillingHistorySearchHospital(BillingHistorySearchRequest request)
        {
            return BillingHistorySearch(request);
        }

        // GET: Bill/Report/Statement
        public ActionResult Statement(int patientRecordId)
        {
            StatementResponse response = new StatementResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("statement", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.Statement(patientRecordId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "Statement");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("Statement", response);
        }

        // GET: Bill/Report/StatementSearch
        private JsonResult StatementSearch(StatementSearchRequest request)
        {
            StatementSearchResponse response = new StatementSearchResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("statement", request.billingType);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.StatementSearch(isBillingAdmin, practiceId, practiceDoctorIds, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "StatementSearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        public JsonResult StatementSearchOffice(StatementSearchRequest request)
        {
            return StatementSearch(request);
        }

        public JsonResult StatementSearchHospital(StatementSearchRequest request)
        {
            return StatementSearch(request);
        }

        // GET: Bill/Report/LogFile
        public ActionResult LogFile()
        {
            Cerebrum.ViewModels.Bill.IndexResponse response = new Cerebrum.ViewModels.Bill.IndexResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("log file", 100);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "LogFile");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("LogFile", response);
        }

        // GET: Bill/Report/LogFileSearch
        [ValidateHeaderAntiForgeryToken]
        public JsonResult LogFileSearch(LogFileSearchRequest request)
        {
            LogFileSearchResponse response = new LogFileSearchResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("log file", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                {
                    if (isBillingAdmin)
                        response = bll.LogFileSearch(request);
                    else
                        response.errorMessage = "You don't have permission to view log file. Please Contact Administrator!";
                }
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "LogFileSearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        // GET: Bill/Report/ShowTextFile
        public JsonResult ShowLogFile(string fileName)
        {
            TextFileResponse response = new TextFileResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("log file", 100);
                if (string.IsNullOrEmpty(response.errorMessage))
                {
                    fileName = HttpUtility.UrlDecode(fileName);
                    response = bll.ShowLogFile(fileName);
                }
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ShowLogFile");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        // GET: Bill/Report/PerformeterSearch
        private JsonResult PerformeterSearch(PerformeterSearchRequest request)
        {
            PerformeterSearchResponse response = new PerformeterSearchResponse();
            try
            {
                response.errorMessage = CheckBillingPermission("performeter", request.billingType);
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.PerformeterSearch(isBillingAdmin, practiceId, practiceDoctorIds, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "PerformeterSearch");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        [ValidateHeaderAntiForgeryToken]
        public JsonResult PerformeterSearchOffice(PerformeterSearchRequest request)
        {
            return PerformeterSearch(request);
        }

        [ValidateHeaderAntiForgeryToken]
        public JsonResult PerformeterSearchHospital(PerformeterSearchRequest request)
        {
            return PerformeterSearch(request);
        }

        // GET: Bill/Report/PerformeterToExcel
        [ValidateHeaderAntiForgeryToken]
        private async Task<ActionResult> PerformeterToExcel(string searchRequest)
        {
            try
            {
                PerformeterSearchRequest request = JsonConvert.DeserializeObject<PerformeterSearchRequest>(searchRequest);
                string errorMessage = CheckBillingPermission("performeter", request.billingType);
                if (string.IsNullOrEmpty(errorMessage))
                {
                    StringWriter sw = bll.PerformeterToExcel(isBillingAdmin, practiceId, practiceDoctorIds, request);
                    Response.Headers["Content-Disposition"] = "attachment; filename=Performeter.csv";
                    Response.ContentType = "text/csv";

                    // In ASP.NET Core, write to response body and return
                    var bytes = System.Text.Encoding.UTF8.GetBytes(sw.ToString());
                    await Response.Body.WriteAsync(bytes, 0, bytes.Length);
                    return new EmptyResult();
                }
                else
                {
                    return BadRequest(errorMessage);
                }
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "PerformeterToExcel");
                return StatusCode(500, "Internal server error");
            }
        }

        public async Task<ActionResult> PerformeterToExcelOffice(string searchRequest)
        {
            return await PerformeterToExcel(searchRequest);
        }

        public async Task<ActionResult> PerformeterToExcelHospital(string searchRequest)
        {
            return await PerformeterToExcel(searchRequest);
        }

        public string ChangeEdtErrorStatus(int edtErrorId)
        {
            string response = string.Empty;
            try
            {
                response = CheckBillingPermission("EdtErrorStatus page", 100);
                if (string.IsNullOrEmpty(response))
                    response = bll.ChangeEdtErrorStatus(edtErrorId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ChangeEdtErrorStatus");
                response = "There was a problem. Please Contact Administrator! ";
            }

            return response;
        }

        [ValidateHeaderAntiForgeryToken]
        public ActionResult EdtErrorDetail(int edtErrorId)
        {
            EdtErrorShowResponse response = new EdtErrorShowResponse();
            try
            {
                var permission = CheckBillingPermission("EdtErrorStatus page");
                response.errorMessage = permission.errorMessage;
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.EdtErrorDetail(edtErrorId, permission);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ShowEdtError");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View(response);
        }

        // GET: Bill/Report/Claim
        public ActionResult Claim(string appointmentId, string admissionActionId)
        {
            ClaimResponse response = new ClaimResponse();
            try
            {
                var permission = CheckBillingPermission("claim page");
                response.errorMessage = permission.errorMessage;
                if (string.IsNullOrEmpty(response.errorMessage))
                {
                    response.appointmentId = appointmentId;
                    response.admissionActionId = admissionActionId;
                }
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "Claim");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View(response);
        }

        // GET: Bill/Report/ClaimDetail
        public JsonResult ClaimDetail(int appointmentId, int admissionActionId)
        {
            ClaimDetailResponse response = new ClaimDetailResponse();
            try
            {
                var permission = CheckBillingPermission("claim page");
                response.errorMessage = permission.errorMessage;
                if (string.IsNullOrEmpty(response.errorMessage))
                    response = bll.ClaimDetail(permission, appointmentId, admissionActionId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ClaimDetail");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        // GET: Bill/Report/SaveClaim
        public string SaveClaim(AppointmentClaimRequest request)
        {
            try
            {
                var permission = CheckBillingPermission("claim page");
                if (!string.IsNullOrEmpty(permission.errorMessage))
                    return permission.errorMessage;
                string ipAddress = IPAddress(Request);
                GroupBillBLL groupBillBLL = new GroupBillBLL(CerebrumUser.UserId, ipAddress);
                return groupBillBLL.SaveAppointmentClaim(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "SaveClaim");
                return "There was a problem. Please Contact Administrator! ";
            }
        }

        // GET: Bill/Report/ChangeClaimBillStatus
        public string ChangeClaimBillStatus(ClaimBillStatusRequest request)
        {
            string response = string.Empty;
            try
            {
                response = CheckBillingPermission("claim page", 100);
                if (string.IsNullOrEmpty(response))
                    response = bll.ChangeClaimBillStatus(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ChangeClaimBillStatus");
                response = "There was a problem. Please Contact Administrator! ";
            }

            return response;
        }

        // GET: Bill/Report/ShowTextFile
        //public ActionResult ShowTextFile(string fileName)
        //{
        //    TextFileResponse response = new TextFileResponse();
        //    try
        //    {
        //        string ipAddress = IPAddress(Request);
        //        fileName = HttpUtility.UrlDecode(fileName);
        //        ReportBLL bll = new ReportBLL(CerebrumUser.UserId,ipAddress);
        //        response = bll.ShowTextFile(fileName);
        //    }
        //    catch (Exception ex)
        //    {
        //        LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "ShowTextFile");
        //        response.errorMessage = "There was a problem. Please Contact Administrator! ";
        //    }

        //    return View("TextFile", response);
        //}

        //billingAdminType    0: hospital;  1:office ;   other: all
        private bool IsBillingAdmin(int billingAdminType)
        {
            if (billingAdminType == 0)
                return CerebrumUser.HasPermission(PERMISSION_BILLINGADMIN_HOSPITAL);
            if (billingAdminType == 1)
                return CerebrumUser.HasPermission(PERMISSION_BILLINGADMIN_OFFICE);

            return CerebrumUser.HasPermission(string.Format($"{PERMISSION_BILLINGADMIN_OFFICE},{PERMISSION_BILLINGADMIN_HOSPITAL}"));
        }

        private string CheckBillingPermission(string pageText, int billingAdminType)
        {
            string ipAddress = IPAddress(Request);
            bll = new ReportBLL(_context, _configuration, CerebrumUser.UserId, ipAddress);

            practiceId = CerebrumUser.PracticeId;
            isBillingAdmin = IsBillingAdmin(billingAdminType);
            if (isBillingAdmin)
                return string.Empty;

            practiceDoctorIds = bll.GetPracticeDoctorIds(practiceId, CerebrumUser.GetUserIdGuid());
            if (practiceDoctorIds.Count == 0)
                return string.Format($"You cannot view any doctor billing data in page '{pageText}'. Please Contact Administrator!");
            bll.practiceDoctorIds = practiceDoctorIds;

            return string.Empty;
        }

        private RemittancePermission CheckBillingPermission(string pageText)
        {
            string ipAddress = IPAddress(Request);
            RemittancePermission permission = new RemittancePermission();
            bll = new ReportBLL(_context, _configuration, CerebrumUser.UserId, ipAddress);
            practiceId = CerebrumUser.PracticeId;
            permission.practiceId = practiceId;

            if (CerebrumUser.HasPermission(string.Format($"{PERMISSION_BILLINGADMIN_OFFICE},{PERMISSION_RAGROUP_BILLING}")))
                permission.isRAGroupBillingAdmin = true;
            if (CerebrumUser.HasPermission(PERMISSION_BILLINGADMIN_HOSPITAL))
                permission.isRASoloBillingAdmin = true;

            var practiceDoctorIds = bll.GetPracticeDoctorIds(practiceId, CerebrumUser.GetUserIdGuid());
            permission.practiceDoctorIds = practiceDoctorIds.Select(a => a.ToString()).ToList();

            if (practiceDoctorIds.Count == 0 && !(permission.isRAGroupBillingAdmin || permission.isRASoloBillingAdmin))
                permission.errorMessage = string.Format($"You cannot view any doctor billing data in page '{pageText}'. Please Contact Administrator!");

            bll.practiceDoctorIds = practiceDoctorIds;
            return permission;
        }

        private string LogException(string methodName, Exception ex, string extraMessage)
        {
            string logMessage = extraMessage + Environment.NewLine + methodName + Environment.NewLine + ex.Message;
            if (ex.InnerException != null)
            {
                logMessage += Environment.NewLine + ex.InnerException.Message;
            }
            this.LogDebugFormat("Exception: {0}", logMessage);
            return logMessage;
        }
    }
}