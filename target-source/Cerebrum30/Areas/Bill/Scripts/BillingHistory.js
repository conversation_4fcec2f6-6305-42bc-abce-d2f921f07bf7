var balanceOwing;
var printingBillingHistory;

$(document).ready(function () {
    PAGESIZE = 20;
    totalRowRequest = false;
    billingHistoryHeaderInfoRequest = false;
    sortByColumn = "";
    sortByOrder = "";
    sortingHeaders = ["imageAppointmentDate", "imageAdmissionDate", "imagePhysician", "imagePatientPayor", "imageService", "imageServicCode", "imageServicDate", "imageDiagnosis", "imageReferringDoctor", "imageNumberOfService", "imageFee", "imageBillStatus"]
    balanceOwing = "";
    printingBillingHistory = false;

    $("#buttonSearch").click(function () { buttonSearchClicked() });
    $("input[name='billingHistoryOfficeBilling']").click(function () { billingHistoryOfficeBillingClicked() });
    $("#buttonBillingHistoryPrint").click(function () { buttonBillingHistoryPrintClicked() });
    $("#startDate").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true, onSelect: function (date, inst) { $(this)[tog(this.value)]("x"); } });
    $("#endDate").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true, onSelect: function (date, inst) { $(this)[tog(this.value)]("x"); } });

    $("#patientNameInput").autocomplete({
        source: function (request, response) { ajaxCall("ExternalDocument/ExternalDocument/GetPatientList", { term: request.term }, false, response); },
        minLength: 3,
        delay: 500,
        select: function (event, ui) { patientSelect(ui); return false; }
    });

    // clearable input
    function tog(v) { return v ? "addClass" : "removeClass"; }
    $(document).on("input", ".clearable", function () { $(this)[tog(this.value)]("x"); })
                .on("mousemove", ".x", function (e) { $(this)[tog(this.offsetWidth - 18 < e.clientX - this.getBoundingClientRect().left)]("onX"); })
                .on("touchstart click", ".onX", function (ev) { ev.preventDefault(); $(this).removeClass("x onX").val("").change(); });

    billingHistoryOfficeBillingClicked();

    if (patientRecordIdBillingHistory > 0) {
        buttonSearchClicked();
    }
});

function buttonSearchClicked() {
    totalRowRequest = true;
    billingHistoryHeaderInfoRequest = true;
    totalPages = 0;
    setPage(1);
}

function loadPage(page) {
    var data = getSearchCriteria(page);
    var url = "Bill/Report/BillingHistorySearchOffice";
    if (data.billingType == 0) {
        url = "Bill/Report/BillingHistorySearchHospital";
    }

    // Add antiforgery token to the data instead of header
    data.__RequestVerificationToken = requestVerificationToken;
    
    $.ajax({
        method: "POST",
        url: url,
        data: data,
        async: printingBillingHistory ? false : true,
        beforeSend: function () {
            $("#ajax-loader").show();
        },
        complete: function () {
            $("#ajax-loader").hide();
        },
        success: function (result) {
            if (result.errorMessage == "") {
                if (printingBillingHistory) {
                    printBillingHistory(result);
                } else {
                    showBillingHistoryList(result);
                }
            } else {
                showMessageModal("error", result.errorMessage, false);
            }
            printingBillingHistory = false;
            totalRowRequest = false;
            billingHistoryHeaderInfoRequest = false;
        },
        error: function (xhr, thrownError) {
            printingBillingHistory = false;
            totalRowRequest = false;
            billingHistoryHeaderInfoRequest = false;
            checkAjaxError(url, xhr, thrownError);
        }
    });
}

function showBillingHistoryList(result) {
    if (result.totalRow != -1) {
        totalItems = result.totalRow;
        balanceOwing = result.balanceOwing;
    }

    if (billingHistoryHeaderInfoRequest) {
        if (result.patientRecordId > 0) {
            $("#patientName").html("<span class='lbl-link'>" + getPatientInfo(result.patientRecordId, result.patientName) + "</span>");
            $("#patientAddress").text(result.patientAddress);
            $("#patientCity").text(result.patientCity);
            $("#patientProvince").text(result.patientProvince);
            $("#patientPostalCode").text(result.patientPostalCode);
            $("#patientDob").text(result.patientDob);

            $("#patientInfo").show();
            $("#payorInfo").hide();
        } else {
            $("#payorName").text(result.payorName);
            $("#payorAddress").text(result.payorAddress);

            $("#patientInfo").hide();
            $("#payorInfo").show();
        }
    }

    if (result.patientRecordId > 0) {
        $("#spanPatientPayor").text("Payor");
    } else {
        $("#spanPatientPayor").text("Patient");
    }

    $("#billingHistoryList tbody").html("");
    var billingType = $("input:radio[name=billingHistoryOfficeBilling]:checked").val();
    if (billingType == 1) {
        $("#billingHistoryList tbody").attr("class", "office-bill-field");
        $("#pagination").addClass("office-bill-field");
        $("#pagination").removeClass("hospital-bill-field");
    } else {
        $("#billingHistoryList tbody").attr("class", "hospital-bill-field");
        $("#pagination").addClass("hospital-bill-field");
        $("#pagination").removeClass("office-bill-field");
    }
    billingHistoryOfficeBillingClicked();

    if (result.billingHistories.length == 0) {
        $("#buttonBillingHistoryPrint").hide();
        $("#billingHistoryList").append("<tr><td colspan='11'><br /><br /><br /><br /><br /><br /><br /><br /><div style='font-size: 28px; color: red; text-align:center;'>No record found<br /><br /><br /><br /><br /></div></td></tr>");
    } else {
        $("#buttonBillingHistoryPrint").show();
        var trClass = "";
        $.each(result.billingHistories, function (n, billingHistory) {
            var tr = "<tr" + trClass + ">";
            if (billingType == 1) {
                tr += "<td><a class='lbl-link' target='_blank' href='Schedule/daysheet?OfficeId=" + billingHistory.officeId + "&Date=" + billingHistory.appointmentDate + "'>" + billingHistory.appointmentDate + "</a></td>";
            } else {
                tr += "<td><a class='lbl-link' target='_blank' href='hd/admissions?Date=" + billingHistory.admissionDate + "&practiceDoctorId=" + billingHistory.practiceDoctorId + "'>" + billingHistory.admissionDate + "</a></td>";
            }
            tr += "<td>" + billingHistory.doctorName + "</td>";
            //tr += "<td><span class='lbl-link'>" + getPatientInfo(billingHistory.patientRecordId, billingHistory.patientName) + "</span></td>";
            tr += "<td>" + billingHistory.payor + "</td>";
            tr += "<td>" + billingHistory.service + "</td>";
            tr += "<td>" + billingHistory.serviceCode + "</td>";
            tr += "<td>" + billingHistory.serviceDate + "</td>";
            tr += "<td>" + billingHistory.diagnosis + "</td>";
            tr += "<td>" + billingHistory.referringDoctor + "</td>";
            tr += "<td>" + billingHistory.numberOfService + "</td>";
            tr += "<td>" + billingHistory.fee + "</td>";
            tr += "<td>" + billingHistory.billStatus + "</td>";
            tr += "</tr>";
            $("#billingHistoryList").append(tr);
            if (trClass == "") {
                trClass = " class='active'";
            } else {
                trClass = "";
            }
        });
    }

    showPagination();
    initPopover();
}

function getSearchCriteria(page) {
    var patientRecordId = patientRecordIdBillingHistory;
    var filters = [];
    filters = addTextFilter(filters, "startDate");
    filters = addTextFilter(filters, "endDate");

    var data = {
        billingType: $("input:radio[name=billingHistoryOfficeBilling]:checked").val(),
        patientRecordId: patientRecordId,
        rowStart: printingBillingHistory ? 0 : (page - 1) * PAGESIZE,
        rowCount: printingBillingHistory ? 10000 : PAGESIZE,
        totalRowRequest: printingBillingHistory ? true : totalRowRequest,
        sortByColumn: sortByColumn,
        sortByOrder: sortByOrder,
        billingHistoryHeaderInfoRequest: printingBillingHistory ? true : billingHistoryHeaderInfoRequest,
        filters: filters
    }

    return data;
}

function changePayorType(payorType) {
    $("input:radio[name='payorType']").filter("[value='" + payorType + "']").click();
}

function patientSelect(ui) {
    $("#patientNameInput").val(ui.item.label);
    patientRecordIdBillingHistory = getId(1, ui.item.value);
}

function getId(index, patientIds) {
    var id = "0";
    if (patientIds != "") {
        var ids = patientIds.split(",")
        if (index < ids.length) {
            id = ids[index];
        }
    }
    return id;
}

function buttonBillingHistoryPrintClicked() {
    printingBillingHistory = true;
    setPage(1);
}

function printBillingHistory(result) {
    printBillingHistoryDivContent("#billingHistoryDiv", "Billing History", result)
}

function printBillingHistoryDivContent(divId, title, result) {
    var url = location.protocol + "//" + location.hostname + (location.port ? ":" + location.port : "");
    var printWindow = window.open("", "Print-Window");
    printWindow.document.write("<html><head><title></title>");

    $('link[href]').each(function (i, e) {  //add css files
        var internalCssFile = url + $(this).attr("href");
        printWindow.document.write("<link rel='stylesheet' href='" + internalCssFile + "' type='text/css' />");
    })

    var css = [];   //add inline css - billingHistory
    for (var i = 0; i < document.styleSheets.length; i++) {
        var sheet = document.styleSheets[i];
        var rules;

        try {
            rules = sheet.rules;
        }
        catch (err) {
            try {
                rules = sheet.cssRules;
            }
            catch (err) {
            }
        }

        if (rules) {
            css.push('\n/* Stylesheet : ' + (sheet.href || '[inline styles]') + ' */');
            for (var j = 0; j < rules.length; j++) {
                var rule = rules[j];
                if ('cssText' in rule)
                    css.push(rule.cssText);
                else
                    css.push(rule.selectorText + ' {\n' + rule.style.cssText + '\n}\n');
            }
        }
    }
    var cssInline = css.join('\n') + '\n';
    printWindow.document.write("<style type='text/css'>" + cssInline + "</style>");

    //printWindow.document.write('<link href="/Content/staticPrint.css" rel="stylesheet"/>');

    printWindow.document.write("</head><body onload='window.print()'>");

    var newBillingHistory = $(divId).clone().prop({ id: "newBillingHistoryDiv", name: "newBillingHistoryDiv" });
    newBillingHistory.find("#billingHistoryList").attr("id", "newBillingHistoryList");
    var newBillingHistoryList = newBillingHistory.find("#newBillingHistoryList");

    newBillingHistoryList.find("tbody").html("");
    if (result.billingHistories.length == 0) {
        newBillingHistoryList.append("<tr><td colspan='9'><br /><br /><br /><br /><br /><br /><br /><br /><div style='font-size: 28px; color: red; text-align:center;'>No record found</div></td></tr>");
    } else {
        var trClass = "";
        $.each(result.billingHistories, function (n, billingHistory) {
            var tr = "<tr" + trClass + ">";
            tr += "<td><a class='lbl-link' href='schedule/daysheet?OfficeId=" + billingHistory.officeId + "&Date=" + billingHistory.appointmentDate + "'>" + billingHistory.appointmentDate + "</a></td>";
            tr += "<td>" + billingHistory.doctorName + "</td>";
            tr += "<td>" + billingHistory.patientName + "</td>";
            tr += "<td>" + billingHistory.diagnosis + "</td>";
            tr += "<td>" + billingHistory.servicesRendered + "</td>";
            tr += "<td>" + billingHistory.serviceCode + "</td>";
            tr += "<td>" + billingHistory.referringDoctor + "</td>";
            tr += "<td>" + billingHistory.numberOfService + "</td>";
            tr += "<td>" + billingHistory.fee + "</td>";
            tr += "</tr>";
            newBillingHistoryList.append(tr);
            if (trClass == "") {
                trClass = " class='active'";
            } else {
                trClass = "";
            }
        });
        newBillingHistoryList.append("<tr" + trClass + "><td colspan='8' align='right' style='margin-right: 32px; padding-right: 32px;'>Balance Owing:</td><td>" + balanceOwing + "</td></tr>");
    }

    //remove all anchor (invisible in print mode)
    newBillingHistory.find("a").each(function () {
        $(this).replaceWith($(this).text());
    });

    var divContents = newBillingHistory[0].outerHTML;
    printWindow.document.write(divContents);
    printWindow.document.write("</body></html>");
    printWindow.document.close();
}

function billingHistoryOfficeBillingClicked() {
    var billingType = $("input:radio[name=billingHistoryOfficeBilling]:checked").val();
    if (billingType == 1) {
        $(".office-bill-field").show();
        $(".hospital-bill-field").hide();
    } else {
        $(".office-bill-field").hide();
        $(".hospital-bill-field").show();
    }
}