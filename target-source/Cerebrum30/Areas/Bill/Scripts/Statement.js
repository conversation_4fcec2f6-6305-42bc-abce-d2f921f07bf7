var balanceOwing;
var printingStatement;

$(document).ready(function () {
    PAGESIZE = 20;
    totalRowRequest = false;
    statementHeaderInfoRequest = false;
    sortByColumn = "";
    sortByOrder = "";
    sortingHeaders = ["imageAppointmentDate", "imageAdmissionDate", "imagePhysician", "imagePatientPayor", "imageDiagnosis", "imageServicesRendered", "imageCode", "imageReferringDoctor", "imageNumberOfService", "imageFee", "imageBillStatus"]
    balanceOwing = "";
    printingStatement = false;

    $("#buttonSearch").click(function () { buttonSearchClicked() });
    $("input[name='statementOfficeBilling']").click(function () { statementOfficeBillingClicked() });
    $("#buttonStatementPrint").click(function () { buttonStatementPrintClicked() });
    $("#startDate").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true, onSelect: function (date, inst) { $(this)[tog(this.value)]("x"); } });
    $("#endDate").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true, onSelect: function (date, inst) { $(this)[tog(this.value)]("x"); } });

    $("#patientNameInput").autocomplete({
        source: function (request, response) { ajaxCall("ExternalDocument/ExternalDocument/GetPatientList", { term: request.term }, false, response); },
        minLength: 3,
        delay: 500,
        select: function (event, ui) { patientSelect(ui); return false; }
    });

    // clearable input
    function tog(v) { return v ? "addClass" : "removeClass"; }
    $(document).on("input", ".clearable", function () { $(this)[tog(this.value)]("x"); })
                .on("mousemove", ".x", function (e) { $(this)[tog(this.offsetWidth - 18 < e.clientX - this.getBoundingClientRect().left)]("onX"); })
                .on("touchstart click", ".onX", function (ev) { ev.preventDefault(); $(this).removeClass("x onX").val("").change(); });

    statementOfficeBillingClicked();
    if (patientRecordIdStatement > 0) {
        buttonSearchClicked();
    }
});

function buttonSearchClicked() {
    totalRowRequest = true;
    statementHeaderInfoRequest = true;
    totalPages = 0;
    setPage(1);
}

function loadPage(page) {
    var data = getSearchCriteria(page);
    var url = "Bill/Report/StatementSearchOffice";
    if (data.billingType == 0) {
        url = "Bill/Report/StatementSearchHospital";
    }
    // Add antiforgery token to the data instead of header
    data.__RequestVerificationToken = requestVerificationToken;
    
    $.ajax({
        method: "POST",
        url: url,
        data: data,
        async: printingStatement ? false : true,
        beforeSend: function () {
            $('#ajax-loader').show();
        },
        complete: function () {
            $('#ajax-loader').hide();
        },
        success: function (result) {
            if (result.errorMessage == "") {
                if (printingStatement) {
                    printStatement(result);
                } else {
                    showStatementList(result);
                }
            } else {
                showMessageModal("error", result.errorMessage, false);
            }
            printingStatement = false;
            totalRowRequest = false;
            statementHeaderInfoRequest = false;
        },
        error: function (xhr, thrownError) {
            printingStatement = false;
            totalRowRequest = false;
            statementHeaderInfoRequest = false;
            checkAjaxError(url, xhr, thrownError);
        }
    });
}

function showStatementList(result) {
    if (result.totalRow != -1) {
        totalItems = result.totalRow;
        balanceOwing = result.balanceOwing;
    }

    if (statementHeaderInfoRequest) {
        if (result.patientRecordId > 0) {
            $("#patientName").html("<span class='lbl-link'>" + getPatientInfo(result.patientRecordId, result.patientName) + "</span>");
            $("#patientAddress").text(result.patientAddress);
            $("#patientCity").text(result.patientCity);
            $("#patientProvince").text(result.patientProvince);
            $("#patientPostalCode").text(result.patientPostalCode);
            $("#patientDob").text(result.patientDob);

            $("#patientInfo").show();
            $("#payorInfo").hide();
        } else {
            $("#payorName").text(result.payorName);
            $("#payorAddress").text(result.payorAddress);

            $("#patientInfo").hide();
            $("#payorInfo").show();
        }
    }

    if (result.patientRecordId > 0) {
        $("#spanPatientPayor").text("Payor");
    } else {
        $("#spanPatientPayor").text("Patient");
    }

    $("#statementList tbody").html("");
    var billingType = $("input:radio[name=statementOfficeBilling]:checked").val();

    if (result.statements.length == 0) {        
        $("#divStatementPrint").hide();
        $("#statementList").append("<tr><td colspan='11'><br /><br /><br /><br /><br /><br /><br /><br /><div style='font-size: 28px; color: red; text-align:center;'>No record found<br /><br /><br /><br /><br /></div></td></tr>");
    } else {
        $("#divStatementPrint").show();
        var trClass = "";
        $.each(result.statements, function (n, statement) {
            var tr = "<tr" + trClass + ">";
            if (billingType == 1) {
                tr += "<td><a class='lbl-link' target='_blank' href='schedule/daysheet?OfficeId=" + statement.officeId + "&Date=" + statement.appointmentDate + "'>" + statement.appointmentDate + "</a></td>";
            } else {
                tr += "<td><a class='lbl-link' target='_blank' href='hd/admissions?Date=" + statement.admissionDate + "&practiceDoctorId=" + statement.practiceDoctorId + "'>" + statement.admissionDate + "</a></td>";
            }
            tr += "<td>" + statement.doctorName + "</td>";

            if (result.patientRecordId > 0) {
                tr += "<td>" + statement.payor + "</td>";
            } else {
                tr += "<td><span class='lbl-link'>" + getPatientInfo(statement.patientRecordId, statement.patientName) + "</span></td>";
            }

            tr += "<td>" + statement.diagnosis + "</td>";
            tr += "<td>" + statement.servicesRendered + "</td>";
            tr += "<td>" + statement.serviceCode + "</td>";
            tr += "<td>" + statement.referringDoctor + "</td>";
            tr += "<td>" + statement.numberOfService + "</td>";
            tr += "<td>" + statement.fee + "</td>";
            tr += "<td>" + statement.billStatus + "</td>";
            tr += "</tr>";
            $("#statementList").append(tr);
            if (trClass == "") {
                trClass = " class='active'";
            } else {
                trClass = "";
            }
        });
        $("#statementList").append("<tr" + trClass + "><td colspan='8' align='right' style='margin-right: 32px; padding-right: 32px;'>Balance Owing:</td><td>" + balanceOwing + "</td><td></td></tr>");
    }

    if (billingType == 1) {
        $("#statementList tbody").attr("class", "office-bill-field");
        $("#buttonStatementPrint").addClass("office-bill-field");
        $("#buttonStatementPrint").removeClass("hospital-bill-field");
        $("#pagination").addClass("office-bill-field");
        $("#pagination").removeClass("hospital-bill-field");
    } else {
        $("#statementList tbody").attr("class", "hospital-bill-field");
        $("#buttonStatementPrint").addClass("hospital-bill-field");
        $("#buttonStatementPrint").removeClass("office-bill-field");
        $("#pagination").addClass("hospital-bill-field");
        $("#pagination").removeClass("office-bill-field");
    }

    showPagination();
    initPopover();
    statementOfficeBillingClicked();
}

function getSearchCriteria(page) {
    var patientRecordId = patientRecordIdStatement;
    var payorId = "0";
    if ($("input:radio[name='payorType']").filter("[value=1]").is(":checked")) {
        patientRecordId = "0";
        payorId = $("#payoyId").val();
    }

    var filters = [];
    filters = addTextFilter(filters, "startDate");
    filters = addTextFilter(filters, "endDate");

    var data = {
        billingType: $("input:radio[name=statementOfficeBilling]:checked").val(),
        patientRecordId: patientRecordId,
        payorId: payorId,
        rowStart: printingStatement ? 0 : (page - 1) * PAGESIZE,
        rowCount: printingStatement ? 10000 : PAGESIZE,
        totalRowRequest: printingStatement ? true : totalRowRequest,
        sortByColumn: sortByColumn,
        sortByOrder: sortByOrder,
        statementHeaderInfoRequest: printingStatement ? true : statementHeaderInfoRequest,
        filters: filters
    }

    return data;
}

function changePayorType(payorType) {
    $("input:radio[name='payorType']").filter("[value='" + payorType + "']").click();
}

function patientSelect(ui) {
    $("#patientNameInput").val(ui.item.label);
    patientRecordIdStatement = getId(1, ui.item.value);
}

function getId(index, patientIds) {
    var id = "0";
    if (patientIds != "") {
        var ids = patientIds.split(",")
        if (index < ids.length) {
            id = ids[index];
        }
    }
    return id;
}

function buttonStatementPrintClicked() {
    console.log('buttonStatementPrintClicked_7');
    printingStatement = true;
    setPage(1);
}

function printStatement(result) {
    printStatementDivContent("#statementDiv", "Statement", result)
}

function printStatementDivContent(divId, title, result) {
    var url = location.protocol + "//" + location.hostname + (location.port ? ":" + location.port : "") + "/";
    var printWindow = window.open("", "Print-Window");
    printWindow.document.write("<html><head><title></title>");

    $('link[href]').each(function (i, e) {  //add css files
        var internalCssFile = url + $(this).attr("href").replace(/^\s+|\s+$/gm, '\/');
        if ($(this).attr("href").indexOf("http") >= 0) {
            internalCssFile = $(this).attr("href");
        }
        printWindow.document.write("<link rel='stylesheet' href='" + internalCssFile + "' type='text/css' />");
    })

    var css = [];   //add inline css
    for (var i = 0; i < document.styleSheets.length; i++) {
        var sheet = document.styleSheets[i];
        var rules;

        try {
            rules = sheet.rules;
        }
        catch (err) {
            try {
                rules = sheet.cssRules;
            }
            catch (err) {
            }
        }
        
        if (rules) {
            css.push('\n/* Stylesheet : ' + (sheet.href || '[inline styles]') + ' */');
            for (var j = 0; j < rules.length; j++) {
                var rule = rules[j];
                if ('cssText' in rule)
                    css.push(rule.cssText);
                else
                    css.push(rule.selectorText + ' {\n' + rule.style.cssText + '\n}\n');
            }
        }
    }
    var cssInline = css.join('\n') + '\n';

    

    printWindow.document.write("<style type='text/css'>" + cssInline + "</style>");
    //printWindow.document.write('<link href="/Content/staticPrint.css" rel="stylesheet"/>');
    printWindow.document.write("</head><body onload='window.print()'>");

    var newStatement = $(divId).clone().prop({ id: "newStatementDiv", name: "newStatementDiv" });
    newStatement.find("#statementList").attr("id", "newStatementList");
    var newStatementList = newStatement.find("#newStatementList");

    newStatementList.find("tbody").html("");
    if (result.statements.length == 0) {
        newStatementList.append("<tr><td colspan='9'><br /><br /><br /><br /><br /><br /><br /><br /><div style='font-size: 28px; color: red; text-align:center;'>No record found</div></td></tr>");
    } else {
        var trClass = "";
        $.each(result.statements, function (n, statement) {
            var tr = "<tr" + trClass + ">";
            tr += "<td><a class='lbl-link' href='schedule/daysheet?OfficeId=" + statement.officeId + "&Date=" + statement.appointmentDate + "'>" + statement.appointmentDate + "</a></td>";
            tr += "<td>" + statement.doctorName + "</td>";

            if (result.patientRecordId > 0) {
                tr += "<td>" + statement.payor + "</td>";
            } else {
                tr += "<td>" + statement.patientName + "</td>";
                //tr += "<td><span class='lbl-link'>" + getPatientInfo(statement.patientRecordId, statement.patientName) + "</span></td>";
            }

            tr += "<td>" + statement.diagnosis + "</td>";
            tr += "<td>" + statement.servicesRendered + "</td>";
            tr += "<td>" + statement.serviceCode + "</td>";
            tr += "<td>" + statement.referringDoctor + "</td>";
            tr += "<td>" + statement.numberOfService + "</td>";
            tr += "<td>" + statement.fee + "</td>";
            tr += "<td>" + statement.billStatus + "</td>";
            tr += "</tr>";
            newStatementList.append(tr);
            if (trClass == "") {
                trClass = " class='active'";
            } else {
                trClass = "";
            }
        });
        newStatementList.append("<tr" + trClass + "><td colspan='8' align='right' style='margin-right: 32px; padding-right: 32px;'>Balance Owing:</td><td>" + balanceOwing + "</td><td></td></tr>");
    }

    //remove all anchor (invisible in print mode)
    newStatement.find("a").each(function () {
        $(this).replaceWith($(this).text());
    });

    var divContents = newStatement[0].outerHTML;
    printWindow.document.write(divContents);
    printWindow.document.write("</body></html>");
    printWindow.document.close();
}

function statementOfficeBillingClicked() {
    var billingType = $("input:radio[name=statementOfficeBilling]:checked").val();
    if (billingType == 1) {
        $(".office-bill-field").show();
        $(".hospital-bill-field").hide();
    } else {
        $(".office-bill-field").hide();
        $(".hospital-bill-field").show();
    }
}