﻿using Cerebrum30.Areas.Measurements.DataAccess;
using Cerebrum30.Areas.VP.DataAccess;
using Cerebrum30.Areas.VP.Models.ViewModels;
using LocalVP = Cerebrum30.Areas.VP.Models.ViewModels;
using Cerebrum30.DAL.DataAccess.Repositories;
using Cerebrum.ViewModels.VP;
using Cerebrum30.Utility;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.Filters;
using AwareMD.WorkSheet.Dto.PlaceHolder;
using Cerebrum30.Areas.PdfConversions;
using Cerebrum30.Utility.HL7;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum30.Utility;
using static Cerebrum30.Utility.AspNetCoreCompatibility;
using Cerebrum30.Controllers;
using Cerebrum30.Areas.Medications.DataAccess;
using System.Configuration;
using Cerebrum30.Filters;
using DifferenceEngine;
using System.Collections;
using Cerebrum30.Areas.Measurements.Models.DataObjects;
using Cerebrum.ViewModels.VP;
using Cerebrum.ViewModels.Doctor;
using Newtonsoft.Json;
using System.Globalization;
using Cerebrum.Data;
using Cerebrum.BLL.VP;
using Cerebrum.BLL.Measurements;
using Cerebrum.BLL.User;
using Cerebrum.ContactManager;
using Cerebrum30.Security;
using Cerebrum.ViewModels.Documents;
using Cerebrum.BLL;
using System.Net.Http;
using Cerebrum.ContactManager;

namespace Cerebrum30.Areas.VP.Controllers
{

    // Note: ValidateInput attribute not needed in ASP.NET Core - input validation is handled differently
    //[CheckPermissions(PermissionType = "vp")]
    [CheckPermission]
    [Area("VP")]
    public class VPController : BaseController
    {
        private VPBLL _vp;
        private MeasurementBLL _measurement;
        private UserBLL _user;
        private IBookingConfirmationBLL _bookingConfirm;
        VPRepository _vpRepo;
        UserRepository _userRepo;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IContactManagerService _contactManagerService;
        private readonly IUnitOfWorkPatientAllergy _unitOfWorkPatientAllergy;
        private CerebrumContext _context;

        public VPController(VPRepository vpRepo, UserRepository userRepo, IBookingConfirmationBLL bookingConfirm, IHttpClientFactory httpClientFactory, IContactManagerService contactManagerService, VPBLL vp, MeasurementBLL measurement, UserBLL user, IUnitOfWorkPatientAllergy unitOfWorkPatientAllergy, CerebrumContext context)
        {
            _httpClientFactory = httpClientFactory;
            _context = context;

            _vpRepo = vpRepo == null ? new VPRepository() : vpRepo;
            _bookingConfirm = bookingConfirm;
            _contactManagerService = contactManagerService;
            _userRepo = userRepo;
            _vp = vp;
            _measurement = measurement;
            _user = user;
            _unitOfWorkPatientAllergy = unitOfWorkPatientAllergy;
        }

        #region Local constants
        public readonly int PRACTICEID = 0;
        public readonly string RELOAD_CPP = "if(opener!=null && opener.LoadCPP){opener.LoadCPP();}";

        readonly string NEW_OPTIONS = "NEW_OPTIONS";
        readonly string ADD_RISK_ERROR = "ADD_RISK_ERROR";
        readonly string EDIT_RISK_ERROR = "EDIT_RISK_ERROR";
        readonly string EDIT_RISK = "EDIT_RISK";
        readonly string CPP_PROBLEMLIST_ADDERROR = "CPP_PROBLEMLIST_ADDERROR";
        readonly string CPP_PROBLEMLIST_EDITERROR = "CPP_PROBLEMLIST_EDITERROR";
        readonly string CPP_PROBLEMLIST_EDIT = "CPP_PROBLEMLIST_EDIT";
        readonly string ADD_CUSTOMIZE_RP = "ADD_CUSTOMIZE_RP";
        readonly string SCRIPT_TO_EXECUTE = "SCRIPT_TO_EXECUTE";
        readonly string NEW_LOG = "NEW_LOG";
        #endregion

        public ActionResult Index(int AppointmentID, int AppointmentTestID, bool isWorkList = false)
        {
            LocalVP.VP_VM vm = new LocalVP.VP_VM()
            {
                PracticeId = CerebrumUser.PracticeId,
                AppointmentID = AppointmentID,
                AppointmentTestID = AppointmentTestID,
                ReportPhraseFormatTable = RootPhraseFormat.Table.ToString()
            };

            //loading data
            var bllVm = new Cerebrum.ViewModels.VP.VP_VM()
            {
                PracticeId = vm.PracticeId,
                AppointmentID = vm.AppointmentID,
                AppointmentTestID = vm.AppointmentTestID
            };
            _vp.LoadVisitSummary(bllVm, HttpContext.User.Identity.Name);

            // Copy back the loaded data
            vm.DoctorID = bllVm.DoctorID;
            // TODO: Map AppointmentPracticeDoctorID property if available
            // vm.AppointmentPracticeDoctorID = bllVm.AppointmentPracticeDoctorID;
            vm.UserID = bllVm.UserID;
            vm.PatientID = bllVm.PatientID;
            vm.OfficeID = bllVm.OfficeID;
            vm.SpecialityID = bllVm.SpecialityID;
            vm.RootPhraseFormat = bllVm.RootPhraseFormat;
            vm.DateStr = bllVm.DateStr;
            vm.TestID = bllVm.TestID;
            vm.IsClassicAppointment = bllVm.IsClassicAppointment;

            vm.ReportCompleted = _measurement.GetTestStatus(AppointmentTestID) == (int)AppointmentTestStatuses.ReportCompleted;
            vm.AppointmentTestLogs = GetLogList(vm.AppointmentID, vm.PatientID);
            vm.AppointmentTestLogID = TempData[NEW_LOG] != null ? (int)TempData[NEW_LOG] : GetMaxLogID(vm.AppointmentTestLogs);

            _vp.LoadReportPhrases(bllVm, CerebrumUser.PracticeDoctorId);
            // Copy report phrases back
            // TODO: Use proper TopLevelReportPhrase mapping
            vm.ReportPhrases = bllVm.ReportPhrases?.Select(rp => new VMTopLevelReportPhrase
            {
                Id = rp.Id,
                Name = rp.Name,
                Value = rp.Value,
                Skipped = rp.Skipped,
                Accumulative = rp.Accumulative,
                Bolded = rp.Bolded
            }).ToList() ?? new List<VMTopLevelReportPhrase>();

            LoadMeasurements(vm);

            LoadCPP(vm);
            vm.PrivacyCount = _vp.GetPrivacyNotes(vm.UserID, vm.PatientID).Count;
            vm.VsHistory = LoadHistory(vm.AppointmentID);
            //vm.AssociatedDocs = _vp.GetAssociatedDoctors(vm.PatientID);

            var vw = View(vm);
            return vw;
        }

        [HttpPost]
        //[CheckPermissions(PermissionType = "vp", Permissions = "doctor")]
        public ActionResult Index(LocalVP.VP_VM model)
        {

            TempData[NEW_OPTIONS] = model.Options;

            TempData[NEW_LOG] = model.AppointmentTestLogID;

            return RedirectToAction("Index", new
            {
                AppointmentID = model.AppointmentID,
                AppointmentTestID = model.AppointmentTestID

            });
        }

        public ActionResult NoAccess()
        {
            return View();
        }

        public ActionResult _PatientName(int PatientID)
        {
            var name = _vp.GetPatientName(PatientID);
            return PartialView("_PatientName", name);
        }

        public ActionResult Options(int docID)
        {
            var vm = new LocalVP.VP_VM();

            vm.DoctorID = docID;
            vm.Options = _vp.GetOptions();

            vm.Options = _vp.LoadPatientOptions(vm.DoctorID, vm.Options);

            return View(vm);
        }

        [HttpPost]
        public JsonResult Save_Options(LocalVP.VP_VM vm)
        {
            bool errored = false;

            try
            {
                _vp.SaveOptions(vm.DoctorID, vm.Options);
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0"
            });
        }

        [ResponseCache(NoStore = true, Duration = 0)]
        public ActionResult GetPhrases(LocalVP.VP_VM vm)
        {
            // Convert local VP_VM to global VP_VM for BLL call
            var bllVm = ConvertToGlobalVP_VM(vm);
            _vp.LoadReportPhrases(bllVm, CerebrumUser.PracticeDoctorId);

            // Convert back to local VP_VM for processing
            vm = ConvertToLocalVP_VM(bllVm);

            // Convert to global VP_VM for the view
            var globalVm = ConvertToGlobalVP_VM(vm);
            return PartialView("ReportPhrases", globalVm);

        }

        [ResponseCache(NoStore = true, Duration = 0)]
        public ActionResult _Measurements(LocalVP.VP_VM vm)
        {
            LoadMeasurements(vm);
            // Convert to global VP_VM for the view
            var globalVm = ConvertToGlobalVP_VM(vm);
            return PartialView("Measurements", globalVm);
        }

        public ActionResult VP_TemplateDetail()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [ResponseCache(NoStore = true, Duration = 0)]
        public ActionResult CPP(LocalVP.VP_VM vm)
        {
            LoadCPP(vm);

            // Convert to global VP_VM for the view
            var globalVm = ConvertToGlobalVP_VM(vm);
            return PartialView("CPP", globalVm);
        }

        public ActionResult CPP_Patient(int PatientID)
        {
            LocalVP.VP_VM vm = new LocalVP.VP_VM();

            vm.PatientID = PatientID;
            vm.UserID = _userRepo.GetUserID(HttpContext.User.Identity.Name);

            if (vm.AppointmentID > 0)
                vm.DoctorID = _vp.GetPracticeDoctor(vm.AppointmentID);
            else
                vm.DoctorID = _vp.GetMainResponsiblePhysician(PatientID);
            vm.PracticeDoctorID = vm.DoctorID;

            // Convert to global VP_VM for the view
            var globalVm = ConvertToGlobalVP_VM(vm);
            return View(globalVm);
        }

        public ActionResult VP_Links(LocalVP.VP_VM vm)
        {
            vm.PrivacyCount = _vp.GetPrivacyNotes(vm.UserID, vm.PatientID).Count;

            // Convert to global VP_VM for the view
            var globalVm = ConvertToGlobalVP_VM(vm);
            return PartialView("VP_Links", globalVm);
        }

        // TODO: Find the right place to move!
        /*
        public ActionResult MultipleLetter_VP_Pdf(int appointmentId)
        {

            PdfTestController pdfController = new PdfTestController();
            pdfController.ControllerContext = this.ControllerContext;

            List<byte[]> contentList = new List<byte[]>();

            var appts = _vp.GetPreviousAppointmentsExclClassic(_vp.GetPatientByAppointment(appointmentId)).OrderByDescending(o => o.Date).ToList();

            int vpTestID = _vp.GetVPTestID();
            foreach (var a in appts)
            {
                //getting contact list 
                DoctorNames lstDocNames = new DoctorNames();
                lstDocNames.VPLetterType = VPLetterType.SendLetter;

                // original:
                var docLst = this._vpRepo.GetContactList(a.AppointmentID, lstDocNames, vpTestID);
                //var docLst = _vp.GetContactList(CerebrumUser.PracticeId, a.AppointmentID);

                docLst.Add(new VMDoctorReport() { Name = "Test Emil Recepient", Email = true, EmailAddress = "<EMAIL>", DocType = DocType.Test });

                int patientID = _userRepo.GetPatientByAppointment(a.AppointmentID);

                int mainDocID = 1;
                int refDocID = 1;
                int ccDocID = 1;

                var mainDoc = docLst.Where(e => e.DocType == DocType.Reporting).ToList().FirstOrDefault();
                var refDoc = docLst.Where(e => e.DocType == DocType.Referral).ToList().FirstOrDefault();
                var ccDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();

                if (mainDoc != null)
                {
                    mainDocID = mainDoc.ID;
                }
                if (refDoc != null)
                {
                    refDocID = refDoc.ID;
                }
                if (ccDoc != null)
                {
                    ccDocID = ccDoc.ID;
                }

                //getting contact list 

                int officeID = _userRepo.GetOfficeIDByAppointment(appointmentId);

                var reportingDoc = _vp.GetDoctor(CerebrumUser.UserId);
                int reportingDocId = reportingDoc != null ? reportingDoc.DoctorId : 0;
                //TODO, take off hard coded doctor IDs

                var data = pdfController.GetContentForLetterPdf_VP(
                           System.Web.HttpContext.Current,
                           a.AppointmentID,
                           patientID,
                           officeID,
                           mainDocID, //main
                           refDocID, //refdoc
                           ccDocID, //ccdoc
                           _userRepo.GetPracticeAppointment(a.AppointmentID),
                           lstDocNames, reportingDocId);//+

                if (data != null)
                    contentList.Add(data);

            }//);

            byte[] contents = null;
            if (contentList.Count > 0)
            {
                contents = Cerebrum30.Areas.PdfConversions.DataAccess.PdfMerger.MergeFilesForServiceC3(contentList);
            }

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        } */


        public ActionResult MultipleLetter_VP_PdfByPatient(int patientID)
        {
            PdfTestController pdfController = null;// new PdfTestController(_bookingConfirm);
            // TODO: Fix ControllerContext for ASP.NET Core - constructor signature changed
            // In ASP.NET Core, ControllerContext is typically set by the framework or through DI
            // pdfController.ControllerContext = new ControllerContext(this.Request.RequestContext, pdfController);
            pdfController.ControllerContext = this.ControllerContext; // Use current controller's context as temporary fix

            List<byte[]> contentList = new List<byte[]>();
            var appts = (_vp.GetPreviousAppointmentsExclClassic(patientID)).OrderByDescending(t => t.Date).ToList();
            int vpTestID = _vp.GetVPTestID();

            appts.ForEach(a =>
            {
                //getting contact list
                DoctorNames lstDocNames = new DoctorNames();
                lstDocNames.VPLetterType = VPLetterType.SendLetter;

                //original:
                var docLst = this._vpRepo.GetContactList(a.AppointmentID, lstDocNames, vpTestID);
                //var docLst = _vp.GetContactList(CerebrumUser.PracticeId, a.AppointmentID);

                int mainDocID = 1;
                int refDocID = 1;
                int ccDocID = 1;

                var mainDoc = docLst.Where(e => e.DocType == DocType.Reporting).ToList().FirstOrDefault();
                var refDoc = docLst.Where(e => e.DocType == DocType.Referral).ToList().FirstOrDefault();
                var ccDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();

                if (mainDoc != null)
                {
                    mainDocID = mainDoc.ID;
                }
                if (refDoc != null)
                {
                    refDocID = refDoc.ID;
                }
                if (ccDoc != null)
                {
                    ccDocID = ccDoc.ID;
                }
                //getting contact list

                int officeID = _userRepo.GetOfficeIDByAppointment(a.AppointmentID);

                //TODO, take off hard coded doctor IDs

                var data = pdfController.GetContentForLetterPdf_VP(
                           System.Web.HttpContext.Current,
                           a.AppointmentID,
                           patientID,
                           officeID,
                           mainDocID, //main
                           refDocID, //refdoc
                           ccDocID, //ccdoc
                           _userRepo.GetPracticeAppointment(a.AppointmentID),
                           lstDocNames);//+

                if (data != null)
                    contentList.Add(data);

            });

            byte[] contents = null;
            if (contentList.Count > 0)
            {
                contents = Cerebrum30.Areas.PdfConversions.DataAccess.PdfMerger.MergeFilesForServiceC3(contentList);
            }

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        public ActionResult ConsultCodes(int specialityID, string cntrlid)
        {
            VMConsultCodeControl vm = new VMConsultCodeControl();

            vm.ControlID = cntrlid;

            vm.Codes = _vp.GetConsultCodes(string.Empty, specialityID);
            vm.Codes = vm.Codes.OrderBy(x => x.Code).ToList();

            return View(vm);
        }

        public ActionResult DiagnoseCodes(int specialityID, string cntrlid)
        {
            VMDiagnoseCodeControl vm = new VMDiagnoseCodeControl();

            vm.ControlID = cntrlid;

            vm.Codes = _vp.GetDiagnosticCodes(specialityID, string.Empty);
            vm.Codes = vm.Codes.OrderBy(x => x.Code).ToList();

            return View(vm);
        }

        public ActionResult EditMeasurements(int userID, int DocID)
        {
            LocalVP.VP_VM vm = new LocalVP.VP_VM();

            var result = _vp.GetUniqueCategories();
            vm.VitalSignCategories = result.Item1;
            vm.LabResultCategories = result.Item2;
            vm.DoctorID = DocID;

            _vp.LoadCustomMeasurementValues(vm.VitalSignCategories, vm.LabResultCategories, DocID);

            return View(vm);
        }

        [HttpPost]
        public JsonResult SaveMeasurements(LocalVP.VP_VM vm)
        {
            bool errored = false;
            try
            {
                _vp.SaveCustomMeasurements(vm.VitalSignCategories, vm.LabResultCategories, vm.DoctorID);
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0"
            });
        }

        public ActionResult GetMedications(int patientId, int appointmentId)
        {
            //Medication_VM medications = new Medication_VM();
            //var visitStartDt = DateTime.MinValue;
            //var visitEndDt = _vp.GetAppointmentDate(appointmentId);
            //visitEndDt = visitEndDt.Value.AbsoluteStart();

            //medications.PatientID = patientId;

            //var patientMedications = (new PatientMedicationRepository()).GetPatientMedicationsInDateRange(patientId, visitStartDt, visitEndDt.Value).ToList();

            //medications.Prior = _vp.GetPriorMedications(patientMedications, appointmentId);

            //medications.Added = _vp.GetAddedMedications(patientMedications, appointmentId);

            //medications.Discontinued = _vp.GetDiscontinuedMedications(patientMedications, appointmentId);

            //medications.DoseChanged = _vp.GetDoseChangedMedications(patientMedications, appointmentId);

            //medications.Allergies = (new UnitOfWorkPatientAllergy()).allergyRep.GetPatientAllergies(patientId, visitStartDt, visitEndDt.Value);


            VMVPMedication vm = new VMVPMedication();

            vm.PatientID = patientId;

            var visitStartDt = DateTime.MinValue;

            var visitEndDt = _vpRepo.GetAppointmentDate(appointmentId);
            visitEndDt = visitEndDt.Value.AbsoluteStart();

            var lstMedication = _vpRepo.GetMedications(patientId, visitStartDt, visitEndDt.Value);

            var prior = lstMedication.ToList();

            prior.ForEach(p =>
            {
                if (p.DiscontinuedMedications.Count > 0)
                {
                    if (p.DateStarted < visitEndDt.Value)
                    {
                        if (!p.DateDiscontinued.HasValue)
                            vm.Prior.Add(p);
                        else
                        if (p.DateDiscontinued.Value >= visitEndDt.Value)
                        {
                            vm.Prior.Add(p);
                        }
                    }
                    else
                    {
                        foreach (var med in p.DiscontinuedMedications)
                        {
                            if (med.DateDiscontinued.HasValue && med.DateDiscontinued.Value >= visitEndDt.Value)
                            {
                                vm.Prior.Add(med);
                                break;
                            }
                        }
                    }
                }
                else
                {
                    if (p.DateDiscontinued.HasValue)
                    {
                        if (p.DateDiscontinued.Value >= visitEndDt.Value)
                            vm.Prior.Add(p);
                    }
                    else
                    if (p.DateStarted < visitEndDt.Value)
                    {
                        vm.Prior.Add(p);
                    }
                }
            });

            vm.Added = lstMedication.Where(x =>
                                                x.DateStarted.Date.AbsoluteStart() == visitEndDt.Value &&
                                                (!x.DateDiscontinued.HasValue || x.DateDiscontinued >= visitEndDt.Value)
                                                && x.DiscontinuedMedications.Count == 0)

                                                .ToList();

            vm.Discontinued = lstMedication.Where(x => x.IsDiscontinued == true && x.DateDiscontinued.Value.Date.AbsoluteStart() == visitEndDt).ToList();

            lstMedication.ForEach(m =>
            {
                if (m.DiscontinuedMedications.Count > 0 && m.DateStarted.AbsoluteStart() == visitEndDt)
                {
                    vm.DoseChanged.Add(m);
                }
            });

            vm.Allergies = _unitOfWorkPatientAllergy.allergyRep.GetPatientAllergies(patientId, visitStartDt, visitEndDt.Value);


            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult GetMedicationsByPatient(int patientID)
        {


            VMVPMedication vm = new VMVPMedication();

            vm.PatientID = patientID;

            var visitStartDt = DateTime.MinValue;

            var visitEndDt = DateTime.Now;
            visitEndDt = visitEndDt.AbsoluteStart();

            PatientMedicationRepository patRepo = new PatientMedicationRepository();
            var lstMedication = patRepo.GetPatientMedicationsInDateRange(patientID, visitStartDt, visitEndDt);

            var prior = lstMedication.OrderBy(o => o.DateCreated).ToList();

            prior.ForEach(p =>
            {
                if (p.DiscontinuedMedications.Count > 0)
                {
                    if (p.DateStarted < visitEndDt)
                    {
                        if (!p.DateDiscontinued.HasValue)
                            vm.Prior.Add(p);
                        else
                        if (p.DateDiscontinued.Value >= visitEndDt)
                        {
                            vm.Prior.Add(p);
                        }
                    }
                    else
                    {
                        foreach (var med in p.DiscontinuedMedications)
                        {
                            if (med.DateDiscontinued.HasValue && med.DateDiscontinued.Value >= visitEndDt)
                            {
                                vm.Prior.Add(med);
                                break;
                            }
                        }
                    }
                }
                else
                {
                    if (p.DateDiscontinued.HasValue)
                    {
                        if (p.DateDiscontinued.Value >= visitEndDt)
                            vm.Prior.Add(p);
                    }
                    else
                    if (p.DateStarted < visitEndDt)
                    {
                        vm.Prior.Add(p);
                    }
                }
            });

            vm.Added = lstMedication.Where(x =>
                                                x.DateStarted.Date.AbsoluteStart() == visitEndDt &&
                                                (!x.DateDiscontinued.HasValue || x.DateDiscontinued >= visitEndDt)
                                                && x.DiscontinuedMedications.Count == 0)
                                                .OrderBy(O => O.DateCreated).ToList();

            vm.Discontinued = lstMedication.Where(x => x.IsDiscontinued == true && x.DateDiscontinued.Value.Date.AbsoluteStart() == visitEndDt).OrderBy(O => O.DateCreated).ToList();

            lstMedication.ForEach(m =>
            {
                if (m.DiscontinuedMedications.Count > 0 && m.DateStarted.AbsoluteStart() == visitEndDt)
                {
                    vm.DoseChanged.Add(m);
                }
            });

            vm.Allergies = _unitOfWorkPatientAllergy.allergyRep.GetPatientAllergies(patientID, visitStartDt, visitEndDt);


            return View("GetMedications", vm);
        }

        [HttpPost]
        public ActionResult UpdateOptions(LocalVP.VP_VM model)
        {

            TempData[NEW_OPTIONS] = model.Options;

            return RedirectToAction("Index", new
            {
                AppointmentID = model.AppointmentID,
                AppointmentTestID = model.AppointmentTestID

            });
        }

        public bool BP_Values_Valid(List<VMVisitMeasurement> lst)
        {
            bool valid = false;

            try
            {
                int dummy = 0;
                string[] bp = new string[] { "BP (right arm)", "BP (left arm)", "BP sitting", "BP standing" };
                var entry = lst.Where(x => bp.Contains(x.Name) && (!string.IsNullOrEmpty(x.Value))).FirstOrDefault();

                if (entry != null)
                {
                    if (!string.IsNullOrEmpty(entry.Value))
                    {
                        var bpvalues = entry.Value.Split("/".ToCharArray());

                        if (bpvalues.Length > 1)
                        {
                            string sysStr = bpvalues[0];
                            string dysStr = bpvalues[1];

                            if (Int32.TryParse(sysStr, out dummy) &&
                                Int32.TryParse(dysStr, out dummy))
                            {
                                valid = true;
                            }
                            else
                            {
                                valid = true;
                            }
                        }

                    }
                }
                else
                {
                    valid = true;
                }
            }
            catch (Exception)
            {
                valid = false;

            }
            return valid;
        }

        public VMVisitMeasurement GetBloodPressureMeasurement(List<VMVisitMeasurement> lst)
        {
            VMVisitMeasurement retVal = null;
            string[] bp = new string[] { "BP (right arm)", "BP (left arm)", "BP sitting", "BP standing" };
            var entry = lst.Where(x => bp.Contains(x.Name) && (!string.IsNullOrEmpty(x.Value))).FirstOrDefault();
            if (entry != null)
            {
                retVal = entry;
            }
            return retVal;
        }

        private string BMI_Calculation(List<VMVisitMeasurement> l)
        {
            decimal weight = 0;
            decimal height = 0;
            decimal bmi = 0;
            decimal dummy = 0;
            var weightMeas = l.Where(s => s.Name == "Weight (Metric)").FirstOrDefault();
            var heightMeas = l.Where(s => s.Name == "Height (Metric)").FirstOrDefault();
            var bmiMeas = l.Where(s => s.Name == "BMI").FirstOrDefault();

            if (weightMeas != null)
            {
                if (!string.IsNullOrEmpty(weightMeas.Value) && decimal.TryParse(weightMeas.Value, out dummy))
                    weight = decimal.Parse(weightMeas.Value);
            }
            if (heightMeas != null)
            {
                if (!string.IsNullOrEmpty(heightMeas.Value) && decimal.TryParse(heightMeas.Value, out dummy))
                    height = decimal.Parse(heightMeas.Value);
            }
            if (bmiMeas != null)
            {
                if (!string.IsNullOrEmpty(bmiMeas.Value) && decimal.TryParse(bmiMeas.Value, out dummy))
                    bmi = decimal.Parse(bmiMeas.Value);
            }

            if (bmi <= 0 && (weight > 0 && height > 0))
            {
                bmiMeas.Value = (10000 * (weight / (height * height))).ToString("N0");
            }

            return bmiMeas != null ? bmiMeas.Value : null;
        }

        public Tuple<List<VMVisitMeasurement>, List<VMTemplateDetail>> Extract_BP_Values(LocalVP.VP_VM vm, List<VMVisitMeasurement> lst, List<VMTemplateDetail> cdf)
        {
            if (cdf != null && cdf.Count() > 0)
            {
                try
                {
                    int dummy = 0;

                    string[] bp = new string[] { "BP (right arm)", "BP (left arm)", "BP sitting", "BP standing" };
                    var entry = lst.Where(x => bp.Contains(x.Name) && (!string.IsNullOrEmpty(x.Value))).FirstOrDefault();

                    if (entry != null)
                    {
                        if (!string.IsNullOrEmpty(entry.Value))
                        {
                            var bpvalues = entry.Value.Split("/".ToCharArray());

                            if (bpvalues.Length > 1)
                            {
                                string sysStr = string.Empty;
                                string dysStr = string.Empty;

                                int sys = 0;
                                int dys = 0;

                                sysStr = bpvalues[0];
                                dysStr = bpvalues[1];

                                if (Int32.TryParse(sysStr, out dummy))
                                {
                                    sys = Int32.Parse(sysStr);
                                    var cdft = cdf.FirstOrDefault(sd => sd.TemplateItemName.ToLower().Equals("bp-sys"));
                                    cdft.Value = sysStr;
                                }

                                if (Int32.TryParse(dysStr, out dummy))
                                {
                                    dys = Int32.Parse(dysStr);
                                    var cdft = cdf.FirstOrDefault(sd => sd.TemplateItemName.ToLower().Equals("bp-dys"));
                                    cdft.Value = dysStr;
                                }

                                var userId = CerebrumUser.UserId;
                                var ipAddress = GetIPAddress();
                                _vp.AddBloodPressure(vm.AppointmentID, vm.PatientID, vm.AppointmentTestLogID, sys, dys, userId, ipAddress);
                            }
                        }
                    }
                }
                catch (Exception)
                {


                }
            }
            return new Tuple<List<VMVisitMeasurement>, List<VMTemplateDetail>>(lst, cdf);
        }

        public List<VMVisitMeasurement> Extract_BP_Values(LocalVP.VP_VM vm, List<VMVisitMeasurement> lst)
        {
            try
            {
                int dummy = 0;

                var entry = lst.Where(x => x.Name == "BP (right arm)" && !string.IsNullOrEmpty(x.Value)).FirstOrDefault();

                if (entry != null)
                {
                    if (!string.IsNullOrEmpty(entry.Value))
                    {
                        var bpvalues = entry.Value.Split("/".ToCharArray());

                        if (bpvalues.Length > 1)
                        {
                            string sysStr = string.Empty;
                            string dysStr = string.Empty;

                            int sys = 0;
                            int dys = 0;

                            sysStr = bpvalues[0];
                            dysStr = bpvalues[1];

                            if (Int32.TryParse(sysStr, out dummy))
                                sys = Int32.Parse(sysStr);

                            if (Int32.TryParse(dysStr, out dummy))
                                dys = Int32.Parse(dysStr);

                            var userId = CerebrumUser.UserId;
                            var ipAddress = GetIPAddress();
                            _vp.AddBloodPressure(vm.AppointmentID, vm.PatientID, vm.AppointmentTestLogID, sys, dys, userId, ipAddress);
                        }
                    }
                }
            }
            catch (Exception)
            {
            }
            return lst;
        }

        public bool DataSaved(LocalVP.VP_VM model)
        {
            #region Checking for validation 

            //clear errors
            model.LabResultCategories.ForEach(c =>
           c.Measurements.ForEach(m => m.ErrorMessage = string.Empty));

            model.VitalSignCategories.ForEach(c =>
          c.Measurements.ForEach(m => m.ErrorMessage = string.Empty));
            #endregion

            bool invalidValueFound = false;

            var labMeas = model.LabResultCategories.SelectMany(c => c.Measurements).Distinct();
            var vitalsMeas = model.VitalSignCategories.SelectMany(c => c.Measurements).Distinct();
            var cfdMeas = model.vm_cdf.TemplateDetails.ToList();

            int fieldCount = labMeas.Count() + vitalsMeas.Count() + cfdMeas.Count;
            var phraseCount = model.ReportPhrases.Count;

            invalidValueFound =

                (fieldCount == 0) ||
                phraseCount == 0 ||
                labMeas.Where(m => !string.IsNullOrEmpty(m.ErrorMessage)).Count() > 0 ||
                vitalsMeas.Where(m => !string.IsNullOrEmpty(m.ErrorMessage)).Count() > 0;
            // ||  cfdMeas.Where(m => !string.IsNullOrEmpty(m.ErrorMessage)).Count() > 0;

            var bpMeas = GetBloodPressureMeasurement(vitalsMeas.ToList());
            if (!BP_Values_Valid(vitalsMeas.ToList()))
            {
                invalidValueFound = true;
                bpMeas.ErrorMessage = "BP has to be in valid format";
            }
            else if (bpMeas != null)
            {
                bpMeas.ErrorMessage = string.Empty;
            }
            var bmimeas = BMI_Calculation(vitalsMeas.ToList());

            if (!invalidValueFound)
            {
                List<VMVisitMeasurementSavedValue> lstVS = new List<VMVisitMeasurementSavedValue>();
                List<VMVisitMeasurementSavedValue> lstLabs = new List<VMVisitMeasurementSavedValue>();
                List<VMVisitMeasurementSavedValue> lstCDF = new List<VMVisitMeasurementSavedValue>();
                List<VMVisitMeasurementSavedValue> lstSaveFinal = new List<VMVisitMeasurementSavedValue>();
                int newLogID = _vp.CreateLog(model.AppointmentID, model.PatientID, model.Finalized, CerebrumUser.UserId, model.IPAddress);
                model.AppointmentTestLogID = newLogID;

                labMeas = labMeas.Where(m => !string.IsNullOrEmpty(m.Value));
                vitalsMeas = vitalsMeas.Where(m => !string.IsNullOrEmpty(m.Value));


                var extbp = Extract_BP_Values(model, vitalsMeas.ToList(), cfdMeas);
                vitalsMeas = extbp.Item1;
                cfdMeas = extbp.Item2;
                cfdMeas = cfdMeas.Where(m => !string.IsNullOrEmpty(m.Value)).ToList();
                foreach (var measure in labMeas)
                {
                    lstVS.Add(new VMVisitMeasurementSavedValue()
                    {
                        AppointmentId = model.AppointmentID,
                        PatientRecordId = model.PatientID,
                        VPMeasurementId = measure.Id,
                        Value = measure.Value,

                        VP_AppointmentTestLogId = newLogID,

                    });
                }

                foreach (var measure in vitalsMeas)
                {
                    lstLabs.Add(new VMVisitMeasurementSavedValue()
                    {
                        AppointmentId = model.AppointmentID,
                        PatientRecordId = model.PatientID,
                        VPMeasurementId = measure.Id,
                        Value = measure.Value,

                        VP_AppointmentTestLogId = newLogID,

                    });
                }

                foreach (var measure in cfdMeas)
                {
                    int outval = -1;
                    if (measure.ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.YesNo && int.TryParse(measure.Value, out outval))
                    {
                        measure.Value = ((YesNo)outval).ToString();
                    }
                    lstCDF.Add(new VMVisitMeasurementSavedValue()
                    {
                        AppointmentId = model.AppointmentID,
                        PatientRecordId = model.PatientID,
                        VPMeasurementId = measure.VPTemplateField,
                        Value = measure.Value,

                        VP_AppointmentTestLogId = newLogID,
                    });
                }

                lstSaveFinal.AddRange((lstVS.Concat(lstLabs)).Concat(lstCDF));

                var userId = CerebrumUser.UserId;
                var ipAddress = GetIPAddress();

                //save each report phrase
                _vp.Save_VP_ReportPhrases(model.ReportPhrases,
                                            model.AppointmentID,
                                            model.PatientID,
                                            newLogID, userId, ipAddress);
                //save each measurement
                _vp.Save_VP_Measurements(lstSaveFinal, userId, ipAddress);

                _vp.SaveOpeningStatement(model.AppointmentID, model.OpeningStatement, userId, ipAddress);

                //save cdf items 
                var cdf_TemplateDetails = _vp.GetTemplateDetailsByPatient(model.PatientID);
                var appdate = _vp.GetAppointmentDate(model.AppointmentID);
                _vp.SaveCDFData(lstSaveFinal, cdf_TemplateDetails, model.PatientID, appdate);

                //saving billing information if any provided
                //HARDCODED STATUS VALUES
                if (!model.AppointmentBillStatus.HasValue || model.AppointmentBillStatus == 1 || model.AppointmentBillStatus == 2)
                {

                    if (model.ConsultCode.Id != 0 ||
                    model.ConsultCode2.Id != 0 ||
                    model.ConsultCode3.Id != 0 ||
                    model.DiagnoseCode.Id.HasValue ||
                    model.DiagnoseCode2.Id.HasValue ||
                    model.DiagnoseCode3.Id.HasValue)
                    {
                        _vp.AddBillingCode_VP(model.AppointmentID,
                                                    model.ConsultCode.Id,
                                                    model.ConsultCode2.Id,
                                                    model.ConsultCode3.Id,
                                                    model.DiagnoseCode.Id.HasValue ? model.DiagnoseCode.Id.Value : 0,
                                                    model.DiagnoseCode2.Id.HasValue ? model.DiagnoseCode2.Id.Value : 0,
                                                    model.DiagnoseCode3.Id.HasValue ? model.DiagnoseCode3.Id.Value : 0,
                                                    userId, ipAddress);
                    }
                    else
                        _vp.RemoveBillingCode_VP(model.AppointmentID);
                }

            }

            bool dataSaved = !invalidValueFound;

            return dataSaved;
        }

        [HttpPost]
        public ActionResult CreateNewLog(LocalVP.VP_VM model)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {

                if (!DataSaved(model))
                {
                    var globalVm = ConvertToGlobalVP_VM(model);
                    return PartialView("Measurements", globalVm);
                }
            }
            catch (Exception exc)
            {
                message = exc.Message;
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Result = message
            });


        }

        public ActionResult Save_Draft(LocalVP.VP_VM model)
        {

            bool errored = false;

            try
            {
                bool saved = DataSaved(model);
                if (!saved)
                {
                    var globalVm = ConvertToGlobalVP_VM(model);
                    return PartialView("Measurements", globalVm);
                }
                int userid = CerebrumUser.UserId;
                string ipAddress = IPAddress(Request);
                _vp.UpdateTestStatus(model.AppointmentTestID, (int)AppointmentTestStatuses.ReadyForDoctor, userid, ipAddress);
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0"
            });

        }

        //  public JsonResult FinalizeChart(int appointmentID, int patientID, bool isAmended = false)
        public ActionResult FinalizeChart_OLD(LocalVP.VP_VM vm)
        {
            bool errored = false;
            int vpTestID = _vp.GetVPTestID();
            string mssg = "Changed Saved";
            string ipAddress = IPAddress(Request);
            try
            {
                bool saved = DataSaved(vm);
                if (!saved)
                {
                    var globalVm = ConvertToGlobalVP_VM(vm);
                    return PartialView("Measurements", globalVm);
                }

                // TODO: This should be moved out first!
                // var atRepo = new Cerebrum3.DataAccess.AppointmentTestRepository(_context);
                this._vpRepo.UpdateTestStatus(vm.AppointmentTestID, (int)AppointmentTestStatuses.ReadyForDoctor, CerebrumUser.UserId, ipAddress);

                int userid = CerebrumUser.UserId;

                var currentStatus = (int)AppointmentTestStatuses.ReportCompleted;
                //update test status first 
                this._vpRepo.UpdateTestStatus(vm.AppointmentTestID, currentStatus, userid, ipAddress);
            }
            catch (Exception exc)
            {
                errored = true;
                mssg = exc.ToString();
            }
            return Json(new { Errored = errored ? "1" : "0", Message = mssg });

        }
        /*        public ActionResult FinalizeChart_Old_2(LocalVP.VP_VM vm)
                {
                    bool errored = false;
                    int vpTestID = this._vpRepo.GetVPTestID();
                    string mssg = "Changed Saved";
                    StringBuilder sb = new StringBuilder();
                    int userid = CerebrumUser.UserId;
                    string ipaddress = Request.UserHostAddress;
                    vm.IPAddress = ipaddress;
                    vm.Finalized = 1;
                    try
                    {

                        bool saved = DataSaved(vm);
                        if (!saved)
                        {
                            var globalVm = ConvertToGlobalVP_VM(vm);
                            return PartialView("Measurements", globalVm);
                        }

                        if (CerebrumUser.IsTrainee)
                        {
                            //if user is trainee, change status ONLY 
                            this._vpRepo.UpdateTestStatus(vm.AppointmentTestID, (int)AppointmentTestStatuses.TraineeReportReady, userid, ipaddress);
                            this._vpRepo.AssignTrainee(vm.AppointmentTestID, CerebrumUser.UserId);
                        }
                        else
                        {
                            var currentStatus = (int)AppointmentTestStatuses.ReportCompleted;
                            //update test status first 
                            this._vpRepo.UpdateTestStatus(vm.AppointmentTestID, currentStatus, userid, ipaddress);
                        }

                        bool isAmended = vm.Amended;
                        PdfTestController pdfController = new PdfTestController();
                        pdfController.ControllerContext = this.ControllerContext;
                        //clean up user repo
                        int officeID = _userRepo.GetOfficeIDByAppointment(vm.AppointmentID);
                        //getting contact list 

                        DoctorNames lstDocNames = new DoctorNames();
                        lstDocNames.VPLetterType = VPLetterType.ChartNote;
                        lstDocNames.Amended = isAmended;

                        var trainee = this._vpRepo.GetTraineeUser(vm.AppointmentTestID);
                        lstDocNames.traineeExternalDoctorID = trainee.ExternalDoctorId;
                        lstDocNames.traineeUID = trainee.Id;
                        lstDocNames.traineeID = trainee.userId;
                        lstDocNames.traineeName = trainee.Name;

                        var docLst = this._vpRepo.GetContactList(vm.AppointmentID, lstDocNames, vpTestID);
                        docLst = docLst.Where(d => d.DocType != AwareMD.Cerebrum.Shared.DocType.Reporting).ToList();
                        var associateList = this._vpRepo.GetAssociatedContactList(vm.AppointmentID);

                        docLst.AddRange(associateList);
                        //docLst.Add(new Doctor_Report() { Name = "Test Emil Recepient", Email = true, EmailAddress = "<EMAIL>", DocType = DocType.Test });
                        int mainDocID = 1;
                        int refDocID = 1;
                        int ccDocID = 1;
                        var mainDoc = docLst.Where(e => e.DocType == DocType.Reporting).ToList().FirstOrDefault();
                        var refDoc = docLst.Where(e => e.DocType == DocType.Referral).ToList().FirstOrDefault();
                        var ccDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();
                        if (mainDoc != null)
                        {
                            mainDocID = mainDoc.ID;
                        }
                        if (refDoc != null)
                        {
                            refDocID = refDoc.ID;
                        }
                        if (ccDoc != null)
                        {
                            ccDocID = ccDoc.ID;
                        }
                        //getting contact list 
                        int reportdoctorid = 0;
                        if (CerebrumUser.IsDoctor)
                            reportdoctorid = _vp.GetDoctor(CerebrumUser.UserId).DoctorId;
                        //TODO, take off hard coded doctor IDs
                        var fileBytes = pdfController.GetContentForLetterPdf_VP(
                            System.Web.HttpContext.Current,
                            vm.AppointmentID,
                            vm.PatientID,
                            officeID,
                            mainDocID, //main
                            refDocID, //refdoc
                            ccDocID, //ccdoc
                            _userRepo.GetPracticeAppointment(vm.AppointmentID),
                            lstDocNames, reportdoctorid);//+

                        if (fileBytes == null)
                        {
                            sb.Append("File not found");
                            errored = true;
                        }

                        if (!errored)
                        {

                            var webUrl = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetUploadURL(officeID);

                            ServerLocationProvider locator = new ServerLocationProvider(vm.AppointmentID, ServerLocationProvider.VP_ID);
                            string locationSaved = locator.GetLocation(DataType.Letter, Module.VP);
                            string url = locator.GetURL(DataType.Letter, Module.VP);

                            locationSaved = AspNetCoreCompatibility.ServerHelper.MapPath("~/Areas/VP/uploads/") + locationSaved;

                            int userID = CerebrumUser.UserId;

                            string fullPath = string.Empty;
                            string fullURL = string.Empty;

                            string fileNameOriginal = "data.pdf";
                            string fileNameAmended = string.Format("amended_{0}.pdf", DateTime.Now.ToString()
                              .Replace(" ", "_")
                              .Replace("/", "_")
                              .Replace(":", "_"));

                            string fullPathOriginal = locationSaved + fileNameOriginal;
                            string fullURLOriginal = url + fileNameOriginal;
                            string fullPathAmended = locationSaved + fileNameAmended;
                            string fullURLAmended = url + fileNameAmended;

                            bool amended = false;

                            if (!Directory.Exists(locationSaved))
                            {
                                Directory.CreateDirectory(locationSaved);
                            }

                            if (isAmended) //generate amended file
                            {
                                System.IO.File.WriteAllBytes(fullPathAmended, fileBytes);
                                amended = true;

                                fullPath = fullPathAmended;
                                fullURL = fullURLAmended;
                            }
                            else
                            {
                                System.IO.File.WriteAllBytes(fullPathOriginal, fileBytes);

                                fullPath = fullPathOriginal;
                                fullURL = fullURLOriginal;
                            }


                            try
                            {
                                //TODO, putting file on clinic server
                                Helper.SendFile(fullPath, url, serverURL: webUrl);

                                SendReport_VM sendVM = new SendReport_VM()
                                {
                                    AppointmentId = vm.AppointmentID,
                                    PatientId = vm.PatientID,
                                    DateEntered = DateTime.Now,
                                    SendType = SendType.ClinicServer,
                                    Location = fullURL,
                                    Sent = true,
                                    Amended = amended,
                                    PhysicalPath = fullURL,
                                    URL = fullURL
                                };

                                this._vpRepo.AddSendReport(sendVM, userid, ipaddress);

                            }
                            catch (Exception exc)
                            {
                                errored = true;
                                sb.Append("Could not send file to clinic server");
                            }

                        }
                    }
                    catch (Exception exc)
                    {
                        errored = true;
                        mssg = exc.ToString();
                    }
                    return Json(new { Errored = errored ? "1" : "0", Message = mssg });

                }
        */
        public ActionResult HRMSentReportLog()
        {
            var notsent = _vpRepo.NotSentHRM();
            return View(notsent);
        }

        public void ReSendFax_HRM(int id, int appointmentId, int appointmentTestId, int patientId, int testid, string fileUrl)
        {
            LocalVP.VP_VM vm = new LocalVP.VP_VM { AppointmentID = appointmentId, PatientID = patientId };
            //Send_Report_PATH
            var pdffilepath = System.Configuration.ConfigurationManager.AppSettings["Send_Report_PATH"].ToString();

            StringBuilder sb = new StringBuilder();
            bool errored = false;

            int vpTestID = testid == 0 ? this._vpRepo.GetVPTestID() : testid;

            var appointmentTestID = appointmentTestId == 0 ? _userRepo.GetAppointmentTestID(vm.AppointmentID, vpTestID) : appointmentTestId;

            PdfTestController pdfController = null;// new PdfTestController(_bookingConfirm);
            pdfController.ControllerContext = this.ControllerContext;

            int officeID = _userRepo.GetOfficeIDByAppointment(vm.AppointmentID);
            //getting contact list 

            DoctorNames lstDocNames = new DoctorNames();
            lstDocNames.VPLetterType = VPLetterType.SendLetter;
            lstDocNames.Amended = false;

            int mainDocID = 1;
            int refDocID = 1;
            int ccDocID = 1;

            var docLst = this._vpRepo.GetContactList(vm.AppointmentID, lstDocNames, vpTestID);
            var mainDoc = docLst.FirstOrDefault(e => e.DocType == DocType.Reporting);
            var refDoc = docLst.Where(e => e.DocType == DocType.Referral).ToList().FirstOrDefault();
            var ccDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();

            var mainRefSame = from dl in docLst
                              group dl by dl.ID into ng
                              select new { ng.Key, Other = ng };
            foreach (var item in mainRefSame)
            {
                var cnt = item.Other.Count();
                var rfemai = item.Other.Count(a => a.DocType == DocType.Reporting || a.DocType == DocType.Referral);

                if (rfemai == 2)
                {
                    docLst = docLst.Where(d => d.DocType != DocType.Reporting).ToList();
                    docLst = docLst.Where(d => d.DocType != DocType.Referral).ToList();
                }
            }

            //In case 
            docLst = docLst.Where(d => d.DocType != DocType.Reporting).ToList();

            var associateList = this._vpRepo.GetAssociatedContactList(vm.AppointmentID);

            docLst.AddRange(associateList);

            if (mainDoc != null)
            {
                mainDocID = mainDoc.ID;
            }
            if (refDoc != null)
            {
                refDocID = refDoc.ID;
            }
            if (ccDoc != null)
            {
                ccDocID = ccDoc.ID;
            }

            byte[] fileBytes = null;
            ServerLocationProvider locator;
            string locationSaved = string.Empty;
            string url = string.Empty;
            string existingfile = string.Empty;

            try
            {
                existingfile = pdffilepath + Path.Combine(fileUrl.Substring(fileUrl.IndexOf("uploads") - 1), "");
            }
            catch (Exception e)
            {
                _log.Error(e.ToString());
            }
            if (!System.IO.File.Exists(existingfile))
            {
                fileBytes = pdfController.GetContentForLetterPdf_VP(
                    System.Web.HttpContext.Current,
                    vm.AppointmentID,
                    vm.PatientID,
                    officeID,
                    mainDocID, //main
                    refDocID, //refdoc
                    ccDocID, //ccdoc
                    _userRepo.GetPracticeAppointment(vm.AppointmentID),
                    lstDocNames);//+

                locator = new ServerLocationProvider(vm.AppointmentID, ServerLocationProvider.VP_ID);
                locationSaved = locator.GetLocation(AwareMD.Cerebrum.Shared.Enums.DataType.Letter, Module.VP);
                url = locator.GetURL(AwareMD.Cerebrum.Shared.Enums.DataType.Letter, Module.VP);

                locationSaved = AspNetCoreCompatibility.ServerHelper.MapPath("~/Areas/VP/uploads/") + locationSaved;

                int userID = CerebrumUser.UserId;

                string fullPath = string.Empty;
                string fullURL = string.Empty;

                string fileNameOriginal = "data.pdf";
                string fileNameAmended = string.Format("amended_{0}.pdf", DateTime.Now.ToString()
                  .Replace(" ", "_")
                  .Replace("/", "_")
                  .Replace(":", "_"));

                string fullPathOriginal = locationSaved + fileNameOriginal;
                string fullURLOriginal = url + fileNameOriginal;
                string fullPathAmended = locationSaved + fileNameAmended;
                string fullURLAmended = url + fileNameAmended;

                if (!Directory.Exists(locationSaved))
                {
                    Directory.CreateDirectory(locationSaved);
                }



                if (fileBytes == null)
                {
                    sb.Append("File not found");
                    errored = true;
                }
                else
                {
                    System.IO.File.WriteAllBytes(fullPathOriginal, fileBytes);
                }
                fileUrl = fullPathOriginal;
            }


            if (!errored)
            {

                var webUrl = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetUploadURL(officeID);


                if (!errored)
                {
                    // Remove main doctor
                    docLst = docLst.Where(w => w.DocType != DocType.Reporting).ToList();

                    foreach (var doc in docLst)
                    {
                        try
                        {

                            //doc.Fax = true;
                            if (doc.Fax)
                            {
                                bool faxSent = true;
                                try
                                {
                                    if (doc.ID > 0)
                                    {

                                        PDF_Writer writer = new PDF_Writer();

                                        var faxFileBytes = System.IO.File.ReadAllBytes(fileUrl);
                                        string faxFileName = this._vpRepo.GetXMLFileName(vm.AppointmentID, vm.PatientID, doc.ID);

                                        string fileName = Path.GetFileName(fileUrl);
                                        string faxFilePath = fileUrl.Replace(fileName, string.Empty) + @"\" + faxFileName;

                                        System.IO.File.WriteAllBytes(faxFilePath, faxFileBytes);
                                        //string xml = writer.ConvertPDFToXML(strBytes, fileName);

                                        //Helper.SendFile(faxFilePath, string.Empty, true, webUrl);
                                    }
                                }
                                catch (Exception excFax)
                                {
                                    faxSent = false;
                                    _log.Error(excFax);
                                }

                                if (faxSent)
                                {
                                    Cerebrum.ViewModels.VP.SendReport_VM sendVM = new Cerebrum.ViewModels.VP.SendReport_VM()
                                    {
                                        AppointmentId = vm.AppointmentID,
                                        PatientId = vm.PatientID,
                                        DateEntered = DateTime.Now,
                                        SendType = AwareMD.Cerebrum.Shared.Enums.SendType.Fax,
                                        Location = fileUrl,
                                        Sent = true,
                                        Amended = false,
                                        PhysicalPath = fileUrl,
                                        URL = fileUrl,
                                        FaxTo = webUrl,
                                        DocName = doc.Name
                                    };

                                    //this.repo.AddSendReport(sendVM, userid, ipaddress);
                                }

                                sb.Append(faxSent ? string.Format("Fax Sent to {0}", "https://c3lds.mycerebrum.com/00faxrec.asp") : "No Fax Sent").Append("<br>");
                            }
                        }
                        catch (Exception exc2)
                        {
                            var json = JsonConvert.SerializeObject(vm);
                            var error2 = $"{json} {exc2.ToString()}";
                            Cerebrum.ViewModels.VP.SendReport_VM sendVM = new Cerebrum.ViewModels.VP.SendReport_VM()
                            {
                                AppointmentId = vm.AppointmentID,
                                PatientId = vm.PatientID,
                                DateEntered = DateTime.Now,
                                SendType = AwareMD.Cerebrum.Shared.Enums.SendType.Fax,
                                Location = fileUrl,
                                Sent = false,
                                Amended = false,
                                PhysicalPath = fileUrl,
                                URL = webUrl,
                                FaxTo = "https://c3lds.mycerebrum.com/00faxrec.asp",
                                DocName = doc.Name,
                                ErrorMessage = error2
                            };
                        }
                        string error = string.Empty;
                        _log.Info($"HRM for {doc.LastName} docid: {doc.ID} HRM: {doc.HRM}");
                        if (doc.HRM && doc.ID > 0)
                        {
                            bool sent = true;
                            string hl7FileName = $"data_{doc.ID}_{vm.AppointmentID}_{vm.TestID}_{vm.PatientID}_{DateTime.Now.ToString("yyyyMMddHHmmssfff")}";
                            //string hrmPath = Server.MapPath(Constants.HRM_UPLOAD_DIR) + hl7FileName;
                            string hrmPath = System.Configuration.ConfigurationManager.AppSettings["HRM_PATH"].ToString() + hl7FileName;
                            var hl7 = new hl7creator(fileUrl);

                            try
                            {
                                HL7_Data_VM vm2 = this._vpRepo.LoadHL7_VM(vm.AppointmentID, vpTestID, doc.ID, vm.PatientID);

                                vm2.AppointmentID = vm.AppointmentID;
                                vm2.PatientID = vm.PatientID;
                                vm2.EmrDocID = doc.ID;
                                vm2.EmrDocumentID = appointmentTestID;
                                vm2.SendingDoctorLastName = mainDoc.LastName;
                                vm2.SendingDoctorFirstName = mainDoc.FirstName;
                                //vm2.DocLastName = doc.Name;
                                vm2.DocLastName = doc.LastName;
                                vm2.DocFirstName = doc.FirstName;

                                var hrmid = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetOfficeHRMID(officeID);
                                hl7.SendingFacility = hrmid;
                                hl7.ExamTypeShort = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetTestHRMShort(vpTestID);
                                hl7.ExamTypeLong = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetTestHRMLong(vpTestID);
                                hl7.Modality = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetTestHRMModality(vpTestID);

                                hl7.EmrDocID = vm2.EmrDocID.ToString();
                                hl7.EmrDocumentID = vm2.EmrDocumentID.ToString();

                                hl7.SendingDoctorLastName = mainDoc.LastName;
                                hl7.SendingDoctorFirstName = mainDoc.FirstName;

                                if (string.IsNullOrEmpty(hl7.ExamTypeShort) || string.IsNullOrEmpty(hl7.ExamTypeLong))
                                    throw new Exception($"HRM Send Erorr : Missing HRMtypeShort or HRMtypeLong for test {vm.TestID}");

                                hl7data data = new hl7data(hrmid, false);

                                data.Prepare(vm2, ref hl7, vm.AppointmentID);
                                hl7.Generate(hrmPath);
                            }

                            catch (Exception exc)
                            {
                                _log.Error($"HRM Sending ERROR: {exc.ToString()}");
                                sent = false;
                                error = exc.ToString();
                            }

                            Cerebrum.ViewModels.VP.SendReport_VM sendVM = new Cerebrum.ViewModels.VP.SendReport_VM()
                            {
                                Id = id,
                                AppointmentId = vm.AppointmentID,
                                PatientId = vm.PatientID,
                                DateEntered = DateTime.Now,
                                SendType = AwareMD.Cerebrum.Shared.Enums.SendType.HRM,
                                Location = fileUrl,
                                Sent = sent,
                                Amended = false,
                                PhysicalPath = hrmPath,
                                DocName = doc.Name,
                                ErrorMessage = error
                            };
                            sb.Append(sent ? "HRM Sent" : "HRM Not Sent").Append("<br>");
                        }
                    }
                }
            }
        }

        public ActionResult SendLetter(LocalVP.VP_VM vm)
        {
            int userid = CerebrumUser.UserId;
            string ipAddress = IPAddress(Request);
            bool isAmended = vm.Amended;
            bool errored = false;

            bool saved = DataSaved(vm);
            if (!saved)
            {
                var globalVm = ConvertToGlobalVP_VM(vm);
                return PartialView("Measurements", globalVm);
            }

            this._vpRepo.UpdateTestStatus(vm.AppointmentTestID, (int)AppointmentTestStatuses.ReadyForDoctor, userid, ipAddress);

            VMUserDoctor traineeDoctor = null;
            if (CerebrumUser.IsTrainee)
            {
                //if user is trainee, change status ONLY 
                this._vpRepo.UpdateTestStatus(vm.AppointmentTestID, (int)AppointmentTestStatuses.TraineeReportReady, userid, ipAddress);
                traineeDoctor = _vp.AssignTrainee(CerebrumUser.PracticeId, vm.AppointmentTestID, CerebrumUser.UserId, CerebrumUser.UserId, ipAddress);
                return Json(new { Errored = errored ? "1" : "0", Message = "Record updated by Trainee" });
            }

            var currentStatus = (int)AppointmentTestStatuses.ReportCompleted;

            //update test status first 
            _vp.UpdateTestStatus(vm.AppointmentTestID, currentStatus, userid, ipAddress);

            var reportManager = new Cerebrum.BLL.Documents.ReportManager(_httpClientFactory);
            var reportRequest = new VMVPReportRequest();
            reportRequest.LetterType = VPLetterType.SendLetter;
            reportRequest.AppointmentId = vm.AppointmentID;
            reportRequest.AppointmentTestId = vm.AppointmentTestID;
            reportRequest.AppointmentTestLogId = vm.AppointmentTestLogID;
            reportRequest.PatientId = vm.PatientID;
            reportRequest.IpAddress = CerebrumUser.IpAddress;
            reportRequest.UserId = CerebrumUser.UserId;
            reportRequest.UserFullName = CerebrumUser.LastName + " " + CerebrumUser.FirstName;
            reportRequest.IsDoctor = CerebrumUser.PracticeDoctorId > 0 ? true : false;
            reportRequest.IsAmended = vm.Amended;


            var response = reportManager.SendVPToCLinic(reportRequest, true);
            errored = response.Sent ? false : true;

            string message = response.Message;


            return Json(new { Errored = errored ? "1" : "0", Message = message });

        }

        public ActionResult FinalizeChart(LocalVP.VP_VM vm)
        {
            bool errored = false;
            int vpTestID = this._vpRepo.GetVPTestID();
            string mssg = "Changed Saved";
            StringBuilder sb = new StringBuilder();
            int userid = CerebrumUser.UserId;
            string ipAddress = IPAddress(Request);
            vm.IPAddress = ipAddress;
            vm.Finalized = 1;
            try
            {

                bool saved = DataSaved(vm);
                if (!saved)
                {
                    var globalVm = ConvertToGlobalVP_VM(vm);
                    return PartialView("Measurements", globalVm);
                }

                if (CerebrumUser.IsTrainee)
                {
                    //if user is trainee, change status ONLY 
                    this._vpRepo.UpdateTestStatus(vm.AppointmentTestID, (int)AppointmentTestStatuses.TraineeReportReady, userid, ipAddress);
                    this._vpRepo.AssignTrainee(vm.AppointmentTestID, CerebrumUser.UserId, CerebrumUser.UserId, GetIPAddress());
                }
                else
                {
                    var currentStatus = (int)AppointmentTestStatuses.ReportCompleted;
                    //update test status first 
                    this._vpRepo.UpdateTestStatus(vm.AppointmentTestID, currentStatus, userid, ipAddress);
                }

                var reportManager = new Cerebrum.BLL.Documents.ReportManager(_httpClientFactory);
                var reportRequest = new VMVPReportRequest();
                reportRequest.LetterType = VPLetterType.ChartNote;
                reportRequest.AppointmentId = vm.AppointmentID;
                reportRequest.AppointmentTestId = vm.AppointmentTestID;
                reportRequest.AppointmentTestLogId = vm.AppointmentTestLogID;
                reportRequest.PatientId = vm.PatientID;
                reportRequest.IpAddress = CerebrumUser.IpAddress;
                reportRequest.UserId = CerebrumUser.UserId;
                reportRequest.UserFullName = CerebrumUser.LastName + " " + CerebrumUser.FirstName;
                reportRequest.IsDoctor = CerebrumUser.PracticeDoctorId > 0 ? true : false;
                reportRequest.IsAmended = vm.Amended;


                var response = reportManager.SendVPToCLinic(reportRequest);
                errored = response.Sent ? false : true;
                mssg = response.Message;


            }
            catch (Exception exc)
            {
                errored = true;
                mssg = exc.ToString();
            }
            return Json(new { Errored = errored ? "1" : "0", Message = mssg });

        }

        [HttpPost]
        public ActionResult Go(LocalVP.VP_VM model)
        {
            //TempData[NEW_TEST_ID] = model.TestID;
            //TempData[NEW_APP_ID] = model.AppointmentID;
            //TempData[NEW_PATIENT_ID] = model.PatientID;

            //appid and testid changed 
            return RedirectToAction("Index", new { AppointmentID = model.AppointmentID, AppointmentTestID = model.AppointmentTestID });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Comorbid(int PatientID, int appointmentID)
        {

            CoMorbid_VM vm = new CoMorbid_VM();

            StringBuilder sbPastMedical = new StringBuilder();
            StringBuilder sbProblemList = new StringBuilder();
            StringBuilder sbMedicationList = new StringBuilder();

            var visitStartDt = DateTime.MinValue;
            var visitEndDt = _userRepo.GetAppointmentDate(appointmentID) ?? (DateTime?)DateTime.Now;

            var visitDtCPP = visitEndDt != null ? visitEndDt.Value.AbsoluteEnd() : DateTime.Now;
            var lstProblem = this._vpRepo.GetCPP_ProblemList(PatientID, true, visitStartDt, visitDtCPP);
            var lstHealth = this._vpRepo.GetCPP_ProblemList(PatientID, false, visitStartDt, visitDtCPP);

            var visitDtMed = visitEndDt.Value.AbsoluteStart();
            var lstMedication = this._vpRepo.GetMedications(PatientID, visitStartDt, visitDtMed).Where(m => m.IsDiscontinued == false).ToList();

            if (lstProblem.Count > 0)
            {
                lstProblem.ForEach(x =>
                   sbProblemList.Append(x.Problem_Description)
                   .Append(" ")
                   .Append(x.DateOfOnset.ToString())
                   .Append(" ")
                   .Append(x.Life_Stage)
                   .Append(Environment.NewLine));
            }

            if (lstHealth.Count > 0)
            {
                lstHealth.ForEach(x =>
                   sbPastMedical.Append(x.Problem_Description)
                   .Append(" ")
                   .Append(x.DateOfOnset.HasValue ? x.DateOfOnset.Value.ToShortDateString() : string.Empty)
                   .Append(" ")
                   .Append(x.Life_Stage)
                   .Append(Environment.NewLine));
            }

            if (lstMedication.Count > 0)
            {
                lstMedication.ForEach(x =>
                   sbMedicationList.Append(x.MedicationName).Append(" ").Append(x.Dose).Append(" ").Append(x.SIG)
                   .Append(Environment.NewLine));
            }


            vm.PastHelath = sbPastMedical.ToString();
            vm.ProblemList = sbProblemList.ToString();
            vm.Medications = sbMedicationList.ToString();

            return View(vm);
        }

        public byte[] GetLettersFiles(List<int> apptIDs, int? vpLetterType)
        {//VPLetterType
            // In ASP.NET Core, use Headers.Accept instead of AcceptTypes
            var s = Request.Headers.Accept.ToString();

            PdfTestController pdfController = null;// new PdfTestController(_bookingConfirm);
            // TODO: In ASP.NET Core, ControllerContext has different constructor and RequestContext doesn't exist
            // pdfController.ControllerContext = new ControllerContext(this.Request.RequestContext, pdfController);

            List<byte[]> contentList = new List<byte[]>();

            var vpTestID = _vpRepo.GetVPTestID();

            apptIDs.ForEach(aid =>
            {

                //getting contact list 
                DoctorNames lstDocNames = new DoctorNames();
                var docLst = this._vpRepo.GetContactList(aid, lstDocNames, vpTestID);

                docLst.Add(new VMDoctorReport() { Name = "Test Emil Recepient", Email = true, EmailAddress = "<EMAIL>", DocType = DocType.Test });

                int patientID = _userRepo.GetPatientByAppointment(aid);

                int mainDocID = 1;
                int refDocID = 1;
                int ccDocID = 1;

                var mainDoc = docLst.Where(e => e.DocType == DocType.Reporting).ToList().FirstOrDefault();
                var refDoc = docLst.Where(e => e.DocType == DocType.Referral).ToList().FirstOrDefault();
                var ccDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();

                if (mainDoc != null)
                {
                    mainDocID = mainDoc.ID;
                }
                if (refDoc != null)
                {
                    refDocID = refDoc.ID;
                }
                if (ccDoc != null)
                {
                    ccDocID = ccDoc.ID;
                }
                //getting contact list 

                int officeID = _userRepo.GetOfficeIDByAppointment(aid);

                //update vpLetterType if parameter is not null
                if (vpLetterType != null)
                {
                    if (lstDocNames != null)
                    {
                        if (vpLetterType == 0)
                        {
                            lstDocNames.VPLetterType = VPLetterType.ChartNote;
                        }
                        else if (vpLetterType == 1)
                        {
                            lstDocNames.VPLetterType = VPLetterType.SendLetter;
                        }
                    }
                }

                //TODO, take off hard coded doctor IDs
                contentList.Add(pdfController.GetContentForLetterPdf_VP(
                           System.Web.HttpContext.Current,
                           aid,
                           patientID,
                           officeID,
                           mainDocID, //main
                           refDocID, //refdoc
                           ccDocID, //ccdoc
                           _userRepo.GetPracticeAppointment(aid),
                           lstDocNames));//+

            });


            var contents = Cerebrum30.Areas.PdfConversions.DataAccess.PdfMerger.MergeFilesForServiceC3(contentList);

            return contents;
        }

        public ActionResult CDF_Templates(int patientID)
        {
            Cerebrum30.Areas.VP.Models.ViewModels.CDF_VM vm = new Cerebrum30.Areas.VP.Models.ViewModels.CDF_VM();

            vm.PatientID = patientID;

            vm.Templates = _vp.GetAllTemplates(CerebrumUser.IsDoctor, CerebrumUser.UserId, CerebrumUser.PracticeId, patientID);

            return View(vm);

        }

        public ActionResult GetAssociatedDoctors(int patientID)
        {
            var vm = _vp.GetAssociatedDoctors(patientID);

            return PartialView("CCDocList", vm);
        }


        [HttpGet]
        public ActionResult GetAssociateDoctorsSearch(string searchTerm)
        {

            var searchResults = _vp.GetAssociateDoctorsBySearch(searchTerm);

            var jsonResult = Json(searchResults);
            return jsonResult;

        }

        public ActionResult AddAssociateDoctor(int docID, int patientID)
        {
            int userid = CerebrumUser.UserId;
            string ipAddress = IPAddress(Request);

            string message = string.Empty;
            bool errored = false;

            try
            {
                if (docID == 0)
                {
                    errored = true;
                    message = "Please select doctor";
                }
                else
                {
                    var added = _vp.AddAssociateDoctor(docID, patientID, userid, ipAddress);
                    if (!added)
                    {
                        errored = true;
                        message = "Already exists";
                    }
                }
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            var jsonResult = Json(new { Errored = errored ? "1" : "0", Message = message });
            return jsonResult;

        }

        public ActionResult RemoveAssociateDoctor(int docID, int patientID)
        {
            string message = string.Empty;
            bool errored = false;

            try
            {
                _vp.RemoveAssociateDoctor(docID, patientID);
            }
            catch (Exception ex)
            {
                errored = true;
                message = ex.Message;
            }

            var jsonResult = Json(new { Errored = errored ? "1" : "0", Message = message });
            return jsonResult;
        }


        #region Private Methods


        private void LoadMeasurements(LocalVP.VP_VM vm)
        {
            var result = _vp.GetUniqueCategories();

            var measurements = _vp.GetCustomMeasurements(vm.DoctorID);
            vm.VitalSignCategories = _vp.GetVitalSignCategories(result.Item1, measurements);
            vm.LabResultCategories = _vp.GeLabResultCategories(result.Item2, measurements);

            //_vp.LoadCustomMeasurements(vm, vm.DoctorID);

            #region Load  CDF
            vm.vm_cdf.PatientID = vm.PatientID;
            vm.vm_cdf.TemplateDetails = _vp.GetTemplateDetailsByPatient(vm.PatientID);
            //taking off cdf items already present in existing categories
            vm.vm_cdf.TemplateDetails = _vp.ExcludeExistingMeas(vm.vm_cdf.TemplateDetails, vm.VitalSignCategories, vm.LabResultCategories);
            #endregion

            var lstSources = new List<Cerebrum.ViewModels.VP.DataSource>();
            var apptDate = _vp.GetAppointmentDate(vm.AppointmentID);

            #region  getting dates for all sources
            var results = _vp.TestResultsByLoinc(vm.PatientID, apptDate.Value);
            if (results.Count > 0)
            {
                vm.HL7_CollectionDate = results[0].CollectionDate;
            }
            vm.DB_CollectionDate = _vp.GetDbDate(vm.AppointmentID, vm.PatientID, vm.AppointmentTestLogID);
            vm.CDF_CollectionDate = _vp.GetLatestCDFDate(vm.PatientID, apptDate.Value);

            if (vm.HL7_CollectionDate.HasValue)
            {
                lstSources.Add(new Cerebrum.ViewModels.VP.DataSource()
                {
                    Name = "HL7",
                    Source = AwareMD.Cerebrum.Shared.Enums.DataSources.HL7,
                    Date = vm.HL7_CollectionDate
                });
            }

            if (vm.DB_CollectionDate.HasValue)
            {
                lstSources.Add(new Cerebrum.ViewModels.VP.DataSource()
                {
                    Name = "DB",
                    Source = AwareMD.Cerebrum.Shared.Enums.DataSources.DB,
                    Date = vm.DB_CollectionDate
                });
            }

            if (vm.CDF_CollectionDate.HasValue)
            {
                lstSources.Add(new Cerebrum.ViewModels.VP.DataSource()
                {
                    Name = "CDF",
                    Source = AwareMD.Cerebrum.Shared.Enums.DataSources.CDF,
                    Date = vm.CDF_CollectionDate
                });
            }
            #endregion

            #region Load DB, HL7, CDF
            foreach (var x in lstSources.OrderBy(x => x.Date).ToList())
            {
                switch (x.Source)
                {
                    case AwareMD.Cerebrum.Shared.Enums.DataSources.DB:
                        _vp.LoadSavedValues(
                           vm.VitalSignCategories,
                           vm.LabResultCategories,
                           vm.vm_cdf.TemplateDetails,
                           vm.AppointmentID,
                           vm.PatientID,
                           vm.AppointmentTestLogID, vm.CDF_CollectionDate, apptDate);
                        break;

                    case AwareMD.Cerebrum.Shared.Enums.DataSources.HL7:
                        vm.LabResultCategories = _vp.LoadLabValuesByLoinc(vm.LabResultCategories, results);
                        vm.vm_cdf.TemplateDetails = _vp.LoadValuesByLoinc(vm.vm_cdf.TemplateDetails, results);
                        break;

                    case AwareMD.Cerebrum.Shared.Enums.DataSources.CDF:
                        vm.VitalSignCategories = _vp.LoadCDFValues(vm.VitalSignCategories, vm.PatientID, apptDate.Value);
                        vm.LabResultCategories = _vp.LoadCDFValues(vm.LabResultCategories, vm.PatientID, apptDate.Value);
                        vm.vm_cdf.TemplateDetails = _vp.GetTemplatePatientData(vm.vm_cdf.TemplateDetails, vm.PatientID, apptDate.Value);
                        break;

                    default:
                        break;
                }
            }
            #endregion

            if (lstSources.Count > 0)
            {
                vm.Source = lstSources.OrderByDescending(x => x.Date).ToList().FirstOrDefault().Name;
            }

            return;
        }

        private void LoadCPP(LocalVP.VP_VM vm)
        {
            var visitStartDt = DateTime.MinValue;
            var visitEndDt = DateTime.MaxValue.AddDays(-10);

            vm.CPP_Categories = _vp.GetCPPCats();

            if (vm.DoctorID > 0)
            {
                vm.CPP_Categories = _vp.GetCustomCPP(vm.DoctorID, vm.CPP_Categories);

                vm.CPP_Categories = _vp.GetCPPSkipped(vm.CPP_Categories, vm.DoctorID, vm.PatientID);

                bool allergy, medication;
                _vp.GetMedicationVisible(vm.DoctorID, out allergy, out medication);

                vm.AllergyVisible = allergy;
                vm.MedicationVisible = medication;
            }

            int practiceDoctorId = 0;
            if (vm.AppointmentID > 0)
            {
                var appointment = _vp.GetAppointment(vm.AppointmentID);
                if (appointment != null)
                {
                    practiceDoctorId = appointment.PracticeDoctorId;
                    visitEndDt = appointment.appointmentTime.AbsoluteEnd();
                }
            }
            else
            {
                practiceDoctorId = _vp.GetMainResponsiblePhysician(vm.PatientID);
            }
            vm.PracticeDoctorID = practiceDoctorId;
            vm.CPP_Categories = _vp.GetCPPCatValues(vm.CPP_Categories, vm.PatientID, practiceDoctorId, visitStartDt, visitEndDt);

            return;
        }

        private string compareLines(string s1, string s2)
        {
            if (string.IsNullOrWhiteSpace(s1)) // old string empty
            {
                if (string.IsNullOrWhiteSpace(s2))
                {
                    return string.Empty;
                }
                // text added
                return addText(s2, TType.Added);
            }
            if (string.IsNullOrWhiteSpace(s2)) // new string empty
            {
                if (string.IsNullOrWhiteSpace(s1))
                {
                    return string.Empty;
                }
                // text deleted
                return addText(s1, TType.Deleted);
            }

            string result = string.Empty;

            DiffList_CharData c1 = new DiffList_CharData(s1.Trim());
            DiffList_CharData c2 = new DiffList_CharData(s2.Trim());
            DiffEngine de = new DiffEngine();
            double time = de.ProcessDiff(c1, c2, DiffEngineLevel.SlowPerfect);

            ArrayList rep = de.DiffReport();
            // when results empty or has only 1 NoChange segment - return empty string
            if (rep == null) return string.Empty;
            if (rep.Count < 1) return string.Empty;
            if (rep.Count == 1)
            {
                DiffResultSpan dr = (DiffResultSpan)rep[0];

                if (dr.Status == DiffResultSpanStatus.NoChange)
                {
                    return string.Empty;
                }
            }

            int idx = 0;
            foreach (DiffResultSpan drs in rep)
            {
                switch (drs.Status)
                {
                    case DiffResultSpanStatus.NoChange:
                        result += addText(s1.Substring(drs.SourceIndex, drs.Length), TType.Normal);
                        break;
                    case DiffResultSpanStatus.DeleteSource:
                        result += addText(s1.Substring(drs.SourceIndex, drs.Length), TType.Deleted);
                        break;
                    case DiffResultSpanStatus.AddDestination:
                        result += addText(s2.Substring(drs.DestIndex, drs.Length), TType.Added);
                        break;
                    case DiffResultSpanStatus.Replace:
                        result += addText(s2.Substring(drs.DestIndex, drs.Length), TType.Changed);
                        break;
                }
                idx++;
            }

            //var result = s1;
            //var result2 = s2;

            return result;
        }

        private string addText(string line, TType tp)
        {
            string retHtml = string.Empty;


            //Color color = Color.Black;
            //Color bkColor = Color.White;

            string color = string.Empty;
            string bkColor = string.Empty;

            switch (tp)
            {
                case TType.Added:
                    //color = Color.White;
                    //bkColor = Color.Blue;
                    color = "white";
                    bkColor = "blue";
                    break;
                case TType.Changed:
                    //bkColor = Color.Green;
                    //color = Color.White;
                    color = "white";
                    bkColor = "green";

                    break;

                case TType.Deleted:
                    //bkColor = Color.Red;
                    //color = Color.White;
                    color = "white";
                    bkColor = "red";
                    break;
            }

            //if (rtxDiff.TextLength > 0)
            //{
            //    //rtxDiff.SelectionColor = rtxDiff.ForeColor;
            //    //rtxDiff.AppendText(" ");
            //}
            //rtxDiff.SelectionStart = rtxDiff.TextLength;
            //rtxDiff.SelectionLength = 0;

            //rtxDiff.SelectionBackColor = bkColor;
            //rtxDiff.SelectionColor = color;
            //rtxDiff.AppendText(line);

            retHtml = string.Format("<span style='background-color:{0};color:{1}'>{2}</span>", bkColor, color, line);
            return retHtml;
        }

        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetAppointmentList()
        {
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository measurementRepo = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            var appointments = measurementRepo.GetAppointments();

            appointments.ForEach
               (
                   l => lst.Add(new SelectListItem
                   {
                       Value = l.ToString(),
                       Text = l.ToString()
                   }
              ));
            //TODO , remove this
            //for (int i = 0; i < 10; i++)
            //{
            //    lst.Add(new SelectListItem() { Text = i.ToString(), Value = i.ToString() });
            //}

            return lst;
        }

        private List<int> vals()
        {
            return new List<int> {19748,19666,19666,19666,20193,19593,19694,20156,20156,20073,20348,21288,19069,21059,22358,22353,
                                  22353,19605,22385,22298,19361,19715,19882,20551,21276,20589,19386,19386,20269,19459,19704,20157,20598,22406,22406,19324,22482,19759,19728,18839,
                                  19755,19755,20246,19902,20689,20181,20181,20272,19412,19959,19082,19411,19973,20275,19714,19201,20681,20141,19975,19975,18838,19366,20925,20925,
                                  20925,19651,20174,19674,20245,19587,19398
            };
        }

        private int GetMaxLogID(List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst)
        {

            int retVal = -1;

            lst = (from c in lst
                   where c.Value != "-1"
                   select c).ToList();

            if (lst != null && lst.Count > 0)
            {
                var maxID = lst.OrderByDescending(l => Int32.Parse(l.Value)).ToList()[0];

                if (maxID != null)

                    retVal = Int32.Parse(maxID.Value);
            }

            return retVal;
        }



        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetLogList(int apptID, int patientID)
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            lst.Add(new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem() { Value = "-1", Text = "---" });

            var logs = _vp.GetLogs(apptID, patientID).OrderByDescending(L => L.Id).ToList();

            logs.ForEach
                (
                    l => lst.Add(new SelectListItem
                    {
                        Value = l.Id.ToString(),
                        Text = l.Date.Value.ToString() + "---" + l.UserName
                    }
               ));

            return lst;
        }

        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetPatientList()
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            lst.Add(new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem() { Value = "-1", Text = "---" });

            var logs = _vp.GetPatientIDList().ToList();

            logs.ForEach
                (
                    l => lst.Add(new SelectListItem
                    {
                        Value = l.ToString(),
                        Text = l.ToString()
                    }
               ));

            return lst;
        }

        public JsonResult GetVSHistory(int appointmentID)
        {

            List<VMVitalSignHistory> lstVSHistory = new List<VMVitalSignHistory>();

            lstVSHistory = LoadHistory(appointmentID);

            return Json(new { Result = lstVSHistory });
        }


        public ActionResult CDF(int PatientID)
        {
            return View();
        }

        public ActionResult VP_Menu(int appointmentID)
        {

            List<VMVitalSignHistory> lstVSHistory = new List<VMVitalSignHistory>();

            lstVSHistory = LoadHistory(appointmentID);

            return View(lstVSHistory);
        }

        private List<VMVitalSignHistory> LoadHistory(int appointmentID)
        {
            List<VMVitalSignHistory> history = _user.GetPreviousAppointments(appointmentID);
            history = _vp.GetVitalSignValues(history);
            history = _vp.ShowNonEmptyValues(history);
            return history;
        }

        #endregion

        #region Billing Codes
        [HttpGet]
        public ActionResult GetConsultCode(string searchTerm, int specialityID)
        {

            var searchResults = _vp.GetConsultCodes(searchTerm, specialityID);

            var jsonResult = Json(searchResults);
            return jsonResult;

        }

        public JsonResult GetConsultCodeList(string term)
        {
            var searchResults = _vp.GetConsultCodeList(term);
            var results = searchResults.Select(a => new { label = a.text, value = a.value }).OrderBy(a => a.label).ToList();
            var jsonResult = Json(results);
            return jsonResult;
        }

        public JsonResult GetDiagnosticCodeList(string term)
        {
            var searchResults = _vp.GetDiagnosticCodeList(term);
            var results = searchResults.Select(a => new { label = a.text, value = a.value }).OrderBy(a => a.label).ToList();
            var jsonResult = Json(results);
            return jsonResult;
        }

        [HttpGet]
        public ActionResult GetDiagnosticCode(int specialityID, string searchTerm)
        {

            var searchResults = _vp.GetDiagnosticCodes(specialityID, searchTerm);

            var jsonResult = Json(searchResults);
            return jsonResult;

        }
        #endregion 

        #region CDF

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Print_CDF(List<DateTime> idLst, List<int> colLst, int patientID)
        {

            VMCDF vm = new VMCDF();

            vm.HeaderItems = _vp.GetTemplateHeadersByPatient(patientID);

            vm.Items = _vp.GetPrintData(idLst, colLst, vm.HeaderItems, patientID, CerebrumUser.PracticeId).OrderBy(o => o.Date).ToList();

            vm.PatientName = _vp.GetPatientName(patientID);

            return View(vm);
        }

        public ActionResult TemplateList(int patientID)
        {
            Cerebrum30.Areas.VP.Models.ViewModels.CDF_VM vm = new Cerebrum30.Areas.VP.Models.ViewModels.CDF_VM();

            vm.PatientID = patientID;

            vm.Templates = _vp.GetAllTemplates(CerebrumUser.IsDoctor, CerebrumUser.UserId, CerebrumUser.PracticeId, patientID);

            vm.TemplateItemList = _vp.GetAllTemplateItems(patientID);

            vm.SavedTemplateID = _vp.GetSavedTemplateByPatient(patientID);

            return View(vm);
        }

        [HttpPost]
        public ActionResult TemplateListPost(string json, string templateName, int PatientID)
        {
            IList<VMTemplateDetail> vm =
            Newtonsoft.Json.JsonConvert.DeserializeObject<IList<VMTemplateDetail>>(json);

            string mssg = string.Empty;
            //bool errored = false;
            try
            {
                vm = vm.GroupBy(i => i.VPTemplateField)
                  .Select(grp => grp.First())
                  .ToList();

                _vp.SaveTemplateDetailsByPatient(vm, templateName, PatientID, CerebrumUser.PracticeId);
            }
            catch (Exception exc)
            {
                mssg = exc.ToString();
                //errored = true;
                _log.Error(exc);
            }

            return RedirectToAction("TemplateList", new { patientID = PatientID });
        }


        [HttpPost]
        public ActionResult SaveTemplateData(
                                            string json,
                                            string templateName,
                                            int PatientID,
                                            int templateID
                                            )
        {
            IList<VMTemplateDetail> vm =
            Newtonsoft.Json.JsonConvert.DeserializeObject<IList<VMTemplateDetail>>(json);

            string mssg = string.Empty;
            //bool errored = false;
            try
            {
                _vpRepo.SaveTemplateDataByDoctor(vm, templateName, templateID, CerebrumUser.UserId, CerebrumUser.PracticeId, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                mssg = exc.ToString();
                //errored = true;
                _log.Error(exc);
            }

            return RedirectToAction("TemplateList", new { patientID = PatientID });
        }

        [HttpPost]
        public ActionResult SaveTemplateDataByPractice(
                                            string json,
                                            string templateName,
                                            int PatientID,
                                            int templateID
                                            )
        {
            IList<VMTemplateDetail> vm =
            Newtonsoft.Json.JsonConvert.DeserializeObject<IList<VMTemplateDetail>>(json);

            string mssg = string.Empty;
            // bool errored = false;
            try
            {
                _vpRepo.SaveTemplateDataByPractice(vm, templateName, templateID, CerebrumUser.PracticeId, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                mssg = exc.ToString();
                //errored = true;
                _log.Error(exc.ToString());
            }

            return RedirectToAction("TemplateList", new { patientID = PatientID });
        }

        public ActionResult TemplateData(int patientID, int? appointmentID = 0)
        {
            if (appointmentID == 0)
            {
                var app = _vpRepo.GetPatientLatestAppointment(CerebrumUser.PracticeId, patientID);
                if (app != null)
                    appointmentID = app.Id;
            }
            VMTemplatePatientData vm = new VMTemplatePatientData();
            vm.PatientID = patientID;
            vm.AppointmentID = (int)appointmentID;

            var AppointmentTestLogs = GetLogList((int)appointmentID, patientID);
            vm.AppointmentTestLogID = _vpRepo.GetMaxLogID(AppointmentTestLogs);

            //vm.TemplateID = repo.GetSavedTemplateByPatient(patientID);
            vm.HeaderItems = _vpRepo.GetTemplateHeadersByPatient(vm.PatientID);
            //vm.Logs = repo.GetTemplatePatientData(vm.HeaderItems, vm.PatientID,string.Empty,string.Empty);
            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult TemplateHistory(
                                                int patientID,
                                                int appointmentID,
                                                int appointmentTestLogID,
                                                string dateFrom,
                                                string dateTo,
                                                bool OutSideTarget,
                                                bool OutSideNormal,
                                                bool OutSideInterval)
        {
            VMTemplatePatientData vm = new VMTemplatePatientData();
            //vm.IncludeMedication = includeMedication;
            vm.PatientID = patientID;


            //vm.TemplateID = repo.GetSavedTemplateByPatient(patientID);

            vm.HeaderItems = _vpRepo.GetTemplateHeadersByPatient(vm.PatientID);

            vm.Logs = _vpRepo.GetTemplatePatientData(vm.HeaderItems, vm.PatientID, dateFrom, dateTo);

            var lstImmuneLog = _vpRepo.GetTemplateInfluenzaVaccineData(CerebrumUser.PracticeId, vm.HeaderItems, vm.PatientID, dateFrom, dateTo);
            var lstPneumo = _vpRepo.GetTemplatePneumoVaccineData(CerebrumUser.PracticeId, vm.HeaderItems, vm.PatientID, dateFrom, dateTo);

            vm.Logs = vm.Logs.Concat(lstImmuneLog).Concat(lstPneumo).OrderByDescending(x => x.Date).ToList();

            var results = this._vpRepo.TestResultsByLOINC(patientID, dateFrom, dateTo);
            results = this._vpRepo.AssignMeasurement(results);

            vm.Logs = this._vpRepo.MergeLOINC(results, vm.HeaderItems, vm.Logs);

            vm.Logs = _vpRepo.ApplyFilters(vm.Logs, OutSideTarget, OutSideNormal, OutSideInterval, vm);

            vm.HeaderItems = _vpRepo.ApplyOutOfDateFilter(vm.HeaderItems, vm.Logs);

            vm.OutSideInterval = OutSideInterval;
            vm.OutSideTarget = OutSideInterval;
            vm.OutSideNormal = OutSideNormal;


            var visitStartDt = DateTime.MinValue;
            var visitEndDt = _userRepo.GetAppointmentDate(appointmentID);
            var visitDtMed = visitEndDt != null ? visitEndDt.Value.AbsoluteStart() : DateTime.Now;
            //if (vm.IncludeMedication)
            //vm.Medications = this.repo.GetMedications(patientID, visitStartDt, visitDtMed).Where(m => m.IsDiscontinued == false).ToList();
            // Convert local VMCDFPatientMedication to shared VMCDFPatientMedication
            var localMedications = this._vpRepo.CDFPatientMedications(patientID, visitStartDt, visitDtMed);
            vm.PatientMedications = new Cerebrum.ViewModels.VP.VMCDFPatientMedication
            {
                DateStarted = localMedications.DateStarted,
                CDFTemplateMedications = localMedications.CDFTemplateMedications,
                Medications = localMedications.Medications
            };
            return View(vm);
        }

        public ActionResult AddTemplate()
        {
            VMTemplate vm = new VMTemplate();

            return View(vm);
        }

        public JsonResult AddConsultCode(int specialityID, int consultCodeId)
        {
            bool errored = false;
            int id = 0;
            string consultCode = string.Empty;

            try
            {
                id = _vp.AddConsultCode(specialityID, consultCodeId, out consultCode);
                if (id == 0)
                    errored = true;
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                id = id,
                consultCode = consultCode
            });
        }

        public JsonResult AddDiagnosticCode(int specialityID, int diagnosticCodeId)
        {
            bool errored = false;
            int id = 0;

            try
            {
                id = _vp.AddDiagnosticCode(specialityID, diagnosticCodeId);
                if (id == 0)
                    errored = true;
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                id = id
            });
        }

        public ActionResult AddNewMeasurement()
        {
            VMVisitMeasurement vm = new VMVisitMeasurement();

            return PartialView(vm);
        }

        public ActionResult EditMeasurement(int mid)
        {
            VMVisitMeasurement vm = new VMVisitMeasurement();

            vm = _vp.GetMeasurement(mid);

            return View(vm);
        }

        [HttpPost]
        public JsonResult SaveNewMeasurement(VMVisitMeasurement vm)
        {
            bool errored = false;
            string error = string.Empty;

            try
            {
                if (string.IsNullOrEmpty(vm.Name))
                {
                    errored = true;
                    error = "Missing Measurement Name";
                }
                else
                {
                    _vp.AddMeasurement(vm);
                }
            }
            catch (Exception exc)
            {
                errored = true;
                error = exc.ToString();
            }

            return Json(new { Errored = errored ? "1" : "0", Message = error });
        }

        [HttpPost]
        public JsonResult SaveExistingMeasurement(VMVisitMeasurement vm)
        {
            bool errored = false;
            string error = string.Empty;

            try
            {
                _vp.EditMeasurement(vm);
            }
            catch (Exception exc)
            {
                errored = true;
                error = exc.ToString();
            }

            return Json(new { Errored = errored ? "1" : "0", Message = error });
        }
        [HttpGet]
        public JsonResult IsTemplateExists(string template)
        {
            bool exists = false;

            try
            {
                if (!string.IsNullOrWhiteSpace(template))
                    exists = this._vpRepo.IsTemplateExists(template, CerebrumUser.PracticeId);

            }
            catch (Exception)
            {
            }

            return Json(new { Errored = false ? "1" : "0", Exists = exists ? "1" : "0" });
        }


        [HttpPost]
        public JsonResult SaveTemplate(VMTemplate vm)
        {
            bool errored = false;

            try
            {
                if (!string.IsNullOrWhiteSpace(vm.Name))
                    this._vpRepo.AddNewTemplate(vm.Name, CerebrumUser.UserId, GetIPAddress());
                else errored = true;
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new { Errored = errored ? "1" : "0" });
        }

        public JsonResult GetTemplateItem(int mid)
        {

            List<VMTemplateDetail> vm = new List<VMTemplateDetail>();

            bool errored = false;

            try
            {
                vm.Add(this._vpRepo.GetTemplateItem(mid));
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new { Errored = errored ? "1" : "0", Data = vm });
        }

        public ActionResult GetMeasurements()
        {
            var vm = this._vpRepo.GetMeasurements();

            return PartialView(vm);
        }

        [HttpPost]
        public ActionResult TemplatePatientDataPost(
                                                string json,
                                                string templateName,
                                                int TemplateID,
                                                int PatientID,
                                                string DateStr,
                                                int AppointmentID
                                                )
        {
            IList<VMTemplatePatientDataItem> vm =
            Newtonsoft.Json.JsonConvert.DeserializeObject<IList<VMTemplatePatientDataItem>>(json);

            vm.ToList().ForEach(x => x.PatientID = PatientID);

            string mssg = string.Empty;
            //bool errored = false;
            try
            {
                var bmi = BMI_Calculation(vm);
                _vpRepo.SaveTemplateDetailsByPatientData(vm, TemplateID, PatientID, DateStr, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                mssg = exc.ToString();
                //errored = true;
                _log.Error(exc);
            }

            return RedirectToAction("TemplateData", new { patientID = PatientID, appointmentID = AppointmentID });
        }
        private string BMI_Calculation(IList<VMTemplatePatientDataItem> l)
        {
            decimal weight = 0;
            decimal height = 0;
            decimal bmi = 0;
            decimal dummy = 0;
            var weightMeas = l.Where(s => s.VPTemplateFieldId == 145).FirstOrDefault();
            var heightMeas = l.Where(s => s.VPTemplateFieldId == 146).FirstOrDefault();
            var bmiMeas = l.Where(s => s.VPTemplateFieldId == 37).FirstOrDefault();

            if (weightMeas != null)
            {
                if (!string.IsNullOrEmpty(weightMeas.Value) && decimal.TryParse(weightMeas.Value, out dummy))
                    weight = decimal.Parse(weightMeas.Value);
            }
            if (heightMeas != null)
            {
                if (!string.IsNullOrEmpty(heightMeas.Value) && decimal.TryParse(heightMeas.Value, out dummy))
                    height = decimal.Parse(heightMeas.Value);
            }
            if (bmiMeas != null)
            {
                if (!string.IsNullOrEmpty(bmiMeas.Value) && decimal.TryParse(bmiMeas.Value, out dummy))
                    bmi = decimal.Parse(bmiMeas.Value);
            }

            if (bmi <= 0 && (weight > 0 && height > 0))
            {
                bmiMeas.Value = (10000 * (weight / (height * height))).ToString("N0");
            }

            return bmiMeas != null ? bmiMeas.Value : null;
        }

        public JsonResult GetPatientTemplateItemDetails(int templateID, int patientID)
        {

            //List<VP_Template_Detail_VM> lst = repo.GetTemplateDetails(templateID);
            List<VMTemplateDetail> lst = new List<VMTemplateDetail>();

            lst = _vpRepo.GetTemplateDetailsByPatient(lst, templateID, patientID);

            return Json(new { Result = lst });
        }
        public JsonResult GetTemplateDetails(int templateID, int patientID)
        {

            List<VMTemplateDetail> lst = new List<VMTemplateDetail>();

            lst = _vpRepo.GetTemplateDetails(lst, templateID, patientID);

            return Json(new { Result = lst });
        }

        #endregion

        #region settings 

        public JsonResult GetLifeStage(string days, string option)
        {
            bool errored = false;
            string mssg = string.Empty;
            int dummy;

            try
            {
                if (string.IsNullOrEmpty(days) || string.IsNullOrEmpty(option))
                {
                    errored = true;
                    mssg = "Please enter all values";
                }
                else
                if (!Int32.TryParse(days, out dummy))
                {
                    errored = true;
                    mssg = "Days must be integer";
                }
                else
                {
                    int age = Convert.ToInt32(days);

                    switch (option)
                    {
                        case "Days":
                            if (age < 28)
                            {
                                mssg = "Newborn";
                            }
                            if (29 <= age && age < 730)
                            {
                                mssg = "Infant";
                            }
                            break;
                        case "Months":
                            if (age == 1)
                            {
                                mssg = "Newborn";
                            }
                            if (1 < age && age < 24)
                            {
                                mssg = "Infant";
                            }
                            if (24 <= age && age <= 180)
                            {
                                mssg = "Child";
                            }
                            if (180 < age && age < 204)
                            {
                                mssg = "Adolescent";
                            }
                            if (204 <= age)
                            {
                                mssg = "Adult";

                            }
                            break;
                        case "Years":
                            if (2 <= age && age <= 15)
                            {
                                mssg = "Child";
                            }
                            if (16 <= age && age <= 17)
                            {
                                mssg = "Adolescent";
                            }
                            if (18 <= age)
                            {
                                mssg = "Adult";

                            }
                            break;
                        default:

                            errored = true;
                            mssg = "Age > 0 and units undefined";
                            break;
                    }
                }

            }
            catch (Exception)
            {
                errored = true;
            }


            return Json(new { Result = errored ? "1" : "0", Message = mssg });
        }

        public JsonResult Save_Skip_Phrases(List<VMTopLevelReportPhrase> list, int AppointmentID, int doctorID)
        {
            bool errored = false;
            try
            {
                this._vpRepo.SaveReportPhrasesSkipped(list, doctorID, 0, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception)
            {
                errored = true;
            }


            return Json(new { Result = errored ? "1" : "0" });
        }

        public JsonResult Save_CPP_Phrases(List<VMCPPCategory> list, int AppointmentID, int doctorID)
        {

            bool errored = false;
            try
            {
                this._vpRepo.SaveCPPSkipped(list, doctorID, 0, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception)
            {
                errored = true;
            }


            return Json(new { Result = errored ? "1" : "0" });
        }



        public ActionResult ReportPhraseCustomize(int reportPhraseID, int userID)
        {

            VP_ReportPhrase_Val vm = _vpRepo.GetReportPhraseCustomValue(reportPhraseID, userID);

            return View(vm);
        }

        public ActionResult ReportPhraseHistory(int reportPhraseID, int appointmentID, int patientID, bool IsAccumulative)
        {

            bool isFirst = true;
            string prevValue = string.Empty;
            List<ReportPhraseHistoryData> lst = new List<ReportPhraseHistoryData>();

            ViewBag.ReportPhraseName = this._vpRepo.GetReportPhraseByID(reportPhraseID);
            var logs = this._vpRepo.GetLogs(appointmentID, patientID).OrderBy(x => x.Id).ToList();
            logs.ForEach(l =>
            {
                string currValue = this._vpRepo.GetReportPhraseValue(reportPhraseID, appointmentID, patientID, l.Id);
                //if (!string.IsNullOrEmpty(currValue))
                {
                    string diff = string.Empty;

                    if (!isFirst)
                    {
                        diff = compareLines(prevValue, currValue);
                    }
                    else
                    {
                        diff = currValue;
                    }

                    // add line when it's first or differencies found
                    if (isFirst || !string.IsNullOrEmpty(diff))
                    {
                        lst.Add(new ReportPhraseHistoryData()
                        {
                            AppointmentID = appointmentID,
                            PatientID = patientID,
                            Name = l.UserName,
                            LogDate = l.Date.Value,
                            Value = currValue,
                            Difference = diff
                            //Difference = compareLines(currValue, prevValue)  
                        });
                        isFirst = false;
                    }
                    prevValue = currValue;
                }
            });
            lst = lst.OrderByDescending(x => x.LogDate).ToList();


            if (IsAccumulative)
            {
                var lastApptID = this._vpRepo.GetPreviousVPAppointment(appointmentID, patientID);
                if (lastApptID != null)
                {
                    ViewBag.AccumulativeValue = this._vpRepo.GetAccumulativeValue(lastApptID.Value, patientID, reportPhraseID);
                }
            }

            return View(lst);
        }


        [HttpPost]
        public ActionResult ReportPhraseCustomize(VP_ReportPhrase_Val vm)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(vm.Text))
                {
                    message = "Custom Text Value required";
                    errored = true;
                }
                else
                {
                    _vpRepo.SaveReportPhraseCustomValue(vm, CerebrumUser.UserId, GetIPAddress());
                }
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            return Json(new { Error = errored ? "1" : "0", Result = message });
        }


        public ActionResult AddOpeningStatementPhraseSubItem(int RootPhraseID, int ParentID, int DocID)
        {
            AddPhraseSubItem_VM vm = new AddPhraseSubItem_VM();
            vm.DocID = DocID;
            return View(vm);

        }

        [HttpPost]
        public ActionResult AddOpeningStatementPhraseSubItem(AddPhraseSubItem_VM vm)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(vm.Name))
                {
                    message = "Missing Name";
                    errored = true;
                }
                else
                {
                    this._vpRepo.AddOpeningStatementSubItem(vm, CerebrumUser.UserId, GetIPAddress());
                }
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            return Json(new { Errored = errored ? "1" : "0", Result = message });

        }

        [HttpPost]
        public ActionResult AddNewRootPhrase(VMReportPhrase newPhrase)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(newPhrase.Name))
                {
                    message = "Missing Name";
                    errored = true;
                }
                else
                {
                    this._vpRepo.AddRootPhrase(newPhrase, CerebrumUser.UserId, GetIPAddress());
                }
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            return Json(new { Errored = errored ? "1" : "0", Result = message });

        }

        public ActionResult AddPhraseSubItem(int RootPhraseID, int ParentID, int docID)
        {

            AddPhraseSubItem_VM vm = new AddPhraseSubItem_VM();
            vm.DocID = docID;
            return View(vm);
        }

        [HttpPost]
        public ActionResult AddPhraseSubItem(AddPhraseSubItem_VM vm)
        {

            bool errored = false;
            string message = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(vm.Name))
                {
                    message = "Missing Name";
                    errored = true;
                }
                else
                {
                    this._vpRepo.AddPhraseSubItem(vm, CerebrumUser.UserId, GetIPAddress());
                }
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            return Json(new { Errored = errored ? "1" : "0", Result = message });


        }
        public ActionResult EditSubItems(int DocID, int Parent)
        {
            try
            {
                VP_ReportPhraseSetting_VM vm = new VP_ReportPhraseSetting_VM();

                vm.MainLevel = false;

                vm.DoctorID = DocID;

                var ReportPhrases = this._vpRepo.GetPhraseSubItems(DocID, Parent);

                vm.VP_ReportPhraseSettings = _vpRepo.LoadReportPhraseSettings(ReportPhrases);

                vm.VP_ReportPhraseSettings = _vpRepo.GetReportPhraseSettingsCustom(vm.VP_ReportPhraseSettings, DocID);

                for (int i = 1; i < vm.VP_ReportPhraseSettings.Count + 1; i++)
                {
                    vm.RankList.Add(new SelectListItem() { Value = i.ToString(), Text = i.ToString() });
                }

                //ReportPhrase_SubItem vm = new ReportPhrase_SubItem();

                return PartialView("EditSubItems", vm);
            }
            catch (Exception exc)
            {
                return Json(new { Errored = true, Result = exc.ToString() });
            }

        }
        public ActionResult EditReportPhraseSetting(int patientID, int practiceID, int UserID)
        {
            VP_ReportPhraseSetting_VM vm = new
                                VP_ReportPhraseSetting_VM(patientID, practiceID, UserID);

            var Options = _vp.GetOptions();
            Options = _vp.LoadPatientOptions(UserID, Options);
            var ReportPhrases = _vpRepo.GetReportPhrases(Options, UserID);

            vm.VP_ReportPhraseSettings = _vpRepo.LoadReportPhraseSettings(ReportPhrases);

            vm.VP_ReportPhraseSettings = _vpRepo.GetReportPhraseSettingsCustom(vm.VP_ReportPhraseSettings, UserID);

            for (int i = 1; i < vm.VP_ReportPhraseSettings.Count + 1; i++)
            {
                vm.RankList.Add(new SelectListItem() { Value = i.ToString(), Text = i.ToString() });
            }

            if (TempData[ADD_CUSTOMIZE_RP] != null)
            {
                vm.ErrorMessage = TempData[ADD_CUSTOMIZE_RP].ToString();
            }

            if (TempData[SCRIPT_TO_EXECUTE] != null)
                vm.ScriptToExecute = TempData[SCRIPT_TO_EXECUTE].ToString();


            return View(vm);

        }
        [HttpPost]
        public ActionResult SaveReportPhraseSetting(VP_ReportPhraseSetting_VM model)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {
                // get all the text boxes that are empty. We cannot save those.
                var emptyTexts = model.VP_ReportPhraseSettings
                    .Where(x => x.Text == null || (x.Text != null && x.Text.Trim() == ""))
                    .ToList();
                if (!emptyTexts.Any())
                {
                    _vpRepo.SaveReportPhraseSetting(model, CerebrumUser.UserId, GetIPAddress());
                }
                else
                {
                    errored = true;
                    string categories = String.Join(",", emptyTexts.Select(x => x.OriginalText).ToList());
                    message = "Root categories cannot be empty. Please enter values. " + categories;
                }
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            return Json(new { Error = errored ? "1" : "0", Result = message });
        }


        [HttpPost]
        public ActionResult SaveReportPhraseSettingLevel2(VP_ReportPhraseSetting_VM model)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {
                _vpRepo.SaveReportPhraseSetting(model, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            return Json(new { Error = errored ? "1" : "0", Result = message });
        }

        public ActionResult EditCPPSetting(int docID)
        {
            VP_CPP_ColumnSettings_VM vm = new VP_CPP_ColumnSettings_VM();

            vm.DoctorID = docID;

            vm.VP_CPP_Setting_List = _vpRepo.GetCPPSettings();

            vm.VP_CPP_Setting_List = _vpRepo.GetCPPSettingsCustom(docID, vm.VP_CPP_Setting_List);

            return View(vm);

        }

        [HttpPost]
        public ActionResult EditCPPSetting(VP_CPP_ColumnSettings_VM vm)
        {

            bool errored = false;

            string message = string.Empty;

            try
            {
                _vpRepo.SaveCPPSettings(vm.DoctorID, vm.VP_CPP_Setting_List, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }


            return Json(new { Errored = errored, Result = message });

        }


        [HttpGet]
        public JsonResult AddReportPhraseAction(int apptID, int patientID, int reportPhraseID, int topLevelReportPhraseID)
        {
            bool errored = false;

            string message = string.Empty;

            try
            {
                _vpRepo.AddReportPhraseSavedValue(apptID, patientID, reportPhraseID, topLevelReportPhraseID, CerebrumUser.UserId, GetIPAddress());

            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }


            return Json(new { Errored = errored, Result = message });
        }

        [HttpGet]
        public JsonResult RemoveReportPhraseAction(int apptID, int patientID, int reportPhraseID, int topLevelReportPhraseID)
        {
            bool errored = false;

            string message = string.Empty;

            try
            {
                _vpRepo.RemoveReportPhraseSavedValue(apptID, patientID, reportPhraseID, topLevelReportPhraseID, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }


            return Json(new { Errored = errored, Result = message });

        }

        #endregion

        #region Privacy Box


        public ActionResult PrivacyBoxHistory(int appointmentID,
                                        int patientID,
                                        int userID,
                                        bool breakGlass = false)
        {
            VMPrivacyNote vm = new VMPrivacyNote();
            vm.PatientId = patientID;
            vm.UserId = userID;
            vm.AppointmentId = appointmentID;
            vm.BreakGlass = breakGlass;

            if (vm.BreakGlass)
                vm.Notes = _vpRepo.GetAllPrivacyNotes(patientID);
            else
                vm.Notes = _vpRepo.GetPrivacyNotes(userID, patientID);

            return View(vm);
        }

        public ActionResult PrivacyBox(
                                        int appointmentID,
                                        int patientID,
                                        int userID,
                                        bool breakGlass = false)
        {
            VMPrivacyNote vm = new VMPrivacyNote();
            vm.PatientId = patientID;
            vm.UserId = userID;
            vm.AppointmentId = appointmentID;
            vm.BreakGlass = breakGlass;
            //if (vm.BreakGlass)
            //    vm.Notes = repo.GetAllPrivacyNotes(vm.PatientId);
            //else
            //    vm.Notes = repo.GetPrivacyNotes(vm.UserId, vm.PatientId);

            return View(vm);
        }

        [HttpPost]
        public JsonResult PrivacyBox(VMPrivacyNote vm)
        {

            bool errored = false;
            string message = string.Empty;

            vm.Note_To_Add.PatientId = vm.PatientId;
            vm.Note_To_Add.UserId = vm.UserId;

            if (string.IsNullOrEmpty(vm.Note_To_Add.Note))
            {
                errored = true;
                message = "Notes missing";
            }
            else
            {
                _vpRepo.AddPrivacyNote(vm.Note_To_Add, CerebrumUser.UserId, GetIPAddress());
                message = "Notes Added";
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = message
            });
        }

        public ActionResult BreakGlass(VMPrivacyNote vm)
        {
            return RedirectToAction("PrivacyBox",
              new
              {
                  appointmentID = vm.AppointmentId,
                  patientID = vm.PatientId,
                  userID = vm.UserId,
                  BreakGlass = !vm.BreakGlass
              });

        }
        public ActionResult CancelPrivacyBox(int appointmentID, int patientID, int userID)
        {
            return RedirectToAction("PrivacyBox",
             new
             {
                 appointmentID = appointmentID,
                 patientID = patientID,
                 userID = userID
             });

        }

        #endregion

        #region CPP settings

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Alert_CPP(int patientID, int practiceDoctorId)
        {
            VMCPPAlertList vm = new VMCPPAlertList();
            vm.PatientID = patientID;
            vm.PatientRecordId = patientID;
            vm.UserID = CerebrumUser.UserId;
            vm.PracticeDoctorID = practiceDoctorId;
            vm.CPPVisibleField = _vp.GetCPPVisibleField(practiceDoctorId, CPP_Categories.ALERTSANDSPECIALNEEDS);

            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Alert_CPP_Data(int patientID)
        {
            VMCPPAlertList vm = new VMCPPAlertList();
            vm.PatientID = patientID;
            vm.PatientRecordId = patientID;
            vm.UserID = CerebrumUser.UserId;
            vm.Alerts = _vp.GetCPPAlerts(patientID, DateTime.MinValue.AddDays(10), DateTime.MaxValue.AddDays(-10));
            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit_Alert_CPP(int entryID, int patientID)
        {
            VMCPPAlert vm = new VMCPPAlert();
            vm = _vp.GetCPPAlert(entryID);
            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult AddAlertCPP(VMCPPAlertList vm)
        {
            bool errored = false;
            vm.AddedBy = CerebrumUser.UserId;
            string errorStr = string.Empty;

            try
            {
                if (string.IsNullOrEmpty(vm.Description))
                {
                    errorStr = "Description Missing";
                    errored = true;
                }

                if (string.IsNullOrEmpty(errorStr))
                {
                    _vp.Add_CPP_Alert(vm, CerebrumUser.UserId, CerebrumUser.IpAddress);
                }

                if (!errored)
                {
                    vm.CPPVisibleField.Col5Visible = true;
                    _vp.SetCPPVisibleField(vm.CPPVisibleField, CerebrumUser.UserId, CerebrumUser.IpAddress);
                }
            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errorStr
            });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditAlertCPP(VMCPPAlert vm)
        {
            bool errored = false;
            vm.AddedBy = CerebrumUser.UserId;
            string errorStr = string.Empty;

            try
            {
                if (string.IsNullOrEmpty(vm.Description))
                {
                    errorStr = "Description Missing";
                    errored = true;
                }

                if (string.IsNullOrEmpty(errorStr))
                {
                    _vpRepo.Edit_CPP_Alert(vm, CerebrumUser.UserId, GetIPAddress());
                }

            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errorStr
            });
        }

        public ActionResult CancelAddAlertCPP(int patientID)
        {
            return RedirectToAction("AddAlertCPP", new { patientID = patientID, userID = CerebrumUser.UserId });

        }

        public ActionResult RiskFactor(int patientID)
        {

            VMRiskFactor vm = new VMRiskFactor();
            vm.PatientId = patientID;
            vm.VP_RiskFactors = _vpRepo.GetRiskFactors(vm.PatientId, DateTime.MinValue.AddDays(10), DateTime.MaxValue.AddDays(-10));

            if (TempData[SCRIPT_TO_EXECUTE] != null)
                vm.ScriptToExecute = TempData[SCRIPT_TO_EXECUTE].ToString();

            if (TempData[ADD_RISK_ERROR] != null)
            {
                vm.Add_ErrorMessage = TempData[ADD_RISK_ERROR].ToString();
            }

            if (TempData[EDIT_RISK_ERROR] != null)
            {
                vm.Edit_ErrorMessage = TempData[EDIT_RISK_ERROR].ToString();
            }

            vm.VP_RiskFactor_Edit = new VMRiskFactor();
            if (TempData[EDIT_RISK] != null)
            {
                vm.VP_RiskFactor_Edit.Id = Int32.Parse(TempData[EDIT_RISK].ToString());
                vm.VP_RiskFactor_Edit = _vpRepo.GetRiskFactor(vm.VP_RiskFactor_Edit.Id);
            }

            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult RiskFactor_CPP(int patientID, int practiceDoctorId)
        {

            VMRiskFactor vm = new VMRiskFactor();
            vm.PatientId = patientID;
            vm.PracticeDoctorID = practiceDoctorId;
            vm.CPPVisibleField = _vp.GetCPPVisibleField(practiceDoctorId, CPP_Categories.RISKFACTORS);
            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult RiskFactor_CPP_Data(int patientID)
        {
            VMRiskFactor vm = new VMRiskFactor();
            vm.PatientId = patientID;
            vm.VP_RiskFactors = _vpRepo.GetRiskFactors(vm.PatientId, DateTime.MinValue.AddDays(10), DateTime.MaxValue.AddDays(-10));
            return View(vm);
        }



        [HttpPost]
        public ActionResult RiskFactor(VMRiskFactor vm)
        {
            int userid = CerebrumUser.UserId;
            string ipAddress = IPAddress(Request);
            string errorStr = string.Empty;

            //vm.VP_RiskFactor_Add.AddedBy = CerebrumUser.UserId;
            vm.AddedBy = CerebrumUser.UserId;
            vm.VP_RiskFactor_Edit.UpdatedBy = CerebrumUser.UserId;

            if (vm.VP_RiskFactor_Edit.Id == 0)
            {
                if (!string.IsNullOrEmpty(errorStr))
                {
                    TempData[ADD_RISK_ERROR] = errorStr;
                }

                if (string.IsNullOrEmpty(errorStr))
                {
                    _vpRepo.AddRiskFactor(vm, CerebrumUser.UserId, ipAddress);

                    TempData[SCRIPT_TO_EXECUTE] = RELOAD_CPP;
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(errorStr))
                {
                    TempData[EDIT_RISK_ERROR] = errorStr;
                }

                if (string.IsNullOrEmpty(errorStr))
                {
                    _vpRepo.EditRiskFactor(vm, userid, ipAddress);
                    TempData[SCRIPT_TO_EXECUTE] = RELOAD_CPP;
                }
                else
                {
                    TempData[EDIT_RISK] = vm.VP_RiskFactor_Edit.Id;
                }
            }

            return RedirectToAction("RiskFactor", new { patientID = vm.PatientId });

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public JsonResult Save_RiskFactor(VMRiskFactor vm)
        {
            int userid = CerebrumUser.UserId;
            string ipAddress = IPAddress(Request);
            bool errored = false;
            vm.AddedBy = CerebrumUser.UserId;
            string errorStr = string.Empty;
            try
            {

                if (vm.VP_RiskFactor_Edit == null)
                {
                    if (!string.IsNullOrEmpty(errorStr))
                    {
                        errored = true;
                    }
                    else
                    {
                        _vpRepo.AddRiskFactor(vm, CerebrumUser.UserId, ipAddress);

                        vm.CPPVisibleField.Col10Visible = true;
                        _vp.SetCPPVisibleField(vm.CPPVisibleField, CerebrumUser.UserId, CerebrumUser.IpAddress);
                    }
                }
                else
                {
                    if (!vm.VP_RiskFactor_Edit.Status.HasValue)
                    {
                        errorStr = "Status missing";
                    }

                    if (!string.IsNullOrEmpty(errorStr))
                    {
                        errored = true;
                    }
                    else
                    {
                        _vpRepo.EditRiskFactor(vm, userid, ipAddress);
                    }
                }
            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errorStr
            });
        }

        public ActionResult Edit_RiskFactor_CPP(int entryID, int patientID)
        {
            TempData[EDIT_RISK] = entryID;

            return RedirectToAction("RiskFactor", new { patientID = patientID });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult RiskFactor_CPP_Edit(int entryID, int patientID)
        {
            VMRiskFactor vm = new VMRiskFactor();

            vm.PatientId = patientID;
            vm.VP_RiskFactor_Edit = _vpRepo.GetRiskFactor(entryID);
            vm.VP_RiskFactor_Edit.PatientId = patientID;
            vm.VP_RiskFactor_Edit.Id = entryID;

            return View(vm);

        }

        public ActionResult ShowCPP_ProblemList(int patientID, bool isProblemList)
        {
            VMCPPProblem vm = new VMCPPProblem();
            vm.PatientID = patientID;
            vm.IsProblemList = isProblemList;
            vm.VP_CPP_Problem_List_VM = _vpRepo.GetCPP_AllProblemList(CerebrumUser.PracticeId, patientID, isProblemList);

            vm.VP_CPP_Problem_List_VM_Add.PatientRecordId = patientID;

            if (TempData[CPP_PROBLEMLIST_EDIT] != null)
            {
                vm.VP_CPP_Problem_List_VM_Edit.Id = Int32.Parse(TempData[CPP_PROBLEMLIST_EDIT].ToString());
                vm.VP_CPP_Problem_List_VM_Edit = _vpRepo.GetCPPProblem(vm.VP_CPP_Problem_List_VM_Edit.Id);
            }
            else
                vm.VP_CPP_Problem_List_VM_Edit.Id = 0;

            if (TempData[CPP_PROBLEMLIST_ADDERROR] != null)
                vm.Add_ErrorMessage = TempData[CPP_PROBLEMLIST_ADDERROR].ToString();


            if (TempData[CPP_PROBLEMLIST_EDITERROR] != null)
                vm.Edit_ErrorMessage = TempData[CPP_PROBLEMLIST_EDITERROR].ToString();


            if (TempData[SCRIPT_TO_EXECUTE] != null)
                vm.ScriptToExecute = TempData[SCRIPT_TO_EXECUTE].ToString();

            return View(vm);
        }
        [HttpPost]
        public ActionResult ShowCPP_ProblemList(VMCPPProblem vm)
        {
            StringBuilder sb = new StringBuilder();

            vm.VP_CPP_Problem_List_VM_Add.AddedBy = CerebrumUser.UserId;
            vm.VP_CPP_Problem_List_VM_Edit.UpdatedBy = CerebrumUser.UserId;

            if (vm.VP_CPP_Problem_List_VM_Edit.Id == 0)
            {
                vm.Add_ErrorMessage = sb.ToString();

                if (string.IsNullOrEmpty(vm.Add_ErrorMessage))
                {

                    _vpRepo.AddCPPProblem(vm.VP_CPP_Problem_List_VM_Add, CerebrumUser.UserId, GetIPAddress());

                    TempData[SCRIPT_TO_EXECUTE] = RELOAD_CPP;
                }
                else
                {
                    TempData[CPP_PROBLEMLIST_ADDERROR] = vm.Add_ErrorMessage;
                }
            }
            else
            {
                vm.Edit_ErrorMessage = sb.ToString();

                if (string.IsNullOrEmpty(vm.Edit_ErrorMessage))
                {
                    string resDataOut = string.Empty;
                    _vpRepo.EditCPPProblem(vm.VP_CPP_Problem_List_VM_Edit, CerebrumUser.UserId, GetIPAddress(), out resDataOut);
                    vm.VP_CPP_Problem_List_VM_Edit.Id = 0;

                    TempData[SCRIPT_TO_EXECUTE] = RELOAD_CPP;
                }
                else
                {
                    TempData[CPP_PROBLEMLIST_EDITERROR] = vm.Edit_ErrorMessage;
                    TempData[CPP_PROBLEMLIST_EDIT] = vm.VP_CPP_Problem_List_VM_Edit.Id;
                }
            }

            return RedirectToAction("ShowCPP_ProblemList", new { patientID = vm.PatientID });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ProblemList_CPP(int patientID, int appointmentId, int practiceDoctorId, bool isProblemList)
        {
            VMCPPProblem vm = new VMCPPProblem();

            vm.PatientID = patientID;
            vm.IsProblemList = isProblemList;
            vm.AppointmentID = appointmentId;
            vm.PracticeDoctorID = practiceDoctorId;
            if (isProblemList)
                vm.CPPVisibleField = _vp.GetCPPVisibleField((int)practiceDoctorId, CPP_Categories.PROBLEMLIST);
            else
                vm.CPPVisibleField = _vp.GetCPPVisibleField((int)practiceDoctorId, CPP_Categories.PASTHEALTH);

            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ProblemList_CPP_Data(int patientID, bool isProblemList, int appointmentID, int practiceDoctorId)
        {
            VMCPPProblem vm = new VMCPPProblem();

            vm.PatientID = patientID;
            vm.IsProblemList = isProblemList;
            vm.AppointmentID = appointmentID;
            vm.PracticeDoctorID = practiceDoctorId;

            vm.VP_CPP_Problem_List_VM = _vpRepo.GetCPP_ProblemList(patientID, isProblemList, DateTime.MinValue.AddDays(10), DateTime.MaxValue.AddDays(-10));
            vm.ActiveList = _vpRepo.GetCPPProblemStatus();
            vm.CPPVisibleField = _vp.GetCPPVisibleField(practiceDoctorId, isProblemList ? CPP_Categories.PROBLEMLIST : CPP_Categories.PASTHEALTH);

            return View(vm);
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ProblemList_CPP_Edit(int EntryID, int patientID, bool IsProblemList)
        {
            //VP_CPP_Problem_List_VM vm = new VP_CPP_Problem_List_VM();
            VMCPPProblem vm = new VMCPPProblem();
            vm.VP_CPP_Problem_List_VM_Edit = _vpRepo.GetCPPProblem(EntryID);
            vm.VP_CPP_Problem_List_VM_Edit.PatientRecordId = patientID;
            return View(vm);
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public JsonResult Save_ProblemList(VP_CPP_Problem_Update_VM data)
        {
            bool errored = false;
            string errorStr = string.Empty;

            try
            {
                StringBuilder sb = new StringBuilder();

                if (string.IsNullOrEmpty(data.description) && string.IsNullOrEmpty(data.procedure))
                {
                    sb.Append("Problem Description / Procedure required").Append(Environment.NewLine);
                }
                errorStr = sb.ToString();
                if (string.IsNullOrEmpty(errorStr))
                {
                    string resDataOut = string.Empty;
                    VMCPPProblem vm = new VMCPPProblem();
                    vm.VP_CPP_Problem_List_VM_Edit.Id = data.id;
                    vm.VP_CPP_Problem_List_VM_Edit.isProblemList = data.isProblemList;
                    vm.VP_CPP_Problem_List_VM_Edit.visible = data.visible;
                    vm.VP_CPP_Problem_List_VM_Edit.PatientRecordId = data.patientRecordID;
                    vm.VP_CPP_Problem_List_VM_Edit.AppointmentID = data.appointmentID;
                    vm.VP_CPP_Problem_List_VM_Edit.Position = data.position;
                    vm.VP_CPP_Problem_List_VM_Edit.CodingSystem = data.codingSystem;
                    vm.VP_CPP_Problem_List_VM_Edit.DiagnosticCode = data.diagnosticCode;
                    vm.VP_CPP_Problem_List_VM_Edit.DiagnosticDescription = data.diagnosticDescription;
                    vm.VP_CPP_Problem_List_VM_Edit.Proc_Interv = data.procedure;
                    vm.VP_CPP_Problem_List_VM_Edit.Problem_Description = data.description;
                    vm.VP_CPP_Problem_List_VM_Edit.Problem_Status = data.status == 0 ? null : (int?)data.status;
                    vm.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Day = data.dateOnsetDay;
                    vm.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Month = data.dateOnsetMonth;
                    vm.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Year = data.dateOnsetYear;
                    vm.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Day = data.resolutionDateDay;
                    vm.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Month = data.resolutionDateMonth;
                    vm.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Year = data.resolutionDateYear;
                    vm.VP_CPP_Problem_List_VM_Edit.ProcDate_Day = data.procedureDateDay;
                    vm.VP_CPP_Problem_List_VM_Edit.ProcDate_Month = data.procedureDateMonth;
                    vm.VP_CPP_Problem_List_VM_Edit.ProcDate_Year = data.procedureDateYear;
                    vm.VP_CPP_Problem_List_VM_Edit.Years = data.lifeStageYear;
                    vm.VP_CPP_Problem_List_VM_Edit.Units = data.lifeStageUnit;
                    vm.VP_CPP_Problem_List_VM_Edit.Life_Stage = data.lifeStageText;
                    vm.VP_CPP_Problem_List_VM_Edit.Notes = data.note;
                    vm.VP_CPP_Problem_List_VM_Edit.AddedBy = CerebrumUser.UserId;
                    vm.VP_CPP_Problem_List_VM_Edit.UpdatedBy = CerebrumUser.UserId;
                    _vpRepo.EditCPPProblem(vm.VP_CPP_Problem_List_VM_Edit, CerebrumUser.UserId, GetIPAddress(), out resDataOut);

                    if (data.practiceDoctorID > 0)
                    {
                        vm.CPPVisibleField.PracticeDoctorId = data.practiceDoctorID;
                        vm.CPPVisibleField.VP_CPP_Category_Id = data.isProblemList ? (int)CPP_Categories.PROBLEMLIST : (int)CPP_Categories.PASTHEALTH;
                        vm.CPPVisibleField.Col1Visible = data.col1Visible;
                        vm.CPPVisibleField.Col2Visible = data.col2Visible;
                        vm.CPPVisibleField.Col3Visible = data.col3Visible;
                        vm.CPPVisibleField.Col4Visible = data.col4Visible;
                        vm.CPPVisibleField.Col5Visible = data.col5Visible;
                        vm.CPPVisibleField.Col6Visible = data.col6Visible;
                        vm.CPPVisibleField.Col7Visible = data.col7Visible;
                        vm.CPPVisibleField.Col8Visible = data.col8Visible;
                        vm.CPPVisibleField.Col9Visible = data.col9Visible;
                        vm.CPPVisibleField.Col10Visible = data.col10Visible;
                        _vp.SetCPPVisibleField(vm.CPPVisibleField, CerebrumUser.UserId, CerebrumUser.IpAddress);
                    }
                }
                else
                    errored = true;
            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errorStr
            });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public JsonResult AddToCPP(int patientID, string diagnosticDescription)
        {
            bool errored = false;
            string errorStr = string.Empty;

            try
            {
                string resDataOut = string.Empty;
                VMCPPProblem vm = new VMCPPProblem();
                vm.VP_CPP_Problem_List_VM_Add.AddedBy = CerebrumUser.UserId;
                vm.VP_CPP_Problem_List_VM_Add.UpdatedBy = CerebrumUser.UserId;
                vm.VP_CPP_Problem_List_VM_Add.isProblemList = true;
                vm.VP_CPP_Problem_List_VM_Add.visible = true;
                vm.VP_CPP_Problem_List_VM_Add.PatientRecordId = patientID;
                vm.VP_CPP_Problem_List_VM_Add.DiagnosticDescription = diagnosticDescription;
                vm.VP_CPP_Problem_List_VM_Add.Problem_Description = diagnosticDescription;
                _vpRepo.EditCPPProblem(vm.VP_CPP_Problem_List_VM_Add, CerebrumUser.UserId, GetIPAddress(), out resDataOut);
            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errorStr
            });
        }

        public ActionResult Cancel_CPP_ProblemList(int patientID, bool IsProblemList)
        {
            return RedirectToAction("ShowCPP_ProblemList", new { patientID = patientID, isProblemList = IsProblemList });

        }
        public ActionResult Edit_CPP_Problem(int EntryID, int patientID, bool IsProblemList)
        {
            TempData[CPP_PROBLEMLIST_EDIT] = EntryID;

            return RedirectToAction("ShowCPP_ProblemList", new { patientID = patientID, isProblemList = IsProblemList });
        }


        #region CPP Family History 

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult FamilyHistoryData(int patientID, int practiceDoctorId)
        {
            VMFamilyHistoryControl vm = new VMFamilyHistoryControl();
            vm.PatientID = patientID;
            vm.FamilyHistories = _vpRepo.GetFamilyHistory(vm.PatientID, DateTime.MinValue.AddDays(10), DateTime.MaxValue.AddDays(-10));
            return PartialView("FamilyHistoryData", vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult FamilyHistory(int patientID, int practiceDoctorId)
        {
            VMFamilyHistoryControl vm = new VMFamilyHistoryControl();
            vm.PatientID = patientID;
            vm.PracticeDoctorID = practiceDoctorId;
            vm.CPPVisibleField = _vp.GetCPPVisibleField(practiceDoctorId, CPP_Categories.FAMILYHISTORY);
            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public JsonResult AddFamilyHistory(VMFamilyHistoryControl vm)
        {

            bool errored = false;
            string errorStr = string.Empty;

            try
            {

                vm.FamilyHistory_Add.AddedBy = CerebrumUser.UserId;
                vm.FamilyHistory_Edit.UpdatedBy = CerebrumUser.UserId;

                vm.FamilyHistory_Add.PatientID = vm.PatientID;
                vm.FamilyHistory_Edit.PatientID = vm.PatientID;

                if (vm.FamilyHistory_Edit.Id == 0)
                {
                    if (string.IsNullOrEmpty(vm.FamilyHistory_Add.ProblemDescription))
                    {
                        errorStr = "Problem Description missing";
                    }

                    //if (string.IsNullOrEmpty(vm.FamilyHistory_Add.RelationShip))
                    //{
                    //    errorStr = "Relationship missing";
                    //}

                    if (!string.IsNullOrEmpty(errorStr))
                    {
                        errored = true;
                    }
                    else
                    {
                        string resDataOut = string.Empty;
                        _vpRepo.AddFamilyHistory(vm.FamilyHistory_Add, CerebrumUser.UserId, GetIPAddress(), out resDataOut);
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(errorStr))
                    {
                        errored = true;
                    }
                    else
                    {
                        _vpRepo.EditFamilyHistory(vm.FamilyHistory_Edit, CerebrumUser.UserId, GetIPAddress());
                    }
                }

                if (!errored)
                {
                    vm.CPPVisibleField.Col5Visible = vm.CPPVisibleField.Col4Visible;
                    vm.CPPVisibleField.Col7Visible = true;
                    _vp.SetCPPVisibleField(vm.CPPVisibleField, CerebrumUser.UserId, CerebrumUser.IpAddress);
                }

            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errorStr
            });

        }

        public ActionResult CancelFamilyHistory(int patientID)
        {
            return RedirectToAction("FamilyHistory", new { patientID = patientID });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult EditFamilyHistory(int patientID, int rowid)
        {
            VMFamilyHistoryControl vm = new VMFamilyHistoryControl();
            //bool errored = false;
            string errorStr = string.Empty;
            try
            {
                vm.FamilyHistory_Edit.Id = rowid;
                vm.FamilyHistory_Edit = _vpRepo.GetSpecificFamilyHistory(vm.FamilyHistory_Edit.Id);
                vm.FamilyHistory_Edit.PatientID = patientID;
            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                //errored = true;
                _log.Error(exc);
            }

            return PartialView("EditFamilyHistory", vm);

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult SaveFamilyHistory(VMFamilyHistoryControl vm)
        {
            bool errored = false;
            string errorStr = string.Empty;

            try
            {
                //if (!vm.FamilyHistory_Edit.Status.HasValue)
                //{
                //    errorStr = "Status missing";
                //}

                if (!string.IsNullOrEmpty(errorStr))
                {
                    errored = true;
                }
                else
                {
                    this._vpRepo.EditFamilyHistory(vm.FamilyHistory_Edit, CerebrumUser.UserId, GetIPAddress());
                }

            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errorStr
            });

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ReasonForDeletion(int patientID, int cppType, int rowid, string cntrlName)
        {

            VMReasonForDeletion vm = new VMReasonForDeletion();

            vm.PatientID = patientID;
            vm.Cpp_Type = cppType;
            vm.Rowid = rowid;
            vm.Hyperlink_Name = cntrlName;

            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public JsonResult SaveReasonForDeletion(VMReasonForDeletion vm)
        {
            bool errored = false;
            string errorStr = string.Empty;

            try
            {
                int userId = CerebrumUser.UserId;
                string ipAddress = GetIPAddress();
                errored = _vp.SaveReasonForDeletion(vm, userId, ipAddress);
            }
            catch (Exception exc)
            {
                errorStr = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errorStr
            });

        }

        public ActionResult Search_ICD10(string CntrlCode, string CntrlName)
        {
            VMICD10 vm = new VMICD10()
            {
                ControlIDToUpdateCode = CntrlCode,
                ControlIDToUpdateName = CntrlName
            };

            return View(vm);
        }

        [HttpPost]
        public ActionResult Search_ICD10(VMICD10 vm)
        {
            vm.List = _vp.Search_ICD10(vm.SearchString);

            return View(vm);
        }



        public ActionResult ICD10Results(VMICD10 vm)
        {

            vm.List = _vp.Search_ICD10(vm.SearchString);

            return PartialView("ICD10Results", vm);
        }

        #endregion 

        #endregion

        #region Reports
        public ActionResult Preview(int appointmentID, int patientID)
        {
            return View();
        }

        public ActionResult SendReportList(int appointmentID, int patientID)
        {
            LocalVP.SendReport_VM vm = new LocalVP.SendReport_VM() { AppointmentId = appointmentID, PatientId = patientID };

            return View(vm);
        }

        public ActionResult GetDocContactList(int appointmentID, int patientID, int officeID)
        {
            var vm = new VMPatientAppointement();

            vm.AppointmentID = appointmentID;
            vm.PatientID = patientID;
            vm.OfficeID = officeID;

            vm.Doctors = this._vpRepo.GetContactList(appointmentID);

            return View(vm);

        }

        public ActionResult ReportHistory(int appointmentID, int patientID)
        {
            var vm = _vpRepo.GetReportHistory(appointmentID, patientID);

            return View(vm);
        }

        #endregion

        #region Immunization
        public ActionResult SaveImmunBilling(string Code, int patientId)
        {
            bool errored = false;
            string mssg = string.Empty;

            try
            {
                var codeId = _vp.GetImmuneCodeID(Code);
                Cerebrum30.Areas.AdminUser.DataAccess.CdsExportRepository cdsRepo = new AdminUser.DataAccess.CdsExportRepository(_context);
                Cerebrum.BLL.Schedule.AppointmentsBLL helper = new Cerebrum.BLL.Schedule.AppointmentsBLL();

                var refdoctor = helper.GetPatientReferralDoctor(patientId);
                var practiceDocId = cdsRepo.GetPracticeDoctorId(patientId);

                var userId = CerebrumUser.UserId;
                var ipAddress = GetIPAddress();
                var practiceId = _vp.GetOfficeByPractice(CerebrumUser.PracticeId);

                _vp.CreateNoVisitAppointment(practiceId, practiceDocId, refdoctor.Id, patientId, codeId, userId, ipAddress);

                mssg = "Changed Saved";
            }
            catch (Exception exc)
            {
                errored = true;
                mssg = "Error occurred";
                _log.Error(exc);
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = mssg
            });

        }
        public ActionResult ImmunBillingCode()
        {
            List<VMImmuneBillingCategory> lst = new List<VMImmuneBillingCategory>();

            lst = _vp.GetImmuneBillingCodes();

            return View(lst);

        }

        public ActionResult SaveProcedure(VMImmunizationType vm)
        {
            bool errored = false;
            string mssg = string.Empty;
            var userId = CerebrumUser.UserId;
            var ipAddress = GetIPAddress();
            try
            {
                _vp.EditProcedure(vm, userId, ipAddress);
            }
            catch (Exception ex)
            {
                var message = ex.Message;
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = mssg
            });
        }

        public ActionResult EditProcedure(int ProcID)
        {
            var vm = new VMImmunizationType();

            vm = _vp.GetImmuneDetails(ProcID);
            vm.AllGenders = _vp.GetGenders();
            vm.AllCategories = _vp.GetImmuneAgeCategories();
            return View(vm);
        }

        public ActionResult GetImmunizationTypesPDF()
        {
            var fileName = AspNetCoreCompatibility.ServerHelper.MapPath("~/Areas/VP/Documents/") + "ImmunizationTypes.pdf";
            var contentType = AspNetCoreCompatibility.MimeMapping.GetMimeMapping(fileName);
            return File(fileName, contentType);
        }

        public IActionResult BonusReport()
        {
            VMBonusReport vm = new VMBonusReport();
            for (int i = 2000; i < 2030; i++)
            {
                vm.Years.Add(new SelectListItem()
                {
                    Text = i.ToString(),
                    Value = i.ToString()
                });
            }
            vm.Doctors = _vp.GetPracticeDoctors(CerebrumUser.PracticeId);
            vm.Types = _vp.GetImmunizationTypes();

            return View(vm);
        }

        [HttpPost]
        public IActionResult GenerateBonus(VMBonusReport vm)
        {
            bool errored = false;
            decimal bonus = -1;
            string mssg = string.Empty;

            try
            {
                if (vm.DocID == 0 || vm.FiscarYear == 0 || vm.ImmunizationType == 0)
                {
                    errored = true;
                    mssg = "Missing values";
                }

                if (!errored)
                {
                    var bonusReport = _vp.GenerateBonus(vm.DocID, vm.FiscarYear, vm.ImmunizationType);
                    return PartialView("_bonusReportMain", bonusReport);
                }
            }
            catch (Exception ex)
            {
                var message = ex.Message;
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = errored ? mssg : bonus.ToString() + " %"
            });


        }

        public JsonResult GetImmunDetails(int recID)
        {
            bool errored = false;
            VMImmunizationType details = new VMImmunizationType();
            try
            {
                details = _vp.GetImmuneDetails(recID);
            }
            catch (Exception ex)
            {
                var message = ex.Message;
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = details
            });

        }

        [HttpPost]
        public JsonResult UpdateRecallList(int ImmunType, int doctorID)
        {
            bool errored = false;
            int count = 0;

            try
            {
                if (ImmunType > 0)
                    //TODO, take off hard coded 
                    count = _vp.UpdateRecallList(ImmunType, doctorID);
            }
            catch (Exception exc)
            {
                errored = true;
                _log.Error(exc.ToString());
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = count.ToString()
            });

        }
        public JsonResult GenerateRecall()
        {
            bool errored = false;
            var userId = CerebrumUser.UserId;
            var ipAddress = GetIPAddress();

            try
            {
                var recalls = _vp.GenerateRecall(userId, ipAddress);

                // TODO: Move it out of this
                recalls.ForEach(x =>
                {

                    _vp.Add_CPP_Alert(new VMCPPAlertList()
                    {
                        PatientID = x.PatientId,
                        UserID = userId,
                        Description = "Overdue alert",
                        Note = string.Format("Immunization {0} was scheduled on {1} and is overdue.", x.Name, x.NextDate),
                        DateActive = DateTime.Now
                    }, userId, ipAddress);

                    //TODO, add to contact messenger 
                    _contactManagerService.AddNewTask(new Cerebrum.ViewModels.ContactManagerNew.ContactManagerTaskData()
                    {
                        patientRecordId = x.PatientId.ToString(),
                        dueDate = x.NextDate.Value.ToShortDateString(),
                        message = string.Format("Immunization {0} was scheduled on {1} and is overdue.", x.Name, x.NextDate),
                    }, CerebrumUser.PracticeId, CerebrumUser.GetUserIdGuid(), userId, ipAddress);

                });
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0"
            });

        }

        public ActionResult Recall(int docID = 0)
        {
            VMRecall vm = new VMRecall();

            vm.Doctors = _vp.GetPracticeDoctors(CerebrumUser.PracticeId);
            vm.Immunizations = _vp.GetImmunizationTypes();
            vm.Statuses = _vp.GetImmunizationStatuses();
            vm.RecallLetters = _vp.GetRecallLetters();
            vm.EnrollmentTypes = _vp.GetEnrollmentTypes();

            for (int i = 2000; i < 2030; i++)
            {
                vm.Years.Add(new SelectListItem()
                {
                    Text = i.ToString(),
                    Value = i.ToString()
                });
            }

            if (docID != 0)
                vm.DoctorID = docID;

            return View(vm);
        }

        public ActionResult Recall_Data(VMRecall vm)
        {
            vm.PracticeID = CerebrumUser.PracticeId;
            if (vm.DoctorID > 0 && vm.SelectedType > 0)
            {
                vm.Patients = _vp.GetRecallList(vm);

            }
            return PartialView("Recall_Data", vm);
        }

        public ActionResult GetUpdatedRecallRow(int immunId, int recallID)
        {
            VMImmunizationPatientRecord vm = new VMImmunizationPatientRecord();

            vm = _vp.GetRecallRow(immunId, recallID);

            return PartialView("Recall_Row", vm);
        }

        public JsonResult UpdateImmunizationNote(int ID, string Note)
        {
            bool errored = false;
            var userId = CerebrumUser.UserId;
            var ipAddress = GetIPAddress();

            _vp.UpdateImmunizationNote(ID, Note, userId, ipAddress);

            return Json(new
            {
                Errored = errored ? "1" : "0"
            });
        }

        public ActionResult Immunization(int patientID)
        {
            VMImmunization vm = new VMImmunization();
            vm.PatientID = patientID;
            vm.Statuses = _vp.GetImmunizationStatuses();
            vm.Types = _vp.GetImmunizationTypes().OrderBy(a => a.Name).ToList();
            vm.BillingCats = _vp.GetImmunBillingCodes();
            vm.Doctors = _vp.GetPracticeDoctors(CerebrumUser.PracticeId);

            return View(vm);
        }


        public ActionResult Immunization_Data(int patientID)
        {
            VMImmunization vm = new VMImmunization();

            vm.PatientID = patientID;
            vm.Statuses = _vp.GetImmunizationStatuses();
            vm.ImmunizationGroups = _vp.GetImmunizationGroups(CerebrumUser.PracticeId, patientID);

            return PartialView(vm);
        }


        public ActionResult ImmunizationHistory(int PatientID, int RowID)
        {
            List<VMImmuneHistory> lst = new List<VMImmuneHistory>();
            ViewBag.ImmunizationName = _vp.GetImmunizationRecord(RowID).Name;
            lst = _vp.GetImmuneHistory(PatientID, RowID);

            return View(lst);
        }

        public JsonResult UpdateImmunizationStatus(VMImmunizationStatusEdit statusEdit)
        {
            VMImmunization vm = new VMImmunization();
            List<VMRecallLog> recallLst = new List<VMRecallLog>();

            var recallID = 0;
            var ImmunID = 0;

            bool errored = false;
            string mssg = string.Empty;
            try
            {
                if (ModelState.IsValid)
                {
                    _vp.UpdateImmunizationStatus(statusEdit);

                    var recallDt = _vp.GetLastServiceDate(statusEdit.recID);
                    var lastServiceDt = _vp.GetLastServiceDateExcludingOverDue(statusEdit.recID);
                    var immunID = _vp.GetImmunID(statusEdit.RecallID);

                    if (recallDt.HasValue)
                    {
                        recallLst.Add(new VMRecallLog()
                        {
                            ImmunID = immunID,
                            RecallID = statusEdit.RecallID,
                            DateStr = recallDt.ToString(),
                            LastServiceDateStr = lastServiceDt.HasValue ? lastServiceDt.ToString() : string.Empty
                        });
                    }
                }
                else
                {
                    mssg = string.Join("; ", ModelState.Values
                                        .SelectMany(x => x.Errors)
                                        .Select(x => x.ErrorMessage));
                    errored = true;
                }

            }
            catch (Exception ex)
            {
                var message = ex.Message;
                errored = true;
            }

            if (recallLst.Count > 0)
            {
                recallID = recallLst[0].RecallID;
                ImmunID = recallLst[0].ImmunID;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                ErrorMessage = mssg,
                RecallID = recallID,
                ImmunID = ImmunID

            });
        }

        public ActionResult GetImmunizations(int patientID)
        {
            VMImmunization vm = new VMImmunization();
            vm.Types = _vp.GetImmunizationTypes();
            return PartialView("_ImmunizationTypes", vm);
        }

        public ActionResult ImmunizationAdminister(int patientID, int immunizationTypeID = 0, int id = 0)
        {
            VMImmunizationPatientRecord vm = new VMImmunizationPatientRecord();

            if (id == 0)
            {
                vm.EditMode = false;
                vm.VP_CPP_ImmunizationTypeId = immunizationTypeID;
                var immunType = _vp.GetImmunizationTypes().Where(i => i.Id == immunizationTypeID).FirstOrDefault();
                if (immunType != null)
                {
                    vm.VP_CPP_ImmunizationType = immunType.Name;
                    if (immunType.Period.HasValue)
                    {
                        vm.NextDate = DateTime.Now.AddMonths(immunType.Period.Value);
                    }
                }
            }
            else
            {
                vm = _vp.GetImmunizationRecord(id);
                vm.EditMode = true;
            }

            vm.PatientId = patientID;
            var result = _vp.GetPatientInfo(patientID);
            if (result != null)
            {
                vm.PatientName = result.Name;
            }

            return View(vm);
        }

        [HttpPost]
        public ActionResult ImmunizationAdminister(VMImmunizationPatientRecord vm)
        {
            var userId = CerebrumUser.UserId;
            var ipAddress = GetIPAddress();
            if (vm.EditMode)
            {
                _vp.EditImmunization(vm, userId, ipAddress);
            }
            else
            {
                _vp.AdministerImmunization(vm, userId, ipAddress);
            }

            return RedirectToAction("Immunization", new { patientID = vm.PatientId });

        }

        public JsonResult ImmunizationAdministerJSON(VMImmunizationPatientRecord vm)
        {
            bool errored = false;

            vm.PhysicianId =
                CerebrumUser.IsDoctor ?
                CerebrumUser.PracticeDoctorId
                : _vp.GetMainResponsiblePhysician(vm.PatientId);

            if (vm.VP_CPP_ImmunizationTypeId == 0)
            {
                vm.Errors.Add("Select Procedure Type");
            }

            if (vm.Errors.Count > 0)
            {
                errored = true;
            }
            else
            {
                if (vm.EditMode)
                {
                    _vpRepo.EditImmunization(vm, CerebrumUser.UserId, GetIPAddress());
                }
                else
                {
                    _vpRepo.AdministerImmunization(vm, CerebrumUser.UserId, GetIPAddress());
                }
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Errors = vm.Errors
            });
        }

        public ActionResult ImmunizationAdd(int patientID)
        {

            VMImmunizationType vm = new VMImmunizationType();

            vm.PatientID = patientID;

            vm.AllCategories = _vp.GetImmuneAgeCategories();
            vm.AllGenders = _vp.GetGenders();
            vm.AllOperators = _vp.Getoperators();

            return View(vm);
        }

        [HttpPost]
        public ActionResult ImmunizationAdd(VMImmunizationType vm)
        {

            if (!vm.Period.HasValue)
            {
                vm.Errors.Add("Period missing");
            }
            if (string.IsNullOrEmpty(vm.Name))
            {
                vm.Errors.Add("Name missing");
            }

            if (vm.Errors.Count == 0)
            {
                _vp.AddImmunization(vm);
                return RedirectToAction("Immunization", new { patientID = vm.PatientID });
            }
            else
            {
                return View(vm);
            }
        }

        public JsonResult AddImmunization(VMImmunizationType vm)
        {
            bool errored = false;

            if (!vm.Period.HasValue)
            {
                vm.Errors.Add("Period missing/Period must be an integer");
            }

            if (string.IsNullOrEmpty(vm.Name))
            {
                vm.Errors.Add("Name missing");
            }
            if (vm.AgeFrom == 0 && vm.Agecategory != "Routine Infants &amp; Children")
            {
                vm.Errors.Add("Age From missing");
            }
            if (vm.AgeTo == 0)
            {
                vm.Errors.Add("Age To missing");
            }
            if (!vm.DateFrom.HasValue)
            {
                vm.Errors.Add("Date From missing");
            }
            if (!vm.DateTo.HasValue)
            {
                vm.Errors.Add("Date To missing");
            }

            if (vm.Errors.Count == 0)
            {
                _vp.AddImmunization(vm);
            }
            else
            {
                errored = true;
            }


            return Json(new
            {
                Errored = errored ? "1" : "0",
                Errors = vm.Errors
            });

        }


        public ActionResult AdministerImmunization(int patientId, int procedureTypeId)
        {
            var currentDate = System.DateTime.Now;
            var patient = _vp.GetPatientInfo(patientId);
            var procedureTypes = _vp.GetImmunizationTypes();
            var statuses = _vp.GetImmunizationStatuses();
            var selectedProcedure = procedureTypes.Where(i => i.Id == procedureTypeId).First();
            var administer = new VMVPAdminister();
            administer.ProcedureTypeId = procedureTypeId;
            administer.ProcedureType = selectedProcedure.Name;
            administer.Frequency = selectedProcedure.Period;
            administer.AgeCategory = selectedProcedure.Agecategory;
            administer.AgeFrom = selectedProcedure.AgeFrom;
            administer.AgeTo = selectedProcedure.AgeTo;
            administer.Gender = selectedProcedure.GenderStr;
            administer.DateServiced = currentDate;
            administer.DateServicedDay = currentDate.Day;
            administer.DateServicedMonth = currentDate.Month;
            administer.DateServicedYear = currentDate.Year;

            administer.PatientId = patientId;
            administer.PatientName = patient.Name;
            administer.DoctorId = CerebrumUser.IsDoctor ?
                CerebrumUser.PracticeDoctorId
                : _vp.GetMainResponsiblePhysician(patientId);

            ViewBag.Statuses = statuses;

            return View("_administer", administer);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult AdministerImmunization(VMVPAdminister administer)
        {

            if (ModelState.IsValid)
            {
                var userId = CerebrumUser.UserId;
                string ipAddress = IPAddress(Request);
                if (_vp.AdministerImmunization(administer, userId, ipAddress))
                {
                    return Json(new { success = true, message = "Procedure Administered" });
                }
                else
                {
                    ModelState.AddModelError("", "An error occured while saving.");
                }
            }

            var statuses = _vp.GetImmunizationStatuses();
            ViewBag.Statuses = statuses;
            return View("_administer", administer);
        }

        #endregion

        #region Send Letter functionality 

        public ActionResult RecallInfo(int rowid, int PatientId)
        {

            var vm = _vp.GetRecallLog(rowid, PatientId);

            return View(vm);
        }

        public JsonResult SendLetter1(VMRecall vm)
        {
            bool errored = false;
            string message = string.Empty;
            List<VMRecallLog> recallLst = new List<VMRecallLog>();

            try
            {
                vm.Patients.ForEach(p =>
                {
                    this._vpRepo.SendRecallLetter1(p.PatientId, p.PatientName, p.Email, p.Name, p.DateServiced);
                    this._vpRepo.AddRecallLog(p.RecallID, p.PatientId, "Overdue - sent letter 1", CerebrumUser.UserId, GetIPAddress());

                    recallLst.Add(new VMRecallLog()
                    {
                        ImmunID = p.Id,
                        RecallID = p.RecallID
                    });

                });

            }
            catch (Exception exc)
            {
                message = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                List = recallLst
            });
        }
        public JsonResult SendLetter2(VMRecall vm)
        {
            bool errored = false;
            string message = string.Empty;
            List<VMRecallLog> recallLst = new List<VMRecallLog>();
            try
            {
                vm.Patients.ForEach(p =>
                {
                    this._vpRepo.SendRecallLetter2(p.PatientId, p.PatientName, p.Email, p.Name, p.DateServiced);
                    this._vpRepo.AddRecallLog(p.RecallID, p.PatientId, "Overdue - sent letter 2", CerebrumUser.UserId, GetIPAddress());

                    recallLst.Add(new VMRecallLog()
                    {
                        ImmunID = p.Id,
                        RecallID = p.RecallID
                    });
                });

            }
            catch (Exception exc)
            {
                message = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                List = recallLst
            });
        }

        public ActionResult GetRecallLetterPrintView(VMRecallLetterRequest printRequest)
        {
            var printView = _vpRepo.GetRecallLetterPrintView(printRequest);
            return PartialView("_recallLetterPrintPreview", printView);
        }

        public ActionResult RecallLetterPrint()
        {
            return View();
        }

        public JsonResult ContactPhone(VMRecall vm)
        {
            bool errored = false;
            string message = string.Empty;
            List<VMRecallLog> recallLst = new List<VMRecallLog>();
            try
            {
                var currentDate = System.DateTime.Now;
                vm.Patients.ForEach(p =>
                {
                    this._vpRepo.ContactPhone(p.Id, currentDate, CerebrumUser.UserId, GetIPAddress());
                    this._vpRepo.AddRecallLog(p.RecallID, p.PatientId, "Phone Called", currentDate, CerebrumUser.UserId, GetIPAddress());
                    recallLst.Add(new VMRecallLog()
                    {
                        ImmunID = p.Id,
                        RecallID = p.RecallID
                    });
                });

            }
            catch (Exception exc)
            {
                message = exc.ToString();
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                List = recallLst
            });
        }

        #endregion

        #region Cohorts

        public ActionResult CohortAdd()
        {

            return View();
        }

        #endregion
        private string GetIPAddress()
        {
            string ipAddress = IPAddress(Request);
            return ipAddress;
        }

        /// <summary>
        /// Convert local VP_VM to global VP_VM for BLL calls
        /// </summary>
        private Cerebrum.ViewModels.VP.VP_VM ConvertToGlobalVP_VM(LocalVP.VP_VM localVm)
        {
            if (localVm == null) return null;

            return new Cerebrum.ViewModels.VP.VP_VM
            {
                AppointmentID = localVm.AppointmentID,
                PatientID = localVm.PatientID,
                DoctorID = localVm.DoctorID,
                AppointmentTestLogID = localVm.AppointmentTestLogID,
                AppointmentTestID = localVm.AppointmentTestID,
                UserID = localVm.UserID,
                PracticeDoctorID = localVm.PracticeDoctorID,
                // Map other properties as needed
                // Note: Some properties may not have direct equivalents
            };
        }

        /// <summary>
        /// Convert global VP_VM back to local VP_VM
        /// </summary>
        private LocalVP.VP_VM ConvertToLocalVP_VM(Cerebrum.ViewModels.VP.VP_VM globalVm)
        {
            if (globalVm == null) return null;

            return new LocalVP.VP_VM
            {
                AppointmentID = globalVm.AppointmentID,
                PatientID = globalVm.PatientID,
                DoctorID = globalVm.DoctorID,
                AppointmentTestLogID = globalVm.AppointmentTestLogID,
                AppointmentTestID = globalVm.AppointmentTestID,
                UserID = globalVm.UserID,
                PracticeDoctorID = globalVm.PracticeDoctorID,
                ReportPhrases = globalVm.ReportPhrases?.Select(rp => new VMTopLevelReportPhrase
                {
                    Id = rp.Id,
                    Name = rp.Name,
                    Value = rp.Value,
                    Skipped = rp.Skipped,
                    Accumulative = rp.Accumulative,
                    Bolded = rp.Bolded,
                    // Map Phrases if needed
                }).ToList() ?? new List<VMTopLevelReportPhrase>(),
                // Map other properties as needed
            };
        }
    }
    public class ReSendObj
    {
        public ReSendObj(string[] a)
        {
            this.HRMOffice = a[0];

            int appid = 0;
            int.TryParse(a[1], out appid);
            this.AppointmentId = appid;

            int patId = 0;
            int.TryParse(a[2], out patId);
            this.PatientId = patId;

            int aptId = 0;
            int.TryParse(a[3], out aptId);
            this.AppointmentTestId = aptId;

            if (a.Length > 4)
            {
                DateTime apDt;
                if (DateTime.TryParseExact(a[4], "yyyy-MM-dd HH:mm-zzz", CultureInfo.InvariantCulture, DateTimeStyles.None, out apDt))
                {
                    this.Date = apDt;
                }
            }
        }
        public string HRMOffice { get; set; } = string.Empty;
        public int AppointmentId { get; set; }
        public int PatientId { get; set; }
        public int AppointmentTestId { get; set; }
        public DateTime? Date { get; set; } = null;

    }
}