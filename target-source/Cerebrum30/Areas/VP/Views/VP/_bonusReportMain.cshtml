@model Cerebrum.ViewModels.VP.VMBonusReportMain
@inherits Cerebrum30.BaseViewPage<Cerebrum.ViewModels.VP.VMBonusReportMain>

<div id="bonus-report-container">
    @*<div class="row">*@
        <div class="form-horizontal">
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Gender, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    <p class="form-control-static">@Html.DisplayFor(model => model.Gender)</p>
                </div>

                <label class="col-md-2 control-label">Age</label>
                <div class="col-md-2">
                    <p class="form-control-static">@Html.DisplayFor(model => model.AgeFrom) - @Html.DisplayFor(model => model.AgeTo) <span class="age-dsc"> @Html.DisplayFor(model => model.AgeUnits)</span></p>
                </div>

                @Html.LabelFor(model => model.Frequency, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    <p class="form-control-static">@Html.DisplayFor(model => model.Frequency)</p>
                </div>
            </div>   
        </div>
    @*</div>*@
    <partial name="_bonusReportItemList" model="Model.BonusReportItems" />
</div>