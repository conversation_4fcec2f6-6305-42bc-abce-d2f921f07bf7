﻿@model  Cerebrum.ViewModels.VP.VMRecall
@inherits Cerebrum30.BaseViewPage<Cerebrum.ViewModels.VP.VMRecall>
@{
    ViewBag.ModuleName = "Recall List";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />
<script src="//cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="//cdn.datatables.net/1.10.13/css/jquery.dataTables.min.css" />


@section scripts {

    <script>

        //var GetHTML = function (data) {

        //    var context =
        //            {
        //                RecallID : data.RecallID,

        //                Name: data.Name,
        //                PatientName: data.PatientName,
        //                Id: data.Id,
        //                PatientId: data.PatientId,
        //                DoctorName: data.DoctorName,
        //                PhoneNumber: data.PhoneNumber,
        //                RecallID: data.RecallID,
        //                ContactedByPhone: data.ContactedByPhone,
        //                VP_CPP_ImmunizationType: data.VP_CPP_ImmunizationType,
        //                VP_CPP_ImmunizationStatus: data.VP_CPP_ImmunizationStatus,
        //                //DateServiced: data.DateServiced,
        //                //DateCreated: data.DateCreated,
        //                //LastServiceDate: data.LastServiceDate,
        //                Notes: data.Notes

        //            };
        //    var source = $("#resultDetailTemplate").html();
        //    var template = Handlebars.compile(source);
        //    var html = template(context);
        //    return html;
        //};
        var modalRecallTaskID = "#recall-task-container";

        var UpdateRecallRow = function (data) {

            //console.log(data.Id);
            var trToUpdate = "#tr-recall_" + data.RecallID;
            $.ajax({

                method: 'GET',
                url: 'VP/VP/GetUpdatedRecallRow',
                data: { immunId: data.ImmunID, recallID: data.RecallID },
                success: function (data) {

                    if (data.Errored == "1") {

                      //  showNotificationMessage('error', 'Error calling Recall');
                    }

                    else {
                        //console.log(data);
                       // showNotificationMessage('success', 'Row updated');
                        $(trToUpdate).replaceWith(data);

                        //$(tdToUpdate).parent().replaceWith(data);
                    }
                },
                error: function (xhr, thrownError) {

                    alert("Error while tryng to call  'VP/UpdateImmunizationNote'  " + xhr.status + " " + thrownError);
                }
            });
        }

        var LoadData = function () {

            $("#recall-data").html('<img src="../../Content/fancybox_loading.gif" />');

            var url = "VP/VP/Recall_Data";
         //   showNotificationMessage('success', 'Loading Data');
            $("#recall-data").load(url, function () {

            });
        }

        $(document).ready(function () {

            $(document).on("RecallTaskSent", function (event) { $(modalRecallTaskID).modal("hide"); });
            LoadData();

            $(document).on('click', '#btn-submit-search', function (e) {

                e.preventDefault();
                var isValidType = false;
                var isValidDoctor = false;
                var isValidEnrollment = false;
                ////var isValidFiscalYear = false;
                var errorMessage = "";

                if($("#SelectedType").val() > 0)
                {
                    isValidType = true;
                }
                else{
                    errorMessage += "Please select a procedure type. ";
                }

                if ($("#DoctorID").val() > 0) {
                    isValidDoctor = true;
                }
                else {
                    errorMessage += "Please select a doctor. ";
                }

                if ($("#EnrollmentTypeId").val() > 0) {
                    isValidEnrollment = true;
                }
                else {
                    errorMessage += "Please select an enrollment type. ";
                }

                //if ($("#FiscalYear").val() > 0) {
                //    isValidFiscalYear = true;
                //}
                //else {
                //    errorMessage += "Please select a fiscal year. ";
                //}


                if (isValidType && isValidDoctor && isValidEnrollment) {
                    $("#recall-data").html('<img src="../../Content/fancybox_loading.gif" />');

                    var url = $(this).data("url");

                    $.ajax({

                        method: 'GET',
                        url: url,
                        data: $("#frm-recall").serialize(),
                        success: function (data) {
                            if (data.Errored == "1") {
                               
                            }
                            else {                               
                                $("#recall-data").html(data);
                            }
                        },
                        error: function (xhr, thrownError) {
                            checkAjaxError(xhr);
                            
                        }
                    });
                }
                else {
                    showMessageModal("error", errorMessage);
                }
            });

            $(document).on('click', '#btn-update-note', function (e) {

                e.preventDefault();

                var id = $(this).data("id");
                var note = $("#txtArea-" + id).val();

                $.ajax({

                    method: 'GET',
                    url: "VP/VP/UpdateImmunizationNote",
                    data: { ID: id, Note: note },

                    success: function (data) {

                        if (data.Errored == "1") {

                           // showNotificationMessage('error', 'Changes could not be saved');
                        }

                        else {

                          //  showNotificationMessage('success', 'Changes saved');
                            // window.location.href = window.location.href;
                        }
                    },
                    error: function (xhr, thrownError) {
                        checkAjaxError(xhr);
                        //alert("Error while tryng to call  'VP/UpdateImmunizationNote'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $(document).on('click', '.btn-send-letter1', function (e) {

                e.preventDefault();

                var errorMessage = validateSentLetter();                

                if (errorMessage == null) {
                    var url = $(this).data("url");
                    $('#ajax-loader').show();

                    var Patients = [];
                    $(".chk-selected").each(function (index) {
                        var self = $(this);
                        if (self.is(":checked")) {

                            var rowid = self.data("rowid");
                            var patientid = self.data("patientid");
                            var recallid = self.data("recallid");
                            var pname = self.data("patientname");
                            var dtserviced = self.data("dtserviced");
                            var immunname = self.data("immunname");
                            var dtlastserviced = self.data("dtlastserviced");

                            Patients.push({
                                Id: rowid,
                                PatientId: patientid,
                                RecallID: recallid,
                                PatientName: pname,
                                Name: immunname,
                                DateServiced: dtserviced,
                                LastServiceDate: dtlastserviced
                            });
                        }
                    });

                    $.ajax({

                        method: 'POST',
                        url: url,
                        data: { Patients: Patients },
                        success: function (data) {

                            if (data.Errored == "1") {
                                // showNotificationMessage('error', 'An error occurred while generatiung Letter 1');
                            }
                            else {

                                //showNotificationMessage('success', 'Letters Sent');
                                for (var i = 0; i < data.List.length ; i++) {
                                    UpdateRecallRow(data.List[i]);
                                }
                                RecallPrintView(Patients);
                            }
                        },
                        error: function (xhr, thrownError) {
                            checkAjaxError(xhr);
                        },
                        complete: function (e) {

                            $('#ajax-loader').hide();

                        }
                    });
                }
                else {                   
                    showMessageModal("error", errorMessage);
                }
            });

            $(document).on('click', '.btn-send-letter2', function (e) {


                e.preventDefault();
                var errorMessage = validateSentLetter();

                if (errorMessage == null) {
                    var url = $(this).data("url");

                    $('#ajax-loader').show();

                    var Patients = [];
                    $(".chk-selected").each(function (index) {
                        var self = $(this);
                        if (self.is(":checked")) {

                            var rowid = self.data("rowid");
                            var patientid = self.data("patientid");
                            var recallid = self.data("recallid");
                            var pname = self.data("patientname");
                            var dtserviced = self.data("dtserviced");
                            var immunname = self.data("immunname");
                            var dtlastserviced = self.data("dtlastserviced");

                            Patients.push({
                                Id: rowid,
                                PatientId: patientid,
                                RecallID: recallid,
                                PatientName: pname,
                                Name: immunname,
                                DateServiced: dtserviced,
                                LastServiceDate: dtlastserviced
                            });
                        }
                    });

                    $.ajax({

                        method: 'POST',
                        url: url,
                        data: { Patients: Patients },
                        //data: $("#frm-recall").serialize(),
                        success: function (data) {
                            if (data.Errored == "1") {
                                //  showNotificationMessage('error', 'An error occurred while generatiung Letter 1');
                            }
                            else {

                                //  showNotificationMessage('success', 'Letters Sent');
                                for (var i = 0; i < data.List.length ; i++) {                                   
                                    UpdateRecallRow(data.List[i]);                                   
                                }
                                RecallPrintView(Patients);
                            }
                        },
                        error: function (xhr, thrownError) {
                            checkAjaxError(xhr);                            
                        },
                        complete: function (e) {

                            $('#ajax-loader').hide();

                        }
                    });
                }
                else {
                    showMessageModal("error", errorMessage);
                }
            });

            //$(document).on('click', '.btn-send-letter1', function (e) {

            //    e.preventDefault();

            //    var url = $(this).data("url");                
            //    $('#ajax-loader').show();

            //    var Patients = [];
            //    $(".chk-selected").each(function (index) {
            //        var self = $(this);
            //        if (self.is(":checked")) {

            //            var rowid = self.data("rowid");
            //            var patientid = self.data("patientid");
            //            var recallid = self.data("recallid");
            //            var pname = self.data("patientname");
            //            var dtserviced = self.data("dtserviced");
            //            var immunname = self.data("immunname");

            //            Patients.push({
            //                Id: rowid,
            //                PatientId: patientid,
            //                RecallID: recallid,
            //                PatientName: pname,
            //                Name: immunname,
            //                DateServiced: dtserviced
            //            });
            //        }
            //    });

            //    //console.log(Patients);

            //    $.ajax({

            //        method: 'POST',
            //        url: url,
            //        //data: $("#frm-recall").serialize(),
            //        data: { Patients: Patients },
            //        success: function (data) {

            //            if (data.Errored == "1") {
            //               // showNotificationMessage('error', 'An error occurred while generatiung Letter 1');
            //            }
            //            else {
                            
            //                //showNotificationMessage('success', 'Letters Sent');
            //                for (var i = 0; i < data.List.length ; i++) {
            //                    //console.log(data.List[i]);
            //                    UpdateRecallRow(data.List[i]);
            //                }                            
            //                RecallPrintView(Patients);
            //            }
            //        },
            //        error: function (xhr, thrownError) {
            //            checkAjaxError(xhr);
            //            //alert("Error while tryng to call  'VP/Send Letter 1'  " + xhr.status + " " + thrownError);
            //        },
            //        complete: function (e) {
                       
            //            $('#ajax-loader').hide();

            //        }
            //    });

                
            //});

            //$(document).on('click', '.btn-send-letter2', function (e) {


            //    e.preventDefault();

            //    var url = $(this).data("url");

            //    $('#ajax-loader').show();

            //    var Patients = [];
            //    $(".chk-selected").each(function (index) {
            //        var self = $(this);
            //        if (self.is(":checked")) {

            //            var rowid = self.data("rowid");
            //            var patientid = self.data("patientid");
            //            var recallid = self.data("recallid");
            //            var pname = self.data("patientname");
            //            var dtserviced = self.data("dtserviced");
            //            var immunname = self.data("immunname");

            //            Patients.push({
            //                Id: rowid,
            //                PatientId: patientid,
            //                RecallID: recallid,
            //                PatientName: pname,
            //                Name: immunname,
            //                DateServiced: dtserviced
            //            });
            //        }
            //    });

            //    $.ajax({

            //        method: 'POST',
            //        url: url,
            //        data: { Patients: Patients },
            //        //data: $("#frm-recall").serialize(),
            //        success: function (data) {
            //            if (data.Errored == "1") {
            //              //  showNotificationMessage('error', 'An error occurred while generatiung Letter 1');
            //            }
            //            else {

            //              //  showNotificationMessage('success', 'Letters Sent');
            //                for (var i = 0; i < data.List.length ; i++) {
            //                    //console.log(data.List[i]);
            //                    UpdateRecallRow(data.List[i]);
            //                    //var name = "#span-dt-contacted_" + data.List[i].RecallID;
            //                    //$(name).text(data.List[i].DateStr);
            //                    //$(name).fadeTo(0, 0);
            //                    //$(name).fadeTo("slow", 1);
            //                }
            //            }
            //        },
            //        error: function (xhr, thrownError) {
            //            checkAjaxError(xhr);
            //            //alert("Error while tryng to call  'VP/Send Letter 2'  " + xhr.status + " " + thrownError);
            //        },
            //        complete: function (e) {

            //            $('#ajax-loader').hide();

            //        }
            //    });

            //});

            $(document).on('click', '.btn-contact-phone', function (e) {


                e.preventDefault();

                var url = $(this).data("url");

                $('#ajax-loader').show();

                var Patients = [];
                $(".chk-selected").each(function (index) {
                    var self = $(this);
                    if (self.is(":checked")) {

                        var rowid = self.data("rowid");
                        var patientid = self.data("patientid");
                        var recallid = self.data("recallid");
                        var pname = self.data("patientname");
                        var dtserviced = self.data("dtserviced");
                        var immunname = self.data("immunname");

                        Patients.push({
                            Id: rowid,
                            PatientId: patientid,
                            RecallID: recallid,
                            PatientName: pname,
                            Name: immunname,
                            DateServiced: dtserviced
                        });
                    }
                });

                $.ajax({

                    method: 'POST',
                    url: url,
                    data: { Patients: Patients },
                    //data: $("#frm-recall").serialize(),
                    success: function (data) {
                        if (data.Errored == "1") {
                           // showNotificationMessage('error', 'An error occurred while Contacting by Phone');
                        }
                        else {

                          //  showNotificationMessage('success', 'Contact By Phone');
                            for (var i = 0; i < data.List.length ; i++) {
                                UpdateRecallRow(data.List[i]);
                                //var name = "#span-contacted-phone_" + data.List[i].RecallID;
                                //$(name).text(data.List[i].Status == "True" ? "Yes" : "No");
                                //$(name).fadeTo(0, 0);
                                //$(name).fadeTo("slow", 1);
                                //var name2 = "#span-dt-contacted_" + data.List[i].RecallID;
                                //$(name2).text(data.List[i].DateStr);
                                //$(name2).fadeTo(0, 0);
                                //$(name2).fadeTo("slow", 1);
                            }
                        }
                    },
                    error: function (xhr, thrownError) {
                        checkAjaxError(xhr);
                        //alert("Error while tryng to call  'VP/Contact By Phone'  " + xhr.status + " " + thrownError);
                    },
                    complete: function (e) {

                        $('#ajax-loader').hide();

                    }
                });

            });

            $(document).on('click', '.btn-send-task', function (e) {
                e.preventDefault();

                var patientRecordIds = "";
                $(".chk-selected").each(function (index) {
                    var self = $(this);
                    if (self.is(":checked")) {
                        if (patientRecordIds != "") {
                            patientRecordIds += ",";
                        }
                        patientRecordIds += self.data("patientid");
                    }
                });

                if (patientRecordIds == "") {
                    showMessageModal("error", "Please select patient(s)", false);
                    return;
                }

                $('#ajax-loader').show();
                var data = { practiceDoctorId: $("#DoctorID").val(), patientRecordIds: patientRecordIds };
                var url = 'ContactManagers/RecallTask';
                $(modalRecallTaskID).load(url, data, function () {
                    $(modalRecallTaskID).modal({ keyboard: false, backdrop: 'static' }, 'show');
                    $('#ajax-loader').hide();
                });
            });

            $(document).on('click', '.hl-info', function (e) {

                e.preventDefault();

                $('#ajax-loader').show();

                var url = $(this).data("url");

                $("#recall-modal-content").load(url, function () {

                    $("#recall-modal-container").modal({
                        keyboard: false,
                        backdrop: 'static'
                    }, 'show');

                    $('#ajax-loader').hide();
                });
            });

            $(document).on('click', '.btn-cancel-model', function (e) {
                e.preventDefault();
                $('#recall-modal-container').modal('hide');
            });

            //add following code to prevent multiple modals overlay issue
            $(document).on('show.bs.modal', '.modal', function (event) {
                var zIndex = 1040 + (10 * $('.modal:visible').length);
                $(this).css('z-index', zIndex);
                setTimeout(function () { $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack'); }, 0);
            });

            $(document).on('click', '#btn-print-recall-letters', function (e) {
                e.preventDefault();
                var modalId = "#printRecallLetterModal";
                var htmlToPrint = $('#patients-recall-letters').html();
                var html = '<!DOCTYPE html><html>';
                html+='<head><title>Recall Letter Print</title></head>';
                html+='<body>'+htmlToPrint+'</body>';
                html+='</html>';
                //$(modalId).modal('hide');

                var recallPrint = window.open('@Url.Action("RecallLetterPrint","VP",new { area = "VP" })');                
                recallPrint.document.open();             
                recallPrint.document.write(html);             
                recallPrint.document.close();
                recallPrint.print();
                recallPrint.close();

            });
        });

        

        var RecallPrintView = function(patientsArray)
        {
            var errorMessage = validateSentLetter();

            if (errorMessage == null) {
                var modalId = "#printRecallLetterModal";
                var modalContentContainer = "#print-recall-letter-content";
                var procTypeId = $("#SelectedType").val();
                var procTypeName = $('#SelectedType option:selected').html();
                var letterCode = $("#LetterCode").val();
                var letterCodeDSC = $('#LetterCode option:selected').html();
                var docId = $("#DoctorID").val();
                var docName = $('#DoctorId option:selected').html();
                var url = "VP/VP/GetRecallLetterPrintView";

                //var patientIds = [];
                var patients = [];
                for (var i = 0; i < patientsArray.length; i++)
                {
                    //patientIds.push(patientsArray[i].PatientId);
                    //console.log(patientsArray[i].PatientId);

                    patients.push({
                        Id: patientsArray[i].Id,
                        PatientId: patientsArray[i].PatientId,
                        RecallId: patientsArray[i].RecallID,                       
                        DateLastServiced: patientsArray[i].LastServiceDate                        
                    });
                }
                
                
                $(modalId).on('show.bs.modal', function () {                    
                    $(modalContentContainer).html('<img src="../../Content/fancybox_loading.gif" />');
                });               

                $(modalId).on('shown.bs.modal', function () {
                    
                });

                $(modalId).on('hidden.bs.modal', function () {
                    $(modalContentContainer).html('');
                });

                $(modalId).modal({
                    keyboard: false,
                    backdrop: 'static'
                }, 'show');

                $.ajax({

                    method: 'POST',
                    url: url,
                    data: {
                        DoctorId: docId,
                        LetterCode: letterCode,
                        LetterName: letterCodeDSC,
                        ProcedureTypeId: procTypeId,
                        ProcedureTypeName: procTypeName,
                        Patients: patients
                        //PatientIds: patientIds
                    },
                    complete: function (e) {
                        $('#ajax-loader').hide();
                    },
                    success: function (data) {
                        $(modalContentContainer).html(data);                        
                    },
                    error: function (xhr, thrownError) {
                        hideModal(modalId);
                        checkAjaxError(xhr);                        
                    }
                });                
            }
            else {
                showMessageModal("error", errorMessage);
            }
        }

        function validateSentLetter()
        {
            var isValidType = false;
            var isValidDoctor = false;
            var isValidLetterCode = false;
            var hasSelected = false;
            var errorMessage = "";

            if ($('.chk-selected').is(":checked")) {
                hasSelected = true;
            }
            else {
                errorMessage += "Please select a checkbox from the grid. ";
            }

            if($("#SelectedType").val() > 0)
            {
                isValidType = true;
            }
            else
            {
                errorMessage += "Please select a procedure type. ";
            }

            if ($("#DoctorID").val() > 0) {
                isValidDoctor = true;
            }
            else {
                errorMessage += "Please select a doctor. ";
            }       

            if ($("#LetterCode").val() > 0) {
                isValidLetterCode = true;
            }
            else {
                errorMessage += "Please select a Recall Letter. ";
            }

            if (isValidType && isValidDoctor & isValidLetterCode && hasSelected) {
                return null;
            }
            else {
                return errorMessage;
            }
        }
        
    </script>
}
<style>
    .table th {
        text-align: center;
    }

    .table-striped > tbody > tr:nth-of-type(2n+1) {
        /*background-color: #e8f2f8;*/
    }

    .ds-filter-container {
        /*padding: 15px;*/
        /*background-color: #F0F0F0;*/
    }

    #LetterCode{
        height:30px !important;
    }
</style>
<div>
    <div id="recall-modal-container" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document" style="width:auto">
            <div class="modal-content">
                <div class="modal-body ">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <div id="recall-modal-content"></div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>
<div id="recall-task-container" class="modal fade" tabindex="-1" role="dialog"></div>
<div class="row">&nbsp;</div>
@using (Html.BeginForm("Recall", "VP", FormMethod.Post, new { @id = "frm-recall", model = @Model }))
{

    <div class="ds-filter-container">
        <h4 class="heading">Filters</h4>
        <div class="col-md-12">&nbsp;</div>
        <div class="row">
            <div class="col-md-12">
                <div class="form-inline pull-left">
                    <div class="form-inline">
                        <div class="form-group form-group-sm">
                            <label for="DoctorID" class="control-label">Doctor</label>
                            @Html.DropDownListFor(m => m.DoctorID, new SelectList(Model.Doctors, "Value", "Text"), "Choose One", new { @class = "form-control" })
                            <span id="validation-doctor" class="text-danger"></span>
                        </div>

                        <div class="form-group form-group-sm">
                            <label class="control-label">Procedure Type</label>
                            @Html.DropDownListFor(model => model.SelectedType, new SelectList(Model.Immunizations, "Id", "Name"), "Choose One", new { @class = "form-control" })
                            <span id="validation-procedure" class="text-danger"></span>
                        </div>

                        <div class="form-group form-group-sm">
                            <label class="control-label">Enrollment Type</label>
                            @*@Html.DropDownListFor(model => model.EnrollmentTypeId, new SelectList(Model.EnrollmentTypes, "Value", "Text"), "Choose One", new { @class = "form-control" })*@
                            @Html.DropDownListFor(model => model.EnrollmentTypeId, new SelectList(Model.EnrollmentTypes, "Value", "Text"), new { @class = "form-control" })
                            <span id="validation-enrollmenttype" class="text-danger"></span>
                        </div>

                        @*<div class="form-group form-group-sm">
                            <label for="pwd">Fiscal Year End:</label>
                            @Html.DropDownListFor(m => m.FiscalYear, new SelectList(Model.Years, "Value", "Text"), "Choose One", new { @class = "dropdown form-control" })
                            <span id="validation-year" class="text-danger"></span>
                        </div>*@

                        <div class="form-group form-group-sm">
                            <label class="control-label">Status</label>
                            @Html.DropDownListFor(model => model.SelectedStatus, new SelectList(Model.Statuses, "Id", "Status"), "-- ALL --", new { @class = "form-control" })
                        </div>                        

                        <div class="form-group form-group-sm">
                            <a class="btn btn-default btn-sm btn-spacing"
                                data-url='@Url.Action("Recall_Data")' id="btn-submit-search">
                                <span class="glyphicon glyphicon-refresh default-text-color"> </span> Refresh
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">&nbsp;</div>
            @*<div class="col-md-12">
                    <div class="form-group-sm">
                        <div class="form-inline">
                            <div class="form-group form-group-sm">
                                @Html.CheckBoxFor(x => x.TestsOnlyExt, new { @class = "chk-search-mode checkbox" })
                                <label class="control-label" for="chk-test-only-ecg ">Tests Only Ext</label>
                                @Html.CheckBoxFor(x => x.ExcludeECG, new { @class = "chk-search-mode checkbox" })
                                <label class="control-label" for="chk-exclude-ecg ">Exclude ECGs</label>
                            </div>
                        </div>
                    </div>
                </div>*@
            <div class="col-md-12">&nbsp;</div>
        </div>
    </div>

    <div class="row">&nbsp;</div>
    <div class="row">&nbsp;</div>

    <div class="text-center">

        <div id="recall-data"></div>

        @*<div class="row">

            <a href="#"
                data-url='@Url.Action("SendLetter1","VP" ,new {  area="VP" })'
                class="btn btn-default  btn-sm btn-send-letter1">Send Letter 1</a>
            <a data-url='@Url.Action("SendLetter2","VP" ,new {  area="VP" })'
                href="#" class="btn btn-default btn-sm btn-send-letter2">Send Letter 2</a>

            <div class="form-group form-group-sm">               
                @Html.DropDownListFor(model => model.LetterCode, new SelectList(Model.RecallLetters, "Code", "Description"), "Choose One", new { @class = "form-control" })
                
            </div>

            <a data-url='@Url.Action("ContactPhone","VP" ,new {  area="VP" })'
                href="#" class="btn btn-default btn-sm btn-contact-phone">Contacted By Phone</a>
            <a data-url='@Url.Action("ContactPhone","VP" ,new {  area="VP" })'
                href="#" class="btn btn-default btn-sm btn-send-task">Send Tasks</a>
        </div>*@
        <div class="form-inline">
            <div class="form-group form-group-sm">
                <label class="control-label">Recall Letter</label>
                @Html.DropDownListFor(model => model.LetterCode, new SelectList(Model.RecallLetters, "Code", "Description"), "Choose One", new { @class = "form-control" })
                <a href="#"
                   data-url='@Url.Action("SendLetter1","VP" ,new {  area="VP" })'
                   class="btn btn-default  btn-sm btn-send-letter1">Send Letter 1</a>
                <a data-url='@Url.Action("SendLetter2","VP" ,new {  area="VP" })'
                   href="#" class="btn btn-default btn-sm btn-send-letter2">Send Letter 2</a>            

                <a data-url='@Url.Action("ContactPhone","VP" ,new {  area="VP" })'
                   href="#" class="btn btn-default btn-sm btn-contact-phone">Contacted By Phone</a>
                <a data-url='@Url.Action("ContactPhone","VP" ,new {  area="VP" })'
                   href="#" class="btn btn-default btn-sm btn-send-task">Send Tasks</a>
            </div>
        </div>
    </div>
}

    <div class="row">&nbsp;</div>
    <div class="row">&nbsp;</div>
    <div class="row">&nbsp;</div>


<div class="modal fade" id="printRecallLetterModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div id="print-recall-letter-content" class="modal-content">
            @*<div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span id="saved-title"></span></h4>
            </div>

            <div class="modal-body">
                <div id="divMedicationPrint">
                    <iframe id="print-med-frame" style="width:100%;background-color:#ffffff;border:none; height:100%;" src="@Url.Action("print","patientmedications",new { area="medications" })"></iframe>
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-print-medication-view" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>*@
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>