﻿@model  Cerebrum.ViewModels.VP.VMBonusReport
@inherits Cerebrum30.BaseViewPage<Cerebrum.ViewModels.VP.VMBonusReport>
@{
    ViewBag.ModuleName = "Bonus Report";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<script>
    $(document).on('click', '#hl-bonus-report', function (e) {

        e.preventDefault();
        $('#ajax-loader').show();
        var url = $(this).data("url");

        $.ajax({
            method: 'POST',
            url: url,
            data: $('#frm-bonus').serialize(),
            success: function (data) {
                if (data.Errored == 1)
                {
                    $('#span-result').html(data.Message);
                }
                else {
                    $('#bonus-report-wrapper').html(data);
                }
                
            },
            error: function (xhr, thrownError) {
                checkAjaxError(xhr);
            },
            complete: function () {
                $('#ajax-loader').hide();
            }
        });
    });

    $(document).on('click', '#hl-bonus-code', function (e) {
        e.preventDefault();
        $('#ajax-loader').show();
        var url = $(this).data("url");
        var data = { practiceDoctorId: 5, officeId: 2, bonusCode: "Q112A", billYear: 2017 }

        $.ajax({
            method: 'POST',
            url: url,
            data: data,
            success: function (result) {
                showMessageModal("info", result, false);
            },
            error: function (xhr, thrownError) {
                showMessageModal("error", "Error while tryng to call 'VP/VP/AddReportPhraseAction'", false);
            },
            complete: function () {
                $('#ajax-loader').hide();
            }
        });
    });
</script>

@using (Html.BeginForm("GenerateBonus", "VP", FormMethod.Post, new { @id = "frm-bonus", model = @Model }))
{
    <div class="container">
        <div class="text-center">
            <div class="row">
                <div class="form-group">
                    <label for="email">Doctor:</label>                    
                        @Html.DropDownListFor(m => m.DocID, new SelectList(Model.Doctors, "Value", "Text"), "--All--", new { @class = "dropdown form-control" })                    
                </div>
                <div class="form-group">
                    <label for="pwd">Fiscal Year End:</label>
                    @Html.DropDownListFor(m => m.FiscarYear, new SelectList(Model.Years, "Value", "Text"), "--All--", new { @class = "dropdown form-control" })
                </div>
                <div class="form-group">
                    <label for="pwd">Immunization:</label>
                    @Html.DropDownListFor(m => m.ImmunizationType, new SelectList(Model.Types, "Id", "Name"), "--All--", new { @class = "dropdown form-control" })
                </div>
                <div class="form-group">
                    <span id="span-result" style="color:red"></span>
                </div>
                <a href="#"
                   data-url='@Url.Action("GenerateBonus")'
                   id="hl-bonus-report" class="btn btn-default">Generate Report</a>
                <br /><br />
                <a href="#"
                   data-url='@Url.Action("BillBonusCode", "GroupBill", new { Area = "Bill" })'
                   id="hl-bonus-code" class="btn btn-default">Bill Bonus Code</a>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div style="margin-top:15px;" id="bonus-report-wrapper">

                </div>
            </div>
        </div>
    </div>

}