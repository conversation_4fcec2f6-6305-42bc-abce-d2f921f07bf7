using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
using Cerebrum30.Controllers;
using Cerebrum30.Filters;
using Cerebrum30.Security;
using Cerebrum30.Utility;
using Cerebrum.ExternalDocument;
using Cerebrum.BLL.Common;
using Cerebrum.BLL.Documents;
using Cerebrum.ViewModels.ExternalDocument;
using System.Net.Http;

/// <summary>
/// External Document's Controller
/// </summary>
//[CheckPermissions(PermissionType = "externalreports")]
namespace Cerebrum30.Areas.ExternalDocument.Controllers
{
    [Area("ExternalDocument")]
    public class ExternalDocumentController : BaseController, ILogGeneric
    {
        private ExternalDocumentBLL bll;
        private IHttpClientFactory _httpClientFactory;
        private IDocumentsBLL _documentsBLL;
        private readonly ITokenHelper _utilityTokenHelper;
        private readonly Cerebrum.BLL.Common.ExternalCommBLL _externalCommBLL;

        /// <summary>
        /// Constructor of External Document Controller
        /// </summary>
        public ExternalDocumentController(ExternalDocumentBLL exbll, IDocumentsBLL documentsBLL, IHttpClientFactory httpClientFactory, ITokenHelper utilityTokenHelper, Cerebrum.BLL.Common.ExternalCommBLL externalCommBLL) : base()
        {
            bll = exbll;
            _documentsBLL = documentsBLL;
            _httpClientFactory = httpClientFactory;
            _utilityTokenHelper = utilityTokenHelper;
            _externalCommBLL = externalCommBLL;
        }

        /// <summary>
        /// Get: ExternalDocument/Index?officeId=2
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> Index(string practiceDoctorId, string patientRecordId)
        {
            IndexInitializationReponse response = new IndexInitializationReponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.Index(practiceDoctorId, patientRecordId);
                if (string.IsNullOrWhiteSpace(response.errorMessage))
                {
                    int userId = CerebrumUser.UserId;
                    int practiceId = CerebrumUser.PracticeId;
                    response.clinicServerAccessDatas = _documentsBLL.GetDownloadURLs(practiceId);
                    foreach (var accessData in response.clinicServerAccessDatas)
                    {
                        accessData.clinicServerToken = await _utilityTokenHelper.GenerateTokenAsync(userId, practiceId, accessData.clinicServerUrl);
                        accessData.clinicServerUrl = $"{accessData.clinicServerUrl.TrimEnd('/')}/{Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH.TrimStart('/').TrimEnd('/')}/";
                    }

                    var practiceSetting = _externalCommBLL.GetPracticeCommTypeByPracticeId(practiceId, 1001); // 1001 = hrm
                    if (practiceSetting != null)
                        response.hrmConfidentialityStatement = practiceSetting.ConfidentialityStatement;
                }
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "Index");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return View(response);
        }

        /// <summary>
        /// Get: ExternalDocument/Assignment
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> Assignment(string externalDocument, int? officeId)
        {
            ClinicServerAccessData clinicServerAccessData = new ClinicServerAccessData();

            int clinicServerOfficeId = officeId ?? CerebrumUser.OfficeId;

            if (clinicServerOfficeId > 0)
            {
                int practiceId = CerebrumUser.PracticeId;
                clinicServerAccessData.officeId = clinicServerOfficeId;

                string url = _documentsBLL.GetDownloadURL(practiceId, clinicServerOfficeId);

                if (!string.IsNullOrWhiteSpace(url))
                {
                    int userId = CerebrumUser.UserId;
                    clinicServerAccessData.clinicServerToken = await _utilityTokenHelper.GenerateTokenAsync(userId, practiceId, url);
                    clinicServerAccessData.clinicServerUrl = $"{url.TrimEnd('/')}/{Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH.TrimStart('/').TrimEnd('/')}/";
                }
            }

            return View(clinicServerAccessData);
        }

        /// <summary>
        /// Get: ExternalDocument/GeneralUpload?officeId=2
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> FaxAssignment(AssignmentRequest request)
        {
            ExternalDocumentAssignmentResponse response = new ExternalDocumentAssignmentResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.FaxAssignment(_httpClientFactory, request, CerebrumUser.UserPermissions);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "FaxAssignment");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("_Fax", response);
        }

        /// <summary>
        /// Get: ExternalDocument/HL7?officeId=2
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> HL7Assignment(AssignmentRequest request)
        {
            ExternalDocumentAssignmentResponse response = new ExternalDocumentAssignmentResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.HL7Assignment(request, CerebrumUser.UserPermissions);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "HL7Assignment");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("_HL7", response);
        }

        /// <summary>
        /// Get: ExternalDocument/HRM?officeId=2
        /// </summary>
        /// <returns></returns>
        public async Task<ActionResult> HRMAssignment(AssignmentRequest request)
        {
            ExternalDocumentAssignmentResponse response = new ExternalDocumentAssignmentResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.HRMAssignment(request, CerebrumUser.UserPermissions);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "HRMAssignment");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return PartialView("_HRM", response);
        }

        /// <summary>
        /// load one page of external document
        /// </summary>
        /// <param name="request">page request</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> LoadExternalDocumentList(DocumentListRequest request)
        {
            DocumentListResponse response = new DocumentListResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.GetExternalDocumentList(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "LoadExternalDocumentList");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// load external document detail info
        /// </summary>
        /// <param name="request">document detail request</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> LoadExternalDocumentDetail(DocumentDetailRequest request)
        {
            DocumentDetailResponse response = new DocumentDetailResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.GetExternalDocumentDetail(request, -1);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "LoadExternalDocumentDetail");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// update external document's comment
        /// </summary>
        /// <param name="request">update request</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> UpdateExternalDocument(DocumentUpdateRequest request)
        {
            DocumentDetailResponse response = new DocumentDetailResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.UpdateExternalDocument(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "UpdateExternalDocument");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// update external document's comment
        /// </summary>
        /// <param name="request">update request</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> UpdateExternalDocumentDetail(DocumentDetailUpdateRequest request)
        {
            DocumentDetailUpdateResponse response = new DocumentDetailUpdateResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.UpdateExternalDocumentDetail(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "UpdateExternalDocumentDetail");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// update external document's comment
        /// </summary>
        /// <param name="request">update request</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> IgnoreExternalDocument(DocumentDetailUpdateRequest request)
        {
            DocumentDetailUpdateResponse response = new DocumentDetailUpdateResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.IgnoreExternalDocument(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "IgnoreExternalDocument");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// alert and mark seeen external document 
        /// </summary>
        /// <param name="newTask">new task request</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<string> AlertReport(Cerebrum.ViewModels.ContactManagerNew.ContactManagerTaskData newTask)
        {
            string response = string.Empty;
            try
            {
                GetExternalDocumentBLL();
                int officeId = CerebrumUser.OfficeId;
                response = await bll.AlertReport(newTask, officeId);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "AlertReport");
                response = "There was a problem. Please Contact Administrator! ";
            }

            return response;
        }

        /// <summary>
        /// Post: ExternalDocument/GetExternalDocumentFileName
        /// </summary>
        /// <param name="officeId">office Id</param>
        /// <param name="uploadFolder">upload Folder</param> 
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> GetExternalDocumentFileName(string officeId, string uploadFolder)
        {
            ExternalDocumentAssignmentResponse response = new ExternalDocumentAssignmentResponse();
            try
            {
                GetExternalDocumentBLL();
                FaxFileNameResponse faxResponse = await bll.GetFaxFileNames(_httpClientFactory, officeId, uploadFolder);
                response.errorMessage = faxResponse.errorMessage;
                response.externalDocuments = faxResponse.faxFileNames;
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GetExternalDocumentFileName");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// Post: ExternalDocument/MoveExternalDocument
        /// </summary>
        /// <param name="officeId">office Id</param>
        /// <param name="externalDocumentName">external document's name</param> 
        /// <param name="oldFolder">old folder</param> 
        /// <param name="newFolder">new folder</param> 
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<ContentResult> MoveExternalDocument(string officeId, string externalDocumentName, string oldFolder, string newFolder)
        {
            string errorMessage = string.Empty;
            try
            {
                GetExternalDocumentBLL();
                errorMessage = await bll.MoveExternalDocument(_httpClientFactory, officeId, externalDocumentName, oldFolder, newFolder);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "MoveExternalDocument");
                errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Content(errorMessage);
        }

        /// <summary>
        /// Post: ExternalDocument/DeleteExternalDocument
        /// </summary>
        /// <param name="officeId">office Id</param>
        /// <param name="externalDocumentName">external document's name</param> 
        /// <param name="folder">folder</param> 
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<ContentResult> DeleteExternalDocument(string officeId, string externalDocumentName, string folder)
        {
            string errorMessage = string.Empty;
            try
            {
                GetExternalDocumentBLL();
                errorMessage = await bll.DeleteExternalDocument(_httpClientFactory, officeId, externalDocumentName, folder);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "DeleteExternalDocument");
                errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Content(errorMessage);
        }

        /// <summary>
        /// Post: ExternalDocument/AssignFax
        /// </summary>
        /// <param name="request">assign data</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<ContentResult> AssignFax(AssignFaxRequest request)
        {
            string errorMessage = string.Empty;
            try
            {
                GetExternalDocumentBLL();
                errorMessage = await bll.AssignFax(_httpClientFactory, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "AssignFax");
                errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Content(errorMessage);
        }

        /// <summary>
        /// Post: ExternalDocument/AssignHL7
        /// </summary>
        /// <param name="request">assign data</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<ContentResult> AssignHL7(AssignHL7HRMReportRequest request)
        {
            string errorMessage = string.Empty;
            try
            {
                GetExternalDocumentBLL();
                errorMessage = await bll.AssignHL7(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "AssignHL7");
                errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Content(errorMessage);
        }

        /// <summary>
        /// Post: ExternalDocument/AssignHRM
        /// </summary>
        /// <param name="request">assign data</param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<ContentResult> AssignHRM(AssignHL7HRMReportRequest request)
        {
            string errorMessage = string.Empty;
            try
            {
                GetExternalDocumentBLL();
                errorMessage = await bll.AssignHRM(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "AssignHRM");
                errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Content(errorMessage);
        }

        /// <summary>
        /// Post: ExternalDocument/GetPatientData
        /// </summary>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> GetPatientData(int appointmentId, int officeId, int patientRecordId, int patientData, int requisitionData)
        {
            PatientDataResponse response = new PatientDataResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.GetPatientData(appointmentId, officeId, patientRecordId, patientData, requisitionData);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GetPatientData");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// Post: ExternalDocument/GetPatientList
        /// </summary>
        /// <param name="term">patient filter</param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public JsonResult GetPatientList(string term)
        {
            List<LabelValueViewModel> patientList = new List<LabelValueViewModel>();
            try
            {
                GetExternalDocumentBLL();
                patientList = bll.GetPatientList(term);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GetPatientList");
                return null;
            }

            return Json(patientList);
        }

        /// <summary>
        /// get comment for HL7 report
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> GetHL7ReportComment(DocumentDetailRequest request)
        {
            DocumentDetailResponse response = new DocumentDetailResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.GetHL7ReportComment(request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GetHL7ReportComment");
                return null;
            }

            return Json(response);
        }

        /// <summary>
        /// Get: ExternalDocument/GetHL7Report
        /// </summary>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> GetHL7Report(AssignReportRequest request)
        {
            HL7Document response = new HL7Document();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.GetHL7Report(request, CerebrumUser.UserPermissions);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GetHL7Report");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// Get: ExternalDocument/GetHL7ReportDetail
        /// </summary>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> GetHL7ReportDetail(int hl7ReportId)
        {
            HL7ReportDetail response = new HL7ReportDetail();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.GetHL7ReportDetailData(hl7ReportId, CerebrumUser.UserPermissions);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GetHL7ReportDetail");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// Get: ExternalDocument/GetHRMReport
        /// </summary>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> GetHRMReport(AssignReportRequest request)
        {
            HRMDocument response = new HRMDocument();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.GetHRMReport(request, CerebrumUser.UserPermissions);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GetHRMReport");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// Get: ExternalDocument/GetHRMReportDetail
        /// </summary>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> GetHRMReportDetail(int hrmReportId)
        {
            HRMReportDetail response = new HRMReportDetail();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.GetHRMReportDetailData(hrmReportId, CerebrumUser.UserPermissions);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GetHRMReportDetail");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// Get: ExternalDocument/UpdateAssignCounter
        /// </summary>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> UpdateAssignCounter(AssignmentRequest request)
        {
            AssignCounterResponse response = new AssignCounterResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.UpdateAssignCounter(_httpClientFactory, request);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "UpdateAssignCounter");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        /// <summary>
        /// Get: ExternalDocument/UpdateAssignCounter
        /// </summary>
        /// <returns></returns>
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> AddNewReportClass(string reportClassName)
        {
            int reportClassId = 0;
            string errorMessage = string.Empty;
            try
            {
                GetExternalDocumentBLL();
                reportClassId = await bll.AddNewReportClass(reportClassName);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "AddNewReportClass");
                errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(new { errorMessage = errorMessage, reportClassId = reportClassId });
        }

        [ValidateAntiForgeryToken]
        public async Task<JsonResult> AddNewReportCategory(int classId, string reportCategoryName)
        {
            int reportCategoryId = 0;
            string errorMessage = string.Empty;
            try
            {
                GetExternalDocumentBLL();
                reportCategoryId = await bll.AddNewReportCategory(classId, reportCategoryName);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "AddNewReportCategory");
                errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(new { errorMessage = errorMessage, reportCategoryId = reportCategoryId });
        }

        [ValidateAntiForgeryToken]
        public async Task<JsonResult> SavePdf(SavePdfRequest request)
        {
            SavePdfResponse response = new SavePdfResponse();
            try
            {
                GetExternalDocumentBLL();
                response = await bll.SavePdf(request, _httpClientFactory);
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "SavePdf");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return Json(response);
        }

        private void GetExternalDocumentBLL()
        {
            string ipAddress = IPAddress(Request);
            bll.SetExternalDocumentBLL(CerebrumUser.PracticeId, CerebrumUser.UserId, User.FindFirstValue(ClaimTypes.NameIdentifier), ipAddress);
        }

        private string LogException(string methodName, Exception ex, string extraMessage)
        {
            string logMessage = extraMessage + Environment.NewLine + methodName + Environment.NewLine + ex.Message;
            if (ex.InnerException != null)
                logMessage += Environment.NewLine + ex.InnerException.Message;
            if (ex.Source != null)
                logMessage += Environment.NewLine + ex.Source;
            if (ex.StackTrace != null)
                logMessage += Environment.NewLine + ex.StackTrace;
            this.LogDebugFormat("Exception: {0}", logMessage);
            return logMessage;
        }
    }
}