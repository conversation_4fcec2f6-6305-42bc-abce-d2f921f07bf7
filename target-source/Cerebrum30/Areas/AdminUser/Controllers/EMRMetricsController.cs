using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Cerebrum30.Areas.AdminUser.Models;
using Cerebrum30.DAL.DataAccessGB;
using Cerebrum30.Utility;
using Cerebrum30.Controllers;
using Cerebrum30.Areas.PdfConversions;
using Cerebrum30.Filters;
using Cerebrum.Data;
using Microsoft.AspNetCore.Hosting;

namespace Cerebrum30.Areas.AdminUser.Controllers
{
    [Area("AdminUser")]
    public class EMRMetricsController : BaseController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly CerebrumContext _context;
        private readonly RepositoriesForEMRRecords _repositoriesForEMRRecords;

        public EMRMetricsController(IWebHostEnvironment webHostEnvironment, CerebrumContext context, RepositoriesForEMRRecords repositoriesForEMRRecords)
        {
            _webHostEnvironment = webHostEnvironment;
            _context = context;
            _repositoriesForEMRRecords = repositoriesForEMRRecords;
        }

        // GET: EMRMetrics
        public ActionResult Index()
        {
            return View();
        }

        public ActionResult PatientVisitsStatistics()
        {
            VistStatistics model = new Models.VistStatistics();
            model.appointments = "";
            model.billing = "";
            model.enc_notes = "";
            model.problem_list = "";
            model.documents = "";
            model.problem_list = "";
            model.prescriptions = "";
            model.reminders = "";
            model.labs = "";

            return View(model);
        }

        [HttpPost]
        public ActionResult PatientVisitsStatistics(VistStatistics emrModel)
        {
            Tuple<bool, DateTime> retStart = CheckTileFormat(emrModel.startDate_s);
            Tuple<bool, DateTime> retEnd = CheckTileFormat(emrModel.endDate_s);
            if (retStart.Item1 == false || retEnd.Item1 == false)
            {
                emrModel.message = "There was problem to to validate dates.";
                return View(emrModel);
            }

            if (emrModel.doctorIdHid == 0)
            {
                emrModel.message = "Select Doctor before search!";
                return View(emrModel);
            }

            DateTime startT = new DateTime(retStart.Item2.Year, retStart.Item2.Month, retStart.Item2.Day, 0, 0, 0);
            DateTime endT = new DateTime(retEnd.Item2.Year, retEnd.Item2.Month, retEnd.Item2.Day, 23, 59, 59);
            emrModel.endDate = endT;
            emrModel.startDate = startT;
            //emrModel.endDate = retEnd.Item2;
            //emrModel.startDate = retStart.Item2;
            emrModel.practiceId = CerebrumUser.PracticeId;
            VistStatistics model = _repositoriesForEMRRecords.GetPatientVisitsStatistics(emrModel);

            if (model.success == false)
            {
                model.appointments = "";
                model.billing = "";
                model.enc_notes = "";
                model.problem_list = "";
                model.documents = "";
                model.problem_list = "";
                model.prescriptions = "";
                model.reminders = "";
                model.labs = "";
                model.message = "There was problem to create report. Contact Administrator!";
            }
            //

            return View(model);
        }

        public ActionResult PatientVisitsStatisticsPrint(int doctorIdHid, string startDate, string endDate, string docName)
        {
            VistStatistics model = new VistStatistics();

            Tuple<bool, DateTime> endD = CheckTileFormat(endDate);
            Tuple<bool, DateTime> startD = CheckTileFormat(startDate);

            DateTime startT = new DateTime(startD.Item2.Year, startD.Item2.Month, startD.Item2.Day, 0, 0, 0);
            DateTime endT = new DateTime(endD.Item2.Year, endD.Item2.Month, endD.Item2.Day, 23, 59, 59);
            model.startDate = startT;
            model.endDate = endT;
            //model.startDate = startD.Item2;
            //model.endDate = endD.Item2;
            model.doctorIdHid = doctorIdHid;
            model.practiceId = CerebrumUser.PracticeId;

            #region garb
            //if (ret1.Item1 == false || ret2.Item1 == false)
            //{
            //    model.message = "Select date before search!";
            //    return View((object)model);
            //}

            //int docIdHiddInt = 0;
            //if (!int.TryParse(doctorIdHid, out docIdHiddInt))
            //{
            //    //model.message = "Select doctor before search!";
            //    //return View((object)model);
            //} 
            #endregion

            VistStatistics emrData = _repositoriesForEMRRecords.PullDataForPatientVisitsStatistics(model);
            emrData.startDate_s = startDate;
            emrData.endDate_s = endDate;
            emrData.docName_s = docName;

            if (emrData.success == false)
            {
                //Error message -----------------------------------------
                RepositoryForTemplates repErr = new RepositoryForTemplates();
                ClassForPdfError formModel = new ClassForPdfError();
                formModel.ServerMapPath = Path.Combine(_webHostEnvironment.WebRootPath, "PdfFiles");
                formModel.message = "There was problem to create Patient Visits Statistics Report. Contact to Administrator!";
                Tuple<ClassForPdfError, byte[]> resultErr = repErr.ErrorPdf(formModel);

                Response.Headers["Content-Disposition"] = "inline; filename=emr.pdf";
                return File(resultErr.Item2, "application/pdf");
            }



            string path = Path.Combine(_webHostEnvironment.WebRootPath, "PdfFiles");
            string printDirectory = DateTime.Now.Ticks.ToString() + "_patientVisitStastistics";
            string printDirectoryPath = Path.Combine(_webHostEnvironment.WebRootPath, "PdfFiles", printDirectory);
            Helper.CreateDirectory(printDirectoryPath);
            byte[] contents = _repositoriesForEMRRecords.FetchPdfBytesForPatientStatisticsPrint(emrData, printDirectoryPath);


            Helper.DeleteDirectory(printDirectoryPath);

            Response.Headers["Content-Disposition"] = "inline; filename=emr.pdf";
            return File(contents, "application/pdf");
        }

        public ActionResult AGPrint(int docIdHidd, string startDate, string endDate, string docName)
        {
            AgeAndGender model = new AgeAndGender();

            Tuple<bool, DateTime> ret2 = CheckTileFormat(endDate);
            Tuple<bool, DateTime> ret1 = CheckTileFormat(startDate);
            #region garb
            //if (ret1.Item1 == false || ret2.Item1 == false)
            //{
            //    model.message = "Select date before search!";
            //    return View((object)model);
            //}

            //int docIdHiddInt = 0;
            //if (!int.TryParse(docIdHidd, out docIdHiddInt))
            //{
            //    //model.message = "Select doctor before search!";
            //    //return View((object)model);
            //} 
            #endregion

            model.practiceId = CerebrumUser.PracticeId;
            AgeAndGender emrData = _repositoriesForEMRRecords.PullDataForAgeAndGender(docIdHidd, ret1.Item2, ret2.Item2, model);
            emrData.startDate = startDate;
            emrData.endDate = endDate;
            emrData.docName = docName;


            string path = Path.Combine(_webHostEnvironment.ContentRootPath, "PdfFiles");
            string printDirectory = DateTime.Now.Ticks.ToString() + "_AgeGender";
            string printDirectoryPath = Path.Combine(_webHostEnvironment.ContentRootPath, "PdfFiles", printDirectory);
            Helper.CreateDirectory(printDirectoryPath);
            byte[] contents = _repositoriesForEMRRecords.FetchPdfBytesForAGPrint(emrData, printDirectoryPath);


            Helper.DeleteDirectory(printDirectoryPath);

            Response.Headers["Content-Disposition"] = "inline; filename=emr.pdf";
            return File(contents, "application/pdf");
        }

        public ActionResult AgeGender()
        {
            AgeAndGender model = new Models.AgeAndGender();
            return View(model);
        }

        [HttpPost]
        public ActionResult AgeGender(AgeAndGender emrModel)
        {
            AgeAndGender model = new AgeAndGender();

            if (emrModel.docName == null || emrModel.docName == "")
            {
                emrModel.docIdHidd = 0;
            }

            Tuple<bool, DateTime> ret2 = null;
            if (emrModel.endDate == null || emrModel.endDate == "")
            {
                ret2 = new Tuple<bool, DateTime>(true, DateTime.MaxValue);
            }
            else
            {
                ret2 = CheckTileFormat(emrModel.endDate);
            }

            Tuple<bool, DateTime> ret1 = null;
            if (emrModel.startDate == null || emrModel.startDate == "")
            {
                ret1 = new Tuple<bool, DateTime>(true, DateTime.MinValue);
            }
            else
            {
                ret1 = CheckTileFormat(emrModel.startDate);
            }


            //Tuple<bool, DateTime> ret1 = CheckTileFormat(emrModel.startDate);
            if (ret1.Item1 == false || ret2.Item1 == false)
            {
                emrModel.message = "Select correct date before search!";
                return View(emrModel);
            }

            int ptnId = emrModel.docIdHidd;
            if (ptnId == 0)
            {
                //emrModel.message = "Select doctor before search!";
                //return View((object)emrModel);
            }


            if (Request.Form["Search"] == "Search")
            {
                emrModel.practiceId = CerebrumUser.PracticeId;
                AgeAndGender emrData = _repositoriesForEMRRecords.PullDataForAgeAndGender(emrModel.docIdHidd, ret1.Item2, ret2.Item2, emrModel);
                return View(emrData);
            }
            else if (Request.Form["Print"] == "Print")
            {
                //int f = 6;
            }
            return View(emrModel);
        }

        private Tuple<bool, DateTime> CheckTileFormat(string endDate)
        {
            Tuple<bool, DateTime> ret = null;
            try
            {
                DateTime dt = Convert.ToDateTime(endDate);
                ret = new Tuple<bool, DateTime>(true, dt);
            }
            catch (Exception)
            {
                ret = new Tuple<bool, DateTime>(false, DateTime.MaxValue);
            }

            return ret;
        }

        [ValidateHeaderAntiForgeryToken]
        [HttpPost]
        public JsonResult SearchPracticeDocs(string term_)  //
        {
            if (term_ != null && term_ != "")
            {
                if (term_.ToLower().Trim() == "all")
                {
                    List<labelValueData> list1 = new List<labelValueData>() { new labelValueData() { label = "All", value = "0" } };
                    return Json(list1);
                }

                int practiceId = CerebrumUser.PracticeId;

                // Direct database query using injected context instead of repository pattern
                List<labelValueData> list = (from ed in _context.ExternalDoctors
                                           join pd in _context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                                           where (ed.lastName.ToLower() + ", " + ed.firstName.ToLower()).StartsWith(term_.ToLower()) &&
                                           pd.PracticeId == practiceId
                                           select new labelValueData
                                           {
                                               label = ed.lastName.ToLower() + ", " + ed.firstName.ToLower(),
                                               value = pd.Id.ToString()
                                           }).ToList();

                return Json(list);
            }
            else
            { return null; }
        }
    }
}