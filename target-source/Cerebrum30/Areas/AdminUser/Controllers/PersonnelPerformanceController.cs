using Cerebrum.BLL.PersonnelPerformance;
using Cerebrum.BLL.Practice;
using Cerebrum.BLL.Schedule;
using Cerebrum.ViewModels.OfficeEmail;
using Cerebrum.ViewModels.PersonnelPerformance;
using Cerebrum.ViewModels.Schedule;
using Cerebrum.ViewModels.User;
using Cerebrum30.Controllers;
using Cerebrum30.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Cerebrum30.Areas.AdminUser.Controllers
{
    [Area("AdminUser")]
    public class PersonnelPerformanceController : BaseController
    {
        private IPracticeBLL _bll;
        private PersonnelPerformanceBLL _personnelPerformanceBLL;
        
        public PersonnelPerformanceController(IPracticeBLL bll, PersonnelPerformanceBLL personnelPerformanceBLL)
        {
            _bll = bll;
            _personnelPerformanceBLL = personnelPerformanceBLL;
        }

        // GET: AdminUser/PersonnelPerformance
        public ActionResult Index()
        {
            var offices = GetOffices();
            ViewBag.Offices = offices;
            ViewBag.OfficesCount = offices.Count;

            var tests = GetTests(CerebrumUser.PracticeId);
            ViewBag.Tests = tests;
            ViewBag.TestsCount = tests.Count;

            var techs = _bll.GetTecnhiciansByPracticeId(CerebrumUser.PracticeId);

            ViewBag.Techs = AddItemAllOfTechnicians(techs);

            return View();
        }

        [HttpPost]
        [ValidateApiHeaderAntiForgeryToken]
        public ActionResult GetTechniciansPerformance(List<int> tests, int officeId, int technicianId, DateTime dateStart, DateTime dateEnd, bool groupByOffice = false, bool groupByTest = false)
        {
            tests.Remove(0);
            PersonnelPerformanceRequest request = new PersonnelPerformanceRequest()
            {
                DateEnd = dateEnd,
                DateStart = dateStart,
                OfficeId = officeId,//CerebrumUser.OfficeId,
                PracticeId = CerebrumUser.PracticeId,
                TechnicianId = technicianId,
                TestIds = tests,
                GroupByOffice = groupByOffice,
                GroupByTest = groupByTest
            };

            List<VMReportTechnicianPerformance> model = _personnelPerformanceBLL.GetTechniciansPerformance(request);

            return PartialView("_techPerformance", model);
        }

        #region private

        private List<VMUserOffice> GetOffices()
        {
            List<VMUserOffice> offices = new List<VMUserOffice>();

            var officeAll = new VMUserOffice()
            {
                Id = 0,
                Name = "All of Offices"
            };
            offices.Add(officeAll);
            if (CerebrumUser.UserOffices != null)
            {
                offices.AddRange(CerebrumUser.UserOffices);
            }

            return offices;
        }
        private List<SelectListItem> AddItemAllOfTechnicians(List<SelectListItem> technicians)
        {
            SelectListItem techsAll = new SelectListItem()
            {
                Text = "All of Technicians",
                Value = "0"
            };

            technicians.Insert(0, techsAll);

            return technicians;
        }

        private List<SelectListItem> GetTests(int practiceId)
        {
            var tests = _bll.GetPracticeTests(practiceId);

            SelectListItem allItems = new SelectListItem()
            {
                Text = "All of Tests",
                Value = "0"
            };

            tests.Insert(0, allItems);

            return tests;
        }
        #endregion
    }
}