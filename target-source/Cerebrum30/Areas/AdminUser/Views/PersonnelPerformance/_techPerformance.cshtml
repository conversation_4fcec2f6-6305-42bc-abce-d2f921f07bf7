@model List<Cerebrum.ViewModels.PersonnelPerformance.VMReportTechnicianPerformance>
@inherits Cerebrum30.BaseViewPage<List<Cerebrum.ViewModels.PersonnelPerformance.VMReportTechnicianPerformance>>

@if (Model.Count > 0)
    {
        int itemNumber = 1;

    <table>
        <thead>
            <tr>
                <th>
                    #
                </th>
                <th>
                    Technician
                </th>
                <th>
                    Total Tests
                </th>
                <th class="text-center show-tests">
                    Test
                </th>
                <th class="text-center show-offices">
                    Office
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                var mame = item.LastName + ", " +  item.FirstName;
                var itemNumberText = itemNumber + ".";
            <tr>
                <td class="text-nowrap padd-3">
                    @itemNumberText
                </td>
                <td class="text-nowrap padd-3">
                    @mame
                </td>
                <td class="text-center padd-3">
                    @item.TotalTests
                </td>
                <td class="text-left show-tests">
                    @item.TestShortName
                </td>
                <td class="text-left show-offices">
                    @item.OfficeName
                </td>
            </tr>
                itemNumber++;
            }
        </tbody>
    </table>
    <br /><br />
}
else
{
    <h4>No data found</h4>
}

