﻿using Cerebrum.Data;
using System;
using System.Web;
namespace Cerebrum30.DAL.DataAccessGB
{
    public class UnitOfWorkEMRRecords : IUnitOfWorkEMRRecords
    {
        private readonly CerebrumContext _context;
        private readonly ServicesGenericRepositoryGB<Appointment> _AppointmentsRepro;
        private readonly ServicesGenericRepositoryGB<Demographic> _DemographicsReprosRepro;
        private readonly ServicesGenericRepositoryGB<ExternalDoctor> _ExternalDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<PracticeDoctor> _PracticeDoctorsReprosRepro;

        private readonly ServicesGenericRepositoryGB<ReportReceived> _ReportReceivedsRepro;
        private readonly ServicesGenericRepositoryGB<HL7Report> _HL7ReportsRepro;
        private readonly ServicesGenericRepositoryGB<HL7ReportDoctor> _HL7ReportDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<HL7ReportVersion> _HL7ReportVersionsRepro;
        private readonly ServicesGenericRepositoryGB<HL7MarkedSeen> _HL7MarkedSeensRepro;
        private readonly ServicesGenericRepositoryGB<CM_TaskMessageRecipient> _CM_TaskMessageRecipientsRepro;
        private readonly ServicesGenericRepositoryGB<BillDetail> _BillDetailsRepro;

        public UnitOfWorkEMRRecords(CerebrumContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _AppointmentsRepro = new ServicesGenericRepositoryGB<Appointment>(_context.Appointments);
            _DemographicsReprosRepro = new ServicesGenericRepositoryGB<Demographic>(_context.Demographics);
            _ExternalDoctorsRepro = new ServicesGenericRepositoryGB<ExternalDoctor>(_context.ExternalDoctors);
            _PracticeDoctorsReprosRepro = new ServicesGenericRepositoryGB<PracticeDoctor>(_context.PracticeDoctors);
            _ReportReceivedsRepro = new ServicesGenericRepositoryGB<ReportReceived>(_context.ReportsReceived);
            _HL7ReportsRepro = new ServicesGenericRepositoryGB<HL7Report>(_context.HL7Reports);
            _HL7ReportDoctorsRepro = new ServicesGenericRepositoryGB<HL7ReportDoctor>(_context.HL7ReportDoctors);
            _HL7ReportVersionsRepro = new ServicesGenericRepositoryGB<HL7ReportVersion>(_context.HL7ReportVersions);
            _HL7MarkedSeensRepro = new ServicesGenericRepositoryGB<HL7MarkedSeen>(_context.HL7MarkedSeens);
            _CM_TaskMessageRecipientsRepro = new ServicesGenericRepositoryGB<CM_TaskMessageRecipient>(_context.CM_TaskMessageRecipients);
            _BillDetailsRepro = new ServicesGenericRepositoryGB<BillDetail>(_context.BillDetails);
        }

        public UnitOfWorkEMRRecords()
        {
            throw new InvalidOperationException("Use UnitOfWorkEMRRecords(CerebrumContext context) constructor with dependency injection.");
        }

        public IGenericRepository<BillDetail> BillDetailsRepro
        {
            get
            {
                return _BillDetailsRepro;
            }
        }

        public IGenericRepository<ReportReceived> ReportReceivedsRepro
        {
            get
            {
                return _ReportReceivedsRepro;
            }
        }
        public IGenericRepository<HL7Report> HL7ReportsRepro
        {
            get
            {
                return _HL7ReportsRepro;
            }
        }
        public IGenericRepository<HL7ReportDoctor> HL7ReportDoctorsRepro
        {
            get
            {
                return _HL7ReportDoctorsRepro;
            }
        }
        public IGenericRepository<HL7ReportVersion> HL7ReportVersionsRepro
        {
            get
            {
                return _HL7ReportVersionsRepro;
            }
        }
        public IGenericRepository<HL7MarkedSeen> HL7MarkedSeensRepro
        {
            get
            {
                return _HL7MarkedSeensRepro;
            }
        }
        public IGenericRepository<CM_TaskMessageRecipient> CM_TaskMessageRecipientsRepro
        {
            get
            {
                return _CM_TaskMessageRecipientsRepro;
            }
        }


        public IGenericRepository<Appointment> AppointmentsRepro
        {
            get
            {
                return _AppointmentsRepro;
            }
        }

        public IGenericRepository<Demographic> DemographicsRepro
        {
            get
            {
                return _DemographicsReprosRepro;
            }
        }

        public IGenericRepository<ExternalDoctor> ExternalDoctorsRepro
        {
            get
            {
                return _ExternalDoctorsRepro;
            }
        }

        public IGenericRepository<PracticeDoctor> PracticeDoctorsRepro
        {
            get
            {
                return _PracticeDoctorsReprosRepro;
            }
        }

        public void Commit()
        {
            //SaveChanges((HttpContext.Current.Request.IsAuthenticated) ? HttpContext.Current.User.Identity.Name : "Anonymous");
            _context.SaveChanges();
        }

        // Properties to expose DbSets for direct access
        public Microsoft.EntityFrameworkCore.DbSet<ExternalDoctor> ExternalDoctors => _context.ExternalDoctors;
        public Microsoft.EntityFrameworkCore.DbSet<PracticeDoctor> PracticeDoctors => _context.PracticeDoctors;
        public Microsoft.EntityFrameworkCore.DbSet<Appointment> Appointments => _context.Appointments;
        public Microsoft.EntityFrameworkCore.DbSet<Demographic> Demographics => _context.Demographics;
        public Microsoft.EntityFrameworkCore.DbSet<HL7Report> HL7Reports => _context.HL7Reports;
        public Microsoft.EntityFrameworkCore.DbSet<HL7ReportDoctor> HL7ReportDoctors => _context.HL7ReportDoctors;
        public Microsoft.EntityFrameworkCore.DbSet<ReportReceived> ReportsReceived => _context.ReportsReceived;
        public Microsoft.EntityFrameworkCore.DbSet<BillDetail> BillDetails => _context.BillDetails;
        public Microsoft.EntityFrameworkCore.DbSet<PatientRecord> PatientRecords => _context.PatientRecords;
        public Microsoft.EntityFrameworkCore.DbSet<VP_CPP_Alert> VP_CPP_Alert => _context.VP_CPP_Alert;
        public Microsoft.EntityFrameworkCore.DbSet<DemographicsMainResponsiblePhysician> DemographicsMainResponsiblePhysicians => _context.DemographicsMainResponsiblePhysicians;
        public Microsoft.EntityFrameworkCore.DbSet<DoctorsReportReviewed> DoctorsReportsReviewed => _context.DoctorsReportsReviewed;

        public void Dispose()
        {
            // Note: We don't dispose the context since it's managed by DI container
            // The context will be disposed by the DI container at the end of the request
        }
    }
}