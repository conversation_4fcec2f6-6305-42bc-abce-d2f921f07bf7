﻿using Cerebrum30.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.IO;
using Cerebrum30.Areas.AdminUser.Models;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum30.Areas.Medications.DataAccess;
using Cerebrum.BLL.Schedule;
using Cerebrum30.Areas.VP.DataAccess;
using Cerebrum30.Areas.PdfConversions.DataAccess;
using Cerebrum.Data;

namespace Cerebrum30.DAL.DataAccessGB
{
    public class RepositoriesForEMRRecords
    {
        private readonly CerebrumContext _context;
        private readonly AppointmentsBLL _appointmentsBLL;
        private readonly Cerebrum30.Areas.VP.DataAccess.VPRepository _vpRepository;
        private readonly Cerebrum30.Areas.Medications.DataAccess.PatientMedicationRepository _patientMedicationRepository;
        UnitOfWorkEMRRecords uofw;

        public RepositoriesForEMRRecords(CerebrumContext context, AppointmentsBLL appointmentsBLL, Cerebrum30.Areas.VP.DataAccess.VPRepository vpRepository, Cerebrum30.Areas.Medications.DataAccess.PatientMedicationRepository patientMedicationRepository)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _appointmentsBLL = appointmentsBLL ?? throw new ArgumentNullException(nameof(appointmentsBLL));
            _vpRepository = vpRepository ?? throw new ArgumentNullException(nameof(vpRepository));
            _patientMedicationRepository = patientMedicationRepository ?? throw new ArgumentNullException(nameof(patientMedicationRepository));
            uofw = new UnitOfWorkEMRRecords(_context);
        }

        // Legacy constructor - deprecated
        public RepositoriesForEMRRecords()
        {
            throw new InvalidOperationException("Use RepositoriesForEMRRecords(CerebrumContext context) constructor with dependency injection.");
        }

        public List<labelValueData> SearchPracticeDocs(string term_, int practiceId)
        {
            List<labelValueData> listItems = null;
            try
            {
                listItems = (from ed in uofw.ExternalDoctors
                             join pd in uofw.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                             where (ed.lastName.ToLower() + ", " + ed.firstName.ToLower()).StartsWith(term_.ToLower()) &&
                             pd.PracticeId == practiceId
                             select new labelValueData
                             {
                                 label = ed.lastName.ToLower() + ", " + ed.firstName.ToLower(),
                                 value = pd.Id.ToString()
                             }).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message + " ### RepositoriesForEMRRecords.SearchPracticeDocs";

                Helper.WriteToLog(Msg);
            }

            return listItems;
        }

        public AgeAndGender PullDataForAgeAndGender(int physician, DateTime startT, DateTime endT, AgeAndGender model)
        {
            List<Age_Gender> list = GetAgeGenderResultPerDoctor(physician, startT, endT, model.practiceId);

            int numberOfAllPatients = 0;
            int patients_0_19_f = 0;
            int patients_0_19_m = 0;
            int patients_20_44_f = 0;
            int patients_20_44_m = 0;
            int patients_45_64_f = 0;
            int patients_45_64_m = 0;
            int patients_65_84_f = 0;
            int patients_65_84_m = 0;
            int patients_85_f = 0;
            int patients_85_m = 0;


            for (int i = 0; i < list.Count; i++)
            {
                numberOfAllPatients += list[i].Number;
                //numberOfAllPatients += 1;
                if (list[i].Sex == Gender.M)
                //if (list[i].Sex == "M")
                {
                    if (list[i].Age <= 19)
                    {
                        patients_0_19_m += list[i].Number;
                        //patients_0_19_m += 1;
                    }
                    else if (list[i].Age >= 20 && list[i].Age <= 44)
                    {
                        patients_20_44_m += list[i].Number;
                        //patients_20_44_m += 1;
                    }
                    else if (list[i].Age >= 45 && list[i].Age <= 64)
                    {
                        patients_45_64_m += list[i].Number;
                        //patients_45_64_m += 1;
                    }
                    else if (list[i].Age >= 65 && list[i].Age <= 84)
                    {
                        patients_65_84_m += list[i].Number;
                        //patients_65_84_m += 1;
                    }
                    else
                    {
                        patients_85_m += list[i].Number;
                        //patients_85_m += 1;
                    }
                }
                else
                {
                    if (list[i].Age <= 19)
                    {
                        patients_0_19_f += list[i].Number;
                        //patients_0_19_f += 1;
                    }
                    else if (list[i].Age >= 20 && list[i].Age <= 44)
                    {
                        patients_20_44_f += list[i].Number;
                        //patients_20_44_f += 1;
                    }
                    else if (list[i].Age >= 45 && list[i].Age <= 64)
                    {
                        patients_45_64_f += list[i].Number;
                        //patients_45_64_f += 1;
                    }
                    else if (list[i].Age >= 65 && list[i].Age <= 84)
                    {
                        patients_65_84_f += list[i].Number;
                        //patients_65_84_f += 1;
                    }
                    else
                    {
                        patients_85_f += list[i].Number;
                        //patients_85_f += 1;
                    }
                }
            }

            int[] percentCheck = new int[] { 0, 0, 0, 0, 0 };
            int num = 0;
            int numCheck = 0;

            //DataRow newItem = table.NewRow();
            //newItem["AgeGenderId"] = 0;
            //newItem["AgeGroup"] = "0 - 19";
            num = numberOfAllPatients == 0 ? 0 : ((patients_0_19_m + patients_0_19_f) * 100) / numberOfAllPatients;
            percentCheck[0] = num;
            //newItem["Percentage"] = (num).ToString() + " %";
            model.ag_19 = (num).ToString() + " %";
            numCheck = patients_0_19_m + patients_0_19_f == 0 ? 0 : (patients_0_19_m * 100) / (patients_0_19_m + patients_0_19_f);
            //newItem["Male"] = (numCheck).ToString() + " %";
            model.ag_19_M = (numCheck).ToString() + " %";
            //newItem["Female"] = ((patients_0_19_m + patients_0_19_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            model.ag_19_F = ((patients_0_19_m + patients_0_19_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            //table.Rows.Add(newItem);

            //newItem = table.NewRow();
            //newItem["AgeGenderId"] = 1;
            //newItem["AgeGroup"] = "20 - 44";
            num = numberOfAllPatients == 0 ? 0 : ((patients_20_44_m + patients_20_44_f) * 100) / numberOfAllPatients;
            percentCheck[1] = num;
            //newItem["Percentage"] = (num).ToString() + " %";
            model.ag_20_44 = (num).ToString() + " %";
            numCheck = patients_20_44_m + patients_20_44_f == 0 ? 0 : (patients_20_44_m * 100) / (patients_20_44_m + patients_20_44_f);
            //newItem["Male"] = (numCheck).ToString() + " %";
            model.ag_20_44_M = (numCheck).ToString() + " %";
            //newItem["Female"] = ((patients_20_44_m + patients_20_44_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            model.ag_20_44_F = ((patients_20_44_m + patients_20_44_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            //table.Rows.Add(newItem);

            //newItem = table.NewRow();
            //newItem["AgeGenderId"] = 2;
            //newItem["AgeGroup"] = "45 - 64";
            num = numberOfAllPatients == 0 ? 0 : ((patients_45_64_m + patients_45_64_f) * 100) / numberOfAllPatients;
            percentCheck[2] = num;
            //newItem["Percentage"] = (num).ToString() + " %";
            model.ag_45_64 = (num).ToString() + " %";
            numCheck = patients_45_64_m + patients_45_64_f == 0 ? 0 : (patients_45_64_m * 100) / (patients_45_64_m + patients_45_64_f);
            //newItem["Male"] = (numCheck).ToString() + " %";
            model.ag_45_64_M = (numCheck).ToString() + " %";
            //newItem["Female"] = ((patients_45_64_m + patients_45_64_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            model.ag_45_64_F = ((patients_45_64_m + patients_45_64_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            //table.Rows.Add(newItem);

            //newItem = table.NewRow();
            //newItem["AgeGenderId"] = 3;
            //newItem["AgeGroup"] = "65 - 84";
            num = numberOfAllPatients == 0 ? 0 : ((patients_65_84_m + patients_65_84_f) * 100) / numberOfAllPatients;
            percentCheck[3] = num;
            //newItem["Percentage"] = (num).ToString() + " %";
            model.ag_65_84 = (num).ToString() + " %";
            numCheck = patients_65_84_m + patients_65_84_f == 0 ? 0 : (patients_65_84_m * 100) / (patients_65_84_m + patients_65_84_f);
            //newItem["Male"] = (numCheck).ToString() + " %";
            model.ag_65_84_M = (numCheck).ToString() + " %";
            //newItem["Female"] = ((patients_65_84_m + patients_65_84_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            model.ag_65_84_F = ((patients_65_84_m + patients_65_84_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            //table.Rows.Add(newItem);

            //newItem = table.NewRow();
            //newItem["AgeGenderId"] = 4;
            //newItem["AgeGroup"] = "85+";
            num = numberOfAllPatients == 0 ? 0 : ((patients_85_m + patients_85_f) * 100) / numberOfAllPatients;
            percentCheck[4] = num;
            int num_ = percentCheck[0] + percentCheck[1] + percentCheck[2] + percentCheck[3] + percentCheck[4];
            if (num_ != 0 || num_ != 100)
            {
                if (percentCheck[0] != 0)
                {
                    percentCheck[0] += (100 - num_);
                    model.ag_19 = percentCheck[0].ToString() + " %";
                }
                else if (percentCheck[1] != 0)
                {
                    percentCheck[1] += (100 - num_);
                    model.ag_20_44 = percentCheck[1].ToString() + " %";
                }
                else if (percentCheck[2] != 0)
                {
                    percentCheck[2] += (100 - num_);
                    model.ag_45_64 = percentCheck[2].ToString() + " %";
                }
                else if (percentCheck[3] != 0)
                {
                    percentCheck[3] += (100 - num_);
                    model.ag_65_84 = percentCheck[3].ToString() + " %";
                }
                else if (percentCheck[4] != 0)
                {
                    percentCheck[4] += (100 - num_);
                    model.ag_85 = percentCheck[4].ToString() + " %";
                }

            }
            //newItem["Percentage"] = (num).ToString() + " %";
            model.ag_85 = (num).ToString() + " %";
            numCheck = patients_85_m + patients_85_f == 0 ? 0 : (patients_85_m * 100) / (patients_85_m + patients_85_f);
            //newItem["Male"] = (numCheck).ToString() + " %";
            model.ag_85_M = (numCheck).ToString() + " %";
            //newItem["Female"] = ((patients_85_m + patients_85_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            model.ag_85_F = ((patients_85_m + patients_85_f) == 0 ? 0 : 100 - numCheck).ToString() + " %";
            return model;
        }

        public List<Age_Gender> GetAgeGenderResultPerDoctor(int physician, DateTime startT, DateTime endT, int practiceId)
        {
            startT = new DateTime(startT.Year, startT.Month, startT.Day, 0, 0, 0);
            endT = new DateTime(endT.Year, endT.Month, endT.Day, 23, 59, 59);
            List<Age_Gender> ageGenderList = new List<Age_Gender>();
            //////////////////////////
            try
            {
                List<AG_Group> patients_ = null;

                if (physician == 0)
                {
                    patients_ = (from a in uofw.Appointments
                                 join pr in uofw.PatientRecords on a.PatientRecordId equals pr.Id
                                 where (
                                 //a.PracticeDoctorId == physician &&
                                 (a.appointmentTime >= startT && a.appointmentTime <= endT) &&
                                 pr.PracticeId == practiceId)
                                 group a by a.PatientRecordId into g
                                 select new AG_Group
                                 {
                                     agId = g.Key,
                                     agNumber = g.Count(),
                                     agAgeSex = (from ag in uofw.Demographics
                                                 where g.Key == ag.PatientRecordId
                                                 select new BirthdayAndGender { Birthday = ag.dateOfBirth, Sex = ag.gender }).FirstOrDefault()
                                 }).ToList();
                }
                else
                {
                    patients_ = (from a in uofw.Appointments
                                 join pr in uofw.PatientRecords on a.PatientRecordId equals pr.Id
                                 where (a.PracticeDoctorId == physician &&
                                 (a.appointmentTime >= startT && a.appointmentTime <= endT) &&
                                 pr.PracticeId == practiceId)
                                 group a by a.PatientRecordId into g
                                 select new AG_Group
                                 {
                                     agId = g.Key,
                                     agNumber = g.Count(),
                                     agAgeSex = (from ag in uofw.Demographics
                                                 where g.Key == ag.PatientRecordId
                                                 select new BirthdayAndGender { Birthday = ag.dateOfBirth, Sex = ag.gender }).FirstOrDefault()
                                 }).ToList();
                }

                if (patients_ != null && patients_.Count > 0)
                {
                    BirthdayAndGender ageSex = null;
                    int id_ = 0;
                    foreach (var patient_ in patients_)
                    {
                        ageSex = patient_.agAgeSex;
                        if (patient_.agAgeSex == null)
                            continue;

                        int age = 0;
                        Gender sex = Gender.F;
                        if (ageSex.Birthday == null)
                        {
                            continue;
                        }
                        else
                        {
                            try
                            {
                                int age_ = Convert.ToDateTime(ageSex.Birthday).Year;
                                age = DateTime.Now.Year - age_;
                                if (age < 0)
                                    continue;
                            }
                            catch (FormatException)
                            {
                                continue;
                            }
                        }
                        //if (ageSex.Sex != "F" && ageSex.Sex != "M")
                        if (ageSex.Sex != Gender.F && ageSex.Sex != Gender.M)
                        {
                            continue;
                        }
                        else
                        {
                            sex = ageSex.Sex;
                        }

                        Age_Gender ag = new Age_Gender();
                        ag.Id = id_;
                        ag.Number = patient_.agNumber;
                        ag.Sex = sex;
                        ag.Age = age;

                        id_ += 1;
                        ageGenderList.Add(ag);
                    }
                }
                //}
            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message + " ### RepositoriesForEMRRecords.GetAgeGenderResultPerDoctor";

                Helper.WriteToLog(Msg);
            }
            ///////////////////
            return ageGenderList;


            //return null;
        }

        public byte[] FetchPdfBytesForAGPrint(AgeAndGender emrData, string printDirectoryPath)
        {

            var pageSize = new Rectangle(595f, 842f);
            Document doc = new Document(pageSize, 30f, 30f, 40f, 30f);

            MemoryStream ms;
            using (ms = new MemoryStream())
            {
                PdfWriter writer = PdfWriter.GetInstance(doc, ms);
                PdfWriter.GetInstance(doc, new FileStream(printDirectoryPath + "/AgeAndGenderDistribution.pdf", FileMode.Create));

                doc.Open();

                PdfPTable table = new PdfPTable(4);
                table.TotalWidth = 523f;
                table.LockedWidth = true;
                float[] widths = new float[] { 3f, 1f, 1f, 1f };
                table.SetWidths(widths);
                table.HorizontalAlignment = 0;

                PdfPCell caption = new PdfPCell(new Phrase("AGE AND GENDER DISTRIBURION", new Font(Font.FontFamily.HELVETICA, 9f, Font.BOLD, BaseColor.BLACK)));
                caption.BackgroundColor = new BaseColor(188, 188, 188);
                caption.Colspan = 4;
                caption.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right
                table.AddCell(caption);

                //First_ row b
                PdfPTable nested_1 = new PdfPTable(6);
                nested_1.TotalWidth = 523f;
                float[] nested_widths = new float[] { 2f, 3f, 1f, 2f, 1f, 5f };
                nested_1.SetWidths(nested_widths);
                nested_1.HorizontalAlignment = 0;
                PdfPCell cell_1 = new PdfPCell(new Phrase("Doctor:", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell_1.BorderWidthTop = 0f;
                cell_1.BorderWidthBottom = 0f;
                cell_1.BorderWidthLeft = 0f;
                cell_1.BorderWidthRight = 0f;
                nested_1.AddCell(cell_1);
                string provider = emrData.docName;
                PdfPCell cell_2 = new PdfPCell(new Phrase(provider, new Font(Font.FontFamily.HELVETICA, 8f, Font.BOLD, BaseColor.BLACK)));
                cell_2.BorderWidthTop = 0f;
                cell_2.BorderWidthBottom = 0f;
                cell_2.BorderWidthLeft = 0f;
                cell_2.BorderWidthRight = 0f;
                nested_1.AddCell(cell_2);
                PdfPCell cell_3 = new PdfPCell(new Phrase("Start:", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell_3.BorderWidthTop = 0f;
                cell_3.BorderWidthBottom = 0f;
                cell_3.BorderWidthLeft = 0f;
                cell_3.BorderWidthRight = 0f;
                nested_1.AddCell(cell_3);
                string start = emrData.startDate;
                PdfPCell cell_4 = new PdfPCell(new Phrase(start, new Font(Font.FontFamily.HELVETICA, 8f, Font.BOLD, BaseColor.BLACK)));
                cell_4.BorderWidthTop = 0f;
                cell_4.BorderWidthBottom = 0f;
                cell_4.BorderWidthLeft = 0f;
                cell_4.BorderWidthRight = 0f;
                nested_1.AddCell(cell_4);
                PdfPCell cell_5 = new PdfPCell(new Phrase("End:", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell_5.BorderWidthTop = 0f;
                cell_5.BorderWidthBottom = 0f;
                cell_5.BorderWidthLeft = 0f;
                cell_5.BorderWidthRight = 0f;
                nested_1.AddCell(cell_5);
                string end = emrData.endDate;
                PdfPCell cell_6 = new PdfPCell(new Phrase(end, new Font(Font.FontFamily.HELVETICA, 8f, Font.BOLD, BaseColor.BLACK)));
                cell_6.BorderWidthTop = 0f;
                cell_6.BorderWidthBottom = 0f;
                cell_6.BorderWidthLeft = 0f;
                cell_6.BorderWidthRight = 0f;
                nested_1.AddCell(cell_6);
                PdfPCell nesthousing_1 = new PdfPCell(nested_1);
                nesthousing_1.BackgroundColor = new BaseColor(240, 240, 240);
                nesthousing_1.Colspan = 4;
                table.AddCell(nesthousing_1);
                //First_ row e

                //Caption b
                PdfPCell cell1_1 = new PdfPCell(new Phrase("Age Group - Years", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_1.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_1.HorizontalAlignment = 1;
                table.AddCell(cell1_1);
                PdfPCell cell1_2 = new PdfPCell(new Phrase("Percentage", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_2.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_2.HorizontalAlignment = 1;
                table.AddCell(cell1_2);
                PdfPCell cell1_3 = new PdfPCell(new Phrase("Male", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_3.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_3.HorizontalAlignment = 1;
                table.AddCell(cell1_3);
                PdfPCell cell1_4 = new PdfPCell(new Phrase("Female", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_4.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_4.HorizontalAlignment = 1;
                table.AddCell(cell1_4);



                PdfPCell cell2_1 = new PdfPCell(new Phrase("0  -  19", new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                cell2_1.BackgroundColor = new BaseColor(240, 240, 240);
                table.AddCell(cell2_1);
                PdfPCell cell2_2 = new PdfPCell(new Phrase(emrData.ag_19, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_2);
                PdfPCell cell2_3 = new PdfPCell(new Phrase(emrData.ag_19_M, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_3);
                PdfPCell cell2_4 = new PdfPCell(new Phrase(emrData.ag_19_F, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_4);

                PdfPCell cell2_1_2 = new PdfPCell(new Phrase("20  -  44", new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                cell2_1_2.BackgroundColor = new BaseColor(240, 240, 240);
                table.AddCell(cell2_1_2);
                PdfPCell cell2_2_2 = new PdfPCell(new Phrase(emrData.ag_20_44, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_2_2);
                PdfPCell cell2_3_2 = new PdfPCell(new Phrase(emrData.ag_20_44_M, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_3_2);
                PdfPCell cell2_4_2 = new PdfPCell(new Phrase(emrData.ag_20_44_F, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_4_2);

                PdfPCell cell2_1_3 = new PdfPCell(new Phrase("45  -  64", new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                cell2_1_3.BackgroundColor = new BaseColor(240, 240, 240);
                table.AddCell(cell2_1_3);
                PdfPCell cell2_2_3 = new PdfPCell(new Phrase(emrData.ag_45_64, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_2_3);
                PdfPCell cell2_3_3 = new PdfPCell(new Phrase(emrData.ag_45_64_M, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_3_3);
                PdfPCell cell2_4_3 = new PdfPCell(new Phrase(emrData.ag_45_64_F, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_4_3);

                PdfPCell cell2_1_4 = new PdfPCell(new Phrase("65  -  84", new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                cell2_1_4.BackgroundColor = new BaseColor(240, 240, 240);
                table.AddCell(cell2_1_4);
                PdfPCell cell2_2_4 = new PdfPCell(new Phrase(emrData.ag_65_84, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_2_4);
                PdfPCell cell2_3_4 = new PdfPCell(new Phrase(emrData.ag_65_84_M, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_3_4);
                PdfPCell cell2_4_4 = new PdfPCell(new Phrase(emrData.ag_65_84_F, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_4_4);

                PdfPCell cell2_1_5 = new PdfPCell(new Phrase("85  -", new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                cell2_1_5.BackgroundColor = new BaseColor(240, 240, 240);
                table.AddCell(cell2_1_5);
                PdfPCell cell2_2_5 = new PdfPCell(new Phrase(emrData.ag_85, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_2_5);
                PdfPCell cell2_3_5 = new PdfPCell(new Phrase(emrData.ag_85_M, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_3_5);
                PdfPCell cell2_4_5 = new PdfPCell(new Phrase(emrData.ag_85_F, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                table.AddCell(cell2_4_5);



                //string ageGroupLit_ = ((Literal)item.FindControl("ageGroupLit")).Text;
                //PdfPCell cell2_1 = new PdfPCell(new Phrase(ageGroupLit_, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                //cell2_1.BackgroundColor = new BaseColor(240, 240, 240);
                //table.AddCell(cell2_1);
                //string percentageLit_ = ((Literal)item.FindControl("percentageLit")).Text;
                //PdfPCell cell2_2 = new PdfPCell(new Phrase(percentageLit_, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                //table.AddCell(cell2_2);
                //string maleLit_ = ((Literal)item.FindControl("maleLit")).Text;
                //PdfPCell cell2_3 = new PdfPCell(new Phrase(maleLit_, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                //table.AddCell(cell2_3);
                //string femaleLit_ = ((Literal)item.FindControl("femaleLit")).Text;
                //PdfPCell cell2_4 = new PdfPCell(new Phrase(femaleLit_, new Font(Font.FontFamily.HELVETICA, 7f, Font.NORMAL, BaseColor.BLACK)));
                //table.AddCell(cell2_4);


                doc.Add(table);
                doc.Close();

                //Response.ContentType = "application/pdf";
                //Response.OutputStream.Write(ms.GetBuffer(), 0, ms.GetBuffer().Length);
                //DataAccess.FileConversions.DeleteDirectorie(printDirectoryPath);
                //Response.OutputStream.Flush();
                //Response.OutputStream.Close();
                //Response.End();


                return ms.GetBuffer();
            }
        }



        public VistStatistics GetPatientVisitsStatistics(VistStatistics model)
        {
            string msg = "";
            model.success = true;
            Tuple<bool, string> retApp = GetAppointmentsPVS(model.doctorIdHid, model.startDate, model.endDate, model.practiceId);
            if (retApp.Item1 == true)
            {
                model.appointments = retApp.Item2;
            }
            else
            {
                msg += "Appointments # ";
            }

            Tuple<bool, string> retBil = GetBillingsPVS(model.doctorIdHid, model.startDate, model.endDate, model.practiceId);
            if (retBil.Item1 == true)
            {
                model.billing = retBil.Item2;
            }
            else
            {
                msg += "Billing # ";
            }

            Tuple<bool, string> retEncNotes = GetEncounterNotesPVS(model.doctorIdHid, model.startDate, model.endDate, model.practiceId);
            if (retEncNotes.Item1 == true)
            {
                model.enc_notes = retEncNotes.Item2;
            }
            else
            {
                msg += "Encounter Notes # ";
            }

            Tuple<bool, string> retPL = GetProblemlistPVS(model.doctorIdHid, model.startDate, model.endDate, model.practiceId);
            if (retPL.Item1 == true)
            {
                model.problem_list = retPL.Item2;
            }
            else
            {
                msg += "Problem List # ";
            }

            Tuple<bool, string> retDoc = GetDocumentsPVS(model.doctorIdHid, model.startDate, model.endDate, model.practiceId);
            if (retDoc.Item1 == true)
            {
                model.documents = retDoc.Item2;
            }
            else
            {
                msg += "Documents # ";
            }

            Tuple<bool, string> retPrecp = GetPrescriptionsPVS(model.doctorIdHid, model.startDate, model.endDate, model.practiceId);
            if (retPrecp.Item1 == true)
            {
                model.prescriptions = retPrecp.Item2;
            }
            else
            {
                msg += "Prescriptions # ";
            }

            Tuple<bool, string> retRem = GetRemindersPVS(model.doctorIdHid, model.startDate, model.endDate, model.practiceId);
            if (retRem.Item1 == true)
            {
                model.reminders = retRem.Item2;
            }
            else
            {
                msg += "Reminders # ";
            }

            Tuple<bool, string> retLab = GetLabsPVS(model.doctorIdHid, model.startDate, model.endDate, model.practiceId);
            if (retLab.Item1 == true)
            {
                model.labs = retLab.Item2;
            }
            else
            {
                msg += "Labs # ";
            }

            if (msg != "")
            {
                model.success = false;
            }

            return model;
        }

        private Tuple<bool, string> GetLabsPVS(int doctorIdHid, DateTime startDate, DateTime endDate, int practiceId)//done
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, 0, 0, 0);
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day, 23, 59, 59);

            var hl7Count = (from r in uofw.HL7Reports
                            join rd in uofw.HL7ReportDoctors on r.Id equals rd.HL7ReportId
                            join pd in uofw.PracticeDoctors on rd.practiceDoctorId equals pd.Id
                            //let mm = (from rv in uofw.HL7ReportVersions.Where(t => t.HL7ReportId == r.Id)
                            //          let ms = uofw.HL7MarkedSeens.Where(m => m.HL7ReportId == rv.HL7ReportId && m.HL7ReportVersionId == rv.Id
                            // && m.practiceDoctorId == pd.Id).FirstOrDefault()
                            //          select new { seenAt = (ms == null || ms.seenAt == null ? null : (DateTime?)ms.seenAt),
                            //              seen = (ms == null || ms.seenAt == null ? 0 : 1) }).OrderBy(t => t.seen).FirstOrDefault()
                            where rd.practiceDoctorId == doctorIdHid && pd.PracticeId == practiceId && r.collectionDateTime != null &&
                                    startDate <= r.collectionDateTime && r.collectionDateTime <= endDate
                            select r).Count();





            bool boolRet = true;
            string strRet = hl7Count.ToString();

            return new Tuple<bool, string>(boolRet, strRet);
        }

        private Tuple<bool, string> GetRemindersPVS(int doctorIdHid, DateTime startDate, DateTime endDate, int practiceId)//done
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, 0, 0, 0);
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day, 23, 59, 59);

            //int count = (from cmt in uofw.CM_TaskMessageRecipients
            //             join u in uofw.PracticeDoctors on cmt.userid equals u.ApplicationUserId
            //             where u.Id == doctorIdHid && u.PracticeId == practiceId && cmt.seenAt != null && //cmt.seenAt.HasValue &&
            //             startDate <= cmt.seenAt && cmt.seenAt <= endDate
            //             select cmt.id).Count();

            // Simplified query to avoid EF Core translation issues
            var alertIds = (from a in uofw.VP_CPP_Alert
                           join b in uofw.Demographics on a.PatientRecordId equals b.PatientRecordId
                           join c in uofw.DemographicsMainResponsiblePhysicians on b.Id equals c.DemographicId
                           where c.PracticeDoctorId == doctorIdHid && a.AddDate > startDate && a.AddDate < endDate && !a.Deleted
                           group a by a.ParentId into g
                           select g.Key).Distinct();
                           
            var count = alertIds.Count();


            bool boolRet = true;
            string strRet = count.ToString();

            return new Tuple<bool, string>(boolRet, strRet);
        }

        private Tuple<bool, string> GetPrescriptionsPVS(int doctorIdHid, DateTime startDate, DateTime endDate, int practiceId)//done
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, 0, 0, 0);
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day, 23, 59, 59);

            int count = _patientMedicationRepository.GetPrescriptionCount(doctorIdHid, startDate, endDate);
            bool boolRet = true;
            string strRet = count.ToString();

            return new Tuple<bool, string>(boolRet, strRet);
        }

        private Tuple<bool, string> GetDocumentsPVS(int doctorIdHid, DateTime startDate, DateTime endDate, int practiceId)//done
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, 0, 0, 0);
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day, 23, 59, 59);

            var faxCount = (from r in uofw.ReportsReceived
                            join dr in uofw.DoctorsReportsReviewed on r.Id equals dr.ReportReceivedId
                            join pd in uofw.PracticeDoctors on dr.practiceDoctorId equals pd.Id
                            where dr.practiceDoctorId == doctorIdHid //&& dr.dateTimeReportReviewed == null 
                            && pd.PracticeId == practiceId && r.receivedDateTime != null &&
                            startDate <= r.receivedDateTime && r.receivedDateTime <= endDate
                            select r).Count();




            bool boolRet = true;
            string strRet = faxCount.ToString();

            return new Tuple<bool, string>(boolRet, strRet);
        }

        private Tuple<bool, string> GetProblemlistPVS(int doctorIdHid, DateTime startDate, DateTime endDate, int practiceId)//done
        {
            int count = _vpRepository.PatientCPPFilledCount(doctorIdHid, startDate, endDate);

            bool boolRet = true;
            string strRet = count.ToString();

            return new Tuple<bool, string>(boolRet, strRet);
        }

        private Tuple<bool, string> GetEncounterNotesPVS(int doctorIdHid, DateTime startDate, DateTime endDate, int practiceId)//done
        {
            //VPRepository rep = new VPRepository();
            //int count = rep.Patient_VP_Submitted_Count(doctorIdHid, startDate, endDate);

            int count = _appointmentsBLL.PracticeDoctorVPCount(practiceId, doctorIdHid, startDate, endDate);

            bool boolRet = true;
            string strRet = count.ToString();

            return new Tuple<bool, string>(boolRet, strRet);
        }

        private Tuple<bool, string> GetBillingsPVS(int doctorIdHid, DateTime startDate, DateTime endDate, int practiceId)//done
        {
            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, 0, 0, 0);
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day, 23, 59, 59);

            int billCount = (from bil in uofw.BillDetails
                             join pd in uofw.PracticeDoctors on bil.practiceDoctorId equals pd.Id
                             where pd.PracticeId == practiceId && bil.practiceDoctorId == doctorIdHid &&
                             bil.billStatusId != null && bil.billStatusId != 0 &&
                             startDate <= bil.date && bil.date <= endDate
                             select bil.id).Count();

            bool boolRet = true;
            string strRet = billCount.ToString();

            return new Tuple<bool, string>(boolRet, strRet);
        }

        private Tuple<bool, string> GetAppointmentsPVS(int doctorIdHid, DateTime startDate, DateTime endDate, int practiceId)//done
        {
            //int count = _appointmentsBLL.GetPracticeDoctorAppointmentCount(doctorIdHid, startDate, endDate);
            int count = _appointmentsBLL.PracticeDoctorActiveAppointmentCount(practiceId, doctorIdHid, startDate, endDate);

            bool boolRet = true;
            string strRet = count.ToString();

            return new Tuple<bool, string>(boolRet, strRet);
        }

        public VistStatistics PullDataForPatientVisitsStatistics(VistStatistics model)
        {
            return GetPatientVisitsStatistics(model);
        }

        public byte[] FetchPdfBytesForPatientStatisticsPrint(VistStatistics emrData, string printDirectoryPath)
        {

            var pageSize = new Rectangle(595f, 842f);
            Document doc = new Document(pageSize, 30f, 30f, 40f, 30f);

            MemoryStream ms;
            using (ms = new MemoryStream())
            {
                PdfWriter writer = PdfWriter.GetInstance(doc, ms);
                PdfWriter.GetInstance(doc, new FileStream(printDirectoryPath + "/PatientVisitsStatistics.pdf", FileMode.Create));

                doc.Open();

                PdfPTable table = new PdfPTable(6);
                table.TotalWidth = 523f;
                table.LockedWidth = true;
                float[] widths = new float[] { 2f, 1f, 2f, 2f, 2f, 6f };
                table.SetWidths(widths);
                table.HorizontalAlignment = 0;

                PdfPCell caption = new PdfPCell(new Phrase("NUMBR OF UNIQUE PATIENT VISITS", new Font(Font.FontFamily.HELVETICA, 9f, Font.BOLD, BaseColor.BLACK)));
                caption.BackgroundColor = new BaseColor(188, 188, 188);
                caption.Colspan = 8;
                caption.PaddingBottom = 10f;
                caption.PaddingTop = 10f;
                caption.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right
                table.AddCell(caption);

                //First_ row b
                PdfPTable nested_1 = new PdfPTable(8);
                float[] nested_widths = new float[] { 2f, 4f, 2f, 4f, 1f, 2f, 1f, 2f };
                nested_1.SetWidths(nested_widths);
                nested_1.HorizontalAlignment = 0;
                PdfPCell cell_1 = new PdfPCell(new Phrase("Doctor:", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell_1.BorderWidthTop = 0f;
                cell_1.BorderWidthBottom = 0f;
                cell_1.BorderWidthLeft = 0f;
                cell_1.BorderWidthRight = 0f;
                nested_1.AddCell(cell_1);
                //string provider = advanceSearchForVisits.Value;
                PdfPCell cell_2 = new PdfPCell(new Phrase(emrData.docName_s ?? "", new Font(Font.FontFamily.HELVETICA, 8f, Font.BOLD, BaseColor.BLACK)));
                cell_2.BorderWidthTop = 0f;
                cell_2.BorderWidthBottom = 0f;
                cell_2.BorderWidthLeft = 0f;
                cell_2.BorderWidthRight = 0f;
                cell_2.PaddingBottom = 5f;
                nested_1.AddCell(cell_2);
                PdfPCell cell_3 = new PdfPCell(new Phrase(""));
                cell_3.BorderWidthTop = 0f;
                cell_3.BorderWidthBottom = 0f;
                cell_3.BorderWidthLeft = 0f;
                cell_3.BorderWidthRight = 0f;
                nested_1.AddCell(cell_3);
                //string patient = patientSearchVisits.Value;
                string patient = "";
                PdfPCell cell_4 = new PdfPCell(new Phrase(patient, new Font(Font.FontFamily.HELVETICA, 8f, Font.BOLD, BaseColor.BLACK)));
                cell_4.BorderWidthTop = 0f;
                cell_4.BorderWidthBottom = 0f;
                cell_4.BorderWidthLeft = 0f;
                cell_4.BorderWidthRight = 0f;
                cell_4.PaddingBottom = 5f;
                nested_1.AddCell(cell_4);
                PdfPCell cell_5 = new PdfPCell(new Phrase("Start:", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell_5.BorderWidthTop = 0f;
                cell_5.BorderWidthBottom = 0f;
                cell_5.BorderWidthLeft = 0f;
                cell_5.BorderWidthRight = 0f;
                nested_1.AddCell(cell_5);
                //string start = startSearchFor.Value;
                PdfPCell cell_6 = new PdfPCell(new Phrase(emrData.startDate_s ?? "", new Font(Font.FontFamily.HELVETICA, 8f, Font.BOLD, BaseColor.BLACK)));
                cell_6.BorderWidthTop = 0f;
                cell_6.BorderWidthBottom = 0f;
                cell_6.BorderWidthLeft = 0f;
                cell_6.BorderWidthRight = 0f;
                cell_6.PaddingBottom = 5f;
                nested_1.AddCell(cell_6);
                PdfPCell cell_7 = new PdfPCell(new Phrase("End:", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell_7.BorderWidthTop = 0f;
                cell_7.BorderWidthBottom = 0f;
                cell_7.BorderWidthLeft = 0f;
                cell_7.BorderWidthRight = 0f;
                nested_1.AddCell(cell_7);
                //string end = endSearchFor.Value;
                PdfPCell cell_8 = new PdfPCell(new Phrase(emrData.endDate_s ?? "", new Font(Font.FontFamily.HELVETICA, 8f, Font.BOLD, BaseColor.BLACK)));
                cell_8.BorderWidthTop = 0f;
                cell_8.BorderWidthBottom = 0f;
                cell_8.BorderWidthLeft = 0f;
                cell_8.BorderWidthRight = 0f;
                cell_8.PaddingBottom = 5f;
                nested_1.AddCell(cell_8);
                PdfPCell nesthousing_cap = new PdfPCell(nested_1);
                nesthousing_cap.BackgroundColor = new BaseColor(240, 240, 240);
                nesthousing_cap.Colspan = 6;
                table.AddCell(nesthousing_cap);
                //First_ row e

                //Caption b
                PdfPCell cell1_1 = new PdfPCell(new Phrase("Sheduled Appointment", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_1.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_1.HorizontalAlignment = 1;
                table.AddCell(cell1_1);
                PdfPCell cell1_2 = new PdfPCell(new Phrase("Billing", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_2.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_2.HorizontalAlignment = 1;
                table.AddCell(cell1_2);
                PdfPCell cell1_3 = new PdfPCell(new Phrase("Encounter Note", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_3.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_3.HorizontalAlignment = 1;
                table.AddCell(cell1_3);
                PdfPCell cell1_4 = new PdfPCell(new Phrase("Problem List", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_4.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_4.HorizontalAlignment = 1;
                table.AddCell(cell1_4);
                PdfPCell cell1_5 = new PdfPCell(new Phrase("Stored Documents", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_5.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_5.HorizontalAlignment = 1;
                table.AddCell(cell1_5);
                PdfPTable nested_cap = new PdfPTable(3);
                float[] widths_cap = new float[] { 2f, 2f, 1f };
                nested_cap.SetWidths(widths_cap);
                nested_cap.HorizontalAlignment = 0;
                PdfPCell cell1_6 = new PdfPCell(new Phrase("Prescriotions new/renewals", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_6.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_6.HorizontalAlignment = 1;
                nested_cap.AddCell(cell1_6);
                PdfPCell cell1_7 = new PdfPCell(new Phrase("Use of reminders/alerts", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_7.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_7.HorizontalAlignment = 1;
                nested_cap.AddCell(cell1_7);
                PdfPCell cell1_8 = new PdfPCell(new Phrase("Labs", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLDITALIC, BaseColor.BLACK)));
                cell1_8.BackgroundColor = new BaseColor(240, 240, 240);
                cell1_8.HorizontalAlignment = 1;
                nested_cap.AddCell(cell1_8);
                PdfPCell nesthousing_cell = new PdfPCell(nested_cap);
                nesthousing_cell.Colspan = 6;
                table.AddCell(nesthousing_cell);
                //Caption e

                //Row b
                PdfPCell cell2_1 = new PdfPCell(new Phrase(emrData.appointments ?? "", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLD, BaseColor.BLACK)));
                cell2_1.HorizontalAlignment = 1;
                table.AddCell(cell2_1);
                PdfPCell cell2_2 = new PdfPCell(new Phrase(emrData.billing ?? "", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLD, BaseColor.BLACK)));
                cell2_2.HorizontalAlignment = 1;
                table.AddCell(cell2_2);
                PdfPCell cell2_3 = new PdfPCell(new Phrase(emrData.enc_notes ?? "", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLD, BaseColor.BLACK)));
                cell2_3.HorizontalAlignment = 1;
                table.AddCell(cell2_3);
                PdfPCell cell2_4 = new PdfPCell(new Phrase(emrData.problem_list ?? "", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLD, BaseColor.BLACK)));
                cell2_4.HorizontalAlignment = 1;
                table.AddCell(cell2_4);
                PdfPCell cell2_5 = new PdfPCell(new Phrase(emrData.documents ?? "", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLD, BaseColor.BLACK)));
                cell2_5.HorizontalAlignment = 1;
                table.AddCell(cell2_5);
                PdfPTable nested_cap_r = new PdfPTable(3);
                float[] widths_cap_r = new float[] { 2f, 2f, 1f };
                nested_cap_r.SetWidths(widths_cap_r);
                nested_cap_r.HorizontalAlignment = 0;
                PdfPCell cell2_6 = new PdfPCell(new Phrase(emrData.prescriptions ?? "", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLD, BaseColor.BLACK)));
                cell2_6.HorizontalAlignment = 1;
                nested_cap_r.AddCell(cell2_6);
                PdfPCell cell2_7 = new PdfPCell(new Phrase(emrData.reminders ?? "", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLD, BaseColor.BLACK)));
                cell2_7.HorizontalAlignment = 1;
                nested_cap_r.AddCell(cell2_7);
                PdfPCell cell2_8 = new PdfPCell(new Phrase(emrData.labs ?? "", new Font(Font.FontFamily.HELVETICA, 7f, Font.BOLD, BaseColor.BLACK)));
                cell2_8.HorizontalAlignment = 1;
                nested_cap_r.AddCell(cell2_8);
                PdfPCell nesthousing_cell_r = new PdfPCell(nested_cap_r);
                nesthousing_cell_r.Colspan = 6;
                table.AddCell(nesthousing_cell_r);


                doc.Add(table);
                doc.Close();


                return ms.GetBuffer();
            }
        }


    }
}