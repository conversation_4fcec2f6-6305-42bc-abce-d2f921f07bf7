<?xml version="1.0" encoding="utf-8"?>
<!-- WEB CONFIG IN THIS PROJECT SHOULD ONLY BE MODIFIED BY -->
<!-- Web.Base.*.config -->
<!-- CHANGES MADE DIRECTLY TO THE Web.config WILL BE OVERWRITTEN -->
<configuration>
	<configSections>
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
		<section name="ssrsCredentials" type="System.Configuration.NameValueSectionHandler" requirePermission="false" />
		<section name="VirtualVisitSettings" type="Cerebrum.VirtualVisit.Config, Cerebrum.VirtualVisit" />
		<section name="WorkSheetApiSettings" type="Cerebrum30.Services.WorkSheet.Config, Cerebrum30" />
		<section name="DHDRSettings" type="Cerebrum.DHDR.Services.Config, Cerebrum.DHDR.Services" />
		<section name="Auth0" type="AwareServiceClient.Auth.Auth0.Auth0Config, AwareServiceClient" />
	</configSections>
	<appSettings>
		<add key="copyright" value="Aware MD" />
		<add key="applicationName" value="Cerebrum \u2122 EMR" />
		<add key="techSupportEmail" value="<EMAIL>" />
		<add key="webpages:Version" value="*******" />
		<add key="webpages:Enabled" value="false" />
		<add key="ClientValidationEnabled" value="true" />
		<add key="UnobtrusiveJavaScriptEnabled" value="true" />
		<add key="transGroupSize" value="5" />
		<add key="owin:AppStartup" value="Cerebrum30.Startup, Cerebrum30" />
		<add key="SMTPTelus" value="mail.exchange.telus.com" />
		<add key="SMTPpw_Telus" value="1chrc23" />
		<add key="SMTPun_Telus" value="<EMAIL>" />
		<add key="SMTP_Telus_Port" value="587" />
		<add key="SMTPssl_Telus" value="on" />
		<add key="SMTPGoogle" value="smtp.gmail.com" />
		<add key="SMTPpw_Googl" value="chrc_2016" />
		<add key="SMTPun_Google" value="<EMAIL>" />
		<add key="SMTP_From" value="<EMAIL>" />
		<add key="CerebrumEnvironment" value="Development" />
		<add key="CerebrumVersion" value="2.3.0" />
		<add key="CerebrumSVNBuild" value="2.3.4612" />
		<add key="drugInteractionWeb" value="false" />
		<add key="dicomUser" value="<EMAIL>" />
		<add key="HL7User" value="<EMAIL>" />
		<add key="BillingDownloadFolder" value="c:\InetPub\Cardio\pace\Billing\FromEDT" />
		<add key="CLINIC_SERVER_IMAGEAPI" value="https://c3clinic.mycerebrum.com/" />
		<add key="AUTH_SERVER" value="https://c3authdev.mycerebrum.com/" />
		<add key="AUTH_SERVER_TOKEN_EXPIRED_IN_SECONDS" value="86400" />
		<add key="AUTH_SERVER_TOKEN_BUFFER_SECONDS" value="30" />
		<add key="clientkey" value="MyCerebrum" />
		<add key="clientsecrect" value="12345" />
		<add key="targetclienturl" value="https://c3clinic.mycerebrum.com/ " />
		<add key="cerebrumclienturl" value="https://mycerebrum.com/" />
		<add key="HL7LabResultFiles" value="C:\Users\<USER>\Documents\HL7_from_lab" />
		<add key="HL7ProcessedLabResultFiles" value="C:\Users\<USER>\Documents\HL7_from_lab\Transferred" />
		<add key="olis_PKI_Cert_File" value="C:\dicom\cert\06976914.cer.txt" />
		<add key="HRM_PATH" value="__HRM_PATH__" />
		<add key="HRMSshCertificateFileFolder" value="C:\Projects\HRM test files (for importing)\SshCertificateFile" />
		<add key="HRMXmlFileArchiveFolder" value="C:\Projects\HRM test files (for importing)\HRM downloaded files from production - Copy\Archive" />
		<add key="BillingLogFileTypes" value="C:\Projects\Cerebrum30\BillingService\bin\Debug\Log\EBSClient*.txt;C:\Projects\Cerebrum30\BillingService\bin\Debug\Log\BillingService*.txt" />
		<add key="UploadFileSize" value="25" />
		<add key="CerebrumServer" value="Main" />
		<add key="CachingOn" value="false" />
		<add key="ClinicalSearchLink" value="https://clinicalsearchstaging.mycerebrum.com/with_lis?" />
		<add key="TwoFactorAuthentication_From" value="<EMAIL>" />
		<add key="olis_Query_File" value="E:\HL7_Laboratory_Results\OLIS\in\" />
		<add key="olis_Response_File" value="E:\HL7_Laboratory_Results\OLIS\out\" />
		<add key="olisURLEndPoint" value="https://olisapitest.mycerebrum.com/" />
		<add key="OLISAPIurl" value="https://olisapitest.mycerebrum.com/"/>
		<add key="FormbuilderLink" value="https://c3dev.mycerebrum.com/FormBuilder/" />
		<add key="FormbuilderFolder" value="E:\FormBuilderPackage_final\Playground" />
		<add key="FrontendApplicationInsightsConnectionString" value="InstrumentationKey=6a62aa46-35c6-4b63-b4f4-f1e125ac92f1;IngestionEndpoint=https://canadacentral-1.in.applicationinsights.azure.com/;LiveEndpoint=https://canadacentral.livediagnostics.monitor.azure.com/;ApplicationId=e5f7dd28-6982-4051-8e22-9e2ad502baa9" />
		<add key="CachePhrases" value="false" />
		<add key="FormbuilderTokenExpiredInSeconds" value="1200" />
		<add key="FbNewImplementation" value="true" />
		<add key="FormbuilderSite" value="http://localhost:61394/" />
		<add key="Cerebrum30Log" value="E:\Cerebrum30Log\Log\" />
		<add key="Cerebrum30ErrorLog" value="E:\Cerebrum30Log\Error\" />
		<add key="LoginMinutes" value="51" />
		<add key="IsSaveChangesUseAsync" value="true" />
		<add key="eConsultActive" value="true" />
		<add key="NewUaoImplementationActive" value="true" />
		<add key="eConsultAuthenticationUrl" value="https://c3devapi.mycerebrum.com/ExternalAuthentication/v1.1" />
		<add key="IsEFormsEnabled" value="true" />
		<add key="EFormsRedirectUri" value="http://localhost:65062/api/OneIdToken2/" />
		<add key="IdentityServerUrl" value="https://login.microsoftonline.com/b9503a1b-6cad-4f88-a78a-9eb883a328f3" />
		<add key="IdentityServerClientId" value="a01dd64c-9152-4105-8788-b6a63e67ec1b" />
		<add key="IdentityServerClientSecret" value="****************************************" />
		<add key="IdentityServerAuthorizeUrlScope" value="https://AMDUAT.onmicrosoft.com/ExtAuthAPI/.default" />
		<add key="IdentityServerAccessTokenScope" value="https://AMDUAT.onmicrosoft.com/ExtAuthAPI/.default" />
		<add key="IdentityServerLogoutScope" value="https://AMDUAT.onmicrosoft.com/ExtAuthAPI/.default" />
		<add key="IdentityServerGetTokenByUserIdScope" value="https://AMDUAT.onmicrosoft.com/ExtAuthAPI/.default" />
		<add key="IdentityServerUpdateOAuthSessionScope" value="https://AMDUAT.onmicrosoft.com/ExtAuthAPI/.default" />
		<add key="IdentityServerRefreshTokenScope" value="https://AMDUAT.onmicrosoft.com/ExtAuthAPI/.default" />
		<add key="IdentityServerTokenEndPoint" value="/oauth2/v2.0/token" />
		<add key="UserServiceBaseUrl" value="https://c3devapi.mycerebrum.com/user/" />
		<add key="UserServiceAuthority" value="https://login.microsoftonline.com/AwareMDUATAD.onmicrosoft.com" />
		<add key="UserServiceClientId" value="706c2a52-9cc7-4440-ad43-65cf97eb35d5" />
		<add key="UserServiceClientSecret" value="**********************************" />
		<add key="UserServiceScope" value="https://AwareMDUATAD.onmicrosoft.com/usersApi/.default" />
		<add key="EconsultOntarioBaseUrl" value="https://provider.pst.ehealthontario.ca/api2/fhir" />
		<add key="X_GTWA_CLIENT_ID" value="2c5de43038e9b214d4d0bc9460dfd5c9" />
		<add key="X_GTWA_CLIENT_SECRET" value="********************************" />
		<add key="EconsultRequestTimeout" value="300" />
		<add key="IdpSessionTimeout" value="15" />
		<add key="EHRServiceName" value="eConsult" />
		<add key="EHRServiceVersion" value="1.000" />
		<add key="EHREndpoint" value="https://provider.pst.ehealthontario.ca/api2/fhir" />
		<add key="APIKey" value="RWxlY3Ryb25pY0Zvcm1zOkVsZWN0cm9uaWNGb3Jtc0tleQ==" />
		<add key="CLIENT_TXT_ID_ORG" value="160084815861" />
		<add key="eConsultAttachedFilesFolder" value="\\cerebrum3cotest\e$\eConsultAttachedFiles\" />
		<add key="eConsultCerebrumUrl" value="http://localhost:65062" />
		<add key="AssertionConsumerServiceURL" value="http://localhost:65062/api/OneIdToken2/" />
		<!-- Staging -->
		<!--
    <add key="AssertionConsumerServiceURL" value="https://staging.mycerebrum.com/api/OneIdToken2/" />
    -->
		<!-- Production -->
		<!-- <add key="AssertionConsumerServiceURL" value="https://cerebrum.mycerebrum.com/api/OneIdToken2/" />-->
		<!--<add key="eConsultCertificate" value="C:\Certificate\40491743-combined.pfx" />-->
		<add key="eConsultCertificate" value="C:\Certificate\HRM.AWAREMD.PST-combined.pfx" />
		<add key="eConsultCertificatePWD" value="123" />
		<add key="eConsultErrorLog" value="C:\Source\C3Working_eConsult_Copy_Prod\Cerebrum30\eConsultLogs\Error\" />
		<add key="eConsultLog" value="C:\Source\C3Working_eConsult_Copy_Prod\Cerebrum30\eConsultLogs\Log\" />
		<add key="HrmUrl" value="https://wsgateway.pst.ehealthontario.ca:9443/API/FHIR/HRM/omduat" />
		<add key="AwareServicesUrl" value="http://services.localhost"/>
		<add key="ConfigCatKey" value="configcat-sdk-1/Z8zcCOH6GUiD4oVe-Qg11g/rgHA8evOf02WiU6Rb6k4-g" />
		<add key="C3HrmServiceWebHookUrl" value="http://cerebrum.c3.localhost/api/WebHookHrmReport/UpdateStatus"/>
		<add key="HrmClientCertificatePath" value="E:\Certificates\HRM.AWAREMD.PST-combined.pfx" />
		<add key="HrmClientCertificatePassword" value="123" />
		<add key="BullsEyeFolder" value="E:\Cerebrum3\BullsEye" />
		<add key="IpAddressHeader" value="HTTP_X_FORWARDED_FOR" />
		<add key="TwoFactorAuthenticationTimeoutSeconds" value="300" />
		<add key="eReferralAPIBaseUrl" value="http://localhost:5014" />
		<add key="eReferralIdentityServerUrl" value="https://login.microsoftonline.com/AMDUAT.onmicrosoft.com" />
		<add key="eReferralAPIClientId" value="a89f0a8c-7089-407a-955f-1e8c5e55789f" />
		<add key="eReferralAPIClientSecret" value="****************************************" />
		<add key="eReferralAPIAccessTokenScope" value="https://AMDUAT.onmicrosoft.com/eReferralWebAPI/.default" />
		<add key="IsPrescribeITActive" value="true" />
		<add key="IsPharmacyModuleActive" value="true" />
		<add key="PrescribeITAPIUrl" value="http://localhost:5000" />
		<add key="PrescribeITSessionLength" value="720" />
		<add key="CerebrumDomain" value="localhost" />
		<add key="PrescribeITClientUrl" value="http://localhost:4200" />
		<add key="PrescribeITOtpMessage" value="OTP Delivered successfully" />
		<add key="IsAppointmentPriorityEnabled" value="true" />
		<add key="BlacklistedPasswordsFilePath" value="\Content\Resources\common-password-blacklist.txt" />
		<add key="Cerebrum3AppData" value="\\stawmduatstoragecace01.file.core.windows.net\c3-shared-files\uat\EDFiles"/>
		<add key="Cerebrum3ClinicalViewerED" value="\labs\OLIS\ClinicalViewerED"/>
	</appSettings>
	<Auth0
	  ClientId="__auth0ClientId__"
	  ClientSecret="__auth0ClientSecret__"
	  Tenant="__auth0Tenant__"
	  ManagementApiAudience="https://__auth0Tenant__/api/v2/"
	  AwareServicesApiAudience="aware-services-api"
	  DefaultRoleId="__auth0DefaultRoleId__"
  />
	<connectionStrings>
		<add name="C3Context" providerName="System.Data.SqlClient" connectionString="Server=CAAWDVSQL01;Database=C3_DEV_Cer30;User ID=cerebrum_sa;Password=***********;Application Name=C3Test; MultipleActiveResultSets=true;" />
		<add name="C3RadContext" providerName="System.Data.SqlClient" connectionString="Server=CAAWDVSQL01;Database=C3_DEV_Cer30RAD;User ID=cerebrum_sa;Password=***********;Application Name=C3Test; MultipleActiveResultSets=true;" />
		<add name="C3AuditContext" providerName="System.Data.SqlClient" connectionString="Server=CAAWDVSQL01;Database=C3_DEV_Cer30Audit;User ID=cerebrum_sa;Password=***********;Application Name=C3Test; MultipleActiveResultSets=true;Max Pool Size=500;" />
		<add name="cm2Entities" providerName="System.Data.EntityClient" connectionString="metadata=res://*/C2.C2Model.csdl|res://*/C2.C2Model.ssdl|res://*/C2.C2Model.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=pre-proddb.chrc.net\mycerebrum;initial catalog=cm2;persist security info=True;user id=cerebrum_sa;password=*******;MultipleActiveResultSets=True;App=EntityFramework&quot;" />
		<add name="DrugsInteractionEntities" providerName="System.Data.EntityClient" connectionString="metadata=res://*/DrugInteraction.csdl|res://*/DrugInteraction.ssdl|res://*/DrugInteraction.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=Cerebrum3dbtest\CEREBRUM3,7568;initial catalog=DrugsInteraction;persist security info=True;user id=cerebrum_sa;password=***********;multipleactiveresultsets=True;application name=EntityFramework&quot;" />
		<add name="C3InterfaceContext" providerName="System.Data.SqlClient" connectionString="Server=CAAWDVSQL01;Database=C3_DEV_Cer30AppLogs;User ID=cerebrum_sa;Password=***********; MultipleActiveResultSets=true;" />
		<add name="C3FormBuilderContext" providerName="System.Data.SqlClient" connectionString="Server=CAAWDVSQL01;Database=C3_DEV_FormBuilderDB;User ID=cerebrum_sa;Password=***********; MultipleActiveResultSets=true;" />
	</connectionStrings>
	<DHDRSettings>
		<ADB2CConfig ClientId="6b84e0dc-a525-41fd-bda8-bb7b261fce27" ClientSecret="****************************************" Authority="https://login.microsoftonline.com/AMDUAT.onmicrosoft.com" Scope="https://AMDUAT.onmicrosoft.com/dhdr/.default" />
		<CerebrumConfig Enabled="true" ApiBaseUrl="https://c3devapi.mycerebrum.com/dhdrservices/api/v1/DHDR" />
		<DHDRDefaultDateLowerLimit Enabled="true" Value="120" />
	</DHDRSettings>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
		</providers>
	</entityFramework>
	<location path="eConsult/Consult">
		<system.web>
			<httpRuntime maxRequestLength="524288000" />
		</system.web>
		<system.webServer>
			<security>
				<requestFiltering>
					<requestLimits maxAllowedContentLength="524288000" />
				</requestFiltering>
			</security>
		</system.webServer>
	</location>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
				<bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Ninject" publicKeyToken="c7192dc5380945e7" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.2.0.0" newVersion="3.2.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.AI.Agent.Intercept" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Hl7.Fhir.ElementModel" publicKeyToken="d706911480550fc3" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.9.0.0" newVersion="1.9.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Hl7.Fhir.Serialization" publicKeyToken="d706911480550fc3" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.9.0.0" newVersion="1.9.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Hl7.Fhir.Support" publicKeyToken="d706911480550fc3" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.9.0.0" newVersion="1.9.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Hl7.Fhir.Support.Poco" publicKeyToken="d706911480550fc3" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.9.0.0" newVersion="1.9.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Hl7.FhirPath" publicKeyToken="d706911480550fc3" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.9.0.0" newVersion="1.9.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.ApplicationInsights" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.21.0.429" newVersion="2.21.0.429" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.IdentityModel.Tokens.Jwt" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-6.5.0.0" newVersion="6.5.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Tokens" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-6.5.0.0" newVersion="6.5.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Logging" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-6.5.0.0" newVersion="6.5.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
		</assemblyBinding>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<ssrsCredentials>
		<add key="SSRSServerUrl" value="http://caawdvsql01/ReportServer/" />
		<add key="UserName" value="svc-c3ssrsuser" />
		<add key="Password" value="5vK5&amp;k21zJO3"/>
		<add key="Domain" value="well" />
	</ssrsCredentials>
	<system.codedom>
		<compilers>
			<compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:6 /nowarn:1659;1699;1701" />
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:14 /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
		</compilers>
	</system.codedom>
	<system.serviceModel>
		<serviceHostingEnvironment aspNetCompatibilityEnabled="true" />
	</system.serviceModel>
	<system.web>
		<!-- timeout value must be the same as LoginMinutes -->
		<compilation debug="true" targetFramework="4.8" batch="false" />
		<customErrors mode="Off" defaultRedirect="~/Error">
			<error redirect="~/Error/NotFound" statusCode="404" />
		</customErrors>
		<httpRuntime maxRequestLength="30000000" targetFramework="4.8" requestValidationMode="2.0" />
		<sessionState mode="InProc" timeout="51" />
		<httpModules>
			<add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation" />
			<add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" />
		</httpModules>
	</system.web>
	<system.webServer>
		<httpProtocol>
			<customHeaders>
			</customHeaders>
		</httpProtocol>
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="30000000" />
			</requestFiltering>
		</security>
		<modules>
		    <remove name="TelemetryCorrelationHttpModule" />
			<add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation" preCondition="managedHandler" />
		    <remove name="ApplicationInsightsWebTracking" />
			<add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler" />
		</modules>
		<validation validateIntegratedModeConfiguration="false" />
	</system.webServer>
	<VirtualVisitSettings>
		<!-- trial account -->
		<CerebrumConfig EnableAllPractices="true" EnabledPracticeIds="" CorsOrigins="*" PatientUrl="https://pvv.mycerebrum.com" PlaceHolderUsername="<EMAIL>" AuditLogLevel="40000" JoinRoomAheadMinutes="5" DefaultEndDurationInMinutes="120" />
		<TextMessageAccount FromPhone="+***********" AccountSid="**********************************" AuthToken="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" />
		<VideoConferenceAccount AccountSid="**********************************" ApiKeySid="SK0f2967d17fe57e1814898805e798d2dd" ApiKeySecret="NrUcWqmwT5eenN9yhjzZ6LZdM76HXd1w" AuthToken="********************************" />
	</VirtualVisitSettings>
	<WorkSheetApiSettings>
		<ADB2CConfig ClientId="6b84e0dc-a525-41fd-bda8-bb7b261fce27" ClientSecret="****************************************" Authority="https://login.microsoftonline.com/AMDUAT.onmicrosoft.com" Scope="https://AMDUAT.onmicrosoft.com/dhdr/.default" />
		<WorkSheetApiConfig Enabled="true" EnabledForAllPractice="false" DefaultCacheRefreshMinute="240" EndpointBase="https://c3devapi.mycerebrum.com/worksheetservices" />
	</WorkSheetApiSettings>
</configuration>
