#define ENABLE_CEREBRUM_LABS
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Cerebrum.Data;
using Cerebrum30.Infrastructure;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.DataProtection;
using System;
using System.IO;
using System.Net.Http;

var builder = WebApplication.CreateBuilder(args);

// Configure Kestrel server options to handle large headers (fix 431 error)
builder.Services.Configure<Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerOptions>(options =>
{
    options.Limits.MaxRequestHeadersTotalSize = 64 * 1024; // 64KB (default is 32KB)
    options.Limits.MaxRequestHeaderCount = 100; // Increase header count limit
    options.Limits.MaxRequestLineSize = 8192; // 8KB for request line
});

// Configure System.Configuration.ConfigurationManager for backward compatibility
// Note: In .NET 8, ConfigurationManager.AppSettings is read-only, so we'll need to 
// update views to use IConfiguration instead or create a custom configuration provider

// Add Entity Framework
builder.Services.AddDbContext<CerebrumContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"),
        b => b.MigrationsAssembly("Cerebrum30").CommandTimeout(120)));

// Add Audit Entity Framework context with proper connection string
builder.Services.AddDbContext<Cerebrum.Data.Audit.CerebrumAuditContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("C3AuditContext")));

// Add RAD Entity Framework context
builder.Services.AddDbContext<Cerebrum.Data.CerebrumRADContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("C3RadContext")));

// Add InterfaceLog Entity Framework context
builder.Services.AddDbContext<Cerebrum.Data.InterfaceLog.InterfaceLogContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add ASP.NET Core Identity
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequiredLength = 6;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequireLowercase = false;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(30);
    options.Lockout.MaxFailedAccessAttempts = 10;
    options.Lockout.AllowedForNewUsers = true;

    // User settings
    options.User.RequireUniqueEmail = true;
    options.SignIn.RequireConfirmedEmail = false;
})
.AddEntityFrameworkStores<CerebrumContext>()
.AddDefaultTokenProviders();

// Configure cookie authentication
builder.Services.ConfigureApplicationCookie(options =>
{
    options.Cookie.HttpOnly = true;
    options.ExpireTimeSpan = TimeSpan.FromMinutes(240); // Default login minutes
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/LogOff";
    options.AccessDeniedPath = "/Account/AccessDenied";
    options.SlidingExpiration = true;
});

// Add claims transformation to restore custom claims when cookies are refreshed
builder.Services.AddTransient<Microsoft.AspNetCore.Authentication.IClaimsTransformation, Cerebrum30.Security.CerebrumClaimsTransformation>();

// Add MVC services
builder.Services.AddControllersWithViews(options =>
{
    // Add global action filter for debugging ViewData issues
    options.Filters.Add<Cerebrum30.Filters.ActionFilterLoggingAttribute>();
})
.AddNewtonsoftJson(options =>
{
    // Configure Newtonsoft.Json for compatibility with legacy code
    options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
    options.SerializerSettings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
    options.SerializerSettings.DefaultValueHandling = Newtonsoft.Json.DefaultValueHandling.Include;
    options.SerializerSettings.Formatting = Newtonsoft.Json.Formatting.None;
    // Preserve original property names to maintain compatibility with existing JavaScript
    options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();
});

// Add HTTP client services
builder.Services.AddHttpClient();

// Configure data protection for antiforgery tokens
builder.Services.AddDataProtection()
    .PersistKeysToFileSystem(new DirectoryInfo(Path.Combine(Directory.GetCurrentDirectory(), "Keys")))
    .SetApplicationName("Cerebrum30");

// Add antiforgery services for CSRF protection
builder.Services.AddAntiforgery(options =>
{
    // Configure antiforgery options for better compatibility
    options.HeaderName = "RequestVerificationToken";
    options.SuppressXFrameOptionsHeader = false;
    // Use a consistent cookie name
    options.Cookie.Name = "__RequestVerificationToken";
    options.Cookie.HttpOnly = true;
    options.Cookie.SameSite = Microsoft.AspNetCore.Http.SameSiteMode.Strict;
});

// Add other services
builder.Services.AddHttpContextAccessor();

// Register configuration service for backward compatibility
builder.Services.AddScoped<Cerebrum30.Services.IConfigurationService, Cerebrum30.Services.ConfigurationService>();

// Register view rendering service for ASP.NET Core
builder.Services.AddScoped<Cerebrum30.Services.ViewRender.IViewRenderService, Cerebrum30.Services.ViewRender.ViewRenderService>();

// Register OLIS API wrapper services conditionally
#if ENABLE_CEREBRUM_LABS
builder.Services.AddScoped<Cerebrum.Labs.OLISAPILayer.IOlisApiWrapper, Cerebrum.Labs.OLISAPILayer.OlisApiWrapper>();
builder.Services.AddScoped<Cerebrum.Labs.OLIS.services.IOLISBuildQuery, Cerebrum.Labs.OLIS.services.OLISBuildQuery>();
builder.Services.AddScoped<Cerebrum.Labs.LabScc.ILabBLL, Cerebrum.Labs.LabScc.LabBLL>();
builder.Services.AddScoped<Cerebrum.Labs.OLIS.services.IOLISTestBLL, Cerebrum.Labs.OLIS.services.OLISTestBLL>();
#else
// Register stub implementations when OLIS is disabled
builder.Services.AddScoped<Cerebrum.Labs.OLISAPILayer.IOlisApiWrapper>(provider => 
    throw new NotImplementedException("OLIS functionality is currently disabled"));
builder.Services.AddScoped<Cerebrum.Labs.OLIS.services.IOLISBuildQuery>(provider => 
    throw new NotImplementedException("OLIS functionality is currently disabled"));
builder.Services.AddScoped<Cerebrum.Labs.LabScc.ILabBLL>(provider => 
    throw new NotImplementedException("OLIS functionality is currently disabled"));
builder.Services.AddScoped<Cerebrum.Labs.OLIS.services.IOLISTestBLL>(provider => 
    throw new NotImplementedException("OLIS functionality is currently disabled"));
#endif

// Add session support
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(240); // Match cookie timeout
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Add response caching services
builder.Services.AddResponseCaching();

// Add logging services
builder.Services.AddLogging();
builder.Services.AddSingleton<log4net.ILog>(provider =>
    log4net.LogManager.GetLogger(typeof(Program)));

// Register BLL services
builder.Services.AddScoped<Cerebrum.BLL.Common.ICommonBLL, Cerebrum.BLL.Common.CommonBLL>(provider =>
    new Cerebrum.BLL.Common.CommonBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Patient.PatientBLL>(provider =>
    new Cerebrum.BLL.Patient.PatientBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Practice.IPracticeAppointmentTypeBLL, Cerebrum.BLL.Practice.PracticeAppointmentTypeBLL>(provider =>
    new Cerebrum.BLL.Practice.PracticeAppointmentTypeBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.User.IUserCredentialBLL, Cerebrum.BLL.User.UserCredentialBLL>(provider =>
{
    var context = provider.GetRequiredService<CerebrumContext>();
    var configuration = provider.GetRequiredService<IConfiguration>();

    // Get configuration values for token generation
    var authServer = configuration["AppSettings:AUTH_SERVER"];
    var clientKey = configuration["AppSettings:clientkey"];
    var clientSecret = configuration["AppSettings:clientsecrect"];
    var cerebrumClientUrl = configuration["AppSettings:cerebrumclienturl"];

    return new Cerebrum.BLL.User.UserCredentialBLL(context, authServer, clientKey, clientSecret, cerebrumClientUrl);
});

// Register password services
builder.Services.AddScoped<Cerebrum.BLL.Utility.BlacklistedPasswordFileService>(provider =>
{
    var blacklistedPasswordFilePath = builder.Configuration["AppSettings:BlacklistedPasswordsFilePath"];
    // Convert Windows-style path to Unix-style and remove leading backslashes
    var normalizedPath = blacklistedPasswordFilePath.Replace('\\', '/').TrimStart('/');
    var fullPath = Path.Combine(builder.Environment.ContentRootPath, normalizedPath);
    return new Cerebrum.BLL.Utility.BlacklistedPasswordFileService(fullPath);
});
builder.Services.AddScoped<Cerebrum.BLL.User.IUserPasswordService, Cerebrum.BLL.User.UserPasswordService>();

// Register Virtual Visit services with proper context injection
builder.Services.AddScoped<Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository>(provider =>
    new Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository(
        provider.GetRequiredService<CerebrumContext>()));
        
builder.Services.AddScoped<Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository>(provider =>
    new Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository(
        provider.GetRequiredService<CerebrumContext>()));
    
builder.Services.AddScoped<Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository>(provider =>
    new Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository(
        provider.GetRequiredService<Cerebrum.Data.Audit.CerebrumAuditContext>()));

builder.Services.AddScoped<Cerebrum.VirtualVisit.Data.IUOWVirtualVisit<
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository,
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository,
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository
>, Cerebrum30.Areas.VirtualVisit.DataAccess.UOWVirtualVisit<
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository,
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository,
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository
>>();

builder.Services.AddScoped<Cerebrum.VirtualVisit.Seedwork.IVirtualVisitBLL,
    Cerebrum.VirtualVisit.VirtualVisitBLL<
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository,
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository,
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository
    >>();

// Register other UOW services that might be needed
builder.Services.AddScoped<Cerebrum30.Areas.Schedule.DataAccess.IUOWDaysheet, Cerebrum30.Areas.Schedule.DataAccess.UOWDaysheet>();
builder.Services.AddScoped<Cerebrum30.Areas.Schedule.DataAccess.IUOWAppointments, Cerebrum30.Areas.Schedule.DataAccess.UOWAppointments>();
builder.Services.AddScoped<Cerebrum30.Areas.Schedule.DataAccess.IUOWSchedule, Cerebrum30.Areas.Schedule.DataAccess.UOWSchedule>();
builder.Services.AddScoped<Cerebrum.BLL.Practice.IPracticeBLL, Cerebrum.BLL.Practice.PracticeBLL>();
builder.Services.AddScoped<Cerebrum3.Infrastructure.IAppointmentRepository>(provider =>
    new Cerebrum3.DataAccess.AppointmentRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum3.Infrastructure.IAppointmentTypeRepository>(provider =>
    new Cerebrum3.DataAccess.AppointmentTypeRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum3.Infrastructure.IAppointmentTestRepository>(provider =>
    new Cerebrum3.DataAccess.AppointmentTestRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum3.Infrastructure.IPracticeTestRepository>(provider =>
    new Cerebrum3.DataAccess.PracticeTestRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum3.Infrastructure.IOfficeRepository>(provider =>
    new Cerebrum3.DataAccess.OfficeRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum3.Infrastructure.ITestRepository>(provider =>
    new Cerebrum3.DataAccess.TestRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum3.Infrastructure.IAppointmentPriorityRepository, Cerebrum3.DataAccess.AppointmentPriorityRepository>();
builder.Services.AddScoped<Cerebrum3.Infrastructure.IPracticeDefaultAppointmentPriorityRepository, Cerebrum3.DataAccess.PracticeDefaultAppointmentPriorityRepository>();
builder.Services.AddScoped<Cerebrum.BLL.Admin.IAppointmentPriorityConfigurationBLL, Cerebrum.BLL.Admin.AppointmentPriorityConfigurationBLL>();

// Register MeasurementController dependencies
builder.Services.AddScoped<Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository>(provider =>
    new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.DAL.DataAccess.Repositories.UserRepository>(provider =>
    new Cerebrum30.DAL.DataAccess.Repositories.UserRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Areas.VP.DataAccess.VPRepository>(provider =>
    new Cerebrum30.Areas.VP.DataAccess.VPRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Measurements.MeasurementBLL>(provider =>
    new Cerebrum.BLL.Measurements.MeasurementBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Services.WorkSheet.IWorkSheetApiToggle, Cerebrum30.Services.WorkSheet.WorkSheetApiToggleDatabase>();

// Register medication repositories
builder.Services.AddScoped<Cerebrum30.Areas.Medications.DataAccess.PatientMedicationRepository>(provider =>
    new Cerebrum30.Areas.Medications.DataAccess.PatientMedicationRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Areas.Medications.DataAccess.PatientAllergyRepository>(provider =>
    new Cerebrum30.Areas.Medications.DataAccess.PatientAllergyRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Areas.Measurements.DataAccess.MeasurementImporterRepository>(provider =>
    new Cerebrum30.Areas.Measurements.DataAccess.MeasurementImporterRepository(provider.GetRequiredService<CerebrumContext>()));

// Register patient medication unit of work
builder.Services.AddScoped<Cerebrum30.Areas.Medications.DataAccess.IUnitOfWorkPatientMedication, Cerebrum30.Areas.Medications.DataAccess.UnitOfWorkPatientMedication>();
builder.Services.AddScoped<Cerebrum30.Areas.Medications.DataAccess.IUnitOfWorkPatientAllergy, Cerebrum30.Areas.Medications.DataAccess.UnitOfWorkPatientAllergy>();

// Register Unit of Work classes that require CerebrumContext
builder.Services.AddScoped<Cerebrum30.Areas.AdminUser.DataAccess.IUnitOfWorkInventory>(provider =>
    new Cerebrum30.Areas.AdminUser.DataAccess.UnitOfWorkInventory(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Areas.Daysheet.DataAccess.IUnitOfWorkDaysheet>(provider =>
    new Cerebrum30.Areas.Daysheet.DataAccess.UnitOfWorkDaysheet(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Infrastructure.IUnitOfWorkOLISTestRepository>(provider =>
    new Cerebrum30.Infrastructure.UnitOfWorkOLISTestRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Infrastructure.IUnitOfWorkAppointmentRepository>(provider =>
    new Cerebrum30.Infrastructure.UnitOfWorkAppointment(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Infrastructure.IUnitOfWorkTestRepository>(provider =>
    new Cerebrum30.Infrastructure.UnitOfWorkTestRepository(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum30.Infrastructure.UnitOfWorkTestRepository>(provider =>
    new Cerebrum30.Infrastructure.UnitOfWorkTestRepository(provider.GetRequiredService<CerebrumContext>()));

// Register additional Unit of Work services
// Note: Some of these services have been commented out due to missing types or incorrect constructors
// builder.Services.AddScoped<Cerebrum30.DAL.Infrastructure.IUnitOfWorkFlowSheets, Cerebrum30.DAL.Infrastructure.UnitOfWorkFlowSheetsForms>();
// builder.Services.AddScoped<Cerebrum30.DAL.Infrastructure.IUnitOfWorkRadiology, Cerebrum30.DAL.Infrastructure.UnitOfWorkRadiology>();
builder.Services.AddScoped<Cerebrum30.Areas.Medications.DataAccess.IUnitOfWorkMedication, Cerebrum30.Areas.Medications.DataAccess.UnitOfWorkMedication>();
builder.Services.AddScoped<Cerebrum30.Areas.Medications.DataAccess.IUnitOfWorkMedicationTemp, Cerebrum30.Areas.Medications.DataAccess.UnitOfWorkMedicationTemp>();
builder.Services.AddScoped<Cerebrum30.Areas.AdminUser.DataAccess.IUnitOfWorkImportCsd, Cerebrum30.Areas.AdminUser.DataAccess.UnitOfWorkImportCsd>();

// Register RadDicom dependencies
builder.Services.AddScoped<Cerebrum.BLL.RadDicom.IRadDicomStudyBLL>(provider =>
{
    var cerebrumContext = provider.GetRequiredService<CerebrumContext>();
    var cerebrumRADContext = provider.GetRequiredService<Cerebrum.Data.CerebrumRADContext>();
    return new Cerebrum.BLL.RadDicom.RadDicomStudyBLL(cerebrumContext, cerebrumRADContext);
});

// Register Requisition dependencies
builder.Services.AddScoped<Cerebrum.BLL.Requisition.IRequisitionBLL, Cerebrum.BLL.Requisition.RequisitionBLL>();

// Register additional BLL services for Admin area
builder.Services.AddScoped<Cerebrum.BLL.AdminUser.IApplicationSettingBLL, Cerebrum.BLL.AdminUser.ApplicationSettingBLL>(provider =>
    new Cerebrum.BLL.AdminUser.ApplicationSettingBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Practice.IOfficeEmailBLL, Cerebrum.BLL.Practice.OfficeEmailBLL>(provider =>
    new Cerebrum.BLL.Practice.OfficeEmailBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.HL7.IHL7LostReportBLL, Cerebrum.BLL.HL7.HL7LostReportBLL>(provider =>
    new Cerebrum.BLL.HL7.HL7LostReportBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Practice.IOfficeDailyRegisterBLL, Cerebrum.BLL.Practice.OfficeDailyRegisterBLL>(provider =>
    new Cerebrum.BLL.Practice.OfficeDailyRegisterBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Practice.IOfficeRoomBLL, Cerebrum.BLL.Practice.OfficeRoomBLL>(provider =>
    new Cerebrum.BLL.Practice.OfficeRoomBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Practice.IOfficeRoomTypeBLL, Cerebrum.BLL.Practice.OfficeRoomTypeBLL>(provider =>
    new Cerebrum.BLL.Practice.OfficeRoomTypeBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.AdminUao.IAdminUaoBLL, Cerebrum.BLL.AdminUao.AdminUaoBLL>(provider =>
    new Cerebrum.BLL.AdminUao.AdminUaoBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.ConnectingOntario.IConnectingOntarioBLL, Cerebrum.BLL.ConnectingOntario.ConnectingOntarioBLL>(provider =>
    new Cerebrum.BLL.ConnectingOntario.ConnectingOntarioBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.ConnectingOntario.IConnectingOntarioConfigBLL, Cerebrum.BLL.ConnectingOntario.ConnectingOntarioConfigBLL>(provider =>
    new Cerebrum.BLL.ConnectingOntario.ConnectingOntarioConfigBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Inventory.IInventoryBLL, Cerebrum.BLL.Inventory.InventoryBLL>(provider =>
    new Cerebrum.BLL.Inventory.InventoryBLL(provider.GetRequiredService<CerebrumContext>()));

// Register HCV services
builder.Services.AddScoped<Cerebrum.BLL.HCV.IAppointmentHealthCardValidationBLL, Cerebrum.BLL.HCV.AppointmentHealthCardValidationBLL>(provider =>
    new Cerebrum.BLL.HCV.AppointmentHealthCardValidationBLL(provider.GetRequiredService<CerebrumContext>()));
// Note: HealthCardValidationBLL requires IPatientDemographicBLL which needs to be registered first
// builder.Services.AddScoped<Cerebrum.BLL.HCV.IHealthCardValidationBLL, Cerebrum.BLL.HCV.HealthCardValidationBLL>(provider =>
//     new Cerebrum.BLL.HCV.HealthCardValidationBLL(provider.GetRequiredService<CerebrumContext>(), provider.GetRequiredService<IPatientDemographicBLL>()));

// Register Reminder services
builder.Services.AddScoped<Cerebrum.Reminder.ReminderSentHistoryRepository>();
builder.Services.AddScoped<Cerebrum.Reminder.ReminderSentHistoryService>();
builder.Services.AddScoped<Cerebrum.Reminder.IReminderBLL, Cerebrum.Reminder.ReminderBLL>(provider =>
    new Cerebrum.Reminder.ReminderBLL(provider.GetRequiredService<CerebrumContext>()));

// Register OLIS test services
builder.Services.AddScoped<Cerebrum.BLL.OLIS.IOLISTestResultCategoryBLL, Cerebrum.BLL.OLIS.OLISTestResultCategoryBLL>(provider =>
    new Cerebrum.BLL.OLIS.OLISTestResultCategoryBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.OLIS.IOLISTestResultNomenclatureBLL, Cerebrum.BLL.OLIS.OLISTestResultNomenclatureBLL>(provider =>
    new Cerebrum.BLL.OLIS.OLISTestResultNomenclatureBLL(provider.GetRequiredService<CerebrumContext>()));

// Register Repository services
// Note: Many of these repository interfaces and implementations don't exist in the expected locations
// They need to be verified and properly registered when found
// Commented out due to missing types

// Register RepositoryForPatientChart and its dependencies
builder.Services.AddScoped<Cerebrum.BLL.Documents.IPatientLetterService, Cerebrum.BLL.Documents.PatientLetterService>();
builder.Services.AddScoped<Cerebrum30.DAL.DataAccessGB.RepositoryForPatientChart>(provider =>
    new Cerebrum30.DAL.DataAccessGB.RepositoryForPatientChart(
        provider.GetRequiredService<CerebrumContext>(),
        provider.GetRequiredService<Cerebrum.Data.CerebrumRADContext>(),
        provider.GetRequiredService<IHttpClientFactory>(),
        provider.GetRequiredService<Cerebrum.BLL.Documents.IPatientLetterService>()
        ));

// Register RepositoryForDemographics with proper context injection
builder.Services.AddScoped<Cerebrum30.DAL.DataAccessGB.RepositoryForDemographics>(provider =>
    new Cerebrum30.DAL.DataAccessGB.RepositoryForDemographics(
        provider.GetRequiredService<CerebrumContext>()));

// Register WebBookingRepository
builder.Services.AddScoped<Cerebrum30.Areas.WebBooking.DataAccess.WebBookingRepository>(provider =>
    new Cerebrum30.Areas.WebBooking.DataAccess.WebBookingRepository(
        provider.GetRequiredService<CerebrumContext>()));

// Register ContactManager dependencies
builder.Services.AddScoped<Cerebrum.ContactManager.IContactManagerEmailService, Cerebrum.ContactManager.ContactManagerEmailService>();
builder.Services.AddScoped<Cerebrum.ContactManager.IContactManagerService, Cerebrum.ContactManager.ContactManagerService>();

// Register additional BLL services from NinjectWebCommon.cs
builder.Services.AddScoped<Cerebrum.BLL.Patient.IPatientBLL, Cerebrum.BLL.Patient.PatientBLL>();
builder.Services.AddScoped<Cerebrum.BLL.Patient.IPatientChartBLL, Cerebrum.BLL.Patient.PatientChartBLL>();
builder.Services.AddScoped<Cerebrum.BLL.PersonnelPerformance.PersonnelPerformanceBLL>();
builder.Services.AddScoped<Cerebrum.Doctors.IPracticeDoctorBLL, Cerebrum.Doctors.PracticeDoctorBLL>();
builder.Services.AddScoped<Cerebrum.BLL.VP.VisitBLL>();
builder.Services.AddScoped<Cerebrum.BLL.VP.VPBLL>(provider =>
    new Cerebrum.BLL.VP.VPBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Study.IStudy, Cerebrum.BLL.Study.Study>();
builder.Services.AddScoped<Cerebrum.BLL.Practice.IOfficeBLL, Cerebrum.BLL.Practice.OfficeBLL>();
builder.Services.AddScoped<Cerebrum.BLL.Schedule.AppointmentsBLL>();
builder.Services.AddScoped<Cerebrum.BLL.Schedule.TriageBLL>(provider =>
    new Cerebrum.BLL.Schedule.TriageBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.Schedule.Scheduler>();
builder.Services.AddScoped<Cerebrum.BLL.Cohort.ICohortBLL, Cerebrum.BLL.Cohort.CohortBLL>();
builder.Services.AddScoped<Cerebrum.BLL.ExternalDoctors.IExternalDoctorBLL, Cerebrum.BLL.ExternalDoctors.ExternalDoctorBLL>();
builder.Services.AddScoped<Cerebrum.BLL.Common.UOWExternalDoctor>();
builder.Services.AddScoped<Cerebrum30.Areas.Admin.DataAccess.IUOWFormbuilder, Cerebrum30.Areas.Admin.DataAccess.UOWFormBuilder>();
builder.Services.AddScoped<Cerebrum30.DAL.DataAccessGB.Repositories.RepositoryForExternalDoctors>();
builder.Services.AddScoped<Cerebrum.BLL.Documents.IDocumentsBLL, Cerebrum.BLL.Documents.DocumentsBLL>(provider =>
    new Cerebrum.BLL.Documents.DocumentsBLL(
        provider.GetRequiredService<CerebrumContext>(),
        provider.GetRequiredService<IWebHostEnvironment>()));

// Register additional repositories and services
// VPRepository already registered above
// MeasurementRepository already registered above
// UserRepository already registered above
builder.Services.AddScoped<Cerebrum.BLL.User.UserBLL>();

// Register UnitOfWorkAdminUsers and RepositoryForAdminUser
builder.Services.AddScoped<Cerebrum30.DAL.DataAccessGB.UnitOfWorkAdminUsers>();
builder.Services.AddScoped<Cerebrum30.DAL.DataAccessGB.RepositoryForAdminUser>();

// Register RepositoryForPatient with dependencies
builder.Services.AddScoped<Cerebrum30.DAL.DataAccessGB.Repositories.RepositoryForPatient>();

// Register RepositoriesForEMRRecords with dependencies
builder.Services.AddScoped<Cerebrum30.DAL.DataAccessGB.RepositoriesForEMRRecords>();

// Register UOWDocuments for dependency injection
builder.Services.AddScoped<Cerebrum30.Areas.Documents.DataAccess.IUOWDocuments, Cerebrum30.Areas.Documents.DataAccess.UOWDocuments>();

// Register BookingConfirmationBLL
builder.Services.AddScoped<Cerebrum.BLL.IBookingConfirmationBLL, Cerebrum.BLL.BookingConfirmationBLL>(provider =>
    new Cerebrum.BLL.BookingConfirmationBLL(provider.GetRequiredService<CerebrumContext>()));

// Register token helper if needed
builder.Services.AddScoped<Cerebrum30.Utility.ITokenHelper, Cerebrum30.Utility.TokenHelper>();

// Register ExternalDocument BLL
builder.Services.AddScoped<Cerebrum.ExternalDocument.ExternalDocumentBLL>(provider =>
    new Cerebrum.ExternalDocument.ExternalDocumentBLL(
        provider.GetRequiredService<CerebrumContext>(),
        provider.GetRequiredService<Cerebrum.ContactManager.IContactManagerService>()));

// Register ExternalCommBLL
builder.Services.AddScoped<Cerebrum.BLL.Common.ExternalCommBLL>(provider =>
    new Cerebrum.BLL.Common.ExternalCommBLL(
        provider.GetRequiredService<CerebrumContext>(),
        provider.GetRequiredService<Cerebrum.Data.InterfaceLog.InterfaceLogContext>()));

// Register HL7BLL
builder.Services.AddScoped<Cerebrum.BLL.HL7.IHL7BLL, Cerebrum.BLL.HL7.HL7BLL>();


// Register filters
builder.Services.AddScoped<Cerebrum30.Filters.BaseOnActionFilter>();

// Register compatibility layer services
builder.Services.AddScoped<Cerebrum30.Infrastructure.ApplicationUserManager>();
builder.Services.AddScoped<Cerebrum30.Infrastructure.ApplicationRoleManager>();
builder.Services.AddScoped<Cerebrum30.Infrastructure.ApplicationSignInManager>();

// Register Auth0 services
builder.Services.AddScoped<AwareServiceClient.Auth.Auth0.Auth0TokenService>();
builder.Services.AddScoped<AwareServiceClient.Factories.AwareHttpClientFactory>();

// Register EconsultBLL
builder.Services.AddScoped<Cerebrum.BLL.Econsult.EconsultBLL>(provider =>
    new Cerebrum.BLL.Econsult.EconsultBLL(provider.GetRequiredService<CerebrumContext>()));

// Register UserTimesheetBLL
builder.Services.AddScoped<Cerebrum.BLL.Timesheet.IUserTimesheetBLL, Cerebrum.BLL.Timesheet.UserTimesheetBLL>(provider =>
    new Cerebrum.BLL.Timesheet.UserTimesheetBLL(provider.GetRequiredService<CerebrumContext>()));

// Register Schedule BLL services
builder.Services.AddScoped<Cerebrum.BLL.Factory.TimePeriodRelationFactory>();
builder.Services.AddScoped<Cerebrum.BLL.Schedule.Util.ITimePeriodRelationService, Cerebrum.BLL.Schedule.Util.TimePeriodRelationService>();
builder.Services.AddScoped<Cerebrum.BLL.Schedule.iStaffScheduleBLL, Cerebrum.BLL.Schedule.StaffScheduleBLL>();
builder.Services.AddScoped<Cerebrum.BLL.Schedule.DaysheetBLL>(provider =>
    new Cerebrum.BLL.Schedule.DaysheetBLL(provider.GetRequiredService<CerebrumContext>()));

// Register ReportLetterQueue services
builder.Services.AddScoped<Cerebrum.BLL.ReportLetterQueue.IAdminReportQueueBLL, Cerebrum.BLL.ReportLetterQueue.AdminReportQueueBLL>(provider =>
    new Cerebrum.BLL.ReportLetterQueue.AdminReportQueueBLL(provider.GetRequiredService<CerebrumContext>()));

var app = builder.Build();

// Initialize AutoMapper
Cerebrum30.AutomapperConfig.RegisterMapper();

// Configure the HTTP request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}
else
{
    // In development, add detailed ViewData error logging
    app.UseMiddleware<Cerebrum30.Middleware.ViewDataExceptionMiddleware>();
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseResponseCaching();
app.UseStaticFiles(); // Serve files from wwwroot

// Serve files from Content directory
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "Content")),
    RequestPath = "/Content"
});

// Serve files from Scripts directory
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "Scripts")),
    RequestPath = "/Scripts"
});

// Serve files from fonts directory
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "fonts")),
    RequestPath = "/fonts"
});

// Serve files from Areas directories
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "Areas")),
    RequestPath = "/Areas"
});

// Serve files from Help directory
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "Help")),
    RequestPath = "/Help"
});

// Serve compiled TypeScript files from wwwroot/js/compiled as /app
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "wwwroot", "js", "compiled")),
    RequestPath = "/app"
});

// Serve original app directory files (templates, etc.) as /app with lower priority
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "app")),
    RequestPath = "/app",
    ServeUnknownFileTypes = true,
    DefaultContentType = "text/html"
});

// Create a simple endpoint for /traceur to prevent 404s from SystemJS
app.MapGet("/traceur", async (HttpResponse response) => {
    // Return a minimal traceur stub that mimics the Traceur API
    response.ContentType = "application/javascript";
    await response.WriteAsync(@"// Traceur stub - TypeScript is pre-compiled
// Minimal Traceur API stub for SystemJS compatibility
window.traceur = {
    options: {},
    compile: function(source, options) {
        // Return source unchanged since we pre-compile TypeScript
        return source;
    },
    transform: function(source, options) {
        return source;
    },
    Compiler: function() {
        return {
            compile: function(source) { return source; }
        };
    }
};

// Also configure SystemJS to not use transpilation
if (typeof System !== 'undefined' && System.config) {
    System.config({
        transpiler: false,
        map: {
            'traceur': '/traceur'
        }
    });
}");
});

// Serve files from node_modules directory
var nodeModulesPath = Path.Combine(builder.Environment.ContentRootPath, "node_modules");
if (Directory.Exists(nodeModulesPath))
{
    app.UseStaticFiles(new StaticFileOptions
    {
        FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(nodeModulesPath),
        RequestPath = "/node_modules"
    });
}

app.UseRouting();

app.UseAuthentication();
app.UseSession();
app.UseAuthorization();

// Configure area routing
app.MapControllerRoute(
    name: "areas",
    pattern: "{area:exists}/{controller=Home}/{action=Index}/{id?}");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
