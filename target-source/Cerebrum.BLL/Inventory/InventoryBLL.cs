using Cerebrum.BLL.Inventory.SPEntities;
using Cerebrum.Data;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.ViewModels.Common;
using Cerebrum.ViewModels.Inventory;
using System;
using System.Collections.Generic;
// EF Core: Use Microsoft.Data.SqlClient instead of System.Data.SqlClient
using Microsoft.Data.SqlClient;
using System.Linq;
// EF Core: Add EntityFrameworkCore for Include extension method
using Microsoft.EntityFrameworkCore;

namespace Cerebrum.BLL.Inventory
{
    public interface IInventoryBLL
    {
        VMInventoryMain GetInventoryMain(int practiceId, VMInventoryRequest request);
        VMInventoryItemsMain GetFilteredInventoryItems(int practiceId, VMInventoryRequest request);
        List<VMInventoryItem> GetInventoryItems(int practiceId, int officeId = 0);
        VMInventoryItem GetInventoryItem(int practiceId, int inventoryId);
        VMInventoryItem GetInventoryItemCreate();
        List<VMInventoryItemHistory> GetInventoryItemHistory(int inventoryId);
        VMInventoryTestDevice GetInventoryTestDevice(int appointmentTestId);
        List<VMLookupItem> GetInventoryTypes(bool addAny = false);
        List<VMLookupItem> GetInventoryStatuses(bool addAny = false);
        List<VMLookupItem> GetInventoryOffices(int practiceId, bool addAny = false);
        List<VMLookupItem> SearchForNotInUse(string search, int practiceId, int officeId, int testId);
        IList<VMInventoryOverDue> GetOverDueDevices(int practiceId, int officeId, DateTime selectedDate);
        bool InsertInventoryItem(VMInventoryItem item, int userId, string ipAddress);
        bool UpdateInventoryItem(VMInventoryItem item, int userId, string ipAddress);
        bool DeleteInventoryItem(int inventoryId, int practiceId, int userId, string ipAddress);
        bool AssignItemToPatient(VMInventoryTestDevice testDevice, int userId, string ipAddress, out string message);
        bool SetItemReturned(int patientEquipmentId, int userId, string ipAddress);
        bool IsInventoryItemInUse(int inventoryId);
    }
    public class InventoryBLL : BLLBase, IInventoryBLL
    {
        #region Constructors
        public InventoryBLL(CerebrumContext context) : base(context)
        {

        }

        #endregion

        public VMInventoryMain GetInventoryMain(int practiceId, VMInventoryRequest request)
        {
            var main = new VMInventoryMain();
            main.Offices = GetInventoryOffices(practiceId, addAny: true);
            main.Statuses = GetInventoryStatuses(addAny: true);
            main.DeviceTypes = GetInventoryTypes(addAny: true);
            main.InventoryMainItems = GetFilteredInventoryItems(practiceId, request);
            main.Request = request;

            return main;
        }

        public VMInventoryItemsMain GetFilteredInventoryItems(int practiceId, VMInventoryRequest request)
        {
            var itemsMain = new VMInventoryItemsMain();
            var officeId = request.OfficeId;
            var statusId = request.StatusId;
            var deviceTypeId = request.DeviceTypeId;
            var search = request.DeviceNumber;
            var pageNumber = request.Page ?? 1;
            var maxPerPage = 20; // max per page for pagination 
            var items = new List<VMInventoryItem>();

            items = GetInventoryItems(practiceId, officeId);

            if (statusId > 0)
            {
                items = items.Where(x => x.StatusId == statusId).ToList();
            }

            if (deviceTypeId > 0)
            {
                items = items.Where(x => x.DeviceTypeId == deviceTypeId).ToList();
            }

            if (!String.IsNullOrWhiteSpace(search))
            {
                items = items.Where(x => x.DeviceNumber.ToLower().StartsWith(search.ToLower())).ToList();
            }

            int count = items.Count();

            if (count > 0)
            {
                int totalPages = Cerebrum.BLL.Utility.UtilityHelper.GetPagingTotalNum(count, maxPerPage);
                if (pageNumber > totalPages)
                {
                    pageNumber = 1;
                    request.Page = pageNumber;
                }

                if (totalPages > 0)
                {
                    int offset = ((pageNumber - 1) * maxPerPage);
                    int currentStart = (offset + maxPerPage);
                    itemsMain.Paging = totalPages >= 2 ? true : false;
                    itemsMain.PageNumber = pageNumber;
                    itemsMain.TotalPages = totalPages;
                    itemsMain.OffSet = offset;
                    itemsMain.MaxPerPage = maxPerPage;
                    itemsMain.TotalItems = count;
                    itemsMain.InventoryItems = items.Skip(offset).Take(maxPerPage).ToList();
                    itemsMain.PagingDescription = "Showing " + (offset + 1) + " to " + (currentStart < count ? "" + currentStart : "" + count) + " of " + count;

                }
            }

            return itemsMain;
        }

        public List<VMInventoryItem> GetInventoryItems(int practiceId, int officeId = 0)
        {
            var inventoryItems = new List<VMInventoryItem>();
            var dbInventoryItems = new List<SP_InventoryItem>();

            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("practiceId",practiceId)
            };

            dbInventoryItems = context.GetData<SP_InventoryItem>("[dbo].[GetInventoryItems]", parms).ToList();

            if (officeId > 0)// filter by office if necessary
            {
                dbInventoryItems = dbInventoryItems.Where(x => x.OfficeId == officeId).ToList();
            }

            foreach (var dbItem in dbInventoryItems)
            {
                var item = LoadInventoryItem(dbItem);
                inventoryItems.Add(item);
            }

            return inventoryItems;
        }

        public VMInventoryItem GetInventoryItem(int practiceId, int inventoryId)
        {
            VMInventoryItem item = null;

            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("practiceId",practiceId),
                new SqlParameter("inventoryId",inventoryId)
            };

            var dbItem = context.GetData<SP_InventoryItem>("[dbo].[GetInventoryItem]", parms).ToList().FirstOrDefault();

            if (dbItem != null)
            {
                item = LoadInventoryItem(dbItem);
            }

            return item;
        }

        public VMInventoryItem GetInventoryItemCreate()
        {
            VMInventoryItem item = new VMInventoryItem();
            return item;
        }

        public List<VMInventoryItemHistory> GetInventoryItemHistory(int inventoryId)
        {
            var itemHistory = new List<VMInventoryItemHistory>();

            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("inventoryId",inventoryId)
            };

            var dbHistory = context.GetData<SP_InventoryItemHistory>("[dbo].[GetInventoryItemHistory]", parms).ToList();

            foreach (var dbItem in dbHistory)
            {
                var item = LoadInventoryItemHistory(dbItem);
                itemHistory.Add(item);
            }

            return itemHistory;
        }

        public VMInventoryTestDevice GetInventoryTestDevice(int appointmentTestId)
        {
            VMInventoryTestDevice testDevice = null;

            var appTest = context.AppointmentTests
                .Include("Appointment")
                .Include("Test")
                .Include("Test.DeviceType")
                .Where(a => a.Id == appointmentTestId)
                .FirstOrDefault();

            if (appTest != null)
            {

                var patientId = appTest.Appointment.PatientRecordId;
                var officeId = appTest.Appointment.OfficeId;
                var demographics = context.Demographics
                .Include("PatientRecord")
                .Where(p => p.PatientRecordId == patientId)
                .OrderByDescending(o => o.Id).FirstOrDefault();

                testDevice = new VMInventoryTestDevice();

                testDevice.AppointmentDate = appTest.Appointment.appointmentTime;
                testDevice.AppointmentId = appTest.Appointment.Id;
                testDevice.OfficeId = appTest.Appointment.OfficeId;
                testDevice.AppointmentTestId = appTest.Id;
                testDevice.TestId = appTest.TestId;
                testDevice.PracticeId = demographics != null ? demographics.PatientRecord.PracticeId : 0;
                testDevice.PatientId = patientId;
                testDevice.PatientFullName = demographics != null ? demographics.lastName + ", " + demographics.firstName : "";
                testDevice.TestName = appTest.Test.testShortName;

                if (appTest.Test.DeviceTypeId != null)
                {
                    int deviceTypeId = (int)appTest.Test.DeviceTypeId;

                    //check if this patient already has a device with status In Use which is 2
                    var device = context.PatientEquipments
                        .Include("InventoryItem")
                        .Include("InventoryItem.StoreInventoryType")
                        .Where(x => x.PatientRecordId == patientId
                        && x.AppointmentTestId == appointmentTestId
                        && x.InventoryItem.statusId == 2
                         && x.InventoryItem.inventoryTypeId == deviceTypeId
                        && x.InventoryItem.officeId == officeId
                        && x.IsActive).OrderByDescending(o => o.Id)
                        .FirstOrDefault();

                    if (device != null)
                    {
                        testDevice.HasDevice = true;
                        testDevice.PatientEquipmentId = device.Id;
                        testDevice.DeviceNumber = device.InventoryItem.code;
                        testDevice.DeviceNumberId = device.InventoryItem.id; // item id
                        testDevice.OfficeTechId = device.AssignedByUserId;
                        testDevice.Notes = device.Notes;
                    }
                }
            }

            return testDevice;
        }

        public List<VMLookupItem> GetInventoryTypes(bool addAny = false)
        {
            List<VMLookupItem> list = new List<VMLookupItem>();
            List<StoreInventoryType> dbList = context.StoreInventoryTypes.ToList();


            if (addAny && dbList.Any())
            {
                list.Add(new VMLookupItem() { Value = "0", Text = "Any" });
            }

            foreach (var dbItem in dbList)
            {
                var item = new VMLookupItem();
                item.Value = dbItem.id.ToString();
                item.Text = dbItem.name;

                list.Add(item);
            }
            return list;
        }

        public List<VMLookupItem> GetInventoryStatuses(bool addAny = false)
        {
            List<VMLookupItem> list = new List<VMLookupItem>();
            List<StoreInventoryStatus> dbList = context.StoreInventoryStatuses.ToList();


            if (addAny && dbList.Any())
            {
                list.Add(new VMLookupItem() { Value = "0", Text = "Any" });
            }

            foreach (var dbItem in dbList)
            {
                //remove the status returned
                if (dbItem.name != null && dbItem.name.ToLower() != "returned")
                {
                    var item = new VMLookupItem();
                    item.Value = dbItem.id.ToString();
                    item.Text = dbItem.name;

                    list.Add(item);
                }
            }
            return list;
        }

        public List<VMLookupItem> GetInventoryOffices(int practiceId, bool addAny = false)
        {
            int activeStatus = 0;
            List<VMLookupItem> list = new List<VMLookupItem>();
            List<Office> dbList = context.Offices.Where(office => office.PracticeId == practiceId && office.status == activeStatus).ToList();

            if (addAny && dbList.Any())
            {
                list.Add(new VMLookupItem() { Value = "0", Text = "Any" });
            }

            foreach (var dbItem in dbList)
            {
                var item = new VMLookupItem();
                item.Value = dbItem.Id.ToString();
                item.Text = dbItem.name;

                list.Add(item);
            }
            return list;
        }

        public List<VMLookupItem> SearchForNotInUse(string search, int practiceId, int officeId, int testId)
        {
            var list = new List<VMLookupItem>();
            if (!String.IsNullOrWhiteSpace(search))
            {
                var dbResults = SearchInventory(search, practiceId, officeId, testId);

                dbResults = dbResults.Where(x => x.statusId == 1).ToList();

                var dbResultsIds = dbResults.Select(x => x.id).ToList();
                // check to make sure that the device is not attached to a patient
                // and it hasnt been returned as yet
                var patientEquipments = context.PatientEquipments
                        .Where(patientEquipment => dbResultsIds.Contains(patientEquipment.InventoryId)
                        && patientEquipment.DateExpectedReturn != null
                        && patientEquipment.DateReturned == null
                        && patientEquipment.IsActive)
                        .ToList();


                foreach (var dbItem in dbResults)
                {
                    if (!patientEquipments.Any(x => x.InventoryId == dbItem.id))
                    {
                        var item = new VMLookupItem();
                        item.Code = dbItem.code;
                        item.Text = dbItem.code + " [" + dbItem.StoreInventoryType.name + "]";
                        item.Value = dbItem.id.ToString();

                        list.Add(item);
                    }
                }

            }

            return list;
        }

        public IList<VMInventoryOverDue> GetOverDueDevices(int practiceId, int officeId, DateTime selectedDate)
        {
            var overDueDevices = new List<VMInventoryOverDue>();
            var returnDate = selectedDate.AddDays(1).ToShortDateString();
            var dbOverDue = new List<SP_OverDue>();
            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("practiceId",practiceId),
                new SqlParameter("officeId",officeId),
                new SqlParameter("selectedDate",returnDate)
            };

            dbOverDue = context.GetData<SP_OverDue>("[dbo].[GetInventoryOverDue]", parms).ToList();

            foreach (var dbItem in dbOverDue)
            {
                var overDue = new VMInventoryOverDue();
                overDue.PatientEquipmentId = dbItem.PatientEquipmentId;
                overDue.AppointmentTestId = dbItem.AppointmentTestId;
                overDue.DeviceNumberId = dbItem.DeviceNumberId;
                overDue.DeviceNumber = dbItem.DeviceNumber;
                overDue.DeviceTypeId = dbItem.DeviceTypeId;
                overDue.DeviceType = dbItem.DeviceType;
                overDue.PatientId = dbItem.PatientId;
                overDue.PatientFullName = dbItem.PatientFullName;
                overDue.AssignedByUserId = dbItem.AssignedByUserId;
                overDue.AssignedByUser = dbItem.AssignedByUser;
                overDue.DateStarted = dbItem.DateStarted;
                overDue.DateExpectedReturn = dbItem.DateExpectedReturn;
                overDue.OfficeId = dbItem.OfficeId;
                overDue.OfficeName = dbItem.OfficeName;
                overDue.Notes = dbItem.Notes;

                overDueDevices.Add(overDue);

            }

            return overDueDevices;
        }

        public virtual bool InsertInventoryItem(VMInventoryItem item, int userId, string ipAddress)
        {
            int saved = 0;
            bool isPracticeOffice = context.Offices.Any(office => office.PracticeId == item.PracticeId && office.Id == item.OfficeId);

            if (isPracticeOffice)
            {
                // if the item does not exist then add it
                if (!CheckIfItemExists(item.PracticeId, item.OfficeId, item.DeviceNumber))
                {
                    var currentDate = System.DateTime.Now;
                    var dbItem = new StoreInventory();
                    dbItem.practiceId = item.PracticeId;
                    dbItem.officeId = item.OfficeId;
                    dbItem.code = item.DeviceNumber;
                    dbItem.statusId = 1; // not is use
                    dbItem.inventoryTypeId = item.DeviceTypeId;
                    dbItem.userId = userId;
                    dbItem.notes = item.Notes;
                    dbItem.IsActive = true;
                    dbItem.DateCreated = currentDate;

                    context.StoreInventorys.Add(dbItem);
                    saved = context.SaveChanges(userId, ipAddress);

                    item.InventoryId = dbItem.id;

                }
            }

            return saved > 0;

        }
        public virtual bool UpdateInventoryItem(VMInventoryItem item, int userId, string ipAddress)
        {
            int saved = 0;

            bool isPracticeOffice = context.Offices.Any(office => office.PracticeId == item.PracticeId && office.Id == item.OfficeId);

            if (isPracticeOffice)
            {
                StoreInventory dbItem = (from storeInventory in context.StoreInventorys
                                         join office in context.Offices on storeInventory.officeId equals office.Id
                                         where office.PracticeId == item.PracticeId && storeInventory.id == item.InventoryId
                                         select storeInventory).FirstOrDefault();

                if (dbItem != null)
                {
                    var currentDate = System.DateTime.Now;
                    dbItem.officeId = item.OfficeId;
                    dbItem.code = item.DeviceNumber;
                    dbItem.inventoryTypeId = item.DeviceTypeId;
                    dbItem.userId = userId;
                    dbItem.notes = item.Notes;
                    dbItem.DateLastModified = currentDate;

                    // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                    context.Entry(dbItem).State = EntityState.Modified;
                    saved = context.SaveChanges(userId, ipAddress);
                }
            }
            return saved > 0;

        }
        public bool DeleteInventoryItem(int inventoryId, int practiceId, int userId, string ipAddress)
        {
            int saved = 0;
            StoreInventory dbItem = (from storeinventory in context.StoreInventorys
                                     join office in context.Offices on storeinventory.officeId equals office.Id
                                     where office.PracticeId == practiceId && storeinventory.id == inventoryId
                                     select storeinventory).FirstOrDefault();

            if (dbItem != null)
            {
                var currentDate = System.DateTime.Now;
                dbItem.IsActive = false;
                dbItem.DateLastModified = currentDate;

                // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                context.Entry(dbItem).State = EntityState.Modified;
                saved = context.SaveChanges(userId, ipAddress);
            }

            return saved > 0;

        }

        public bool AssignItemToPatient(VMInventoryTestDevice testDevice, int userId, string ipAddress, out string message)
        {
            var saved = false;
            message = "Device was not assigned.";
            if (testDevice.DeviceNumberId > 0)
            {
                int inventoryId = testDevice.DeviceNumberId;
                int appointmentTestId = testDevice.AppointmentTestId;

                if (testDevice.MarkAsReturned && testDevice.PatientEquipmentId > 0)
                {
                    saved = SetItemReturned(testDevice.PatientEquipmentId, userId, ipAddress);
                    if (saved)
                    {
                        message = "Device was succesfully returned.";
                    }
                    else
                    {
                        message = "An error occurred while returning the device";
                    }
                }
                else if (appointmentTestId > 0)
                {
                    var appTest = GetAppointmentTest(appointmentTestId);
                    if (appTest != null)
                    {
                        var test = appTest.Test;
                        int deviceDuration = test.DeviceDuration != null ? (int)test.DeviceDuration : 0;
                        int deviceTypeId = test.DeviceTypeId != null ? (int)test.DeviceTypeId : 0;

                        var dbpatEquipment = new PatientEquipment();
                        var currentDate = System.DateTime.Now;

                        dbpatEquipment.AppointmentTestId = appointmentTestId;
                        dbpatEquipment.InventoryId = inventoryId;
                        dbpatEquipment.Notes = testDevice.Notes;
                        dbpatEquipment.DateStarted = testDevice.AppointmentDate;
                        dbpatEquipment.AssignedByUserId = testDevice.OfficeTechId;
                        dbpatEquipment.PatientRecordId = testDevice.PatientId;
                        dbpatEquipment.IsActive = true;
                        dbpatEquipment.DateCreated = currentDate;

                        if (deviceDuration > 0)
                        {
                            var returnDate = CalculateExpectedReturnDate(testDevice.AppointmentDate, deviceDuration);
                            dbpatEquipment.DateExpectedReturn = returnDate;
                        }

                        if (!IsInventoryItemInUse(inventoryId))// if is not is use, then we can use it
                        {
                            var inventoryItem = context.StoreInventorys.Find(inventoryId);
                            if (inventoryItem != null)
                            {
                                inventoryItem.statusId = 2;
                                // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                                context.Entry(inventoryItem).State = EntityState.Modified;
                                context.PatientEquipments.Add(dbpatEquipment);

                                appTest.AppointmentTestStatusId = (int)AppointmentTestStatuses.Test_Started;
                                // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                                context.Entry(appTest).State = EntityState.Modified;
                                context.SaveChanges(userId, ipAddress);
                                saved = true;
                                message = "Device was succesfully assigned.";
                            }
                        }
                        else
                        {
                            message = "Device is currently in use.";
                        }

                    }
                    else
                    {
                        message = "The test could not be found.";
                    }
                }

            }

            return saved;
        }

        public bool SetItemReturned(int patientEquipmentId, int userId, string ipAddress)
        {
            var saved = false;
            var patientEquipment = context.PatientEquipments.Find(patientEquipmentId);

            if (patientEquipment != null)
            {
                var inventoryItem = context.StoreInventorys.Find(patientEquipment.InventoryId);
                if (inventoryItem != null)
                {
                    var date = System.DateTime.Now;
                    var appTest = context.AppointmentTests.Find(patientEquipment.AppointmentTestId);

                    patientEquipment.DateReturned = date;
                    patientEquipment.ReturnedByUserId = userId;
                    // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                    context.Entry(patientEquipment).State = EntityState.Modified;

                    inventoryItem.statusId = 1; // not in use
                    // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                    context.Entry(inventoryItem).State = EntityState.Modified;

                    //change the test status to completed after the device is returned
                    if (appTest != null)
                    {
                        appTest.AppointmentTestStatusId = (int)AppointmentTestStatuses.Test_Completed;

                        // EF Core: Use Microsoft.EntityFrameworkCore.EntityState instead of System.Data.Entity.EntityState
                        context.Entry(appTest).State = EntityState.Modified;

                        context.SaveChanges(userId, ipAddress);
                        saved = true;
                    }
                }

            }

            return saved;
        }

        public bool IsInventoryItemInUse(int inventoryId)
        {
            var inUse = false;
            var item = context.StoreInventorys.Find(inventoryId);

            if (item != null)
            {

                var patientEquipments = context.PatientEquipments
                       .Where(patientEquipment => patientEquipment.InventoryId == inventoryId
                       && patientEquipment.DateExpectedReturn != null
                       && patientEquipment.DateReturned == null
                       && patientEquipment.IsActive)
                       .ToList();

                if (patientEquipments.Any())
                {
                    inUse = true;
                }
                else if (item.statusId == 1 && item.IsActive)
                {
                    inUse = false;
                }
                else if (item.statusId == 2 && item.IsActive) // in use
                {
                    inUse = true;
                }

            }

            return inUse;
        }

        public virtual bool CheckIfItemExists(int practiceId, int officeId, string deviceNuber)
        {
            var exists = (from storeInventory in context.StoreInventorys
                          join office in context.Offices on storeInventory.officeId equals office.Id
                          where office.PracticeId == practiceId && office.Id == officeId
                          && storeInventory.code.ToLower() == deviceNuber.ToLower()
                          select storeInventory).Any();

            return exists;
        }

        #region Private Methods

        private List<StoreInventory> SearchInventory(string search, int practiceId, int officeId, int testId)
        {
            var dbResults = new List<StoreInventory>();
            var dbTest = context.Tests.Find(testId);

            if (dbTest != null)
            {
                var deviceTypeId = dbTest.DeviceTypeId;
                dbResults = context.StoreInventorys
                    .Include("StoreInventoryType")
                    .Where(storeInventory => storeInventory.code.ToLower().StartsWith(search.ToLower())
                    && storeInventory.officeId == officeId
                    && storeInventory.inventoryTypeId == deviceTypeId
                    && storeInventory.IsActive)
                    .ToList();

                if (officeId > 0)
                {
                    dbResults = dbResults.Where(x => x.officeId == officeId).ToList();
                }
            }

            return dbResults;
        }

        private AppointmentTest GetAppointmentTest(int appointmentTestId)
        {
            var appTest = context.AppointmentTests
                .Include("Appointment")
                .Include("Test")
                .Include("Test.DeviceType")
                .Where(a => a.Id == appointmentTestId)
                .FirstOrDefault();

            return appTest;
        }

        // calculates it in days
        private DateTime CalculateExpectedReturnDate(DateTime startDate, int duration)
        {
            DateTime expectedReturn = startDate.AddDays(duration);

            return expectedReturn;
        }

        private VMInventoryItem LoadInventoryItem(SP_InventoryItem dbItem)
        {
            var item = new VMInventoryItem();
            item.InventoryId = dbItem.InventoryId;
            item.DeviceNumber = dbItem.DeviceNumber;
            item.DeviceTypeId = dbItem.DeviceTypeId;
            item.DeviceType = dbItem.DeviceType;
            item.OfficeId = dbItem.OfficeId;
            item.OfficeName = dbItem.OfficeName;
            item.StatusId = dbItem.StatusId;
            item.StatusType = dbItem.StatusType;
            item.Notes = dbItem.Notes;
            item.HistoryCount = dbItem.HistoryCount;
            item.DateCreated = dbItem.DateCreated;

            return item;
        }

        private VMInventoryItemHistory LoadInventoryItemHistory(SP_InventoryItemHistory dbItem)
        {
            var item = new VMInventoryItemHistory();
            item.InventoryId = dbItem.InventoryId;
            item.DeviceNumber = dbItem.DeviceNumber;
            item.DeviceTypeId = dbItem.DeviceTypeId;
            item.DeviceType = dbItem.DeviceType;
            item.OfficeId = dbItem.OfficeId;
            item.OfficeName = dbItem.OfficeName;
            item.Notes = dbItem.Notes;
            item.PatientId = dbItem.PatientId;
            item.PatientFullName = dbItem.PatientFullName;
            item.PatientEquipmentId = dbItem.PatientEquipmentId;
            item.AssignedByUserId = dbItem.AssignedByUserId;
            item.AssignedByUser = dbItem.AssignedByUser;
            item.DateCreated = dbItem.DateCreated;
            item.DateExpectedReturn = dbItem.DateExpectedReturn;
            item.DateStarted = dbItem.DateStarted;
            item.DateReturned = dbItem.DateReturned;
            item.AppointmentTestId = dbItem.AppointmentTestId;
            item.TestId = dbItem.TestId;
            item.TestName = dbItem.TestName;
            item.AppointmentTestStatus = dbItem.AppointmentTestStatus;
            item.AppointmentTestStatusId = dbItem.AppointmentTestStatusId;
            item.AppointmentTestStatusColor = dbItem.AppointmentTestStatusColor;
            item.AppointmentTestStatusCSSClass = dbItem.AppointmentTestStatusCSSClass;

            return item;
        }
        #endregion

    }
}

