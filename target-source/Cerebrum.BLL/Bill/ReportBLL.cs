using System;
using System.Collections.Generic;
using System.Configuration;
// EF Core: Remove EF6 SqlServer reference
// using System.Data.Entity.SqlServer;
// EF Core: Use Microsoft.Data.SqlClient instead of System.Data.SqlClient
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Web;

using System.Xml;
using System.Data.SqlTypes;

using Newtonsoft.Json;

using Cerebrum.BLL.Schedule;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.ViewModels.Bill;
using Cerebrum.ViewModels.Requisition;
using AwareMD.Cerebrum.Shared;

namespace Cerebrum.BLL.Bill
{
    public class ReportBLL : IDisposable, ILogGeneric 
    {
        private const string DATEFORMAT = "MM/dd/yyyy";
        private const string DATEFORMAT2 = "MM/dd/yyyy HH:mm";
        private const string DATEFORMAT3 = "yyyyMMdd";
        private const string ORDER_ASC = "asc";
        private const string ORDER_DESC = "desc";

        private const string BILLINGTYPE_PROFESSIONAL = "professional";
        private const string BILLINGTYPE_TECHNICAL = "technical";
        private const string BILLINGTYPE_IHF = "ihf";

        private const string TEST_VP = "VP";

        const string FOLDER_GROUP = "Group";
        const string FOLDER_HOSPITAL = "Hospital";
        const string FOLDER_X = "X";
        const string FOLDER_RECONCILIATION = "Reconciliation";
        const string FOLDER_ERROR = "Error";
        const string FOLDER_CONFIRMATION = "Confirmation";
        const string FOLDER_ARCHIVE = "Archive";
        const string FOLDER_LOG = "Log";
        const string FOLDER_RA = "RA";
        const string FOLDER_NOTFOUND = "Notfound";

        private const string BILLSTATUS_PAID = "paid";
        private const string BILLSTATUS_NOTBILLED = "not billed";
        private const string PERSONAL_GROUPID = "0000";
        private const string All_PRACTICE_DOCTOR_ID = "*";

        const string RA_DATA_FILE_EXTENSION = ".data";

        private int userId = 0;
        private string ipAddress = string.Empty;
        private CerebrumContext context;
        private Microsoft.Extensions.Configuration.IConfiguration _configuration;
        public List<int> practiceDoctorIds { get; set; } = new List<int>();

        public ReportBLL(int userId, string ipAddress)
        {
            context = new CerebrumContext();
            this.userId = userId;
            this.ipAddress = ipAddress;
        }

        public ReportBLL(CerebrumContext context, int userId, string ipAddress)
        {
            this.context = context;
            this.userId = userId;
            this.ipAddress = ipAddress;
        }

        public ReportBLL(CerebrumContext context, Microsoft.Extensions.Configuration.IConfiguration configuration, int userId, string ipAddress)
        {
            this.context = context;
            this._configuration = configuration;
            this.userId = userId;
            this.ipAddress = ipAddress;
        }

        public List<int> GetPracticeDoctorIds(int practiceId, string userId)
        {
            List<int> practiceDoctorIds = new List<int>();

            var associatedDoctors = (from u in context.UserBillingDoctors
                                     join p in context.PracticeDoctors on u.ExternalDoctorId equals p.ExternalDoctorId
                                     where u.ApplicationUserId == userId && p.PracticeId == practiceId
                                     select new { p.Id });

            practiceDoctorIds = associatedDoctors.Select(t => t.Id).ToList();

            var practiceDoctor = (from p in context.PracticeDoctors
                                  where p.ApplicationUserId == userId && p.PracticeId == practiceId
                                  select new { p.Id }).FirstOrDefault();
            if (practiceDoctor != null)
                practiceDoctorIds.Add(practiceDoctor.Id);

            return practiceDoctorIds.Distinct().ToList();
        }

        public PerformeterResponse Performeter(int practiceId)
        {
            PerformeterResponse response = new PerformeterResponse();
            response.doctors = GetPracticeDoctors(practiceId);
            response.offices = GetOffices(practiceId);
            response.edtGroups = GetEdtGroups(practiceId);
            response.hospitals = GetHospitals();

            List<TextValueViewModel> tests = (from pt in context.PracticeTests
                                              join t in context.Tests on pt.TestId equals t.Id
                                              where pt.PracticeId == practiceId //&& pt.isActive && t.isActive
                                              orderby t.testShortName, t.order
                                              select new TextValueViewModel { text = t.testShortName, value = t.Id.ToString() }).ToList();
            response.internalTests = tests;

            //get list of reasons
            List<TextValueViewModel> reasons = (from t in context.AppointmentTypes.Where(t => t.AppointmentTypeId != null)
                                                select new TextValueViewModel { text = t.name, value = t.Id.ToString() }).ToList();
            response.appointmentTypes = reasons;

            //get list of reasons
            List<TextValueViewModel> payments = new List<TextValueViewModel>(); ;
            foreach (PaymentMethod payment in Enum.GetValues(typeof(PaymentMethod)))
            {
                payments.Add(new TextValueViewModel { text = payment.GetEnumDescription(), value = ((int)payment).ToString() });
            }
            response.payments = payments;

            //get list of appointment bill statuses
            List<TextValueViewModel> billStatuses = (from t in context.BillStatuses.AsNoTracking()
                                                     select new TextValueViewModel { text = t.name, value = t.id.ToString() }).ToList();
            response.appointmentBillStatuses = billStatuses;
            response.claimStatuses = billStatuses;

            //get list of test bill code
            List<TextValueViewModel> testBillCodes = (from t in context.BillingTestRates
                                                      orderby t.claimCode
                                                      select new TextValueViewModel { text = t.claimCode, value = t.claimCode }).Distinct().ToList();
            response.testBillCodes = testBillCodes;

            //get list of consult code
            List<TextValueViewModel> consultCodes = (from t in context.BillingConsultRates
                                                     orderby t.claimCode
                                                     select new TextValueViewModel { text = t.claimCode, value = t.claimCode }).Distinct().ToList();
            response.consultCodes = consultCodes;

            //get list of cohort
            var cohorts = (from a in context.Cohorts
                           join b in context.CohortClasses on a.CohortClassId equals b.Id
                           where b.Description.ToLower().Contains("billing") && b.PracticeId == practiceId
                           orderby a.Description
                           select new TextValueViewModel { text = a.Description, value = a.Id.ToString() }).Distinct().ToList();

            response.cohorts = cohorts;

            return response;
        }

        public EdtFileResponse EdtFile(int practiceId)
        {
            EdtFileResponse response = new EdtFileResponse();
            response.doctors = GetPracticeDoctors(practiceId);
            response.offices = GetOffices(practiceId);
            return response;
        }

        public EdtErrorResponse EdtError(int practiceId)
        {
            EdtErrorResponse response = new EdtErrorResponse();
            response.doctors = GetPracticeDoctors(practiceId);
            response.offices = GetOffices(practiceId);
            return response;
        }

        public EdtErrorSearchResponse EdtErrorSearch(bool isBillingAdmin, int practiceId, List<int> practiceDoctorIds, EdtErrorSearchRequest request)
        {
            EdtErrorSearchResponse response = new EdtErrorSearchResponse();

            var edtErrors = (from a in context.BillingEDTErrors
                             let p = (from s in context.PracticeDoctors join t in context.ExternalDoctors on s.ExternalDoctorId equals t.Id where s.IsActive && s.ExternalDoctorId == a.externalDoctorId && s.PracticeId == practiceId select new { s.Id, s.PracticeId, t.lastName, t.firstName}).FirstOrDefault()
                             let d = (from d in context.Offices where d.Id == a.officeId && d.PracticeId == practiceId select d).FirstOrDefault()
                             where (isBillingAdmin || practiceDoctorIds.Contains(p.Id)) && (a.externalDoctorId == 0 || p != null) && (a.officeId == 0 || d != null)
                             select new
                             {
                                 a.id,
                                 doctorName = (p == null ? string.Empty : (p.lastName == null ? string.Empty : p.lastName.ToUpper() + ", ") + (p.firstName ?? string.Empty)),
                                 office = d == null || d.name == null ? string.Empty : d.name,
                                 a.officeId,
                                 edtGroup = a.edtGroupId == PERSONAL_GROUPID ? string.Empty : a.edtGroupId,
                                 practiceDoctorId = p == null ? 0 : p.Id,
                                 a.fileName,
                                 a.DateDownloaded,
                                 a.status
                             });

            //filter
            if (request.filters != null && request.filters.Count > 0)
            {
                for (int i = 0; i < request.filters.Count; i++)
                {
                    string filter = request.filters[i].text.Trim().ToLower();
                    switch (request.filters[i].value.Trim().ToLower())
                    {
                        case "downloadstart":
                            DateTime downloadStart = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            edtErrors = edtErrors.Where(p => p.DateDownloaded != null && EF.Functions.DateDiffDay(p.DateDownloaded, downloadStart) <= 0);
                            break;
                        case "downloadend":
                            DateTime downloadEnd = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            edtErrors = edtErrors.Where(p => p.DateDownloaded != null && EF.Functions.DateDiffDay(p.DateDownloaded, downloadEnd) >= 0);
                            break;
                        case "practicedoctorid":
                            edtErrors = edtErrors.Where(p => ("," + filter + ",").Contains("," + p.practiceDoctorId.ToString() + ","));
                            break;
                        case "officeid":
                            edtErrors = edtErrors.Where(p => ("," + filter + ",").Contains("," + p.officeId.ToString() + ","));
                            break;
                    }
                }
            }
            if (request.ErrorsNotFixed)
                edtErrors = edtErrors.Where(p => p.status == 0);

            if (request.totalRowRequest)
            {
                response.totalRow = edtErrors.Count();
            }

            //sorting
            if (string.IsNullOrEmpty(request.sortByColumn) || string.IsNullOrEmpty(request.sortByOrder))
            {
                edtErrors = edtErrors.OrderByDescending(p => p.DateDownloaded);
            }
            else
            {
                switch (request.sortByColumn.Trim().ToLower())
                {
                    case "doctor":
                        if (request.sortByOrder == ORDER_ASC)
                        {
                            edtErrors = edtErrors.OrderBy(p => p.doctorName);
                        }
                        else
                        {
                            edtErrors = edtErrors.OrderByDescending(p => p.doctorName);
                        }
                        break;
                    case "office":
                        if (request.sortByOrder == ORDER_ASC)
                        {
                            edtErrors = edtErrors.OrderBy(p => p.office);
                        }
                        else
                        {
                            edtErrors = edtErrors.OrderByDescending(p => p.office);
                        }
                        break;
                    case "edtgroup":
                        if (request.sortByOrder == ORDER_ASC)
                        {
                            edtErrors = edtErrors.OrderBy(p => p.edtGroup);
                        }
                        else
                        {
                            edtErrors = edtErrors.OrderByDescending(p => p.edtGroup);
                        }
                        break;
                    case "file":
                        if (request.sortByOrder == ORDER_ASC)
                        {
                            edtErrors = edtErrors.OrderBy(p => p.fileName);
                        }
                        else
                        {
                            edtErrors = edtErrors.OrderByDescending(p => p.fileName);
                        }
                        break;
                    case "status":
                        if (request.sortByOrder == ORDER_ASC)
                        {
                            edtErrors = edtErrors.OrderBy(p => p.status);
                        }
                        else
                        {
                            edtErrors = edtErrors.OrderByDescending(p => p.status);
                        }
                        break;
                    default:
                        if (request.sortByOrder == ORDER_ASC)
                        {
                            edtErrors = edtErrors.OrderBy(p => p.DateDownloaded);
                        }
                        else
                        {
                            edtErrors = edtErrors.OrderByDescending(p => p.DateDownloaded);
                        }
                        break;
                }
            }

            var edtErrorList = edtErrors.Skip(request.rowStart).Take(request.rowCount).ToList()
                             .Select(a => new EdtErrorData { id = a.id, doctor = a.doctorName, office = a.office, edtGroup = a.edtGroup, fileName = Path.GetFileName(a.fileName), dateDownloaded = a.DateDownloaded.ToString(DATEFORMAT, CultureInfo.InvariantCulture), status = a.status == 0 ? "Not Fixed" : "Fixed" }).ToList();
            response.edtErrors = edtErrorList;

            return response;
        }

        public string ChangeEdtErrorStatus(int edtErrorId)
        {
            string response = string.Empty;

            var edtError = context.BillingEDTErrors.Where(a => a.id == edtErrorId).FirstOrDefault();
            if (edtError == null)
                response = string.Format("Cannot find the Claim Error (id={0})", edtErrorId);
            else
            {
                edtError.status = 1;
                context.SaveChanges(userId, ipAddress);
            }

            return response;
        }

        public EdtErrorShowResponse EdtErrorDetail(int edtErrorId, RemittancePermission permission)
        {
            EdtErrorShowResponse response = new EdtErrorShowResponse();

            var edtError = context.BillingEDTErrors.Where(a => a.id == edtErrorId).FirstOrDefault();
            if (edtError == null)
            {
                response.errorMessage = $"Cannot find the Claim Error (id={edtErrorId})";
                return response;
            }

            int edtErrorExternalDoctorId = edtError.externalDoctorId;
            if (edtError.officeId != 0)
                edtErrorExternalDoctorId = 0;
            if (!CheckPermission(0, edtErrorExternalDoctorId, edtError.edtGroupId, permission))
            {
                response.errorMessage = $"No permission to view this Claim Error file (id: {edtErrorId})";
                return response;
            }

            response.officeId = edtError.officeId;
            response.errorFileName = Path.GetFileName(edtError.fileName);
            if (File.Exists(edtError.fileName))
                response.doctorEdtErrorData = GetDoctorEdtErrorData(permission.practiceId, edtError.fileName);
            else
                response.errorMessage = string.Format("Cannot find the Claim Error File ({0})", response.errorFileName);


            return response;
        }

        public EdtFileSearchResponse EdtFileSearch(bool isBillingAdmin, int practiceId, List<int> practiceDoctorIds, EdtFileSearchRequest request)
        {
            EdtFileSearchResponse response = new EdtFileSearchResponse();
            List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("isBillingAdmin", isBillingAdmin ? 1 : 0),
                    new SqlParameter("practiceId", practiceId),
                    new SqlParameter("practiceDoctorIds", string.Join(",", practiceDoctorIds)),
                    new SqlParameter("rowStart", request.rowStart),
                    new SqlParameter("rowCount", request.rowCount),
                    new SqlParameter("totalRowRequest", request.totalRowRequest),
                    new SqlParameter("sortByColumn", request.sortByColumn ?? string.Empty),
                    new SqlParameter("sortByOrder", request.sortByOrder ?? string.Empty),
                    new SqlParameter("filterNotSent", request.checkBoxNotSent ? 1 : 0),
                    new SqlParameter("filterNotConfirmed", request.checkBoxNotConfirmed ? 1 : 0),
                    new SqlParameter("filterError", request.checkBoxError ? 1 : 0),
                    //new SqlParameter("filterServiceStart", GetDateFilter("servicestart", request.filters)),
                    //new SqlParameter("filterServiceEnd", GetDateFilter("serviceend", request.filters)),
                    new SqlParameter("filterSentDateStart", GetDateFilter("sentdatestart", request.filters)),
                    new SqlParameter("filterSentDateEnd", GetDateFilter("sentdateend", request.filters)),
                    new SqlParameter("filterConfirmedStart", GetDateFilter("confirmedstart", request.filters)),
                    new SqlParameter("filterConfirmedEnd", GetDateFilter("confirmedend", request.filters)),
                    new SqlParameter("filterConfirmdlStart", GetDateFilter("confirmdlstart", request.filters)),
                    new SqlParameter("filterConfirmdlEnd", GetDateFilter("confirmdlend", request.filters)),
                    new SqlParameter("filterPracticeDoctorIds", GetFilter("practicedoctorid", request.filters)),
                    new SqlParameter("filterOfficeIds", GetFilter("officeid", request.filters))
                };
            SqlParameter totalRow = new SqlParameter("totalRow", System.Data.SqlDbType.Int);
            totalRow.Direction = System.Data.ParameterDirection.ReturnValue;
            parameters.Add(totalRow);

            var edtFiles = context.GetData<EdtFileData>("dbo.GetBillingEdtFileSearch", parameters, 300).ToList();
            response.edtFiles = edtFiles;
            if (request.totalRowRequest)
                response.totalRow = (int)totalRow.Value;

            return response;
        }

        public TextFileResponse EdtFileContent(int edtFileId, RemittancePermission permission)
        {
            TextFileResponse response = new TextFileResponse();

            string errorMessage = string.Empty;
            string fileName = GetEdtFile(edtFileId, 0, permission, out errorMessage);
            response.errorMessage = errorMessage;
            if (!string.IsNullOrWhiteSpace(response.errorMessage))
                return response;

            response.fileName = Path.GetFileName(fileName);
            response.content = File.ReadAllText(fileName);
            return response;
        }

        public TextFileResponse ConfirmedFileContent(int edtFileId, RemittancePermission permission)
        {
            TextFileResponse response = new TextFileResponse();

            string errorMessage = string.Empty;
            string fileName = GetEdtFile(edtFileId, 1, permission, out errorMessage);
            response.errorMessage = errorMessage;
            if (!string.IsNullOrWhiteSpace(response.errorMessage))
                return response;

            response.fileName = Path.GetFileName(fileName);
            response.content = File.ReadAllText(fileName);
            return response;
        }

        public RemittanceFileSearchResponse RemittanceSearch(bool isBillingAdmin, int practiceId, RemittancePermission permission, RemittanceFileSearchRequest request)
        {
            RemittanceFileSearchResponse response = new RemittanceFileSearchResponse();

            if (request.billingType == 1 && !permission.isRAGroupBillingAdmin)
            {
                response.errorMessage = "You don't have permission to view group RA files. Please Contact Administrator! ";
                return response;
            }

            //if (request.billingType == 0 && !permission.isRASoloBillingAdmin)
            //{ 
            //    if (!permission.practiceDoctorIds.Contains(request.practiceDoctorId))
            //    {
            //        response.errorMessage = $"You don't have permission to view doctor (practice doctor id: {request.practiceDoctorId}) RA files. Please Contact Administrator! ";
            //        return response;
            //    }
            //}
            List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("isRASoloBillingAdmin", permission.isRASoloBillingAdmin ? 1 : 0),
                    new SqlParameter("isRAGroupBillingAdmin", permission.isRAGroupBillingAdmin ? 1 : 0),
                    new SqlParameter("practiceId", practiceId),
                    new SqlParameter("practiceDoctorIds", string.Join(",", practiceDoctorIds)),
                    new SqlParameter("billingType", request.billingType),
                    new SqlParameter("billingFileType", (int)BillingFileType.Reconciliation),
                    new SqlParameter("rowStart", request.rowStart),
                    new SqlParameter("rowCount", request.rowCount),
                    new SqlParameter("totalRowRequest", request.totalRowRequest),
                    new SqlParameter("sortByColumn", request.sortByColumn ?? string.Empty),
                    new SqlParameter("sortByOrder", request.sortByOrder ?? string.Empty),
                    new SqlParameter("filterDownloadStart", GetDateFilter("downloadstart", request.filters)),
                    new SqlParameter("filterDownloadEnd", GetDateFilter("downloadend", request.filters)),
                };
            SqlParameter totalRow = new SqlParameter("totalRow", System.Data.SqlDbType.Int);
            totalRow.Direction = System.Data.ParameterDirection.ReturnValue;
            parameters.Add(totalRow);

            var files = context.GetData<RemittanceFileData>("dbo.GetBillingRemittanceSearch", parameters).ToList();
            if (files.Count > 0)
            {
                files.ForEach(x => { x.fileName = Path.GetFileName(x.fileFullName); x.date = x.dateEntered.ToString(DATEFORMAT); });
            }
            response.files = files;
            if (request.totalRowRequest)
                response.totalRow = (int)totalRow.Value;

            return response;
        }

        public RemittanceDetailResponse RemittanceDetail(int billingFileId, RemittancePermission permission)
        {
            RemittanceDetailResponse response = new RemittanceDetailResponse();

            string errorMessage = string.Empty;
            string fileName = GetRAFile(billingFileId, permission, out errorMessage);
            response.errorMessage = errorMessage;
            if (!string.IsNullOrWhiteSpace(response.errorMessage))
                return response;

            response = GetRemittanceAdviceData(fileName, permission);
            return response;
        }

        public TextFileResponse RemittanceOriginalContent(int billingFileId, RemittancePermission permission)
        {
            TextFileResponse response = new TextFileResponse();

            string errorMessage = string.Empty;
            string fileName = GetRAFile(billingFileId, permission, out errorMessage);
            response.errorMessage = errorMessage;
            if (!string.IsNullOrWhiteSpace(response.errorMessage))
                return response;

            response.fileName = Path.GetFileName(fileName);
            response.content = File.ReadAllText(fileName);
            return response;
        }

        public ClaimDetailResponse ClaimDetail(RemittancePermission permission, int appointmentId, int admissionActionId)
        {
            ClaimDetailResponse response = new ClaimDetailResponse();

            var claims = (from d in context.BillDetails
                          join p in context.PracticeDoctors on d.practiceDoctorId equals p.Id
                          join e in context.ExternalDoctors on p.ExternalDoctorId equals e.Id
                          let r = (from s in context.ExternalDoctors where s.Id == d.referralDoctorId select s).FirstOrDefault()
                          let o = (from t in context.Offices where t.Id == d.officeId select t).FirstOrDefault()
                          let h = context.BillingEDTFiles.Where(t => t.id == d.edtFileId).FirstOrDefault()
                          where p.PracticeId == permission.practiceId && d.appointmentId == appointmentId && d.hdAdmissionActionId == admissionActionId && ((permission.isRAGroupBillingAdmin || permission.isRASoloBillingAdmin) || permission.practiceDoctorIds.Contains(p.Id.ToString()))
                          select new
                          {
                              id = d.id,
                              practiceDoctorId = p.Id,
                              docor = (e.lastName == null || e.lastName == "" ? string.Empty : e.lastName + ", ") + (e.firstName ?? string.Empty),
                              referralDoctor = (r.lastName == null || r.lastName == "" ? string.Empty : r.lastName + ", ") + (r.firstName ?? string.Empty),
                              officeId = d.officeId,
                              office = o == null ? string.Empty : o.name,
                              d.edtGroupId,
                              d.serviceDate,
                              serviceCode = d.serviceCode,
                              diagnoseCode = d.diagnoseCode,
                              fee = d.fee,
                              numberOfServices = d.numberOfServices,
                              billStatusId = d.billStatusId ?? -1,
                              MOHID = d.MOHId,
                              errorCode = d.errorCode,
                              sendDate = h == null ? null : h.DateSent,
                              ohipPayDate = d.ohipPayDate,
                              reconciledDate = d.reconciledDate,
                              d.payment,
                              d.specialty,
                              d.manualReview
                          }).ToList()
                          .Select(d => new ClaimData
                          {
                              id = d.id,
                              practiceDoctorId = d.practiceDoctorId,
                              doctor = d.docor,
                              referralDoctor = d.referralDoctor,
                              officeId = d.officeId,
                              office = d.office,
                              edtGroup = d.edtGroupId ?? string.Empty,
                              serviceDate = d.serviceDate.ToString(DATEFORMAT, CultureInfo.InvariantCulture),
                              serviceCode = d.serviceCode,
                              diagnoseCode = d.diagnoseCode ?? string.Empty,
                              fee = d.fee.ToString(),
                              feeCurrency = string.Format("{0:C}", (float)d.fee / 100),
                              numberOfServices = d.numberOfServices,
                              billStatusId = d.billStatusId,
                              payment = (int)d.payment,
                              speciality = d.specialty.ToString(),
                              manualReview = d.manualReview,
                              MOHID = d.MOHID ?? string.Empty,
                              errorCode = d.errorCode,
                              dateSent = d.sendDate == null ? string.Empty : d.sendDate.Value.ToString(DATEFORMAT, CultureInfo.InvariantCulture),
                              datePaid = d.ohipPayDate == null ? string.Empty : d.ohipPayDate.Value.ToString(DATEFORMAT, CultureInfo.InvariantCulture),
                              dateReconciled = d.reconciledDate == null ? string.Empty : d.reconciledDate.Value.ToString(DATEFORMAT, CultureInfo.InvariantCulture)
                          }).ToList();
            response.claims = claims;

            if (appointmentId > 0)
            {
                var appointment = (from a in context.Appointments
                                   join d in context.Demographics on a.PatientRecordId equals d.PatientRecordId
                                   let h = context.HealthCards.Where(t => t.DemographicId == d.Id).FirstOrDefault()
                                   where a.Id == appointmentId
                                   select new { a.billStatusId, a.appointmentTime, patientRecordId = d.PatientRecordId, d.firstName, d.lastName, healthCard = h }).FirstOrDefault();
                if (appointment != null)
                {
                    response.patientName = (appointment.firstName ?? string.Empty) + " " + (appointment.lastName ?? string.Empty);
                    response.patientRecordId = appointment.patientRecordId;
                    response.healthCardNumber = appointment.healthCard == null ? string.Empty : appointment.healthCard.number;
                    response.appointmentDate = appointment.appointmentTime.ToString(DATEFORMAT, CultureInfo.InvariantCulture);
                    response.billStatusId = appointment.billStatusId ?? -1;
                }
            }
            else
            {
                var admission = (from a in context.HDAdmissions
                                 join b in context.HDAdmissionActions on a.Id equals b.AdmissionId
                                   join d in context.Demographics on a.PatientRecordId equals d.PatientRecordId
                                   let h = context.HealthCards.Where(t => t.DemographicId == d.Id).FirstOrDefault()
                                   where b.Id == admissionActionId
                                 select new { b.BillStatusId, a.DateAdmitted, patientRecordId = d.PatientRecordId, d.firstName, d.lastName, healthCard = h }).FirstOrDefault();
                if (admission != null)
                {
                    response.patientName = (admission.firstName ?? string.Empty) + " " + (admission.lastName ?? string.Empty);
                    response.patientRecordId = admission.patientRecordId;
                    response.healthCardNumber = admission.healthCard == null ? string.Empty : admission.healthCard.number;
                    response.admissionDate = admission.DateAdmitted.ToString(DATEFORMAT, CultureInfo.InvariantCulture);
                    response.billStatusId = admission.BillStatusId;
                }
            }

            response.billStatus = (from a in context.BillStatuses.AsNoTracking() select new TextValueViewModel { text = a.name, value = a.id.ToString() }).ToList();
            List<TextValueViewModel> paymentMethods = new List<TextValueViewModel>();
            foreach (PaymentMethod paymentMethod in Enum.GetValues(typeof(PaymentMethod)))
                paymentMethods.Add(new TextValueViewModel { text = paymentMethod.GetEnumDescription(), value = ((int)paymentMethod).ToString() });
            response.paymentMethods = paymentMethods;

            response.manualReviews = new List<TextValueViewModel> { new TextValueViewModel { text = " ", value = " " }, new TextValueViewModel { text = "Y", value = "Y" } };

            return response;
        }

        public string ChangeClaimBillStatus(ClaimBillStatusRequest request)
        {
            string response = string.Empty;

            int id;
            int id2;
            string[] ids;
            foreach (var billStatus in request.billStatusList)
            {
                ids = billStatus.text.Split(':');
                id = int.Parse(ids[0]);
                id2 = int.Parse(ids[1]);
                switch (billStatus.value.ToLower())
                {
                    case "appointmentbillstatus":       //appointmentId:appointmentBillStatusId
                        var appointment = context.Appointments.Where(a => a.Id == id).FirstOrDefault();
                        if (appointment != null)
                            appointment.billStatusId = id2;
                        break;
                    case "claimbillstatus":     //billDetailId:appointmentBillStatusId
                        var billDetail = context.BillDetails.Where(a => a.id == id).FirstOrDefault();
                        if (billDetail != null)
                            billDetail.billStatusId = id2;
                        break;
                }
            }
            context.SaveChanges(userId, ipAddress);

            return response;
        }

        public PerformeterSearchResponse PerformeterSearch(bool isBillingAdmin, int practiceId, List<int> practiceDoctorIds, PerformeterSearchRequest request)
        {
            PerformeterSearchResponse response = new PerformeterSearchResponse();

            try
            {
                MemoryStream memoryStream = new MemoryStream();
                XmlWriter writer = XmlWriter.Create(memoryStream);
                writer.WriteStartElement("BillingPerformeter");
                writer.WriteElementString("isBillingAdmin", isBillingAdmin ? "1" : "0");
                writer.WriteElementString("practiceId", practiceId.ToString());
                writer.WriteElementString("practiceDoctorIds", string.Join(",", practiceDoctorIds));
                writer.WriteElementString("billingType", request.billingType == 0 ? "0" : "1");
                writer.WriteElementString("columnDoctor", request.columnDoctor ? "1" : "0");
                writer.WriteElementString("columnOffice", request.columnOffice ? "1" : "0");
                writer.WriteElementString("columnEdtGroup", request.columnEdtGroup ? "1" : "0");
                writer.WriteElementString("columnHospital", request.columnHospital ? "1" : "0");
                writer.WriteElementString("columnPatient", request.columnPatient ? "1" : "0");
                writer.WriteElementString("columnAppointment", request.columnAppointment ? "1" : "0");
                writer.WriteElementString("columnAdmission", request.columnAdmission ? "1" : "0");
                writer.WriteElementString("columnServiceCode", request.columnServiceCode ? "1" : "0");
                writer.WriteElementString("columnReferralDoctor", request.columnReferralDoctor ? "1" : "0");
                writer.WriteElementString("columnFamilyDoctor", request.columnFamilyDoctor ? "1" : "0");
                writer.WriteElementString("columnAppointmentBillStatus", request.columnAppointmentBillStatus ? "1" : "0");
                writer.WriteElementString("columnAdmissionBillStatus", request.columnAdmissionBillStatus ? "1" : "0");
                writer.WriteElementString("columnClaimStatus", request.columnClaimStatus ? "1" : "0");
                writer.WriteElementString("columnTest", request.columnTest ? "1" : "0");
                writer.WriteElementString("columnCohort", request.columnCohort ? "1" : "0");
                writer.WriteElementString("rowStart", request.rowStart.ToString());
                writer.WriteElementString("rowCount", request.rowCount.ToString());
                writer.WriteElementString("totalRowRequest", request.totalRowRequest ? "1" : "0");
                writer.WriteElementString("sortByColumn", request.sortByColumn);
                writer.WriteElementString("sortByOrder", request.sortByOrder);
                writer.WriteElementString("filterPracticeDoctorIds", GetFilter("practicedoctorid", request.filters));
                writer.WriteElementString("filterOfficeIds", GetFilter("officeid", request.filters));
                writer.WriteElementString("filterEdtGroupIds", GetFilter("edtGroupId", request.filters));
                writer.WriteElementString("filterHospitalIds", GetFilter("hospitalId", request.filters));
                writer.WriteElementString("filterTestIds", GetFilter("testId", request.filters));
                writer.WriteElementString("filterPatientName", GetFilter("patientname", request.filters));
                writer.WriteElementString("filterFamilyDoctorName", GetFilter("familydoctorname", request.filters));
                writer.WriteElementString("filterReferralDoctorName", GetFilter("referraldoctorname", request.filters));
                writer.WriteElementString("filterServiceStart", GetDateFilter("servicestart", request.filters));
                writer.WriteElementString("filterServiceEnd", GetDateFilter("serviceend", request.filters));
                writer.WriteElementString("filterReconcileStart", GetDateFilter("reconcilestart", request.filters));
                writer.WriteElementString("filterReconcileEnd", GetDateFilter("reconcileend", request.filters));
                writer.WriteElementString("filterSendDateStart", GetDateFilter("senddatestart", request.filters));
                writer.WriteElementString("filterSendDateEnd", GetDateFilter("senddateend", request.filters));
                writer.WriteElementString("filterAppointmentTypeIds", GetFilter("appointmenttypeid", request.filters));
                writer.WriteElementString("filterPaymentIds", GetFilter("paymentid", request.filters));
                writer.WriteElementString("filterAppointmentBillStatusIds", GetFilter("appointmentbillstatusid", request.filters));
                writer.WriteElementString("filterAdmissionBillStatusIds", GetFilter("admissionbillstatusid", request.filters));
                writer.WriteElementString("filterClaimStatusIds", GetFilter("claimstatusid", request.filters));
                writer.WriteElementString("filterTestBillCodeIds", GetFilter("testbillcodeid", request.filters));
                writer.WriteElementString("filterConsultCodeIds", GetFilter("consultcodeid", request.filters));
                writer.WriteElementString("filterCohortIds", GetFilter("cohortid", request.filters));
                writer.WriteEndElement();
                writer.Flush();
                SqlXml xmlParameter = new SqlXml(memoryStream);
                List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("xmlParameter", xmlParameter)
                };
                SqlParameter totalRow = new SqlParameter("totalRow", System.Data.SqlDbType.Int);
                totalRow.Direction = System.Data.ParameterDirection.ReturnValue;
                parameters.Add(totalRow);

                var dbData = context.GetData<SP_PerformeterData>("dbo.GetBillingPerformeter", parameters, 300).ToList();
                if (request.totalRowRequest)
                    response.totalRow = (int)totalRow.Value;

                foreach (var spPerformeter in dbData)
                {
                    var performeter = new PerformeterData();
                    performeter.quantity = spPerformeter.quantity;
                    performeter.amount = string.Format("{0:C}", (float)spPerformeter.amount / 100);
                    performeter.amountProfessional = string.Format("{0:C}", (float)spPerformeter.amountProfessional / 100);
                    performeter.amountTechnical = string.Format("{0:C}", (float)spPerformeter.amountTechnical / 100);
                    performeter.amountPaid = string.Format("{0:C}", (float)spPerformeter.amountPaid / 100);
                    performeter.amountPaidProfessional = string.Format("{0:C}", (float)spPerformeter.amountPaidProfessional / 100);
                    performeter.amountPaidTechnical = string.Format("{0:C}", (float)spPerformeter.amountPaidTechnical / 100);
                    performeter.amountDelta = string.Format("{0:C}", ((float)(spPerformeter.amount - spPerformeter.amountPaid)) / 100);
                    if (request.columnDoctor)
                        performeter.doctorName = spPerformeter.doctorName;
                    if (request.columnOffice && request.billingType == 1)
                        performeter.office = spPerformeter.office;
                    if (request.columnEdtGroup && request.billingType == 1)
                        performeter.edtGroup = spPerformeter.edtGroup;
                    if (request.columnHospital && request.billingType == 0)
                        performeter.hospital = spPerformeter.hospital;
                    if (request.columnPatient)
                    {
                        performeter.patientRecordId = spPerformeter.patientRecordId.ToString();
                        performeter.patientName = spPerformeter.patientName;
                    }
                    if (request.columnAppointment && request.billingType == 1)
                    {
                        performeter.appointmentTime = spPerformeter.appointmentTime.ToString(DATEFORMAT, CultureInfo.InvariantCulture);
                        performeter.officeId = spPerformeter.officeId.ToString();
                    }
                    if (request.columnAdmission && request.billingType == 0)
                    {
                        performeter.admissionDate = spPerformeter.admissionDate.ToString(DATEFORMAT, CultureInfo.InvariantCulture);
                        performeter.practiceDoctorId = spPerformeter.practiceDoctorId;
                    }
                    if (request.columnServiceCode)
                        performeter.serviceCode = spPerformeter.serviceCode;
                    if (request.columnReferralDoctor)
                        performeter.referralDoctorName = spPerformeter.referralDoctorName;
                    if (request.columnFamilyDoctor)
                        performeter.familyDoctorName = spPerformeter.familyDoctorName;
                    if (request.columnAppointmentBillStatus && request.billingType == 1)
                        performeter.appointmentBillStatus = spPerformeter.appointmentBillStatus;
                    if (request.columnAdmissionBillStatus && request.billingType == 0)
                        performeter.admissionBillStatus = spPerformeter.admissionBillStatus;
                    if (request.columnClaimStatus)
                        performeter.claimBillStatus = spPerformeter.claimBillStatus;
                    if (request.columnTest && request.billingType == 1)
                        performeter.test = spPerformeter.test;
                    if (request.columnCohort)
                        performeter.cohort = spPerformeter.cohort;

                    response.performeters.Add(performeter);
                }

                if (request.columnDoctor)
                    response.columns.Add(new PerformeterColumn { textName = "Doctor", valueName = "doctorName" });
                if (request.columnOffice && request.billingType == 1)
                    response.columns.Add(new PerformeterColumn { textName = "Office", valueName = "office" });
                if (request.columnEdtGroup && request.billingType == 1)
                    response.columns.Add(new PerformeterColumn { textName = "Billing Group", valueName = "edtGroup" });
                if (request.columnHospital && request.billingType == 0)
                    response.columns.Add(new PerformeterColumn { textName = "Hospital", valueName = "hospital" });
                if (request.columnPatient)
                {
                    response.columns.Add(new PerformeterColumn { textName = "PatientRecordId", valueName = "patientRecordId", hidden = true });
                    response.columns.Add(new PerformeterColumn { textName = "Patient", valueName = "patientName" });
                }
                if (request.columnAppointment && request.billingType == 1)
                {
                    response.columns.Add(new PerformeterColumn { textName = "Appointment", valueName = "appointmentTime" });
                    response.columns.Add(new PerformeterColumn { textName = "OfficeId", valueName = "officeId", hidden = true });
                }
                if (request.columnAdmission && request.billingType == 0)
                {
                    response.columns.Add(new PerformeterColumn { textName = "Admission", valueName = "admissionDate" });
                    response.columns.Add(new PerformeterColumn { textName = "practiceDoctorId", valueName = "practiceDoctorId", hidden = true });
                }
                if (request.columnServiceCode)
                    response.columns.Add(new PerformeterColumn { textName = "B.Code", valueName = "serviceCode" });
                if (request.columnReferralDoctor)
                    response.columns.Add(new PerformeterColumn { textName = "R.Doctor", valueName = "referralDoctorName" });
                if (request.columnFamilyDoctor)
                    response.columns.Add(new PerformeterColumn { textName = "F.Doctor", valueName = "familyDoctorName" });
                if (request.columnAppointmentBillStatus && request.billingType == 1)
                    response.columns.Add(new PerformeterColumn { textName = "Appointment Bill Status", valueName = "appointmentBillStatus" });
                if (request.columnAdmissionBillStatus && request.billingType == 0)
                    response.columns.Add(new PerformeterColumn { textName = "Admission Status", valueName = "admissionBillStatus" });
                if (request.columnClaimStatus)
                    response.columns.Add(new PerformeterColumn { textName = "Claim Status", valueName = "claimBillStatus" });
                if (request.columnTest && request.billingType == 1)
                    response.columns.Add(new PerformeterColumn { textName = "Test", valueName = "test" });
                if (request.columnCohort)
                    response.columns.Add(new PerformeterColumn { textName = "Cohort", valueName = "cohort" });

                response.columns.Add(new PerformeterColumn { textName = "Quantity", valueName = "quantity" });
                response.columns.Add(new PerformeterColumn { textName = "Amount Claimed", valueName = "amount" });
                response.columns.Add(new PerformeterColumn { textName = "Professional Claimed", valueName = "amountProfessional" });
                response.columns.Add(new PerformeterColumn { textName = "Technical Claimed", valueName = "amountTechnical" });
                response.columns.Add(new PerformeterColumn { textName = "Amount Paid", valueName = "amountPaid" });
                response.columns.Add(new PerformeterColumn { textName = "Professional Paid", valueName = "amountPaidProfessional" });
                response.columns.Add(new PerformeterColumn { textName = "Technical Paid", valueName = "amountPaidTechnical" });
                response.columns.Add(new PerformeterColumn { textName = "Total Amount Delta", valueName = "amountDelta" });
            }
            catch (Exception ex)
            {
                LogException(new StackTrace().GetFrame(0).GetMethod().Name, ex, "GerPerformeterData");
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return response;
        }

        public StringWriter PerformeterToExcel(bool isBillingAdmin, int practiceId, List<int> practiceDoctorIds, PerformeterSearchRequest request)
        {
            PerformeterSearchResponse response = PerformeterSearch(isBillingAdmin, practiceId, practiceDoctorIds, request);
            StringWriter sw = new StringWriter();
            if (string.IsNullOrEmpty(response.errorMessage))
            {
                string data = string.Empty;
                foreach (var column in response.columns)
                {
                    if (!column.hidden)
                    {
                        data += "," + column.textName;
                    }
                }
                sw.WriteLine(data.Trim(','));

                foreach (var performeter in response.performeters)
                {
                    data = string.Empty;
                    foreach (var column in response.columns)
                    {
                        if (!column.hidden)
                        {
                            PropertyInfo property = typeof(PerformeterData).GetProperty(column.valueName);
                            object value = property.GetValue(performeter, null);
                            if (!string.IsNullOrEmpty(data))
                                data += ",";
                            if (value != null)
                                data += value.ToString().Replace("$", string.Empty).Replace(",", string.Empty);
                        }
                    }
                    sw.WriteLine(data);
                }
            }
            else
                sw.WriteLine(response.errorMessage);

            return sw;
        }

        public ToBeBilledResponse ToBeBilled(int practiceId)
        {
            ToBeBilledResponse response = new ToBeBilledResponse();
            response.doctors = GetPracticeDoctors(practiceId);
            response.offices = GetOffices(practiceId);
            response.testStatuses = context.AppointmentTestStatus.Select(a => new TextValueViewModel { text = a.Status, value = a.Id.ToString() }).ToList();
            return response;
        }

        public ToBeBilledSearchResponse ToBeBilledSearch(bool isBillingAdmin, int practiceId, List<int> practiceDoctorIds, ToBeBilledSearchRequest request)
        {
            ToBeBilledSearchResponse response = new ToBeBilledSearchResponse();

            List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("isBillingAdmin", isBillingAdmin ? 1 : 0),
                    new SqlParameter("practiceId", practiceId),
                    new SqlParameter("practiceDoctorIds", string.Join(",", practiceDoctorIds)),
                    new SqlParameter("billingType", request.billingType),
                    new SqlParameter("rowStart", request.rowStart),
                    new SqlParameter("rowCount", request.rowCount),
                    new SqlParameter("totalRowRequest", request.totalRowRequest),
                    new SqlParameter("sortByColumn", request.sortByColumn ?? string.Empty),
                    new SqlParameter("sortByOrder", request.sortByOrder ?? string.Empty),
                    new SqlParameter("columnPatient", request.columnPatient ? 1 : 0),
                    new SqlParameter("filterArrived", GetBoolFilter("filterarrived", request.filters)),
                    new SqlParameter("filterNotArrived", GetBoolFilter("filternotarrived", request.filters)),
                    new SqlParameter("filterPaymentMethods", GetPaymentMethodFilter(request.filterPaymentMethods)),
                    new SqlParameter("filterNotFinished", GetBoolFilter("filternotfinished", request.filters)),
                    new SqlParameter("filterBilled", GetBoolFilter("filterbilled", request.filters)),
                    new SqlParameter("filterNotBilled", GetBoolFilter("filternotbilled", request.filters)),
                    new SqlParameter("filterPatientName", GetFilter("filterpatientname", request.filters)),
                    new SqlParameter("filterAppointmentStart", GetDateFilter("filterappointmentstart", request.filters)),
                    new SqlParameter("filterAppointmentEnd", GetDateFilter("filterappointmentend", request.filters)),
                    new SqlParameter("filterFamilyDoctorName", GetFilter("filterfamilydoctorname", request.filters)),
                    new SqlParameter("filterReferralDoctorName", GetFilter("filterreferraldoctorname", request.filters)),
                    new SqlParameter("filterPracticeDoctorIds", GetFilter("filterpracticedoctorids", request.filters)),
                    new SqlParameter("filterOfficeIds", GetFilter("filterofficeids", request.filters))                    
                };
            SqlParameter totalRow = new SqlParameter("totalRow", System.Data.SqlDbType.Int);
            totalRow.Direction = System.Data.ParameterDirection.ReturnValue;
            parameters.Add(totalRow);

            var data = context.GetData<ToBeBilledData>("dbo.GetBillingToBeBilledSearch", parameters, 300).ToList();
            response.data = data;
            if (request.totalRowRequest)
                response.totalRow = (int)totalRow.Value;

            if (request.columnPatient)
            {
                response.columns.Add(new ToBeBilledColumn { textName = "PatientRecordId", valueName = "patientRecordId", hidden = true });
                response.columns.Add(new ToBeBilledColumn { textName = "Patient", valueName = "patientName" });
            }

            if (request.billingType == 1)
            {
                response.columns.Add(new ToBeBilledColumn { textName = "AppointmentId", valueName = "appointmentId", hidden = true });
                response.columns.Add(new ToBeBilledColumn { textName = "Appointment", valueName = "appointmentTime" });
            }
            else
            {
                response.columns.Add(new ToBeBilledColumn { textName = "AdmissionId", valueName = "admissionId", hidden = true });
                response.columns.Add(new ToBeBilledColumn { textName = "AdmissionActionId", valueName = "admissionActionId", hidden = true });
                response.columns.Add(new ToBeBilledColumn { textName = "Admission", valueName = "admissionDate" });
            }
            response.columns.Add(new ToBeBilledColumn { textName = "practiceDoctorId", valueName = "practiceDoctorId", hidden = true });
            response.columns.Add(new ToBeBilledColumn { textName = "Doctor", valueName = "doctorName" });
            response.columns.Add(new ToBeBilledColumn { textName = "Office", valueName = "office" });
            response.columns.Add(new ToBeBilledColumn { textName = "Show Claim", valueName = "hasClaim" });

            return response;
        }

        public ClaimSearchResponse ClaimSearch(int practiceId)
        {
            ClaimSearchResponse response = new ClaimSearchResponse();
            response.doctors = GetPracticeDoctors(practiceId);
            response.offices = GetOffices(practiceId);

            //get list of payments
            List<TextValueViewModel> payments = new List<TextValueViewModel>(); ;
            foreach (PaymentMethod payment in Enum.GetValues(typeof(PaymentMethod)))
            {
                payments.Add(new TextValueViewModel { text = payment.GetEnumDescription(), value = ((int)payment).ToString() });
            }
            response.payments = payments;

            //get list of appointment bill statuses
            List<TextValueViewModel> billStatuses = (from t in context.BillStatuses.AsNoTracking()
                                                     select new TextValueViewModel { text = t.name, value = t.id.ToString() })
                                                              .ToList();
            response.billStatuses = billStatuses;

            //get list of test code
            List<TextValueViewModel> testCodes = (from t in context.PracticeTests
                                                  join a in context.Tests on t.TestId equals a.Id
                                                  where a.isActive && t.PracticeId == practiceId
                                                  orderby a.testShortName
                                                  select new TextValueViewModel { text = a.testShortName, value = a.Id.ToString() }).Distinct().ToList();
            response.testCodes = testCodes;

            //get list of consult code
            List<TextValueViewModel> consultCodes = (from t in context.BillingConsultRates
                                                     orderby t.claimCode
                                                     select new TextValueViewModel { text = t.claimCode, value = t.claimCode })
                                                    .Distinct()
                                                    .ToList();
            response.consultCodes = consultCodes;

            //get list of diagnose code
            List<TextValueViewModel> diagnoseCodes = (from t in context.DiagnoseCode
                                                     orderby t.Code
                                                     select new TextValueViewModel { text = t.Code, value = t.Code })
                                                      .Distinct()
                                                      .ToList();
            response.diagnoseCodes = diagnoseCodes;

            return response;
        }

        public ClaimSearchDataResponse ClaimSearchData(bool isBillingAdmin, int practiceId, List<int> practiceDoctorIds, ClaimSearchDataRequest request)
        {
            ClaimSearchDataResponse response = new ClaimSearchDataResponse();
            var billDetails = (from a in context.Bills
                               join b in context.BillDetails on a.id equals b.billId
                               join g in context.Demographics on b.PatientRecordId equals g.PatientRecordId
                               join h in context.PracticeDoctors on b.practiceDoctorId equals h.Id
                               join i in context.ExternalDoctors on h.ExternalDoctorId equals i.Id
                               let e = (from e in context.Offices where e.Id == a.officeId select e).FirstOrDefault()
                               let f = (from f in context.Appointments where f.Id == b.appointmentId select f).FirstOrDefault()
                               let ac = (from ac in context.HDAdmissions where ac.Id == b.hdAdmissionId select ac).FirstOrDefault()
                               let t = (from r in context.Tests join s in context.AppointmentTests on r.Id equals s.TestId where s.Id == b.appointmentTestId && s.IsActive select r).FirstOrDefault()
                               let ef = (from p in context.BillingEDTFiles where p.id == b.edtFileId select p).FirstOrDefault()
                               let cbs = (from s in context.BillStatuses where s.id == b.billStatusId select s).FirstOrDefault()
                               where (isBillingAdmin || practiceDoctorIds.Contains(h.Id)) && h.PracticeId == practiceId
                               select new
                               {
                                   b.PatientRecordId,
                                   patientName = (g.lastName == null ? string.Empty : g.lastName.ToUpper() + ", ") + (g.firstName ?? string.Empty),
                                   PracticeDoctorId = b.practiceDoctorId,
                                   doctorName = (i.lastName == null ? string.Empty : i.lastName.ToUpper() + ", ") + (i.firstName ?? string.Empty),
                                   OfficeId = b.officeId,
                                   office = e == null ? string.Empty : e.name,
                                   b.billStatusId,
                                   testId = t == null ? -1 : t.Id,
                                   claimBillStatus = cbs == null ? string.Empty : cbs.name,
                                   appointmentTime = f == null ? DateTime.MinValue : f.appointmentTime,
                                   admissionDate = ac == null ? DateTime.MinValue : ac.DateAdmitted,
                                   b.payment,
                                   b.serviceCode,
                                   diagnoseCode = b.diagnoseCode ?? string.Empty,
                                   errorCode = ef == null ? string.Empty : ef.Error.ToString(),
                                   b.MOHId,
                                   billedDate = b.date,
                                   b.serviceDate,
                                   b.ohipPayDate,
                                   b.reconciledDate,
                                   paid = b.ohipAmountPaid,
                                   //b.claimNumber,
                                   b.appointmentId,
                                   admissionId = b.hdAdmissionId,
                                   b.healthCardNumber
                               });

            var aaa = billDetails.ToList();
            //filter
            if (request.billingType == 1)
                billDetails = billDetails.Where(p => p.appointmentId > 0);
            else
                billDetails = billDetails.Where(p => p.admissionId > 0);

            if (request.filters != null && request.filters.Count > 0)
            {
                for (int i = 0; i < request.filters.Count; i++)
                {
                    string filter = request.filters[i].text.Trim().ToLower();
                    switch (request.filters[i].value.Trim().ToLower())
                    {
                        case "patientname":
                            billDetails = billDetails.Where(p => p.patientName.ToLower().Contains(filter));
                            break;
                        case "servicedatestart":
                            DateTime serviceDateStart = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => EF.Functions.DateDiffDay(p.serviceDate, serviceDateStart) <= 0);
                            break;
                        case "servicedateend":
                            DateTime serviceDateEnd = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => EF.Functions.DateDiffDay(p.serviceDate, serviceDateEnd) >= 0);
                            break;
                        case "billingcode":
                            billDetails = billDetails.Where(p => p.serviceCode.ToLower().Contains(filter));
                            break;
                        case "billingdatestart":
                            DateTime billingDateStart = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => EF.Functions.DateDiffDay(p.billedDate, billingDateStart) <= 0);
                            break;
                        case "billingdateend":
                            DateTime billingDateEnd = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => EF.Functions.DateDiffDay(p.billedDate, billingDateEnd) >= 0);
                            break;
                        case "diagnosecode":
                            billDetails = billDetails.Where(p => p.diagnoseCode.ToLower().Contains(filter));
                            break;
                        case "paymentdatestart":
                            DateTime paymentDateStart = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => EF.Functions.DateDiffDay(p.ohipPayDate, paymentDateStart) <= 0);
                            break;
                        case "paymentdateend":
                            DateTime paymentDateEnd = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => EF.Functions.DateDiffDay(p.ohipPayDate, paymentDateEnd) >= 0);
                            break;
                        case "errorcode":
                            billDetails = billDetails.Where(p => p.errorCode.ToLower().Contains(filter));
                            break;
                        case "reconcileddatestart":
                            DateTime reconciledDateStart = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => EF.Functions.DateDiffDay(p.reconciledDate, reconciledDateStart) <= 0);
                            break;
                        case "reconcileddateend":
                            DateTime reconciledDateEnd = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => EF.Functions.DateDiffDay(p.reconciledDate, reconciledDateEnd) >= 0);
                            break;
                        case "practicedoctorid":
                            billDetails = billDetails.Where(p => ("," + filter + ",").Contains("," + p.PracticeDoctorId.ToString() + ","));
                            break;
                        case "officeid":
                            billDetails = billDetails.Where(p => ("," + filter + ",").Contains("," + p.OfficeId.ToString() + ","));
                            break;
                        case "paymentid":
                            billDetails = billDetails.Where(p => ("," + filter + ",").Contains("," + ((int)p.payment).ToString() + ","));
                            break;
                        case "billstatusid":
                            billDetails = billDetails.Where(p => ("," + filter + ",").Contains("," + (p.billStatusId ?? -1).ToString() + ","));
                            break;
                        case "testid":
                            billDetails = billDetails.Where(p => ("," + filter + ",").Contains("," + p.testId.ToString() + ","));
                            break;
                        case "consultcodes":
                            billDetails = billDetails.Where(p => ("," + filter + ",").Contains("," + p.serviceCode + ","));
                            break;
                        case "diagnosecodes":
                            billDetails = billDetails.Where(p => ("," + filter + ",").Contains("," + p.diagnoseCode + ","));
                            break;
                        case "ohipnumber":
                            billDetails = billDetails.Where(p => p.healthCardNumber.ToLower().Contains(filter));
                            break;
                        case "claimnumberappointmentadmissionid":
                            if (request.billingType == 1)
                                billDetails = billDetails.Where(p => p.appointmentId.ToString().Contains(filter));
                            else
                                billDetails = billDetails.Where(p => p.admissionId.ToString().Contains(filter));
                            break;
                        case "claimnumberohip":
                            billDetails = billDetails.Where(p => p.MOHId.ToLower().Contains(filter));
                            break;
                    }
                }
            }

            if (request.totalRowRequest)
            {
                response.totalRow = billDetails.Count();
            }

            //sorting
            if (string.IsNullOrEmpty(request.sortByColumn) || string.IsNullOrEmpty(request.sortByOrder))
                billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
            else
            {
                switch (request.sortByColumn.Trim().ToLower())
                {
                    case "patient":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.patientName);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.patientName);
                        break;
                    case "doctor":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.doctorName);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.doctorName);
                        break;
                    case "office":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.office);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.office);
                        break;
                    case "status":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.claimBillStatus);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.claimBillStatus);
                        break;
                    case "appointment":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.appointmentTime);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
                        break;
                    case "admission":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.admissionDate);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.admissionDate);
                        break;
                    case "billcode":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.serviceCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.serviceCode);
                        break;
                    case "diagnosecode":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.diagnoseCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.diagnoseCode);
                        break;
                    case "errorcode":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.errorCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.errorCode);
                        break;
                    case "mohid":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.MOHId);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.MOHId);
                        break;
                    case "billed":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.billedDate);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.billedDate);
                        break;
                    case "reconciled":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.reconciledDate);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.reconciledDate);
                        break;
                    case "paid":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.paid);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.paid);
                        break;
                    default:
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.appointmentTime);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
                        break;
                }
            }

            var data = billDetails.Skip(request.rowStart).Take(request.rowCount).ToList()
                                            .Select(b => new ClaimSearchData
                                            {
                                                patientRecordId = b.PatientRecordId,
                                                patientName = b.patientName,
                                                practiceDoctorId = b.PracticeDoctorId,
                                                doctorName = b.doctorName,
                                                officeId = b.OfficeId,
                                                office = b.office,
                                                billStatus = b.claimBillStatus,
                                                appointmentTime = b.appointmentTime == DateTime.MinValue ? string.Empty : b.appointmentTime.ToString(DATEFORMAT, CultureInfo.InvariantCulture),
                                                admissionDate = b.admissionDate == DateTime.MinValue ? string.Empty : b.admissionDate.ToString(DATEFORMAT, CultureInfo.InvariantCulture),
                                                serviceCode = b.serviceCode,
                                                diagnoseCode = b.diagnoseCode,
                                                errorCode = b.errorCode,
                                                MoHID = b.MOHId ?? string.Empty,
                                                billedDate = b.billedDate.ToString(DATEFORMAT, CultureInfo.InvariantCulture),
                                                reconciledDate = b.reconciledDate == null ? string.Empty : b.reconciledDate.Value.ToString(DATEFORMAT, CultureInfo.InvariantCulture),
                                                paid = b.paid == null ? string.Empty : string.Format("{0:C}", (float)b.paid / 100),
                                            }).ToList();
            response.data = data;

            return response;
        }

        public BillingHistoryResponse BillingHistory(int patientRecordId)
        {
            BillingHistoryResponse response = new BillingHistoryResponse();

            response.patientRecordId = patientRecordId;
            if (patientRecordId > 0)
            {
                var patient = (from d in context.Demographics
                               let dh = context.HealthCards.Where(dh => dh.DemographicId == d.Id).OrderByDescending(dh => dh.dateIssued).FirstOrDefault()
                               where d.PatientRecordId == patientRecordId
                               select new { d.lastName, d.firstName, number = (dh == null ? string.Empty : dh.number), d.dateOfBirth }).FirstOrDefault();
                if (patient != null)
                    response.patientInfo = (patient.lastName == null ? string.Empty : patient.lastName.ToUpper() + ", ") + (patient.firstName ?? string.Empty) + " " + (patient.number ?? string.Empty) + " " + (patient.dateOfBirth == null ? string.Empty : patient.dateOfBirth.Value.ToString("MM/dd/yyyy"));
            }

            return response;
        }

        public BillingHistorySearchResponse BillingHistorySearch(bool isBillingAdmin, int practiceId, List<int> practiceDoctorIds, BillingHistorySearchRequest request)
        {
            BillingHistorySearchResponse response = new BillingHistorySearchResponse();
            response.patientRecordId = request.patientRecordId;
            var billDetails = (from b in context.BillDetails
                               //join e in context.Offices on b.officeId equals e.Id
                               join g in context.Demographics on b.PatientRecordId equals g.PatientRecordId
                               join h in context.PracticeDoctors on b.practiceDoctorId equals h.Id
                               join i in context.ExternalDoctors on h.ExternalDoctorId equals i.Id
                               join j in context.BillStatuses on b.billStatusId equals j.id
                               let f = (from f in context.Appointments where f.Id == b.appointmentId select f).FirstOrDefault()
                               let ac = (from t in context.HDAdmissions where t.Id == b.hdAdmissionId select t).FirstOrDefault()
                               let rd = (from s in context.ExternalDoctors where s.Id == b.referralDoctorId select s).FirstOrDefault()
                               where (isBillingAdmin || practiceDoctorIds.Contains(h.Id))
                                    && b.PatientRecordId == request.patientRecordId && h.PracticeId == practiceId
                               select new
                               {
                                   b.appointmentId,
                                   admissionId = b.hdAdmissionId,
                                   b.PatientRecordId,
                                   patientName = (g.lastName == null ? string.Empty : g.lastName.ToUpper() + ", ") + (g.firstName ?? string.Empty),
                                   b.payor,
                                   b.practiceDoctorId,
                                   doctorName = (i.lastName == null ? string.Empty : i.lastName.ToUpper() + ", ") + (i.firstName ?? string.Empty),
                                   referringDoctor = rd == null ? string.Empty : ((rd.lastName == null ? string.Empty : rd.lastName.ToUpper() + ", ") + (rd.firstName ?? string.Empty)),
                                   b.officeId,
                                   b.serviceDate,
                                   service = b.service,
                                   appointmentTime = f == null ? DateTime.MinValue : f.appointmentTime,
                                   admissionDate = ac == null ? DateTime.MinValue : ac.DateAdmitted,
                                   b.serviceCode,
                                   b.diagnoseCode,
                                   b.numberOfServices,
                                   b.fee,
                                   billStatus = j.name,
                                   paymentMethod = b.payment
                               });

            //filter
            if (request.billingType == 1)
                billDetails = billDetails.Where(p => p.appointmentId > 0);
            else
                billDetails = billDetails.Where(p => p.admissionId > 0);

            if (request.filters != null && request.filters.Count > 0)
            {
                for (int i = 0; i < request.filters.Count; i++)
                {
                    if (string.IsNullOrEmpty(request.filters[i].text))
                        continue;

                    string filter = request.filters[i].text.Trim().ToLower();
                    switch (request.filters[i].value.Trim().ToLower())
                    {
                        case "startdate":
                            DateTime startDate = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => (p.appointmentId > 0 && EF.Functions.DateDiffDay(p.appointmentTime, startDate) <= 0) || (p.admissionId > 0 && EF.Functions.DateDiffDay(p.admissionDate, startDate) <= 0));
                            break;
                        case "enddate":
                            DateTime endDate = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => (p.appointmentId > 0 && EF.Functions.DateDiffDay(p.appointmentTime, endDate) >= 0) || (p.admissionId > 0 && EF.Functions.DateDiffDay(p.admissionDate, endDate) >= 0));
                            break;
                    }
                }
            }

            if (request.totalRowRequest)
                response.totalRow = billDetails.Count();

            //sorting
            if (string.IsNullOrEmpty(request.sortByColumn) || string.IsNullOrEmpty(request.sortByOrder))
                billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
            else
            {
                switch (request.sortByColumn.Trim().ToLower())
                {
                    case "appointmentdate":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.appointmentTime);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
                        break;
                    case "admissiondate":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.admissionDate);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.admissionDate);
                        break;
                    case "physician":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.doctorName);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.doctorName);
                        break;
                    case "patientpayor":
                        if (request.patientRecordId > 0)
                        {
                            if (request.sortByOrder == ORDER_ASC)
                                billDetails = billDetails.OrderBy(p => p.patientName);
                            else
                                billDetails = billDetails.OrderByDescending(p => p.patientName);
                            break;
                        }
                        else
                        {
                            if (request.sortByOrder == ORDER_ASC)
                                billDetails = billDetails.OrderBy(p => p.payor);
                            else
                                billDetails = billDetails.OrderByDescending(p => p.payor);
                            break;
                        }
                    case "diagnosis":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.diagnoseCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.diagnoseCode);
                        break;
                    case "service":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.serviceCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.serviceCode);
                        break;
                    case "servicecode":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.serviceCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.serviceCode);
                        break;
                    case "servicedate":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.serviceDate);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.serviceDate);
                        break;
                    case "diagnosecode":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.diagnoseCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.diagnoseCode);
                        break;
                    case "referringdoctor":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.referringDoctor);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.referringDoctor);
                        break;
                    case "numberofservice":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.numberOfServices);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.numberOfServices);
                        break;
                    case "fee":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.fee);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.fee);
                        break;
                    case "billstatus":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.billStatus);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.billStatus);
                        break;
                    default:
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.appointmentTime);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
                        break;
                }
            }

            var data = billDetails.Skip(request.rowStart).Take(request.rowCount).ToList()
                                            .Select(b => new BillingHistorySearchData
                                            {
                                                officeId = b.officeId,
                                                appointmentDate = b.appointmentTime == DateTime.MinValue ? string.Empty : b.appointmentTime.ToString(DATEFORMAT),
                                                admissionDate = b.admissionDate == DateTime.MinValue ? string.Empty : b.admissionDate.ToString(DATEFORMAT),
                                                patientRecordId = b.PatientRecordId,
                                                patientName = b.patientName,
                                                payor = b.payor ?? string.Empty,
                                                practiceDoctorId = b.practiceDoctorId,
                                                doctorName = b.doctorName,
                                                service = b.service,
                                                serviceCode = b.serviceCode,
                                                serviceDate = b.serviceDate.ToString(DATEFORMAT),
                                                diagnosis = b.diagnoseCode ?? string.Empty,
                                                referringDoctor = b.referringDoctor,
                                                numberOfService = b.numberOfServices.ToString(),
                                                fee = string.Format("{0:C}", (float)b.fee / 100),
                                                billStatus = b.billStatus,
                                                paymentMethod = b.paymentMethod
                                            }).ToList();
            response.billingHistories = data;

            if (request.billingHistoryHeaderInfoRequest)
            {
                if (request.patientRecordId > 0)
                {
                    var patient = (from a in context.Demographics
                                   let b = context.DemographicsAddress.Where(t => t.DemographicId == a.Id && t.IsActive).FirstOrDefault()
                                   where a.PatientRecordId == request.patientRecordId
                                   select new
                                   {
                                       a.firstName,
                                       a.lastName,
                                       a.dateOfBirth,
                                       addressLine1 = b == null ? string.Empty : b.addressLine1,
                                       addressLine2 = b == null ? string.Empty : b.addressLine2,
                                       city = b == null ? string.Empty : b.city,
                                       province = b == null ? string.Empty : b.province,
                                       postalCode = b == null ? string.Empty : b.postalCode
                                   }).FirstOrDefault();
                    if (patient != null)
                    {
                        response.patientName = (patient.lastName == null ? string.Empty : patient.lastName.ToUpper() + ", ") + (patient.firstName ?? string.Empty);
                        response.patientDob = patient.dateOfBirth == null ? string.Empty : patient.dateOfBirth.Value.ToString(DATEFORMAT);
                        response.patientAddress = patient.addressLine1 ?? string.Empty + (patient.addressLine2 == null ? string.Empty : "br />" + patient.addressLine2);
                        response.patientCity = patient.city;
                        response.patientProvince = patient.province;
                        response.patientPostalCode = patient.postalCode;
                    }
                }
            }

            return response;
        }

        public StatementResponse Statement(int patientRecordId)
        {
            StatementResponse response = new StatementResponse();

            response.patientRecordId = patientRecordId;
            //get list of payors
            List<TextValueViewModel> payors = (from t in context.BillingPayors
                                               select new TextValueViewModel { text = t.name, value = t.id.ToString() })
                                                              .OrderBy(t => t.text)
                                                              .ToList();
            response.payors = payors;

            if (patientRecordId > 0)
            {
                var patient = (from d in context.Demographics
                               let dh = context.HealthCards.Where(dh => dh.DemographicId == d.Id).OrderByDescending(dh => dh.dateIssued).FirstOrDefault()
                               where d.PatientRecordId == patientRecordId
                               select new { d.lastName, d.firstName, number = (dh == null ? string.Empty : dh.number), d.dateOfBirth }).FirstOrDefault();
                if (patient != null)
                    response.patientInfo = (patient.lastName == null ? string.Empty : patient.lastName.ToUpper() + ", ") + (patient.firstName ?? string.Empty) + " " + (patient.number ?? string.Empty) + " " + (patient.dateOfBirth == null ? string.Empty : patient.dateOfBirth.Value.ToString("MM/dd/yyyy"));
            }

            return response;
        }

        public StatementSearchResponse StatementSearch(bool isBillingAdmin, int practiceId, List<int> practiceDoctorIds, StatementSearchRequest request)
        {
            StatementSearchResponse response = new StatementSearchResponse();
            response.patientRecordId = request.patientRecordId;
            response.payorId = request.payorId;
            var billDetails = (from b in context.BillDetails
                               //join e in context.Offices on b.officeId equals e.Id
                               join g in context.Demographics on b.PatientRecordId equals g.PatientRecordId
                               join h in context.PracticeDoctors on b.practiceDoctorId equals h.Id
                               join i in context.ExternalDoctors on h.ExternalDoctorId equals i.Id
                               join j in context.BillStatuses on b.billStatusId equals j.id
                               let f = (from f in context.Appointments where f.Id == b.appointmentId select f).FirstOrDefault()
                               let ac = (from t in context.HDAdmissions where t.Id == b.hdAdmissionId select t).FirstOrDefault()
                               let rd = (from s in context.ExternalDoctors where s.Id == b.referralDoctorId select s).FirstOrDefault()
                               where  (!(b.payment == PaymentMethod.OHIP || b.payment == PaymentMethod.RMB || b.payment == PaymentMethod.WCB))
                                    && (isBillingAdmin || practiceDoctorIds.Contains(h.Id)) && h.PracticeId == practiceId
                                    //&& j.name.ToLower() != BILLSTATUS_PAID 
                                    && ((request.patientRecordId != 0 && b.PatientRecordId == request.patientRecordId) || (request.payorId != 0 && b.payorId == request.payorId))
                               select new
                               {
                                   b.appointmentId,
                                   admissionId = b.hdAdmissionId,
                                   b.PatientRecordId,
                                   patientName = (g.lastName == null ? string.Empty : g.lastName.ToUpper() + ", ") + (g.firstName ?? string.Empty),
                                   b.payor,
                                   b.practiceDoctorId,
                                   doctorName = (i.lastName == null ? string.Empty : i.lastName.ToUpper() + ", ") + (i.firstName ?? string.Empty),
                                   referringDoctor = rd == null ? string.Empty : ((rd.lastName == null ? string.Empty : rd.lastName.ToUpper() + ", ") + (rd.firstName ?? string.Empty)),
                                   b.officeId,
                                   appointmentTime = f == null ? DateTime.MinValue : f.appointmentTime,
                                   admissionDate = ac == null ? DateTime.MinValue : ac.DateAdmitted,
                                   b.payment,
                                   b.serviceCode,
                                   b.diagnoseCode,
                                   b.numberOfServices,
                                   b.fee,
                                   billStatus = j.name
                               });

            //filter
            if (request.billingType == 1)
                billDetails = billDetails.Where(p => p.appointmentId > 0);
            else
                billDetails = billDetails.Where(p => p.admissionId > 0);

            if (request.filters != null && request.filters.Count > 0)
            {
                for (int i = 0; i < request.filters.Count; i++)
                {
                    if (string.IsNullOrEmpty(request.filters[i].text))
                        continue;

                    string filter = request.filters[i].text.Trim().ToLower();
                    switch (request.filters[i].value.Trim().ToLower())
                    {
                        case "startdate":
                            DateTime startDate = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => (request.billingType == 1 && EF.Functions.DateDiffDay(p.appointmentTime, startDate) <= 0) || (request.billingType != 1 && EF.Functions.DateDiffDay(p.admissionDate, startDate) <= 0));
                            break;
                        case "enddate":
                            DateTime endDate = DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture);
                            // EF Core: Replace SqlFunctions.DateDiff with EF.Functions.DateDiffDay
                            billDetails = billDetails.Where(p => (request.billingType == 1 && EF.Functions.DateDiffDay(p.appointmentTime, endDate) >= 0) || (request.billingType != 1 && EF.Functions.DateDiffDay(p.admissionDate, endDate) >= 0));
                break;
                    }
                }
            }

            if (request.totalRowRequest)
            {
                response.totalRow = billDetails.Count();
                if (response.totalRow > 0)
                {
                    //response.balanceOwing = string.Format("{0:C}", ((float)(from t in billDetails where t.billStatus.ToLower() != BILLSTATUS_PAID select t.fee).Sum()) / 100);
                    response.balanceOwing = "N/A";
                    var billDetailsOwing = (from t in billDetails where t.billStatus.ToLower() != BILLSTATUS_PAID select t.fee).ToList();
                    if (billDetailsOwing.Count > 0)
                        response.balanceOwing = string.Format("{0:C}", (((float)billDetailsOwing.Sum()) / 100));
                }
            }

            //sorting
            if (string.IsNullOrEmpty(request.sortByColumn) || string.IsNullOrEmpty(request.sortByOrder))
                billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
            else
            {
                switch (request.sortByColumn.Trim().ToLower())
                {
                    case "date":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.appointmentTime);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
                        break;
                    case "physician":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.doctorName);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.doctorName);
                        break;
                    case "patientpayor":
                        if (request.patientRecordId > 0)
                        {
                            if (request.sortByOrder == ORDER_ASC)
                                billDetails = billDetails.OrderBy(p => p.patientName);
                            else
                                billDetails = billDetails.OrderByDescending(p => p.patientName);
                            break;
                        }
                        else
                        {
                            if (request.sortByOrder == ORDER_ASC)
                                billDetails = billDetails.OrderBy(p => p.payor);
                            else
                                billDetails = billDetails.OrderByDescending(p => p.payor);
                            break;
                        }
                    case "diagnosis":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.diagnoseCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.diagnoseCode);
                        break;
                    case "servicesrendered":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.serviceCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.serviceCode);
                        break;
                    case "code":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.serviceCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.serviceCode);
                        break;
                    case "diagnosecode":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.diagnoseCode);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.diagnoseCode);
                        break;
                    case "referringdoctor":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.referringDoctor);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.referringDoctor);
                        break;
                    case "numberofservice":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.numberOfServices);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.numberOfServices);
                        break;
                    case "fee":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.fee);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.fee);
                        break;
                    case "billstatus":
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.billStatus);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.billStatus);
                        break;
                    default:
                        if (request.sortByOrder == ORDER_ASC)
                            billDetails = billDetails.OrderBy(p => p.appointmentTime);
                        else
                            billDetails = billDetails.OrderByDescending(p => p.appointmentTime);
                        break;
                }
            }

            var data = billDetails.Skip(request.rowStart).Take(request.rowCount).ToList()
                                            .Select(b => new StatementSearchData
                                                {
                                                    officeId = b.officeId,
                                                    appointmentDate = b.appointmentTime == DateTime.MinValue ? string.Empty : b.appointmentTime.ToString(DATEFORMAT),
                                                    admissionDate = b.admissionDate == DateTime.MinValue ? string.Empty : b.admissionDate.ToString(DATEFORMAT),
                                                    practiceDoctorId = b.practiceDoctorId,
                                                    patientRecordId = b.PatientRecordId,
                                                    patientName = b.patientName,
                                                    payor = b.payor ?? string.Empty,
                                                    doctorName = b.doctorName,
                                                    diagnosis = b.diagnoseCode ?? string.Empty,
                                                    servicesRendered = string.Empty,
                                                    serviceCode = b.serviceCode ?? string.Empty,
                                                    referringDoctor = b.referringDoctor,
                                                    numberOfService = b.numberOfServices.ToString(),
                                                    fee = string.Format("{0:C}", (float)b.fee / 100),
                                                    billStatus = b.billStatus
                                                }).ToList();
            response.statements = data;

            if (request.statementHeaderInfoRequest)
            {
                if (request.patientRecordId > 0)
                {
                    var patient = (from a in context.Demographics
                                   let b = context.DemographicsAddress.Where(t => t.DemographicId == a.Id && t.IsActive).FirstOrDefault()
                                   where a.PatientRecordId == request.patientRecordId
                                   select new
                                   {
                                       a.firstName,
                                       a.lastName,
                                       a.dateOfBirth,
                                       addressLine1 = b == null ? string.Empty : b.addressLine1,
                                       addressLine2 = b == null ? string.Empty : b.addressLine2,
                                       city = b == null ? string.Empty : b.city,
                                       province = b == null ? string.Empty : b.province,
                                       postalCode = b == null ? string.Empty : b.postalCode
                                   }).FirstOrDefault();
                    if (patient != null)
                    {
                        response.patientName = (patient.lastName == null ? string.Empty : patient.lastName.ToUpper() + ", ") + (patient.firstName ?? string.Empty);
                        response.patientDob = patient.dateOfBirth == null ? string.Empty : patient.dateOfBirth.Value.ToString(DATEFORMAT);
                        response.patientAddress = patient.addressLine1 ?? string.Empty + (patient.addressLine2 == null ? string.Empty : "br />" + patient.addressLine2);
                        response.patientCity = patient.city;
                        response.patientProvince = patient.province;
                        response.patientPostalCode = patient.postalCode;
                        //if (patient.address != null)
                        //{
                        //    response.patientAddress = patient.address.addressLine1 ?? string.Empty + (patient.address.addressLine2 == null ? string.Empty : "br />" + patient.address.addressLine2);
                        //    response.patientCity = patient.address.city;
                        //    response.patientProvince = patient.address.province;
                        //    response.patientPostalCode = patient.address.postalCode;
                        //}
                    }
                }
                else
                {
                    var payor = context.BillingPayors.Where(a => a.id == request.payorId).FirstOrDefault();
                    if (payor != null)
                    {
                        response.payorName = payor.name;
                        response.payorAddress = payor.address;
                    }
                }
            }

            return response;
        }

        public LogFileSearchResponse LogFileSearch(LogFileSearchRequest request)
        {
            LogFileSearchResponse response = new LogFileSearchResponse();

            string logFileTypes = _configuration?["AppSettings:BillingLogFileTypes"];
            if (string.IsNullOrEmpty(logFileTypes))
            {
                response.errorMessage = "BillingLogFileTypes configuration not found";
                return response;
            }
            string[] logFileTypeArray = logFileTypes.Split(';');
            IEnumerable<FileInfo> files = Enumerable.Empty<FileInfo>();
            foreach (var logFileType in logFileTypeArray)
            {
                string folder = Path.GetDirectoryName(logFileType);
                string searchPattern = Path.GetFileName(logFileType);
                files = files.Concat(new DirectoryInfo(folder).GetFiles(searchPattern));
            }

            var logFiles = files.Select(a => new { fileName = Path.GetFileName(a.FullName), a.FullName, a.LastWriteTime });
            if (request.totalRowRequest)
                response.totalRow = logFiles.Count();

            //sorting
            if (string.IsNullOrEmpty(request.sortByColumn) || string.IsNullOrEmpty(request.sortByOrder))
                logFiles = logFiles.OrderByDescending(p => p.LastWriteTime);
            else
            {
                switch (request.sortByColumn.Trim().ToLower())
                {
                    case "filename":
                        if (request.sortByOrder == ORDER_ASC)
                            logFiles = logFiles.OrderBy(p => p.fileName);
                        else
                            logFiles = logFiles.OrderByDescending(p => p.fileName);
                        break;
                    default:
                        if (request.sortByOrder == ORDER_ASC)
                            logFiles = logFiles.OrderBy(p => p.LastWriteTime);
                        else
                            logFiles = logFiles.OrderByDescending(p => p.LastWriteTime);
                        break;
                }
            }

            var logFilePage = logFiles.Skip(request.rowStart).Take(request.rowCount).ToList();
            response.logFiles = logFilePage.Select(a => new LogFileSearchData { fileName = a.fileName, fileFullName = a.FullName, lastModified = a.LastWriteTime.ToString(DATEFORMAT2) }).ToList();
            if (response.logFiles.Count > 0)
            {
                var logFile = ShowLogFile(response.logFiles[0].fileFullName);
                if (string.IsNullOrEmpty(logFile.errorMessage))
                    response.logFile = logFile;
                else
                    response.errorMessage = logFile.errorMessage;
            }

            return response;
        }

        private List<DoctorEdtErrorData> GetDoctorEdtErrorData(int practiceId, string edtErrorFileName)
        {
            DoctorEdtErrorData doctorEdtData = null;
            PatientEdtErrorData patientEdtData = null;
            PaymentEdtErrorData paymentEdtData = null;
            List<DoctorEdtErrorData> doctorEdtErrorData = new List<DoctorEdtErrorData>();
            string record;
            string[] records = File.ReadAllLines(edtErrorFileName);

            string billingNumber;
            string healthCardNumber;
            int claimNumber;
            List<string> billingNumbers = new List<string>();
            List<string> healthCardNumbers = new List<string>();
            List<int> claimNumbers = new List<int>();
            //BillingEDTErrorCodes

            for (int n = 0; n < records.Count(); n++)
            {
                record = records[n] + "".PadLeft(100, ' ');
                switch (record.Substring(0, 3).ToUpper())
                {
                    case "HX1":
                        billingNumber = record.Substring(27, 6);
                        if (!billingNumbers.Contains(billingNumber))
                            billingNumbers.Add(billingNumber);

                        break;
                    case "HXH":
                        healthCardNumber = record.Substring(3, 10);
                        if (!healthCardNumbers.Contains(healthCardNumber))
                            healthCardNumbers.Add(healthCardNumber);

                        billingNumber = record.Substring(35, 6);
                        if (!billingNumbers.Contains(billingNumber))
                            billingNumbers.Add(billingNumber);

                        claimNumber = 0;
                        if (int.TryParse(record.Substring(23, 8), out claimNumber))
                        {
                            if (!claimNumbers.Contains(claimNumber))
                                claimNumbers.Add(claimNumber);
                        }

                        break;
                    case "HXR":
                        healthCardNumber = record.Substring(3, 12).Trim().TrimStart('0');
                        if (!healthCardNumbers.Contains(healthCardNumber))
                            healthCardNumbers.Add(healthCardNumber);

                        break;
                }
            }

            int nReadNumber = 80;
            int nLength;
            List<SP_EdtErrorDoctorData> doctors = new List<SP_EdtErrorDoctorData>();
            List<SP_EdtErrorPatientData> patients = new List<SP_EdtErrorPatientData>();
            List<SP_EdtErrorBillDetailData> billDetails = new List<SP_EdtErrorBillDetailData>();
            List<SP_EdtErrorAppointment> appointments = new List<SP_EdtErrorAppointment>();
            List<SP_EdtErrorAdmissionAction> admissionActions = new List<SP_EdtErrorAdmissionAction>();
            List<Billing_EDTErrorCode> edtErrorCodes;

            if (billingNumbers.Count > 0)
            {
                List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("practiceId", practiceId),
                    new SqlParameter("billingNumbers", string.Join(",", billingNumbers))
                };
                doctors = context.GetData<SP_EdtErrorDoctorData>("dbo.GetBillingEdtErrorDoctors", parameters, 300).ToList();
            }

            if (healthCardNumbers.Count > 0)
            {
                for (int i = 0; i < healthCardNumbers.Count; i += nReadNumber)
                {
                    nLength = nReadNumber;
                    if (i + nLength > healthCardNumbers.Count)
                        nLength = healthCardNumbers.Count - i;

                    List<SqlParameter> parameters = new List<SqlParameter>
                    {
                        new SqlParameter("practiceId", practiceId),
                        new SqlParameter("healthCardNumbers", string.Join(",", healthCardNumbers.GetRange(i, nLength)))
                    };
                    var patients2 = context.GetData<SP_EdtErrorPatientData>("dbo.GetBillingEdtErrorPatients", parameters, 300).ToList();
                    patients.AddRange(patients2);
                }
            }

            if (claimNumbers.Count > 0)
            {
                for (int i = 0; i < claimNumbers.Count; i += nReadNumber)
                {
                    nLength = nReadNumber;
                    if (i + nLength > claimNumbers.Count)
                        nLength = claimNumbers.Count - i;

                    List<SqlParameter> parameters = new List<SqlParameter>
                    {
                        new SqlParameter("practiceId", practiceId),
                        new SqlParameter("claimNumbers", string.Join(",", claimNumbers.GetRange(i, nLength)))
                    };
                    var billDetails2 = context.GetData<SP_EdtErrorBillDetailData>("dbo.GetBillingEdtErrorBillDetailData", parameters, 300).ToList();
                    billDetails.AddRange(billDetails2);
                }
            }

            if (billDetails.Count > 0)
            {
                List<int> appointmentIds = billDetails.Where(s => s.appointmentId != 0).Select(a => a.appointmentId).ToList();
                List<int> admissionActionIds = billDetails.Where(s => s.admissionActionId != 0).Select(a => a.admissionActionId).ToList();

                if (appointmentIds.Count > 0)
                {
                    for (int i = 0; i < appointmentIds.Count; i += nReadNumber)
                    {
                        nLength = nReadNumber;
                        if (i + nLength > appointmentIds.Count)
                            nLength = appointmentIds.Count - i;

                        List<SqlParameter> parameters = new List<SqlParameter>
                        {
                            new SqlParameter("practiceId", practiceId),
                            new SqlParameter("appointmentIds", string.Join(",", appointmentIds.GetRange(i, nLength)))
                        };
                        var appointments2 = context.GetData<SP_EdtErrorAppointment>("dbo.GetBillingEdtErrorAppointments", parameters, 300).ToList();
                        appointments.AddRange(appointments2);
                    }
                }

                if (admissionActionIds.Count > 0)
                {
                    for (int i = 0; i < admissionActionIds.Count; i += nReadNumber)
                    {
                        nLength = nReadNumber;
                        if (i + nLength > admissionActionIds.Count)
                            nLength = admissionActionIds.Count - i;

                        List<SqlParameter> parameters = new List<SqlParameter>
                        {
                            new SqlParameter("practiceId", practiceId),
                            new SqlParameter("admissionActionIds", string.Join(",", admissionActionIds.GetRange(i, nLength)))
                        };
                        var admissions2 = context.GetData<SP_EdtErrorAdmissionAction>("dbo.GetBillingEdtErrorAdmissionActions", parameters, 300).ToList();
                        admissionActions.AddRange(admissions2);
                    }
                }
            }

            edtErrorCodes = context.BillingEDTErrorCodes.ToList();

            for (int n = 0; n < records.Count(); n++)
            {
                record = records[n] + "".PadLeft(100, ' ');
                switch (record.Substring(0, 3).ToUpper())
                {
                    case "HX1":
                        doctorEdtData = ProcessHX1(record, doctors);
                        doctorEdtErrorData.Add(doctorEdtData);
                        break;
                    case "HXH":
                        patientEdtData = ProcessHXH(practiceId, record, doctors, appointments, admissionActions, billDetails);
                        if (patientEdtData != null)
                            doctorEdtData.patientEdtErrorData.Add(patientEdtData);
                        break;
                    case "HXR":
                        if (patientEdtData != null)
                            ProcessHXR(record, patients, patientEdtData);
                        break;
                    case "HX8":
                        if (patientEdtData != null)
                        {
                            paymentEdtData = ProcessHX8(record);
                            patientEdtData.paymentEdtErrorData.Add(paymentEdtData);
                        }
                        break;
                    case "HXT":
                        if (patientEdtData != null)
                        {
                            paymentEdtData = ProcessHXT(record, patientEdtData.errorCode, edtErrorCodes);
                            patientEdtData.paymentEdtErrorData.Add(paymentEdtData);
                        }
                        break;
                }
            }

            return doctorEdtErrorData;
        }

        public TextFileResponse ShowLogFile(string fileName)
        {
            TextFileResponse response = new TextFileResponse();
            fileName = HttpUtility.UrlDecode(fileName);
            response.fileName = Path.GetFileName(fileName);
            string folder = Path.GetDirectoryName(fileName).ToLower();

            bool isLogFile = false;
            string[] logFileTypes = ConfigurationManager.AppSettings["BillingLogFileTypes"].Split(';');
            foreach (var logFileType in logFileTypes)
            {
                if (Path.GetDirectoryName(logFileType).ToLower() == folder)
                    isLogFile = true;
            }

            if (!(isLogFile && Path.GetExtension(fileName).ToLower() == ".txt"))
            {
                response.errorMessage = "No permission to view this file";
                return response;
            }

            if (File.Exists(fileName))
                response.content = File.ReadAllText(fileName);
            else
                response.errorMessage = "File doesn't exist";

            return response;
        }

        private DoctorEdtErrorData ProcessHX1(string data, List<SP_EdtErrorDoctorData> doctors)
        {
            DoctorEdtErrorData doctorEdtData = new DoctorEdtErrorData();
            //string edtGroupId = data.Substring(23, 4);
            string billingNumber = data.Substring(27, 6);
            //string specialtyCode = data.Substring(33, 2);
            //var externalDoctor = (from e in context.ExternalDoctors
            //                      join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
            //                      where e.OHIPPhysicianId == billingNumber
            //                      select new { practiceDoctorId = p.Id, e.firstName, e.lastName, applicationUserId = p.ApplicationUserId }).FirstOrDefault();
            var externalDoctor = (from e in doctors
                                  where e.billingNumber == billingNumber
                                  select new { e.practiceDoctorId, e.firstName, e.lastName, e.applicationUserId }).FirstOrDefault();
            if (externalDoctor != null)
            {
                doctorEdtData.practiceDoctorId = externalDoctor.practiceDoctorId;
                doctorEdtData.applicationUserId = externalDoctor.applicationUserId;
                doctorEdtData.doctorName = externalDoctor.firstName + " " + externalDoctor.lastName;
            }
            else
                doctorEdtData.doctorName = string.Format("Unknown (billing number: {0})", billingNumber);

            doctorEdtData.ProcessDate = string.Format("{0}/{1}/{2}", data.Substring(42, 2), data.Substring(44, 2), data.Substring(38, 4));

            return doctorEdtData;
        }

        private PatientEdtErrorData ProcessHXH(int practiceId, string data, List<SP_EdtErrorDoctorData> doctors, List<SP_EdtErrorAppointment> appointments, List<SP_EdtErrorAdmissionAction> admissionActions, List<SP_EdtErrorBillDetailData> billDetails)
        {
            PatientEdtErrorData patientEdtErrorData = new PatientEdtErrorData();
            patientEdtErrorData.healthCardNumber = data.Substring(3, 10);
            patientEdtErrorData.healthCardVersion = data.Substring(13, 2);
            patientEdtErrorData.dateOfBirth = string.Format("{0}/{1}/{2}", data.Substring(19, 2), data.Substring(21, 2), data.Substring(15, 4));
            patientEdtErrorData.errorCode = string.IsNullOrEmpty(data.Substring(64, 15)) ? string.Empty : data.Substring(64, 15).Trim();

            int claimNumber = 0;
            if (!int.TryParse(data.Substring(23, 8), out claimNumber))
                claimNumber = 0;
            var billingDetail = (from a in billDetails
                                 where !string.IsNullOrWhiteSpace(a.healthCardNumber) && !string.IsNullOrWhiteSpace(patientEdtErrorData.healthCardNumber) &&
                                        a.healthCardNumber.Trim() == patientEdtErrorData.healthCardNumber.Trim() &&
                                         //a.healthCardVersion.Trim() == patientEdtErrorData.healthCardVersion.Trim() &&
                                         (a.appointmentId == claimNumber || a.admissionActionId == claimNumber)
                                 select a).FirstOrDefault();
            if (billingDetail == null)
            {
                patientEdtErrorData.appointmentId = 0;
                patientEdtErrorData.admissionActionId = 0;
                patientEdtErrorData.patientRecordId = 0;
                patientEdtErrorData.patientName = "Unknown";
                //return null;
            }
            else
            {
                patientEdtErrorData.appointmentId = billingDetail.appointmentId;
                patientEdtErrorData.admissionActionId = billingDetail.admissionActionId;
                patientEdtErrorData.patientRecordId = billingDetail.patientRecordId;
                patientEdtErrorData.patientName = (billingDetail.lastName ?? string.Empty) + ", " + (billingDetail.firstName ?? string.Empty);
            }

            string billingNumber = data.Substring(35, 6);
            var externalDoctor = doctors.Where(a => a.billingNumber == billingNumber).FirstOrDefault();
            if (externalDoctor != null)
            {
                patientEdtErrorData.referralDoctorId = externalDoctor.externalDoctorId;
                patientEdtErrorData.referralDoctorName = externalDoctor.firstName + " " + externalDoctor.lastName;
            }
            else
                patientEdtErrorData.referralDoctorName = billingNumber;

            patientEdtErrorData.referralNumber = billingNumber;

            if (patientEdtErrorData.appointmentId > 0)
            {
                var appointment = appointments.Where(a => a.id == patientEdtErrorData.appointmentId).FirstOrDefault();
                if (appointment != null)
                {
                    patientEdtErrorData.officeId = appointment.officeId;
                    patientEdtErrorData.appointmentDate = appointment.appointmentTime.ToString(DATEFORMAT, CultureInfo.InvariantCulture);
                }
            }
            else if (patientEdtErrorData.admissionActionId > 0)
            {
                //var admission = (from a in context.HDAdmissions
                //                 join b in context.HDAdmissionActions on a.Id equals b.AdmissionId
                //                 where b.Id == patientEdtErrorData.admissionActionId
                //                 select new { a.DateAdmitted }).FirstOrDefault();
                var admission = admissionActions.Where(a => a.admissionActionId == patientEdtErrorData.admissionActionId).FirstOrDefault();
                if (admission != null)
                    patientEdtErrorData.admissionDate = admission.dateAdmitted.ToString(DATEFORMAT, CultureInfo.InvariantCulture);
            }

            return patientEdtErrorData;
        }

        private void ProcessHXR(string data, List<SP_EdtErrorPatientData> patients, PatientEdtErrorData patientEdtData)
        {
            if (patientEdtData == null)
                patientEdtData = new PatientEdtErrorData();
            patientEdtData.healthCardNumber = data.Substring(3, 12).Trim().TrimStart('0');
            patientEdtData.healthCardVersion = string.Empty;

            //var patient = (from a in context.HealthCards join b in context.Demographics on a.DemographicId equals b.Id where a.number == patientEdtData.healthCardNumber select b).FirstOrDefault();
            var patient = patients.Where(a => a.healthCardNumber == patientEdtData.healthCardNumber).FirstOrDefault();
            if (patient == null)
            {
                patientEdtData.patientRecordId = 0;
                patientEdtData.patientName = "Unknown";
                patientEdtData.dateOfBirth = string.Empty;
            }
            else
            {
                patientEdtData.patientRecordId = patient.patientRecordId;
                patientEdtData.patientName = patient.firstName + " " + patient.lastName;
                patientEdtData.dateOfBirth = string.Format("{0}/{1}/{2}", data.Substring(19, 2), data.Substring(21, 2), data.Substring(15, 4));
            }
        }

        private PaymentEdtErrorData ProcessHX8(string data)
        {
            PaymentEdtErrorData paymentEdtErrorData = new PaymentEdtErrorData();
            paymentEdtErrorData.hx8Message = data.Substring(5, 55);
            return paymentEdtErrorData;
        }

        private PaymentEdtErrorData ProcessHXT(string data, string errorCode, List<Billing_EDTErrorCode> edtErrorCodes)
        {
            PaymentEdtErrorData paymentEdtErrorData = new PaymentEdtErrorData();
            paymentEdtErrorData.serviceCode = data.Substring(3, 5);
            paymentEdtErrorData.fee = string.Format("{0:C}", (float.Parse(data.Substring(10, 6)) / 100));
            paymentEdtErrorData.numberOfServices = data.Substring(16, 2);
            paymentEdtErrorData.serviceDate = string.Format("{0}/{1}/{2}", data.Substring(22, 2), data.Substring(24, 2), data.Substring(18, 4));
            paymentEdtErrorData.diagnoseCode = data.Substring(26, 4);
            paymentEdtErrorData.errorCode = string.IsNullOrEmpty(data.Substring(64, 15)) ? string.Empty : data.Substring(64, 15).Trim();
            if (string.IsNullOrEmpty(paymentEdtErrorData.errorCode))
                paymentEdtErrorData.errorCode = errorCode;
            if (!string.IsNullOrEmpty(paymentEdtErrorData.errorCode))
            {
                var edtError = edtErrorCodes.Where(a => a.errorCode == paymentEdtErrorData.errorCode).FirstOrDefault();
                if (edtError == null)
                    paymentEdtErrorData.errorMessage = "Cannot find the error message";
                else
                    paymentEdtErrorData.errorMessage = edtError.errorMessage;
            }

            return paymentEdtErrorData;
        }

        private RemittanceDetailResponse GetRemittanceAdviceData(string remittanceAdviceFileName, RemittancePermission permission)
        {
            RemittanceDetailResponse response = new RemittanceDetailResponse();
            response.fileName = Path.GetFileName(remittanceAdviceFileName);

            string dataType;
            string record;
            string doctorBillingNumber = string.Empty;
            string[] records = File.ReadAllLines(remittanceAdviceFileName);

            record = records[0];
            if (record.Substring(0, 7) != "HR1V030")
            {
                response.errorMessage = "****WRONG HEADER****";
                return response;
            }

            string remittanceAdviceFileData = remittanceAdviceFileName + RA_DATA_FILE_EXTENSION;
            if (File.Exists(remittanceAdviceFileData))
            {
                try
                {
                    RemittanceDetailResponse response2 = JsonConvert.DeserializeObject<RemittanceDetailResponse>(File.ReadAllText(remittanceAdviceFileData));
                    response.detailDatas = response2.detailDatas;
                    response.doctorRADatas = response2.doctorRADatas;
                    return response;
                }
                catch (Exception ex)
                {
                    this.LogDebug("Cannot load ra data", ex);
                    File.Delete(remittanceAdviceFileData);
                    this.LogDebugFormat("Delete ra data file {0}", remittanceAdviceFileData);
                }
            }

            List<string> billingNumbers = new List<string>();
            List<string> healthCardNumbers = new List<string>();
            for (int n = 0; n < records.Count(); n++)
            {
                record = records[n] + "".PadLeft(100, ' ');
                if (record.Substring(0, 3).ToUpper() == "HR4")
                {
                    string billingNumber = record.Substring(15, 6).Trim();
                    string healthCardNumber = record.Substring(52, 12).Trim();

                    if (!billingNumbers.Contains(billingNumber))
                        billingNumbers.Add(billingNumber);
                    if (!healthCardNumbers.Contains(healthCardNumber))
                        healthCardNumbers.Add(healthCardNumber);
                }
            }

            List<SP_RADoctorData> doctors = new List<SP_RADoctorData>();
            List<SP_RAPatientData> patients = new List<SP_RAPatientData>();

            if (billingNumbers.Count > 0)
            {
                List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("practiceId", permission.practiceId),
                    new SqlParameter("billingNumbers", string.Join(",", billingNumbers))
                };
                doctors = context.GetData<SP_RADoctorData>("dbo.GetBillingRADoctors", parameters, 300).ToList();
            }

            if (healthCardNumbers.Count > 0)
            {
                int readingNumber = 50;
                for (int i = 0; i < healthCardNumbers.Count(); i = i+ readingNumber)
                {
                    int endIndex = Math.Min(healthCardNumbers.Count(), i + readingNumber);
                    var healthCardNumbersReading = healthCardNumbers.GetRange(i, endIndex - i);
                    List<SqlParameter> parameters = new List<SqlParameter>
                        {
                            new SqlParameter("practiceId", permission.practiceId),
                            new SqlParameter("healthCardNumbers", string.Join(",", healthCardNumbersReading))
                        };
                    var patientsReading = context.GetData<SP_RAPatientData>("dbo.GetBillingRAPatients", parameters, 300).ToList();
                    patients.AddRange(patientsReading);
                }
            }

            var testServiceCodes = context.BillingTestRates.ToList();
            var consultServiceCodes = context.BillingConsultRates.ToList();

            for (int n = 0; n < records.Count(); n++)
            {
                record = records[n] + "".PadLeft(100, ' ');
                dataType = record.Substring(0, 3).ToUpper();
                switch (dataType)
                {
                    case "HR1":
                        RemittanceDetailHR1 hr1 = new RemittanceDetailHR1();
                        hr1.payeeName = record.Substring(29, 30);
                        hr1.totalAmountPayable = record.Substring(68, 1) + string.Format("{0:C}", float.Parse(record.Substring(59, 9)) / 100);
                        response.detailDatas.Add(new RemittanceDetailData { dataType = dataType, dada = JsonConvert.SerializeObject(hr1) });
                        break;
                    case "HR2":
                        RemittanceDetailHR2 hr2 = new RemittanceDetailHR2();
                        hr2.address = record.Substring(33, 25);
                        response.detailDatas.Add(new RemittanceDetailData { dataType = dataType, dada = JsonConvert.SerializeObject(hr2) });
                        break;
                    case "HR3":
                        RemittanceDetailHR3 hr3 = new RemittanceDetailHR3();
                        hr3.address = record.Substring(3, 50);
                        response.detailDatas.Add(new RemittanceDetailData { dataType = dataType, dada = JsonConvert.SerializeObject(hr3) });
                        break;
                    case "HR4":
                        RemittanceDetailHR4 hr4 = new RemittanceDetailHR4();
                        doctorBillingNumber = record.Substring(15, 6);
                        hr4.doctorName = string.Format("Unknown (who's billing number is {0})", doctorBillingNumber);
                        var doctor = doctors.Where(a => a.billingNumber == doctorBillingNumber.Trim()).FirstOrDefault();
                        if (doctor != null)
                            hr4.doctorName = (doctor.lastName == null ? string.Empty : doctor.lastName.ToUpper() + ", ") + (doctor.firstName ?? string.Empty);
                        string serviceLocationIndicator = record.Substring(69, 3);
                        if (serviceLocationIndicator == "HIP" || serviceLocationIndicator == "HOP")
                        {
                            hr4.appointmentId = string.Empty;
                            hr4.admissionActionId = record.Substring(23, 8);
                        }
                        else
                        {
                            hr4.appointmentId = record.Substring(23, 8);
                            hr4.admissionActionId = string.Empty;
                        }
                        hr4.healthRegistration = record.Substring(52, 14);
                        hr4.patientName = "Unknown";
                        var patient = patients.Where(a => a.healthCardNumber == record.Substring(52, 12).Trim()).FirstOrDefault();
                        if (patient != null)
                        {
                            hr4.patientName = (patient.lastName == null ? string.Empty : patient.lastName.ToUpper() + ", ") + (patient.firstName ?? string.Empty);
                            hr4.demographicId = patient.demographicId;
                            hr4.patientRecordId = patient.patientRecordId;
                        }
                        response.detailDatas.Add(new RemittanceDetailData { dataType = dataType, dada = JsonConvert.SerializeObject(hr4) });

                        if (!response.doctorRADatas.Exists(a => a.billingNumber == doctorBillingNumber))
                            response.doctorRADatas.Add(new DoctorRAData { doctorName = hr4.doctorName, billingNumber = doctorBillingNumber });

                        break;
                    case "HR5":
                        RemittanceDetailHR5 hr5 = new RemittanceDetailHR5();
                        int submitted = int.Parse(record.Substring(31, 6));
                        int paid = int.Parse(record.Substring(37, 6));
                        string serviceCode = record.Substring(25, 5);
                        hr5.claimNumber = record.Substring(3, 11);
                        hr5.transactionType = record.Substring(14, 1);
                        hr5.serviceDate = string.Format("{0}/{1}/{2}", record.Substring(19, 2), record.Substring(21, 2), record.Substring(15, 4));
                        hr5.numberofServices = record.Substring(23, 2);
                        hr5.serviceCode = serviceCode;
                        hr5.submitted = string.Format("{0:C}", (float)submitted / 100);
                        hr5.paid = record.Substring(43, 1) + string.Format("{0:C}", (float)paid / 100);
                        hr5.explanatoryCode = record.Substring(44, 2);
                        response.detailDatas.Add(new RemittanceDetailData { dataType = dataType, dada = JsonConvert.SerializeObject(hr5) });

                        if (record.Substring(43, 1) == "-")
                            paid = -paid;

                        var doctorRAData = response.doctorRADatas.Where(a => a.billingNumber == doctorBillingNumber).FirstOrDefault();
                        if (doctorRAData != null)
                        {
                            doctorRAData.submittedTotal += submitted;
                            doctorRAData.paidTotal += paid;
                        }

                        int billingTypeId = 0;
                        var testServiceCode = testServiceCodes.Where(a => a.claimCode == serviceCode).FirstOrDefault();
                        if (testServiceCode == null)
                        {
                            var consultServiceCode = consultServiceCodes.Where(a => a.claimCode == serviceCode).FirstOrDefault();
                            if (consultServiceCode != null)
                                billingTypeId = consultServiceCode.billingTypeId;
                        }
                        else
                            billingTypeId = testServiceCode.billingTypeId;

                        if (billingTypeId == 1)
                        {
                            doctorRAData.submittedProfessional += submitted;
                            doctorRAData.paidProfessional += paid;
                        }
                        else if (billingTypeId == 2)
                        {
                            doctorRAData.submittedTechnical += submitted;
                            doctorRAData.paidTechnical += paid;
                        }

                        break;
                    case "HR6":
                        RemittanceDetailHR6 hr6 = new RemittanceDetailHR6();
                        hr6.claimsAdjustment = record.Substring(12, 1) + string.Format("{0:C}", float.Parse(record.Substring(3, 9)) / 100);
                        hr6.advances = record.Substring(22, 1) + string.Format("{0:C}", float.Parse(record.Substring(13, 9)) / 100);
                        hr6.reductions = record.Substring(32, 1) + string.Format("{0:C}", float.Parse(record.Substring(23, 9)) / 100);
                        response.detailDatas.Add(new RemittanceDetailData { dataType = dataType, dada = JsonConvert.SerializeObject(hr6) });
                        break;
                    case "HR7":
                        RemittanceDetailHR7 hr7 = new RemittanceDetailHR7();
                        hr7.transactionCode = record.Substring(3, 2);
                        hr7.transactionDate = string.Format("{0}/{1}/{2}", record.Substring(10, 2), record.Substring(12, 2), record.Substring(6, 4));
                        hr7.amount = record.Substring(22, 1) + string.Format("{0:C}", float.Parse(record.Substring(14, 8)) / 100);
                        hr7.message = record.Substring(23, 50);
                        response.detailDatas.Add(new RemittanceDetailData { dataType = dataType, dada = JsonConvert.SerializeObject(hr7) });
                        break;
                    case "HR8":
                        RemittanceDetailHR8 hr8 = new RemittanceDetailHR8();
                        hr8.message = record.Substring(3, 70);
                        response.detailDatas.Add(new RemittanceDetailData { dataType = dataType, dada = JsonConvert.SerializeObject(hr8) });
                        break;
                }
            }

            File.WriteAllText(remittanceAdviceFileData, JsonConvert.SerializeObject(response));

            return response;
        }

        private string GetFilter(string filterName, List<TextValueViewModel>filters)
        {
            if (filters == null)
                return string.Empty;

            var filter = filters.Where(a => a.value.ToLower() == filterName.ToLower()).FirstOrDefault();
            if (filter == null)
                return string.Empty;

            return filter.text;
        }

        private string GetDateFilter(string filterName, List<TextValueViewModel> filters)
        {
            string filter = GetFilter(filterName, filters);
            if (string.IsNullOrWhiteSpace(filter))
                return string.Empty;

            return DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture).ToString(DATEFORMAT3, CultureInfo.InvariantCulture);
        }

        private int GetBoolFilter(string filterName, List<TextValueViewModel> filters)
        {
            string filter = GetFilter(filterName, filters);
            if (string.IsNullOrWhiteSpace(filter))
                return 0;

            return 1;
        }

        private string GetPaymentMethodFilter(List<TextValueViewModel> filters)
        {
            if (filters == null || filters.Count == 0)
                return string.Empty;

            string filterReturn = string.Empty;
            foreach (var filter in filters)
            {
                try
                {
                    PaymentMethod paymentMethod;
                    if (Enum.TryParse(filter.value, out paymentMethod))
                    {
                        if (Enum.IsDefined(typeof(PaymentMethod), paymentMethod))
                        {
                            if (!string.IsNullOrWhiteSpace(filterReturn))
                                filterReturn += ",";
                            filterReturn += ((int)paymentMethod).ToString();
                        }
                    }

                }
                catch
                {
                }
            }

            return filterReturn;
        }       

        private List<TextValueViewModel> GetPracticeDoctors(int practiceId)
        {
            AppointmentsBLL scheduleBLL = new AppointmentsBLL(context);
            var practiceDoctors = scheduleBLL.GetPracticeDoctors(practiceId);
            if (practiceDoctorIds.Count != 0)
                practiceDoctors = practiceDoctors.Where(a => practiceDoctorIds.Contains(a.PracticeDoctorId)).ToList();

            return practiceDoctors.Select(e => new TextValueViewModel { text = (e.LastName ?? string.Empty) + ", " + (e.FirstName ?? string.Empty), value = e.PracticeDoctorId.ToString() }).ToList();
        }

        private List<TextValueViewModel> GetOffices(int practiceId)
        {
            //get list of offices
            List<TextValueViewModel> offices = (from t in context.Offices.Where(t => t.PracticeId == practiceId && t.status == 0)
                                                select new TextValueViewModel { text = t.name, value = t.Id.ToString() })
                                                              .OrderBy(t => t.text)
                                                              .ToList();

            return offices;
        }

        private List<TextValueViewModel> GetEdtGroups(int practiceId)
        {
            //get list of edt groups
            List<TextValueViewModel> edtGroups = (from t in context.Offices
                                                  join s in context.BillingGroups on t.Id equals s.officeId
                                                  where t.PracticeId == practiceId && t.status == 0
                                                  orderby s.edtGroupId
                                                  select new TextValueViewModel { text = s.edtGroupId, value = s.edtGroupId }).Distinct().ToList();

            return edtGroups;
        }

        private List<TextValueViewModel> GetHospitals()
        {
            //get list of edt groups
            List<TextValueViewModel> hospitals = (from t in context.Hospitals
                                                  orderby t.name
                                                  select new TextValueViewModel { text = t.name, value = t.Id.ToString() }).Distinct().ToList();

            return hospitals;
        }        

        private dynamic GetDoctor(string billingNumber)
        {
            var doctor = (from e in context.ExternalDoctors
                          join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                          where e.OHIPPhysicianId == billingNumber
                          select new { name = (e.lastName == null ? string.Empty : e.lastName.ToUpper() + ", ") + (e.firstName ?? string.Empty), externalDoctorId = e.Id, practiceDoctorId = p.Id }).FirstOrDefault();

            if (doctor == null)
                return new { name = string.Empty, externalDoctorId = 0, practiceDoctorId = 0 };

            return doctor;
        }

        private dynamic GetPatient(string healthCardNUmber)
        {
            var patient = (from h in context.HealthCards
                           join d in context.Demographics on h.DemographicId equals d.Id
                           join p in context.PatientRecords on d.PatientRecordId equals p.Id
                           where h.number.Trim() == healthCardNUmber.Trim()
                           select new { name = (d.lastName == null ? string.Empty : d.lastName.ToUpper() + ", ") + (d.firstName ?? string.Empty), demographicId = d.Id, patientRecordId = p.Id }).FirstOrDefault();

            if (patient == null)
                return new { name = string.Empty, demographicId = 0, patientRecordId = 0 };

            return patient;
        }

        private string GetEdtFile(int edtFileId, int fileType, RemittancePermission permission, out string errorMessage)
        {
            errorMessage = string.Empty;

            string fileDescription = fileType == 0 ? "EDT" : "Confirmed";

            var edtFile = context.BillingEDTFiles.Where(a => a.id == edtFileId).FirstOrDefault();
            if (edtFile == null)
            {
                errorMessage = $"Cannot find {fileDescription} file(id: {edtFileId})";
                return string.Empty;
            }

            int edtFilePracticeDoctorId = edtFile.practiceDoctorId;
            if (edtFile.officeId == 0)
                edtFilePracticeDoctorId = 0;
            if (!CheckPermission(edtFilePracticeDoctorId, 0, edtFile.edtGroupId, permission))
            {
                errorMessage = $"No permission to view this {fileDescription} file (id: {edtFileId})";
                return string.Empty;
            }
                
            string fileName = fileType == 0 ? edtFile.claimFileName : edtFile.confirmedFileName;

            if (!File.Exists(fileName))
            {
                errorMessage = $"Cannot find {fileDescription} file ({Path.GetFileName(fileName)})";
                return string.Empty;
            }

            return fileName;
        }

        private bool CheckPermission(int resourcePracticeDoctorId, int resourceExternalDoctorId, string resourceEdtGroupId, RemittancePermission permission)
        {
            bool isSamePractice = false;
            if (permission.practiceId == -1)
                isSamePractice = true;
            else
            {
                if (resourcePracticeDoctorId > 0)
                {
                    isSamePractice = context.PracticeDoctors.Where(a => a.Id == resourcePracticeDoctorId && a.PracticeId == permission.practiceId).Any();
                } else if (resourceExternalDoctorId > 0)
                {
                    isSamePractice = context.PracticeDoctors.Where(a => a.ExternalDoctorId == resourceExternalDoctorId && a.PracticeId == permission.practiceId).Any();
                }
                else
                {
                    isSamePractice = (from a in context.BillingGroups
                                      join b in context.Offices on a.officeId equals b.Id
                                      where a.edtGroupId == resourceEdtGroupId && b.PracticeId == permission.practiceId
                                      select a).Any();
                }
            }

            if (!isSamePractice)
                return false;

            if (resourcePracticeDoctorId > 0 || resourceExternalDoctorId > 0)
            {
                if (permission.isRASoloBillingAdmin)
                    return true;

                if (resourcePracticeDoctorId > 0)
                {
                    if (permission.practiceDoctorIds.Contains(resourcePracticeDoctorId.ToString()))
                        return true;

                    return false;
                }

                if (context.PracticeDoctors.Where(a => a.ExternalDoctorId == resourceExternalDoctorId && permission.practiceDoctorIds.Contains(a.Id.ToString())).Any())
                    return true;

                return false;
            }

            if (permission.isRAGroupBillingAdmin)
                return true;

            return false;
        }

        private string GetRAFile(int billingFileId, RemittancePermission permission, out string errorMessage)
        {
            errorMessage = string.Empty;

            var billingFile = context.Billing_Files.Where(a => a.id == billingFileId && a.fileType == BillingFileType.Reconciliation).FirstOrDefault();
            if (billingFile == null)
            {
                errorMessage = $"Cannot find RA file (id: {billingFileId})";
                return string.Empty;
            }

            if (!CheckPermission(billingFile.practiceDoctorId, 0, billingFile.edtGroupId, permission))
            {
                errorMessage = $"No permission to view this RA file (id: {billingFileId})";
                return string.Empty;
            }

            if (!File.Exists(billingFile.fileName))
            {
                errorMessage = string.Format("Cannot find remittance advice file ({0})", Path.GetFileName(billingFile.fileName));
                return string.Empty;
            }

            string[] records = File.ReadAllLines(billingFile.fileName);
            string edtGroupId = records[0].Substring(7, 4);
            if (edtGroupId == PERSONAL_GROUPID)
            {
                string billingNumber = records[0].Substring(11, 6);
                var practiceDoctor = (from e in context.ExternalDoctors
                                      join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                                      where (permission.practiceId == -1 || p.PracticeId == permission.practiceId) && e.OHIPPhysicianId == billingNumber && (permission.isRASoloBillingAdmin || permission.practiceDoctorIds.Contains(p.Id.ToString()))
                                      select p).FirstOrDefault();
                if (practiceDoctor == null)
                {
                    errorMessage = "You don't have permission to view this remittance advice file. Please Contact Administrator! ";
                    return string.Empty;
                }
                permission.practiceId = practiceDoctor.PracticeId;
            }
            else
            {
                if (!permission.isRAGroupBillingAdmin)
                {
                    errorMessage = "You don't have permission to view this remittance advice file. Please Contact Administrator! ";
                    return string.Empty;
                }
                var edtGroup = (from a in context.BillingGroups
                                join b in context.Offices on a.officeId equals b.Id
                                where a.edtGroupId == edtGroupId
                                select new { b.PracticeId }).FirstOrDefault();
                if (edtGroup == null || !(permission.practiceId == -1 || edtGroup.PracticeId == permission.practiceId))
                {
                    errorMessage = "You don't have permission to view this remittance advice file. Please Contact Administrator! ";
                    return string.Empty;
                }
                permission.practiceId = edtGroup.PracticeId;
            }

            return billingFile.fileName;
        }

        private string LogException(string methodName, Exception ex, string extraMessage)
        {
            string logMessage = extraMessage + Environment.NewLine + methodName + Environment.NewLine + ex.Message;
            if (ex.InnerException != null)
            {
                logMessage += Environment.NewLine + ex.InnerException.Message;
            }
            this.LogDebugFormat("Exception: {0}", logMessage);
            return logMessage;
        }

        public void Dispose()
        {
            context.Dispose();
        }
    }
}
