﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Cerebrum.ViewModels.Requisition;
using Cerebrum.ViewModels.ReportLetterQueue;
using Cerebrum.Data;
using Cerebrum.ViewModels.ReportLetterQueue.SP_Entities;
// EF Core: Use Microsoft.Data.SqlClient instead of System.Data.SqlClient
using Microsoft.Data.SqlClient;
using AwareMD.Cerebrum.Shared;

namespace Cerebrum.BLL.ReportLetterQueue
{
    public interface IAdminReportQueueBLL
    {
        // add method here when ready
        List<TextValueViewModel> GetReportQueueStatuses();

        List<VMAdminReportQueueSearch> GetSearchReportQueue(DateTime date, int reportQueueStatus, int practiceId);

        void ResendSuspendedReports(int practiceId);
    }

    public class AdminReportQueueBLL : IAdminReportQueueBLL
    {
        private CerebrumContext context;

        public AdminReportQueueBLL(CerebrumContext cerebrumContext)
        {
            context = cerebrumContext;
        }

        public List<TextValueViewModel> GetReportQueueStatuses()
        {
            List<TextValueViewModel> reportQueueStatus = new List<TextValueViewModel>();
            foreach (ReportQueueStatus reportMethod in Enum.GetValues(typeof(ReportQueueStatus)))
                reportQueueStatus.Add(new TextValueViewModel { text = reportMethod.GetEnumDescription(), value = ((int)reportMethod).ToString() });

            return reportQueueStatus;
        }

        public List<VMAdminReportQueueSearch> GetSearchReportQueue(DateTime date, int reportQueueStatus, int practiceId)
        {
            var queueSearch = new List<VMAdminReportQueueSearch>();
            var search = SP_GetReportQueueSearch(date, reportQueueStatus, practiceId);

            var statuses = GetReportQueueStatuses();
            var idx = 1;
            var dateFormat = "MM/dd/yyyy h:mm tt";

            foreach (var item in search)
            {
                DateTime? dateSent = null;
                if (item.SendStatusId == (int)ReportQueueStatus.Sent)
                {
                    dateSent = item.DateLastModified;
                }
                var sendStatus = statuses.Where(i => i.value == item.SendStatusId.ToString()).FirstOrDefault();

                var queue = new VMAdminReportQueueSearch()
                {
                    Attempts = item.NumberOfAttempts,
                    DateInQueue = item.DateInQueue,
                    DateInQueueStr = item.DateInQueue.ToString(dateFormat),
                    TimeInQueueStr = item.DateInQueue.ToString("h:mm tt"),
                    DateSent = dateSent,
                    DateSentStr = dateSent?.ToString(dateFormat),
                    Id = idx,
                    OfficeName = item.OfficeName,
                    PatientName = item.PatientFirstName + " " + item.PatientLastName,
                    ReportQueueId = item.ReportQueueId,
                    SendByUser = item.UserFullName,
                    SendStatus = sendStatus.text,
                    TestDate = item.TestDate,
                    TestDateStr = item.TestDate.ToString(dateFormat),
                    TestName = item.TestShortName

                };

                queueSearch.Add(queue);
                idx = idx + 1;
            }

            return queueSearch;
        }

        public void ResendSuspendedReports(int practiceId)
        {
            var reportQueueList = (from t1 in context.AppointmentTests
                                   join t3 in context.Appointments on t1.AppointmentId equals t3.Id
                                   join t2 in context.Offices on t3.OfficeId equals t2.Id
                                   join t4 in context.ReportQueues on t1.Id equals t4.AppointmentTestId
                                   where t4.SendStatusId == 4 && t2.PracticeId == practiceId
                                   select t4.Id).ToList();

            var reportQueues = context.ReportQueues.Where(f => reportQueueList.Contains(f.Id)).ToList();
            
            foreach (var item in reportQueues)
            {
                item.NumberOfAttempts = 0;
                item.SendStatusId = 1;
                item.DateLastModified = DateTime.Now;
            }

            context.SaveChanges();

        }

        #region private
        private List<SP_AdminReportQueueSearch> SP_GetReportQueueSearch(DateTime date, int reportQueueStatus, int practiceId)
        {
            var parameters = new List<SqlParameter>
                             {
                                new SqlParameter("@QueueDate", date),
                                new SqlParameter("@ReportQueueStatus", reportQueueStatus),
                                new SqlParameter("@PracticeId", practiceId)
                             };

            return context.GetData<SP_AdminReportQueueSearch>("[dbo].[GetReportQueueSearch]", parameters).ToList();

        }

        #endregion
    }
}
