﻿using Cerebrum.Data;
using Cerebrum.ViewModels.Reminder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Reminder
{
    public class ReminderSentHistoryService
    {
        private readonly ReminderSentHistoryRepository _repository;

        public ReminderSentHistoryService(ReminderSentHistoryRepository repository)
        {
            _repository = repository;
        }

        public void SaveSentHistory(int reminderTypeId, int officeId, int appointmentId, int patientRecordId, int modifyingUserId, string modifyingIpAddress)
        {
            var existing = _repository.Get(reminderTypeId, officeId, appointmentId, patientRecordId);

            if (existing != null)
            {
                existing.IsResendRequired = false;
                existing.messageSentDate = DateTime.Now;
            }
            else
            {
                var history = new ReminderSentHistory
                {
                    appointmentId = appointmentId.ToString(),
                    patientRecordId = patientRecordId,
                    officeId = officeId,
                    messageSentDate = DateTime.Now,
                    reminderTypeId = reminderTypeId
                };

                _repository.Add(history);
            }

            _repository.Save(modifyingUserId, modifyingIpAddress);
        }
    }


}
