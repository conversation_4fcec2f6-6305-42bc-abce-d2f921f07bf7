using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using Cerebrum.Reminder.SPEntities;
using Cerebrum.ViewModels.Reminder;
using Cerebrum.ViewModels.Requisition;
using log4net;
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;

namespace Cerebrum.Reminder
{
    public interface IReminderBLL
    {
        ReminderResponse EmailReminder(int practiceId);
        ReminderResponse SaveEmailReminderData(int practiceId, EmailReminderData request, int userId, string ipAddress);
        ReminderResponse TextReminder(int practiceId);
        EmailReminderData LoadEmailReminderData(int practiceId, int officeId);
        EmailReminderData LoadTextReminderData(int practiceId, int officeId);
        ReminderResponse SaveTextReminderData(int practiceId, EmailReminderData request, int userId, string ipAddress);
        ReminderResponse VoiceReminder(int practiceId);
        EmailReminderData LoadVoiceReminderData(int practiceId, int officeId);
        ReminderResponse SaveVoiceReminderData(EmailReminderData request, int userId, string ipAddress);
        EmailConfirmedData AppointmentConfirmed(string confirmedData, int userId, string ipAddress);
        List<ReminderOffice> GetAppointmentReminders(string reminderType, int officeId);
        int GetReminderTypeId(string reminderTypeName);
        List<CallListItem> GetCallList(int officeId);
        void SetCallResults(List<ResultItem> resultList, int userId, string updatedFromIp);
    }
    public class ReminderBLL : IDisposable, ILogGeneric, IReminderBLL
    {
        #region Constants

        private const string EMAILREMINDERTYPE = "emailreminder";
        private const string TEXTREMINDERTYPE = "textreminder";
        private const string VOICEREMINDERTYPE = "voicereminder";

        #endregion

        #region Variables

        private CerebrumContext context;
        private readonly ReminderSentHistoryService _reminderHistoryService;
        protected static readonly ILog Logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public ReminderBLL() : this(new CerebrumContext()) { } // TODO: Use DI when available
        #endregion
        public ReminderBLL(CerebrumContext _c)
        {
            context = _c;
            _reminderHistoryService = new ReminderSentHistoryService(new ReminderSentHistoryRepository(_c));
        }

        public ReminderResponse EmailReminder(int practiceId)
        {
            ReminderResponse response = new ReminderResponse();
            response.offices = GetOffices(practiceId);
            return response;
        }

        public virtual EmailReminderData LoadEmailReminderData(int practiceId, int officeId)
        {
            EmailReminderData response = new EmailReminderData();
            response.officeId = officeId;

            bool samePracticeOffice = context.Offices.Any(a => a.Id == officeId && a.PracticeId == practiceId);
            if (samePracticeOffice)
            {
                int emailReminderTypeId = GetReminderTypeId(EMAILREMINDERTYPE);
                if (emailReminderTypeId <= 0)
                {
                    response.errorMessage = "Cannot find email reminder type";
                    return response;
                }

                ReminderRule emailReminder = (from rr in context.ReminderRules
                                              join o in context.Offices on rr.officeId equals o.Id
                                              where o.PracticeId == practiceId && o.Id == officeId && rr.reminderTypeId == emailReminderTypeId
                                              select rr).FirstOrDefault();
                if (emailReminder != null)
                {
                    response.subject = emailReminder.subject;
                    response.body = emailReminder.body;
                    response.schedule = emailReminder.scheduleMessage.ToString();
                    response.schedule2 = emailReminder.scheduleMessage2.ToString();
                }

                var email = context.OfficeEmails.Where(a => a.OfficeId == officeId).FirstOrDefault();
                if (email != null)
                {
                    response.mailServerUrl = email.serverUrl;
                    response.mailServerPort = email.serverPort;
                    response.userName = email.userName;
                    response.password = email.password;
                    response.enableSsl = email.enableSsl;
                }
            }
            return response;
        }

        public ReminderResponse SaveEmailReminderData(int practiceId, EmailReminderData request, int userId, string ipAddress)
        {
            bool samePracticeOffice = context.Offices.Any(a => a.Id == request.officeId && a.PracticeId == practiceId);

            ReminderResponse response = new ReminderResponse();
            if (samePracticeOffice)
            {

                int emailReminderTypeId = GetReminderTypeId(EMAILREMINDERTYPE);
                if (emailReminderTypeId <= 0)
                {
                    response.errorMessage = "Cannot find email reminder type";
                    return response;
                }

                var emailReminder = context.ReminderRules.Where(a => a.officeId == request.officeId && a.reminderTypeId == emailReminderTypeId).FirstOrDefault();
                if (emailReminder == null)
                {
                    emailReminder = new ReminderRule();
                    emailReminder.officeId = request.officeId;
                    emailReminder.reminderTypeId = emailReminderTypeId;
                    context.ReminderRules.Add(emailReminder);
                }

                int scheduleMessage = 0;
                int scheduleMessage2 = 0;
                if (string.IsNullOrWhiteSpace(request.schedule) || !int.TryParse(request.schedule, out scheduleMessage))
                    scheduleMessage = 0;
                if (string.IsNullOrWhiteSpace(request.schedule2) || !int.TryParse(request.schedule2, out scheduleMessage2))
                    scheduleMessage2 = 0;

                emailReminder.subject = request.subject;
                emailReminder.body = request.body;
                emailReminder.scheduleMessage = scheduleMessage;
                emailReminder.scheduleMessage2 = scheduleMessage2;

                var email = context.OfficeEmails.Where(a => a.OfficeId == request.officeId).FirstOrDefault();
                if (email == null)
                {
                    email = new OfficeEmail();
                    email.OfficeId = request.officeId;
                    email.createdDateTime = DateTime.Now;
                    email.updatedDateTime = DateTime.Now;
                    context.OfficeEmails.Add(email);
                }
                else
                    email.updatedDateTime = DateTime.Now;

                email.serverUrl = request.mailServerUrl;
                email.serverPort = request.mailServerPort;
                email.userName = request.userName;
                email.password = request.password;
                email.enableSsl = request.enableSsl;

                context.SaveChanges(userId, ipAddress);
            }
            else
            {
                response.errorMessage = "Invalid request";
            }
            return response;
        }

        public ReminderResponse TextReminder(int practiceId)
        {
            ReminderResponse response = new ReminderResponse();
            response.offices = GetOffices(practiceId);
            return response;
        }

        public virtual EmailReminderData LoadTextReminderData(int practiceId, int officeId)
        {
            EmailReminderData response = new EmailReminderData();
            response.officeId = officeId;

            int textReminderTypeId = GetReminderTypeId(TEXTREMINDERTYPE);
            if (textReminderTypeId <= 0)
            {
                response.errorMessage = "Cannot find text reminder type";
                return response;
            }

            ReminderRule textReminder = (from rr in context.ReminderRules
                                         join o in context.Offices on rr.officeId equals o.Id
                                         where o.PracticeId == practiceId && o.Id == officeId && rr.reminderTypeId == textReminderTypeId
                                         select rr).FirstOrDefault();

            if (textReminder != null)
            {
                response.body = textReminder.body;
                response.schedule = textReminder.scheduleMessage.ToString();
                response.schedule2 = textReminder.scheduleMessage2.ToString();
            }

            return response;
        }

        public ReminderResponse SaveTextReminderData(int practiceId, EmailReminderData request, int userId, string ipAddress)
        {
            ReminderResponse response = new ReminderResponse();
            bool samePracticeOffice = context.Offices.Any(a => a.Id == request.officeId && a.PracticeId == practiceId);
            if (samePracticeOffice)
            {
                int textReminderTypeId = GetReminderTypeId(TEXTREMINDERTYPE);
                if (textReminderTypeId <= 0)
                {
                    response.errorMessage = "Cannot find text reminder type";
                    return response;
                }

                bool isNewData = false;
                var textReminder = context.ReminderRules.Where(a => a.officeId == request.officeId && a.reminderTypeId == textReminderTypeId).FirstOrDefault();
                if (textReminder == null)
                {
                    textReminder = new ReminderRule();
                    textReminder.officeId = request.officeId;
                    textReminder.reminderTypeId = textReminderTypeId;
                    isNewData = true;
                }

                int scheduleMessage = 0;
                int scheduleMessage2 = 0;
                if (string.IsNullOrWhiteSpace(request.schedule) || !int.TryParse(request.schedule, out scheduleMessage))
                    scheduleMessage = 0;
                if (string.IsNullOrWhiteSpace(request.schedule2) || !int.TryParse(request.schedule2, out scheduleMessage2))
                    scheduleMessage2 = 0;

                textReminder.body = request.body;
                textReminder.scheduleMessage = scheduleMessage;
                textReminder.scheduleMessage2 = scheduleMessage2;

                if (isNewData)
                    context.ReminderRules.Add(textReminder);

                context.SaveChanges(userId, ipAddress);
            }
            else
            {
                response.errorMessage = "Invalid request";
            }
            return response;
        }

        public ReminderResponse VoiceReminder(int practiceId)
        {
            ReminderResponse response = new ReminderResponse();
            response.offices = GetOffices(practiceId);
            return response;
        }

        public virtual EmailReminderData LoadVoiceReminderData(int practiceId, int officeId)
        {
            EmailReminderData response = new EmailReminderData();
            response.officeId = officeId;

            int voiceReminderTypeId = GetReminderTypeId(VOICEREMINDERTYPE);
            if (voiceReminderTypeId <= 0)
            {
                response.errorMessage = "Cannot find voice reminder type";
                return response;
            }

            ReminderRule voiceReminder = (from rr in context.ReminderRules
                                          join o in context.Offices on rr.officeId equals o.Id
                                          where o.PracticeId == practiceId && o.Id == officeId && rr.reminderTypeId == voiceReminderTypeId
                                          select rr).FirstOrDefault();

            if (voiceReminder != null)
            {
                response.body = voiceReminder.body;
                response.schedule = voiceReminder.scheduleMessage.ToString();
                response.schedule2 = voiceReminder.scheduleMessage2.ToString();
            }

            return response;
        }

        public ReminderResponse SaveVoiceReminderData(EmailReminderData request, int userId, string ipAddress)
        {
            ReminderResponse response = new ReminderResponse();

            int voiceReminderTypeId = GetReminderTypeId(VOICEREMINDERTYPE);
            if (voiceReminderTypeId <= 0)
            {
                response.errorMessage = "Cannot find voice reminder type";
                return response;
            }

            bool isNewData = false;
            var voiceReminder = context.ReminderRules.Where(a => a.officeId == request.officeId && a.reminderTypeId == voiceReminderTypeId).FirstOrDefault();
            if (voiceReminder == null)
            {
                voiceReminder = new ReminderRule();
                voiceReminder.officeId = request.officeId;
                voiceReminder.reminderTypeId = voiceReminderTypeId;
                isNewData = true;
            }

            int scheduleMessage = 0;
            int scheduleMessage2 = 0;
            if (string.IsNullOrWhiteSpace(request.schedule) || !int.TryParse(request.schedule, out scheduleMessage))
                scheduleMessage = 0;
            if (string.IsNullOrWhiteSpace(request.schedule2) || !int.TryParse(request.schedule2, out scheduleMessage2))
                scheduleMessage2 = 0;

            voiceReminder.body = request.body;
            voiceReminder.scheduleMessage = scheduleMessage;
            voiceReminder.scheduleMessage2 = scheduleMessage2;

            if (isNewData)
                context.ReminderRules.Add(voiceReminder);

            context.SaveChanges(userId, ipAddress);

            return response;
        }

        public EmailConfirmedData AppointmentConfirmed(string confirmedData, int userId, string ipAddress)
        {
            EmailConfirmedData response = new EmailConfirmedData();

            if (string.IsNullOrWhiteSpace(confirmedData))
            {
                response.errorMessage = "Empty confirmed data";
                return response;
            }

            string data = confirmedData.Substring(1);
            if (string.IsNullOrWhiteSpace(data))
            {
                response.errorMessage = "Invalid confirmed data";
                return response;
            }

            string decryptedData = DecryptData(data);
            Dictionary<string, string> parameters = decryptedData.Split(new[] { '&' }, StringSplitOptions.RemoveEmptyEntries)
                                                   .Select(a => a.Split('='))
                                                   .ToDictionary(b => b[0], b => b[1]);

            string reminderTypeIdParam = parameters["reminderTypeId"];
            int reminderTypeId = 0;
            if (!int.TryParse(reminderTypeIdParam, out reminderTypeId))
            {
                response.errorMessage = "Invalid reminderTypeId";
                return response;
            }

            string patientRecordIdParam = parameters["patientRecordId"];
            if (string.IsNullOrWhiteSpace(patientRecordIdParam))
            {
                response.errorMessage = "Missing patientRecordId";
                return response;
            }

            int patientRecordId = 0;
            if (!int.TryParse(patientRecordIdParam, out patientRecordId))
            {
                response.errorMessage = "Invalid patientRecordId";
                return response;
            }

            string appointmentIdParam = parameters["appointmentId"];
            if (string.IsNullOrWhiteSpace(appointmentIdParam))
            {
                response.errorMessage = "Missing appointmentId";
                return response;
            }

            int appointmentId = 0;
            if (!int.TryParse(appointmentIdParam, out appointmentId))
            {
                response.errorMessage = "Invalid appointmentId";
                return response;
            }
            var appointment = context.Appointments.Where(a => a.PatientRecordId == patientRecordId && a.Id == appointmentId).FirstOrDefault();
            if (appointment == null)
            {
                response.errorMessage = string.Format($"Cannot find appointment (id: {appointmentId}) for patient (id: {patientRecordId})");
                return response;
            }

            var reminderTypes = context.ReminderTypes.ToList();
            int emailReminderTypeId = reminderTypes.Where(a => a.name.ToLower() == EMAILREMINDERTYPE.ToLower()).Select(b => b.id).FirstOrDefault();
            int textReminderTypeId = reminderTypes.Where(a => a.name.ToLower() == TEXTREMINDERTYPE.ToLower()).Select(b => b.id).FirstOrDefault();
            AppConfirmation appointmentConfirmation = AppConfirmation.confirmedEmail;
            if (reminderTypeId != 0 && reminderTypeId == textReminderTypeId)
                appointmentConfirmation = AppConfirmation.confirmedText;

            if (!(appointment.appointmentConfirmation == AppConfirmation.confirmed || appointment.appointmentConfirmation == AppConfirmation.confirmedEmail || appointment.appointmentConfirmation == AppConfirmation.confirmedText || appointment.appointmentConfirmation == AppConfirmation.confirmedvr))
            {
                appointment.appointmentConfirmation = appointmentConfirmation; //AppConfirmation.confirmedEmail;
            }
            UpdateOtherAppointmentConfirmation(appointment, appointmentConfirmation, userId, ipAddress);

            context.SaveChanges(userId, ipAddress);

            response.appointmentTime = appointment.appointmentTime;
            var info = (from b in context.Offices
                        join c in context.PracticeDoctors on appointment.PracticeDoctorId equals c.Id
                        join d in context.ExternalDoctors on c.ExternalDoctorId equals d.Id
                        where b.Id == appointment.OfficeId
                        select new
                        {
                            d.firstName,
                            d.lastName,
                            b.name,
                            b.businessName
                        }).FirstOrDefault();

            if (info != null)
            {
                response.doctorName = (string.IsNullOrWhiteSpace(info.lastName) ? string.Empty : info.lastName) + (string.IsNullOrWhiteSpace(info.firstName) ? string.Empty : ", " + info.firstName);
                response.officeName = (string.IsNullOrWhiteSpace(info.name) ? string.Empty : info.name) + (string.IsNullOrWhiteSpace(info.businessName) ? string.Empty : " (" + info.businessName + ")");
            }

            return response;
        }

        public List<ReminderOffice> GetAppointmentReminders(string reminderType, int officeId)
        {
            var reminderOffices = new List<ReminderOffice>();
            var dbReminderOffices = GetAppointmentRemindersSP(reminderType, officeId);

            foreach (var dbItem in dbReminderOffices)
            {
                ReminderOffice office = null;
                ReminderAppointment app = null;
                ReminderAppointmentTest appTest = null;

                office = reminderOffices.FirstOrDefault(x => x.officeId == dbItem.officeId);
                if (office == null)
                {
                    office = LoadOffice(dbItem);
                    app = LoadAppointment(dbItem);
                    appTest = LoadAppointmentTest(dbItem);

                    app.appointmentTests.Add(appTest);
                    office.appointments.Add(app);
                    reminderOffices.Add(office);
                } // office already in the list
                else
                {
                    app = office.appointments.FirstOrDefault(x => x.appointmentId == dbItem.appointmentId);
                    if (app == null)
                    {
                        app = LoadAppointment(dbItem);
                        appTest = LoadAppointmentTest(dbItem);
                        app.appointmentTests.Add(appTest);
                        office.appointments.Add(app);
                    }
                    else // appointment all ready in the office list
                    {
                        if (!app.appointmentTests.Any(x => x.appointmentTestId == dbItem.AppointmentTestId))
                        {
                            appTest = LoadAppointmentTest(dbItem);
                            app.appointmentTests.Add(appTest);
                        }
                    }
                }


            }

            return reminderOffices;
        }

        public int GetReminderTypeId(string reminderTypeName)
        {
            int reminderTypeId = 0;

            var reminderType = context.ReminderTypes.Where(a => a.name.ToLower() == reminderTypeName.ToLower()).FirstOrDefault();
            if (reminderType != null)
                reminderTypeId = reminderType.id;

            return reminderTypeId;
        }

        public List<CallListItem> GetCallList(int officeId)
        {
            List<CallListItem> callList = new List<CallListItem>();

            var offices = GetAppointmentReminders(VOICEREMINDERTYPE, officeId);
            var office = offices.Where(a => a.officeId == officeId).FirstOrDefault();
            if (office == null)
                return callList;

            string officePhoneNumber = GetCorrectPhoneNumber(office.phone);
            if (string.IsNullOrWhiteSpace(officePhoneNumber))
                return callList;

            string address = office.address1 + (string.IsNullOrWhiteSpace(office.address2) ? string.Empty : " " + office.address2) + (string.IsNullOrWhiteSpace(office.city) ? string.Empty : Environment.NewLine + office.city);
            foreach (var appointment in office.appointments)
            {
                string patientPhoneNumber = GetCorrectPhoneNumber(appointment.patientPhoneNumbers);
                if (patientPhoneNumber != "")
                {
                    string testName = string.Empty;
                    string testFullName = string.Empty;
                    string testInstruction = string.Empty;

                    CallListItem item = new CallListItem();
                    item.cell = patientPhoneNumber;
                    item.officephonenumber = officePhoneNumber;
                    item.aid = appointment.appointmentId;
                    item.adate = appointment.appointmentTime;
                    item.docname = appointment.practiceDoctorFirstName + " " + appointment.practiceDoctorLastName;
                    item.officeDirections = string.Empty;
                    item.officeID = officeId;
                    item.officeInstructions = string.Empty;
                    item.officename = office.officeBusinessName;
                    item.pat_fname = appointment.patientFirstName;
                    item.pat_lname = appointment.patientLastName;
                    item.pid = appointment.patientRecordId;
                    item.result = string.Empty;
                    item.wavfile_name = item.aid + "_" + patientPhoneNumber + ".wav";
                    item.wavfile_officeNumber = string.Empty;

                    if (appointment.appointmentTests != null && appointment.appointmentTests.Count > 0)
                    {
                        var appointmentTestNames = appointment.appointmentTests.Select(b => new { b.testShortName, b.testFullName }).ToList();
                        if (appointmentTestNames.Count > 0)
                        {
                            testName = string.Join(", ", appointmentTestNames.Select(a => a.testShortName));
                            testFullName = string.Join(", ", appointmentTestNames.Select(a => a.testFullName));
                        }
                        var appointmentTestInstructions = appointment.appointmentTests.Where(a => !string.IsNullOrWhiteSpace(a.testInstruction)).Select(b => b.testInstruction).ToList();
                        if (appointmentTestInstructions.Count > 0)
                            testInstruction = string.Join(Environment.NewLine, appointmentTestInstructions);

                        item.IsTest = true;
                        item.testName = testFullName;
                        item.testInstructions = testInstruction;
                    }

                    string textMessage = office.body.Replace("{{patientFirstName}}", appointment.patientFirstName).Replace("{{patientLastName}}", appointment.patientLastName);
                    textMessage = textMessage.Replace("{{doctorFirstName}}", appointment.practiceDoctorFirstName).Replace("{{doctorLastName}}", appointment.practiceDoctorLastName);
                    textMessage = textMessage.Replace("{{officeName}}", office.officeName).Replace("{{officeFullName}}", office.officeBusinessName).Replace("{{officePhone}}", officePhoneNumber).Replace("{{officeAddress}}", address);
                    textMessage = textMessage.Replace("{{appointmentDate}}", appointment.appointmentTime.ToString("dddd, d MMMM yyyy")).Replace("{{appointmentTime}}", appointment.appointmentTime.ToString("h:mm tt"));
                    textMessage = textMessage.Replace("{{testName}}", testName).Replace("{{testFullName}}", testFullName).Replace("{{testInstruction}}", testInstruction);
                    textMessage = textMessage.Replace("\n", " ").Replace("\r", string.Empty).Replace("<BR />", " ");

                    item.textMessage = textMessage;

                    this.LogInfo($"Adding voice message for appointment(appointmentId: {appointment.appointmentId}; patient: {appointment.patientLastName}, {appointment.patientFirstName}; phone: {patientPhoneNumber})", null);
                    callList.Add(item);
                }
            }

            return callList;
        }

        public void SetCallResults(List<ResultItem> resultList, int userId, string updatedFromIp)
        {
            if (resultList == null || resultList.Count == 0)
                return;

            int voiceReminderTypeId = GetReminderTypeId(VOICEREMINDERTYPE);
            //var confirmedList = resultList.Where(a => a.result.Trim() == "9").ToList();
            Log($"resultList: {string.Join(";", resultList.Select(s => s.aid.ToString() + "," + s.result).ToList())}");
            foreach (var item in resultList)
            {
                AppConfirmation appointmentConfirmation = AppConfirmation.confirmedvr;
                try
                {
                    bool error = true;
                    if (Enum.TryParse(item.result.Trim(), out appointmentConfirmation))
                    {
                        if (Enum.IsDefined(typeof(AppConfirmation), appointmentConfirmation))
                            error = false;
                    }
                    if (error)
                    {
                        Log($"Invalid appointment confirmation status: aid={item.aid}, result={item.result}");
                        continue;
                    }
                }
                catch
                {
                    Log($"Exception: invalid appointment confirmation status: aid={item.aid}, result={item.result}");
                    continue;
                }

                var appointment = context.Appointments.Where(x => x.Id == item.aid).FirstOrDefault();
                if (appointment == null)
                    Log($"Appointment (id: {item.aid}) does not exist");
                else
                {
                    appointment.appointmentConfirmation = appointmentConfirmation;
                    UpdateOtherAppointmentConfirmation(appointment, appointmentConfirmation, userId, updatedFromIp);
                    _reminderHistoryService.SaveSentHistory(voiceReminderTypeId, appointment.OfficeId, appointment.Id, appointment.PatientRecordId, userId, updatedFromIp);
                }
            }

            context.SaveChanges(userId, updatedFromIp);
        }

        private void UpdateOtherAppointmentConfirmation(Appointment appointment, AppConfirmation appointmentConfirmation, int userId, string updatedFromIp)
        {
            var appointments = context.Appointments.Where(a => a.PatientRecordId == appointment.PatientRecordId && EF.Functions.DateDiffDay(a.appointmentTime, appointment.appointmentTime) == 0 && a.Id != appointment.Id).ToList();
            foreach (var app in appointments)
            {
                if (!(app.appointmentConfirmation == AppConfirmation.confirmed || app.appointmentConfirmation == AppConfirmation.confirmedEmail || app.appointmentConfirmation == AppConfirmation.confirmedText || app.appointmentConfirmation == AppConfirmation.confirmedvr))
                {
                    app.appointmentConfirmation = appointmentConfirmation;
                    context.Entry(app).State = EntityState.Modified;
                }
            }
            context.SaveChanges(userId, updatedFromIp);
        }

        private List<TextValueViewModel> GetOffices(int practiceId)
        {
            //get list of offices
            List<TextValueViewModel> offices = (from t in context.Offices.Where(t => t.PracticeId == practiceId && t.status == 0)
                                                select new TextValueViewModel { text = t.name, value = t.Id.ToString() })
                                                              .OrderBy(t => t.text)
                                                              .ToList();

            return offices;
        }

        private List<SP_AppointmentReminder> GetAppointmentRemindersSP(string reminderType, int officeId)
        {
            List<SqlParameter> parameters = new List<SqlParameter>
            {
                new SqlParameter("reminderType", reminderType),
                new SqlParameter("officeId", officeId)
            };

            var reminders = context.GetData<SP_AppointmentReminder>("[dbo].[GetAppointmentReminders_v2]", parameters).ToList();

            return reminders;
        }

        private ReminderOffice LoadOffice(SP_AppointmentReminder dbReminder)
        {
            var office = new ReminderOffice();
            office.officeId = dbReminder.officeId;
            office.practiceId = dbReminder.practiceId;
            office.officeName = dbReminder.officeName;
            office.officeBusinessName = dbReminder.officeBusinessName;
            office.address1 = dbReminder.address1;
            office.address2 = dbReminder.address2;
            office.city = dbReminder.city;
            office.phone = dbReminder.phone;
            office.fax = dbReminder.fax;
            office.reminderTypeId = dbReminder.reminderTypeId;
            office.reminderType = dbReminder.reminderType;
            office.serverUrl = dbReminder.serverUrl;
            office.serverPort = dbReminder.serverPort;
            office.userName = dbReminder.userName;
            office.password = dbReminder.password;
            office.subject = dbReminder.subject;
            office.body = dbReminder.body;
            office.scheduleMessage = dbReminder.scheduleMessage;
            office.scheduleMessage2 = dbReminder.scheduleMessage2;
            office.enableSsl = dbReminder.enableSsl;
            office.officeAppointmentDate = dbReminder.officeAppointmentDate;

            return office;
        }

        private ReminderAppointment LoadAppointment(SP_AppointmentReminder dbReminder)
        {
            var appointment = new ReminderAppointment();
            appointment.appointmentId = dbReminder.appointmentId;
            appointment.appointmentTime = dbReminder.appointmentTime;
            appointment.patientRecordId = dbReminder.patientRecordId;
            appointment.demodgraphicId = dbReminder.demodgraphicId;
            appointment.patientFirstName = dbReminder.patientFirstName;
            appointment.patientLastName = dbReminder.patientLastName;
            appointment.patientPhoneNumbers = dbReminder.patientPhoneNumbers;
            appointment.patientEmail = dbReminder.patientEmail;
            appointment.practiceDoctorId = dbReminder.practiceDoctorId;
            appointment.practiceDoctorExternalDoctorId = dbReminder.practiceDoctorExternalDoctorId;
            appointment.practiceDoctorFirstName = dbReminder.practiceDoctorFirstName;
            appointment.practiceDoctorLastName = dbReminder.practiceDoctorLastName;
            appointment.AppointmentTypeName = dbReminder.appointmentTypeName;

            return appointment;
        }

        private ReminderAppointmentTest LoadAppointmentTest(SP_AppointmentReminder dbReminder)
        {
            var appointmentTest = new ReminderAppointmentTest();

            appointmentTest.appointmentId = dbReminder.appointmentId;
            appointmentTest.appointmentTestId = dbReminder.AppointmentTestId;
            appointmentTest.TestId = dbReminder.TestId;
            appointmentTest.testStartTime = dbReminder.testStartTime;
            appointmentTest.testShortName = dbReminder.testShortName;
            appointmentTest.testFullName = dbReminder.testFullName;
            appointmentTest.testInstruction = dbReminder.TestInstruction;
            return appointmentTest;
        }

        private string GetCorrectPhoneNumber(string number)
        {
            string answer = "";
            string digits = Regex.Replace(number, "[^0-9]", "");
            if (digits.Length > 9) // Normal cell number ************ 
            {
                //string areacode = digits.Substring(0, 3);
                //if (digits[0] == 1) areacode = digits.Substring(1, 3);
                //if (ontarioaareacodes.Contains(areacode) == false)
                //{
                //    //LogClass.Write("Long Distance call ----49=" + digits);
                //    answer = "";
                //}
                answer = digits;
            }
            else
                this.LogInfo($"Invalid phone number: {number}, after correction: {digits})", null);
            return answer;
        }

        private string DecryptData(string data)
        {
            data = WebUtility.UrlDecode(data);
            byte[] encodedbyte = Convert.FromBase64String(data);
            var plainText = Encoding.UTF8.GetString(encodedbyte);
            return plainText;
        }

        public string LogException(string methodName, Exception ex, string extraMessage)
        {
            string logMessage = extraMessage + Environment.NewLine + methodName + Environment.NewLine + ex.Message;
            if (ex.InnerException != null)
            {
                logMessage += Environment.NewLine + ex.InnerException.Message;
            }
            Logger.FatalFormat("Exception: {0}", logMessage);
            return logMessage;
        }

        public void Log(string message)
        {
            Logger.Info(message);
        }

        public void Dispose()
        {
            context.Dispose();
        }
    }
}
