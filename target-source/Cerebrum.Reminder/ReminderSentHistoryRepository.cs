﻿using Cerebrum.Data;
using System;
using System.Linq;

namespace Cerebrum.Reminder
{
    public class ReminderSentHistoryRepository
    {
        private readonly CerebrumContext _context;

        public ReminderSentHistoryRepository(CerebrumContext context)
        {
            _context = context;
        }
        public ReminderSentHistory Get(int reminderTypeId, int officeId, int appointmentId, int patientRecordId)
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            return _context.ReminderSentHistories
                .Where(x =>
                    x.appointmentId == appointmentId.ToString() &&
                    x.patientRecordId == patientRecordId &&
                    x.officeId == officeId &&
                    x.messageSentDate >= today && x.messageSentDate < tomorrow &&
                    x.reminderTypeId == reminderTypeId &&
                    x.IsResendRequired)
                .FirstOrDefault();
        }
        public void Add(ReminderSentHistory history)
        {
            _context.ReminderSentHistories.Add(history);
        }

        public void Save(int userId, string ip)
        {
            _context.SaveChanges(userId, ip);
        }
    }
}
