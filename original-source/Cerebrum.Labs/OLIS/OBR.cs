﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// Observation Request Segment
    /// </summary>
    public class OBR
    {
        public string SegmentID0 { get; set; } = "OBR";
        public string SetID1 { get; set; }
        public PlaceGroupNumber placeOrderNumber2 { get; set; }
        public PlaceGroupNumber fillerOrderNumber3 { get; set; }
        public UniversalServiceIdentifier universalServiceIdentifier4 { get; set; }
        public string priority5 { get; set; }
        public DateTime? requestedDateTime6 { get; set; }
        public DateTime? observationDatTime7 { get; set; }
        public DateTime? observationEndDateTime8 { get; set; }
        public CollectionVolume collectionVolume9 { get; set; }
        public string collectorIdentifier10 { get; set; }
        public string specimenActionCode11 { get; set; }
        public string dangerCode12 { get; set; }
        public string relevantClinicalInformation13 { get; set; }
        public DateTime? specimenReceivedDateTime14 { get; set; }
        public SpecimenSource specimenSource15 { get; set; }
        public Practitioner orderingPractitioner16 { get; set; }
        public PhoneNumber orderCallbackPhoneNumber17 { get; set; }
        public string referringLabUserReadableSpecimenIdentifier18 { get; set; }
        public string referringLabSpecimenBarCodeNumber19 { get; set; }
        public string performaingLabUserReadableSpecimenIdentifier20 { get; set; }
        public string fillerFiled2_21 { get; set; }
        public DateTime? resultReportStatusChangeDateTime22 { get; set; }
        public string chargeToPractice23 { get; set; }
        public string diagnosticServiceSectID24 { get; set; }
        public string testRequestStatus25 { get; set; }
        public ParentResult parentResult26 { get; set; }
        public QuantityTiming quantityTiming27 { get; set; }
        public ResultCopiesTo resultCopiesTo28 { get; set; }
        public Parent parent29 { get; set; }
        public string pointOfCareTestIdentifier30 { get; set; }
        public string numberofSampleContainers37 { get; set; }
        public string transportLogisticsOfCollectedSample38 { get; set; }
        public AssigningJurisdiction collectorsComment39 { get; set; }        
        public List<OBX> OBXs { get; set; } = new List<OBX>();
        
        public static OBR Parse(string line)
        {
            line = line.Replace("<CR>", "");
            
            if (!string.IsNullOrEmpty(line))
            {
                string[] arr = line.Split('|');
                var obr = new OBR();
                obr.SetID1 = arr[1];
                obr.placeOrderNumber2 = PlaceGroupNumber.Parse(arr[2]);
                obr.fillerOrderNumber3 =PlaceGroupNumber.Parse(arr[3]);
                var uid=(new UniversalServiceIdentifier()).ToUSID(UniversalServiceIdentifier.Parse(arr[4], '^'));
                obr.universalServiceIdentifier4 = uid;
                obr.priority5 = arr[5];
                var datecheck = arr[6].DateFormat();
                obr.requestedDateTime6 = datecheck == null ? null : (DateTime?)DateTime.ParseExact(datecheck.value, datecheck.format, CultureInfo.CurrentCulture);
                
                var addcolon = arr[7].Insert(arr[7].Length - 2, ":").Replace(" ", "-"); 
                obr.observationDatTime7 = arr[7].TrimIt() == "" ? null : (DateTime?)DateTime.ParseExact(addcolon, "yyyyMMddHHmmsszzz", CultureInfo.CurrentCulture);

                addcolon = arr[8]!=""? arr[8].Insert(arr[8].Length - 2, ":").Replace(" ", "-"):null;
                obr.observationEndDateTime8 =(addcolon==null && arr[8].TrimIt() == "") ? null : (DateTime?)DateTime.ParseExact(addcolon, "yyyyMMddHHmmsszzz", CultureInfo.CurrentCulture);

                obr.collectionVolume9 = CollectionVolume.Parse(arr[9]);
                obr.collectorIdentifier10 = arr[10];
                obr.specimenActionCode11 = arr[11];
                obr.dangerCode12 = arr[12];
                obr.relevantClinicalInformation13 = arr[13];

                addcolon =arr[14]!=""? arr[14].Insert(arr[14].Length - 2, ":").Replace(" ", "-"):null;
                obr.specimenReceivedDateTime14 =(addcolon==null && arr[14].TrimIt() == "") ? null : (DateTime?)DateTime.ParseExact(addcolon, "yyyyMMddHHmmsszzz", CultureInfo.CurrentCulture);
                
                
                obr.specimenSource15 = SpecimenSource.Parse(arr[15]);
                obr.orderingPractitioner16 = Practitioner.Parse(arr[16]);
                obr.orderCallbackPhoneNumber17 = PhoneNumber.Parse(arr[17]);
                obr.referringLabUserReadableSpecimenIdentifier18 = arr[18];
                obr.referringLabSpecimenBarCodeNumber19 = arr[19];
                obr.performaingLabUserReadableSpecimenIdentifier20 = arr[20];
                obr.fillerFiled2_21 = arr[21];
                if (arr.Length > 22)
                {
                    addcolon = arr[22]!=""? arr[22].Insert(arr[22].Length - 2, ":").Replace(" ", "-"):null;
                    obr.resultReportStatusChangeDateTime22 = (addcolon==null && arr[22].TrimIt() == "" )? null : (DateTime?)DateTime.ParseExact(addcolon, "yyyyMMddHHmmsszzz", CultureInfo.CurrentCulture);
                }
                obr.chargeToPractice23 = arr.Length>23?arr[23]:null;
                obr.diagnosticServiceSectID24 =arr.Length>24? arr[24]:null;
                obr.testRequestStatus25 = arr.Length > 25?arr[25]:null;
                obr.parentResult26 = arr.Length > 26? ParentResult.Parse(arr[26], '^'):null;
                obr.quantityTiming27 = arr.Length > 27 ? QuantityTiming.Parse(arr[27]):null;
                obr.resultCopiesTo28 = arr.Length > 28 ? ResultCopiesTo.Parse(arr[28]):null;
                obr.parent29 = arr.Length > 29 ? Parent.Parse(arr[29]):null;
                obr.pointOfCareTestIdentifier30 = arr.Length > 30 ? arr[30]:null;
                obr.numberofSampleContainers37 = arr.Length > 37 ? arr[37]:null;
                obr.transportLogisticsOfCollectedSample38 = arr.Length > 38 ? arr[38]:null;
                obr.collectorsComment39 = arr.Length > 39 ? AssigningJurisdiction.Parse(arr[39], '^'):null;

                return obr;
            }
            return null;
        }
    }

}
