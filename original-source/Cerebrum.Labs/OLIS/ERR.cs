﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// Error Segment
    /// </summary>
    public class ERR
    {
        public ErrorCodeAndLocation errorCodeAndLocation { get; set; }
        public static ERR Parse(string line)
        {
            line = line.Replace("<CR>", "");
            if (!string.IsNullOrEmpty(line))
            {
                string[] arr = line.Split('|');

                var err = new ERR();
                
                err.errorCodeAndLocation = ErrorCodeAndLocation.Parse(arr[1]);
                return err;
            }
            return null;
        }
    }
    public class ErrorCodeAndLocation
    {
        public string SegmentID { get; set; }
        public string Sequence { get; set; }
        public string FieldPosition { get; set; }
        public AssigningJurisdiction CodeIdentifyingError { get; set; }

       
        public static ErrorCodeAndLocation Parse(string line)
        {
            if (!string.IsNullOrEmpty(line))
            {
                string[] arr = line.Split('^');
                if (arr.Length > 0)
                {
                    var ecloc = new ErrorCodeAndLocation();
                    ecloc.SegmentID = arr[0];
                    ecloc.Sequence = arr[1];
                    ecloc.FieldPosition = arr[2];
                    ecloc.CodeIdentifyingError =AssigningJurisdiction.Parse(arr[3], '&');
                    return ecloc;
                }
            }
            return null;
        }
    }
}
