﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.OLIS
{
    public class ZNT
    {
        public string SegmentID0 { get; set; } = "ZNT";
        public SourceOrganization sourceOrganization1 { get; set; }

        public static ZNT Parse(string line)
        {
            line = line.Replace("<CR>", "");
            if (!string.IsNullOrEmpty(line))
            {
                string[] s = line.Split('|');
                var znt = new ZNT();
                znt.sourceOrganization1 = SourceOrganization.Parse(s[1], '^');
                return znt;
            }
            return null;
        }
    }
}
