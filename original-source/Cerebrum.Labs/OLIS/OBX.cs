﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// Observation Result Segment
    /// </summary>
    public class OBX
    {
        public string segmentId0 { get; set; }
        public int? setId1 { get; set; }
        public string valueType2 { get; set; }
        public AssigningJurisdiction observationIdentifier3 { get; set; }
        public string  observationSubId4{ get; set; }
        public string observationValue5 { get; set; }
        public string units6 { get; set; }
        public string referenceRange7 { get; set; }
        public string abnormalFlags8 { get; set; }
        public string probability9 { get; set; }
        public string natureOfAbnormalTest10 { get; set; }
        public string observationResultStatus11 { get; set; }
        public DateTime? dateTimeOfObservation14 { get; set; }
        public AssigningJurisdiction observationMethod17 { get; set; }
        public ZBX ZBX { get; set; }
        public List<NTE> NTEs { get; set; } = new List<NTE>();
        public static  OBX Parse(string s)
        {
            s = s.Replace("<CR>", "");
            if (s!=null && s.Trim().StartsWith("OBX"))
            {
                string[] v = s.Split('|');
                var obx = new OBX();
                obx.setId1 = v[1].Trim() != "" ? (int?)int.Parse(v[1]) : null;
                obx.valueType2 = v[2];
                obx.observationIdentifier3 = AssigningJurisdiction.Parse(v[3], '^');
                obx.observationSubId4 = v[4].Trim();
                obx.observationValue5 = v[5];
                obx.units6 = v[6];
                obx.referenceRange7 = v[7];
                obx.abnormalFlags8 = v[8];
                obx.observationResultStatus11 = v[11];
                obx.dateTimeOfObservation14= (v.Length>14 && v[14]!="")? (DateTime?)DateTime.ParseExact(v[14], "yyyyMMddHHmmss", CultureInfo.InvariantCulture) : null;
                return obx;
            }
            return null;
        }
    }
}