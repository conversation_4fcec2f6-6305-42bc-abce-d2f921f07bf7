﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// Diagnosis Segment
    /// </summary>
    public class DG1
    {
        public string SegmentID0 { get; set; } = "DG1";
        public int? setID1 { get; set; }
        public string diagnosisCodingMethod2 { get; set; } = string.Empty;
        public AssigningJurisdiction diagnosisCode3 { get; set; }

        public static DG1 Parse(string line)
        {
            if(!string.IsNullOrEmpty(line))
            {
                line = line.Replace("<CR>", "");
                string[] s = line.Split('|');
                var d = new DG1();
                d.setID1 = s[1] == "" ? null : (int?)int.Parse(s[1]);
                d.diagnosisCode3 = AssigningJurisdiction.Parse(s[3],'^');
            }
            return null;
        }
        public override string ToString()
        {
            var dg = new StringBuilder("DG1");
            dg.Append("|");
            dg.Append(setID1);
            dg.Append("|");
            dg.Append(diagnosisCodingMethod2);
            dg.Append("|");
            dg.Append(diagnosisCode3.ToString());
            dg.Append("<CR>\n");
            return dg.ToString();
        }

    }
}
