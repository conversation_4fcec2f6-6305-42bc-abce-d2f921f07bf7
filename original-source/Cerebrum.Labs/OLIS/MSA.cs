﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    public class MSA
    {
        public string SegmentID0 { get; set; } = "MSA";
        public string AcknowledgmentCode1 { get; set; }
        public string MessageControlID { get; set; }

        public static MSA Parse(string line)
        {
            if(!string.IsNullOrEmpty(line))
            {
                string[] s = line.Split('|');
            }
            return null;
        }
    }
}
