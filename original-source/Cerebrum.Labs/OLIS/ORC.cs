﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Globalization;
namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// Common Order Segment
    /// </summary>
    public class ORC
    {
        public string SegmentID0 { get; set; }
        public string orderControl1 { get; set; }
        public string placeOrderNumber2 { get; set; }
        public string fillerOrderNumber3 { get; set; }
        public PlaceGroupNumber placeGroupNumber4 { get; set; }
        public DateTime? dateTimeofTransaction9 { get; set; }

        public OrderingFacility orderingFacility21 { get; set; }
        public Address orderingFacilityAddress22 { get; set; }
        public PhoneNumber orderingFacilityPhoneNumber23 { get; set; }
        public Address orderingPractitionerAddress24 { get; set; }
        public static ORC Parse(string line)
        {
            line = line.Replace("<CR>", "");
            if (!string.IsNullOrEmpty(line))
            {
                string[] arr = line.Split('|');
                var orc = new ORC();
                orc.SegmentID0 = arr[0];
                orc.orderControl1 = arr[1];
                orc.placeOrderNumber2 = arr[2];
                orc.fillerOrderNumber3 = arr[3];
                orc.placeGroupNumber4 = PlaceGroupNumber.Parse(arr[4]);
                var datecheck = arr[9].DateFormat();
                orc.dateTimeofTransaction9 = datecheck==null?null:(DateTime?) DateTime.ParseExact(datecheck.value,datecheck.format, CultureInfo.CurrentCulture);
                orc.orderingFacility21 =arr.Length>21? OrderingFacility.Parse(arr[21]):null; 
                orc.orderingFacilityAddress22 =arr.Length>22? Address.Parse(arr[22]):null;
                orc.orderingFacilityPhoneNumber23 =arr.Length>23? PhoneNumber.Parse(arr[23]):null;
                orc.orderingPractitionerAddress24 =arr.Length>24? Address.Parse(arr[24]):null;
                return orc;
            }
            return null;
        }
        public override string ToString()
        {
            var s = new StringBuilder();
            s.Append(SegmentID0);
            s.Append("|");
            s.Append(orderControl1);
            s.Append("|");
            s.Append(placeOrderNumber2);
            s.Append("|");
            s.Append(fillerOrderNumber3);
            s.Append("|");
            s.Append(placeGroupNumber4.ToString());
            s.Append("|");
            s.Append(dateTimeofTransaction9 == null ? "" : ((DateTime)dateTimeofTransaction9).ToString("yyyyMMddHHmmsszzz"));
            s.Append("|");
            s.Append(orderingFacility21.ToString());
            s.Append("|");
            s.Append(orderingFacilityAddress22.ToString());
            s.Append("|");
            s.Append(orderingFacilityPhoneNumber23.ToString());
            s.Append("|");
            s.Append(orderingPractitionerAddress24.ToString());
            s.Append("<CR>\n");
            return s.ToString();
        }
    }
}
