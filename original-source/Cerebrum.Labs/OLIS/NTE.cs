﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// NTE -Segment
    /// </summary>
    public class NTE
    {
        public string SegmentID0 { get; set; } = "NTE";
        public int? setID1 { get; set; }
        public string sourceOfComment2 { get; set; }
        public string comment3 { get; set; }
        public CommentType commentType4 { get; set; }
        public ZNT ZNT { get; set; }
        public static NTE Parse(string line)
        {            
            if (!string.IsNullOrEmpty(line))
            {
                line = line.Replace("<CR>", "");
                string[] arr = line.Split('|');
                var nte = new NTE();
                if (nte.SegmentID0.Equals(arr[0]))
                {
                    nte.setID1 = arr[1]==""?null:(int?)int.Parse(arr[1]);
                    nte.sourceOfComment2 = arr[2];
                    nte.comment3 = arr[3];
                    nte.commentType4 = CommentType.Parse(arr[4]);
                    return nte;
                }
            }
            return null;
        }

    }
}
