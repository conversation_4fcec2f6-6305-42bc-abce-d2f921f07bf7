﻿using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{

    /// <summary>
    /// Read OLIS File
    /// </summary>
    public class OLISReadHL7File
    {
        private readonly ILog _log = LogManager.GetLogger(typeof(OLISReadHL7File));
        public OLISHL7Report ReadFile(string filepath)
        {
            if (File.Exists(filepath))
            {
                bool pidflag = false, obrflag = false, obxflag = false;
                var hl7lines = File.ReadAllLines(filepath);
                var hl7 = new OLISHL7Report();
                foreach (var l in hl7lines)
                {
                    try
                    {
                        if (l.Trim() != "")
                        {
                            var start = l.Substring(0, 3);
                            switch (start)
                            {
                                case "MSH":
                                    {
                                        hl7.MSH = MSH.Parse(l);
                                    }
                                    break;
                                //case "MSA": { } break;
                                case "PID":
                                    {
                                        hl7.PID = PID.Parse(l);
                                        pidflag = true;
                                        obxflag = false;
                                        obrflag = false;
                                    }
                                    break;
                                case "ZPD":
                                    {
                                        hl7.PID.ZPD = ZPD.Parse(l);
                                    }
                                    break;
                                case "NTE":
                                    {
                                        var nte = NTE.Parse(l);
                                        if (pidflag)
                                        {
                                            hl7.PID.NTE = nte;
                                        }
                                        if (obrflag)
                                        {
                                            var lastCO = hl7.commonOrders.LastOrDefault();
                                            lastCO.NTEs.Add(nte);
                                        }
                                        if (obxflag)
                                        {
                                            var lastCO = hl7.commonOrders.LastOrDefault();
                                            var lstobx = lastCO.OBR.OBXs.LastOrDefault();
                                            lstobx.NTEs.Add(nte);
                                        }
                                    }
                                    break;
                                case "ZNT":
                                    {
                                        var znt = ZNT.Parse(l);
                                        if (pidflag)
                                        {
                                            hl7.PID.NTE.ZNT = znt;
                                        }
                                        if (obrflag)
                                        {
                                            var lastCO = hl7.commonOrders.LastOrDefault();
                                            var lst = lastCO.NTEs.LastOrDefault();
                                            if(lst!=null)
                                            {
                                                lst.ZNT = znt;
                                            }

                                        }
                                        if (obxflag)
                                        {
                                            var lastCO = hl7.commonOrders.LastOrDefault();
                                            var lstobx = lastCO.OBR.OBXs.LastOrDefault();
                                            if(lstobx!=null)
                                            {
                                                if(lstobx.NTEs.Count>0)
                                                {
                                                    var lstnte = lstobx.NTEs.LastOrDefault().ZNT = znt;
                                                }
                                            }
                                        }
                                    }
                                    break;
                                case "PV1":
                                    {
                                        hl7.PV1 = PV1.Parse(l);
                                    }
                                    break;
                                case "ORC":
                                    {
                                        var co = new CommonOrder();
                                        co.ORC = ORC.Parse(l);
                                        hl7.commonOrders.Add(co);
                                    }
                                    break;
                                case "OBR":
                                    {
                                        var lastCO = hl7.commonOrders.LastOrDefault();
                                        lastCO.OBR = OBR.Parse(l);
                                    }
                                    break;
                                case "ZBR":
                                    {
                                        var lastCO = hl7.commonOrders.LastOrDefault();
                                        lastCO.ZBR = ZBR.Parse(l);
                                        pidflag = false;
                                        obrflag = true;
                                        obxflag = false;
                                    }
                                    break;
                                case "DG1": { } break;
                                case "OBX":
                                    {
                                        var obx = OBX.Parse(l);
                                        var lastCO = hl7.commonOrders.LastOrDefault();
                                        lastCO.OBR.OBXs.Add(obx);
                                        
                                        pidflag = false;
                                        obrflag = false;
                                        obxflag = true;
                                    }
                                    break;
                                case "ZBX":
                                    {
                                        var lastCO = hl7.commonOrders.LastOrDefault();
                                        var lastobx = lastCO.OBR.OBXs.LastOrDefault();
                                        lastobx.ZBX = ZBX.Parse(l);
                                    }
                                    break;
                                case "BLG":
                                    {
                                        var lastCO = hl7.commonOrders.LastOrDefault();
                                        lastCO.BLG = BLG.Parse(l);
                                    }
                                    break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex.Message + "\n" + ex.Source.ToString());
                    }
                }
                return hl7;
            }
            return null;
        }

    }
}