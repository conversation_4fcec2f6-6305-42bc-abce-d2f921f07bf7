﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// Message Header Segment
    /// </summary>
    public class MSH
    {
        public string SegmentID0 { get; set; } = "MSH";
        public string fieldSeparator1 { get; set; } = "|";
        public string encodingCharacters2 { get; set; } = @"^~\&";
        public MSHApplication sendingApplication3 { get; set; }
        public MSHApplication sendingFacility4 { get; set; }
        public MSHApplication receivingApplication5 { get; set; }= new MSHApplication { universalID = "OLIS", universalIDType = "X500" };
        public string receivingFacility6 { get; set; }
        public DateTime dateTimeOfMessage7 { get; set; } = DateTime.Now;
        public string security8 { get; set; } = string.Empty; 
        public MessageType messageType9 { get; set; }
        public string messageControlID10 { get; set; }
        public string processingID11 { get; set; }
        public string versionID12 { get; set; } = "2.3.1";
        public string sequenceNumber13 { get; set; }
        public string continuationPointer14 { get; set; }
        public string acceptAcknowledgementType15 { get; set; }
        public string applicationAcknowledgementType16 { get; set; }
        public string countryCode17 { get; set; }
        public string characterSet18 { get; set; } = @"8859/1";
        public string principalLanguageOfMessage19 { get; set; }

        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append(SegmentID0);
            sb.Append(fieldSeparator1);
            sb.Append(encodingCharacters2);
            sb.Append("|");
            sb.Append(sendingApplication3);
            sb.Append("|");
            sb.Append(sendingFacility4);
            sb.Append("|");
            sb.Append(receivingApplication5);
            sb.Append("|");
            sb.Append(receivingFacility6);
            sb.Append("|");
            sb.Append(dateTimeOfMessage7.ToString("yyyyMMddHHmmsszzz").Replace(":", ""));
            sb.Append("|");
            sb.Append(security8);
            sb.Append("|");
            sb.Append(messageType9.ToString());
            sb.Append("|");
            sb.Append(messageControlID10);
            sb.Append("|");
            sb.Append(processingID11);
            sb.Append("|");
            sb.Append(versionID12);
            sb.Append("|");
            sb.Append(sequenceNumber13);
            sb.Append("|");
            sb.Append(continuationPointer14);
            sb.Append("|");
            sb.Append(acceptAcknowledgementType15);
            sb.Append("|");
            sb.Append(applicationAcknowledgementType16);
            sb.Append("|");
            sb.Append(countryCode17);
            sb.Append("|");
            sb.Append(characterSet18);
            sb.Append("<CR>\n");
            return sb.ToString();
        }
        public static MSH Parse(string line)
        {
            line = line.Replace("<CR>", "");
            if (!string.IsNullOrEmpty(line))
            {
                string[] arr = line.Split('|');
                var msh = new MSH();
                msh.encodingCharacters2 = arr[1];
                msh.sendingApplication3 = MSHApplication.Parse(arr[2]);
                msh.sendingFacility4 = MSHApplication.Parse(arr[3]);
                msh.receivingApplication5 = MSHApplication.Parse(arr[4]);

                var addcolon = arr[6].Insert(arr[6].Length - 2, ":").Replace(" ","-");
                 
                msh.dateTimeOfMessage7 = DateTime.ParseExact(addcolon, "yyyyMMddHHmmsszzz", CultureInfo.CurrentCulture);
                msh.security8 = arr[7];
                msh.messageType9 = MessageType.Parse(arr[8]);
                msh.messageControlID10 = arr[9];
                msh.processingID11 = arr[10];
                msh.versionID12 = arr[11];
                msh.characterSet18 = arr[12];
                return msh;
            }
            return null;
        }
        
        public static MSH Test()
        {
            var msh = new MSH();
            msh.sendingApplication3 = new MSHApplication
                                      {
                                            namespaceID = string.Empty,
                                            universalID = "CN=Name of Sample External System, OU = Applications, OU = SampleOU, OU = Subscribers, DC = subscribers, DC = ssh",
                                            universalIDType = "X500"
                                      };
            msh.sendingFacility4 = new MSHApplication
            {
                universalID = "SampleConformanceID1"
            };
            msh.messageType9 = new MessageType
            {
                messageCode1 = "ORU",
                messageStructure3 = "R01",
                triggerEvent2 = "ORU_R01"
            };
            msh.messageControlID10 = "018fd1f1-c544-404f-b228-5dbdfa76a962";
            msh.processingID11 = "T";
            
            return msh;
        }
    }
}
