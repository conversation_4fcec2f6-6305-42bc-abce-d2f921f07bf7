﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    public class EventIdentifier: AssigningJurisdiction
    {
        public override string ToString()
        {
            return text2;
        }
    }
    /// <summary>
    /// ERQ -Segment
    /// </summary>
    
    public class ERQ
    {
        public string SegmentID0 { get; set; } = "ERQ";
        public string QueryTag1 { get; set; }
        public EventIdentifier EventIdentifier2 { get; set; }
        public string InputParameterList3 { get; set; }


        public override string ToString()
        {
            var s = new StringBuilder();
            s.Append(SegmentID0);
            s.Append("|");
            s.Append(QueryTag1);
            s.Append("|");
            s.Append(EventIdentifier2.ToString());
            s.Append("|");
            s.Append(InputParameterList3);
            s.Append("<CR>");
            return s.ToString();
        }
        public static ERQ Parse(string line) 
        {
            try
            {
                if (!string.IsNullOrEmpty(line))
                {
                    line = line.Replace("<CR>", "");
                    string[] arr = line.Split('|');
                    var erq = new ERQ();
                    if (erq.SegmentID0.Equals(arr[0]))
                    {
                        erq.QueryTag1 = arr[1] == "" ? null : arr[1];
                        var ei = new EventIdentifier();
                        ei.text2 = arr[2];
                        erq.InputParameterList3 = arr[3];
                        return erq;
                    }
                }
            }catch (Exception ex)
            {
                throw new Exception("ERQ Parse Exception. " + ex.Message);
            }
            return null;
        }

    }
}
