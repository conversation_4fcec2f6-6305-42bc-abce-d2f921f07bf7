﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// This field identifies the type of the message, and therefore determines the structure of the remainder of the
    /// message following the MSH segment
    /// Order Message: ORM^O01^ORM_O01
    /// Acknowledgement Message Type:ORR^O02^O02
    /// </summary>
    public class MessageType
    {
        public string messageCode1 { get; set; }
        public string triggerEvent2 { get; set; }
        public string messageStructure3 { get; set; }

        public override string ToString()
        {
            var mt = new StringBuilder();
            mt.Append(messageCode1);
            mt.Append("^");
            mt.Append(triggerEvent2);
            mt.Append("^");
            mt.Append(messageStructure3);
            return mt.ToString();
        }

        public static MessageType Parse(string line)
        {
            if (!string.IsNullOrEmpty(line))
            {
                string[] arr = line.Split('^');
                var mt = new MessageType();
                mt.messageCode1 = arr[0];
                mt.triggerEvent2 = arr[1];
                mt.messageStructure3 = arr[2];
                return mt;
            }
            return null;
        }
    }
}
