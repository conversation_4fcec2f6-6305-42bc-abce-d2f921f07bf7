﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum30.Areas.Labs.Models.OLIS
{
    /// <summary>
    /// This field is required for proper patient identification except for non-nominal testing.
    /// Some components of the patient name have an optionality of “RE” to support patient names that do not contain
    /// the component(e.g., a patient may not have a second name).
    /// Note that the PID.5.2 First Name has an optionality of “RE”. The first name must be submitted for those patients
    /// who have a first name, but the optionality is “RE” to allow for a small but significant number of patients who do not
    /// have a first name.
    /// </summary>
    public class Name
    {
        public string lastName1 { get; set; }
        public string firstName2 { get; set; }
        public string middleName3 { get; set; }
        public string suffix4 { get; set; }
        public string prefix5 { get; set; }
        public string degree6 { get; set; }
        public string nameTypeCode7 { get; set; }
        public override string ToString()
        {
            var n = new StringBuilder();
            n.Append(lastName1.TrimIt());
            n.Append("^");
            n.Append(firstName2.TrimIt());
            n.Append("^");
            n.Append(middleName3.TrimIt());
            n.Append("^");
            n.Append(suffix4.TrimIt());
            n.Append("^");
            n.Append(prefix5.TrimIt());
            n.Append("^");
            n.Append(degree6.TrimIt());
            n.Append("^");
            n.Append(nameTypeCode7.TrimIt());
            return n.ToString();
        }
        public static Name Parse(string nm)
        {

            string[] nmArr = nm.Split('^');
            var nmObj = new Name();

            nmObj.lastName1 = nmArr[0];
            nmObj.firstName2 = nmArr[1];
            if (nmArr.Length > 2)
            {
                nmObj.middleName3 = nmArr[2];
                nmObj.suffix4 = nmArr[3];
                nmObj.prefix5 = nmArr[4];
                nmObj.degree6 = nmArr[5];
            }
            return nmObj;
        }
    }
}
