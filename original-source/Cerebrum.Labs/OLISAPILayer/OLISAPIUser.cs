﻿using Cerebrum.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.OLISAPILayer
{   
    public interface IOLISAPIUser
    {
        string GetUserName(int userId);
    }
    public class OLISAPIUser : IOLISAPIUser
    {
        private CerebrumContext _context;
        public OLISAPIUser(CerebrumContext context)
        {
            _context = context;
        }
        public string GetUserName(int userId)
        {
            var user = _context.Users.FirstOrDefault(fd => fd.UserID == userId);
            return user != null ? user.UserName : string.Empty;
        }
    }
}
