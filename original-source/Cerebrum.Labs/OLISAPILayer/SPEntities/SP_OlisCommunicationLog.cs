﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.OLISAPILayer.SPEntities
{
    public class SP_OlisCommunicationLog
    {
        public int LogId { get; set; }
        public int PracticeId { get; set; }
        public int OfficeId { get; set; }
        public int userId { get; set; }        
        public string QueryType { get; set; }
        public int? PatientRecordId { get; set; }        
        public string RequestingHIC { get; set; }        
        public string OLISTranscationID { get; set; }
        public EMRToOLISQueryType EMRQueryType { get; set; }
        public bool consentViewOverride { get; set; }        
        public string ConsentComment { get; set; }
        public Guid MSH10ClientTransactionID { get; set; }        
        public string MessageType { get; set; }// Request or Response
        public bool IsSuccessStatusCode { get; set; }        
        public string Message { get; set; }
        public DateTime createdDateTime { get; set; }
        public int? OLISCommunicationLogId { get; set; }        
        public string Error { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime? ToDate { get; set; }

        public string UserName { get; set; }
        public string OfficeName { get; set; }
        public string DoctorFirstName { get; set; }
        public string DoctorLastName { get; set; }

        public int? ReportId { get; set; }
        public string PatientName { get; set; }
        public string OLISAccessionNumber { get; set; }
        public string CerebrumAccessionNumber { get; set; }
        public OLISPatientReportStatus ReportStatus { get; set; }
        public string RejectReason { get; set; }

        public bool IsReceived { get; set; }
        public int TotalRecords { get; set; }

    }
}
