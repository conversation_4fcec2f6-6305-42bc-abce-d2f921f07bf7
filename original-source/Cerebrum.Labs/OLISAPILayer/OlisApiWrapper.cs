﻿using Cerebrum.DTO.OLIS;
using Cerebrum.Labs.OLIS;
using Cerebrum.ViewModels.OLIS;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace Cerebrum.Labs.OLISAPILayer
{
    public interface IOlisApiWrapper
    {
        Task<string> OLIS_Health_Check_Async();
        Task<VMOlisLogsMain> GetLogsMainAsync(VMOLISLogSearch search);
        Task<string> GetOLISSavedReportAsync(int reportid, int practiceId, int userid, string ipaddress);
        Task<string> RejectReportAsync(int reportReceivedId, int pid_setid, string reason, int practiceId, int userid, string ipaddress);
        Task<VMOLISPreviewReport> GetReportPreviewAsync(int reportid, int practiceId, int userid, string ipaddress);
        Task<VMOLISPreviewReport> GetReportPreviewFilterAsync(VMOLISReportFilter request);
        Task<List<OLISQueryResponse>> SendOLISZ01RequestAsync(VMOLISSearch request);
        Task<List<OLISHL7Reports>> SendOLISZ01ConsentBlockOverrideRequestAsync(VMOLISConsentReinstatement request);
        Task<List<OLISQueryResponse>> SendOLISZ04RequestAsync(VMOLISSearch request);
        Task<List<VMOLISReportSave>> ReportsSaveAllAsync(int reportReceivedId, int practiceId, int practiceDoctorId, bool isDoctor, int userId, string ipAddress, bool markseen);
        Task<VMOLISReportSave> ReportSaveAsync(int reportReceivedId, int practiceId, int practiceDoctorId, bool isDoctor, int pid_setid, int userId, string ipAddress, bool markseen, List<string> requestedtests, List<string> results);
    }
    public class OlisApiWrapper: IOlisApiWrapper
    {
        private log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private IHttpClientFactory _httpClientFactory;
        public string olisApiBaseUrl { get; private set; }
        public bool hasApiBaseUrl { get; private set; }

        public OlisApiWrapper(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
            init();
        }
        public async Task<string> OLIS_Health_Check_Async()
        {
            string health_check = string.Empty;

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-upload");
                setHeader(httpClient);
                HttpResponseMessage response = await httpClient.GetAsync("api/OlisQuery/healthcheck/");
                response.EnsureSuccessStatusCode();
                health_check = await response.Content.ReadAsAsync<string>();
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("OLIS API Exception.", e);
            }

            return health_check;
        }
        public async Task<VMOlisLogsMain> GetLogsMainAsync(VMOLISLogSearch search)
        {
            var main = new VMOlisLogsMain();

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-download");
                setHeader(httpClient);
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/olis/communicationlogs", search);
                response.EnsureSuccessStatusCode();

                main = await response.Content.ReadAsAsync<VMOlisLogsMain>();
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("OLIS API Exception.", e);
            }

            return main;
        }
        public async Task<string> GetOLISSavedReportAsync(int reportid, int practiceId, int userid, string ipaddress)
        {
            //var rptlst = new VMOlisReport();
            ReportRequest request = new ReportRequest();
            request.ReportId = reportid;
            request.PracticeId = practiceId;
            request.UserId = userid;
            request.IpAddress = ipaddress;

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-download");
                setHeader(httpClient);
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/olisreports/report", request);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();//<VMOlisReport>();
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("OLIS API Exception.", e);
            }
        }
        public async Task<string> RejectReportAsync(int reportReceivedId, int pid_setid, string reason, int practiceId, int userid, string ipaddress)
        {
            var request = new RejectReportRequest();
            request.ReportId = reportReceivedId;
            request.pid_setid = pid_setid;
            request.reason = reason;
            request.PracticeId = practiceId;
            request.UserId = userid;
            request.IpAddress = ipaddress;

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-download");
                setHeader(httpClient);
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/olisreports/rejectreport/", request);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("OLIS API Exception.", e);
            }

        }
        public async Task<VMOLISPreviewReport> GetReportPreviewAsync(int reportid, int practiceId, int userid, string ipaddress)
        {
            var rptlst = new VMOLISPreviewReport();
            var request = new ReportPreviewRequest();
            request.ReportId = reportid;
            request.PracticeId = practiceId;
            request.UserId = userid;
            request.IpAddress = ipaddress;

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-download");
                setHeader(httpClient);
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/olisreports/preview/", request);
                response.EnsureSuccessStatusCode();

                rptlst = await response.Content.ReadAsAsync<VMOLISPreviewReport>();
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("OLIS API Exception.", e);
            }

            return rptlst;
        }

        public async Task<VMOLISPreviewReport> GetReportPreviewFilterAsync(VMOLISReportFilter request)
        {
            var rptlst = new VMOLISPreviewReport();

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-download");
                setHeader(httpClient);
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/olisreports/preview/filter", request);
                response.EnsureSuccessStatusCode();

                rptlst = await response.Content.ReadAsAsync<VMOLISPreviewReport>();
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("OLIS API Exception.", e);
            }

            return rptlst;
        }
        public async Task<List<OLISQueryResponse>> SendOLISZ01RequestAsync(VMOLISSearch request)
        {
            var responseLst = new List<OLISQueryResponse>();

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-upload");
                setHeader(httpClient);
                _log.Debug($"Sending request to : {httpClient.BaseAddress}");
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/OlisQuery/SendOLISZ01Request/", request);
                response.EnsureSuccessStatusCode();
                _log.Debug($"Response Received status code: {response.StatusCode}");
                responseLst = await response.Content.ReadAsAsync<List<OLISQueryResponse>>();
                _log.Debug($"Response Received : {responseLst.Count()}");
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("Z01 OLIS API Exception.", e);
            }

            return responseLst;
        }
        public async Task<List<OLISHL7Reports>> SendOLISZ01ConsentBlockOverrideRequestAsync(VMOLISConsentReinstatement request)
        {
            List<OLISHL7Reports> responseLst = new List<OLISHL7Reports>();

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-upload");
                setHeader(httpClient);
                _log.Debug($"Sending consent block override request to : {httpClient.BaseAddress}");
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/OlisQuery/SendOLISZ01ConsentBlockOverrideRequest/", request);
                response.EnsureSuccessStatusCode();
                _log.Debug($"Response Received status code: {response.StatusCode}");
                responseLst = await response.Content.ReadAsAsync<List<OLISHL7Reports>>();
                _log.Debug($"consent block override Response Received : {responseLst.Count()}");
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("Consent block override OLIS API Exception.", e);
            }

            return responseLst;
        }
        public async Task<List<OLISQueryResponse>> SendOLISZ04RequestAsync(VMOLISSearch request)
        {
            var responseLst = new List<OLISQueryResponse>();

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-upload");
                setHeader(httpClient);
                _log.Debug($"Sending request to : {httpClient.BaseAddress}");
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/OlisQuery/SendOLISZ04Request/", request);
                response.EnsureSuccessStatusCode();
                _log.Debug($"Response Received status code: {response.StatusCode}");
                responseLst = await response.Content.ReadAsAsync<List<OLISQueryResponse>>();
                _log.Debug($"Response Received : {responseLst.Count()}");
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("Z04 OLIS API Exception.", e);
            }

            return responseLst;
        }

        public async Task<List<VMOLISReportSave>> ReportsSaveAllAsync(int reportReceivedId, int practiceId, int practiceDoctorId, bool isDoctor, int userId, string ipAddress, bool markseen)
        {
            var save = new List<VMOLISReportSave>();
            var saveRequest = new ReportSaveAll();
            saveRequest.reportReceivedId = reportReceivedId;
            saveRequest.PracticeId = practiceId;
            saveRequest.PracticeDoctorId = practiceDoctorId;
            saveRequest.IsDoctor = isDoctor; // TODO: Divyesh needs to check this
            saveRequest.UserId = userId;
            saveRequest.IpAddress = ipAddress;
            saveRequest.markseen = markseen;

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-download");
                setHeader(httpClient);
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/olisreports/saveall", saveRequest);
                response.EnsureSuccessStatusCode();

                save = await response.Content.ReadAsAsync<List<VMOLISReportSave>>();
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
                throw new Exception("OLIS API Exception.", e);
            }

            return save;
        }

        public async Task<VMOLISReportSave> ReportSaveAsync(int reportReceivedId, int practiceId, int practiceDoctorId, bool isDoctor, int pid_setid, int userId, string ipAddress, bool markseen, List<string> requestedtests, List<string> results)
        {
            var save = new VMOLISReportSave { Success = false, Message = "OLIS report save error. Please contact admin" };
            var saveRequest = new ReportSave();
            saveRequest.reportReceivedId = reportReceivedId;
            saveRequest.PracticeId = practiceId;
            saveRequest.PracticeDoctorId = practiceDoctorId;
            saveRequest.IsDoctor = isDoctor; // TODO: Divyesh needs to check this
            saveRequest.UserId = userId;
            saveRequest.IpAddress = ipAddress;
            saveRequest.markseen = markseen;
            saveRequest.requestedtests = requestedtests;
            saveRequest.results = results;
            saveRequest.pid_set = pid_setid;

            try
            {
                var httpClient = _httpClientFactory.CreateClient("olis-download");
                setHeader(httpClient);
                HttpResponseMessage response = await httpClient.PostAsJsonAsync("api/olisreports/save", saveRequest);
                response.EnsureSuccessStatusCode();

                var resp = await response.Content.ReadAsAsync<List<VMOLISReportSave>>();

                if (resp != null)
                {
                    return resp.FirstOrDefault();
                }
                return save;
            }
            catch (HttpRequestException e)
            {
                _log.Error(e);
            }
            catch (Exception e)
            {
                _log.Error(e);
            }

            return save;
        }
        private void setHeader(HttpClient c)
        {
            c.BaseAddress = new Uri(olisApiBaseUrl);
            c.DefaultRequestHeaders.Accept.Clear();
            c.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }
        private void init()
        {
            olisApiBaseUrl = ConfigurationManager.AppSettings["OLISAPIurl"];
        }
    }
}
