﻿using System;
using System.Linq;
using System.IO;
using Cerebrum.Data;
using Cerebrum.Labs.LifeLabs.Models;
using Cerebrum.Labs.common;
using System.Collections.Generic;
using AwareMD.Cerebrum.Shared.Enums;
using System.Configuration;
using System.Transactions;
using Cerebrum.Labs.Service;
using Cerebrum.ViewModels.OLIS;
using Cerebrum.ViewModels.HL7;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;

namespace Cerebrum.Labs.LifeLabs.services
{
    /// <summary>
    /// LifeLab Report Service - Parse Report and save
    /// </summary>
    public class LifeLabReportService : LabReportServiceBase
    {
        public List<VMOLISReportSave> ResponseSaved;
        private BLL.Requisition.IRequisitionBLL _requisitionBll;
        private int reportSaved;
        private List<HL7LostReportDoctor> HL7LostReportDoctors = null;
        private string _auditLogFileName = string.Empty;
        public Dictionary<int, string> MissedDoctors = new Dictionary<int, string>();

        public LifeLabReportService(int userId, string ipAddress, string filePath = "") :
            base(userId, ipAddress, filePath)
        {
            _requisitionBll = new BLL.Requisition.RequisitionBLL(_context);
            string report = ConfigurationManager.AppSettings["HL7LabResultFiles"].ToString();
            string resultFolder = ConfigurationManager.AppSettings["HL7LabResultFiles"].ToString();
            _auditLogFileName = ConfigurationManager.AppSettings["LifeLabsAuditFilePath"]?.ToString();// + "\\lifelabs\\CURHST.0";
            this.ERROR_PATH = Path.Combine(resultFolder, "HL7error");
        }

        /// <summary>
        /// Save Report to database
        /// </summary>
        override public bool SaveUploadReport(string _filePath)
        {
            ResponseSaved = new List<VMOLISReportSave>();

            LLReadHL7File _ll;
            try
            {
                reportSaved = 0;

                _ll = new LLReadHL7File();
                List<LLHL7Report> reports = IsValidPath(_filePath) ? _ll.ReadFile(_filePath) : _ll.ReadText(_filePath);
                int cnt = reports.Count();
                string tmpAuditFileName = System.IO.Path.GetTempFileName();
                _log.Info("====================START====================");
                var fitx = IsValidPath(_filePath) ? Path.GetFileName(_filePath) : "text";
                _log.Info($"Total {cnt} Report in File: {fitx}");

                foreach (var r in reports)
                {
                    SaveReport(r, _filePath);
                    var auditLog = LifeLabsAuditLogs(r);
                    AppendToLogFile(auditLog, tmpAuditFileName);
                }
                try
                {
                    string lockFName = Path.GetDirectoryName(_auditLogFileName) + @"\busy.txt";
                    while (File.Exists(lockFName))
                    {
                        Thread.Sleep(5000);
                    }
                    AppendFile2File(_auditLogFileName, tmpAuditFileName);
                    File.Delete(tmpAuditFileName);
                }
                catch (Exception ex)
                {
                    _log.Error("LifeLab Audit Log file Error", ex);
                }
                _log.Info($"Saved {reportSaved} reports from {cnt}");
                _log.Info("==================== END ====================");

                //Set PracticeId=0
                PracticeId = 0;
            }
            catch (Exception ex)
            {
                _log.Error($"{_filePath} Life Lab File Reading Error.", ex);
            }
            try
            {
                // Report not saved, move file to Error folder
                if (File.Exists(_filePath))
                {
                    string filename = Path.GetFileName(_filePath);
                    var newFilePath = Path.Combine(ERROR_PATH, filename);
                    if (File.Exists(newFilePath))
                    {
                        string dt = DateTime.Now.ToString("yyyyMMddHHmmss");
                        filename = $"{filename}~{dt}";
                        newFilePath = Path.Combine(ERROR_PATH, filename);
                    }
                    File.Move(_filePath, newFilePath);
                }
            }
            catch (Exception ex)
            {
                _log.Error($"HL7 file Moving error. {_filePath}", ex);
            }
            return true;
        }

        public bool SaveUploadTextReport(int HL7LostReportId)
        {
            ResponseSaved = new List<VMOLISReportSave>();
            LLReadHL7File _ll;
            try
            {
                var lostReport = _context.HL7LostReports.FirstOrDefault(w => w.Id == HL7LostReportId && w.HL7LostReportDoctors.All(a => a.ExternalDoctorId > 0));

                if (lostReport != null && lostReport.HL7File != null)
                {
                    HL7LostReportDoctors = lostReport.HL7LostReportDoctors.ToList();
                    PracticeId = (int)lostReport.HL7LostReportDoctors.FirstOrDefault(fd => fd.PracticeId > 0).PracticeId;

                    byte[] decompressed = Cerebrum.Labs.common.CompressFile.Decompress(lostReport.HL7File);
                    Encoding iso = Encoding.GetEncoding("ISO-8859-1");
                    string text = Encoding.Default.GetString(decompressed);
                    if (!string.IsNullOrWhiteSpace(text))
                    {
                        _ll = new LLReadHL7File();
                        List<LLHL7Report> reports = _ll.ReadText(text);
                        int cnt = reports.Count();

                        _log.Info("==================T==START==T==================");
                        _log.Info($"Total {cnt} Report in text File");
                        reportSaved = 0;

                        foreach (var r in reports)
                        {
                            SaveReport(r, filePath);
                        }

                        _log.Info($"Saved {reportSaved} reports from {cnt}");
                        _log.Info("==================T== END ==T==================");
                        lostReport.ReportSaved = true;
                        lostReport.HL7File = null;
                        _context.SaveChanges(UserId, IPAddress);
                    }
                }
                //Set PracticeId=0
                PracticeId = 0;
                HL7LostReportDoctors = null;
            }
            catch (Exception ex)
            {
                _log.Error($"{filePath} Life Lab File Reading Error.", ex);
            }
            return true;
        }

        private void SaveReport(LLHL7Report r, string filePath)
        {
            var alldocs = Doctors(r.ZCLs9, 0);

            var allPractices = alldocs.Where(w => w.PracticeId > 0).Select(s => s.PracticeId).Distinct().ToList();

            if (!allPractices.Any())
            {
                _log.Info($"Practice not found. Report rejected");
                if (!string.IsNullOrWhiteSpace(filePath))
                    AddToLostReport(r, filePath);
                return;
            }
            DateTime currentDateTime = DateTime.Now;

            foreach (var practiceId in allPractices)
            {
                PracticeId = (int)practiceId;

                using (TransactionScope transactionScope = TransactionUtils.CreateTransactionScope())
                {
                    try
                    {

                        _log.Info($"Acc#:{r.MSH1.messageControlID10} Patient:{r.PID2.patientName5.familyName}");
                        //PracticeId = PracticeId > 0 ? PracticeId : PhysicianPractice(r.ZCLs9, r.PV13);

                        var msg = HL7Message(r.MSH1, filePath, currentDateTime);
                        _context.HL7Messages.Add(msg);
                        _context.SaveChanges(UserId, IPAddress);

                        var pat = HL7Patient(r.PID2);
                        pat.createdDate = currentDateTime;

                        _log.Info($" {pat.healthCardNo}  {pat.familyName}");

                        pat.HL7MessageId = msg.Id;
                        _context.HL7Patients.Add(pat);
                        _context.SaveChanges(UserId, IPAddress);

                        var z = r.ZCTs12.FirstOrDefault();
                        #region Status
                        string reportStatus = string.Empty;
                        if (r.ZFRs7 != null && r.ZFRs7.Count() > 0)
                        {
                            var status = r.ZFRs7.FirstOrDefault().LLReportFormStatus3;
                            reportStatus = ReportStatus(status);
                        }
                        #endregion

                        #region Check Duplicate
                        var AccessionCheck = AccessionNumberExists(pat.healthCardNo, z.OBR.observationDateTime7, z.OBR.specimenReceivedDateTime14, "LifeLabs", r.MSH1.messageControlID10, PracticeId);
                        if (AccessionCheck.IsExists && reportStatus.ToLower().Equals("final"))
                        {
                            var msgs = $"Report Already Exists, File: {filePath},status:{reportStatus}, Accession {r.MSH1.messageControlID10}, CerebrumAccession: {AccessionCheck.CerebrumAccession}";
                            ResponseSaved.Add(new VMOLISReportSave { ReportAlreadyExists = true, Message = msgs });
                            _log.Info(msgs);
                            //continue;
                        }
                        #endregion Check Duplicate
                        if (AccessionCheck.TotalReportVersions < REPORT_VERSIONS_COUNT_SETTING)
                        {
                            var hl7rpt = HL7OBR(z, pat.Id, r.MSH1.messageControlID10, AccessionCheck.CerebrumAccession, PracticeId);
                            hl7rpt.createdDate = currentDateTime;
                            hl7rpt.status = reportStatus;

                            hl7rpt.assignmentStatus = ReportAssignedStatus(PracticeId, pat.PatientRecordId);

                            var zclient = r.PV13.admittingDoctor17;

                            hl7rpt.physicianNo = zclient.requisitionPhysicianCode;
                            hl7rpt.physicianName = zclient.familyName;

                            hl7rpt.reviewerNames = CombineReviewers(r.ZCLs9);

                            if (hl7rpt.Id <= 0)
                            {
                                _context.HL7Reports.Add(hl7rpt);
                                _context.SaveChanges(UserId, IPAddress);
                            }


                            foreach (var d in alldocs)
                            {
                                var doctorExists = _context.HL7ReportDoctors.FirstOrDefault(w => w.HL7ReportId == hl7rpt.Id && (w.ExternalDoctorId == d.ExternalDoctorId));
                                if (doctorExists == null)
                                {
                                    var dbdoc = new HL7ReportDoctor { HL7ReportId = hl7rpt.Id, ExternalDoctorId = d.ExternalDoctorId, practiceDoctorId = d.practiceDoctorId, IsPrimary = d.IsPrimary, createdDate = currentDateTime };
                                    _context.HL7ReportDoctors.Add(dbdoc);
                                    _context.SaveChanges(UserId, IPAddress);
                                }
                            }

                            var reportVersion = new HL7ReportVersion
                            {
                                HL7Report = hl7rpt,
                                HL7MessageId = msg.Id,
                                priority = hl7rpt.priority,
                                status = hl7rpt.status,
                                requestedDateTime = hl7rpt.requestedDateTime,
                                physicianNo = hl7rpt.physicianNo,
                                physicianName = hl7rpt.physicianName,
                                resultedDateTime = hl7rpt.resultedDateTime,
                                createdDate = currentDateTime,
                            };
                            _context.HL7ReportVersions.Add(reportVersion);
                            _context.SaveChanges(UserId, IPAddress);

                            foreach (var zct in r.ZCTs12.OrderBy(od => od.SetId))
                            {
                                var obr = zct.OBR;
                                var obxs = zct.OBXs;
                                foreach (var obx in obxs)
                                {
                                    try
                                    {
                                        var hl7r = HL7Result(obx, obr.observationDateTime7);
                                        if (hl7r != null)
                                        {
                                            hl7r.LabTestRequestCode = string.IsNullOrWhiteSpace(obr.univarsalServiceID4) ? null : obr.univarsalServiceID4;
                                            hl7r.HL7ReportVersionId = reportVersion.Id;
                                            hl7r.createdDate = currentDateTime;
                                            _context.HL7Results.Add(hl7r);
                                            _context.SaveChanges(UserId, IPAddress);

                                            var notes = HL7ResultNote(obx.NTEs, hl7r.Id);
                                            if (notes != null && notes.Count() > 0)
                                            {
                                                foreach (var rn in notes)
                                                {
                                                    rn.createdDate = currentDateTime;
                                                    _context.HL7ResultNotes.Add(rn);
                                                }

                                                _context.SaveChanges(UserId, IPAddress);
                                            }
                                            ++reportSaved;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _log.Error($"Life Lab File Reading Error in obx", ex);
                                    }

                                }
                                //HL7 file patient exists, remove file
                                //in MSH the file has been compressed and moved to transferred folder.
                                //if(File.Exists(filePath))
                                //    File.Delete(filePath);
                            }
                            if (reportSaved > 0)
                            {
                                transactionScope.Complete();
                                File.Delete(filePath);
                            }
                            ResponseSaved.Add(new VMOLISReportSave { ReportAlreadyExists = false, Message = "Report Saved Successfully", ReportId = hl7rpt.Id, PatientName = pat.familyName });
                        }
                        else
                        {
                            _log.Info($"Report Already Exists, File: {filePath},status:{reportStatus}, Accession {r.MSH1.messageControlID10}, CerebrumAccession: {AccessionCheck.CerebrumAccession} VersionCount: {AccessionCheck.TotalReportVersions}");
                            transactionScope.Dispose();
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Info($"Error processing report. Report not saved", ex);
                    }
                }
            }
        }
        private LifeLabsAuditLog LifeLabsAuditLogs(LLHL7Report r)
        {
            var al = new LifeLabsAuditLog();
            var p = r.PID2;
            var msgDate = DateTime.Parse(r.MSH1.dateTimeOfMessage7.ToString());

            al._MessageDate = msgDate.ToString("dd-MMM-yyyy");
            al._MessageTime = msgDate.ToString("hh:mm:ss");
            String healthCardNo = p.patientSSN19, _healthCardSer = "";
            ProcessHealthCardNo(ref healthCardNo, ref _healthCardSer);
            al._healthCardNo = healthCardNo;
            al._healthCardSer = _healthCardSer;
            al._PatientName = p.patientName5.familyName + " " + p.patientName5.givenName;

            string[] ids = r.MSH1.messageControlID10.Split(new Char[] { '-' });
            if (ids.Length > 1)
            {
                al._accNum = ids[1];
            }
            else if (ids.Length > 0)
            {
                al._accNum = ids[0];
            }
            if (r.ZFRs7 != null)
            {
                foreach (var reportZFR in r.ZFRs7)
                {
                    al._fstatus.Add(("0" == reportZFR.LLReportFormStatus3) ? "P" : "F");
                    al._ftype.Add(RetriveFormType(reportZFR.LLReportForm2));
                }
            }
            return al;
        }
        /// <summary>
        ///  Save LifeLabs Audit file
        /// </summary>
        private void AppendToLogFile(LifeLabsAuditLog a, string tmpAuditFilePath)
        {
            // sa
            string path = tmpAuditFilePath;
            string line = "";

            DateTime dt = DateTime.Now;

            string[] vars = {
                a._date,
                a._time,
                a._logid,
                "", //_fstatus,
                "", //_ftype,
                a._accNum,
                a._healthCardNo,
                a._healthCardSer,
                a._PatientName,
                a._OrderingClient,
                a._MessageDate,
                a._MessageTime
            };

            /*
            Processed Date 		11 chars 
            Processed Time		8 chars
            Log ID			7 chars – REC for Received report
            Form Status		1 char – F for final – P for partial
            Form Type		1 char – See ***
            Accession		9 chars – MSH-10 second hyphen piece
            Health Card Number	10 chars – PID-19 strip off X
            Health Card Version Code	2 chars – PID-19
            Patient Name		61 chars
            Ordering Client #		8 chars – PV1-17 
            Message Date		11 chars - **
            Message Time		8 chars    - **
             */

            a._date = a._date.PadRight(11);
            a._time = a._time.PadRight(8);
            a._logid = a._logid.PadRight(7);
            //_fstatus       =  _fstatus       .PadRight( 1);
            //_ftype         =  _ftype         .PadRight( );
            a._accNum = a._accNum.PadRight(9);
            a._healthCardNo = a._healthCardNo.PadRight(10);
            a._healthCardSer = a._healthCardSer.PadRight(2);
            a._PatientName = a._PatientName.PadRight(61);
            a._OrderingClient = a._OrderingClient.PadRight(8);
            a._MessageDate = a._MessageDate.PadRight(11);
            a._MessageTime = a._MessageTime.PadRight(8);

            //line = string.Format("{0:11}  {1:8}  {2:7}  {3:1}  {4:1}  {5:9}  {6:10}  {7:2}  {8:61}  {9:8}  {10:11}  {11:8}", vars);

            int idx = 0;
            foreach (string ftype in a._ftype)
            {
                string fstatus = "P";

                if (a._fstatus.Count > idx)
                    fstatus = a._fstatus[idx];

                line = a._date
                        + "  " + a._time
                        + "  " + a._logid
                        + "  " + fstatus
                        + "  " + ftype
                        + "  " + a._accNum
                        + "  " + a._healthCardNo
                        + "  " + a._healthCardSer
                        + "  " + a._PatientName
                        + "  " + a._OrderingClient
                        + "  " + a._MessageDate
                        + "  " + a._MessageTime
                        + "\n";
                idx++;

                try
                {
                    File.AppendAllText(path, line);
                }
                catch (Exception ex)
                {
                    _log.Error("Error While Creating an lifelabs audit file", ex);
                }
            }

        }
        private void AppendFile2File(string auditFileName, string tmpAuditFileName)
        {
            if (File.Exists(tmpAuditFileName))
            {
                string readText = File.ReadAllText(tmpAuditFileName);
                File.AppendAllText(auditFileName, readText, Encoding.UTF8);
            }
        }
        private string CombineReviewers(List<ZCL> ccDoctors)
        {
            if (ccDoctors == null) return "";
            var ccNames = (from item in ccDoctors select item.consultingDoctor2.familyName).ToList().Distinct().ToList();
            string s = string.Join(", ", ccNames);

            // make sure the length less than 500 - corr. DB field size
            if (s.Length > 500) s = s.Substring(0, 499);

            return s;
        }
        private DocumentAssignmentStatuses ReportAssignedStatus(int practiceId, int patientRecordId)
        {
            try
            {
                if (!Is_TBD_Patient(practiceId, patientRecordId))
                {
                    if (!_requisitionBll.BloodUrineRequisitionExist(patientRecordId))
                    {

                        _log.Info($"OLIS Report patient Requisition found");

                        return DocumentAssignmentStatuses.Assigned;
                    }
                    else
                    {
                        _log.Info($"OLIS Report patient Requisition not found");
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error($"ERROR in ReportAssignedStatus", ex);
            }
            return DocumentAssignmentStatuses.Unassigned;
        }
        private bool Is_TBD_Patient(int practiceId, int patientRecordId)
        {
            var tbd = _context.Demographics.FirstOrDefault(f => f.PatientRecord.PracticeId == practiceId && f.firstName.ToLower().Equals("tbd_patient"));
            return tbd != null ? tbd.PatientRecordId == patientRecordId : false;
        }
        private string ReportStatus(string status)
        {
            switch (status)
            {
                case "0":
                    return "Partial";
                case "1":
                    return "Final";
            }
            return string.Empty;
        }

        private void AddToLostReport(LLHL7Report report, string filePath)
        {
            _log.Warn($" X X X X X X X X X X X X X X X X X X X X X X {report.MSH1.messageControlID10} X X X X X X X X X X X X X X X X X X X X X X X X X X X");
            try
            {
                var accession = report.MSH1.messageControlID10;
                string patientOHIP = null;
                if (!string.IsNullOrEmpty(report.PID2.patientSSN19))
                {
                    var hc = report.PID2.patientSSN19.StartsWith("X") ? report.PID2.patientSSN19.Substring(1, report.PID2.patientSSN19.Length - 1) : "";
                    string[] h = hc.Split(' ');
                    var hcard = h.Length == 2 ? h[0] : hc;
                    var hver = h.Length == 2 ? h[1].Trim() : null;
                    patientOHIP = $"{hcard} {hver}";
                }

                var file = IsValidPath(filePath) ? CompressFile.EncodeCompressArray(filePath) : CompressFile.EncodeCompressString(filePath);
                //byte[] decompressed = Cerebrum.Labs.common.CompressFile.Decompress(f.Message);
                var lostReport = new HL7LostReport();
                lostReport.Lab = "LifeLabs";
                lostReport.AccessionNumber = accession;
                lostReport.PatientOHIP = patientOHIP;
                lostReport.PatientFirstName = report.PID2.patientName5.givenName;
                lostReport.PatientLastName = report.PID2.patientName5.familyName;
                lostReport.LastModifiedByUserId = UserId;
                lostReport.HL7File = file;
                lostReport.ReportSaved = false;
                lostReport.IsActive = true;
                var doctors = new List<HL7LostReportDoctor>();

                foreach (var c in report.ZCLs9)
                {
                    var physician = c.consultingDoctor2;
                    var physicianNo = physician.requisitionPhysicianCode;
                    var doctor = new HL7LostReportDoctor();
                    doctor.LastName = physician.Name2;
                    doctor.FirstName = physician.Name1;
                    doctor.OHIPOrGroup = physician.requisitionPhysicianCode;
                    doctor.Address = physician.Name2;
                    doctor.LastModifiedByUserId = UserId;
                    doctor.Address = $"{c.clientAddress3.addressLine11} {c.clientAddress3.addressLine22} {c.clientAddress3.addressLine33} {c.clientAddress3.province4} {c.clientAddress3.postalCode5}";
                    doctor.ReportSaved = false;
                    doctors.Add(doctor);
                }
                // check if report already exists, active and not saved
                // if report already saved means patient and dr's practiced matched by user
                bool reportExists = _context.HL7LostReports.Any(a => a.AccessionNumber.Trim().ToLower().Equals(accession) && a.IsActive == true && a.ReportSaved == false);

                using (TransactionScope transactionScope = TransactionUtils.CreateTransactionScopeSupress())
                {
                    try
                    {
                        _context.HL7LostReports.Add(lostReport);
                        _context.SaveChanges(UserId, IPAddress);

                        if (doctors.Any())
                        {
                            foreach (var d in doctors)
                            {
                                d.HL7LostReportId = lostReport.Id;
                                _context.HL7LostReportDoctors.Add(d);
                            }
                            _context.SaveChanges(UserId, IPAddress);
                        }
                        transactionScope.Complete();
                    }
                    catch (Exception e)
                    {
                        _log.Error("Add to Lost Report Transaction Error", e);
                        transactionScope.Dispose();
                    }
                    finally
                    {
                        transactionScope.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error("Add to Lost Report Error");
                _log.Error(ex);
            }
            _log.Warn($" X X X X X X X X X X X X X X X X X X X X X X {report.MSH1.messageControlID10} X X X X X X X X X X X X X X X X X X X X X X X X X X X");
        }
        private HL7Message HL7Message(MSH m, string filepath, DateTime currentDateTime)
        {
            return HL7Message(filepath,
                            m.sendingApplication3,
                            m.type9,
                            m.dateTimeOfMessage7,
                            currentDateTime);
        }
        private HL7Patient HL7Patient(PID p)
        {
            try
            {
                var hc = p.patientSSN19.StartsWith("X") ? p.patientSSN19.Substring(1, p.patientSSN19.Length - 1) : "";
                string[] h = hc.Split(' ');
                var hcard = h.Length == 2 ? h[0] : hc;
                var hver = h.Length == 2 ? h[1].Trim() : null;

                var pid = new HL7Patient();
                pid.healthCardNo = hcard;
                pid.versionNo = hver;
                pid.externalID = p.patientIDExternalID2;
                pid.internalID = p.patientIDInternalID3;
                pid.familyName = p.patientName5.familyName;
                pid.firstName = p.patientName5.givenName;
                pid.middleName = p.patientName5.middleInitial;
                pid.Reference = p.referencePatient4;
                pid.DOB = p.dateOfBirth7;
                pid.sex = p.sex8;
                pid.phoneNo = p.phoneNumber13;
                pid.createdDate = DateTime.Now;

                pid.PatientRecordId = FindPatient(PracticeId,
                    hcard,
                    pid.firstName,
                    pid.familyName,
                    pid.sex,
                    pid.DOB);

                if (pid.PatientRecordId <= 0)
                {
                    PatientRecord prec = AddNewPatientRecord(pid);
                    if (prec != null)
                    {
                        pid.PatientRecordId = prec.Id;
                    }
                }
                return pid;
            }
            catch (Exception ex)
            {
                _log.Error("PID Saving Error : " + ex.Message + "\n" + ex.Source, ex);
            }
            return null;
        }
        private PatientRecord AddNewPatientRecord(HL7Patient p)
        {
            _context = _context ?? new CerebrumContext();

            //var pr = _context.Practices.Find(PracticeId);
            var patientrecord = new PatientRecord { PracticeId = PracticeId };

            _context.PatientRecords.Add(patientrecord);
            _context.SaveChanges(UserId, IPAddress);

            var cerP = new Demographic();
            cerP.PatientRecord = patientrecord;
            cerP.dateOfBirth = p.DOB;
            cerP.firstName = p.firstName;
            cerP.lastName = p.familyName;

            cerP.middleName = p.middleName;
            cerP.active = Active.Other;

            var demoAddress = new DemographicsAddress();
            demoAddress.addressLine1 = p.address1;
            demoAddress.addressLine2 = p.address2;
            demoAddress.city = p.city;
            if (p.postalCode != null)
            {
                if (p.postalCode.Length < 7)
                {
                    demoAddress.postalCode = p.postalCode;
                }

                else
                    demoAddress.postalCode = p.postalCode.Substring(0, 6);    // truncate Zip code to 6 - OLIS has 
            }
            demoAddress.province = p.province;
            switch (p.sex)
            {
                case "M":
                case "MALE":
                    cerP.gender = Gender.M;
                    break;
                case "F":
                case "FEMALE":
                    cerP.gender = Gender.F;
                    break;
                case "U":
                case "UNKNOWN":
                    cerP.gender = Gender.U;
                    break;
                case "O":
                case "OTHER":
                    cerP.gender = Gender.O;
                    break;
            }
            var demohc = new DemographicsHealthCard();
            demohc.number = p.healthCardNo;
            if (p.provinceCode != null && p.provinceCode.Trim() != "")
                demohc.provinceCode = (Province)Enum.Parse(typeof(Province), p.province);
            demohc.version = p.versionNo;

            cerP.healthcards.Add(demohc);

            var demophone = new DemographicsPhoneNumber();
            demophone.phoneNumber = p.phoneNo;

            cerP.phoneNumbers.Add(demophone);

            patientrecord.Demographics.Add(cerP);

            _context.Demographics.Add(cerP);
            _context.SaveChanges(UserId, IPAddress);
            //_context.PatientRecords.Add(patientrecord);
            //_context.SaveChanges(UserId, IPAddress);

            return patientrecord;
        }

        private HL7Report HL7OBR(ZCT z, int patientId, string accessionNumber, string CerebrumAccessionNumber, int practiceId)
        {
            try
            {
                var report = (from hr in _context.HL7Reports
                              join hp in _context.HL7Patients on hr.HL7PatientId equals hp.Id
                              join pr in _context.PatientRecords on hp.PatientRecordId equals pr.Id
                              where hr.accessionNumber.Trim().ToLower().Equals(accessionNumber.Trim().ToLower())
                                 && pr.PracticeId == practiceId
                              select hr).FirstOrDefault();
                //var report = _context.HL7Reports.FirstOrDefault(a => a.accessionNumber.Trim().ToLower().Equals(accessionNumber.Trim().ToLower()));
                if (report == null)
                {
                    var o = z.OBR;
                    var ob = new HL7Report();
                    ob.assignmentStatus = DocumentAssignmentStatuses.Unassigned;
                    ob.HL7PatientId = patientId;
                    ob.priority = o.quantityTiming27;
                    ob.accessionNumber = accessionNumber;
                    ob.requestedDateTime = o.observationDateTime7;
                    ob.collectionDateTime = o.observationDateTime7;

                    //var acc = new VMAccessionNumber(accessionNumber, o.observationDateTime7, this.PracticeId);
                    ob.CerebrumAccession = CerebrumAccessionNumber;
                    ob.OLISAccession = null;

                    ob.resultedDateTime = o.specimenReceivedDateTime14;
                    ob.createdDate = DateTime.Today;
                    return ob;
                }
                return report;
            }
            catch (Exception ex)
            {
                _log.Error("OBR processing error", ex);
            }

            return null;
        }
        private HL7Result HL7Result(OBX o, DateTime? collectionDate)
        {
            if (o != null)
            {
                var r = new HL7Result { createdDate = DateTime.Now };

                r.setId = (int)o.SetID1;
                r.valueType = o.valueType2;
                r.observationSubId = o.observationSubID4;
                r.collectionDate = collectionDate;
                r.ReportURL = o.ReportURL;

                if (o.observationSubID4.IndexOf("-") > 0)
                {
                    string[] ids = o.observationSubID4.Split('-');
                    r.testCodeIdentifier = ids[2];
                }
                else
                {
                    r.testCodeIdentifier = o.observationIdentifier3.identifier0;
                }

                r.testResult = o.observationValue5;
                decimal testRestulFloat;
                if (decimal.TryParse(r.testResult, out testRestulFloat))
                {
                    r.testResultFloat = testRestulFloat;
                }
                //decimal tf;
                //decimal.TryParse(o.observationValue5, out tf);
                //r.testResultFloat = tf;
                r.structureRefRange = o.referenceRange7;
                if (o.referenceRange7.IndexOf("-") > 0)
                {
                    string[] ranges = o.referenceRange7.Split('-');
                    r.range1 = decimal.Parse(ranges[0]);
                    r.range2 = decimal.Parse(ranges[1]);
                }
                r.abnormalFlag = o.abnormalFlags8;

                if (o.zmn == null)
                {
                    //_log.Warn("null zmn");
                    r.testDescription = o.observationIdentifier3.text1.Trim();
                }

                if (o.zmn != null)
                {
                    r.testDescription = o.zmn.LLReportName4.Trim();
                    r.units = o.zmn.Units5;
                    if (o.observationValue5.Trim() == "" && o.observationResultStatus11.Equals("P"))
                    {
                        _log.Info("Result Pending");
                    }
                    r.resultStatus = o.observationResultStatus11;

                    r.reportCode = o.zmn.LLResultCode8;

                    var zrg = o.zmn.zrgs;
                    if (zrg.Count > 1)
                    {
                        var newzrg = (from i in zrg
                                      from j in zrg.SkipWhile(j => j != i)
                                      where i.LLReportGroupDescription5 == j.LLReportGroupDescription5 // Remove the self-comparison if you want to
                                      select i);
                        var header = newzrg.Where(w => w.LLReportGroupHeading7.Trim() != "").FirstOrDefault();
                        r.reportGroupHead = header != null ? header.LLReportGroupHeading7 : null;
                    }


                    r.reportGroupDescription = (zrg != null && zrg.Count > 0) ? zrg.FirstOrDefault().LLReportGroupDescription5 : null;
                    //r.groupSortCode = zrg != null ? zrg.FirstOrDefault().LLReportSequence1:null;
                }
                return r;
            }
            return null;
        }
        private List<HL7ResultNote> HL7ResultNote(List<NTE> notes, int resultId)
        {
            var ns = new List<HL7ResultNote>();
            if (notes != null && notes.Count() > 0)
            {
                int count = 0;
                foreach (var no in notes)
                {
                    foreach (var mc in no.ZMCs)
                    {
                        var n = new HL7ResultNote();
                        n.HL7ResultId = resultId;
                        n.setId = ++count;
                        n.comments = mc.LLMessageCodeDescription6;
                        n.createdDate = DateTime.Now;
                        ns.Add(n);
                    }
                }

            }
            return ns;
        }
        private List<VMHL7ReportDoctor> Doctors(List<ZCL> ZCLs9, int hl7reportId)
        {
            var docs = new List<VMHL7ReportDoctor>();
            bool primaryDoctor = true;
            if (HL7LostReportDoctors == null)
            {

                foreach (var cl in ZCLs9)
                {
                    var doc = ReportDoctor(cl);
                    if (doc != null)
                    {
                        var hl7rdoc = new VMHL7ReportDoctor
                        {
                            PracticeId = doc.PracticeId,
                            createdDate = DateTime.Now,
                            practiceDoctorId = doc.practiceDoctorId,
                            ExternalDoctorId = doc.ExternalDoctorId,
                            IsPrimary = primaryDoctor,
                            HL7ReportId = hl7reportId
                        };
                        if (!docs.Any(ad => ad.ExternalDoctorId == doc.ExternalDoctorId))
                        {
                            docs.Add(hl7rdoc);
                        }
                        if (primaryDoctor)
                        {
                            primaryDoctor = false;
                        }
                    }
                }
            }
            else
            {
                foreach (var doc in HL7LostReportDoctors)
                {
                    var hl7rdoc = new VMHL7ReportDoctor
                    {
                        PracticeId = doc.PracticeId,
                        createdDate = DateTime.Now,
                        practiceDoctorId = doc.PracticeDoctorId,
                        ExternalDoctorId = doc.ExternalDoctorId != null ? (int)doc.ExternalDoctorId : 0,
                        IsPrimary = primaryDoctor,
                        HL7ReportId = hl7reportId
                    };
                    if (!docs.Any(ad => ad.ExternalDoctorId == doc.ExternalDoctorId))
                    {
                        docs.Add(hl7rdoc);
                    }
                    if (primaryDoctor)
                    {
                        primaryDoctor = false;
                    }
                }
            }
            return docs;
        }
        private VMHL7ReportDoctor ReportDoctor(ZCL z)
        {
            VMHL7ReportDoctor practiceDoc = null;
            VMHL7ReportDoctor externalDoc = null;
            try
            {
                var billing = z.consultingDoctor2.requisitionPhysicianCode.Trim();
                string billing2 = billing;
                if (billing.Length < 6)
                {
                    billing = new string('0', 6 - billing.Length) + billing;
                }

                var doc = _context.ExternalDoctors.Where(f => (f.OHIPPhysicianId.Equals(billing) || f.OHIPPhysicianId.Equals(billing2)) && f.active).ToList();

                if (doc != null && doc.Count() > 0)
                {
                    var d = doc.FirstOrDefault();

                    // check if this doctor has multiple office
                    var usof = (from e in _context.ExternalDoctors
                                join pd in _context.PracticeDoctors on e.Id equals pd.ExternalDoctorId
                                join uo in _context.UserOffices on pd.ApplicationUserId equals uo.ApplicationUserId
                                where uo.LabOfficeCode.ToLower().Equals(billing) && e.Id == d.Id && e.active
                                select pd).FirstOrDefault();

                    if (usof != null)
                    {
                        practiceDoc = new VMHL7ReportDoctor { PracticeId = usof.PracticeId, practiceDoctorId = usof.Id, ExternalDoctorId = usof.ExternalDoctorId, createdDate = DateTime.Now, };
                    }
                    else
                    {
                        var pracdoc = _context.PracticeDoctors.Where(pod => pod.ExternalDoctorId == d.Id && pod.IsActive);

                        if (pracdoc != null && pracdoc.Count() > 0)
                        {
                            string postalCode = string.Empty;
                            if (!string.IsNullOrWhiteSpace(z.clientAddress3.postalCode5))
                            {
                                postalCode = z.clientAddress3.postalCode5.Trim().ToLower();
                            }
                            foreach (var prdo in pracdoc)
                            {
                                if (!string.IsNullOrWhiteSpace(postalCode))
                                {
                                    Office office = _context.Offices.FirstOrDefault(w => w.PracticeId == prdo.PracticeId && w.postalCode.ToLower().Equals(postalCode));
                                    if (office != null)
                                    {
                                        practiceDoc = new VMHL7ReportDoctor { PracticeId = prdo.PracticeId, practiceDoctorId = prdo.Id, ExternalDoctorId = prdo.ExternalDoctorId, createdDate = DateTime.Now, };
                                        break;
                                    }
                                    else
                                    {
                                        practiceDoc = new VMHL7ReportDoctor { PracticeId = prdo.PracticeId, practiceDoctorId = prdo.Id, ExternalDoctorId = prdo.ExternalDoctorId, createdDate = DateTime.Now, };
                                    }
                                }
                                else
                                {
                                    practiceDoc = new VMHL7ReportDoctor { PracticeId = prdo.PracticeId, practiceDoctorId = prdo.Id, ExternalDoctorId = prdo.ExternalDoctorId, createdDate = DateTime.Now, };
                                }

                            }

                        }
                    }

                    if (practiceDoc == null && doc != null)
                    {
                        externalDoc = new VMHL7ReportDoctor { ExternalDoctorId = d.Id, createdDate = DateTime.Now };
                    }
                    //_log.Error("Doctor office addres not match to find practice id");
                }

                if (practiceDoc == null)
                {
                    var physicianNo = billing2.ToLower();// z.consultingDoctor2.requisitionPhysicianCode.Trim();
                    var usof = from e in _context.ExternalDoctors
                               join pd in _context.PracticeDoctors on e.Id equals pd.ExternalDoctorId
                               join uo in _context.UserOffices on pd.ApplicationUserId equals uo.ApplicationUserId
                               where uo.LabOfficeCode.ToLower().Equals(physicianNo)
                               select new { pd };

                    if (usof.Any())
                    {
                        var prdo = usof.FirstOrDefault().pd;
                        practiceDoc = new VMHL7ReportDoctor { PracticeId = prdo.PracticeId, practiceDoctorId = prdo.Id, ExternalDoctorId = prdo.ExternalDoctorId, createdDate = DateTime.Now, };
                    }
                }

            }
            catch (Exception ex)
            {
                _log.Error("Practice Doctor  Error: ", ex);
            }
            if (practiceDoc != null) return practiceDoc;
            else if (externalDoc != null) return externalDoc;
            return null;
        }
        private bool IsValidPath(string path)
        {
            try
            {
                Regex driveCheck = new Regex(@"^[a-zA-Z]:\\$");
                if (!driveCheck.IsMatch(path.Substring(0, 3))) return false;
                string strTheseAreInvalidFileNameChars = new string(Path.GetInvalidPathChars());
                strTheseAreInvalidFileNameChars += @":/?*" + "\"";
                Regex containsABadCharacter = new Regex("[" + Regex.Escape(strTheseAreInvalidFileNameChars) + "]");
                if (containsABadCharacter.IsMatch(path.Substring(3, path.Length - 3)))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }
        private void ProcessHealthCardNo(ref string str, ref string healthCardSer)
        {
            if (str.Length > 12)
                healthCardSer = str.Substring(str.Length - 2);  // get two right chars
            str = new string(str.Where(c => char.IsDigit(c)).ToArray());

            if (!CheckOhipCard(str))
            {
                str = "";
                healthCardSer = "";
            }
        }
        private static bool CheckOhipCard(string ohip)
        {
            if (ohip.Length != 10)
            {
                return false;
            }

            char[] ohips = ohip.ToCharArray();

            byte[] array = new byte[10];

            for (int i = 0; i < 10; i++)
            {
                string s = ohips[i].ToString();
                if (!byte.TryParse(s, out array[i]))
                {
                    return false;
                }
            }

            return CheckCard(array);
        }
        private static bool CheckCard(byte[] oh)
        {
            oh[0] = Mul2(oh[0]);
            oh[2] = Mul2(oh[2]);
            oh[4] = Mul2(oh[4]);
            oh[6] = Mul2(oh[6]);
            oh[8] = Mul2(oh[8]);

            int sum = 0;
            for (int i = 0; i < 9; i++)
            {
                sum += oh[i];
            }

            sum %= 10;

            return (10 - sum == oh[9]);
        }
        private string RetriveFormType(string reportForm)
        {
            switch (reportForm)
            {
                case "1": return "S";  //  (Chemistry/Hematology)
                case "4": return "X";  //  (Diagnostic imaging)
                case "5": return "C";  //  (Cytology)
                case "6": return "H";  //  (Histopathology)
                case "7": return "A";  //  (Chemistry)
                case "9": return "M";  //  (Microbiology)
            }
            return "";  // 
        }
        /*
* MOD 10 OhipCard validation
*/
        private static byte Mul2(byte n)
        {
            n <<= 1;
            n = (byte)(n / 10 + n % 10);
            return n;
        }
    }

}