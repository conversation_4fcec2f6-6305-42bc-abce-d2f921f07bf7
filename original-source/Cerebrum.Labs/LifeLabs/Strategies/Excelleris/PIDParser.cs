﻿using Cerebrum.Labs.LifeLabs.Models;
using Cerebrum.Labs.LifeLabs.Strategies.BaseClasses;

namespace Cerebrum.Labs.LifeLabs.Strategies.Excelleris
{
    public class PIDParser : BasePIDParser, IPIDParser
    {
        public override PID Parse(string input)
        {
            var pid = ParseBase(input); 

            if (pid != null)
            {
                string[] v = input.Split('|');
                string[] patientIds = v[3].Split('^');
                string patientId = patientIds[0];

                pid.patientIDExternalID2 = patientId;
                pid.patientIDInternalID3 = patientId;
                pid.patientSSN19 = $"X{patientId} {patientIds[8].Split('&')[2]}";
                pid.referencePatient4 = v[4];
            }

            return pid;
        }
    }
}