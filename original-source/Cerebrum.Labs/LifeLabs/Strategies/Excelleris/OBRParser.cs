﻿using Cerebrum.Labs.LifeLabs.Models;
using Cerebrum.Labs.LifeLabs.Strategies.BaseClasses;


namespace Cerebrum.Labs.LifeLabs.Strategies.Excelleris
{
    public class OBRParser: BaseOBRParser, IOBRParser
    {
        public override OBR Parse(string input)
        {
            var obr = ParseBase(input);

            if (obr != null)
            {
                string[] v = input.Split('|');
                obr.orderProvider16 = ParseDoctor(v, 16);
                obr.resultCopiesTo28 = ParseDoctor(v, 28);
            }

            return obr;
        }
        private LLDoctor ParseDoctor(string[] fields, int index)
        {
            if (fields.Length > index)
            {
                var parts = fields[index].Split('^');
                return new LLDoctor
                {
                    requisitionPhysicianCode = parts.Length > 0 ? parts[0] : string.Empty,
                    familyName = parts.Length > 2 ? $"{parts[1]} {parts[2]}" : string.Empty
                };
            }
            return new LLDoctor();
        }
    }
}
