﻿namespace Cerebrum.Labs.LifeLabs.Models
{
    public class ZRG
    {
        public string LLReportSequence1 { get; set; }
        public string LLReportGroupIdentifier2 { get; set; }
        public string LLReportGroupVersion3 { get; set; }
        public string LLReportFlags4 { get; set; }
        public string LLReportGroupDescription5 { get; set; }
        public string LLIndex6 { get; set; }
        public string LLReportGroupHeading7 { get; set; }

        public static ZRG Parse(string s)
        {
            if(s!=null && s.Trim().StartsWith("ZRG"))
            {
                string[] v = s.Split('|');
                var zrg = new ZRG();
                zrg.LLReportSequence1 = v[1];
                zrg.LLReportGroupIdentifier2 = v[2];
                zrg.LLReportGroupVersion3 = v[3];
                zrg.LLReportFlags4 = v[4];
                zrg.LLReportGroupDescription5 = v[5];
                zrg.LLIndex6 = v[6];
                zrg.LLReportGroupHeading7 = v[7];
                return zrg;
            }
            return null;
        }
    }
}
