﻿namespace Cerebrum.Labs.LifeLabs.Models
{
    public class ZLB
    {
        public string LLLabIdentifier2 { get; set; }
        public string LLLabIdentifierVersion3 { get; set; }
        public string LLLabAddress4 { get; set; }
        public string LLPrimaryLab5 { get; set; }
        public string LLPrimaryLabVersion6 { get; set; }
        public string LLLISUCI7 { get; set; }
        public string LLLISVOL8 { get; set; }

        public static ZLB Parse(string s)
        {
            if(s!=null && s.Trim().StartsWith("ZLB"))
            {
                string[] v = s.Split('|');
                var zlb = new ZLB();
                zlb.LLLabIdentifier2 = v[2];
                zlb.LLLabIdentifierVersion3 = v[3];
                zlb.LLLabAddress4 = v[4];
                zlb.LLPrimaryLab5 = v[5];
                zlb.LLPrimaryLabVersion6 = v[6];
                zlb.LLLISUCI7 = v[7];
                zlb.LLLISVOL8 = v[8];
                return zlb;
            }return null;
        }
    }
}
