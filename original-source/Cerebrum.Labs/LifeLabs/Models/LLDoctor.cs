﻿namespace Cerebrum.Labs.LifeLabs.Models
{
    public class LLDoctor
    {
        public string version { get; set; }
        public string requisitionPhysicianCode { get; set; }

        public string familyName { get; set; }
        public string Name1 {
            get
            {
                if (string.IsNullOrWhiteSpace(familyName)) return "";
                string[] names = familyName.Split(' ');
                if (names.Length>0) return names[0];
                return familyName;
            }
        }
        public string Name2 {
            get
            {
                if (string.IsNullOrWhiteSpace(familyName)) return "";
                string[] names = familyName.Split(' ');
                if (names.Length>1) return names[1];
                return familyName;
            }
        }

        public string  initials { get; set; }
        public string prefix { get; set; }
        public string copyToFlag { get; set; }

        public static LLDoctor Parse(string s)
        {
            var d = new LLDoctor();
            if (!string.IsNullOrWhiteSpace(s))
            {
                string[] v = s.Split('^');
                var vr = v[0].Split('-');
                d.version = vr[0];
                d.requisitionPhysicianCode = vr[1];
                d.familyName = v[1];
                d.initials = v[3];
                d.prefix = v[5];
            }
            return d;
        }
    }
}
