﻿namespace Cerebrum.Labs.LifeLabs.Models
{
    public class ZMC
    {
        public string SetID1 { get; set; }
        public string LLMessageCodeIdentifier2 { get; set; }
        public string LLMessageCodeVersion3 { get; set; }
        public string NumberOfMessageCodeDescriptionLines4 { get; set; }
        public string LLSignificantFlag5 { get; set; }
        public string LLMessageCodeDescription6 { get; set; }

        public static ZMC Parse(string s)
        {
            if(s!=null && s.Trim().StartsWith("ZMC"))
            {
                string[] v = s.Split('|');
                var zmc = new ZMC();
                zmc.SetID1 = v[1];
                zmc.LLMessageCodeIdentifier2 = v[2];
                zmc.LLMessageCodeVersion3 = v[3];
                zmc.NumberOfMessageCodeDescriptionLines4 = v[4];
                zmc.LLSignificantFlag5 = v[5];
                zmc.LLMessageCodeDescription6 = v[6];
                return zmc;
            }
            return null;
        }
    }
}
