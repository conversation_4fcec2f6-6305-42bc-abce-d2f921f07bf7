﻿using Cerebrum.Labs.OLIS;
using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Bson;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Cerebrum.Labs.common
{
    public static class CompressFile
    {
        public static OLISHL7Reports DeCompressJsonArray(byte[] reportBytes)
        {
            OLISHL7Reports reports = null;
            MemoryStream outFile = new MemoryStream(reportBytes);
            using (BsonDataReader reader = new BsonDataReader(outFile))
            {
                JsonSerializer serializer = new JsonSerializer();
                reports = serializer.Deserialize<OLISHL7Reports>(reader);
            }

            return reports;
        }
        public static byte[] CompressJsonArray(OLISHL7Reports reports)
        {
            MemoryStream outFile = new MemoryStream();
            using (BsonDataWriter writer = new BsonDataWriter(outFile))
            {
                JsonSerializer serializer = new JsonSerializer();
                serializer.Serialize(writer, reports);
            }
            return outFile.ToArray();
        }
        public static byte[] Decompress(byte[] data)
        {
            using (var compressedStream = new MemoryStream(data))
            using (var zipStream = new GZipStream(compressedStream, CompressionMode.Decompress))
            using (var resultStream = new MemoryStream())
            {
                zipStream.CopyTo(resultStream);
                return resultStream.ToArray();
            }
        }
        public static async Task CompressToFileAsync(string source, string outputFile)
        {
            byte[] buffer = File.ReadAllBytes(source);
            using (var inputStream = new MemoryStream(buffer))
                await CompressToFileAsync(inputStream, outputFile);
        }
        public static async Task CompressToFileAsync(byte[] buffer, string outputFile)
        {
            using (var inputStream = new MemoryStream(buffer))
                await CompressToFileAsync(inputStream, outputFile);
        }

        public static async Task CompressToFileAsync(Stream inputStream,
                                                     string outputFile)
        {
            using (var outputStream = File.Create(outputFile))
            using (var gzip = new GZipStream(outputStream, CompressionMode.Compress))
            {
                await inputStream.CopyToAsync(gzip);
                gzip.Close();
            }
        }

        public static byte[] CompressNew(string source, string destination)
        {
            byte[] fi = File.ReadAllBytes(source);
            MemoryStream outFile = new MemoryStream();
            using (MemoryStream inFile = new MemoryStream(fi))
            using (GZipStream Compress = new GZipStream(outFile, CompressionMode.Compress))
            {
                inFile.CopyTo(Compress);
            }

            byte[] outarr = outFile.ToArray();

            FileStream newOutFile = File.Create(destination);
            newOutFile.Write(outarr, 0, outarr.Length);
            newOutFile.Close();
            return outFile.ToArray();
        }
        public static byte[] CompressArray(string source)
        {
            byte[] fi = File.ReadAllBytes(source);
            MemoryStream outFile = new MemoryStream();
            using (MemoryStream inFile = new MemoryStream(fi))
            using (GZipStream Compress = new GZipStream(outFile, CompressionMode.Compress))
            {
                inFile.CopyTo(Compress);
            }

            return outFile.ToArray();
        }

        public static byte[] EncodeCompressArray(string source)
        {
            string fi = File.ReadAllText(source);

            Encoding iso = Encoding.GetEncoding("ISO-8859-1");
            byte[] isoBytes = iso.GetBytes(fi);
            MemoryStream outFile = new MemoryStream();
            using (MemoryStream inFile = new MemoryStream(isoBytes))
            using (GZipStream Compress = new GZipStream(outFile, CompressionMode.Compress))
            {
                inFile.CopyTo(Compress);
            }

            return outFile.ToArray();
        }
        public static byte[] EncodeCompressString(string text)
        {
            Encoding iso = Encoding.GetEncoding("ISO-8859-1");
            byte[] isoBytes = iso.GetBytes(text);
            MemoryStream outFile = new MemoryStream();
            using (MemoryStream inFile = new MemoryStream(isoBytes))
            using (GZipStream Compress = new GZipStream(outFile, CompressionMode.Compress))
            {
                inFile.CopyTo(Compress);
            }

            return outFile.ToArray();
        }
        public static byte[] StreamToByteArray(Stream input)
        {
            byte[] buffer = new byte[16 * 1024];
            using (MemoryStream ms = new MemoryStream())
            {
                int read;
                while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
                {
                    ms.Write(buffer, 0, read);
                }
                return ms.ToArray();
            }
        }
        public static byte[] CompressString(string uncompressedString)
        {
            var compressedStream = new MemoryStream();
            var uncompressedStream = new MemoryStream(Encoding.UTF8.GetBytes(uncompressedString));

            using (var compressorStream = new DeflateStream(compressedStream, CompressionMode.Compress, true))
            {
                uncompressedStream.CopyTo(compressorStream);
            }

            //return Convert.ToBase64String(compressedStream.ToArray());
            return compressedStream.ToArray();
        }
        public static string Compress(FileInfo fi, string destination = "")
        {
            var _log = LogManager.GetLogger(typeof(CompressFile));
            // Get the stream of the source file.
            using (FileStream inFile = fi.OpenRead())
            {
                // Prevent compressing hidden and 
                // already compressed files.
                if ((File.GetAttributes(fi.FullName) & FileAttributes.Hidden) != FileAttributes.Hidden & fi.Extension != ".gz")
                {
                    // Create the compressed file.
                    //destination = string.IsNullOrWhiteSpace(destination) ? fi.FullName : Path.Combine(destination, fi.Name);
                    destination = destination + ".gz";
                    using (FileStream outFile = File.Create(destination))
                    {
                        using (GZipStream Compress = new GZipStream(outFile, CompressionMode.Compress))
                        {
                            // Copy the source file into 
                            // the compression stream.
                            inFile.CopyTo(Compress);


                            var s = string.Format("Compressed {0} from {1} to {2} bytes.",
                                fi.Name, fi.Length.ToString(), outFile.Length.ToString());
                            _log.Info(s);
                            return destination;
                        }
                    }
                }
            }
            return null;
        }
    }
}