﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.common
{
    public static class OntarioHealthCard
    {
        public static bool IsValidCheck(string hc)
        {
            if (checkNullOrAllDigitis(hc))
            {
                int[] ary = hc.Select(s => int.Parse(s.ToString())).ToArray();
                try
                {
                    var mod10 = int.Parse((ary.Select((h, i) => new { d = h, Index = i }).Where(x => ((x.Index % 2 != 0) && x.Index != 9)).Select(
                            ss => ss.d).Sum() +
                        ary.Select((h, i) => new { d = h, Index = i }).Where(x => x.Index % 2 == 0).Select(
                        s => (s.d * 2).ToString().Sum(sm => sm - '0')).Sum()).ToString()[1].ToString());

                    var valid = int.Parse(ary[9].ToString()) == (mod10 > 0 ? 10 - mod10 : mod10);

                    return valid;
                }
                catch
                {
                    return false;
                }

            }
            return false;
        }
        private static bool checkNullOrAllDigitis(string ohip)
        {
            if (!string.IsNullOrWhiteSpace(ohip))
            {
                bool ismatch = ohip.All(char.IsDigit);
                return ohip.Length == 10 && ismatch;
            }
            return false;
        }
    }
}
