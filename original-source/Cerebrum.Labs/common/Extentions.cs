﻿using Cerebrum.BLL.Utility;
using Cerebrum.Data.Entities.OLIS;
using Cerebrum.Labs.OLIS;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Cerebrum.Labs.common
{
    
    public class DateFormat
    {
        public string format { get; set; }
        public string value { get; set; }
    }
    public static class Extentions
    {
        public static OLISHL7Reports ToReport(this OLISReceivedReport rr)
        {
            if(rr.jsonReports!=null)
                return CompressFile.DeCompressJsonArray(rr.jsonReports);
            return null;
        }
        public static string EncryptResult(this string result)
        {
            var plainTextBytes = Encoding.UTF8.GetBytes(result);
            string encodedText = Convert.ToBase64String(plainTextBytes);

            return encodedText;
        }
        public static string DecryptResult(this string result)
        {
            byte[] encodedbyte = Convert.FromBase64String(result);
            var plainText = Encoding.UTF8.GetString(encodedbyte);
            return plainText;
        }
        public static string RemoveSpecialChars(this string str)
        {
            var reg = Regex.Split(str, @"[^0-9\.]+").FirstOrDefault();
            return reg;
        }
        public static bool SpecialCharacter(this string chk)
        {
            return (chk.Contains(">") || chk.Contains("<"));
        }
        public static string TrimIt(this string val)
        { 
            if (string.IsNullOrEmpty(val))
                return "";
            return val.Trim();
        }
        public static DateTime? ToDateTime(this string s)
        {
            if (!string.IsNullOrWhiteSpace(s))
            {
                DateTime dt;

                CultureInfo enUS = new CultureInfo("en-US");

                string dateformat = "yyyyMMddHHmmsszzz";
                if (s.Length == 8)
                {
                    dateformat = "yyyyMMdd";
                    DateTime.TryParseExact(s, dateformat, enUS, DateTimeStyles.None, out dt);
                    return dt;
                }
                else if (s.Length == 14)
                {
                    dateformat = "yyyyMMddHHmmss";
                    DateTime.TryParseExact(s, dateformat, enUS, DateTimeStyles.None, out dt);
                    return dt;
                }
                else if (s.IndexOf('-') > 0)
                {
                    var ti = TimeZoneInfo.GetSystemTimeZones();

                    var dtstr = int.Parse(s.Substring(s.IndexOf('-')).Replace("0", ""));
                    var nt = ti.FirstOrDefault(w => w.BaseUtcOffset.Hours == dtstr);
                    if (DateTime.TryParseExact(s, dateformat, enUS, DateTimeStyles.None, out dt))
                    {
                        TimeZone zone = TimeZone.CurrentTimeZone;
                        var mm = zone.ToLocalTime(dt);
                        var tz = zone.ToUniversalTime(dt);

                        char[] delim = { ' ' };
                        string[] words = zone.DaylightName.Split(delim, StringSplitOptions.RemoveEmptyEntries);
                        string abbrev = string.Empty;
                        foreach (string chaStr in words)
                        {
                            abbrev += chaStr[0];
                        }
                        return dt;
                    }
                }
            }
            return null;
        }
        public static DateFormat DateFormat(this string val)
        {
            try
            {
                var v = new DateFormat();
                if (val.Length == 8)
                {
                    v.format = "yyyyMMdd";
                    v.value = val;
                    return v;
                }
                if (val.Length == 14)
                {
                    v.format = "yyyyMMddHHmmss";
                    v.value = val;
                    return v;
                }

                if (val.Length == 19)
                {
                    var addcolon = val != "" ? val.Insert(val.Length - 2, ":").Replace(" ", "-") : null;
                    v.format = "yyyyMMddHHmmsszzz";
                    v.value = addcolon;
                    return v;
                }
            }catch
            {
                return null;
            }
            return null;
        }
    }
}
