﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Cerebrum.Labs.common
{
    /// <summary>
    /// Name from HL7 Result file
    /// </summary>
    public class Name
    {
        public Name(string v1, string v2, string v3)
        {
            this.familyName   =v1;
            this.givenName    =v2;
            this.middleInitial=v3;
        }

        public string familyName    { get; set; }
        public string givenName     { get; set; }
        public string middleInitial { get; set; }

        public static Name Parse(string name)
        {
            if(!string.IsNullOrWhiteSpace(name))
            {
                char separator = '=';

                if(name.Contains("^")) separator = '^';
                else
                    if(name.Contains(" ")) separator = ' ';

                if(separator != '=')
                {
                    string[] v = name.Split(separator);

                    return new Name(v[0].Trim(),
                                    v.Count() > 1 ? v[1].Trim() : "",
                                    v.Count() > 2 ? v[2].Trim() : "");
                }
                return new Name(name.Trim(), "", "");
            }
            return null;
        }
    }
}