﻿using Cerebrum.ViewModels.OLIS;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;

namespace Cerebrum.Labs.Common
{
    public class EncapsulatedDataFileHelper
    {
        private readonly string _serverVirtualPath;
        private readonly string _serverFileUrl;

        // <summary>
        /// Initializes a new instance of the EncapsulatedDataFileHelper class.
        /// </summary>
        /// <param name="serverVirtualPath">The base virtual path where files will be saved.</param>
        /// <param name="serverFileUrl">The base URL used to build file access links.</param>
        public EncapsulatedDataFileHelper(string serverVirtualPath, string serverFileUrl)
        {
            _serverVirtualPath = serverVirtualPath;
            _serverFileUrl = serverFileUrl;
        }

        /// <summary>
        /// Saves a file or image based on its type and returns a report with the access URL and type.
        /// </summary>
        /// <param name="content">The file content as a byte array.</param>
        /// <param name="type">The file type/extension.</param>
        /// <param name="observationDate">The date associated with the file (used to organize directories).</param>
        /// <param name="accession">The accession identifier used in the file name.</param>
        /// <returns>An AttachedReport containing the file URL and type.</returns>
        public AttachedReport SaveEncapsulatedDataDocument(byte[] content, string type, DateTime? observationDate, string accession)
        {
            if (string.IsNullOrWhiteSpace(type))
                throw new ArgumentException("File type cannot be null or empty.", nameof(type));

            type = type.ToLower();
            string dateDirectory = observationDate?.ToString("yyyyMMdd") ?? DateTime.Now.ToString("yyyyMMdd");
            string fileName = $"{accession}.{type}";

            string filePath;

            switch (type)
            {
                case "pdf":
                case "rtf":
                case "doc":
                case "docx":
                case "html":
                case "xml":
                    filePath = SaveFile(content, dateDirectory, fileName);
                    break;

                case "jpg":
                case "jpeg":
                    filePath = SaveImage(content, dateDirectory, fileName, ImageFormat.Jpeg);
                    type = "jpeg"; 
                    break;

                case "png":
                    filePath = SaveImage(content, dateDirectory, fileName, ImageFormat.Png);
                    break;

                case "gif":
                    filePath = SaveImage(content, dateDirectory, fileName, ImageFormat.Gif);
                    break;

                case "tiff":
                    filePath = SaveImage(content, dateDirectory, fileName, ImageFormat.Tiff);
                    break;

                default:
                    throw new NotSupportedException($"File type '{type}' is not supported.");
            }

            return CreateAttachedReport(filePath, type);
        }

        /// <summary>
        /// Saves a file to disk under the specified date directory.
        /// </summary>
        /// <param name="content">The file content as a byte array.</param>
        /// <param name="dateDirectory">The subdirectory name based on the observation date.</param>
        /// <param name="fileName">The file name including its extension.</param>
        /// <returns>The virtual path of the saved file.</returns>
        private string SaveFile(byte[] content, string dateDirectory, string fileName)
        {
            string virtualPath = Path.Combine(_serverVirtualPath, dateDirectory);
            string directoryPath = ResolveDirectoryPath(virtualPath);

            CreateDirectoryIfNotExists(directoryPath);

            string fullFilePath = Path.Combine(directoryPath, fileName);
            File.WriteAllBytes(fullFilePath, content);

            return Path.Combine(virtualPath, fileName);
        }

        /// <summary>
        /// Saves an image to disk under the specified date directory with the given image format.
        /// </summary>
        /// <param name="content">The image content as a byte array.</param>
        /// <param name="dateDirectory">The subdirectory name based on the observation date.</param>
        /// <param name="fileName">The image file name including its extension.</param>
        /// <param name="format">The image format (e.g., JPEG, PNG).</param>
        /// <returns>The virtual path of the saved image file.</returns>
        private string SaveImage(byte[] content, string dateDirectory, string fileName, ImageFormat format)
        {
            string virtualPath = Path.Combine(_serverVirtualPath, dateDirectory);
            string directoryPath = ResolveDirectoryPath(virtualPath);

            CreateDirectoryIfNotExists(directoryPath);

            string fullFilePath = Path.Combine(directoryPath, fileName);
            using (var image = Image.FromStream(new MemoryStream(content)))
            {
                image.Save(fullFilePath, format);
            }

            return Path.Combine(virtualPath, fileName);
        }

        /// <summary>
        /// Resolves a virtual path to a physical server path.
        /// </summary>
        /// <param name="virtualPath">The virtual path to resolve.</param>
        /// <returns>The physical path on the server.</returns>
        private string ResolveDirectoryPath(string virtualPath)
        {
            return virtualPath.Contains("~")
                ? System.Web.HttpContext.Current.Server.MapPath(virtualPath)
                : virtualPath;
        }

        /// <summary>
        /// Creates a directory at the given path if it does not already exist.
        /// </summary>
        /// <param name="directoryPath">The full physical directory path.</param>
        private void CreateDirectoryIfNotExists(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }

        /// <summary>
        /// Creates an AttachedReport object containing the URL to access the saved file and its type.
        /// </summary>
        /// <param name="filePath">The virtual file path of the saved document.</param>
        /// <param name="type">The type of the file.</param>
        /// <returns>An AttachedReport with the file's access URL and type.</returns>
        private AttachedReport CreateAttachedReport(string filePath, string type)
        {
            return new AttachedReport
            {
                URL = $"{_serverFileUrl}?type={type}&url={filePath}",
                type = type
            };
        }
    }
}
