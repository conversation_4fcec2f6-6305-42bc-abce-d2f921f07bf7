﻿using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using Cerebrum.Labs.common;
using Cerebrum.Labs.GammaDynacare.Models;
using Cerebrum.Labs.Service;
using Cerebrum.ViewModels.HL7;
using Cerebrum.ViewModels.OLIS;
using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;

namespace Cerebrum.Labs.GammaDynacare.Services
{
    /// <summary>
    /// GammaDynacare Report Service
    /// </summary>
    public class GammaDynacareReportService : LabReportServiceBase
    {
        private GDMLReadHL7File _gd;

        private BLL.Requisition.IRequisitionBLL _requistionBll;
        public GammaDynacareReportService(int userid, string ip) :
            base(userid, ip, "")
        {
            _gd = new GDMLReadHL7File();
            _context = new CerebrumContext();
            UserId = userid;
            IPAddress = ip;
            _log = LogManager.GetLogger(typeof(GammaDynacareReportService));
            _requistionBll = new BLL.Requisition.RequisitionBLL(_context);
        }
        public GammaDynacareReportService(string filepath, int userid, string ip) :
            base(userid, ip, filepath)
        {
            _gd = new GDMLReadHL7File();

            UserId = userid;
            IPAddress = ip;
            _requistionBll = new BLL.Requisition.RequisitionBLL(_context);
            _log = LogManager.GetLogger(typeof(GammaDynacareReportService));
        }

        override public bool SaveUploadReport(string _filePath)
        {
            this.filePath = _filePath;
            bool bError = false;

            try
            {
                GDMLHL7Report rpt = null;
                _context = new CerebrumContext();
                var isvalidpath = IsValidPath(_filePath);
                if (isvalidpath)
                {
                    rpt = _gd.ReadFile(filePath);
                }
                else
                {
                    string[] hl7lines = Regex.Split(_filePath, "\r\n|\r|\n");
                    rpt = _gd.Report(hl7lines);
                }

                var msg = HL7Message(rpt.MSH1, filePath);
                _context.HL7Messages.Add(msg);
                _context.SaveChanges(UserId, IPAddress);

                Save(rpt, msg.Id);

                //HL7 file patient exists, remove file
                //in MSH the file has been compressed and moved to transferred folder.
                if (File.Exists(filePath))
                    File.Delete(filePath);
            }
            catch (Exception ex)
            {
                _log.Error($"File Error: {filePath} {ex.ExceptionDetails()}");
                bError = true;
            }
            if (bError)
            {
                string destPath = Path.Combine(ERROR_PATH, Path.GetFileName(filePath));
                try
                {
                    if (!File.Exists(destPath))
                    {
                        File.Move(filePath, destPath);
                    }
                }
                catch (Exception ex)
                {
                    _log.Error($"Error moving not-processed file: {filePath} to {destPath} {ex.ExceptionDetails()}");
                }
                if (File.Exists(filePath))
                {
                    try
                    {
                        File.Delete(filePath);
                    }
                    catch (Exception ex)
                    {
                        _log.Error($"Error deleting file: {filePath} {ex.ExceptionDetails()}");
                    }
                }
            }
            return bError;
        }
        public bool SaveLostReport(int HL7LostReportId, int practiceId = 0)
        {
            var ResponseSaved = new List<VMOLISReportSave>();
            GDMLHL7Report rpt = null;
            int reportSaved = 0;
            string hl7_text = string.Empty;
            try
            {
                HL7LostReport lostReport = _context.HL7LostReports.FirstOrDefault(w => w.Id == HL7LostReportId && w.HL7LostReportDoctors.Any(a => a.ExternalDoctorId > 0));

                if (lostReport != null && lostReport.HL7File != null)
                {
                    string accession = lostReport.AccessionNumber.ToLower().Trim();
                    var HL7LostReportDoctors = lostReport.HL7LostReportDoctors.ToList();
                    PracticeId = (int)lostReport.HL7LostReportDoctors.FirstOrDefault(fd => fd.PracticeId > 0).PracticeId;

                    byte[] decompressed = Cerebrum.Labs.common.CompressFile.Decompress(lostReport.HL7File);
                    Encoding iso = Encoding.GetEncoding("ISO-8859-1");
                    hl7_text = Encoding.Default.GetString(decompressed);
                    if (!string.IsNullOrWhiteSpace(hl7_text))
                    {
                        rpt = new GDMLHL7Report();
                        string[] hl7lines = Regex.Split(hl7_text, "\r\n|\r|\n");
                        rpt = _gd.Report(hl7lines);
                        int cnt = rpt.PIDs2.Count();

                        _log.Info("==================T==START==T==================");
                        _log.Info($"Total {cnt} Report in text File");
                        reportSaved = 0;
                        var pid = (from rpd in rpt.PIDs2
                                   where rpd.patientIDInternalID3.Trim().Equals(accession)
                                   select rpd).FirstOrDefault();

                        var msg = HL7Message(rpt.MSH1, "");
                        _context.HL7Messages.Add(msg);
                        _context.SaveChanges(UserId, IPAddress);

                        //Save exact PID
                        int savedReport = SavePID(pid, msg.Id, lostReport.Id);

                        _log.Info($"Saved {reportSaved} reports from {cnt}");
                        _log.Info("==================T== END ==T==================");
                        var allReportSaved = _context.HL7LostReportDoctors.All(a => a.HL7LostReportId == HL7LostReportId && a.ReportSaved == true);
                        if (savedReport > 0 && allReportSaved)
                        {
                            lostReport.ReportSaved = allReportSaved;
                            lostReport.HL7File = null;
                            _context.SaveChanges(UserId, IPAddress);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                string hl7txt = string.IsNullOrWhiteSpace(hl7_text) ? "No  HL7 Text" : hl7_text.Substring(0, 50);
                _log.Error($"{filePath} Gamma Lab File Reading Error.{ex.ExceptionDetails()} {hl7txt}");
            }
            return true;
        }
        private void AddToLostReport(PID report, List<VMHL7ReportDoctor> patientPracticeDoctors)
        {
            var pl = report.patientExternalID2;
            var hc = pl.healthCardNumber1;
            try
            {

                string[] h = hc.Split(' ');
                var hcard = h.Length == 2 ? h[0] : hc;
                var hver = h.Length == 2 ? h[1].Trim() : null;
                string accession = report.patientIDInternalID3.ToLower().Trim();
                var firstorder = report.OBRs.FirstOrDefault();

                var file = IsValidPath(filePath) ? CompressFile.EncodeCompressArray(filePath) : CompressFile.EncodeCompressString(filePath);

                var lostReport = new HL7LostReport();
                lostReport.Lab = "GDML";
                lostReport.AccessionNumber = accession;
                lostReport.PatientOHIP = $"{hcard} {hver}";
                lostReport.PatientFirstName = report.patientName5.givenName;
                lostReport.PatientLastName = report.patientName5.familyName;
                lostReport.LastModifiedByUserId = UserId;
                lostReport.HL7File = file;
                lostReport.ReportSaved = false;
                lostReport.IsActive = true;
                List<string> docNames = new List<string>();
                var doctors = new List<HL7LostReportDoctor>();

                foreach (var c in patientPracticeDoctors)
                {
                    if (c.PracticeId != null && c.PracticeId > 0 && c.PatientRecordId == 0)
                    {
                        var AccessionCheck = AccessionNumberExists(hc, firstorder.collectionDateTime7, firstorder.requestedDatetime6, "GDML", accession, (int)c.PracticeId);
                        var doctor = new HL7LostReportDoctor();
                        doctor.PracticeId = c.PracticeId;
                        doctor.PracticeDoctorId = c.practiceDoctorId;
                        doctor.ExternalDoctorId = c.ExternalDoctorId;
                        doctor.LastName = c.LastName;
                        doctor.FirstName = c.FirstName;
                        doctor.OHIPOrGroup = c.OHIPPhysicianId;
                        doctor.Address = null;
                        doctor.LastModifiedByUserId = UserId;
                        doctor.ReportSaved = AccessionCheck.IsExists;
                        doctor.IsActive = true;
                        doctors.Add(doctor);
                    }
                }
                // check if report already exists, active and not saved
                // if report already saved means patient and dr's practiced matched by user
                bool reportExists = _context.HL7LostReports.Any(a => a.AccessionNumber.Trim().ToLower().Equals(accession) && a.IsActive == true && a.ReportSaved == false);

                if (!reportExists)
                {
                    _log.Warn($" X X X X X X X X X X X X X X X X X X X X X X {hc}-{report.patientName5.familyName} X X X X X X X X X X X X X X X X X X X X X X X X X X X");
                    using (TransactionScope transactionScope = TransactionUtils.CreateTransactionScopeSupress())
                    {
                        try
                        {
                            _context.HL7LostReports.Add(lostReport);
                            _context.SaveChanges(UserId, IPAddress);

                            if (doctors.Any())
                            {
                                foreach (var d in doctors)
                                {
                                    d.HL7LostReportId = lostReport.Id;
                                    _context.HL7LostReportDoctors.Add(d);
                                }
                                _context.SaveChanges(UserId, IPAddress);
                            }
                            transactionScope.Complete();
                        }
                        catch (Exception e)
                        {
                            _log.Error($"Add to Lost Report Transaction Error {e.ExceptionDetails()}");
                            transactionScope.Dispose();
                        }
                        finally
                        {
                            transactionScope.Dispose();
                        }
                    }
                    _log.Warn($" X X X X X X X X X X X X X X X X X X X X X X {hc}-{report.patientName5.familyName} X X X X X X X X X X X X X X X X X X X X X X X X X X X");
                }
                else
                {
                    _log.Info($" 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 {hc}-{report.patientName5.familyName} {accession} Already exists 0 0 0 0 0 0 0 0 0 0 0 0 0 0 ");
                }
            }
            catch (Exception ex)
            {
                _log.Error($"Add to Lost Report Error {ex.ExceptionDetails()}");
            }

        }
        // check patient practice
        public virtual List<VMHL7ReportDoctor> FindPatientPractice(List<VMHL7ReportDoctor> practiceDoctors, PID pid)
        {
            foreach (var d in practiceDoctors)
            {
                int PracticeId = (int)d.PracticeId;
                int patientId = FindPatient(PracticeId, pid);
                d.PatientRecordId = patientId;
            }
            return practiceDoctors;
        }
        private int SavePID(PID pid, int msgId, int hl7LostReportId = 0)
        {
            int savedCnt = 0;
            bool isValidPath = IsValidPath(filePath);
            var file = isValidPath ? filePath : "";

            using (TransactionScope transactionScope = TransactionUtils.CreateTransactionScope())
            {

                if (pid.patientExternalID2 != null && (!OntarioHealthCard.IsValidCheck(pid.patientExternalID2.healthCardNumber1)))
                {
                    _log.Info($"Invalid OHIP:{pid.patientExternalID2.healthCardNumber1}");
                }

                List<string> docNames = new List<string>();
                var doctors = GetDoctors(pid, ref docNames);

                if (doctors.Count() == 0)
                {
                    _log.Debug($"could not find doctors. file{file} patient {pid.patientName5.familyName} {pid.patientName5.givenName} {pid.patientExternalID2.healthCardNumber1}");
                    //instead of continue, return from here.
                    return 0;
                }

                var practiceDoctorGroup = (from d in doctors
                                           where d.PracticeId != null && d.PracticeId > 0
                                           group d by d.OHIPPhysicianId into dg
                                           select new { OHIPPhysicianId = dg.Key, Other = dg }).ToList();
                foreach (var dg in practiceDoctorGroup)
                {
                    var practiceDoctors = dg.Other.ToList();

                    int doctorPracticeCount = practiceDoctors.Count();
                    bool patientSavedToLostReport = false;

                    practiceDoctors = FindPatientPractice(practiceDoctors, pid);

                    foreach (var d in practiceDoctors)
                    {
                        HL7Patient pat = null;
                        int PracticeId = (int)d.PracticeId;
                        if (d.PatientRecordId > 0)
                        {
                            pat = HL7Patient(pid);

                            if (pat != null)
                            {
                                pat.HL7MessageId = msgId;
                                pat.PatientRecordId = d.PatientRecordId;
                                // add patient
                                _context.HL7Patients.Add(pat);
                                _context.SaveChanges(UserId, IPAddress);

                                var firstorder = pid.OBRs.FirstOrDefault();
                                var accession = pid.patientIDInternalID3;

                                #region Check Duplicate

                                var AccessionCheck = AccessionNumberExists(pat.healthCardNo, firstorder.collectionDateTime7, firstorder.requestedDatetime6, "GDML", accession, PracticeId);
                                if (AccessionCheck.IsExists)
                                {
                                    _log.Debug($"Report Already Exists, File: {file},{pat.familyName}, Accession {pid.patientIDInternalID3}, CerebrumAccession: {AccessionCheck.CerebrumAccession}");
                                    //continue;
                                }
                                #endregion Check Duplicate

                                if (AccessionCheck.TotalReportVersions < REPORT_VERSIONS_COUNT_SETTING)
                                {
                                    var report = HL7Report(firstorder, pid.patientIDInternalID3, AccessionCheck.CerebrumAccession, pat.Id, PracticeId);
                                    report.reviewerNames = string.Join(",", docNames); // add all CC doctors
                                    report.assignmentStatus = ReportAssignedStatus(PracticeId, pat.PatientRecordId);

                                    if (report.Id < 1)
                                    {

                                        _context.HL7Reports.Add(report);

                                        _context.SaveChanges(UserId, IPAddress);
                                        foreach (var rd in doctors)
                                        {
                                            var dbrptdoc = new HL7ReportDoctor
                                            {
                                                practiceDoctorId = rd.practiceDoctorId,
                                                ExternalDoctorId = rd.ExternalDoctorId,
                                                IsPrimary = rd.IsPrimary,
                                                createdDate = DateTime.Now,
                                                HL7ReportId = report.Id
                                            };
                                            _context.HL7ReportDoctors.Add(dbrptdoc);

                                            if (hl7LostReportId > 0)
                                            {
                                                var lostReportDoctor = _context.HL7LostReportDoctors.FirstOrDefault(w => w.HL7LostReportId == hl7LostReportId && w.PracticeDoctorId == rd.practiceDoctorId);
                                                if (lostReportDoctor != null)
                                                {
                                                    //if user is saving lost report and doctors are same, set reportSaved=true
                                                    lostReportDoctor.ReportSaved = true;
                                                    _context.SaveChanges(UserId, IPAddress);

                                                }
                                            }
                                        }

                                        _context.SaveChanges(UserId, IPAddress);
                                    }

                                    var rptVer = HL7ReportVersion(firstorder, msgId, report.Id);
                                    rptVer.resultedDateTime = report.resultedDateTime;
                                    rptVer.requestedDateTime = report.requestedDateTime;

                                    _context.HL7ReportVersions.Add(rptVer);
                                    _context.SaveChanges(UserId, IPAddress);

                                    var ntes = pid.NTEs.ToList();
                                    var notes = HL7ReportNote(ntes, rptVer.Id);
                                    foreach (var n in notes)
                                    {
                                        _context.HL7ReportNotes.Add(n);
                                    }
                                    _context.SaveChanges(UserId, IPAddress);

                                    var statuslist = new List<string>();

                                    var obrs = pid.OBRs;
                                    foreach (var o in obrs)
                                    {
                                        foreach (var x in o.OBXs)
                                        {
                                            var saveResult = HL7Result(x, rptVer.Id, o);
                                            saveResult.LabTestRequestCode = o.universalServiceID4 != null ? o.universalServiceID4.ToString() : null;
                                            statuslist.Add(saveResult.resultStatus);
                                            _context.HL7Results.Add(saveResult);

                                            _context.SaveChanges();

                                            if (x.NTEs != null && x.NTEs.Count() > 0)
                                            {
                                                var resultnotes = HL7ResultNote(x.NTEs, saveResult.Id);
                                                foreach (var rn in resultnotes)
                                                {
                                                    _context.HL7ResultNotes.Add(rn);
                                                }
                                                // Aduit with transaction 7
                                                _context.SaveChanges();
                                            }
                                        }
                                    }
                                    var statuscount = statuslist.Count(c => c == "F");
                                    var obxcnt = obrs.SelectMany(s => s.OBXs).Count();
                                    if (statuscount == obxcnt)
                                    {
                                        var rptup = _context.HL7Reports.Find(report.Id);
                                        rptup.status = "F";
                                        var rptupv = _context.HL7ReportVersions.Find(rptVer.Id);
                                        rptupv.status = "F";
                                        _context.SaveChanges();
                                    }
                                }
                                else
                                {
                                    _log.Debug($"Report Already Exists, File: {file},{pat.familyName}, Accession {pid.patientIDInternalID3}, CerebrumAccession: {AccessionCheck.CerebrumAccession} VersionCount: {AccessionCheck.TotalReportVersions}");
                                }
                            }
                        }
                        else if (hl7LostReportId == 0 && (!patientSavedToLostReport)) // if doctor has more than 1 practice and patient not saved
                        {
                            AddToLostReport(pid, practiceDoctors);
                            patientSavedToLostReport = true;
                        }
                    }
                }
                transactionScope.Complete();
                transactionScope.Dispose();
                savedCnt++;
            }

            return savedCnt;
        }
        private void Save(GDMLHL7Report rpt, int msgId)
        {

            var pidgrps = (from rpd in rpt.PIDs2
                           group rpd by rpd.patientIDInternalID3 into hcgrp
                           select new { hcgrp.Key, Other = hcgrp }).ToList();

            foreach (var p in pidgrps)
            {
                var pids = p.Other.Select(s => s).ToList();
                var pid = pids.FirstOrDefault();

                SavePID(pid, msgId);
            }

        }
        private DocumentAssignmentStatuses ReportAssignedStatus(int practiceId, int patientRecordId)
        {
            try
            {
                if (!Is_TBD_Patient(practiceId, patientRecordId))
                {
                    if (!_requistionBll.BloodUrineRequisitionExist(patientRecordId))
                    {

                        _log.Debug($"OLIS Report patient Requisition found");

                        return DocumentAssignmentStatuses.Assigned;
                    }
                    else
                    {
                        _log.Debug($"OLIS Report patient Requisition not found");
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error($"ERROR in ReportAssignedStatus {ex.ExceptionDetails()}");
            }
            return DocumentAssignmentStatuses.Unassigned;
        }
        private bool Is_TBD_Patient(int practiceId, int patientRecordId)
        {
            var tbd = _context.Demographics.FirstOrDefault(f => f.PatientRecord.PracticeId == practiceId && f.firstName.ToLower().Equals("tbd_patient"));
            return tbd.PatientRecordId == patientRecordId;
        }
        private HL7Message HL7Message(MSH m, string filePath)
        {
            return HL7Message(filePath,
                               m.sendingApplication2.ToString(),
                               m.messageType8.ToString(),
                               m.dateTimeOfMessage6,
                               DateTime.Now);
        }
        private HL7Patient HL7Patient(PID p)
        {
            try
            {
                var pl = p.patientExternalID2;
                var name = p.patientName5;

                //Build Object
                var pi = new HL7Patient();
                pi.healthCardNo = pl != null ? pl.healthCardNumber1 : "";
                pi.versionNo = pl != null ? pl.versionCode2 : "";
                pi.familyName = name != null ? name.familyName : "";
                pi.firstName = name != null ? name.givenName : "";
                pi.middleName = name != null ? name.middleInitial : "";
                pi.DOB = p.dateOfBirth7;
                pi.sex = p.sex8;

                if (p.patientPhoneNumber13 != null)
                {
                    pi.phoneNo = p.patientPhoneNumber13;
                }

                var pa = p.patientAddress11;
                if (pa != null)
                {
                    pi.address1 = pa.addressLine11;
                    pi.city = pa.addressLine22;
                    pi.province = pa.province4;
                    pi.postalCode = pa.postalCode5;
                }
                pi.createdDate = DateTime.Now;

                return pi;
            }
            catch (Exception ex)
            {
                _log.Error($"Finding patient error HL7Patient: {ex.ExceptionDetails()}");
            }
            return null;
        }
        protected int FindPatient(int practiceId, PID p)
        {
            var pl = p.patientExternalID2;
            var name = p.patientName5;
            var healthCardNo = pl != null ? pl.healthCardNumber1 : "";
            var familyName = name != null ? name.familyName : "";
            var firstName = name != null ? name.givenName : "";
            var DOB = p.dateOfBirth7;
            var sex = p.sex8;
            return FindPatient(practiceId,
                   healthCardNo,
                   firstName,
                   familyName,
                   sex,
                   DOB);
        }
        private List<VMHL7ReportDoctor> GetDoctors(PID p, ref List<string> docNames)
        {
            try
            {
                var dls = new List<VMHL7ReportDoctor>();
                bool first = false;
                var obrs = p.OBRs.Select(s => s.orderingPhysician16).ToList();
                foreach (var o in obrs)
                {
                    try
                    {
                        var ohip = o.physicianOHIP3.Trim();
                        if (!string.IsNullOrWhiteSpace(ohip))
                        {
                            var externaldoctors = _context.ExternalDoctors.Where(f => f.OHIPPhysicianId.Equals(ohip) && f.active);
                            if (externaldoctors != null && externaldoctors.Count() > 0)
                            {
                                var dc = externaldoctors.OrderByDescending(od => od.Id).FirstOrDefault();
                                var practicedoctors = _context.PracticeDoctors.Where(ex => ex.ExternalDoctorId == dc.Id && ex.IsActive == true);
                                if (practicedoctors != null && practicedoctors.Count() > 0)
                                {
                                    foreach (var pd in practicedoctors)
                                    {
                                        var d = new VMHL7ReportDoctor()
                                        {
                                            OHIPPhysicianId = ohip,
                                            FirstName = dc.firstName,
                                            LastName = dc.lastName,
                                            PracticeId = pd.PracticeId,
                                            practiceDoctorId = pd.Id,
                                            ExternalDoctorId = dc.Id,
                                            IsPrimary = !first,
                                            createdDate = DateTime.Now
                                        };
                                        if (!dls.Any(w => w.ExternalDoctorId == dc.Id && w.practiceDoctorId == pd.Id))
                                        {
                                            dls.Add(d);
                                        }

                                        first = true;

                                        _log.Debug($"practice:{pd.PracticeId},Doctor:{ohip},{dc.lastName}");
                                    }
                                }
                                else
                                {
                                    var d = new VMHL7ReportDoctor()
                                    {
                                        ExternalDoctorId = dc.Id,
                                        IsPrimary = false,
                                        createdDate = DateTime.Now
                                    };
                                    if (!dls.Any(w => w.ExternalDoctorId == dc.Id))
                                    {
                                        dls.Add(d);
                                    }
                                }
                            }
                            else
                            {
                                _log.Warn($"physicianOHIP:{ohip}, {o.physicianName2} could not find active practice doctor");
                            }

                        }
                    }
                    catch (Exception e)
                    {
                        _log.Error($"Find all Doctors Error {e.ExceptionDetails()}");
                    }
                }
                //CC doctors
                if (p.ZDRs != null)
                {
                    foreach (var rp in p.ZDRs)
                    {
                        string name = rp.physicianLastName42 + " " + rp.physicianOrClientOrCompanyName41;
                        if (!docNames.Any(c => c == name))
                        {
                            docNames.Add(name);
                        }
                        string physicianClientNumber3 = rp.physicianClientNumber3.IndexOf("^") > 0 ? rp.physicianClientNumber3.Split('^')[0] : rp.physicianClientNumber3;
                        var copyDoctor = _context.ExternalDoctors.FirstOrDefault(f => f.OHIPPhysicianId.Contains(physicianClientNumber3));
                        if (copyDoctor != null)
                        {
                            if (!dls.Any(w => w.ExternalDoctorId == copyDoctor.Id))
                            {
                                var practiceDoctors = _context.PracticeDoctors.Where(ex => ex.ExternalDoctorId == copyDoctor.Id && ex.IsActive).ToList();
                                foreach (var practicedoctor in practiceDoctors)
                                {
                                    var d = new VMHL7ReportDoctor()
                                    {
                                        LastName = copyDoctor.lastName,
                                        FirstName = copyDoctor.firstName,
                                        OHIPPhysicianId = copyDoctor.OHIPPhysicianId,
                                        PracticeId = practicedoctor != null ? (int?)practicedoctor.PracticeId : null,
                                        practiceDoctorId = practicedoctor != null ? (int?)practicedoctor.Id : null,
                                        ExternalDoctorId = copyDoctor.Id,
                                        IsPrimary = !first,
                                        createdDate = DateTime.Now
                                    };

                                    dls.Add(d);
                                }
                            }
                        }
                    }
                }
                return dls;
            }
            catch (Exception ex)
            {
                _log.Error($"Find all Doctors Error: {ex.ExceptionDetails()}");
            }

            return null;
        }
        private List<HL7ReportNote> HL7ReportNote(List<NTE> notes, int reportVersionId)
        {
            var ns = new List<HL7ReportNote>();
            if (notes != null && notes.Count() > 0)
            {
                foreach (var no in notes)
                {
                    var n = new HL7ReportNote();
                    n.HL7ReportVersionId = reportVersionId;
                    n.setId = (int)no.SetID2;
                    n.comments = no.comment3;
                    n.createdDate = DateTime.Now;
                    ns.Add(n);
                }

            }
            return ns;
        }

        private HL7Report HL7Report(OBR obr, string accession, string cerebrumAccession, int hl7PatientId, int practiceId)
        {
            if (obr != null)
            {
                try
                {
                    var findhl7 = (from hr in _context.HL7Reports
                                   join hp in _context.HL7Patients on hr.HL7PatientId equals hp.Id
                                   join pr in _context.PatientRecords on hp.PatientRecordId equals pr.Id
                                   where hr.accessionNumber.Equals(accession) && pr.PracticeId == practiceId
                                   select hr);

                    if (findhl7 != null && findhl7.Count() > 0) return findhl7.FirstOrDefault();
                }
                catch (Exception e)
                {
                    _log.Error(e.ExceptionDetails());
                }

                var r = new HL7Report();
                r.accessionNumber = accession;

                var op = obr.orderingPhysician16;
                r.physicianNo = op.physicianOHIP3;
                r.physicianName = op.physicianName2;
                r.priority = obr.priority5;
                r.requestedDateTime = obr.requestedDatetime6;
                r.collectionDateTime = obr.collectionDateTime7;

                //var acc = new VMAccessionNumber(hl7Patient.healthCardNo,obr.collectionDateTime7,obr.requestedDatetime6,"GDML",acc);
                r.CerebrumAccession = cerebrumAccession;
                r.OLISAccession = null;
                r.assignmentStatus = DocumentAssignmentStatuses.Unassigned;
                r.resultedDateTime = obr.resultedDateTime22;
                //r.status 
                r.createdDate = DateTime.Now;
                r.HL7PatientId = hl7PatientId;
                return r;
            }
            return null;
        }
        private HL7ReportVersion HL7ReportVersion(OBR obr, int hl7MessageId, int hl7ReportId)
        {
            if (obr != null)
            {
                var r = new HL7ReportVersion();

                r.HL7MessageId = hl7MessageId;
                r.HL7ReportId = hl7ReportId;

                var op = obr.orderingPhysician16;
                r.physicianNo = op.physicianOHIP3;
                r.physicianName = op.physicianName2;
                r.priority = obr.priority5;
                r.createdDate = DateTime.Now;
                return r;
            }
            return null;
        }
        private HL7Result HL7Result(OBX o, int reportVersionId, OBR obr)
        {
            try
            {
                var obx = new HL7Result();
                obx.HL7ReportVersionId = reportVersionId;
                obx.setId = (int)o.SetID1;
                obx.valueType = o.valueType2;
                obx.testCodeIdentifier = o.observationIdentifier3.GDMLTestCode;
                obx.testDescription = o.observationIdentifier3.testDescription;
                obx.observationSubId = Convert.ToString(o.OobservationSubID4);
                obx.testResult = o.observationResult5;

                decimal testRestulFloat;
                if (decimal.TryParse(obx.testResult, out testRestulFloat))
                {
                    obx.testResultFloat = testRestulFloat;
                }

                obx.units = o.units6;
                obx.collectionDate = obr.collectionDateTime7;
                if (o.referenceRange7 != null)
                {
                    obx.structureRefRange = o.referenceRange7.structuredReferenceRange1;
                    obx.formattedRefRange = o.referenceRange7.formattedReferenceRange2;
                    if (o.referenceRange7.rang1 != null && (!string.IsNullOrWhiteSpace(o.referenceRange7.rang1)))
                    {
                        decimal rng1;
                        decimal.TryParse(o.referenceRange7.rang1, out rng1);
                        obx.range1 = rng1;
                    }
                    if (o.referenceRange7.rang2 != null && (!string.IsNullOrWhiteSpace(o.referenceRange7.rang2)))
                    {
                        decimal rng2;
                        decimal.TryParse(o.referenceRange7.rang2, out rng2);
                        obx.range2 = rng2;
                    }
                }
                string status = string.Empty;
                if (!(string.IsNullOrWhiteSpace(o.observationResultStatus10)))
                    status = o.observationResultStatus10;
                else if (!(string.IsNullOrWhiteSpace(o.observationResultStatus11)))
                    status = o.observationResultStatus11;
                //_log.Info($"Gamman Result Status: {status} actual1:{o.observationResultStatus10} actual2:{o.observationResultStatus11}");
                obx.resultStatus = status;
                obx.abnormalFlag = o.abnormalFlag8;
                //obx.resultStatus = o.observationResultStatus11;
                obx.reportGroupHead = obr.universalServiceID4.department4;
                obx.reportGroupDescription = obr.universalServiceID4.testDescription2;
                obx.createdDate = DateTime.Now;
                return obx;
            }
            catch (Exception ex)
            {
                _log.Error($"HL7Result {ex.ExceptionDetails()}");
            }
            return null;
        }
        private List<HL7ResultNote> HL7ResultNote(List<NTE> notes, int resultId)
        {
            var ns = new List<HL7ResultNote>();
            if (notes != null && notes.Count() > 0)
            {
                foreach (var no in notes)
                {
                    var n = new HL7ResultNote();
                    n.HL7ResultId = resultId;
                    n.setId = (int)no.SetID2;
                    n.comments = no.comment3;
                    n.createdDate = DateTime.Now;
                    ns.Add(n);
                }

            }
            return ns;
        }
        private bool IsValidPath(string path)
        {
            try
            {
                Regex driveCheck = new Regex(@"^[a-zA-Z]:\\$");
                if (!driveCheck.IsMatch(path.Substring(0, 3))) return false;
                string strTheseAreInvalidFileNameChars = new string(Path.GetInvalidPathChars());
                strTheseAreInvalidFileNameChars += @":/?*" + "\"";
                Regex containsABadCharacter = new Regex("[" + Regex.Escape(strTheseAreInvalidFileNameChars) + "]");
                if (containsABadCharacter.IsMatch(path.Substring(3, path.Length - 3)))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}