﻿using Cerebrum.Labs.common;

namespace Cerebrum.Labs.GammaDynacare.Models
{
    /// <summary>
    /// Z<PERSON>- Physician/Client/CC Segment
    /// </summary>
    public class ZDR
    {
        public string SetID1 { get; set; }
        public string segmentType2 { get; set; }
        public string physicianClientNumber3 { get; set; }
        public string physicianOrClientOrCompanyName41 { get; set; }
        public string physicianLastName42 { get; set; }
        public string physicianMiddleName43 { get; set; }
        public Address physicianAddress5 { get; set; }
        public string telephone6 { get; set; }


        public static ZDR Parse(string s)
        {
            if (!string.IsNullOrEmpty(s))
            {
                string[] v = s.Split('|');
                var zdr = new ZDR();
                zdr.SetID1 = v[1];
                zdr.segmentType2 = v[2];
                zdr.physicianClientNumber3 = v[3];
                if (zdr.segmentType2.Equals("P") || v[4].Contains("^"))
                {
                    var name = Name.Parse(v[4]);
                    zdr.physicianOrClientOrCompanyName41 = name.familyName;
                    zdr.physicianLastName42 = name.givenName;
                    zdr.physicianMiddleName43 = name.middleInitial;
                }
                else if(zdr.segmentType2.Equals("C"))
                {
                    zdr.physicianOrClientOrCompanyName41 = v[4];
                }
                zdr.physicianAddress5 = Address.Parse(v[5]);
                zdr.telephone6 = v[6];
                return zdr;
            }
            return null;
        }
    }
}
