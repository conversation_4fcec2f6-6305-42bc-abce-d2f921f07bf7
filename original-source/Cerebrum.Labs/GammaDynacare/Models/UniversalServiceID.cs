﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.GammaDynacare.Models
{
    /// <summary>
    /// OBR Universal ServiceID
    /// </summary>
    public class UniversalServiceID
    {
        public string GDMLTestCode1 { get; set; }
        public string testDescription2 { get; set; }
        public string MOHTestCode3 { get; set; }
        public string department4 { get; set; }

        public static UniversalServiceID Parse(string s)
        {
            if(!string.IsNullOrEmpty(s))
            {
                string[] v = s.Split('^');
                return new UniversalServiceID {
                    GDMLTestCode1 = v[0],
                    testDescription2=v[1],
                    MOHTestCode3=v[2],
                    department4=v.Length>3? v[3]:string.Empty
                };
            }
            return null;
        }
        public override string ToString()
        {
            try
            {
                return $"{this.GDMLTestCode1}^{this.testDescription2}^{this.MOHTestCode3}^{this.department4}";
            }
            catch
            {

            }
            return null;
        }
    }
}
