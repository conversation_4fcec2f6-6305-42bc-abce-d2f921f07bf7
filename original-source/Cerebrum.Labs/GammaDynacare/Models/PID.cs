﻿using Cerebrum.Labs.common;
using System;
using System.Collections.Generic;
using System.Globalization;

namespace Cerebrum.Labs.GammaDynacare.Models
{
    /// <summary>
    /// PID - Patient Identification
    /// </summary>
    public class PID
    {
        public int? SetID1 { get; set; }
        public PatientExternalID patientExternalID2 { get; set; }
        public string patientIDInternalID3 { get; set; }

        public Name patientName5 { get; set; }
        public DateTime? dateOfBirth7 { get; set; }
        public string sex8 { get; set; }
        public Address patientAddress11 { get; set; }
        public string patientPhoneNumber13 { get; set; }

        public List<NTE> NTEs { get; set; } = new List<NTE>();
        public List<ZDR> ZDRs { get; set; } = new List<ZDR>();
        public List<OBR> OBRs { get; set; } = new List<OBR>();

        public static PID Parse(string s)
        {
            if (s != null && s.Trim().StartsWith("PID"))
            {
                string[] v = s.Split('|');
                var pid = new PID();
                pid.SetID1 = v[1].Trim() != "" ? (int?)int.Parse(v[1]) : null;
                pid.patientExternalID2 = PatientExternalID.Parse(v[2]);
                pid.patientIDInternalID3 = v[3];
                pid.patientName5 = Name.Parse(v[5]);
                pid.dateOfBirth7 = v[7].Trim() != "" ? (DateTime?)DateTime.ParseExact(v[7], "yyyyMMdd", CultureInfo.InvariantCulture) : null;
                pid.sex8 = v[8];
                pid.patientAddress11 = Address.Parse(v[11]);
                pid.patientPhoneNumber13 = v.Length>12? v[13]:"";
                return pid;
            }
            return null;
        }
    }
}
