﻿using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.GammaDynacare.Models
{
    /// <summary>
    /// Read GammaDynacare HL7 File
    /// </summary>
    public class GDMLReadHL7File
    {
        private readonly ILog _log = LogManager.GetLogger(typeof(GDMLReadHL7File));

        public GDMLHL7Report ReadFile(string filepath)
        {
            var text = File.ReadAllLines(filepath);
            return Report(text);
        }
        public GDMLHL7Report Report(string[] text)
        {
            //var text = File.ReadAllLines(filepath);
            var hl7 = new GDMLHL7Report();
            bool pidflag = false;
            bool obrflag = false;
            bool obxflag = false;
            foreach (var l in text)
            {
                if(l.Trim() != "")
                {
                    var start = l.Substring(0, 3);
                    switch (start)
                    {
                        case "MSH":
                            {
                                hl7.MSH1 = MSH.Parse(l);
                            }
                            break;
                        case "PID":
                            {
                                pidflag = true;
                                obrflag = false;
                                obxflag = false;
                                var pid = PID.Parse(l);
                                hl7.PIDs2.Add(pid);
                            } break;
                        case "NTE":
                            {
                                var nte = NTE.Parse(l);
                                if (pidflag)
                                {
                                    var pid=hl7.PIDs2.Last();
                                    pid.NTEs.Add(nte);
                                }
                                else if(obrflag)
                                {
                                    var pid = hl7.PIDs2.Last();
                                    var obr = pid.OBRs.Last();
                                    obr.NTEs.Add(nte);
                                }else if(obxflag)
                                {
                                    var pid = hl7.PIDs2.Last();
                                    var obr = pid.OBRs.Last();
                                    var obx = obr.OBXs.Last();
                                    obx.NTEs.Add(nte);
                                }
                            }
                            break;
                        case "ZDR":
                            {
                                var zdr = ZDR.Parse(l);
                                if(pidflag)
                                {
                                    var pid = hl7.PIDs2.Last();
                                    pid.ZDRs.Add(zdr);
                                }
                            }break;
                        case "OBR":
                            {
                                var obr = OBR.Parse(l);
                                pidflag = false;
                                obrflag = true;
                                obxflag = false;
                                var pid = hl7.PIDs2.Last();
                                pid.OBRs.Add(obr);
                            }
                            break;
                        case "OBX": {
                                pidflag = false;
                                obrflag = false;
                                obxflag = true;
                                var pid = hl7.PIDs2.Last();
                                var obr = pid.OBRs.Last();
                                var obx = OBX.Parse(l);
                                if (obx.valueType2.Equals("FT"))
                                {
                                    var preobx = obr.OBXs.FirstOrDefault(f => f.observationIdentifier3.GDMLTestCode.Trim().ToLower().Equals(obx.observationIdentifier3.GDMLTestCode.ToLower().Trim()));
                                    //if (preobx == null)
                                    //{
                                    //    preobx = obr.OBXs.LastOrDefault();
                                    //}
                                    if (preobx != null)
                                    {
                                        int count = (preobx != null && preobx.NTEs.Count() > 0) ? preobx.NTEs.Count() + 1 : 1;
                                        string[] comments = obx.observationResult5.Split('~');
                                        foreach (var c in comments)
                                        {
                                            var nte = new NTE();
                                            nte.SetID2 = (count++);
                                            nte.comment3 = c;//.Replace("\\.br\\","");
                                            preobx.NTEs.Add(nte);
                                        }
                                    }
                                    else
                                    {
                                        obr.OBXs.Add(obx);
                                    }
                                }
                                else
                                {
                                    if(obx.referenceRange7!=null && obx.referenceRange7.parsedString.Count()>0)
                                    {
                                        int count = 1;
                                        foreach (var c in obx.referenceRange7.parsedString)
                                        {
                                            var nte = new NTE();
                                            nte.SetID2 = (count++);
                                            nte.comment3 = c;
                                            obx.NTEs.Add(nte);
                                        }
                                    }
                                    obr.OBXs.Add(obx);
                                }
                            } break;
                    }
                }
                
            }

            return hl7?? null;
        }
    }
}
