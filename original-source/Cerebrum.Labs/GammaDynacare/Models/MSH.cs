﻿using System;
using System.Globalization;

namespace Cerebrum.Labs.GammaDynacare.Models
{
    /// <summary>
    /// MSH - Message Header
    /// </summary>
    public class MSH
    {
        public string encodingCharacters1 { get; set; }
        public string sendingApplication2 { get; set; }
        public DateTime? dateTimeOfMessage6 { get; set; }
        public string messageType8 { get; set; }
        public string messageControlID9 { get; set; }
        public string processingID10 { get; set; }
        public string versionID11 { get; set; }

        public static MSH Parse(string s)
        {
            if(s!=null && s.Trim().StartsWith("MSH"))
            {
                string[] v = s.Split('|');
                var msh = new MSH();
                msh.encodingCharacters1 = v[1];
                msh.sendingApplication2 = v[2];
                var dateformat = v[6].Length > 12 ? "yyyyMMddHHmmss" : "yyyyMMddHHmm";
                msh.dateTimeOfMessage6= v[6].Trim() != "" ? (DateTime?)DateTime.ParseExact(v[6], dateformat, CultureInfo.InvariantCulture) : null;
                msh.messageType8 = v[8];
                msh.messageControlID9 = v[9];
                msh.processingID10 = v[10];
                msh.versionID11 = v[11];
                return msh;
            }
            return null;
        }

    }
}
