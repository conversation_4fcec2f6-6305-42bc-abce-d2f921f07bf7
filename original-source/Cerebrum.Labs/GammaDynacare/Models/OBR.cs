﻿using System;
using System.Collections.Generic;
using System.Globalization;

namespace Cerebrum.Labs.GammaDynacare.Models
{
    /// <summary>
    /// GammaDynacare Observation Request Segment
    /// </summary>
    public class OBR
    {
        public int? SetID1 { get; set; }
        public UniversalServiceID universalServiceID4 { get; set; }
        public string priority5 { get; set; }
        public DateTime? requestedDatetime6 { get; set; }
        public DateTime? collectionDateTime7 { get; set; }
        public string collectionVolumn9 { get; set; }
        public string abnormalFlag11 { get; set; }
        public OrderingPhysician orderingPhysician16 { get; set; }
        public DateTime? resultedDateTime22 { get; set; }
        public List<NTE> NTEs { get; set; } = new List<NTE>();
        public List<OBX> OBXs { get; set; } = new List<OBX>();
        public static OBR Parse(string s)
        {
            if(!string.IsNullOrEmpty(s))
            {
                string[] v = s.Split('|');
                var obr = new OBR();
                obr.SetID1 = v[1].Trim() != "" ? (int?)int.Parse(v[1]) : null;
                obr.universalServiceID4 = UniversalServiceID.Parse(v[4]);
                obr.priority5 = v[5];
                var dateformat = v[6].Length > 12 ? "yyyyMMddHHmmss" : "yyyyMMddHHmm";
                obr.requestedDatetime6= v[6].Trim() != "" ? (DateTime?)DateTime.ParseExact(v[6], dateformat, CultureInfo.InvariantCulture) : null;
                dateformat = v[7].Length > 12 ? "yyyyMMddHHmmss" : "yyyyMMddHHmm";
                obr.collectionDateTime7 = v[7].Trim() != "" ? (DateTime?)DateTime.ParseExact(v[7], dateformat, CultureInfo.InvariantCulture) : null;
                obr.abnormalFlag11 = v[11];
                obr.orderingPhysician16 = OrderingPhysician.Parse(v[16]);
                dateformat = v[22].Length > 12 ? "yyyyMMddHHmmss" : "yyyyMMddHHmm";
                obr.resultedDateTime22 = v[22].Trim() != "" ? (DateTime?)DateTime.ParseExact(v[22], dateformat, CultureInfo.InvariantCulture) : null;
                return obr;
            }
            return null;
        }
    }
}
