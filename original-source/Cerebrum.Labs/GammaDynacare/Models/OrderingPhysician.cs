﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.GammaDynacare.Models
{
    public class OrderingPhysician
    {
        public string physician1 { get; set; }
        public string physicianName2 { get; set; }
        public string physicianOHIP3 { get; set; }
        public static OrderingPhysician Parse(string s)
        {
            if(!string.IsNullOrEmpty(s))
            {
                string[] v = s.Split('^');
                if (v.Count() < 3)
                {
                    var op = new OrderingPhysician();
                    op.physicianOHIP3 = v[0];
                    op.physicianName2 = v[1];
                    return op;
                }
                else
                {
                    var op = new OrderingPhysician();

                    op.physician1 = v[0];
                    op.physicianName2 = v[1];
                    op.physicianOHIP3 = v[2];
                }
            }
            return null;
        }
    }
}
