﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.GammaDynacare.Models
{
    /// <summary>
    /// OBX - Observation result
    /// </summary>
    public class OBX
    {

        public int? SetID1 { get; set; }
        public string valueType2 { get; set; }
        public ObservationIdentifier observationIdentifier3 { get; set; }
        public int? OobservationSubID4 { get; set; }
        public string observationResult5 { get; set; }
        public string units6 { get; set; }
        public ReferenceRange referenceRange7 { get; set; }
        public string abnormalFlag8 { get; set; }
        public string observationResultStatus10 { get; set; }
        public string observationResultStatus11 { get; set; }
        public List<NTE> NTEs { get; set; } = new List<NTE>();
        public static OBX Parse(string s)
        {
            if (s != null && s.Trim().StartsWith("OBX"))
            {
                string[] v = s.Split('|');
                var obx = new OBX();
                obx.SetID1 = v[1].Trim() != "" ? (int?)int.Parse(v[1]) : null;
                obx.valueType2 = v[2];
                obx.observationIdentifier3 = ObservationIdentifier.Parse(v[3]);
                obx.OobservationSubID4 = v[4].Trim() != "" ? (int?)int.Parse(v[4]) : null;
                obx.observationResult5 = v[5];
                obx.units6 = v[6];
                obx.referenceRange7 = ReferenceRange.Parse(v[7]);
                obx.abnormalFlag8 = v[8];
                obx.observationResultStatus10 = v[10];
                obx.observationResultStatus11 = v[11];
                return obx;
            }
            return null;
        }
    }
}
