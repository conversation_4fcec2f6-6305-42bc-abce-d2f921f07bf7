﻿using Cerebrum.Labs.Common;
using System;
using System.Configuration;

namespace Cerebrum.Labs.Service
{
    public static class EncapsulateDataService
    {
        private static EncapsulatedDataFileHelper _encapsulatedDataFileHelper;

        /// <summary>
        /// Initializes the service by configuring the file paths for encapsulated data and the clinical viewer URL.
        /// Must be called before accessing the EncapsulatedDataFileHelper.
        /// </summary>
        public static void Initialize()
        {
            string appData = ConfigurationManager.AppSettings["Cerebrum3AppData"];
            string clinicalViewerEd = ConfigurationManager.AppSettings["Cerebrum3ClinicalViewerED"];

            _encapsulatedDataFileHelper = new EncapsulatedDataFileHelper(appData, clinicalViewerEd);
        }

        /// <summary>
        /// Gets the initialized instance of EncapsulatedDataFileHelper.
        /// Throws an exception if Initialize() has not been called beforehand.
        /// </summary>
        public static EncapsulatedDataFileHelper EncapsulatedDataFileHelper
        {
            get
            {
                if (_encapsulatedDataFileHelper == null)
                    throw new InvalidOperationException("ServiceLocator.Initialize() must be called before using services.");
                return _encapsulatedDataFileHelper;
            }
        }
    }
}

