﻿using Cerebrum.Data;
using Cerebrum.Labs.GammaDynacare.Services;
using Cerebrum.Labs.LifeLabs.Common;
using Cerebrum.Labs.LifeLabs.services;
using Cerebrum.Labs.LifeLabs.Services;
using Cerebrum.Labs.OLIS.services;
using Cerebrum.ViewModels.OLIS;
using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Cerebrum.Labs.Service
{
    /// <summary>
    /// File upload service
    /// </summary>
    public class FileUploadService
    {
        private readonly ILog _log = LogManager.GetLogger(typeof(FileUploadService));
        private string _ipAddress = "";
        private GammaDynacareReportService _gammaService;
        private string Dropbox_Path;
        private enum Labs { LifeLabs, GammaDynacare, OLIS };
        private int HL7UserId = 0;

        public FileUploadService(string IPAddress)
        {
            HL7UserId = 606;
            _ipAddress = IPAddress;
        }
        public FileUploadService(int userId, string IPAddress)
        {
            HL7UserId = userId;
            _ipAddress = IPAddress;
        }
        public FileUploadService(string IPAddress, string userName, string dropBox)
        {
            FindHL7User(userName);
            _ipAddress = IPAddress;
            Dropbox_Path = dropBox;
        }
        public bool ProcessHL7File(string text, int userId, string ipaddress)
        {
            bool labFound = false;

            if (text.Contains('\v') || text.StartsWith(Constants.DEFAULT_HEADER) || text.StartsWith(Constants.EXCELLERIS_HEADER))
            {
                var hl7txts = text.Split('\v').Where(s => (!string.IsNullOrEmpty(s))).ToList();
                foreach (var t in hl7txts)
                {
                    var replacer = t.Replace("\r", "").Replace("\n", "");
                    if (replacer.StartsWith(Constants.DEFAULT_HEADER) || replacer.StartsWith(Constants.EXCELLERIS_HEADER))
                    {
                        labFound = true;
                        UploadLifeLabHL7File(text);
                    }
                }
            }

            if (!labFound)
            {
                if (text.Contains(@"MSH|^~\&|GDML"))
                {
                    labFound = true;
                    GammaDynacareReportService srv = new GammaDynacareReportService(userId, _ipAddress);
                    srv.SaveReport(text);
                }
            }
            if (!labFound)
            {
                if (text.Contains(@"MSH|^~\&|^OLIS^X500"))
                {
                    labFound = true;
                    var upresp = UploadOLISHL7(25, 61, 1, "83206", text, ipaddress);
                }
            }


            if (!labFound)
            {
                System.Diagnostics.Debug.WriteLine("This File is not from LifeLabs/GammaDynacare/");
            }

            return labFound;
        }
        public List<VMOLISReportSave> ProcessFile(int practiceId, int officeId, int userId, string requestingHic, string ipaddress)
        {
            if (string.IsNullOrWhiteSpace(Dropbox_Path)) return null;
            List<VMOLISReportSave> response = new List<VMOLISReportSave>();
            //if (string.IsNullOrEmpty(configPath) || configPath.Length < 2)
            //    configPath = Dropbox_Path;

            var di = new System.IO.DirectoryInfo(Dropbox_Path);
            var allfiles = di.GetFiles();
            foreach (var f in allfiles)
            {
                bool labFound = false;

                var text = File.ReadAllText(f.FullName);
                if (text.Contains('\v') || text.StartsWith(Constants.DEFAULT_HEADER) || text.StartsWith(Constants.EXCELLERIS_HEADER))
                {
                    var hl7txts = text.Split('\v').Where(s => (!string.IsNullOrEmpty(s))).ToList();
                    foreach (var t in hl7txts)
                    {
                        var replacer = t.Replace("\r", "").Replace("\n", "");
                        if (replacer.StartsWith(Constants.DEFAULT_HEADER) || replacer.StartsWith(Constants.EXCELLERIS_HEADER))
                        {
                            labFound = true;
                            var allre = UploadLifeLabHL7(f.FullName);
                            response.AddRange(allre);
                        }
                    }
                }

                if (!labFound)
                {
                    var textLines = File.ReadAllLines(f.FullName);
                    if (textLines.Any(a => a.StartsWith(@"MSH|^~\&|GDML")))
                    {
                        labFound = true;
                        SaveGammaDynacareHL7(f.FullName);
                    }
                }
                if (!labFound)
                {
                    var textLines = File.ReadAllLines(f.FullName);

                    if (textLines.Any(a => a.StartsWith(@"MSH|^~\&|^OLIS^X500")))
                    {
                        labFound = true;
                        var upresp = UploadOLISHL7(practiceId, officeId, userId, requestingHic, f.FullName, ipaddress);
                        response.Add(upresp);
                    }
                    else //olis error
                    {
                        var upresp = UploadOLISHL7(practiceId, officeId, userId, requestingHic, f.FullName, ipaddress);
                        response.Add(upresp);
                    }
                }

                if (!labFound)
                {
                    System.Diagnostics.Debug.WriteLine("This File is not from LifeLabs/GammaDynacare/OLIS");
                }
            }

            return response;
        }

        /// <summary>
        /// Upload Life Lab HL7 File
        /// </summary>
        /// <param name="filepath"></param>
        /// <returns></returns>
        public List<VMOLISReportSave> UploadLifeLabHL7(string filepath)
        {
            _log.Info("FileUpload: UploadLifeLabHL7: Path: " + filepath);

            var lls = new LifeLabReportService(HL7UserId, _ipAddress);
            lls.SaveUploadReport(filepath);
            return lls.ResponseSaved;
        }
        public bool UploadLifeLabHL7File(string filepath)
        {
            _log.Info("FileUpload: UploadLifeLabHL7: Path: " + filepath);

            var lls = new LifeLabReportService(HL7UserId, _ipAddress);

            return lls.SaveUploadReport(filepath);
        }
        public bool SaveLostReport(int HL7LostReportId, string lab)
        {
            bool saved = false;
            
            lab = lab.Trim().ToLower();

            switch (lab)
            {
                case "gdml":
                    {
                        saved = SaveGDMLHL7LostReport(HL7LostReportId);
                    }
                    break;
                case "lifelabs":
                    {
                        saved = SaveLifeLabHL7LostReport(HL7LostReportId);
                    }
                    break;
            }
            return saved;
        }
        public bool SaveLifeLabHL7LostReport(int HL7LostReportId)
        {
            _log.Info($"File Save: SaveLifeLabHL7LostReport: Path: {HL7LostReportId}");

            var lls = new LifeLabReportService(HL7UserId, _ipAddress);
            var saved = lls.SaveUploadTextReport(HL7LostReportId);
            return saved;
        }
        public bool SaveGDMLHL7LostReport(int HL7LostReportId)
        {
            _log.Info($"File Save: Save GDML HL7LostReport: Path: {HL7LostReportId}");

            var gds = new GammaDynacareReportService(HL7UserId, _ipAddress);
            var saved = gds.SaveLostReport(HL7LostReportId);
            return saved;
        }
        public int SaveLifeLabReports(string configPath)
        {
            // prepare list for missed doctors
            Dictionary<int, string> MissedDoctors = new Dictionary<int, string>();

            string[] allfiles = null;
            try
            {
                allfiles = Directory.GetFiles(configPath, "*.*", SearchOption.AllDirectories);
            }
            catch (Exception ex)
            {
                _log.Error($"Directory not accessible {ex.ToString()}");
            }
            if (allfiles != null && allfiles.Count() > 0)
            {
                foreach (var file in allfiles)
                {
                    LifeLabReportService srv = new LifeLabReportService(HL7UserId, _ipAddress, file);
                    srv.SaveReport(file);

                    CopyDictionary(MissedDoctors, srv.MissedDoctors);

                    _log.Info("FileUpload: UploadLifeLabHL7: Path: " + file);
                }

                CheckMissedDoctors(MissedDoctors);

                return allfiles.Length;
            }
            return 0;

        }
        public int SaveLifeLabReport(string filePath)
        {
            LifeLabReportService srv = new LifeLabReportService(HL7UserId, _ipAddress, filePath);
            srv.SaveReport(filePath);
            _log.Info("FileUpload: UploadLifeLabHL7: Path: " + filePath);

            return 0;

        }
        private void CheckMissedDoctors(Dictionary<int, string> missedDoctors)
        {
            _log.Info("Missed Doctors codes:");
            foreach (var it in missedDoctors)
            {
                _log.Info($"{it.Key}: {it.Value}");
            }
        }

        private void CopyDictionary(Dictionary<int, string> dto, Dictionary<int, string> dfrom)
        {
            foreach (var item in dfrom)
            {
                if (!dto.ContainsKey(item.Key))
                {
                    dto.Add(item.Key, item.Value);
                }
            }
        }

        /// <summary>
        /// Upload GammaDynacare HL7 File
        /// </summary>
        /// <param name="filepath"></param>
        /// <returns></returns>
        public void UploadGammaReport(string filepath)
        {
            _gammaService = new GammaDynacareReportService(HL7UserId, _ipAddress);
            _gammaService.SaveReport(filepath);
            _log.Info("FileUpload: UploadGammaDynacareHL7: Path: " + filepath);
        }

        public int SaveGammaDynacareHL7(string configPath)
        {
            string[] allfiles = null;
            try
            {
                allfiles = Directory.GetFiles(configPath, "*.*", SearchOption.AllDirectories);
            }
            catch (Exception ex)
            {
                _log.Error($"Directory not accessible {ex.ToString()}");
            }

            if (allfiles != null && allfiles.Count() > 0)
            {
                foreach (var file in allfiles)
                {
                    GammaDynacareReportService srv = new GammaDynacareReportService(file, HL7UserId, _ipAddress);
                    srv.SaveReport(file);
                    _log.Info("FileUpload: UploadGammaDynacareHL7: Path: " + file);
                }
                return allfiles.Count();
            }
            return 0;

        }

        public VMOLISReportSave UploadOLISHL7(int practiceId, int officeId, int userId, string requestingHic, string filepath, string ipaddress)
        {
            var oser = new OLISReportService(0, HL7UserId, _ipAddress);

            _log.Info("FileUpload: UploadOLISHL7: Path: " + filepath);
            return oser.SaveReport(practiceId, officeId, userId, requestingHic, filepath, ipaddress);
        }

        private int FindHL7User(string userName)
        {
            try
            {
                CerebrumContext _c3context = new CerebrumContext();
                HL7UserId = string.IsNullOrWhiteSpace(userName) ? 0 : _c3context.Users.FirstOrDefault(uf => uf.Email.Equals(userName)).UserID;
                return HL7UserId;
            }
            catch (Exception ex)
            {
                _log.Error($"HL7 user not found. {ex.ToString()}");
            }
            return 0;
        }

        public async Task DownloadLifeLabFilesAsync(string destinationPath)
        {
            try
            {
                FSService srv = new FSService();
                await srv.DownloadLifeLabFilesAsync(destinationPath);
                _log.Info("DownloadLifeLabFiles process completed");
            }
            catch (Exception ex)
            {
                _log.Error("Error occurred in DownloadLifeLabFilesAsync", ex);
                throw;
            }
        }
    }
}