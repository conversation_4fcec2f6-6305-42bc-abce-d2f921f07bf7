﻿using Cerebrum.Data;
using log4net;
using System.Collections.Generic;
using System.Linq;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Configuration;
using System.IO;
using Cerebrum.Labs.common;
using Cerebrum.ViewModels.HL7;
using Cerebrum.ViewModels.OLIS;

namespace Cerebrum.Labs.Service
{
    abstract public class LabReportServiceBase
    {
        protected int PracticeId = 0;
        protected int UserId = 0;
        protected CerebrumContext _context;

        protected string filePath;
        protected string IPAddress = "";
        protected int REPORT_VERSIONS_COUNT_SETTING = 10;
        public string processedHL7filePath { get; set; }
        public string ERROR_PATH { get; set; }

        protected ILog _log = LogManager.GetLogger(typeof(LabReportServiceBase));

        abstract public bool SaveUploadReport(string filePath);

        public LabReportServiceBase(int userId, string ipAddress, string filePath = "")
        {
            _context = new CerebrumContext();
            UserId = userId;
            IPAddress = ipAddress;
            object pathProc = ConfigurationManager.AppSettings["HL7ProcessedLabResultFiles"].ToString();
            processedHL7filePath = (pathProc != null) ? pathProc.ToString() : ".\\proc\\";
            pathProc = ConfigurationManager.AppSettings["HL7ERRORLabResultFiles"];
            ERROR_PATH = (pathProc != null) ? pathProc.ToString() : ".\\error\\";

            var reportVersionCountSetting = ConfigurationManager.AppSettings["HL7ReportVersionsCountSettings"]?.ToString();
            REPORT_VERSIONS_COUNT_SETTING = reportVersionCountSetting != null ? int.Parse(reportVersionCountSetting) : 10;
        }
        public void SaveReport(string _filePath)
        {
            filePath = _filePath;

            _log.Info($"Processing file {filePath}");
            SaveUploadReport(filePath);
        }

        protected List<PracticeDoctor> PracticeDoctorsByOHIP(string DrOhipBilling)
        {
            //int practiceId = 0;
            List<PracticeDoctor> _practiceDoctor = null;
            if (!string.IsNullOrWhiteSpace(DrOhipBilling))
            {
                var externalDoc = _context.ExternalDoctors.Where(w => (w.OHIPPhysicianId != null && w.OHIPPhysicianId.Trim().Equals(DrOhipBilling)));
                if (externalDoc != null && externalDoc.Count() > 0)
                {
                    var ids = externalDoc.Select(s => s.Id);
                    var prcs = _context.PracticeDoctors.Where(pw => ids.Contains(pw.ExternalDoctorId) && pw.IsActive == true);

                    if (prcs.Count() > 0)
                    {
                        //practiceId = prcs.First().PracticeId;
                        _practiceDoctor = prcs.ToList();
                    }
                }
            }
            //return practiceId;
            return _practiceDoctor;
        }
        protected int FindPatient(int practiceId, string hcard, string fname, string lname, string gender, DateTime? DOB)
        {
            PatientRecord cerebrumpatient = null;

            if (OntarioHealthCard.IsValidCheck(hcard))
            {
                /* US#2904 Acceptance Criteria
                   1. Include patients with all statues, not only "Active" status
                 */
                cerebrumpatient = GetPatientRecord(practiceId, hcard);
                if (cerebrumpatient != null)
                {
                    return cerebrumpatient.Id;
                }
            }

            if (cerebrumpatient == null)
            {
                #region Patient Lookup
                try
                {
                    string lastname = lname.Trim().ToLower();
                    string firstname = fname.Trim().ToLower();

                    List<Demographic> patQuery = GetDemographics(practiceId, lastname, firstname, DOB);

                    if (!string.IsNullOrWhiteSpace(gender))
                    {
                        Gender g = (Gender)Enum.Parse(typeof(Gender), gender);
                        List<Demographic> pq = (from p in patQuery where p.gender == g select p).ToList();

                        patQuery = pq;
                    }

                    _log.Debug($"Practice:{practiceId} hc:{hcard.Substring(5)} demographics:{lastname} records:{patQuery.Count}");

                    Demographic patient = patQuery.Count() > 1 ? patQuery.FirstOrDefault(f => f.active == Active.Active) : patQuery.FirstOrDefault();
                    
                    if (patient != null)
                    {
                        return patient.PatientRecordId;
                    }
                }
                catch (Exception ex)
                {
                    _log.Error("Patient not found in cerebrum");
                    _log.Error(ex.Message);
                }
                #endregion
            }
            return 0;
        }
        public virtual List<Demographic> GetDemographics(int practiceId, string lastname, string firstname, DateTime? DOB)
        {
            var patQuery = (from cp in _context.PatientRecords
                            join dem in _context.Demographics on cp.Id equals dem.PatientRecordId
                            where cp.PracticeId == practiceId
                                            && dem.lastName.ToLower().Equals(lastname)
                                            && dem.firstName.ToLower().Equals(firstname)
                                            && dem.dateOfBirth == DOB
                            //&& dem.active == Active.Active
                            select dem);
            return patQuery.ToList();
        }
        public virtual PatientRecord GetPatientRecord(int practiceId, string hcard)
        {
            return (from cp in _context.PatientRecords
                    join dem in _context.Demographics on cp.Id equals dem.PatientRecordId
                    join dh in _context.HealthCards on dem.Id equals dh.DemographicId
                    where dh.number.Equals(hcard) && dem.active==Active.Active && cp.PracticeId == practiceId
                    select cp).FirstOrDefault();
        }

        protected HL7Message HL7Message(string filepath,
            string sendingApp,
            string messageType,
            DateTime? messageDate,
            DateTime createdDate
            )
        {
            try
            {
                // file can be deleted when processing first MSH in a file
                if (File.Exists(filepath))
                {
                    //Compressed the file and move
                    try
                    {
                        try
                        {
                            var path = Path.Combine(processedHL7filePath, DateTime.Today.ToString("yyyyMMdd"));
                            if (!Directory.Exists(path))
                                Directory.CreateDirectory(path);
                            FileInfo msgZipFile = new FileInfo(filepath);
                            string compressedFile = CompressFile.Compress(msgZipFile, Path.Combine(path, msgZipFile.Name));

                        }
                        catch (Exception ex)
                        {
                            _log.Error($"Compressed File writing error {filepath}", ex);
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Error($"Compressed File writing error {filepath}", ex);
                    }
                }
                var msh = new HL7Message();
                msh.sendingApp = sendingApp;
                msh.messageType = messageType;
                msh.messageDate = messageDate;
                msh.createdDate = createdDate;
                return msh;
            }
            catch (Exception ex)
            {
                _log.Error("Error Saving message to DB: " + ex.Message + "\n" + ex.Source, ex);
            }
            return null;
        }
        protected VMAccessionNumberCheck AccessionNumberExists(string patientHealthCard, DateTime? collectionDateTime7, DateTime? requestedDatetime6, string reportinglab, string labaccession, int practiceId)
        {
            var check = new VMAccessionNumberCheck();
            DateTime cdate = DateTime.Now;

            if (collectionDateTime7 != null)
                cdate = (DateTime)collectionDateTime7;
            else
            {
                cdate = (DateTime)requestedDatetime6;
            }
            string tempacc = string.Empty;
            if (labaccession.IndexOf('-') > 0)
            {
                string[] accs = labaccession.Split('-');
                if (accs.Length > 1)
                    tempacc = accs[1];
            }
            else if (string.IsNullOrWhiteSpace(tempacc))
            {
                tempacc = labaccession.Length > 9 ? labaccession.Substring(labaccession.Length - 9) : labaccession;
            }
            var acc = new VMAccessionNumber(patientHealthCard, cdate, reportinglab, tempacc);
            check.CerebrumAccession = acc.CerebrumAccessionNumber;
            var dbreports = (from hr in _context.HL7Reports
                             join hp in _context.HL7Patients on hr.HL7PatientId equals hp.Id
                             join pr in _context.PatientRecords on hp.PatientRecordId equals pr.Id
                             where hr.accessionNumber.Equals(labaccession) && pr.PracticeId == practiceId
                             select new { hr, VersionCount = hr.HL7ReportVersions.Count() });
            check.IsExists = dbreports != null && dbreports.Any();
            try
            {
                check.TotalReportVersions = (dbreports != null && dbreports.Any()) ? dbreports.FirstOrDefault().VersionCount : 0;
            }
            catch (Exception ex)
            {
                _log.Error("Version Count Error:" + ex.ToString());
            }
            return check;
        }
    }
}