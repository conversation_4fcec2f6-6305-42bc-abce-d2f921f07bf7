﻿using Cerebrum.Data;
using Cerebrum.Labs.common;
using Cerebrum.ViewModels.HL7;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace Cerebrum.Labs.Service
{
    public interface iHL7ReportService
    {
        HL7ResultVM GetSensitiveResult(int resultid, string reason, int practiceId, int userId, string ipAddress);
        List<VMHL7SensitiveResultAccessLog> GetSensitiveResultAccessedLog(int resultid, int practiceId);
    }
    public class HL7ReportService : iHL7ReportService
    {
        private readonly CerebrumContext _context;
        public HL7ReportService()
        {
            _context = new CerebrumContext();
        }
        public HL7ReportService(CerebrumContext context)
        {
            _context = context;
        }
        public HL7ResultVM GetSensitiveResult(int resultid,string reason, int practiceId, int userId, string ipAddress)
        {
            HL7ResultVM result = null;
            using (TransactionScope transactionScope = TransactionUtils.CreateTransactionScope())
            {
                HL7SensitiveResultAccessLog log = new HL7SensitiveResultAccessLog();
                var find = (from res in _context.HL7Results
                            join rv in _context.HL7ReportVersions on res.HL7ReportVersionId equals rv.Id
                            join rp in _context.HL7Reports on rv.HL7ReportId equals rp.Id
                            join pat in _context.HL7Patients on rp.HL7PatientId equals pat.Id
                            join pr in _context.PatientRecords on pat.PatientRecordId equals pr.Id
                            where pr.PracticeId == practiceId && res.Id == resultid
                            select new { res, HL7PatientId = pat.Id, HL7ReportId = rp.Id, HL7ReportVersionId = rv.Id, HL7ResultId = res.Id }).FirstOrDefault();
                log.Reason = reason;
                log.HL7PatientId = find.HL7PatientId;
                log.HL7ReportId = find.HL7ReportId;
                log.HL7ReportVersionId = find.HL7ReportVersionId;
                log.HL7ResultId = find.HL7ResultId;
                log.UserId = userId;
                log.IPAddress = ipAddress;

                if (find != null && find.res!=null)
                {
                    result = find.res.ToVM();
                    result.testResult = result.testResult.DecryptResult();
                }

                _context.HL7SensitiveResultAccessLogs.Add(log);
                _context.SaveChanges(userId, ipAddress);

                transactionScope.Complete();
                transactionScope.Dispose();
            }

            return result;
        }

        public List<VMHL7SensitiveResultAccessLog> GetSensitiveResultAccessedLog(int resultid, int practiceId)
        {
            List<VMHL7SensitiveResultAccessLog> accesslogs = new List<VMHL7SensitiveResultAccessLog>();
            accesslogs = (from al in _context.HL7SensitiveResultAccessLogs
                          join u in _context.Users on al.UserId equals u.UserID
                          where al.HL7ResultId == resultid && u.PracticeID==practiceId
                          select new VMHL7SensitiveResultAccessLog { UserName = u.LastName + " ," + u.FirstName, HL7ResultId = al.HL7ResultId, IPAddress = al.IPAddress, Reason = al.Reason, AccessedDateTime = al.AccessedDateTime }).ToList();

            return accesslogs;
        }
    }
}
