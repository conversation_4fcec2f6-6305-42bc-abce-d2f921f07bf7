﻿using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Schema;

namespace AwareMD.JsonValidator
{
    /// <summary>
    /// 
    /// </summary>
    /// <remarks>ALL UNIT TESTS are in project <see cref="Cerebrum.SendReport.Hrm.Test"/>.</remarks>
    public static class CustomErrorMessage
    {
        private const string ERROR_ELEMENT_PREFIX = "error_";

        private const string JSON_EXPRESSION_START_WITH = "{";
        private const string JSON_EXPRESSION_END_WITH = "}";
        private const string JSON_EXPRESSION_VARIABLE_SEPARATOR = ".";
        private const string JSON_EXPRESSION_VARIABLE_PATH = "[PATH]";
        private const string JSON_EXPRESSION_VARIABLE_PARENT = "[PARENT]";

        /// <summary>
        /// This helper method is to generate a list of error messages from the result of Newtonsoft error validation. 
        /// </summary>
        /// <param name="validationErrors">Result error list after executing the <see cref="Newtonsoft.Json.Schema.Extensions.IsValid(Newtonsoft.Json.Linq.JToken, JsonSchema, out IList{string})"/></param>
        /// <remarks>
        /// ALL UNIT TESTS are in project <see cref="Cerebrum.SendReport.Hrm.Test"/>.
        /// </remarks>
        public static IList<string> GetErrorMessageList(IList<ValidationError> validationErrors, JObject jObject = null)
        {
            List<string> result = new List<string>();

            if (validationErrors != null)
            {
                foreach (ValidationError each in validationErrors)
                {
                    List<string> errors = new List<string>();

                    switch (each.ErrorType)
                    {
                        case ErrorType.Required:
                            errors.AddRange(GetErrorMessageInMultipleExtension(each));
                            break;
                        case ErrorType.AnyOf:
                        case ErrorType.OneOf:
                            errors.AddRange(GetErrorMessageAnyOf(each));
                            break;
                        case ErrorType.Enum:
                        case ErrorType.MaximumItems:
                        case ErrorType.MinimumItems:
                        case ErrorType.MaximumLength:
                        case ErrorType.MinimumLength:
                        case ErrorType.Minimum:
                        case ErrorType.Format:
                        case ErrorType.Type:
                            errors.AddRange(GetErrorMessageInSingleExtension(each));
                            break;
                        default:
                            errors.Add(each.Message);
                            break;
                    }

                    result.AddRange(GetErrorMessageList(each, errors, jObject));
                }
            }

            return result;
        }

        private static IList<string> GetErrorMessageList(ValidationError validationError, IList<string> errors, JObject jObject)
        {
            if (jObject == null || errors.Count == 0)
            {
                return errors;
            }

            List<string> result = new List<string>();
            foreach (var error in errors)
            {
                string data = error;
                try
                {
                    IList<string> jsonExpressions = GetJsonExpressions(error);
                    foreach (var jsonExpression in jsonExpressions)
                    {
                        if (string.IsNullOrEmpty(jsonExpression))
                        {
                            continue;
                        }

                        string jsonToken = GetJsonToken(validationError, jsonExpression, jObject);
                        if (!string.IsNullOrEmpty(jsonToken))
                        {
                            string jsonValue = jObject.SelectToken(jsonToken).ToString();
                            data = data.Replace(jsonExpression, jsonValue);
                        }
                    }
                }
                catch
                {
                }

                result.Add(data);
            }

            return result;
        }

        /// <param name="error">example for error:  "Doctor's (id: {[Path].[Parent].id}, name: {[Path].[Parent].[Parent].name.family}) CPSO is missing"></param>   
        /// <returns>return array: {[Path].[Parent].id}, {[Path].[Parent].[Parent].name.family}</returns>
        private static IList<string> GetJsonExpressions(string error)
        {
            List<string> result = new List<string>();

            int index = 0;
            while (index < error.Length)
            {
                int index1 = error.IndexOf(JSON_EXPRESSION_START_WITH, index);
                if (index1 < 0)
                {
                    break;
                }

                int index2 = error.IndexOf(JSON_EXPRESSION_END_WITH, index1);
                if (index2 < 0)
                {
                    break;
                }

                string jsonExpression = error.Substring(index1, index2 - index1 + 1);
                result.Add(jsonExpression);
                index = index2;
            }

            return result;
        }

        /// <param name="validationError"></param>
        /// <param name="jsonExpression"></param>  //ex.   "{[Path].[Parent].[Parent].name.family}"
        /// <returns></returns>  //ex.   "entry[6].resource.name.family"
        private static string GetJsonToken(ValidationError validationError, string jsonExpression, JObject jObject)
        {
            if (string.IsNullOrEmpty(jsonExpression) || jsonExpression.Length < 3 || !jsonExpression.StartsWith(JSON_EXPRESSION_START_WITH) || !jsonExpression.EndsWith(JSON_EXPRESSION_END_WITH))
            {
                return string.Empty;
            }

            string jsonToken = string.Empty;
            try
            {
                string expression = jsonExpression.Substring(1, jsonExpression.Length - 2);  //remove first & last character
                string[] elements = expression.Split(JSON_EXPRESSION_VARIABLE_SEPARATOR.ToCharArray());

                for (int i = 0; i < elements.Length; i++)
                {
                    if (string.IsNullOrEmpty(elements[i]))
                    {
                        continue;
                    }

                    string tokenElement = elements[i].Trim();
                    string element = tokenElement.ToUpper();
                    switch (element)
                    {
                        case JSON_EXPRESSION_VARIABLE_PATH:
                            jsonToken = validationError.Path;
                            break;

                        case JSON_EXPRESSION_VARIABLE_PARENT:
                            if (!(string.IsNullOrEmpty(jsonToken) || jObject == null))
                            {
                                jsonToken = jObject.SelectToken(jsonToken).Parent.Parent.Path;
                            }
                            break;

                        default:
                            if (!string.IsNullOrEmpty(jsonToken))
                            {
                                jsonToken = jsonToken + JSON_EXPRESSION_VARIABLE_SEPARATOR;
                            }
                            jsonToken = jsonToken + tokenElement;
                            break;
                    }
                }
            }
            catch
            {
                return string.Empty;
            }

            return jsonToken;
        }

        private static IList<string> GetErrorMessageInMultipleExtension(ValidationError validationError)
        {
            List<string> result = new List<string>();

            if (validationError != null
                && validationError.Value is List<string>
                )
            {
                List<string> allRequiredFields = (List<string>)validationError.Value;
                foreach (string fieldName in allRequiredFields)
                {
                    string elementName = ERROR_ELEMENT_PREFIX + validationError.ErrorType.ToString() + "_" + fieldName;
                    if (validationError.Schema.ExtensionData.Keys.Contains(elementName))
                    {
                        result.Add(validationError.Schema.ExtensionData[elementName].ToString());
                    }
                    else
                    {
                        result.Add(validationError.Message);
                    }
                }
            }
            else
            {
                result.Add(validationError.Message);
            }

            return result;
        }


        private static IList<string> GetErrorMessageInSingleExtension(ValidationError validationError)
        {
            List<string> result = new List<string>();

            if (validationError != null)
            {
                string elementName = ERROR_ELEMENT_PREFIX + validationError.ErrorType.ToString();
                if (validationError.Schema.ExtensionData.Keys.Contains(elementName))
                {
                    result.Add(validationError.Schema.ExtensionData[elementName].ToString());
                }
                else
                {
                    result.Add(validationError.Message);
                }
            }

            return result;
        }


        private static IList<string> GetErrorMessageAnyOf(ValidationError validationError)
        {
            List<string> result = new List<string>();

            if (validationError != null
                && validationError.ChildErrors != null
                && validationError.ChildErrors.Count > 0)
            {
                List<string> allRequiredFields = new List<string>();
                foreach (ValidationError each in validationError.ChildErrors)
                {
                    if (each.Value is List<string>)
                    {
                        allRequiredFields.AddRange((System.Collections.Generic.List<string>)each.Value);
                    }
                }
                string elementName = ERROR_ELEMENT_PREFIX + validationError.ErrorType.ToString() +
                    (allRequiredFields.Count > 0 ? "_" + string.Join("_", allRequiredFields) : string.Empty);

                if (validationError.Schema.ExtensionData.Keys.Contains(elementName))
                {
                    result.Add(validationError.Schema.ExtensionData[elementName].ToString());
                }
                else
                {
                    result.Add(validationError.Message);
                }
            }
            return result;
        }
    }
}
