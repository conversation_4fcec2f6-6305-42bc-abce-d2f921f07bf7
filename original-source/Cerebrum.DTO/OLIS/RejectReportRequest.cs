﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.DTO.OLIS
{
    public class RejectReportRequest
    {
        [Required]
        public int ReportId { get; set; }
        [Required]
        public int pid_setid { get; set; }
        [Required]
        public string reason { get; set; }

        [Required]
        public int PracticeId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public string IpAddress { get; set; }
    }
}
