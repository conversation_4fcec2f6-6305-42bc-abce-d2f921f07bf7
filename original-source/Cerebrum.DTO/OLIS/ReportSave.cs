﻿using Cerebrum.DTO.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.DTO.OLIS
{
    public class ReportSave : BaseRequest
    {
        public int reportReceivedId { get; set; }
        public bool markseen { get; set; }
        public bool IsDoctor { get; set; }
        public int PracticeDoctorId { get; set; }
        public int pid_set { get; set; }
        public string filename { get; set; }
        public List<string> requestedtests { get; set; }
        public List<string> results { get; set; }

    }
}
