﻿using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.DTO.Doctor
{
    public interface IPracticeDoctorDTO
    {
        int PracticeDoctorId { get; set; }
        int PracticeId { get; set; }
        int ExternalDoctorId { get; set; }
        string LastName { get; set; }
        string FirstName { get; set; }
        string MiddleName { get; set; }
        string HRMId { get; set; }
        string OHIPPhysicianId { get; set; }
        string CPSO { get; set; }
        string emailAddress { get; set; }
        string Degrees { get; set; }
        string specialization { get; set; }
        SpecialtyCodes specialtyCodes { get; set; }
        string PhysicianType { get; set; }
        bool IsActive { get; set; }
    }
}
