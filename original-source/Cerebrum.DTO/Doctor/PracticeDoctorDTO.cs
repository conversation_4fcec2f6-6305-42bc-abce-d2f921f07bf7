﻿using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.DTO.Doctor
{
    public class PracticeDoctorDTO: IPracticeDoctorDTO
    {
        public int PracticeDoctorId { get; set; }
        public int PracticeId { get; set; }
        public int ExternalDoctorId { get; set; }
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string HRMId { get; set; }
        public string OHIPPhysicianId { get; set; }
        public string CPSO { get; set; }
        public string emailAddress { get; set; }
        public string Degrees { get; set; }
        public string specialization { get; set; }
        public SpecialtyCodes specialtyCodes { get; set; }
        public string PhysicianType { get; set; }
        public bool IsActive { get; set; }        
    }
}
