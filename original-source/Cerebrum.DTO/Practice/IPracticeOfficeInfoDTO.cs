﻿
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.DTO.Practice
{
    public interface IPracticeOfficeInfoDTO
    {
        int PracticeId { get; set; }
        string PracticeName { get; set; }
        int OfficeId { get; set; }
        string OfficeName { get; set; }
        string BusinessName { get; set; }
        string Address1 { get; set; }
        string Address2 { get; set; }
        string City { get; set; }
        Province Province { get; set; }
        string PostalCode { get; set; }
        string Country { get; set; }
        string Phone { get; set; }
        string Fax { get; set; }
        bool IsHeadOffice { get; set; }
    }
}
