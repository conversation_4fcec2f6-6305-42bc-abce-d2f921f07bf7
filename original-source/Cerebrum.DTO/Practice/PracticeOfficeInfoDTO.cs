﻿
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.DTO.Practice
{
    public class PracticeOfficeInfoDTO: IPracticeOfficeInfoDTO
    {
       public int PracticeId { get; set; }
       public string PracticeName { get; set; }
       public int OfficeId { get; set; }
       public string OfficeName { get; set; }
       public string BusinessName { get; set; }
       public string Address1 { get; set; }
       public string Address2 { get; set; }
       public string City { get; set; }
       public Province Province { get; set; }
       public string PostalCode { get; set; }
       public string Country { get; set; }
       public string Phone { get; set; }
       public string Fax { get; set; }
       public bool IsHeadOffice { get; set; }
    }
}
