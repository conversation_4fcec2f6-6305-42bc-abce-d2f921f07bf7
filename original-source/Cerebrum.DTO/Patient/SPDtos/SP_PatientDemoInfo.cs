﻿using AwareMD.Cerebrum.Shared.Enums;
using System;

namespace Cerebrum.DTO.Patient.SPDtos
{
    public class SP_PatientDemoInfo
    {
        public int PracticeId { get; set; }
        public int PatientRecordId { get; set; }
        public Salutation Salutation { get; set; }
        public int DemographicId { get; set; }
        public string firstName { get; set; }
        public string middleName { get; set; }
        public string lastName { get; set; }
        public string preferredName { get; set; }
        public bool? useAliases { get; set; }
        public string aliasFirstName { get; set; }
        public string aliasMiddleName { get; set; }
        public string aliasLastName { get; set; }
        public string PatientPhoneNumbers { get; set; }
        public DateTime? dateOfBirth { get; set; }
        public string AgeAccurate { get; set; } // this includes years months and days  
        public Gender gender { get; set; }
        public string OHIP { get; set; }
        public string OHIPVersionCode { get; set; }
        public DateTime? OHIPExpiry { get; set; }
        public PaymentMethod DefaultPaymentMethod { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }
    }

    public class SP_PatientPhoneNumber
    {
        public int Id { get; set; }
        public string phoneNumber { get; set; }
        public string extention { get; set; }
        public string TypeOfPhoneNumber { get; set; }
        public bool IsActive { get; set; }
    }
}
