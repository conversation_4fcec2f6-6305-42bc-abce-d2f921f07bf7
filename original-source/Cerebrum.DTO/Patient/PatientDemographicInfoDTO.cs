﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Cerebrum.DTO.Patient
{
    public class PatientDemographicInfoDTO : IPatientDemographicInfoDTO
    {
        public int PracticeId { get; set; }
        public int DemographicId { get; set; }
        public int PatientRecordId { get; set; }
        public Salutation Salutation { get; set; }
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public Gender Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string OHIP { get; set; }
        public string OHIPVersionCode { get; set; }
        public PaymentMethod DefaultPaymentMethod { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }
        public string Address
        {
            get
            {
                List<string> address = (new List<string> { Address1, Address2, City, (Province ?? string.Empty).StartsWith("CA")?Province.Substring(2):Province, PostalCode }).Where(a=>!string.IsNullOrEmpty(a)).ToList();
                return string.Join(", ", address);
            }
        }
        public IList<IDemographicPhonenumberDTO> PatientPhoneNumbers { get; set; }
        public int active { get; set; }
    }


}
