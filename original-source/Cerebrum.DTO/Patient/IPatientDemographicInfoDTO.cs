﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;

namespace Cerebrum.DTO.Patient
{
    public interface IPatientDemographicInfoDTO
    {
        int PracticeId { get; set; }
        int DemographicId { get; set; }
        int PatientRecordId { get; set; }
        Salutation Salutation { get; set; }        
        string LastName { get; set; }
        string FirstName { get; set; }
        string MiddleName { get; set; }
        Gender Gender { get; set; }
        DateTime? DateOfBirth { get; set; }
        string OHIP { get; set; }
        string OHIPVersionCode { get; set; }
        PaymentMethod DefaultPaymentMethod { get; set; }
        string Address1 { get; set; }
        string Address2 { get; set; }
        string Province { get; set; }
        string City { get; set; }
        string Country { get; set; }
        string PostalCode { get; set; }
        IList<IDemographicPhonenumberDTO> PatientPhoneNumbers { get; set; }
        int active { get; set; }
    }
}
