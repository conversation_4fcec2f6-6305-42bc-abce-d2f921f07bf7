﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net461" />
  <package id="bootstrap" version="4.3.1" targetFramework="net461" />
  <package id="jQuery" version="3.3.1" targetFramework="net461" />
  <package id="jQuery.Validation" version="1.17.0" targetFramework="net461" />
  <package id="log4net" version="2.0.12" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights" version="2.9.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.4.0" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.9.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.9.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.9.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.9.1" targetFramework="net461" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.9.1" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.TelemetryCorrelation" version="1.0.5" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.11" targetFramework="net461" />
  <package id="Microsoft.Net.Compilers" version="3.0.0" targetFramework="net461" developmentDependency="true" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net461" />
  <package id="Modernizr" version="2.8.3" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net461" />
  <package id="popper.js" version="1.14.0" targetFramework="net461" />
  <package id="Respond" version="1.4.2" targetFramework="net461" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.5.0" targetFramework="net461" />
  <package id="WebGrease" version="1.6.0" targetFramework="net461" />
</packages>