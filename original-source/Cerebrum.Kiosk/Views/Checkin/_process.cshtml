﻿@model Cerebrum.ViewModels.Kiosk.VMKioskAppointmentMain

@{    
    var appInfo = Model.KioskAppointments.FirstOrDefault();
}

<div class="row">

    <div class="col-md-12">
        <div id="div-frm-checkin-process">
            <div class="jumbotron">
                @using (Html.BeginForm("process", "checkin", new { area = "" }, FormMethod.Post, new { @id = "frm-checkin-process" }))
                {
                    @Html.HiddenFor(x => x.HealthCard)
                    @Html.HiddenFor(x => x.IsSuccess)
                    @Html.HiddenFor(x => x.ConfirmMessage)
                    for (int i = 0; i < Model.KioskAppointments.Count; i++)
                    {
                        @Html.HiddenFor(x => x.KioskAppointments[i].AppointmentId)
                        @Html.HiddenFor(x => x.KioskAppointments[i].AppointmentTime)
                        @Html.HiddenFor(x => x.KioskAppointments[i].PracticeId)
                        @Html.HiddenFor(x => x.KioskAppointments[i].OfficeId)
                        @Html.HiddenFor(x => x.KioskAppointments[i].PatientId)
                        @Html.HiddenFor(x => x.KioskAppointments[i].AddressLine1)
                        @Html.HiddenFor(x => x.KioskAppointments[i].AddressLine2)
                        @Html.HiddenFor(x => x.KioskAppointments[i].City)
                        @Html.HiddenFor(x => x.KioskAppointments[i].Province)
                        @Html.HiddenFor(x => x.KioskAppointments[i].PostalCode)
                        @Html.HiddenFor(x => x.KioskAppointments[i].Country)
                        @Html.HiddenFor(x => x.KioskAppointments[i].HealthCard)
                        @Html.HiddenFor(x => x.KioskAppointments[i].FamilyDoctor)
                    }
                    <div class="form-group">
                        <div class="col-md-12 text-center">
                            <div><h1>@Model.ConfirmMessage</h1></div>
                            <div id="div-app-info">
                                <div>@appInfo.AddressLine1</div>
                                @if (!String.IsNullOrWhiteSpace(appInfo.AddressLine2))
                                {
                                    <span> @appInfo.AddressLine2</span>
                                }
                                @if (!String.IsNullOrWhiteSpace(appInfo.City))
                                {
                                    <div>@appInfo.City</div>
                                }
                                @if (!String.IsNullOrWhiteSpace(appInfo.Province))
                                {
                                    <span> @appInfo.Province</span>
                                }
                                @if (!String.IsNullOrWhiteSpace(appInfo.PostalCode))
                                {
                                    <span> @appInfo.PostalCode</span>
                                }
                                @if (!String.IsNullOrWhiteSpace(appInfo.Country))
                                {
                                    <div>@appInfo.Country</div>
                                }
                                @if (!String.IsNullOrWhiteSpace(appInfo.FamilyDoctor))
                                {
                                    <div>Family Doctor: @appInfo.FamilyDoctor</div>
                                }
                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <div class="col-md-6 text-left">
                            <button data-btn-value="1" type="button" class="btn btn-lg btn-primary btn-process-submit">Yes</button>
                        </div>
                        <div class="col-md-6 text-right">
                            <button data-btn-value="0" type="button" class="btn btn-lg btn-primary btn-process-submit">No</button>                            
                        </div>
                    </div>
                }
                <div class="row">
                    <div class="col-md-12 text-center">
                        <div id="patient-info-counter"></div>
                    </div>
                </div>
            </div>
        </div>
       
    </div>

</div>

<script type="text/javascript">
    $(function () {
        setTimeout("patientInfoDisplayCounter()", 1000);
    });   
</script>