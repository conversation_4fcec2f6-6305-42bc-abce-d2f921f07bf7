﻿@model Cerebrum.ViewModels.Kiosk.VMKioskMessage

<div class="row">    
    <div class="col-md-12">
        <div id="div-message-container">
            <div class="jumbotron text-center">
                <h3 class="">@Model.DisplayMessage</h3>
                <hr />
                <div class="row">
                    <div class="col-md-12 text-right">
                        <a href="@Url.Action("index","checkin",new { area=""})" class="btn btn-lg btn-primary">Home</a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-center">
                        <div id="message-info-counter"></div>
                    </div>
                </div>
        </div>
    </div>
    </div>
</div>


<script type="text/javascript">
    $(function () {
        setTimeout("messageInfoDisplayCounter()", 1000);
    });
</script>
