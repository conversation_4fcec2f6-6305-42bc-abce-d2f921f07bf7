﻿@model Cerebrum.ViewModels.Kiosk.VMCheckinRequest

@{    
    var showLogo = Model.ShowLogo && Model.OfficeLogo != null && Model.OfficeLogo.Length > 0 ? true : false;
    var divCol = showLogo ? "col-md-6": "col-md-12";
    var imgSrc = showLogo ? String.Format("data:image/gif;base64,{0}", Convert.ToBase64String(Model.OfficeLogo)): "";
   
}

<div class="row">
    <div class="col-md-12">
        <div class="jumbotron text-center">
            <h1 class="display-3">Self Check-In</h1>
        </div>
    </div>
</div>

@if (!showLogo)
{
    if (!String.IsNullOrWhiteSpace(Model.Message))
    {
        <div class="row">
            <div class="text-center col-md-12">
                <h1>@Model.Message</h1>
            </div>
        </div>
    }
}

<div class="row">
    @if (showLogo)
    {        
        <div class="@divCol">        
            <div class="row">
                <div class="col-md-12">  
                    <div id="div-img-office-logo">
                        <img id="img-office-logo" src="@imgSrc" class="img-fluid" alt="Office Logo">
                    </div> 
                </div>
            </div>
        </div>
    }    
    <div class="@divCol">
        <div id="div-frm-checkin">
            @using (Html.BeginForm("index", "checkin", new { area = "" }, FormMethod.Post, new { @id = "frm-checkin" }))
            {
                @Html.HiddenFor(model => model.IpAddress)
                @Html.HiddenFor(model => model.OfficeLogo)
                @Html.HiddenFor(model => model.OfficeName)
                @Html.HiddenFor(model => model.Message)
                @Html.HiddenFor(model => model.ShowLogo)
                @Html.HiddenFor(model => model.VersionCode)
                @Html.HiddenFor(model => model.IssuedDate)
                @Html.HiddenFor(model => model.ExpiryDate)
                <div class="form-group">
                    <div class="col-md-12">
                        <div id="hc-alert" class="">
                        </div>
                        <div class="text-center">Please enter or swipe your healthcard</div>
                       
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-12">
                        @Html.EditorFor(model => model.HealthCard, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })
                        @Html.ValidationMessageFor(model => model.HealthCard, "", new { @class = "text-danger" })
                    </div>
                </div>
            }
        </div>
        <table id="tbl-keyboard" class="">
            <tr class="tr-num-row">
                <td class="td-input-row">
                    <button data-btn-value="1" class="btn btn-light btn-lg btn-keyboad-input">1</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="2" class="btn btn-light btn-lg btn-keyboad-input">2</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="3" class="btn btn-light btn-lg btn-keyboad-input">3</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="1" class="btn btn-light btn-lg btn-keyboad-input-lg" id="btn-keyboard-delete">Delete</button>
                </td>
            </tr>

            <tr class="tr-num-row">
                <td class="td-input-row">
                    <button data-btn-value="4" class="btn btn-light btn-lg btn-keyboad-input">4</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="5" class="btn btn-light btn-lg btn-keyboad-input">5</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="6" class="btn btn-light btn-lg btn-keyboad-input">6</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="1" class="btn btn-light btn-lg btn-keyboad-input-lg" id="btn-keyboard-clear">Clear</button>
                </td>
            </tr>
            <tr class="tr-num-row">
                <td class="td-input-row">
                    <button data-btn-value="7" class="btn btn-light btn-lg btn-keyboad-input">7</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="8" class="btn btn-light btn-lg btn-keyboad-input">8</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="9" class="btn btn-light btn-lg btn-keyboad-input">9</button>
                </td>
                <td class="td-input-row">
                    <a id="btn-keyboard-cancel" href="@Url.Action("index","checkin",new { area=""})" class="btn btn-light btn-lg btn-keyboad-input-lg">Cancel</a>                    
                </td>
            </tr>
            <tr class="tr-num-row">
                <td class="td-input-row">
                    <button data-btn-value="" class="btn btn-light btn-lg btn-keyboad-input-place-holder">&nbsp;</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="0" class="btn btn-light btn-lg btn-keyboad-input">0</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="" class="btn btn-light btn-lg btn-keyboad-input-place-holder">&nbsp;</button>
                </td>
                <td class="td-input-row">
                    <button data-btn-value="1" class="btn btn-light btn-lg btn-keyboad-input-lg" id="btn-keyboard-enter">Enter</button>
                </td>
            </tr>
        </table>
    </div>
    <div id="hc-swipe"></div>
</div>
