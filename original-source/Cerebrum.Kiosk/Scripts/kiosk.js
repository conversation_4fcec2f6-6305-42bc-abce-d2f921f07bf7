﻿var patInfoRefreshCount = 60;
var messageRefreshCount = 10;
var mainDivContainer = '#main-container';
$(function () {
    var ohip = [];
    var keyCodeCount = 0;
    $(this).keypress(function (e) {
        
        ohip.push(String.fromCharCode(e.which));
        console.log(ohip.join());
        if (e.keyCode == 13) {
            keyCodeCount = keyCodeCount + 1;
        }
        if (keyCodeCount == 2) {
            e.preventDefault();
            keyCodeCount = 0;

            var txtHc = ohip.join("");

            var hc = new HealthCard(txtHc);

            if (hc.IsValidHealthCard == true) {
                showLoader();
                $('#HealthCard').val(hc.HealthNumber);
                $('#frm-checkin').submit();
                setTimeout(function () {
                    $('#HealthCard').val('');
                    ohip = [];
                }, 5000);
            } else {
                $('#HealthCard').val(hc.HealthNumber);
                $('#VersionCode').val(hc.versionCode);
                $('#ExpiryDate').val(hc.ExpiryDate);
                $("#hc-alert").addClass("alert alert-danger");
                $("#hc-alert").html("Invalid HealthCard. Please try again.");
                setTimeout(function () {
                    $('#hc-alert').fadeOut('slow');
                    ohip = [];
                }, 5000);
            }
           
        }
    });
    $(document).on('click', '#HealthCard', function (e) {
        e.preventDefault();

    });

    $(document).on('click', '.btn-keyboad-input', function (e) {
        e.preventDefault();
        var btn = $(this);
        var btnValue = btn.data('btn-value');
        var heathCardInput = $('#HealthCard').val();

        $('#HealthCard').val(heathCardInput + btnValue);


    });

    $(document).on('click', '#btn-keyboard-delete', function (e) {
        e.preventDefault();
        var btn = $(this);

        var heathCardInput = $('#HealthCard');
        var heathCardVal = heathCardInput.val();
        if (heathCardVal.length > 0) {
            var newVal = heathCardVal.substr(0, (heathCardVal.length - 1));
            if (newVal.length <= 0) {
                newVal = '';
            }

            heathCardInput.val(newVal);
        }
    });

    $(document).on('click', '#btn-keyboard-clear', function (e) {
        e.preventDefault();
        var btn = $(this);
        $('#HealthCard').val('');

    });


    $(document).on('click', '#btn-keyboard-enter', function (e) {
        e.preventDefault();
        var btn = $(this);

        var txtHc = $("#HealthCard").val();
        $('#frm-checkin').submit();

        //var hc =new HealthCard(txtHc);
        //if (hc.IsValidHealthCard == true) {
        //    showLoader();
        //    $('#frm-checkin').submit();
        //} else {
        //    $("#hc-alert").addClass("alert alert-danger");
        //    $("#hc-alert").html("Invalid HealthCard");
        //    setTimeout(function () {
        //        $('#hc-alert').fadeOut('slow');
        //    }, 5000 );
        //}
    });

    $(document).on('click', '.btn-process-submit', function (e) {
        e.preventDefault();
        var btn = $(this);
        var btnValue = btn.data('btn-value');
        if (btnValue == 1) {
            $('#frm-checkin-process #IsSuccess').val('true');
        }
        else {
            $('#frm-checkin-process #IsSuccess').val('false');
        }

        showLoader();
        $('#frm-checkin-process').submit();

    });

    $(document).on('submit', '#frm-checkin', function (e) {
        e.preventDefault();
        removeErrorNotifications();

        if ($('#HealthCard').val() != '') {

            $.ajax({
                url: this.action,
                type: this.method,
                data: $(this).serialize(),
                complete: function () {
                    hideLoader();
                },
                success: function (result) {
                    $(mainDivContainer).html(result);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    getErrorModal(null, true);
                }
            });
        }
    });

    $(document).on('submit', '#frm-checkin-process', function (e) {
        e.preventDefault();
        removeErrorNotifications();

        $.ajax({
            url: this.action,
            type: this.method,
            data: $(this).serialize(),
            complete: function () {
                hideLoader();
            },
            success: function (result) {
                $(mainDivContainer).html(result);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                getErrorModal(null, true);
            }
        });


    });

});
function disableKeypress() {
    document.onkeydown = function (e) {
        return false;
    }
}
function enableKeypress() {
    document.onkeydown = function (e) {
        return true;
    }
}
function showLoader() {
    $('#ajax-loader').show();
    
}

function hideLoader() {
    $('#ajax-loader').hide();
    
}

function addModalHtml(modalHtml) {
    if ($('#cb-modal-container').length) {
        $('#cb-modal-container').append(modalHtml);
    }
    else {
        $('body').append('<div id="cb-modal-container"></div>');
        $('#cb-modal-container').append(modalHtml);
    }
}

function addModalHtmlBottom(modalHtml) {
    if ($('#cb-modal-container-bottom').length) {
        $('#cb-modal-container-bottom').append(modalHtml);
    }
    else {
        $('body').append('<div id="cb-modal-container-bottom"></div>');
        $('#cb-modal-container-bottom').append(modalHtml);
    }
}

function getErrorModal(errorMessage, insertAtBottom) {

    hideLoader();

    var message = "An error occurred while processing your request.";
    if (errorMessage != null && errorMessage.length > 0) { message = errorMessage; }
    var modalID = "#errorModal";
    var modalContent = "#errorModal-ct";
    var modalSizeClass = "modal-dialog";
    var addToBottom = isInsertAtBottom(insertAtBottom);

    var modalHtml = '<div id="' + modalID.replace('#', '') + '" tabindex="-1" class="modal fade in"><div class="' + modalSizeClass + '"><div class="modal-content"><div id="' + modalContent.replace('#', '') + '"></div></div></div></div>';

    $(modalID).remove();

    if (addToBottom) {
        addModalHtmlBottom(modalHtml);
    }
    else {
        addModalHtml(modalHtml);
    }

    $(modalContent).html('<div class="modal-header">' +
        '<h4 class="modal-title">Error</h4><button type="button" class="close" data-dismiss="modal">&times;</button>' +
        '</div>' +
        '<div class="modal-body">' +
        '<div class="alert alert-danger">' +
        '<h3>' + message + '</h3>' +
        '</div></div>' +
        '<div class="modal-footer"><button class="btn btn-default btn-sm" data-dismiss="modal">Close</button></div>');

    $(modalID).on('hidden.bs.modal', function () {
        $(this).remove();
    });

    $(modalID).modal({
        keyboard: false,
        backdrop: 'static'
    }, 'show');


}

function showMessageModal(messageType, message, isHtml, insertAtBottom) {

    hideAjaxLoader();

    var modalID = "#messageModal";
    var modalContent = "#messageModal-ct";
    var modalSizeClass = "modal-dialog";
    var htmlMessage = '';
    var addToBottom = isInsertAtBottom(insertAtBottom);

    if (isHtml == null || (isHtml != null && isHtml == false)) {
        if (messageType.toLowerCase() == "success") {
            htmlMessage = '<div class="alert alert-success role="alert">' + message + '</div>';
        }
        else if (messageType.toLowerCase() == "error") {
            htmlMessage = '<div class="alert alert-danger role="alert">' + message + '</div>';
        }
        else if (messageType.toLowerCase() == "warning") {
            htmlMessage = '<div class="alert alert-warning role="alert">' + message + '</div>';
        }
        else {
            htmlMessage = '<div class="alert alert-info role="alert">' + message + '</div>';
        }
    } else if (isHtml != null && isHtml == true) {
        htmlMessage = message;
    }

    var modalHtml = '<div id="' + modalID.replace('#', '') + '" tabindex="-1" class="modal fade in"><div class="' + modalSizeClass + '"><div class="modal-content"><div id="' + modalContent.replace('#', '') + '"></div></div></div></div>';

    $(modalID).remove();

    if (addToBottom) {
        addModalHtmlBottom(modalHtml);
    }
    else {
        addModalHtml(modalHtml);
    }

    $(modalContent).html('<div class="modal-header">' +
        '<h4 class="modal-title">Message</h4><button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>' +
        '</div>' +
        '<div class="modal-body">' + htmlMessage + '</div>' +
        '<div class="modal-footer"><button class="btn btn-default btn-sm" data-dismiss="modal">Close</button></div>');

    $(modalID).on('hidden.bs.modal', function () {
        $(this).remove();
    });

    $(modalID).modal({
        keyboard: false,
        backdrop: 'static'
    }, 'show');


}

function isInsertAtBottom(value) {
    var isInsertAtBottom = false;

    if (value != null) {
        if (value == true) {
            isInsertAtBottom = true;
        }
    }

    return isInsertAtBottom;
}

function removeErrorNotifications() {
    $('.msg-validation').remove();
    $('.field-validation-error').remove();
}

function patientInfoDisplayCounter() {
    patInfoRefreshCount--;
    if (patInfoRefreshCount < 0) {
        var url = $('body').data('home-url');
        window.location = url;
    }
    else {
        var msg = 'Redirecting to home screen in ' + patInfoRefreshCount;
        $('#patient-info-counter').html(msg);
        setTimeout("patientInfoDisplayCounter()", 1000);
    }
}

function messageInfoDisplayCounter() {
    messageRefreshCount--;
    if (messageRefreshCount < 0) {
        var url = $('body').data('home-url');
        window.location = url;
    }
    else {
        var msg = 'Redirecting to home screen in ' + messageRefreshCount;
        $('#message-info-counter').html(msg);
        setTimeout("messageInfoDisplayCounter()", 1000);
    }
}


function HealthCard(card) {
    if (card.startsWith("%B610054")) {
        console.log("In Heath card");
        var hcs = card.split('^');
        this.PatientRecordId = '';
        this.HealthNumber = hcs[0].substr(hcs[0].length - 10);

        var name = hcs[1].split('/');
        this.LastName = name[0];
        this.FirstName = name[1].split(' ')[0];
        this.MiddleName = name[1].indexOf(' ') > 0 ? name[1].split(' ')[1] : "";
        var expyear = ((new Date().getFullYear()) + "").substr(0, 2) + hcs[2].substr(0, 2);
        var expmonth = hcs[2].substr(2, 2);

        dobs = hcs[2].substr(8, 8);

        this.ExpiryDate = expmonth + "/" + dobs.substr(dobs.length - 2) + "/" + expyear;
        this.BirthDate = dobs.substr(4, 2) + "/" + dobs.substr(dobs.length - 2) + "/" + dobs.substr(0, 4);

        this.versionCode = hcs[2].substr(16, 2);
        var shc = hcs[2].split('?');
        var gender = shc[0].substr(shc[0].length - 2);

        // Gender info in not available in New Health Card

        //if (gender == "01")
        //    this.Gender = "M"
        //else
        //    this.Gender = "F"

        //this.Gender = shc[0].substr(shc[0].length - 2) == "01" ? "M" : "F";

        var issudty = ((new Date().getFullYear()) + "").substr(0, 2) + shc[0].substr(shc[0].length - 8, 2);
        var issudtm = shc[0].substr(shc[0].length - 6, 2);
        var issudtd = shc[0].substr(shc[0].length - 4, 2);
        this.IssueDate = issudtm + "/" + issudtd + "/" + issudty;
        this.IsValidHealthCard = HealthCardValidation(this.HealthNumber);
    } else {
        this.IsValidHealthCard = HealthCardValidation(card);
    }
}
HealthCardValidation = function (healthcard) {

    var nstr = "";
    var ary = healthcard.split("");

    for (i = 0; i < 9; i++) {
        if (i % 2 == 0) {
            var ni = (ary[i] * 2);

            if (ni > 9) {
                var arr = (ni + "").split("");
                var sum = 0;
                for (j = 0; j < 2; j++) {
                    sum = sum + parseInt(arr[j]);
                }
                nstr = nstr + sum;

            } else {
                nstr = nstr + ni;
            }
        }
        else
            nstr += ary[i];
    }

    var sumOfAlldigits = 0;
    var karr = nstr.split("");

    for (k = 0; k < karr.length; k++) {
        sumOfAlldigits = sumOfAlldigits + parseInt(karr[k]);

    }

    //get last digit of sum 43 = 4(3) 
    var lastdigit = sumOfAlldigits.toString().split("");
    //Subtract the unit position from 10
    var sub = 10 - lastdigit[1];
    //compare last digit of health card is equal to result of subration 
    var eq = (sub == ary[9] ? true : false);
    return eq;
}
