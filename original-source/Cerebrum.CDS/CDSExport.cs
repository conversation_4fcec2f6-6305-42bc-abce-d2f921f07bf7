﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Cerebrum.Data;
using Cerebrum.ViewModels.CDS;
using System.Data.Entity;

namespace Cerebrum.CDS
{
    public class CDSExport
    {
        private int userId = 0;
        private string ipAddress = string.Empty;
        private CerebrumContext context;

        public CDSExport(int userId, string ipAddress)
        {
            context = new CerebrumContext();
            this.userId = userId;
            this.ipAddress = ipAddress;
        }

        public CDSExport(CerebrumContext context, int userId, string ipAddress)
        {
            this.context = context;
            this.userId = userId;
            this.ipAddress = ipAddress;
        }

        public string SaveExportRequest(int practiceId, CdsExportCon expModel)
        {
            try
            {
                // parheps we need to add condition - !a.Suspended
                var dbItem = context.CDSqueues.Where(a => a.PracticeId == practiceId
                                                       && a.PracticelDoctorId == expModel.practicelDoctorId
                                                       && !a.Processed).FirstOrDefault();
                if (dbItem == null)
                {
                    // add new
                    var newItem = new CDSqueue
                    {
                        Address = expModel.Address_chb,
                        Categories = expModel.categories.Where(t => t.isChecked == true).Select(tt => tt.value).Sum(),
                        CerebrumVersion = expModel.CerebrumVersion,
                        CreatedDateTime = DateTime.Now,
                        DocText = expModel.docText,
                        EncryptKey = expModel.encryptKey,
                        //GrpText = expModel.grpText,
                        IsEncrypting = expModel.isEncrypting,
                        IsPerDoctor = expModel.isPerDoctor,
                        LaboratoryResultsReferenceRange = expModel.LaboratoryResultsReferenceRange_chb,
                        NormalAbnormalFlag = expModel.NormalAbnormalFlag_chb,
                        OfficeId = Convert.ToInt32(expModel.officeId),
                        PatientRecordId = expModel.patientRecordId,
                        PersonStatusCode = expModel.PersonStatusCode_chb,
                        PhGroup = expModel.PhGroup,
                        PracticeId = practiceId,
                        PracticelDoctorId = expModel.practicelDoctorId,
                        Processed = false, //default
                        Purpose = expModel.Purpose_chb,
                        ReportsSourceAuthorPhysician = expModel.ReportsSourceAuthorPhysician_chb,
                        UpdatedDateTime = DateTime.Now,
                        UserId = userId,
                        Version = expModel.Version,
                        YNIndicator = expModel.YNIndicator_chb,
                        CreateFinalReadMe = expModel.createFinalReadMe
                    };

                    context.CDSqueues.Add(newItem);
                }
                else
                {
                    // update existing
                    dbItem.Address = expModel.Address_chb;
                    dbItem.Categories = expModel.categories.Where(t => t.isChecked == true).Select(tt => tt.value).Sum();
                    dbItem.CerebrumVersion = expModel.CerebrumVersion;
                    // do not update CreatedDateTime
                    //dbItem.CreatedDateTime = DateTime.Now;
                    dbItem.DocText = expModel.docText;
                    dbItem.EncryptKey = expModel.encryptKey;
                    //dbItem.GrpText = expModel.grpText;
                    dbItem.IsEncrypting = expModel.isEncrypting;
                    dbItem.IsPerDoctor = expModel.isPerDoctor;
                    dbItem.LaboratoryResultsReferenceRange = expModel.LaboratoryResultsReferenceRange_chb;
                    dbItem.NormalAbnormalFlag = expModel.NormalAbnormalFlag_chb;
                    dbItem.OfficeId = Convert.ToInt32(expModel.officeId);
                    dbItem.PatientRecordId = expModel.patientRecordId;
                    dbItem.PersonStatusCode = expModel.PersonStatusCode_chb;
                    dbItem.PhGroup = expModel.PhGroup;
                    dbItem.PracticeId = practiceId;
                    dbItem.PracticelDoctorId = expModel.practicelDoctorId;
                    dbItem.Processed = false; //default
                    dbItem.Purpose = expModel.Purpose_chb;
                    dbItem.ReportsSourceAuthorPhysician = expModel.ReportsSourceAuthorPhysician_chb;
                    dbItem.UpdatedDateTime = DateTime.Now;
                    dbItem.UserId = userId;
                    dbItem.Version = expModel.Version;
                    dbItem.YNIndicator = expModel.YNIndicator_chb;
                    dbItem.CreateFinalReadMe = expModel.createFinalReadMe;

                    context.Entry(dbItem).State = EntityState.Modified;

                }
                context.SaveChanges();

                return string.Empty;
            }
            catch (Exception ex)
            {
                var err = ex.Message;
                // write to log here
                throw;
            }

        }
    }
}
