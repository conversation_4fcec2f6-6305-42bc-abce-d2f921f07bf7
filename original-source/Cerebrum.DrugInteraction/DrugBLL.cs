﻿using Cerebrum.ViewModels.Medications;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.DrugInteraction
{
    public class DrugBLL
    {
        readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private DrugsInteractionEntities _context;
        public DrugBLL()
        {
            _context = new DrugsInteractionEntities();
        }
        public string GetDatabaseVersionInfo()
        {
            string[] rectype = new string[] { "CDI", "CPV" };
            var db = from c in _context.CD2SUM_A
                     where rectype.Contains(c.Record_Type)
                     group c by c.Record_Type into g
                     select new { g.Key, other = g };
            string info = "Drug Interaction Database ";
            foreach (var i in db)
            {
                if (i.Key.Equals("CPV"))
                {
                    var k = i.other.Where(w => w.Sequence_Number == 10 || w.Sequence_Number == 60).Select(s => s.Data_or_Comment.Trim());
                    info += string.Join(" : ", k);
                }
                else
                {
                    info += (string.Join(" : ", i.other.Select(s => s.Data_or_Comment.Trim())))+" ";
                }
            }
            info = info.Replace("(Issue)","");
            return info;
        }
        public List<VMInteraction> GetDrugInteractions(List<Tuple<string, string>> nx_ids)
        {
            var lst = new List<drugClass>();

            foreach (var nx in nx_ids)
            {
                var din = (from d in _context.CD2DIN_O
                           where d.External_Drug_Identifier.Contains(nx.Item1)
                           select new { d.External_Drug_Identifier, d.Knowledge_Base_Drug_Code }).FirstOrDefault();
                var kdc = din.Knowledge_Base_Drug_Code;
                var kdc1 = kdc.Length >= 5 ? kdc.Substring(0, 5) : "";
                //For Test
                #region  check drugname
                var dname = _context.DrugName2KDC_B.FirstOrDefault(k => k.KDC1.Equals(kdc1));
                #endregion
                //
                var ingrads = (from i in _context.D01Class_C
                               where i.KDC1.Equals(kdc1) && i.RTYPE.Equals("D01")
                               select i).ToList();
                foreach (var i in ingrads)
                {
                    var dc = new drugClass { nx_id = nx.Item1, drug = nx.Item2, KDC = kdc1, classID = i.CLASSID };
                    lst.Add(dc);
                }
            }
            var c3 = new List<Cartesian>();
            foreach (var c1 in lst)
            {
                foreach (var c2 in lst)
                {
                    var c = new Cartesian
                    {
                        nx_id1 = c1.nx_id,
                        drug1 = c1.drug,
                        KDC1A = c1.KDC,
                        classA = c1.classID,
                        nx_id2 = c2.nx_id,
                        drug2 = c2.drug,
                        KDC1B = c2.KDC,
                        classB = c2.classID
                    };
                    if (!c.classA.Equals(c.classB))
                        c3.Add(c);
                }
            }
            c3 = c3.Distinct<Cartesian>().ToList();
            var c3new = new List<Cartesian>();
            foreach (var cr in c3)
            {
                int classA = int.Parse(cr.classA);
                int classB = int.Parse(cr.classB);
                if (classB < classA)
                {
                    var temp = cr.KDC1A;
                    cr.KDC1A = cr.KDC1B;
                    cr.KDC1B = temp;

                    temp = cr.classA;
                    cr.classA = cr.classB;
                    cr.classB = temp;

                    temp = cr.nx_id1;
                    cr.nx_id1 = cr.nx_id2;
                    cr.nx_id2 = temp;

                    temp = cr.drug1;
                    cr.drug1 = cr.drug2;
                    cr.drug2 = temp;

                    var newcr = new Cartesian
                    {
                        nx_id1 = cr.nx_id1,
                        drug1 = cr.drug1,
                        KDC1A = cr.KDC1A,
                        classA = cr.classA,
                        nx_id2 = cr.nx_id2,
                        drug2 = cr.drug2,
                        KDC1B = cr.KDC1B,
                        classB = cr.classB
                    };
                    c3new.Add(newcr);
                }
            }

            var interactionsList = new List<VMInterActions>();

            foreach (var c in c3new)
            {
                var interactions = from m in _context.M01InteractionMainFile_G
                                   where m.CLASSID1.Equals(c.classA) && m.CLASSID2.Equals(c.classB)
                                   select new VMInterActions
                                   {
                                       nx_id1 = c.nx_id1,
                                       drug1 = c.drug1,
                                       KDC1 = c.KDC1A,
                                       classID1 = m.CLASSID1,
                                       nx_id2 = c.nx_id2,
                                       drug2 = c.drug2,
                                       KDC2 = c.KDC1B,
                                       classID2 = m.CLASSID2,
                                       DrugInteractionID = m.DrugInteractionID,
                                       OnsetCode = m.OnsetCode,
                                       SeverityCode = m.SeverityCode,
                                       DocumentationCode = m.DocumentationCode,
                                       ManagementCode = m.ManagementCode,
                                       ContraindicationsCode = m.ContraindicationsCode
                                   };

                interactionsList.AddRange(interactions);
            }
            var inactions = new List<VMInteraction>();
            // var n = c3new.Where(l =>l.KDC1A.Equals("24673") && l.classB.Equals("12191"));
            foreach (var i in interactionsList)
            {
                var monograph = (from mg in _context.M01InteractionMonographs_I
                                 where mg.CLASSID1.Equals(i.classID1) && mg.CLASSID2.Equals(i.classID2) && mg.DrugInteractionID.Equals(i.DrugInteractionID)
                                 && mg.TextName.Equals("WAR")
                                 select mg).ToList();

                var drugnameA = _context.DrugName2KDC_B.FirstOrDefault(k => k.KDC1.Equals(i.KDC1));
                var drugnameB = _context.DrugName2KDC_B.FirstOrDefault(k => k.KDC1.Equals(i.KDC2));
                int count = 0;
                foreach (var mo in monograph)
                {
                    var interaction = new VMInteraction();
                    interaction.Id = ++count;
                    interaction.nx_id1 = i.nx_id1;
                    interaction.MedicationName = i.drug1;// drugnameA?.DrugName;
                    interaction.nx_id2 = i.nx_id2;
                    interaction.InteratingMediationName = i.drug2;// drugnameB?.DrugName;
                    interaction.Description = mo.ActualText;
                    // format drug
                    if (!string.IsNullOrWhiteSpace(interaction.MedicationName))
                        interaction.Description = interaction.Description.Replace("@A@", interaction.MedicationName);
                    if (!string.IsNullOrWhiteSpace(interaction.InteratingMediationName))
                        interaction.Description = interaction.Description.Replace("@B@", interaction.InteratingMediationName);
                    interaction.Management = i.ManagementCode;
                    inactions.Add(interaction);
                }
            }
            var drugGrp = new List<VMInteraction>();
            var ing = from ac in inactions
                      group ac by new { ac.MedicationName, ac.InteratingMediationName } into g
                      select new { g.Key, other = g };
            foreach (var ng in ing)
            {
                string description = string.Empty;
                var ninc = ng.other.FirstOrDefault();

                foreach (var n in ng.other)
                {
                    description += n.Description;
                }
                ninc.Description = description;
                drugGrp.Add(ninc);
            }


            return drugGrp;
        }
        public List<VMInteraction> GetDrugInteractions(List<string> nx_ids)
        {
            var lst = new List<drugClass>();

            foreach (var nx in nx_ids)
            {
                var din = (from d in _context.CD2DIN_O
                           where d.External_Drug_Identifier.Contains(nx)
                           select new { d.External_Drug_Identifier, d.Knowledge_Base_Drug_Code }).FirstOrDefault();
                var kdc = din.Knowledge_Base_Drug_Code;
                var kdc1 = kdc.Length >= 5 ? kdc.Substring(0, 5) : "";
                //For Test
                #region  check drugname
                var dname = _context.DrugName2KDC_B.FirstOrDefault(k => k.KDC1.Equals(kdc1));
                #endregion
                //
                var ingrads = (from i in _context.D01Class_C
                               where i.KDC1.Equals(kdc1) && i.RTYPE.Equals("D01")
                               select i).ToList();
                foreach (var i in ingrads)
                {
                    var dc = new drugClass { nx_id = nx, KDC = kdc1, classID = i.CLASSID };
                    lst.Add(dc);
                }
            }
            var c3 = new List<Cartesian>();
            foreach (var c1 in lst)
            {
                foreach (var c2 in lst)
                {
                    var c = new Cartesian { nx_id1 = c1.nx_id, KDC1A = c1.KDC, classA = c1.classID, nx_id2 = c2.nx_id, KDC1B = c2.KDC, classB = c2.classID };
                    if (!c.classA.Equals(c.classB))
                        c3.Add(c);
                }
            }
            c3 = c3.Distinct<Cartesian>().ToList();
            var c3new = new List<Cartesian>();
            foreach (var cr in c3)
            {
                int classA = int.Parse(cr.classA);
                int classB = int.Parse(cr.classB);
                if (classB < classA)
                {
                    var temp = cr.KDC1A;
                    cr.KDC1A = cr.KDC1B;
                    cr.KDC1B = temp;

                    temp = cr.classA;
                    cr.classA = cr.classB;
                    cr.classB = temp;

                    temp = cr.nx_id1;
                    cr.nx_id1 = cr.nx_id2;
                    cr.nx_id2 = temp;

                    var newcr = new Cartesian { nx_id1 = cr.nx_id1, KDC1A = cr.KDC1A, classA = cr.classA, nx_id2 = cr.nx_id2, KDC1B = cr.KDC1B, classB = cr.classB };
                    c3new.Add(newcr);
                }
            }

            var interactionsList = new List<VMInterActions>();

            foreach (var c in c3new)
            {
                var interactions = from m in _context.M01InteractionMainFile_G
                                   where m.CLASSID1.Equals(c.classA) && m.CLASSID2.Equals(c.classB)
                                   select new VMInterActions { nx_id1 = c.nx_id1, KDC1 = c.KDC1A, classID1 = m.CLASSID1, nx_id2 = c.nx_id2, KDC2 = c.KDC1B, classID2 = m.CLASSID2, DrugInteractionID = m.DrugInteractionID, OnsetCode = m.OnsetCode, SeverityCode = m.SeverityCode, DocumentationCode = m.DocumentationCode, ManagementCode = m.ManagementCode, ContraindicationsCode = m.ContraindicationsCode };

                interactionsList.AddRange(interactions);
            }
            var inactions = new List<VMInteraction>();
            // var n = c3new.Where(l =>l.KDC1A.Equals("24673") && l.classB.Equals("12191"));
            foreach (var i in interactionsList)
            {
                var monograph = (from mg in _context.M01InteractionMonographs_I
                                 where mg.CLASSID1.Equals(i.classID1) && mg.CLASSID2.Equals(i.classID2) && mg.DrugInteractionID.Equals(i.DrugInteractionID)
                                 && mg.TextName.Equals("WAR")
                                 select mg).ToList();

                var drugnameA = _context.DrugName2KDC_B.FirstOrDefault(k => k.KDC1.Equals(i.KDC1));
                var drugnameB = _context.DrugName2KDC_B.FirstOrDefault(k => k.KDC1.Equals(i.KDC2));
                int count = 0;
                foreach (var mo in monograph)
                {
                    var interaction = new VMInteraction();
                    interaction.Id = ++count;
                    interaction.nx_id1 = i.nx_id1;
                    interaction.MedicationName = drugnameA?.DrugName;
                    interaction.nx_id2 = i.nx_id2;
                    interaction.InteratingMediationName = drugnameB?.DrugName;
                    interaction.Description = mo.ActualText;
                    // format drug
                    if (!string.IsNullOrWhiteSpace(interaction.MedicationName))
                        interaction.Description = interaction.Description.Replace("@A@", interaction.MedicationName);
                    if (!string.IsNullOrWhiteSpace(interaction.InteratingMediationName))
                        interaction.Description = interaction.Description.Replace("@B@", interaction.InteratingMediationName);
                    interaction.Management = i.ManagementCode;
                    inactions.Add(interaction);
                }
            }

            return inactions;
        }

        public List<VMInteraction> GetInteractions(List<VMInteractionRequest> dinsAndNames)
        {
            var list = new List<VMInteraction>();
            try
            {
                var dbInterations = GetDBInteractions(dinsAndNames);

                foreach (var item in dbInterations)
                {
                    var isNew = false;
                    var interac = list.Where(a => a.nx_id1 == item.Din1 && a.nx_id2 == item.Din2).FirstOrDefault();
                    if (interac != null)
                    {
                        interac.Description += item.ActualText;
                    }
                    else
                    {
                        isNew = true;
                        interac = new VMInteraction();
                        interac.MedicationName = item.MedicationName1;
                        interac.InteratingMediationName = item.MedicationName2;
                        interac.nx_id1 = item.Din1;
                        interac.nx_id2 = item.Din2;
                        interac.Description = item.ActualText;
                        interac.SeverityDescription = item.SeverityDescription;
                        interac.SeverityCode = item.SeverityCode;
                        interac.DocumentationCode = item.DocumentationCode;
                        interac.DocumentationDescription = item.DocumentationDescription;
                        interac.OnsetCode = item.OnsetCode;
                        interac.OnsetDescription = item.OnsetDescription;

                    }

                    //if (!string.IsNullOrWhiteSpace(interac.MedicationName))
                    //    interac.Description = interac.Description.Replace("@A@", interac.MedicationName);
                    //if (!string.IsNullOrWhiteSpace(interac.InteratingMediationName))
                    //    interac.Description = interac.Description.Replace("@B@", interac.InteratingMediationName);

                    if (!string.IsNullOrWhiteSpace(interac.MedicationName))
                        interac.Description = interac.Description.Replace("@B@", interac.MedicationName);
                    if (!string.IsNullOrWhiteSpace(interac.InteratingMediationName))
                        interac.Description = interac.Description.Replace("@A@", interac.InteratingMediationName);

                    if (isNew)
                    {
                        list.Add(interac);
                    }

                }

            }
            catch(Exception ex)
            {
                _log.Error(ex);
            }
            return list;
        }

        private List<SPInteraction> GetDBInteractions(List<VMInteractionRequest> dinsAndNames)
        {
            var dbInteractions = new List<SPInteraction>();

            var interactionsParam = new SqlParameter("tblDins", System.Data.SqlDbType.Structured);
            interactionsParam.TypeName = "dbo.InteractionInput";
            interactionsParam.Value = GetTableInput(dinsAndNames);
            var interactionsParams = new List<SqlParameter>();
            interactionsParams.Add(interactionsParam);
            dbInteractions = _context.GetData<SPInteraction>("dbo.GetInteractions", interactionsParams).ToList();

            return dbInteractions;
        }

        private System.Data.DataTable GetTableInput(List<VMInteractionRequest> dinsAndNames)
        {
            var table = new System.Data.DataTable();
            table.Columns.Add("Din", typeof(string));
            table.Columns.Add("MedicationName", typeof(string));


            foreach (var item in dinsAndNames)
            {               
                table.Rows.Add(item.Din, item.MedicationName);
            }

            return table;
        }

        //public List<VMInteraction> GetInteractions(List<string> dinsAndNames)
        //{
        //    var list = new List<VMInteraction>();
        //    try
        //    {
        //        var dbInterations = GetDBInteractions(dinsAndNames);

        //        foreach (var item in dbInterations)
        //        {
        //            var isNew = false;
        //            var interac = list.Where(a => a.nx_id1 == item.Din1 && a.nx_id2 == item.Din2).FirstOrDefault();
        //            if (interac != null)
        //            {
        //                interac.Description += item.ActualText;
        //            }
        //            else
        //            {
        //                isNew = true;
        //                interac = new VMInteraction();
        //                interac.MedicationName = item.MedicationName1;
        //                interac.InteratingMediationName = item.MedicationName2;
        //                interac.nx_id1 = item.Din1;
        //                interac.nx_id2 = item.Din2;
        //                interac.Description = item.ActualText;
        //                interac.SeverityDescription = item.SeverityDescription;
        //                interac.SeverityCode = item.SeverityCode;
        //                interac.DocumentationCode = item.DocumentationCode;
        //                interac.DocumentationDescription = item.DocumentationDescription;
        //                interac.OnsetCode = item.OnsetCode;
        //                interac.OnsetDescription = item.OnsetDescription;

        //            }

        //            if (!string.IsNullOrWhiteSpace(interac.MedicationName))
        //                interac.Description = interac.Description.Replace("@A@", interac.MedicationName);
        //            if (!string.IsNullOrWhiteSpace(interac.InteratingMediationName))
        //                interac.Description = interac.Description.Replace("@B@", interac.InteratingMediationName);

        //            if (isNew)
        //            {
        //                list.Add(interac);
        //            }

        //        }

        //    }
        //    catch (Exception ex)
        //    {

        //    }
        //    return list;
        //}

        //private List<SPInteraction> GetDBInteractions(List<string> dinsAndNames)
        //{
        //    var dbInteractions = new List<SPInteraction>();

        //    var interactionsParam = new SqlParameter("tblDins", System.Data.SqlDbType.Structured);
        //    interactionsParam.TypeName = "dbo.InteractionInput";
        //    interactionsParam.Value = GetTableInput(dinsAndNames);
        //    var interactionsParams = new List<SqlParameter>();
        //    interactionsParams.Add(interactionsParam);
        //    dbInteractions = _context.GetData<SPInteraction>("dbo.GetInteractions", interactionsParams).ToList();

        //    return dbInteractions;
        //}

        //private System.Data.DataTable GetTableInput(List<string> dinsAndNames)
        //{
        //    var table = new System.Data.DataTable();
        //    table.Columns.Add("Din", typeof(string));
        //    table.Columns.Add("MedicationName", typeof(string));


        //    foreach (var item in dinsAndNames)
        //    {
        //        string[] ar = item.Split('!');
        //        string din = ar[0];
        //        string medName = ar[1];
        //        table.Rows.Add(din, medName);
        //    }

        //    return table;
        //}
    }
    class Cartesian
    {
        public string nx_id1 { get; set; }
        public string drug1 { get; set; }
        public string KDC1A { get; set; }
        public string classA { get; set; }
        public string nx_id2 { get; set; }
        public string drug2 { get; set; }
        public string KDC1B { get; set; }
        public string classB { get; set; }
    }
    class VMInterActions
    {
        public string nx_id1 { get; set; }
        public string drug1 { get; set; }
        public string KDC1 { get; set; }
        public string classID1 { get; set; }
        public string nx_id2 { get; set; }
        public string drug2 { get; set; }
        public string KDC2 { get; set; }
        public string classID2 { get; set; }
        public string DrugInteractionID { get; set; }
        public string OnsetCode { get; set; }
        public string SeverityCode { get; set; }
        public string DocumentationCode { get; set; }
        public string ManagementCode { get; set; }
        public string ContraindicationsCode { get; set; }

    }

    class drugClass
    {
        public string nx_id { get; set; }
        public string drug { get; set; }
        public string KDC { get; set; }
        public string classID { get; set; }
    }

    public class SPInteraction
    {       
        public string Din1 { get; set; }
        public string MedicationName1 { get; set; }        
        public string CLASSA { get; set; }
        public string Din2 { get; set; }
        public string MedicationName2 { get; set; }
        public string CLASSB { get; set; }
        public string DrugInteractionID { get; set; }
        public string OnsetCode { get; set; }
        public string OnsetDescription { get; set; }
        public string SeverityCode { get; set; }
        public string SeverityDescription { get; set; }
        public string DocumentationCode { get; set; }
        public string DocumentationDescription { get; set; }
        public string ManagementCode { get; set; }
        public string ContraindicationsCode { get; set; }
        public string ActualText { get; set; }
    }
}
