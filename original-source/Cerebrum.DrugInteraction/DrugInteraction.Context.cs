﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cerebrum.DrugInteraction
{
    using System;
    using System.Collections.Generic;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.SqlClient;
    using System.Linq;

    public partial class DrugsInteractionEntities : DbContext
    {
        public DrugsInteractionEntities()
            : base("name=DrugsInteractionEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<CD2AHFS_N> CD2AHFS_N { get; set; }
        public virtual DbSet<CD2ATTC_T> CD2ATTC_T { get; set; }
        public virtual DbSet<CD2ATTN_S> CD2ATTN_S { get; set; }
        public virtual DbSet<CD2DBN_I> CD2DBN_I { get; set; }
        public virtual DbSet<CD2DDID_E> CD2DDID_E { get; set; }
        public virtual DbSet<CD2DF_J> CD2DF_J { get; set; }
        public virtual DbSet<CD2DIN_O> CD2DIN_O { get; set; }
        public virtual DbSet<CD2DN_H> CD2DN_H { get; set; }
        public virtual DbSet<CD2DXRF_F> CD2DXRF_F { get; set; }
        public virtual DbSet<CD2GPAH_M> CD2GPAH_M { get; set; }
        public virtual DbSet<CD2GPDD_L> CD2GPDD_L { get; set; }
        public virtual DbSet<CD2GPPC_Q> CD2GPPC_Q { get; set; }
        public virtual DbSet<CD2LBL_P> CD2LBL_P { get; set; }
        public virtual DbSet<CD2RT_K> CD2RT_K { get; set; }
        public virtual DbSet<CD2RTD_G> CD2RTD_G { get; set; }
        public virtual DbSet<CD2SUM_A> CD2SUM_A { get; set; }
        public virtual DbSet<CD2TCGPI_U> CD2TCGPI_U { get; set; }
        public virtual DbSet<CD2VAL_D> CD2VAL_D { get; set; }
        public virtual DbSet<D01Class_C> D01Class_C { get; set; }
        public virtual DbSet<D01ClassIngredient_D> D01ClassIngredient_D { get; set; }
        public virtual DbSet<D02PARClass_E> D02PARClass_E { get; set; }
        public virtual DbSet<D02PARClassIngredient_F> D02PARClassIngredient_F { get; set; }
        public virtual DbSet<DrugName2KDC_B> DrugName2KDC_B { get; set; }
        public virtual DbSet<DTMINAK_Q> DTMINAK_Q { get; set; }
        public virtual DbSet<DTMLMIB_R> DTMLMIB_R { get; set; }
        public virtual DbSet<DTMMAP_O> DTMMAP_O { get; set; }
        public virtual DbSet<DTMNAME_S> DTMNAME_S { get; set; }
        public virtual DbSet<DTMOLD_P> DTMOLD_P { get; set; }
        public virtual DbSet<M01InteractingDrugs_H> M01InteractingDrugs_H { get; set; }
        public virtual DbSet<M01InteractionMainFile_G> M01InteractionMainFile_G { get; set; }
        public virtual DbSet<M01InteractionMonographs_I> M01InteractionMonographs_I { get; set; }
        public virtual DbSet<M02PARMainFile_J> M02PARMainFile_J { get; set; }
        public virtual DbSet<M02PARMonographs_L> M02PARMonographs_L { get; set; }
        public virtual DbSet<M02PARReactingDrugs_K> M02PARReactingDrugs_K { get; set; }
        public virtual DbSet<N01KDC2DrugName_M> N01KDC2DrugName_M { get; set; }
        public virtual DbSet<NDC2KDC_A> NDC2KDC_A { get; set; }
        public virtual DbSet<R01RouteFullName_N> R01RouteFullName_N { get; set; }
        public virtual DbSet<sysdiagram> sysdiagrams { get; set; }


        public IEnumerable<T> GetData<T>(string storedProcedure, List<SqlParameter> parameters = null) where T : class
        {
            List<T> list = new List<T>();
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                
                try
                {
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    cmd.Connection.Open();
                    list = ((IObjectContextAdapter)this).ObjectContext.Translate<T>(cmd.ExecuteReader()).ToList();
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            return list;
        }
    }
}
