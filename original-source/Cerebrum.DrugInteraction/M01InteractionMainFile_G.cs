//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Cerebrum.DrugInteraction
{
    using System;
    using System.Collections.Generic;
    
    public partial class M01InteractionMainFile_G
    {
        public double ID { get; set; }
        public string CLASSID1 { get; set; }
        public string CLASSID2 { get; set; }
        public string DrugInteractionID { get; set; }
        public string RTYPE { get; set; }
        public string RSTYPE { get; set; }
        public string DURATION1 { get; set; }
        public string SCHEDULE1 { get; set; }
        public string Class1CharacterCount { get; set; }
        public string InteractingDrugsCount1 { get; set; }
        public string DURATION2 { get; set; }
        public string SCHEDULE2 { get; set; }
        public string Class2CharacterCount { get; set; }
        public string InteractingDrugsCount2 { get; set; }
        public string OnsetCode { get; set; }
        public string SeverityCode { get; set; }
        public string DocumentationCode { get; set; }
        public string ManagementCode { get; set; }
        public string ActivityCode1 { get; set; }
        public string ActivityCode2 { get; set; }
        public string ContraindicationsCode { get; set; }
        public string WarningTextCharacterCount { get; set; }
        public string EffectsTextCharacterCount { get; set; }
        public string MechanismTextCharacterCount { get; set; }
        public string ManagementTextCharacterCount { get; set; }
        public string DiscussionTextCharcterCount { get; set; }
        public string ReferenceTextCharacterCount { get; set; }
        public string InteractionType { get; set; }
    }
}
