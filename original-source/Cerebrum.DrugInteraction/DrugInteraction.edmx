﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="DrugsInteractionModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <!--Errors Found During Generation:
warning 6013: The table/view 'DrugsInteraction.dbo.C1' does not have a primary key defined and no valid primary key could be inferred. This table/view has been excluded. To use the entity, you will need to review your schema, add the correct keys, and uncomment it.
        <EntityType Name="C1">
          <Property Name="CLASSID" Type="nvarchar" MaxLength="5" />
        </EntityType>-->
        <EntityType Name="CD2AHFS_N">
          <Key>
            <PropertyRef Name="AHFS_8_Code" />
          </Key>
          <Property Name="AHFS_8_Code" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="AHFS_Clssfctn_Dscrptn" Type="nvarchar" MaxLength="80" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="37" />
        </EntityType>
        <EntityType Name="CD2ATTC_T">
          <Key>
            <PropertyRef Name="Attribute_Identifier" />
            <PropertyRef Name="Attribute_Code" />
          </Key>
          <Property Name="Attribute_Identifier" Type="bigint" Nullable="false" />
          <Property Name="Attribute_Code" Type="varchar" MaxLength="4" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Attribute_Code_Description" Type="nvarchar" MaxLength="40" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="25" />
        </EntityType>
        <EntityType Name="CD2ATTN_S">
          <Key>
            <PropertyRef Name="Attribute_Identifier" />
          </Key>
          <Property Name="Attribute_Identifier" Type="bigint" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Attribute_Name" Type="nvarchar" MaxLength="40" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="13" />
        </EntityType>
        <EntityType Name="CD2DBN_I">
          <Key>
            <PropertyRef Name="Drug_Name_ID" />
          </Key>
          <Property Name="Drug_Name_ID" Type="bigint" Nullable="false" />
          <Property Name="Country_Code" Type="nvarchar" MaxLength="2" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Brand_Name_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="18" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6013: The table/view 'DrugsInteraction.dbo.CD2DDAT_R' does not have a primary key defined and no valid primary key could be inferred. This table/view has been excluded. To use the entity, you will need to review your schema, add the correct keys, and uncomment it.
        <EntityType Name="CD2DDAT_R">
          <Property Name="Drug_Descriptor_ID" Type="bigint" />
          <Property Name="Country_Code" Type="nvarchar" MaxLength="2" />
          <Property Name="Attribute_Identifier" Type="bigint" />
          <Property Name="Attribute_Code" Type="nvarchar" MaxLength="4" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="21" />
        </EntityType>-->
        <EntityType Name="CD2DDID_E">
          <Key>
            <PropertyRef Name="Drug_Descriptor_ID" />
          </Key>
          <Property Name="Drug_Descriptor_ID" Type="bigint" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Routed_Drug_ID" Type="bigint" />
          <Property Name="Dosage_Form_ID" Type="int" />
          <Property Name="Dispensable_Drug_Strength" Type="nvarchar" MaxLength="15" />
          <Property Name="Dispensable_Drug_Strength_UOM" Type="nvarchar" MaxLength="15" />
          <Property Name="Dispensable_Drug_Description" Type="nvarchar" MaxLength="70" />
          <Property Name="New_Dispensable_Drug_ID" Type="bigint" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="40" />
        </EntityType>
        <EntityType Name="CD2DF_J">
          <Key>
            <PropertyRef Name="Dosage_Form_ID" />
          </Key>
          <Property Name="Dosage_Form_ID" Type="int" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Dosage_Form_Abbreviation" Type="nvarchar" MaxLength="4" />
          <Property Name="Dosage_Form_15_Abbreviation" Type="nvarchar" MaxLength="15" />
          <Property Name="Dosage_Form_Description" Type="nvarchar" MaxLength="40" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="31" />
        </EntityType>
        <EntityType Name="CD2DIN_O">
          <Key>
            <PropertyRef Name="External_Drug_Identifier" />
          </Key>
          <Property Name="External_Drug_Identifier" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Knowledge_Base_Drug_Code" Type="nvarchar" MaxLength="10" />
          <Property Name="Dispensable_Drug_ID" Type="bigint" />
          <Property Name="Generic_Product_Identifier" Type="nvarchar" MaxLength="14" />
          <Property Name="Inactive_Date" Type="nvarchar" MaxLength="8" />
          <Property Name="ID_Number_Type_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="NAPRA_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Schedule_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Generic_Product_Packaging_Code" Type="nvarchar" MaxLength="8" />
          <Property Name="Alternate_Package_Size" Type="real" />
          <Property Name="Alternate_Package_Size_UOM" Type="nvarchar" MaxLength="2" />
          <Property Name="Metric_Strength" Type="real" />
          <Property Name="Mtrc_Strngth_Unt_of_Msr" Type="nvarchar" MaxLength="11" />
          <Property Name="Labeler_Code" Type="nvarchar" MaxLength="5" />
          <Property Name="Old_External_Drug_Identifier" Type="nvarchar" MaxLength="20" />
          <Property Name="New_External_Drug_Identifier" Type="nvarchar" MaxLength="20" />
          <Property Name="Third_Party_Restriction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Maintenance_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Form_Type_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Internal_External_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Single_Combination_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Storage_Condition_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Limited_Stability_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="61" />
        </EntityType>
        <EntityType Name="CD2DN_H">
          <Key>
            <PropertyRef Name="Drug_Name_ID" />
          </Key>
          <Property Name="Drug_Name_ID" Type="bigint" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Drug_Name" Type="nvarchar" MaxLength="30" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="23" />
        </EntityType>
        <EntityType Name="CD2DXRF_F">
          <Key>
            <PropertyRef Name="Drug_Descriptor_ID" />
            <PropertyRef Name="Country_Code" />
          </Key>
          <Property Name="Drug_Descriptor_ID" Type="bigint" Nullable="false" />
          <Property Name="Country_Code" Type="varchar" MaxLength="2" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Generic_Product_Identifier" Type="nvarchar" MaxLength="14" />
          <Property Name="GPI_Assignment_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Knowledge_Base_Drug_Code" Type="nvarchar" MaxLength="10" />
          <Property Name="KDC_Assignment_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Single_Active_Ingredient_Indic" Type="nvarchar" MaxLength="1" />
          <Property Name="Device_Indicator" Type="nvarchar" MaxLength="1" />
          <Property Name="Historical_Indicator_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Obsolete_Date" Type="nvarchar" MaxLength="8" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="30" />
        </EntityType>
        <EntityType Name="CD2GPAH_M">
          <Key>
            <PropertyRef Name="Generic_Product_Identifier" />
            <PropertyRef Name="Country_Code" />
            <PropertyRef Name="AHFS_8_Code" />
          </Key>
          <Property Name="Generic_Product_Identifier" Type="varchar" MaxLength="14" Nullable="false" />
          <Property Name="Country_Code" Type="varchar" MaxLength="2" Nullable="false" />
          <Property Name="AHFS_8_Code" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="AHFS_Limiter_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="20" />
        </EntityType>
        <EntityType Name="CD2GPDD_L">
          <Key>
            <PropertyRef Name="Generic_Product_Identifier" />
            <PropertyRef Name="Country_Code" />
            <PropertyRef Name="Drug_Descriptor_ID" />
          </Key>
          <Property Name="Generic_Product_Identifier" Type="varchar" MaxLength="14" Nullable="false" />
          <Property Name="Country_Code" Type="varchar" MaxLength="2" Nullable="false" />
          <Property Name="Drug_Descriptor_ID" Type="bigint" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="21" />
        </EntityType>
        <EntityType Name="CD2GPPC_Q">
          <Key>
            <PropertyRef Name="Generic_Product_Packaging_Code" />
          </Key>
          <Property Name="Generic_Product_Packaging_Code" Type="varchar" MaxLength="8" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Package_Size" Type="real" />
          <Property Name="Package_Size_Unit_of_Measure" Type="nvarchar" MaxLength="2" />
          <Property Name="Package_Quantity" Type="int" />
          <Property Name="Unit_DoseUnit_of_Use_Package" Type="nvarchar" MaxLength="1" />
          <Property Name="Package_Description" Type="nvarchar" MaxLength="10" />
          <Property Name="Generic_Product_Identifier" Type="nvarchar" MaxLength="14" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="14" />
        </EntityType>
        <EntityType Name="CD2LBL_P">
          <Key>
            <PropertyRef Name="Labeler_Code" />
            <PropertyRef Name="Country_Code" />
          </Key>
          <Property Name="Labeler_Code" Type="varchar" MaxLength="5" Nullable="false" />
          <Property Name="Country_Code" Type="varchar" MaxLength="2" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Manufacturers_Labeler_Name" Type="nvarchar" MaxLength="30" />
          <Property Name="Manufacturers_Abbreviated_Name" Type="nvarchar" MaxLength="10" />
          <Property Name="Parent_Labeler_Code" Type="nvarchar" MaxLength="5" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="27" />
        </EntityType>
        <EntityType Name="CD2RT_K">
          <Key>
            <PropertyRef Name="Route_ID" />
          </Key>
          <Property Name="Route_ID" Type="bigint" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Route_Abbreviation" Type="nvarchar" MaxLength="2" />
          <Property Name="Route_15_Abbreviation" Type="nvarchar" MaxLength="15" />
          <Property Name="Route_Description" Type="nvarchar" MaxLength="40" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="17" />
        </EntityType>
        <EntityType Name="CD2RTD_G">
          <Key>
            <PropertyRef Name="Routed_Drug_ID" />
          </Key>
          <Property Name="Routed_Drug_ID" Type="bigint" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="Drug_Name_ID" Type="bigint" />
          <Property Name="Route_ID" Type="int" />
          <Property Name="Routed_Drug_Description" Type="nvarchar" MaxLength="60" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="26" />
        </EntityType>
        <EntityType Name="CD2SUM_A">
          <Key>
            <PropertyRef Name="Record_Type" />
            <PropertyRef Name="Sequence_Number" />
          </Key>
          <Property Name="Record_Type" Type="varchar" MaxLength="3" Nullable="false" />
          <Property Name="Reserve_1" Type="nvarchar" MaxLength="1" />
          <Property Name="Sequence_Number" Type="int" Nullable="false" />
          <Property Name="Reserve_2" Type="nvarchar" MaxLength="1" />
          <Property Name="Comment_Marker" Type="nvarchar" MaxLength="1" />
          <Property Name="Data_or_Comment" Type="nvarchar" MaxLength="87" />
        </EntityType>
        <EntityType Name="CD2TCGPI_U">
          <Key>
            <PropertyRef Name="TC_GPI_Key" />
            <PropertyRef Name="Country_Code" />
            <PropertyRef Name="Record_Type" />
          </Key>
          <Property Name="TC_GPI_Key" Type="varchar" MaxLength="14" Nullable="false" />
          <Property Name="Country_Code" Type="varchar" MaxLength="2" Nullable="false" />
          <Property Name="Record_Type" Type="varchar" MaxLength="1" Nullable="false" />
          <Property Name="Transaction_Code" Type="nvarchar" MaxLength="1" />
          <Property Name="TC_GPI_Name" Type="nvarchar" MaxLength="60" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="34" />
        </EntityType>
        <EntityType Name="CD2VAL_D">
          <Key>
            <PropertyRef Name="Field_Identifier" />
            <PropertyRef Name="Field_Value" />
          </Key>
          <Property Name="Field_Identifier" Type="varchar" MaxLength="4" Nullable="false" />
          <Property Name="Field_Value" Type="varchar" MaxLength="15" Nullable="false" />
          <Property Name="Language_Code" Type="int" />
          <Property Name="Value_Description" Type="nvarchar" MaxLength="40" />
          <Property Name="Value_Abbreviation" Type="nvarchar" MaxLength="15" />
          <Property Name="Reserve" Type="nvarchar" MaxLength="20" />
        </EntityType>
        <EntityType Name="D01Class_C">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="LINENUM" Type="nvarchar" MaxLength="4" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="CLASSID" Type="nvarchar" MaxLength="5" />
        </EntityType>
        <EntityType Name="D01ClassIngredient_D">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="CLASSID" Type="nvarchar" MaxLength="5" />
          <Property Name="LINENUM" Type="nvarchar" MaxLength="4" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="INGREDIENTID" Type="nvarchar" MaxLength="5" />
        </EntityType>
        <EntityType Name="D02PARClass_E">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="LINENUM" Type="nvarchar" MaxLength="4" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="CLASSID" Type="nvarchar" MaxLength="5" />
        </EntityType>
        <EntityType Name="D02PARClassIngredient_F">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="CLASSID" Type="nvarchar" MaxLength="5" />
          <Property Name="LINENUM" Type="nvarchar" MaxLength="4" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="INGREDIENTID" Type="nvarchar" MaxLength="5" />
        </EntityType>
        <EntityType Name="DrugName2KDC_B">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="DrugName" Type="nvarchar" MaxLength="50" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="KDC2" Type="nvarchar" MaxLength="2" />
          <Property Name="KDC3" Type="nvarchar" MaxLength="3" />
          <Property Name="ACTCODE" Type="nvarchar" MaxLength="1" />
          <Property Name="ROUTE" Type="nvarchar" MaxLength="2" />
          <Property Name="NeedName" Type="nvarchar" MaxLength="1" />
          <Property Name="NameType" Type="nvarchar" MaxLength="1" />
        </EntityType>
        <EntityType Name="DTMINAK_Q">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="RecordCode" Type="nvarchar" MaxLength="1" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="KDC2" Type="nvarchar" MaxLength="2" />
        </EntityType>
        <EntityType Name="DTMLMIB_R">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="DrugInteractionID" Type="nvarchar" MaxLength="4" />
          <Property Name="ExternalListID" Type="nvarchar" MaxLength="4" />
        </EntityType>
        <EntityType Name="DTMMAP_O">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="OldPARClassNumber" Type="nvarchar" MaxLength="5" />
          <Property Name="NewPARClassNumber" Type="nvarchar" MaxLength="5" />
          <Property Name="DatePARReplaced" Type="nvarchar" MaxLength="8" />
        </EntityType>
        <EntityType Name="DTMNAME_S">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="ExternalListID" Type="nvarchar" MaxLength="4" />
          <Property Name="ExternalListDesc" Type="nvarchar" MaxLength="70" />
        </EntityType>
        <EntityType Name="DTMOLD_P">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="OldPARClassNumber" Type="nvarchar" MaxLength="5" />
          <Property Name="OldPARClassDescription" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="M01InteractingDrugs_H">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="CLASSID1" Type="nvarchar" MaxLength="5" />
          <Property Name="CLASSID2" Type="nvarchar" MaxLength="5" />
          <Property Name="DrugInteractionID" Type="nvarchar" MaxLength="4" />
          <Property Name="ClassIndicator" Type="nvarchar" MaxLength="1" />
          <Property Name="LineNumber" Type="nvarchar" MaxLength="4" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="ReferenceIndicator" Type="nvarchar" MaxLength="1" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="KDC2" Type="nvarchar" MaxLength="2" />
        </EntityType>
        <EntityType Name="M01InteractionMainFile_G">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="CLASSID1" Type="nvarchar" MaxLength="5" />
          <Property Name="CLASSID2" Type="nvarchar" MaxLength="5" />
          <Property Name="DrugInteractionID" Type="nvarchar" MaxLength="4" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="DURATION1" Type="nvarchar" MaxLength="3" />
          <Property Name="SCHEDULE1" Type="nvarchar" MaxLength="3" />
          <Property Name="Class1CharacterCount" Type="nvarchar" MaxLength="2" />
          <Property Name="InteractingDrugsCount1" Type="nvarchar" MaxLength="2" />
          <Property Name="DURATION2" Type="nvarchar" MaxLength="3" />
          <Property Name="SCHEDULE2" Type="nvarchar" MaxLength="3" />
          <Property Name="Class2CharacterCount" Type="nvarchar" MaxLength="2" />
          <Property Name="InteractingDrugsCount2" Type="nvarchar" MaxLength="2" />
          <Property Name="OnsetCode" Type="nvarchar" MaxLength="1" />
          <Property Name="SeverityCode" Type="nvarchar" MaxLength="1" />
          <Property Name="DocumentationCode" Type="nvarchar" MaxLength="1" />
          <Property Name="ManagementCode" Type="nvarchar" MaxLength="1" />
          <Property Name="ActivityCode1" Type="nvarchar" MaxLength="1" />
          <Property Name="ActivityCode2" Type="nvarchar" MaxLength="1" />
          <Property Name="ContraindicationsCode" Type="nvarchar" MaxLength="1" />
          <Property Name="WarningTextCharacterCount" Type="nvarchar" MaxLength="4" />
          <Property Name="EffectsTextCharacterCount" Type="nvarchar" MaxLength="4" />
          <Property Name="MechanismTextCharacterCount" Type="nvarchar" MaxLength="4" />
          <Property Name="ManagementTextCharacterCount" Type="nvarchar" MaxLength="4" />
          <Property Name="DiscussionTextCharcterCount" Type="nvarchar" MaxLength="4" />
          <Property Name="ReferenceTextCharacterCount" Type="nvarchar" MaxLength="4" />
          <Property Name="InteractionType" Type="nvarchar" MaxLength="1" />
        </EntityType>
        <EntityType Name="M01InteractionMonographs_I">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="CLASSID1" Type="nvarchar" MaxLength="5" />
          <Property Name="CLASSID2" Type="nvarchar" MaxLength="5" />
          <Property Name="DrugInteractionID" Type="nvarchar" MaxLength="4" />
          <Property Name="LineNumber" Type="nvarchar" MaxLength="4" />
          <Property Name="TextName" Type="nvarchar" MaxLength="3" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="ActualText" Type="nvarchar" MaxLength="72" />
        </EntityType>
        <EntityType Name="M02PARMainFile_J">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="CLASSID1" Type="nvarchar" MaxLength="5" />
          <Property Name="CLASSID2" Type="nvarchar" MaxLength="5" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="Class1CharacterCount" Type="nvarchar" MaxLength="2" />
          <Property Name="Class2CharacterCount" Type="nvarchar" MaxLength="2" />
          <Property Name="PriorReactionHeaderCharCount" Type="nvarchar" MaxLength="4" />
          <Property Name="ReactingDrugHeaderCharCount" Type="nvarchar" MaxLength="4" />
          <Property Name="DiscussionTextCharacterCount" Type="nvarchar" MaxLength="4" />
          <Property Name="ReferencesTextCharacterCount" Type="nvarchar" MaxLength="4" />
          <Property Name="ReactingDrugCount" Type="nvarchar" MaxLength="2" />
          <Property Name="Symptoms" Type="nvarchar" MaxLength="6" />
        </EntityType>
        <EntityType Name="M02PARMonographs_L">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="CLASSID1" Type="nvarchar" MaxLength="5" />
          <Property Name="CLASSID2" Type="nvarchar" MaxLength="5" />
          <Property Name="LineNumber" Type="nvarchar" MaxLength="4" />
          <Property Name="TextName" Type="nvarchar" MaxLength="3" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="MonographText" Type="nvarchar" MaxLength="72" />
        </EntityType>
        <EntityType Name="M02PARReactingDrugs_K">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="CLASSID1" Type="nvarchar" MaxLength="5" />
          <Property Name="CLASSID2" Type="nvarchar" MaxLength="5" />
          <Property Name="LineNumber" Type="nvarchar" MaxLength="4" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="KDC2" Type="nvarchar" MaxLength="2" />
        </EntityType>
        <EntityType Name="N01KDC2DrugName_M">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="KDC2" Type="nvarchar" MaxLength="2" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="DrugName" Type="nvarchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="NDC2KDC_A">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="NDC" Type="nvarchar" MaxLength="11" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="RSTYPE" Type="nvarchar" MaxLength="1" />
          <Property Name="KDC1" Type="nvarchar" MaxLength="5" />
          <Property Name="KDC2" Type="nvarchar" MaxLength="2" />
          <Property Name="KDC3" Type="nvarchar" MaxLength="3" />
          <Property Name="ACTCODE" Type="nvarchar" MaxLength="1" />
          <Property Name="ROUTE" Type="nvarchar" MaxLength="2" />
        </EntityType>
        <EntityType Name="R01RouteFullName_N">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="float" Nullable="false" />
          <Property Name="Route" Type="nvarchar" MaxLength="2" />
          <Property Name="RTYPE" Type="nvarchar" MaxLength="3" />
          <Property Name="ActivityCode" Type="nvarchar" MaxLength="1" />
          <Property Name="RouteFullName" Type="nvarchar" MaxLength="38" />
        </EntityType>
        <EntityType Name="sysdiagrams">
          <Key>
            <PropertyRef Name="diagram_id" />
          </Key>
          <Property Name="name" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="principal_id" Type="int" Nullable="false" />
          <Property Name="diagram_id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="version" Type="int" />
          <Property Name="definition" Type="varbinary(max)" />
        </EntityType>
        <EntityContainer Name="DrugsInteractionModelStoreContainer">
          <EntitySet Name="CD2AHFS_N" EntityType="Self.CD2AHFS_N" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2ATTC_T" EntityType="Self.CD2ATTC_T" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2ATTN_S" EntityType="Self.CD2ATTN_S" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2DBN_I" EntityType="Self.CD2DBN_I" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2DDID_E" EntityType="Self.CD2DDID_E" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2DF_J" EntityType="Self.CD2DF_J" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2DIN_O" EntityType="Self.CD2DIN_O" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2DN_H" EntityType="Self.CD2DN_H" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2DXRF_F" EntityType="Self.CD2DXRF_F" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2GPAH_M" EntityType="Self.CD2GPAH_M" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2GPDD_L" EntityType="Self.CD2GPDD_L" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2GPPC_Q" EntityType="Self.CD2GPPC_Q" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2LBL_P" EntityType="Self.CD2LBL_P" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2RT_K" EntityType="Self.CD2RT_K" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2RTD_G" EntityType="Self.CD2RTD_G" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2SUM_A" EntityType="Self.CD2SUM_A" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2TCGPI_U" EntityType="Self.CD2TCGPI_U" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="CD2VAL_D" EntityType="Self.CD2VAL_D" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="D01Class_C" EntityType="Self.D01Class_C" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="D01ClassIngredient_D" EntityType="Self.D01ClassIngredient_D" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="D02PARClass_E" EntityType="Self.D02PARClass_E" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="D02PARClassIngredient_F" EntityType="Self.D02PARClassIngredient_F" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DrugName2KDC_B" EntityType="Self.DrugName2KDC_B" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DTMINAK_Q" EntityType="Self.DTMINAK_Q" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DTMLMIB_R" EntityType="Self.DTMLMIB_R" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DTMMAP_O" EntityType="Self.DTMMAP_O" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DTMNAME_S" EntityType="Self.DTMNAME_S" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="DTMOLD_P" EntityType="Self.DTMOLD_P" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="M01InteractingDrugs_H" EntityType="Self.M01InteractingDrugs_H" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="M01InteractionMainFile_G" EntityType="Self.M01InteractionMainFile_G" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="M01InteractionMonographs_I" EntityType="Self.M01InteractionMonographs_I" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="M02PARMainFile_J" EntityType="Self.M02PARMainFile_J" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="M02PARMonographs_L" EntityType="Self.M02PARMonographs_L" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="M02PARReactingDrugs_K" EntityType="Self.M02PARReactingDrugs_K" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="N01KDC2DrugName_M" EntityType="Self.N01KDC2DrugName_M" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="NDC2KDC_A" EntityType="Self.NDC2KDC_A" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="R01RouteFullName_N" EntityType="Self.R01RouteFullName_N" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="sysdiagrams" EntityType="Self.sysdiagrams" Schema="dbo" store:Type="Tables" />
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="DrugsInteractionModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="CD2AHFS_N">
          <Key>
            <PropertyRef Name="AHFS_8_Code" />
          </Key>
          <Property Name="AHFS_8_Code" Type="String" MaxLength="10" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="AHFS_Clssfctn_Dscrptn" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="37" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2ATTC_T">
          <Key>
            <PropertyRef Name="Attribute_Identifier" />
            <PropertyRef Name="Attribute_Code" />
          </Key>
          <Property Name="Attribute_Identifier" Type="Int64" Nullable="false" />
          <Property Name="Attribute_Code" Type="String" MaxLength="4" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Attribute_Code_Description" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="25" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2ATTN_S">
          <Key>
            <PropertyRef Name="Attribute_Identifier" />
          </Key>
          <Property Name="Attribute_Identifier" Type="Int64" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Attribute_Name" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="13" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2DBN_I">
          <Key>
            <PropertyRef Name="Drug_Name_ID" />
          </Key>
          <Property Name="Drug_Name_ID" Type="Int64" Nullable="false" />
          <Property Name="Country_Code" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Brand_Name_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="18" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2DDID_E">
          <Key>
            <PropertyRef Name="Drug_Descriptor_ID" />
          </Key>
          <Property Name="Drug_Descriptor_ID" Type="Int64" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Routed_Drug_ID" Type="Int64" />
          <Property Name="Dosage_Form_ID" Type="Int32" />
          <Property Name="Dispensable_Drug_Strength" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Dispensable_Drug_Strength_UOM" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Dispensable_Drug_Description" Type="String" MaxLength="70" FixedLength="false" Unicode="true" />
          <Property Name="New_Dispensable_Drug_ID" Type="Int64" />
          <Property Name="Reserve" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2DF_J">
          <Key>
            <PropertyRef Name="Dosage_Form_ID" />
          </Key>
          <Property Name="Dosage_Form_ID" Type="Int32" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Dosage_Form_Abbreviation" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="Dosage_Form_15_Abbreviation" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Dosage_Form_Description" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="31" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2DIN_O">
          <Key>
            <PropertyRef Name="External_Drug_Identifier" />
          </Key>
          <Property Name="External_Drug_Identifier" Type="String" MaxLength="20" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Knowledge_Base_Drug_Code" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Dispensable_Drug_ID" Type="Int64" />
          <Property Name="Generic_Product_Identifier" Type="String" MaxLength="14" FixedLength="false" Unicode="true" />
          <Property Name="Inactive_Date" Type="String" MaxLength="8" FixedLength="false" Unicode="true" />
          <Property Name="ID_Number_Type_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="NAPRA_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Schedule_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Generic_Product_Packaging_Code" Type="String" MaxLength="8" FixedLength="false" Unicode="true" />
          <Property Name="Alternate_Package_Size" Type="Single" />
          <Property Name="Alternate_Package_Size_UOM" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="Metric_Strength" Type="Single" />
          <Property Name="Mtrc_Strngth_Unt_of_Msr" Type="String" MaxLength="11" FixedLength="false" Unicode="true" />
          <Property Name="Labeler_Code" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Old_External_Drug_Identifier" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="New_External_Drug_Identifier" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
          <Property Name="Third_Party_Restriction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Maintenance_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Form_Type_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Internal_External_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Single_Combination_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Storage_Condition_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Limited_Stability_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="61" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2DN_H">
          <Key>
            <PropertyRef Name="Drug_Name_ID" />
          </Key>
          <Property Name="Drug_Name_ID" Type="Int64" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Drug_Name" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="23" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2DXRF_F">
          <Key>
            <PropertyRef Name="Drug_Descriptor_ID" />
            <PropertyRef Name="Country_Code" />
          </Key>
          <Property Name="Drug_Descriptor_ID" Type="Int64" Nullable="false" />
          <Property Name="Country_Code" Type="String" MaxLength="2" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Generic_Product_Identifier" Type="String" MaxLength="14" FixedLength="false" Unicode="true" />
          <Property Name="GPI_Assignment_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Knowledge_Base_Drug_Code" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="KDC_Assignment_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Single_Active_Ingredient_Indic" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Device_Indicator" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Historical_Indicator_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Obsolete_Date" Type="String" MaxLength="8" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2GPAH_M">
          <Key>
            <PropertyRef Name="Generic_Product_Identifier" />
            <PropertyRef Name="Country_Code" />
            <PropertyRef Name="AHFS_8_Code" />
          </Key>
          <Property Name="Generic_Product_Identifier" Type="String" MaxLength="14" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Country_Code" Type="String" MaxLength="2" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="AHFS_8_Code" Type="String" MaxLength="10" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="AHFS_Limiter_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2GPDD_L">
          <Key>
            <PropertyRef Name="Generic_Product_Identifier" />
            <PropertyRef Name="Country_Code" />
            <PropertyRef Name="Drug_Descriptor_ID" />
          </Key>
          <Property Name="Generic_Product_Identifier" Type="String" MaxLength="14" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Country_Code" Type="String" MaxLength="2" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Drug_Descriptor_ID" Type="Int64" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="21" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2GPPC_Q">
          <Key>
            <PropertyRef Name="Generic_Product_Packaging_Code" />
          </Key>
          <Property Name="Generic_Product_Packaging_Code" Type="String" MaxLength="8" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Package_Size" Type="Single" />
          <Property Name="Package_Size_Unit_of_Measure" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="Package_Quantity" Type="Int32" />
          <Property Name="Unit_DoseUnit_of_Use_Package" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Package_Description" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Generic_Product_Identifier" Type="String" MaxLength="14" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="14" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2LBL_P">
          <Key>
            <PropertyRef Name="Labeler_Code" />
            <PropertyRef Name="Country_Code" />
          </Key>
          <Property Name="Labeler_Code" Type="String" MaxLength="5" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Country_Code" Type="String" MaxLength="2" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Manufacturers_Labeler_Name" Type="String" MaxLength="30" FixedLength="false" Unicode="true" />
          <Property Name="Manufacturers_Abbreviated_Name" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <Property Name="Parent_Labeler_Code" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="27" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2RT_K">
          <Key>
            <PropertyRef Name="Route_ID" />
          </Key>
          <Property Name="Route_ID" Type="Int64" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Route_Abbreviation" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="Route_15_Abbreviation" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Route_Description" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="17" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2RTD_G">
          <Key>
            <PropertyRef Name="Routed_Drug_ID" />
          </Key>
          <Property Name="Routed_Drug_ID" Type="Int64" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Drug_Name_ID" Type="Int64" />
          <Property Name="Route_ID" Type="Int32" />
          <Property Name="Routed_Drug_Description" Type="String" MaxLength="60" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="26" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2SUM_A">
          <Key>
            <PropertyRef Name="Record_Type" />
            <PropertyRef Name="Sequence_Number" />
          </Key>
          <Property Name="Record_Type" Type="String" MaxLength="3" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Reserve_1" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Sequence_Number" Type="Int32" Nullable="false" />
          <Property Name="Reserve_2" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Comment_Marker" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Data_or_Comment" Type="String" MaxLength="87" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2TCGPI_U">
          <Key>
            <PropertyRef Name="TC_GPI_Key" />
            <PropertyRef Name="Country_Code" />
            <PropertyRef Name="Record_Type" />
          </Key>
          <Property Name="TC_GPI_Key" Type="String" MaxLength="14" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Country_Code" Type="String" MaxLength="2" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Record_Type" Type="String" MaxLength="1" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Transaction_Code" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="TC_GPI_Name" Type="String" MaxLength="60" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="34" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="CD2VAL_D">
          <Key>
            <PropertyRef Name="Field_Identifier" />
            <PropertyRef Name="Field_Value" />
          </Key>
          <Property Name="Field_Identifier" Type="String" MaxLength="4" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Field_Value" Type="String" MaxLength="15" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="Language_Code" Type="Int32" />
          <Property Name="Value_Description" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
          <Property Name="Value_Abbreviation" Type="String" MaxLength="15" FixedLength="false" Unicode="true" />
          <Property Name="Reserve" Type="String" MaxLength="20" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="D01Class_C">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="LINENUM" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="D01ClassIngredient_D">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="LINENUM" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="INGREDIENTID" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="D02PARClass_E">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="LINENUM" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="D02PARClassIngredient_F">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="LINENUM" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="INGREDIENTID" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="DrugName2KDC_B">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="DrugName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="KDC2" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="KDC3" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="ACTCODE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="ROUTE" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="NeedName" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="NameType" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="DTMINAK_Q">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="RecordCode" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="KDC2" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="DTMLMIB_R">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="DrugInteractionID" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="ExternalListID" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="DTMMAP_O">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="OldPARClassNumber" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="NewPARClassNumber" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="DatePARReplaced" Type="String" MaxLength="8" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="DTMNAME_S">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="ExternalListID" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="ExternalListDesc" Type="String" MaxLength="70" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="DTMOLD_P">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="OldPARClassNumber" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="OldPARClassDescription" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="M01InteractingDrugs_H">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="CLASSID1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID2" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="DrugInteractionID" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="ClassIndicator" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="LineNumber" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="ReferenceIndicator" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="KDC2" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="M01InteractionMainFile_G">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="CLASSID1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID2" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="DrugInteractionID" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="DURATION1" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="SCHEDULE1" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="Class1CharacterCount" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="InteractingDrugsCount1" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="DURATION2" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="SCHEDULE2" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="Class2CharacterCount" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="InteractingDrugsCount2" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="OnsetCode" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="SeverityCode" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="DocumentationCode" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="ManagementCode" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="ActivityCode1" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="ActivityCode2" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="ContraindicationsCode" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="WarningTextCharacterCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="EffectsTextCharacterCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="MechanismTextCharacterCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="ManagementTextCharacterCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="DiscussionTextCharcterCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="ReferenceTextCharacterCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="InteractionType" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="M01InteractionMonographs_I">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="CLASSID1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID2" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="DrugInteractionID" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="LineNumber" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="TextName" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="ActualText" Type="String" MaxLength="72" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="M02PARMainFile_J">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="CLASSID1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID2" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="Class1CharacterCount" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="Class2CharacterCount" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="PriorReactionHeaderCharCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="ReactingDrugHeaderCharCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="DiscussionTextCharacterCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="ReferencesTextCharacterCount" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="ReactingDrugCount" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="Symptoms" Type="String" MaxLength="6" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="M02PARMonographs_L">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="CLASSID1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID2" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="LineNumber" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="TextName" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="MonographText" Type="String" MaxLength="72" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="M02PARReactingDrugs_K">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="CLASSID1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="CLASSID2" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="LineNumber" Type="String" MaxLength="4" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="KDC2" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="N01KDC2DrugName_M">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="KDC2" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="DrugName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="NDC2KDC_A">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="NDC" Type="String" MaxLength="11" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="RSTYPE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="KDC1" Type="String" MaxLength="5" FixedLength="false" Unicode="true" />
          <Property Name="KDC2" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="KDC3" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="ACTCODE" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="ROUTE" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="R01RouteFullName_N">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Double" Nullable="false" />
          <Property Name="Route" Type="String" MaxLength="2" FixedLength="false" Unicode="true" />
          <Property Name="RTYPE" Type="String" MaxLength="3" FixedLength="false" Unicode="true" />
          <Property Name="ActivityCode" Type="String" MaxLength="1" FixedLength="false" Unicode="true" />
          <Property Name="RouteFullName" Type="String" MaxLength="38" FixedLength="false" Unicode="true" />
        </EntityType>
        <EntityType Name="sysdiagram">
          <Key>
            <PropertyRef Name="diagram_id" />
          </Key>
          <Property Name="name" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="principal_id" Type="Int32" Nullable="false" />
          <Property Name="diagram_id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="version" Type="Int32" />
          <Property Name="definition" Type="Binary" MaxLength="Max" FixedLength="false" />
        </EntityType>
        <EntityContainer Name="DrugsInteractionEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="CD2AHFS_N" EntityType="Self.CD2AHFS_N" />
          <EntitySet Name="CD2ATTC_T" EntityType="Self.CD2ATTC_T" />
          <EntitySet Name="CD2ATTN_S" EntityType="Self.CD2ATTN_S" />
          <EntitySet Name="CD2DBN_I" EntityType="Self.CD2DBN_I" />
          <EntitySet Name="CD2DDID_E" EntityType="Self.CD2DDID_E" />
          <EntitySet Name="CD2DF_J" EntityType="Self.CD2DF_J" />
          <EntitySet Name="CD2DIN_O" EntityType="Self.CD2DIN_O" />
          <EntitySet Name="CD2DN_H" EntityType="Self.CD2DN_H" />
          <EntitySet Name="CD2DXRF_F" EntityType="Self.CD2DXRF_F" />
          <EntitySet Name="CD2GPAH_M" EntityType="Self.CD2GPAH_M" />
          <EntitySet Name="CD2GPDD_L" EntityType="Self.CD2GPDD_L" />
          <EntitySet Name="CD2GPPC_Q" EntityType="Self.CD2GPPC_Q" />
          <EntitySet Name="CD2LBL_P" EntityType="Self.CD2LBL_P" />
          <EntitySet Name="CD2RT_K" EntityType="Self.CD2RT_K" />
          <EntitySet Name="CD2RTD_G" EntityType="Self.CD2RTD_G" />
          <EntitySet Name="CD2SUM_A" EntityType="Self.CD2SUM_A" />
          <EntitySet Name="CD2TCGPI_U" EntityType="Self.CD2TCGPI_U" />
          <EntitySet Name="CD2VAL_D" EntityType="Self.CD2VAL_D" />
          <EntitySet Name="D01Class_C" EntityType="Self.D01Class_C" />
          <EntitySet Name="D01ClassIngredient_D" EntityType="Self.D01ClassIngredient_D" />
          <EntitySet Name="D02PARClass_E" EntityType="Self.D02PARClass_E" />
          <EntitySet Name="D02PARClassIngredient_F" EntityType="Self.D02PARClassIngredient_F" />
          <EntitySet Name="DrugName2KDC_B" EntityType="Self.DrugName2KDC_B" />
          <EntitySet Name="DTMINAK_Q" EntityType="Self.DTMINAK_Q" />
          <EntitySet Name="DTMLMIB_R" EntityType="Self.DTMLMIB_R" />
          <EntitySet Name="DTMMAP_O" EntityType="Self.DTMMAP_O" />
          <EntitySet Name="DTMNAME_S" EntityType="Self.DTMNAME_S" />
          <EntitySet Name="DTMOLD_P" EntityType="Self.DTMOLD_P" />
          <EntitySet Name="M01InteractingDrugs_H" EntityType="Self.M01InteractingDrugs_H" />
          <EntitySet Name="M01InteractionMainFile_G" EntityType="Self.M01InteractionMainFile_G" />
          <EntitySet Name="M01InteractionMonographs_I" EntityType="Self.M01InteractionMonographs_I" />
          <EntitySet Name="M02PARMainFile_J" EntityType="Self.M02PARMainFile_J" />
          <EntitySet Name="M02PARMonographs_L" EntityType="Self.M02PARMonographs_L" />
          <EntitySet Name="M02PARReactingDrugs_K" EntityType="Self.M02PARReactingDrugs_K" />
          <EntitySet Name="N01KDC2DrugName_M" EntityType="Self.N01KDC2DrugName_M" />
          <EntitySet Name="NDC2KDC_A" EntityType="Self.NDC2KDC_A" />
          <EntitySet Name="R01RouteFullName_N" EntityType="Self.R01RouteFullName_N" />
          <EntitySet Name="sysdiagrams" EntityType="Self.sysdiagram" />
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="DrugsInteractionModelStoreContainer" CdmEntityContainer="DrugsInteractionEntities">
          <EntitySetMapping Name="CD2AHFS_N">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2AHFS_N">
              <MappingFragment StoreEntitySet="CD2AHFS_N">
                <ScalarProperty Name="AHFS_8_Code" ColumnName="AHFS_8_Code" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="AHFS_Clssfctn_Dscrptn" ColumnName="AHFS_Clssfctn_Dscrptn" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2ATTC_T">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2ATTC_T">
              <MappingFragment StoreEntitySet="CD2ATTC_T">
                <ScalarProperty Name="Attribute_Identifier" ColumnName="Attribute_Identifier" />
                <ScalarProperty Name="Attribute_Code" ColumnName="Attribute_Code" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Attribute_Code_Description" ColumnName="Attribute_Code_Description" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2ATTN_S">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2ATTN_S">
              <MappingFragment StoreEntitySet="CD2ATTN_S">
                <ScalarProperty Name="Attribute_Identifier" ColumnName="Attribute_Identifier" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Attribute_Name" ColumnName="Attribute_Name" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2DBN_I">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2DBN_I">
              <MappingFragment StoreEntitySet="CD2DBN_I">
                <ScalarProperty Name="Drug_Name_ID" ColumnName="Drug_Name_ID" />
                <ScalarProperty Name="Country_Code" ColumnName="Country_Code" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Brand_Name_Code" ColumnName="Brand_Name_Code" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2DDID_E">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2DDID_E">
              <MappingFragment StoreEntitySet="CD2DDID_E">
                <ScalarProperty Name="Drug_Descriptor_ID" ColumnName="Drug_Descriptor_ID" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Routed_Drug_ID" ColumnName="Routed_Drug_ID" />
                <ScalarProperty Name="Dosage_Form_ID" ColumnName="Dosage_Form_ID" />
                <ScalarProperty Name="Dispensable_Drug_Strength" ColumnName="Dispensable_Drug_Strength" />
                <ScalarProperty Name="Dispensable_Drug_Strength_UOM" ColumnName="Dispensable_Drug_Strength_UOM" />
                <ScalarProperty Name="Dispensable_Drug_Description" ColumnName="Dispensable_Drug_Description" />
                <ScalarProperty Name="New_Dispensable_Drug_ID" ColumnName="New_Dispensable_Drug_ID" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2DF_J">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2DF_J">
              <MappingFragment StoreEntitySet="CD2DF_J">
                <ScalarProperty Name="Dosage_Form_ID" ColumnName="Dosage_Form_ID" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Dosage_Form_Abbreviation" ColumnName="Dosage_Form_Abbreviation" />
                <ScalarProperty Name="Dosage_Form_15_Abbreviation" ColumnName="Dosage_Form_15_Abbreviation" />
                <ScalarProperty Name="Dosage_Form_Description" ColumnName="Dosage_Form_Description" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2DIN_O">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2DIN_O">
              <MappingFragment StoreEntitySet="CD2DIN_O">
                <ScalarProperty Name="External_Drug_Identifier" ColumnName="External_Drug_Identifier" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Knowledge_Base_Drug_Code" ColumnName="Knowledge_Base_Drug_Code" />
                <ScalarProperty Name="Dispensable_Drug_ID" ColumnName="Dispensable_Drug_ID" />
                <ScalarProperty Name="Generic_Product_Identifier" ColumnName="Generic_Product_Identifier" />
                <ScalarProperty Name="Inactive_Date" ColumnName="Inactive_Date" />
                <ScalarProperty Name="ID_Number_Type_Code" ColumnName="ID_Number_Type_Code" />
                <ScalarProperty Name="NAPRA_Code" ColumnName="NAPRA_Code" />
                <ScalarProperty Name="Schedule_Code" ColumnName="Schedule_Code" />
                <ScalarProperty Name="Generic_Product_Packaging_Code" ColumnName="Generic_Product_Packaging_Code" />
                <ScalarProperty Name="Alternate_Package_Size" ColumnName="Alternate_Package_Size" />
                <ScalarProperty Name="Alternate_Package_Size_UOM" ColumnName="Alternate_Package_Size_UOM" />
                <ScalarProperty Name="Metric_Strength" ColumnName="Metric_Strength" />
                <ScalarProperty Name="Mtrc_Strngth_Unt_of_Msr" ColumnName="Mtrc_Strngth_Unt_of_Msr" />
                <ScalarProperty Name="Labeler_Code" ColumnName="Labeler_Code" />
                <ScalarProperty Name="Old_External_Drug_Identifier" ColumnName="Old_External_Drug_Identifier" />
                <ScalarProperty Name="New_External_Drug_Identifier" ColumnName="New_External_Drug_Identifier" />
                <ScalarProperty Name="Third_Party_Restriction_Code" ColumnName="Third_Party_Restriction_Code" />
                <ScalarProperty Name="Maintenance_Code" ColumnName="Maintenance_Code" />
                <ScalarProperty Name="Form_Type_Code" ColumnName="Form_Type_Code" />
                <ScalarProperty Name="Internal_External_Code" ColumnName="Internal_External_Code" />
                <ScalarProperty Name="Single_Combination_Code" ColumnName="Single_Combination_Code" />
                <ScalarProperty Name="Storage_Condition_Code" ColumnName="Storage_Condition_Code" />
                <ScalarProperty Name="Limited_Stability_Code" ColumnName="Limited_Stability_Code" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2DN_H">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2DN_H">
              <MappingFragment StoreEntitySet="CD2DN_H">
                <ScalarProperty Name="Drug_Name_ID" ColumnName="Drug_Name_ID" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Drug_Name" ColumnName="Drug_Name" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2DXRF_F">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2DXRF_F">
              <MappingFragment StoreEntitySet="CD2DXRF_F">
                <ScalarProperty Name="Drug_Descriptor_ID" ColumnName="Drug_Descriptor_ID" />
                <ScalarProperty Name="Country_Code" ColumnName="Country_Code" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Generic_Product_Identifier" ColumnName="Generic_Product_Identifier" />
                <ScalarProperty Name="GPI_Assignment_Code" ColumnName="GPI_Assignment_Code" />
                <ScalarProperty Name="Knowledge_Base_Drug_Code" ColumnName="Knowledge_Base_Drug_Code" />
                <ScalarProperty Name="KDC_Assignment_Code" ColumnName="KDC_Assignment_Code" />
                <ScalarProperty Name="Single_Active_Ingredient_Indic" ColumnName="Single_Active_Ingredient_Indic" />
                <ScalarProperty Name="Device_Indicator" ColumnName="Device_Indicator" />
                <ScalarProperty Name="Historical_Indicator_Code" ColumnName="Historical_Indicator_Code" />
                <ScalarProperty Name="Obsolete_Date" ColumnName="Obsolete_Date" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2GPAH_M">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2GPAH_M">
              <MappingFragment StoreEntitySet="CD2GPAH_M">
                <ScalarProperty Name="Generic_Product_Identifier" ColumnName="Generic_Product_Identifier" />
                <ScalarProperty Name="Country_Code" ColumnName="Country_Code" />
                <ScalarProperty Name="AHFS_8_Code" ColumnName="AHFS_8_Code" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="AHFS_Limiter_Code" ColumnName="AHFS_Limiter_Code" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2GPDD_L">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2GPDD_L">
              <MappingFragment StoreEntitySet="CD2GPDD_L">
                <ScalarProperty Name="Generic_Product_Identifier" ColumnName="Generic_Product_Identifier" />
                <ScalarProperty Name="Country_Code" ColumnName="Country_Code" />
                <ScalarProperty Name="Drug_Descriptor_ID" ColumnName="Drug_Descriptor_ID" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2GPPC_Q">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2GPPC_Q">
              <MappingFragment StoreEntitySet="CD2GPPC_Q">
                <ScalarProperty Name="Generic_Product_Packaging_Code" ColumnName="Generic_Product_Packaging_Code" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Package_Size" ColumnName="Package_Size" />
                <ScalarProperty Name="Package_Size_Unit_of_Measure" ColumnName="Package_Size_Unit_of_Measure" />
                <ScalarProperty Name="Package_Quantity" ColumnName="Package_Quantity" />
                <ScalarProperty Name="Unit_DoseUnit_of_Use_Package" ColumnName="Unit_DoseUnit_of_Use_Package" />
                <ScalarProperty Name="Package_Description" ColumnName="Package_Description" />
                <ScalarProperty Name="Generic_Product_Identifier" ColumnName="Generic_Product_Identifier" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2LBL_P">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2LBL_P">
              <MappingFragment StoreEntitySet="CD2LBL_P">
                <ScalarProperty Name="Labeler_Code" ColumnName="Labeler_Code" />
                <ScalarProperty Name="Country_Code" ColumnName="Country_Code" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Manufacturers_Labeler_Name" ColumnName="Manufacturers_Labeler_Name" />
                <ScalarProperty Name="Manufacturers_Abbreviated_Name" ColumnName="Manufacturers_Abbreviated_Name" />
                <ScalarProperty Name="Parent_Labeler_Code" ColumnName="Parent_Labeler_Code" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2RT_K">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2RT_K">
              <MappingFragment StoreEntitySet="CD2RT_K">
                <ScalarProperty Name="Route_ID" ColumnName="Route_ID" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Route_Abbreviation" ColumnName="Route_Abbreviation" />
                <ScalarProperty Name="Route_15_Abbreviation" ColumnName="Route_15_Abbreviation" />
                <ScalarProperty Name="Route_Description" ColumnName="Route_Description" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2RTD_G">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2RTD_G">
              <MappingFragment StoreEntitySet="CD2RTD_G">
                <ScalarProperty Name="Routed_Drug_ID" ColumnName="Routed_Drug_ID" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="Drug_Name_ID" ColumnName="Drug_Name_ID" />
                <ScalarProperty Name="Route_ID" ColumnName="Route_ID" />
                <ScalarProperty Name="Routed_Drug_Description" ColumnName="Routed_Drug_Description" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2SUM_A">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2SUM_A">
              <MappingFragment StoreEntitySet="CD2SUM_A">
                <ScalarProperty Name="Record_Type" ColumnName="Record_Type" />
                <ScalarProperty Name="Reserve_1" ColumnName="Reserve_1" />
                <ScalarProperty Name="Sequence_Number" ColumnName="Sequence_Number" />
                <ScalarProperty Name="Reserve_2" ColumnName="Reserve_2" />
                <ScalarProperty Name="Comment_Marker" ColumnName="Comment_Marker" />
                <ScalarProperty Name="Data_or_Comment" ColumnName="Data_or_Comment" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2TCGPI_U">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2TCGPI_U">
              <MappingFragment StoreEntitySet="CD2TCGPI_U">
                <ScalarProperty Name="TC_GPI_Key" ColumnName="TC_GPI_Key" />
                <ScalarProperty Name="Country_Code" ColumnName="Country_Code" />
                <ScalarProperty Name="Record_Type" ColumnName="Record_Type" />
                <ScalarProperty Name="Transaction_Code" ColumnName="Transaction_Code" />
                <ScalarProperty Name="TC_GPI_Name" ColumnName="TC_GPI_Name" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CD2VAL_D">
            <EntityTypeMapping TypeName="DrugsInteractionModel.CD2VAL_D">
              <MappingFragment StoreEntitySet="CD2VAL_D">
                <ScalarProperty Name="Field_Identifier" ColumnName="Field_Identifier" />
                <ScalarProperty Name="Field_Value" ColumnName="Field_Value" />
                <ScalarProperty Name="Language_Code" ColumnName="Language_Code" />
                <ScalarProperty Name="Value_Description" ColumnName="Value_Description" />
                <ScalarProperty Name="Value_Abbreviation" ColumnName="Value_Abbreviation" />
                <ScalarProperty Name="Reserve" ColumnName="Reserve" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="D01Class_C">
            <EntityTypeMapping TypeName="DrugsInteractionModel.D01Class_C">
              <MappingFragment StoreEntitySet="D01Class_C">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="LINENUM" ColumnName="LINENUM" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="CLASSID" ColumnName="CLASSID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="D01ClassIngredient_D">
            <EntityTypeMapping TypeName="DrugsInteractionModel.D01ClassIngredient_D">
              <MappingFragment StoreEntitySet="D01ClassIngredient_D">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="CLASSID" ColumnName="CLASSID" />
                <ScalarProperty Name="LINENUM" ColumnName="LINENUM" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="INGREDIENTID" ColumnName="INGREDIENTID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="D02PARClass_E">
            <EntityTypeMapping TypeName="DrugsInteractionModel.D02PARClass_E">
              <MappingFragment StoreEntitySet="D02PARClass_E">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="LINENUM" ColumnName="LINENUM" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="CLASSID" ColumnName="CLASSID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="D02PARClassIngredient_F">
            <EntityTypeMapping TypeName="DrugsInteractionModel.D02PARClassIngredient_F">
              <MappingFragment StoreEntitySet="D02PARClassIngredient_F">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="CLASSID" ColumnName="CLASSID" />
                <ScalarProperty Name="LINENUM" ColumnName="LINENUM" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="INGREDIENTID" ColumnName="INGREDIENTID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DrugName2KDC_B">
            <EntityTypeMapping TypeName="DrugsInteractionModel.DrugName2KDC_B">
              <MappingFragment StoreEntitySet="DrugName2KDC_B">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="DrugName" ColumnName="DrugName" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="KDC2" ColumnName="KDC2" />
                <ScalarProperty Name="KDC3" ColumnName="KDC3" />
                <ScalarProperty Name="ACTCODE" ColumnName="ACTCODE" />
                <ScalarProperty Name="ROUTE" ColumnName="ROUTE" />
                <ScalarProperty Name="NeedName" ColumnName="NeedName" />
                <ScalarProperty Name="NameType" ColumnName="NameType" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DTMINAK_Q">
            <EntityTypeMapping TypeName="DrugsInteractionModel.DTMINAK_Q">
              <MappingFragment StoreEntitySet="DTMINAK_Q">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="RecordCode" ColumnName="RecordCode" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="KDC2" ColumnName="KDC2" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DTMLMIB_R">
            <EntityTypeMapping TypeName="DrugsInteractionModel.DTMLMIB_R">
              <MappingFragment StoreEntitySet="DTMLMIB_R">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="DrugInteractionID" ColumnName="DrugInteractionID" />
                <ScalarProperty Name="ExternalListID" ColumnName="ExternalListID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DTMMAP_O">
            <EntityTypeMapping TypeName="DrugsInteractionModel.DTMMAP_O">
              <MappingFragment StoreEntitySet="DTMMAP_O">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="OldPARClassNumber" ColumnName="OldPARClassNumber" />
                <ScalarProperty Name="NewPARClassNumber" ColumnName="NewPARClassNumber" />
                <ScalarProperty Name="DatePARReplaced" ColumnName="DatePARReplaced" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DTMNAME_S">
            <EntityTypeMapping TypeName="DrugsInteractionModel.DTMNAME_S">
              <MappingFragment StoreEntitySet="DTMNAME_S">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="ExternalListID" ColumnName="ExternalListID" />
                <ScalarProperty Name="ExternalListDesc" ColumnName="ExternalListDesc" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DTMOLD_P">
            <EntityTypeMapping TypeName="DrugsInteractionModel.DTMOLD_P">
              <MappingFragment StoreEntitySet="DTMOLD_P">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="OldPARClassNumber" ColumnName="OldPARClassNumber" />
                <ScalarProperty Name="OldPARClassDescription" ColumnName="OldPARClassDescription" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="M01InteractingDrugs_H">
            <EntityTypeMapping TypeName="DrugsInteractionModel.M01InteractingDrugs_H">
              <MappingFragment StoreEntitySet="M01InteractingDrugs_H">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="CLASSID1" ColumnName="CLASSID1" />
                <ScalarProperty Name="CLASSID2" ColumnName="CLASSID2" />
                <ScalarProperty Name="DrugInteractionID" ColumnName="DrugInteractionID" />
                <ScalarProperty Name="ClassIndicator" ColumnName="ClassIndicator" />
                <ScalarProperty Name="LineNumber" ColumnName="LineNumber" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="ReferenceIndicator" ColumnName="ReferenceIndicator" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="KDC2" ColumnName="KDC2" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="M01InteractionMainFile_G">
            <EntityTypeMapping TypeName="DrugsInteractionModel.M01InteractionMainFile_G">
              <MappingFragment StoreEntitySet="M01InteractionMainFile_G">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="CLASSID1" ColumnName="CLASSID1" />
                <ScalarProperty Name="CLASSID2" ColumnName="CLASSID2" />
                <ScalarProperty Name="DrugInteractionID" ColumnName="DrugInteractionID" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="DURATION1" ColumnName="DURATION1" />
                <ScalarProperty Name="SCHEDULE1" ColumnName="SCHEDULE1" />
                <ScalarProperty Name="Class1CharacterCount" ColumnName="Class1CharacterCount" />
                <ScalarProperty Name="InteractingDrugsCount1" ColumnName="InteractingDrugsCount1" />
                <ScalarProperty Name="DURATION2" ColumnName="DURATION2" />
                <ScalarProperty Name="SCHEDULE2" ColumnName="SCHEDULE2" />
                <ScalarProperty Name="Class2CharacterCount" ColumnName="Class2CharacterCount" />
                <ScalarProperty Name="InteractingDrugsCount2" ColumnName="InteractingDrugsCount2" />
                <ScalarProperty Name="OnsetCode" ColumnName="OnsetCode" />
                <ScalarProperty Name="SeverityCode" ColumnName="SeverityCode" />
                <ScalarProperty Name="DocumentationCode" ColumnName="DocumentationCode" />
                <ScalarProperty Name="ManagementCode" ColumnName="ManagementCode" />
                <ScalarProperty Name="ActivityCode1" ColumnName="ActivityCode1" />
                <ScalarProperty Name="ActivityCode2" ColumnName="ActivityCode2" />
                <ScalarProperty Name="ContraindicationsCode" ColumnName="ContraindicationsCode" />
                <ScalarProperty Name="WarningTextCharacterCount" ColumnName="WarningTextCharacterCount" />
                <ScalarProperty Name="EffectsTextCharacterCount" ColumnName="EffectsTextCharacterCount" />
                <ScalarProperty Name="MechanismTextCharacterCount" ColumnName="MechanismTextCharacterCount" />
                <ScalarProperty Name="ManagementTextCharacterCount" ColumnName="ManagementTextCharacterCount" />
                <ScalarProperty Name="DiscussionTextCharcterCount" ColumnName="DiscussionTextCharcterCount" />
                <ScalarProperty Name="ReferenceTextCharacterCount" ColumnName="ReferenceTextCharacterCount" />
                <ScalarProperty Name="InteractionType" ColumnName="InteractionType" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="M01InteractionMonographs_I">
            <EntityTypeMapping TypeName="DrugsInteractionModel.M01InteractionMonographs_I">
              <MappingFragment StoreEntitySet="M01InteractionMonographs_I">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="CLASSID1" ColumnName="CLASSID1" />
                <ScalarProperty Name="CLASSID2" ColumnName="CLASSID2" />
                <ScalarProperty Name="DrugInteractionID" ColumnName="DrugInteractionID" />
                <ScalarProperty Name="LineNumber" ColumnName="LineNumber" />
                <ScalarProperty Name="TextName" ColumnName="TextName" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="ActualText" ColumnName="ActualText" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="M02PARMainFile_J">
            <EntityTypeMapping TypeName="DrugsInteractionModel.M02PARMainFile_J">
              <MappingFragment StoreEntitySet="M02PARMainFile_J">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="CLASSID1" ColumnName="CLASSID1" />
                <ScalarProperty Name="CLASSID2" ColumnName="CLASSID2" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="Class1CharacterCount" ColumnName="Class1CharacterCount" />
                <ScalarProperty Name="Class2CharacterCount" ColumnName="Class2CharacterCount" />
                <ScalarProperty Name="PriorReactionHeaderCharCount" ColumnName="PriorReactionHeaderCharCount" />
                <ScalarProperty Name="ReactingDrugHeaderCharCount" ColumnName="ReactingDrugHeaderCharCount" />
                <ScalarProperty Name="DiscussionTextCharacterCount" ColumnName="DiscussionTextCharacterCount" />
                <ScalarProperty Name="ReferencesTextCharacterCount" ColumnName="ReferencesTextCharacterCount" />
                <ScalarProperty Name="ReactingDrugCount" ColumnName="ReactingDrugCount" />
                <ScalarProperty Name="Symptoms" ColumnName="Symptoms" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="M02PARMonographs_L">
            <EntityTypeMapping TypeName="DrugsInteractionModel.M02PARMonographs_L">
              <MappingFragment StoreEntitySet="M02PARMonographs_L">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="CLASSID1" ColumnName="CLASSID1" />
                <ScalarProperty Name="CLASSID2" ColumnName="CLASSID2" />
                <ScalarProperty Name="LineNumber" ColumnName="LineNumber" />
                <ScalarProperty Name="TextName" ColumnName="TextName" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="MonographText" ColumnName="MonographText" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="M02PARReactingDrugs_K">
            <EntityTypeMapping TypeName="DrugsInteractionModel.M02PARReactingDrugs_K">
              <MappingFragment StoreEntitySet="M02PARReactingDrugs_K">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="CLASSID1" ColumnName="CLASSID1" />
                <ScalarProperty Name="CLASSID2" ColumnName="CLASSID2" />
                <ScalarProperty Name="LineNumber" ColumnName="LineNumber" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="KDC2" ColumnName="KDC2" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="N01KDC2DrugName_M">
            <EntityTypeMapping TypeName="DrugsInteractionModel.N01KDC2DrugName_M">
              <MappingFragment StoreEntitySet="N01KDC2DrugName_M">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="KDC2" ColumnName="KDC2" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="DrugName" ColumnName="DrugName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="NDC2KDC_A">
            <EntityTypeMapping TypeName="DrugsInteractionModel.NDC2KDC_A">
              <MappingFragment StoreEntitySet="NDC2KDC_A">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="NDC" ColumnName="NDC" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="RSTYPE" ColumnName="RSTYPE" />
                <ScalarProperty Name="KDC1" ColumnName="KDC1" />
                <ScalarProperty Name="KDC2" ColumnName="KDC2" />
                <ScalarProperty Name="KDC3" ColumnName="KDC3" />
                <ScalarProperty Name="ACTCODE" ColumnName="ACTCODE" />
                <ScalarProperty Name="ROUTE" ColumnName="ROUTE" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="R01RouteFullName_N">
            <EntityTypeMapping TypeName="DrugsInteractionModel.R01RouteFullName_N">
              <MappingFragment StoreEntitySet="R01RouteFullName_N">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="Route" ColumnName="Route" />
                <ScalarProperty Name="RTYPE" ColumnName="RTYPE" />
                <ScalarProperty Name="ActivityCode" ColumnName="ActivityCode" />
                <ScalarProperty Name="RouteFullName" ColumnName="RouteFullName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="sysdiagrams">
            <EntityTypeMapping TypeName="DrugsInteractionModel.sysdiagram">
              <MappingFragment StoreEntitySet="sysdiagrams">
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="principal_id" ColumnName="principal_id" />
                <ScalarProperty Name="diagram_id" ColumnName="diagram_id" />
                <ScalarProperty Name="version" ColumnName="version" />
                <ScalarProperty Name="definition" ColumnName="definition" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="true" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>