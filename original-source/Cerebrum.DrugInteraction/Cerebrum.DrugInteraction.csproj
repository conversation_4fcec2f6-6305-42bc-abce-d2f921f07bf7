﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9A4D2849-1B71-49D0-B7F6-E0323A1254BD}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Cerebrum.DrugInteraction</RootNamespace>
    <AssemblyName>Cerebrum.DrugInteraction</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Uat|AnyCPU'">
    <OutputPath>bin\Uat\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging|AnyCPU'">
    <OutputPath>bin\Staging\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugCli|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugCli\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugSharedDb|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugSharedDb\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugCliSharedDb|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugCliSharedDb\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CD2AHFS_N.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2ATTC_T.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2ATTN_S.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2DBN_I.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2DDID_E.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2DF_J.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2DIN_O.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2DN_H.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2DXRF_F.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2GPAH_M.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2GPDD_L.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2GPPC_Q.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2LBL_P.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2RTD_G.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2RT_K.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2SUM_A.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2TCGPI_U.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="CD2VAL_D.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="DrugBLL.cs" />
    <Compile Include="D01ClassIngredient_D.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="D01Class_C.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="D02PARClassIngredient_F.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="D02PARClass_E.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="DrugInteraction.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DrugInteraction.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="DrugInteraction.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="DrugInteraction.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DrugInteraction.edmx</DependentUpon>
    </Compile>
    <Compile Include="DrugName2KDC_B.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="DTMINAK_Q.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="DTMLMIB_R.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="DTMMAP_O.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="DTMNAME_S.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="DTMOLD_P.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="M01InteractingDrugs_H.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="M01InteractionMainFile_G.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="M01InteractionMonographs_I.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="M02PARMainFile_J.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="M02PARMonographs_L.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="M02PARReactingDrugs_K.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="N01KDC2DrugName_M.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="NDC2KDC_A.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="R01RouteFullName_N.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
    <Compile Include="sysdiagram.cs">
      <DependentUpon>DrugInteraction.tt</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EntityDeploy Include="DrugInteraction.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>DrugInteraction.Designer.cs</LastGenOutput>
    </EntityDeploy>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.Config" />
    <None Include="DrugInteraction.edmx.diagram">
      <DependentUpon>DrugInteraction.edmx</DependentUpon>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="DrugInteraction.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>DrugInteraction.Context.cs</LastGenOutput>
      <DependentUpon>DrugInteraction.edmx</DependentUpon>
    </Content>
    <Content Include="DrugInteraction.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>DrugInteraction.edmx</DependentUpon>
      <LastGenOutput>DrugInteraction.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Cerebrum.Data\Cerebrum.Data.csproj">
      <Project>{EFD7CFF2-C670-499D-8123-A6692F4EF798}</Project>
      <Name>Cerebrum.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cerebrum.ViewModels\Cerebrum.ViewModels.csproj">
      <Project>{D211D7DE-696B-4779-A5C3-244DA5EB0C1B}</Project>
      <Name>Cerebrum.ViewModels</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>