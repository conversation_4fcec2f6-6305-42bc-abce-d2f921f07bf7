﻿using Cerebrum.BLL.User;
using Cerebrum.Data;
using Cerebrum.Labs.OLIS.services;
using Cerebrum.Labs.Service;
using System.ServiceProcess;
using System.IO;
using System.Diagnostics;
using System.Configuration.Install;
using System.Reflection;
using System;
using Cerebrum.BLL.Utility;
using AutoMapper;

namespace Cerebrum.Lab.Console
{
    class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>        
        protected static log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        static void Main(string[] argv)
        {
            Mapper.Initialize(c =>
            {
                Cerebrum.BLL.Startup.RegisterMapper(c);
                Cerebrum.Data.Startup.RegisterMapper(c);
                Cerebrum.ViewModels.Startup.RegisterMapper(c);
                Cerebrum.Labs.Startup.RegisterMapper(c);
                Labs.Startup.InitializeServices();
            });
            if (argv.Length == 0)
            {
                ServiceBase[] ServicesToRun;
                ServicesToRun = new ServiceBase[]
                {
                    new C3LabService()
                };
                ServiceBase.Run(ServicesToRun);
            }
            else
            {
                // install service
                string argv0 = argv[0].ToLower();
                switch (argv0)
                {
                    case "-i":
                        string name = "";
                        if (argv.Length > 1)
                            name = argv[1];
                        InstallService(name);
                        break;
                    case "-u":
                        string name1 = "";
                        if (argv.Length > 1)
                            name1 = argv[1];
                        UninstallService(name1);
                        break;
                    case "-t":
                        if (argv.Length > 1)
                        {
                            test(argv[1]);
                        }
                        else
                        {
                            test();
                        }
                        break;
                    default:
                        if (File.Exists(argv[0]))
                        {
                            test(argv[0]);
                        }
                        break;
                }
            }
        }
        private static void test(string v)
        {
            _log.Info("C3 Lab Service test is Ok");
        }

        private static void test()
        {

            Worker w = new Labs.Service.Worker();
            w.DoAjob();
        }
        private static void InstallService(string name)
        {
            ServiceParams.ServiceName = name;
            ManagedInstallerClass.InstallHelper(new string[] { Assembly.GetExecutingAssembly().Location });
        }
        private static void UninstallService(string name)
        {
            ServiceParams.ServiceName = name;
            ManagedInstallerClass.InstallHelper(new string[] { "/u", Assembly.GetExecutingAssembly().Location });
        }
    }
}
