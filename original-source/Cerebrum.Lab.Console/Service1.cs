﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Labs.Service
{
    partial class C3LabService : ServiceBase
    {
        protected Worker worker;

        public C3LabService()
        {
            InitializeComponent();
        }

        public void Test()
        {
            OnStart(null);
        }

        protected override void OnStart(string[] args)
        {
            worker = new Worker();
            worker.Start();
        }

        protected override void OnStop()
        {
            worker.Stop();
        }
    }
}
