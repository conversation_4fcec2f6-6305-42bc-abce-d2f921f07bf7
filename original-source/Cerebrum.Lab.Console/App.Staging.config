﻿<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

	<configSections xdt:Transform="Replace">
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" requirePermission="false" />
		<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
		<sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="Cerebrum.Lab.Console.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
		</sectionGroup>
	</configSections>

	<startup xdt:Transform="Replace">
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
	</startup>

	<appSettings xdt:Transform="Replace">
		<add key="env" value="staging" />
		<add key="HL7User" value="<EMAIL>" />
		<add key="HL7LabResultFiles" value="e:\HL7_Laboratory_Results" />
		<add key="HL7ProcessedLabResultFiles" value="e:\HL7_Laboratory_Results\Transferred" />
		<add key="LifeLabsAuditFilePath" value="e:\HL7_Laboratory_Results\LifeLabs\CURHST.0" />
		<add key="HL7ReportVersionsCountSettings" value="10" />
	</appSettings>

	<connectionStrings xdt:Transform="Replace">
		<add name="C3Context" connectionString="__C3Context__"
		  providerName="System.Data.SqlClient" />
		<add name="C3AuditContext" connectionString="__C3AuditContext__"
		  providerName="System.Data.SqlClient" />
	</connectionStrings>

	<log4net xdt:Transform="Replace">
		<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
			<file value="logs\log.txt" />
			<appendToFile value="true" />
			<rollingStyle value="Size" />
			<maxSizeRollBackups value="10" />
			<maximumFileSize value="2MB" />
			<staticLogFileName value="true" />
			<PreserveLogFileNameExtension value="true" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%date,[%thread],%-5level,[%M %C],%message%newline" />
			</layout>
		</appender>
		<root>
			<level value="ALL" />
			<appender-ref ref="RollingFileAppender" />
		</root>
	</log4net>

	<runtime xdt:Transform="Replace">
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>

	<applicationSettings xdt:Transform="Replace">
		<Cerebrum.Lab.Console.Properties.Settings>
			<setting name="Timeout" serializeAs="String">
				<value>200</value>
			</setting>
			<setting name="HL7User" serializeAs="String">
				<value><EMAIL></value>
			</setting>
			<setting name="OLISsendFolder" serializeAs="String">
				<value>E:\HL7_Laboratory_Results\OLIS\out\</value>
			</setting>
			<setting name="OLISgetFolder" serializeAs="String">
				<value>E:\HL7_Laboratory_Results\OLIS\in\</value>
			</setting>
			<setting name="HL7LabResultFiles" serializeAs="String">
				<value>E:\HL7_Laboratory_Results</value>
			</setting>
			<setting name="HL7ProcessedLabResultFiles" serializeAs="String">
				<value>E:\HL7_Laboratory_Results\Transferred\</value>
			</setting>
			<setting name="GammaGetFolder" serializeAs="String">
				<value>E:\HL7_Laboratory_Results\GammaDynaCare</value>
			</setting>
			<setting name="LLabsGetFolder" serializeAs="String">
				<value>E:\HL7_Laboratory_Results\LifeLabs</value>
			</setting>
			<setting name="OlisOn" serializeAs="String">
				<value>0</value>
			</setting>
		</Cerebrum.Lab.Console.Properties.Settings>
	</applicationSettings>
	
</configuration>