﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Cerebrum.Lab.Console.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="Timeout" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">200</Value>
    </Setting>
    <Setting Name="HL7User" Type="System.String" Scope="Application">
      <Value Profile="(Default)"><EMAIL></Value>
    </Setting>
    <Setting Name="OLISsendFolder" Type="System.String" Scope="Application">
      <Value Profile="(Default)">E:\C3\hl7\OLIS\in\</Value>
    </Setting>
    <Setting Name="OLISgetFolder" Type="System.String" Scope="Application">
      <Value Profile="(Default)">E:\C3\hl7\OLIS\out\</Value>
    </Setting>
    <Setting Name="HL7LabResultFiles" Type="System.String" Scope="Application">
      <Value Profile="(Default)">E:\C3\hl7\HL7_Laboratory_Results</Value>
    </Setting>
    <Setting Name="HL7ProcessedLabResultFiles" Type="System.String" Scope="Application">
      <Value Profile="(Default)">E:\C3\hl7\done\</Value>
    </Setting>
    <Setting Name="GammaGetFolder" Type="System.String" Scope="Application">
      <Value Profile="(Default)">E:\C3\hl7\HL7_Laboratory_Results\Gamma\</Value>
    </Setting>
    <Setting Name="LLabsGetFolder" Type="System.String" Scope="Application">
      <Value Profile="(Default)">E:\C3\hl7\HL7_Laboratory_Results\llabs\</Value>
    </Setting>
    <Setting Name="OlisOn" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">0</Value>
    </Setting>
  </Settings>
</SettingsFile>