﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration.Install;
using System.Linq;
using System.Threading.Tasks;

namespace Cerebrum.Lab.Console
{
    [RunInstaller(true)]
    public partial class ProjectInstaller : System.Configuration.Install.Installer
    {
        public ProjectInstaller()
        {
            InitializeComponent();
            setNames();
        }

        private void setNames()
        {
            if(!string.IsNullOrEmpty(ServiceParams.ServiceName))
            {
                C3LabServiceInstaller.ServiceName = ServiceParams.ServiceName;
                C3LabServiceInstaller.DisplayName = ServiceParams.ServiceName;
            }
        }

        private void C3LabServiceProcessInstaller_BeforeInstall(object sender, InstallEventArgs e)
        {
            setNames();
        }

        private void C3LabServiceProcessInstaller_BeforeUninstall(object sender, InstallEventArgs e)
        {
            setNames();
        }
    }
}
