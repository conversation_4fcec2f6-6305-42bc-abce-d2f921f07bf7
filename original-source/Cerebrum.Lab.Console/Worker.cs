﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Cerebrum.BLL.Common;
using Cerebrum.BLL.User;
using Cerebrum.Labs.OLIS.services;
using Cerebrum.ViewModels.Common;
using Cerebrum.ViewModels.OLIS;
//using Cerebrum.Labs.Service;
using Cerebrum.ViewModels.User;
using Cerebrum.BLL.Utility;
using System.Net.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Cerebrum.Labs.Service
{
    class Worker
    {
        private string newExamPath;
        private int    counter=0;
        private System.IO.FileSystemWatcher fileSystemWatcher1;
        private Thread m_thread = null;
        EventWaitHandle[] events;
        static log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        #region Service
        public Worker()
        {
            OnLoad();
        }
        public void Start()
        {
            events = new ManualResetEvent[] { new ManualResetEvent(false) };
            m_thread = new Thread(new ParameterizedThreadStart(Run));
            m_thread.Start();
        }
        public void Stop()
        {
            _log.Info("Stopping service");
            if(events == null)
                return;
            if(events.Length == 0)
                return;
            if(events[0] == null)
                return;
            events[0].Set();
        }
        public void Run(object data)
        {
            this.fileSystemWatcher1 = new System.IO.FileSystemWatcher();
            this.fileSystemWatcher1.Path = newExamPath;
            this.fileSystemWatcher1.EnableRaisingEvents = true;
            this.fileSystemWatcher1.IncludeSubdirectories = true;
            this.fileSystemWatcher1.NotifyFilter = System.IO.NotifyFilters.LastWrite;
            // this.fileSystemWatcher1.SynchronizingObject = this;
            this.fileSystemWatcher1.Changed += new System.IO.FileSystemEventHandler(this.fileSystemWatcher1_Changed);

            while(true)
            {
                int res = WaitHandle.WaitAny(events, Config.Timeout * 1000);
                switch(res)
                {
                case 0:         
                    _log.Info("Ending service loop");
                    return;
                case WaitHandle.WaitTimeout:
                    DoAjob();
                    break;
                }
            }
        }

        private void OnLoad()
        {
            newExamPath = Config.OlisGetPath;

            Info("look for:" + newExamPath);

            CheckDir(newExamPath);

            AddAll();
        }

        private void fileSystemWatcher1_Changed(object sender, System.IO.FileSystemEventArgs e)
        {
            AddAll();
        }
        private void AddAll()
        {
            if(Config.OlisOn != 1) return;

            string[] files = System.IO.Directory.GetFiles(newExamPath, "*.*", SearchOption.AllDirectories);
            foreach(string f in files)
            {
                Info("Adding file:" + f);
                try
                {
                    ProcessFile(f);
                }
                catch(Exception e)
                {
                    Info("Exception in AddAll");
                    Info(e.Message);
                    Info("Checking dir: " + newExamPath);
                    MoveFile(f, "");
                }
                counter++;
            }
        }

        private static void Info(string v)
        {
            _log.Info(v);
        }

        /// <summary>
        /// OLIS response processing
        /// </summary>
        /// <param name="name"></param>
        private void ProcessFile(string name)
        {
            if(!File.Exists(name))
            {
                return;
            }

            // check is file locked by other process
            for (int i = 0; i < 8; i++)
            {
                if (!IsFileLocked(name))
                    break;
                Thread.Sleep(2000);
            }

            // file still locked
            if (IsFileLocked(name))
                return;

            // Process OLIS message file
            OLISCommunication comm = new OLISCommunication(Config.OlisSendPath,Config.OlisGetPath);

            VMOLISReadResposeFile olisRsp = comm.ReadPollingResponse(name);

            bool isOk = olisRsp.isOK;

            int practiceDoctorId        = olisRsp.PracticeDoctorId;
            DateTime  pollingTime       = olisRsp.PollingDateTime;
            int       userid            = olisRsp.UserId;
            string    ipaddress         = olisRsp.IPAddress;
            bool      bSaveSuccessRun   = olisRsp.SaveSuccessRun;
            DateTime? lastSuccessRunDate= null;
            if(bSaveSuccessRun)
            {
                lastSuccessRunDate= pollingTime;
            }

            if(isOk)
            {
                SaveLastRunTime(
                    practiceDoctorId,
                    pollingTime,
                    userid,
                    ipaddress,
                    bSaveSuccessRun,
                    lastSuccessRunDate);
            }
        }

        private void SaveLastRunTime(
            int practiceDoctorId,
            DateTime pollingTime,
            int userid,
            string ipaddress,
            bool bSaveSuccessRun,
            DateTime? lastSuccessRunDate)
        {
            ExternalCommBLL extComm = new ExternalCommBLL();
            VMLastPoll vmLastPoll = new VMLastPoll()
            {
                UserId = userid,
                IpAddress = ipaddress,
                LastRunDate = pollingTime,
                PracticeDoctorId = practiceDoctorId,
                CommTypeCode = (int) CommType.OLIS,
                LastSuccessRunDate = (bSaveSuccessRun) ? lastSuccessRunDate : null,
                IsSuccessfulRun = (bSaveSuccessRun) ? true : false,
                PracticeCommTypeId = 0  /* not used for OLIS */
            };
            if(!extComm.InsertLastRunTime(vmLastPoll, false /* not used for OLIS */ ))
            {
                _log.Info($"Last query time for OLIS {practiceDoctorId} wasn't saved");
            }
        }

        private void MoveFile(string f, string outd)
        {
            Info("Save FAILED");

            string name = Path.Combine(outd, Path.GetFileName(f));

            try
            {
                File.Move(f, name);
            }
            catch
            {
                if(File.Exists(f))
                    File.Delete(f);
            }
        }

        public static void CheckDir(string dir)
        {
            if(!Directory.Exists(dir))
            {
                try
                {
                    Directory.CreateDirectory(dir);
                }
                catch(Exception e)
                {
                    System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                    string methodName = st.GetFrame(1).GetMethod().Name;

                    Info("Exception in " + methodName + "():");
                    Info("Ex: " + e.Message);
                    return;
                }
            }
        }

        #endregion Service

        #region Labs
        public void DoAjob()
        {
            var ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault(ipa => ipa.AddressFamily == AddressFamily.InterNetwork);

            var fus = new FileUploadService(ip.ToString(), Config.HL7User, Config.DropBox);
            int files = 0;
            if(!string.IsNullOrWhiteSpace(Config.GammaGetFolder))
            {
                files = fus.SaveGammaDynacareHL7(Config.GammaGetFolder);
                _log.Info($"{ip} gammaDynacare file(s) {files} processed.");
            }
            if(isStopping())
            {
                return;
            }
            if(!string.IsNullOrWhiteSpace(Config.LLabsGetFolder))
            {
                fus.DownloadLifeLabFilesAsync(Config.LLabsGetFolder).GetAwaiter().GetResult();
                files = fus.SaveLifeLabReports(Config.LLabsGetFolder);
                _log.Info($"{ip} Lifelabs file(s) {files} processed.");
            }

            if(isStopping())
            {
                return;
            }
            if(Config.OlisOn == 1)
            {
                SendOLISRequests();
            }
        }

        private bool isStopping()
        {
            if(events == null)
                return false;
            if(events.Length == 0)
                return false;
            if(events[0] == null)
                return false;
            return (events[0].WaitOne(0));  // return true when event 0 raised
        }

        private void SendOLISRequests()
        {
            try
            {
                OLISqueryProcessor p = new OLISqueryProcessor();
                IHttpClientFactory httpClientFactory = new ServiceCollection()
                    .AddHttpClient()
                    .BuildServiceProvider()
                    .GetService<IHttpClientFactory>();
                p.tryQuery(httpClientFactory);
            }
            catch(Exception x)
            {
                Lab.Console.LogClass.Error("Error generating OLIS auto-queries\n"+x.Message);
            }
        }

#endregion Labs
        protected virtual bool IsFileLocked(string name)
        {
            FileStream stream = null;

            try
            {
                stream = File.Open(name, FileMode.Open, FileAccess.Read, FileShare.None);
            }
            catch (IOException)
            {
                //the file is unavailable because it is:
                //still being written to
                //or being processed by another thread
                //or does not exist (has already been processed)
                return true;
            }
            finally
            {
                if (stream != null)
                    stream.Close();
            }

            //file is not locked
            return false;
        }
    }
}
