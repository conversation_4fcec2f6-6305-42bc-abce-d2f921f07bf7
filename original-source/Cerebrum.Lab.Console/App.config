﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
		<section name="Auth0" type="AwareServiceClient.Auth.Auth0.Auth0Config, AwareServiceClient" />
        <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" requirePermission="false" />
        <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Cerebrum.Lab.Console.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
	</startup>
	
  <appSettings>
    <add key="env" value="prod" />
    <add key="HL7User" value="<EMAIL>" />
    <add key="HL7LabResultFiles" value="E:\HL7_Laboratory_Results" />
    <add key="HL7ERRORLabResultFiles" value="E:\HL7_Laboratory_Results\HL7error" />
    <add key="HL7ProcessedLabResultFiles" value="E:\HL7_Laboratory_Results\Transferred" />
    <add key="LifeLabsAuditFilePath" value="E:\HL7_Laboratory_Results\LifeLabs\CURHST.0" />
    <add key="HL7ReportVersionsCountSettings" value="10" />
	<add key="AwareServicesUrl" value="http://services.localhost" />
	<add key="Cerebrum3AppData" value="C:\inetpub\wwwroot\" />
	<add key="Cerebrum3ClinicalViewerED" value="\labs\OLIS\ClinicalViewerED" />
  </appSettings>
	
  <connectionStrings>
	  <add name="C3Context" providerName="System.Data.SqlClient" connectionString="Server=C3AGL,60010;Database=Cer30;User ID=cerebrum_sa;Password=Restls6E6Y6a$;MultiSubnetFailover=True; MultipleActiveResultSets=true;Application Name=C3ProdIntegrat_HL7;" />
	  <add name="C3AuditContext" providerName="System.Data.SqlClient" connectionString="Server=C3AuditAGL,60030;Database=Cer30Audit;User ID=cerebrum_sa;Password=Restls6E6Y6a$;MultiSubnetFailover=True; MultipleActiveResultSets=true;Application Name=C3ProdIntegrat_HL7;" />
  </connectionStrings>
	
  <Auth0 ClientId="c8tVHmtQ21XC0z4B6x92dT7fNLss6qPQ" ClientSecret="****************************************************************" Tenant="dev-wma8g43orgki3wkm.us.auth0.com" ManagementApiAudience="https://dev-wma8g43orgki3wkm.us.auth0.com/api/v2/" AwareServicesApiAudience="aware-services-api" DefaultRoleId="rol_WCslWkPDEkAHtxrT" />
	
  <log4net>
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs\log.txt" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="100" />
      <maximumFileSize value="2MB" />
      <staticLogFileName value="true" />
      <PreserveLogFileNameExtension value="true" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date,[%thread],%-5level,[%M %C],%message%newline" />
      </layout>
    </appender>
    <root>
      <level value="ALL" />
      <appender-ref ref="RollingFileAppender" />
    </root>
  </log4net>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.1.0" newVersion="3.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
	
  <applicationSettings>
    <Cerebrum.Lab.Console.Properties.Settings>
      <setting name="Timeout" serializeAs="String">
        <value>200</value>
      </setting>
      <setting name="HL7User" serializeAs="String">
        <value><EMAIL></value>
      </setting>
      <setting name="OLISsendFolder" serializeAs="String">
        <value>E:\HL7_Laboratory_Results\OLIS\out\</value>
      </setting>
      <setting name="OLISgetFolder" serializeAs="String">
        <value>E:\HL7_Laboratory_Results\OLIS\in\</value>
      </setting>
      <setting name="HL7LabResultFiles" serializeAs="String">
        <value>E:\C3\hl7\HL7_Laboratory_Results</value>
      </setting>
      <setting name="HL7ProcessedLabResultFiles" serializeAs="String">
        <value>E:\HL7_Laboratory_Results\Transferred\</value>
      </setting>
      <setting name="GammaGetFolder" serializeAs="String">
        <value>E:\HL7_Laboratory_Results\GammaDynaCare</value>
      </setting>
      <setting name="LLabsGetFolder" serializeAs="String">
        <value>E:\HL7_Laboratory_Results\LifeLabs</value>
      </setting>
      <setting name="OlisOn" serializeAs="String">
        <value>0</value>
      </setting>
    </Cerebrum.Lab.Console.Properties.Settings>
  </applicationSettings>
	
</configuration>
