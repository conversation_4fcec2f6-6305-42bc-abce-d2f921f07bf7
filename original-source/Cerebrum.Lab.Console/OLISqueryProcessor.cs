﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using Cerebrum.BLL.Common;
using Cerebrum.BLL.User;
using Cerebrum.Lab.Console;
using Cerebrum.Labs.OLIS.services;
using Cerebrum.ViewModels.Common;
using Cerebrum.ViewModels.OLIS;
using Cerebrum.ViewModels.User;
using System.Net.Http;

namespace Cerebrum.Labs.Service
{
    internal class OLISqueryProcessor : CommScheduler
    {
        public OLISqueryProcessor()
        {
            ctype = CommunicationType.OLIS;
        }

        override protected bool ProcessPractice(IHttpClientFactory httpClientFactor, VMPracticePoll practice, DateTime lastPollingTime, DateTime pollingTime)
        {
            IPAddress ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault(ipa => ipa.AddressFamily == AddressFamily.InterNetwork);

            if(ip != null)
            {
                bool res = SendOLISRequest(practice.PracticeId, practice.PracticeDoctorId, ip.ToString(), lastPollingTime, pollingTime);
                if(res) // update last Success Run Date when Ok
                {
                    //practice.LastSuccessRunDate = pollingTime;
                    practice.LastRunDate = pollingTime;
                }
                return res;
            }
            LogClass.Error("Can't retrieve local IP-address");
            return false;
        }

        protected bool SendOLISRequest(int practiceID, int PracticeDoctorId, string ip, DateTime lastPollingTime, DateTime pollingTime)
        {
            bool result = true;
            log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

            var username = Config.HL7User;
            var bll = new UserBLL();
            var u = bll.GetUserByEmail(username);
            var user = new VMUser { UserId = u.UserId, FirstName = u.FirstName, LastName = u.LastName, IPAddress = ip };
            OLISBuildQuery _q = new OLISBuildQuery(Config.OlisSendPath,Config.OlisGetPath);

            VMOLISPractitioner doctor = _q.GetPracticeDoctor(practiceID, PracticeDoctorId);

            _log.Info("======================= OLIS QUERY ===============================");
            try
            {
                _log.Info($"OLIS Query for : {doctor.RequestingHIC} starting");
                //OLISQuery oq = new OLISQuery(z04, dt1, dt2, doctor);
                //oq.save()

                _q.SendZ04(user, doctor, lastPollingTime, pollingTime);
                _log.Info($"OLIS Query for : {doctor.RequestingHIC} Ended");
            }
            catch(Exception ex)
            {
                result = false;
                _log.Error(ex.ToString());
            }
            _log.Info("xxxxxxxxxxxxxxxxxxxxxx OLIS QUERY xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");

            return result;
        }
    }
}