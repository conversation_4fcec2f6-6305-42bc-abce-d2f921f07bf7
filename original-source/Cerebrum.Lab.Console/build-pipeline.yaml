trigger:
  branches:
    include:
      - dev
      - uat
      - staging
      - main
  paths:
    include:
      - /Cerebrum.Lab.Console
      - /Cerebrum.Lab

pr: none

pool:
  vmImage: 'windows-2022'

variables:
  solution: 'Cerebrum30.sln'
  buildPlatform: 'Any CPU'
  buildConfigurationRelease: 'Release'
  buildConfigurationDebug: 'DebugCli'
  buildConfigurationStaging: 'Staging'
  buildConfigurationUat: 'Uat'
  branch: 'feature'
  version: '0.0.0'
  counter: $[counter(variables['version'], 0)]

stages:
  - stage: Setup
    jobs:
      - job: pipeline_setup
        variables:
           myVar: initialValue
        steps:
          - task: PowerShell@2
            name: Set_Branch_Name
            displayName: "Checking branch name"
            inputs:
              targetType: 'inline'
              script: |
                $Branch = "$(Build.SourceBranch)" -replace "refs/heads/", ""
                echo $Branch
                
                Write-Host "##vso[task.setvariable variable=branch]$Branch"

                if ($Branch -eq 'dev') {
                  Write-Host 'dev branch detected.'
                  Write-Host "##vso[task.setvariable variable=branch]dev"
                }
                elseif ($Branch -eq 'uat') {
                  Write-Host 'uat branch detected.'
                  Write-Host "##vso[task.setvariable variable=branch]uat"
                }
                elseif ($Branch -eq 'staging') {
                  Write-Host 'staging branch detected.'
                  Write-Host "##vso[task.setvariable variable=branch]staging"
                }
                elseif ($Branch -eq 'main') {
                  Write-Host 'main branch detected.'
                  Write-Host "##vso[task.setvariable variable=branch]main"
                }
          - task: PowerShell@2
            name: clean_branch_name
            displayName: "Cleaning branch name"
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "Branch is $(branch)"
                $slashesRemoved = "$(branch)" -replace '/', '_'
                Write-Host "##vso[task.setvariable variable=branch;]$slashesRemoved"
                Write-Host "##vso[build.updatebuildnumber]$slashesRemoved.c$(counter)"

          - task: PowerShell@2
            inputs:
              targetType: 'inline'
              script: |
                $url = "https://download.microsoft.com/download/F/1/D/F1DEB8DB-D277-4EF9-9F48-3A65D4D8F965/NDP461-DevPack-KB3105179-ENU.exe"
                $outputPath = "$(Agent.TempDirectory)\NDP461-DevPack-KB3105179-ENU.exe"
                Invoke-WebRequest -Uri $url -OutFile $outputPath
                Start-Process -FilePath $outputPath -ArgumentList "/quiet", "/norestart" -Wait          

          - task: NuGetToolInstaller@0
            displayName: "User Nuget 6.4.0"
            inputs:
              versionSpec: '6.4.0'

          - task: PowerShell@2
            inputs:
              targetType: 'inline'
              script: |
                $url = "https://download.microsoft.com/download/F/1/D/F1DEB8DB-D277-4EF9-9F48-3A65D4D8F965/NDP461-DevPack-KB3105179-ENU.exe"
                $outputPath = "$(Agent.TempDirectory)\NDP461-DevPack-KB3105179-ENU.exe"
                Invoke-WebRequest -Uri $url -OutFile $outputPath
                Start-Process -FilePath $outputPath -ArgumentList "/quiet", "/norestart" -Wait          
          

          - task: NuGetCommand@2
            displayName: 'Restore Nuget Packages'
            inputs:
              restoreSolution: '$(solution)'
              vstsFeed: '9bd5d4ea-47f9-46d0-a856-e60a33cc9dbb/ef24df52-36f8-4f84-9ab9-4648d3068d7b'

          - task: VSBuild@1
            displayName: "Build Solution for feature"
            condition: and(ne(variables.branch, 'dev'), ne(variables.branch, 'uat'), ne(variables.branch, 'staging'), ne(variables.branch, 'main'))
            inputs:
              solution: $(solution)
              msbuildArgs: '/t:Cerebrum_Lab_Console /p:OutputPath="$(build.artifactstagingdirectory)/feature"'
              platform: $(buildPlatform)
              configuration: $(BuildConfigurationDebug)

          - task: VSBuild@1
            displayName: "Build Solution for DEV"
            condition: eq(variables.branch, 'dev')
            inputs:
              solution: $(solution)
              msbuildArgs: '/t:Cerebrum_Lab_Console /p:OutputPath="$(build.artifactstagingdirectory)/debug"'
              platform: $(buildPlatform)
              configuration: $(BuildConfigurationDebug)

          - task: VSBuild@1
            displayName: "Build Solution for UAT"
            condition: eq(variables.branch, 'uat')
            inputs:
              solution: $(solution)
              msbuildArgs: '/t:Cerebrum_Lab_Console /p:OutputPath="$(build.artifactstagingdirectory)/uat"'
              platform: $(buildPlatform)
              configuration: $(buildConfigurationUat)
              
          - task: VSBuild@1
            displayName: "Build Solution STAGING"
            condition: eq(variables.branch, 'staging')
            inputs:
              solution: $(solution)
              msbuildArgs: '/t:Cerebrum_Lab_Console /p:OutputPath="$(build.artifactstagingdirectory)/staging"'
              platform: $(buildPlatform)
              configuration: $(buildConfigurationStaging)

          - task: VSBuild@1
            displayName: "Build Solution for PROD"
            condition: eq(variables.branch, 'main')
            inputs:
              solution: $(solution)
              msbuildArgs: '/t:Cerebrum_Lab_Console /p:OutputPath="$(build.artifactstagingdirectory)/main"'
              platform: $(buildPlatform)
              configuration: $(buildConfigurationRelease)

          - task: PublishSymbols@2
            displayName: 'Publish symbols path'
            inputs:
              SearchPattern: '**\bin\**\*.pdb'
              PublishSymbols: false
            continueOnError: true
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact - feature'
            condition: and(ne(variables.branch, 'dev'), ne(variables.branch, 'uat'), ne(variables.branch, 'staging'), ne(variables.branch, 'main'))
            inputs:
              PathtoPublish: '$(build.artifactstagingdirectory)/feature'
              ArtifactName: 'drop_feature'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact - DEV'
            condition: eq(variables.branch, 'dev')
            inputs:
              PathtoPublish: '$(build.artifactstagingdirectory)/debug'
              ArtifactName: 'drop_debug'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact - UAT'
            condition: eq(variables.branch, 'uat')
            inputs:
              PathtoPublish: '$(build.artifactstagingdirectory)/uat'
              ArtifactName: 'drop_uat'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact - STAGING'
            condition: eq(variables.branch, 'staging')
            inputs:
              PathtoPublish: '$(build.artifactstagingdirectory)/staging'
              ArtifactName: 'drop_staging'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact - PROD'
            condition: eq(variables.branch, 'main')
            inputs:
              PathtoPublish: '$(build.artifactstagingdirectory)/main'
              ArtifactName: 'drop_main'