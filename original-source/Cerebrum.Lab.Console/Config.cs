﻿using System.Configuration;

namespace Cerebrum.Labs.Service
{
    public class Config
    {
        public static string OlisGetPath
        {
            get
            {
                //return global::ConfigurationManager.AppSettings["OLISgetFolder"].ToString();
                return Cerebrum.Lab.Console.Properties.Settings.Default.OLISgetFolder;
            }
        }
        public static string OlisSendPath
        {
            get
            {
                //return ConfigurationManager.AppSettings["OLISsendFolder"].ToString();
                return Cerebrum.Lab.Console.Properties.Settings.Default.OLISsendFolder;
            }
        }
        public static int Timeout
        {
            get
            {
                return Cerebrum.Lab.Console.Properties.Settings.Default.Timeout;
            }
        }
        public static string HL7ProcessedLabResultFiles
        {
            get
            {
                return Cerebrum.Lab.Console.Properties.Settings.Default.HL7ProcessedLabResultFiles;
            }
        }
        public static string GammaGetFolder
        {
            get
            {
                return Cerebrum.Lab.Console.Properties.Settings.Default.GammaGetFolder;
            }
        }
        public static string LLabsGetFolder
        {
            get
            {
                return Cerebrum.Lab.Console.Properties.Settings.Default.LLabsGetFolder;
            }
        }
        public static string HL7User
        {
            get
            {
                return Cerebrum.Lab.Console.Properties.Settings.Default.HL7User;
            }
        }
        public static string HL7ReportVersionsCountSettings
        {
            get
            {
                var value = ConfigurationManager.AppSettings["HL7ReportVersionsCountSettings"];
                if (value != null) return value.ToString();
                return "";
            }
        }
        public static string DropBox
        {
            get
            {
                var value = ConfigurationManager.AppSettings["OntarioMD_Test_Dropbox"];
                if (value != null) return value.ToString();
                return "";
            }
        }

        public static int OlisOn
        {
            get
            {
                return Cerebrum.Lab.Console.Properties.Settings.Default.OlisOn;
            }
        }

    }
}