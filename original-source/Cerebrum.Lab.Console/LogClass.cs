﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Lab.Console
{
    public class LogClass
    {
        private static log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static void Write(string s)
        {
            _log.Info(s);
        }

        public static void Error(string s)
        {
            _log.Error(s);
        }
    }
}
