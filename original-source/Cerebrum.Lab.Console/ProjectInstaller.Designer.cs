﻿namespace Cerebrum.Lab.Console
{
    partial class ProjectInstaller
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.C3LabServiceProcessInstaller = new System.ServiceProcess.ServiceProcessInstaller();
            this.C3LabServiceInstaller = new System.ServiceProcess.ServiceInstaller();
            // 
            // C3LabServiceProcessInstaller
            // 
            this.C3LabServiceProcessInstaller.Account = System.ServiceProcess.ServiceAccount.LocalSystem;
            this.C3LabServiceProcessInstaller.Password = null;
            this.C3LabServiceProcessInstaller.Username = null;
            this.C3LabServiceProcessInstaller.BeforeInstall += new System.Configuration.Install.InstallEventHandler(this.C3LabServiceProcessInstaller_BeforeInstall);
            this.C3LabServiceProcessInstaller.BeforeUninstall += new System.Configuration.Install.InstallEventHandler(this.C3LabServiceProcessInstaller_BeforeUninstall);
            // 
            // C3LabServiceInstaller
            // 
            this.C3LabServiceInstaller.DelayedAutoStart = true;
            this.C3LabServiceInstaller.ServiceName = "C3LabService";
            this.C3LabServiceInstaller.StartType = System.ServiceProcess.ServiceStartMode.Automatic;
            // 
            // ProjectInstaller
            // 
            this.Installers.AddRange(new System.Configuration.Install.Installer[] {
            this.C3LabServiceProcessInstaller,
            this.C3LabServiceInstaller});

        }

        #endregion

        private System.ServiceProcess.ServiceProcessInstaller C3LabServiceProcessInstaller;
        private System.ServiceProcess.ServiceInstaller C3LabServiceInstaller;
    }
}