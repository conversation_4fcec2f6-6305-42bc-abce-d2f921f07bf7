﻿using System;
using System.Configuration;
using System.IO;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Text.RegularExpressions;
using System.Xml;

namespace Cerebrum.BLL.Utility
{
    public class ResultParser
    {
        #region const
        private const string SAMLResponse_TXT = "SAMLResponse";
        private const string SAMLResponse_TXT2 = "SAMLResponse=";
        private const string VALUE_TXT = "VALUE=\"";
        private const string SUCCESS_STATUS_CODE = "urn:oasis:names:tc:SAML:2.0:status:Success";
        #endregion const

        #region props
        public string clientCertPath { get; set; } = ConfigurationManager.AppSettings["eConsultCertificate"];///@"E:\emr\doc\eConsult\AwareMD\86251072-combined.pfx";
        //public string psw { get; set; } = "Cerebrum123"; old
        public string psw { get; set; } = "123";

        public string DecryptedContent { get; private set; }
        public string ResponseContent { get; private set; }
        public string StatusCode { get; private set; }
        public bool IsValid { get; private set; }
        public string StatusMessage { get; private set; }
        public bool IsError { get; private set; } = false;
        #endregion props

        #region intr
        /// <summary>
        /// Retrieve and decode from UUE SAML response
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public string Retrieve(string text)
        {
            string uue = RetrieveValue(text);
            if (string.IsNullOrWhiteSpace(uue)) return string.Empty;

            return decode(uue);
        }
        /// <summary>
        /// Decrypt SAML response from 
        /// </summary>
        /// <param name="txt"></param>
        /// <returns></returns>
        public string RetrieveSamlResponse(string txt)
        {
            var text = WebUtility.UrlDecode(txt);   // un-normalize symbols
            int pos = text.IndexOf(SAMLResponse_TXT2);
            if (pos < 0) return "";

            pos += SAMLResponse_TXT2.Length;

            var uue = text.Substring(pos);

            var res = decode(uue);

            IsValid = Validate(res);

            Decrypt(res);

            return res;
        }
        /// <summary>
        /// Encoding BASE64 string
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static string decode(string text)
        {
            byte[] mybyte = System.Convert.FromBase64String(text);
            string returntext = System.Text.Encoding.UTF8.GetString(mybyte);
            return returntext;
        }
        #endregion intr

        #region impl
        private string RetrieveValue(string text)
        {
            int pos = text.IndexOf(SAMLResponse_TXT);
            if (pos <= 0) return "";
            pos = text.IndexOf(VALUE_TXT, pos + 1);
            if (pos <= 0) return "";

            pos += VALUE_TXT.Length;
            int pos2 = text.IndexOf('"', pos);
            if (pos2 <= 0) return "";

            return text.Substring(pos, pos2 - pos);
        }

        private void Decrypt(string res)
        {
            //EncryptedAssertion;
            DecryptAssertion da = new DecryptAssertion(res, clientCertPath, psw);

            da.Decrypt();
            DecryptedContent = da.DecryptedContent;
            ResponseContent = res;
        }

        private bool Validate(string SAMLResponse)
        {
            String decodedSaml = SAMLResponse;
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.PreserveWhitespace = false;
            xmlDoc.Load(new StringReader(decodedSaml));

            // check status
            StatusCode = GetStatus(xmlDoc);
            if (StatusCode != SUCCESS_STATUS_CODE)
            {
                // retrieve StatusMessage
                IsError = true;
                StatusMessage = GetStatusMessage(xmlDoc);
            }

            // CheckSignature
            bool samlValid = CheckSignature(xmlDoc);

            return samlValid;
        }

        private bool CheckSignature(XmlDocument xmlDoc)
        {
            XmlNamespaceManager nSpace = new XmlNamespaceManager(xmlDoc.NameTable);
            nSpace.AddNamespace("ds", SignedXml.XmlDsigNamespaceUrl);
            XmlNodeList nodeList = xmlDoc.GetElementsByTagName("dsig:Signature");
            XmlElement signNode = (XmlElement)nodeList[0];
            SamlSignedXml samlSignedXml = new SamlSignedXml((XmlElement)xmlDoc.DocumentElement, "id");
            samlSignedXml.LoadXml((XmlElement)signNode);

            // retrieve certificate from XML
            X509Certificate2 certificate = FindCertificate(samlSignedXml.KeyInfo);

            if (certificate == null) return false;

            bool samlValid = samlSignedXml.CheckSignature(certificate, true);

            return samlValid;
        }

        private string GetStatusMessage(XmlDocument xmlDoc)
        {
            XmlNodeList nodes = xmlDoc.GetElementsByTagName("samlp:StatusMessage");

            if (nodes == null || nodes.Count == 0) return "";
            if (nodes[0] == null) return "";

            return nodes[0].FirstChild.Value;
        }

        private string GetStatus(XmlDocument xmlDoc)
        {
            XmlNodeList nodes = xmlDoc.GetElementsByTagName("samlp:Status");

            if (nodes == null || nodes.Count == 0) return "";

            if (nodes[0].FirstChild == null) return "";
            if (nodes[0].FirstChild.Attributes == null) return "";
            if (nodes[0].FirstChild.FirstChild == null)
            {
                if (nodes[0].FirstChild.Attributes.Count < 1) return "";
                return nodes[0].FirstChild.Attributes[0].Value;
            }
            if (nodes[0].FirstChild.FirstChild.Attributes.Count < 1) return "";

            return nodes[0].FirstChild.FirstChild.Attributes[0].Value;
        }

        private X509Certificate2 FindCertificate(KeyInfo keyInfo)
        {
            X509Certificate2 dcert2 = null;
            foreach (KeyInfoClause clause in keyInfo)
            {
                if (clause is KeyInfoX509Data)
                {
                    if (((KeyInfoX509Data)clause).Certificates.Count > 0)
                    {
                        dcert2 = (X509Certificate2)((KeyInfoX509Data)clause).Certificates[0];
                        return dcert2;
                    }
                }
            }
            return null;
        }
        #endregion impl
    }
    public class SamlSignedXml : SignedXml
    {
        private string _referenceAttributeId = "";
        public SamlSignedXml(XmlElement element, string referenceAttributeId)
            : base(element)
        {
            _referenceAttributeId = referenceAttributeId;
        }
        public override XmlElement GetIdElement(XmlDocument document, string idValue)
        {
            var cond = string.Format("//*[@{0}='{1}']", _referenceAttributeId, idValue);
            var el = (XmlElement)document.SelectSingleNode(cond);
            if (el != null)
            {
                return el;
            }

            // just take top element
            var nodes = document.GetElementsByTagName("samlp:Response");
            if (nodes == null || nodes.Count == 0) return null;
            return (XmlElement)nodes[0];
        }
    }
}
