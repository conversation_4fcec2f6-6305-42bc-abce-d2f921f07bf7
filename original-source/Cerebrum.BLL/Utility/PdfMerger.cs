﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using Cerebrum.BLL.Utility;
using iTextSharp.text;
using iTextSharp.text.pdf;

namespace Cerebrum.BLL.Documents
{
    public class PdfMerger
    {
        public static string HeaderText = "";
        public static bool isNotPaging = false;

        //static Logger _log = new Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        protected static log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        // Merge pdf files.

        public static byte[] MergeFiles(List<byte[]> sourceFiles, string headerText)
        {
            iTextSharp.text.Document document = new iTextSharp.text.Document();
            MemoryStream output = new MemoryStream();
            try
            {
                // Initialize pdf writer
                PdfWriter writer = PdfWriter.GetInstance(document, output);
                writer.PageEvent = new PdfPageEvents();

                // Open document to write
                document.Open();
                PdfContentByte content = writer.DirectContent;

                // Iterate through all pdf documents
                for(int fileCounter = 0; fileCounter < sourceFiles.Count; fileCounter++)
                {
                    try
                    {
                        // Create pdf reader
                        PdfReader reader = new PdfReader(sourceFiles[fileCounter]);
                        int numberOfPages = reader.NumberOfPages;

                        // Iterate through all pages
                        for(int currentPageIndex = 1; currentPageIndex <= numberOfPages; currentPageIndex++)
                        {
                            if((sourceFiles.Count == 1) || (fileCounter == 1 && currentPageIndex == 1))
                            {
                                HeaderText = "";
                            }
                            else
                            {
                                HeaderText = headerText;
                            }
                            // Determine page size for the current page
                            document.SetPageSize(reader.GetPageSizeWithRotation(currentPageIndex));
                            // Create page
                            document.NewPage();
                            PdfImportedPage importedPage = writer.GetImportedPage(reader, currentPageIndex);
                            // Determine page orientation
                            int pageOrientation = reader.GetPageRotation(currentPageIndex);
                            if(pageOrientation == 270)
                            {
                                content.AddTemplate(importedPage, 0, 1f, -1f, 0, reader.GetPageSizeWithRotation(currentPageIndex).Width, 0);
                            }
                            else if(pageOrientation == 180)
                            {
                                content.AddTemplate(importedPage, -1f, 0, 0, -1f, reader.GetPageSizeWithRotation(currentPageIndex).Width, reader.GetPageSizeWithRotation(currentPageIndex).Height);
                            }
                            else if(pageOrientation == 90)
                            {
                                content.AddTemplate(importedPage, 0, -1f, 1f, 0, 0, reader.GetPageSizeWithRotation(currentPageIndex).Height);
                            }
                            else
                            {
                                content.AddTemplate(importedPage, 1f, 0, 0, 1f, 0, 0);
                            }
                        }
                    }
                    catch(Exception ex)
                    {
                        _log.Error($"{MethodBase.GetCurrentMethod().Name}()", ex);
                    }
                }
            }
            catch(Exception ex)
            {
                _log.Error($"{MethodBase.GetCurrentMethod().Name}()", ex);
            }
            finally
            {
                document?.Close();
            }
            HeaderText = "";
            return output?.GetBuffer();
        }

        //Merge pdf, jpg files for service
        public static byte[] MergeFilesForService(List<byte[]> sourceFiles, string headerText, int numPages)
        {
            HeaderText = headerText;
            iTextSharp.text.Document document = new iTextSharp.text.Document();
            MemoryStream output = new MemoryStream();
            //try
            //{
            try
            {
                // Initialize pdf writer
                PdfWriter writer = PdfWriter.GetInstance(document, output);
                writer.PageEvent = new PdfPageEvents();

                // Open document to write
                document.Open();
                PdfContentByte content = writer.DirectContent;

                // Iterate through all pdf documents
                for(int fileCounter = 0; fileCounter < sourceFiles.Count; fileCounter++)
                {
                    // Create pdf reader
                    try
                    {
                        PdfReader reader = new PdfReader(sourceFiles[fileCounter]);
                        int numberOfPages = reader.NumberOfPages;
                        // first file always full length
                        if(numPages > 0 && numPages <= numberOfPages && fileCounter>0)
                        {
                            numberOfPages = numPages;
                        }

                        // Iterate through all pages
                        for(int currentPageIndex = 1; currentPageIndex <= numberOfPages; currentPageIndex++)
                        {
                            if((sourceFiles.Count == 1) || (fileCounter == 1 && currentPageIndex == 1))
                            {
                                HeaderText = "";
                            }
                            else
                            {
                                HeaderText = headerText;
                            }
                            // Determine page size for the current page
                            document.SetPageSize(reader.GetPageSizeWithRotation(currentPageIndex));
                            // Create page
                            document.NewPage();
                            PdfImportedPage importedPage = writer.GetImportedPage(reader, currentPageIndex);
                            // Determine page orientation
                            int pageOrientation = reader.GetPageRotation(currentPageIndex);
                            if(pageOrientation == 270)
                            {
                                content.AddTemplate(importedPage, 0, 1f, -1f, 0, reader.GetPageSizeWithRotation(currentPageIndex).Width, 0);
                            }
                            else if(pageOrientation == 180)
                            {
                                content.AddTemplate(importedPage, -1f, 0, 0, -1f, reader.GetPageSizeWithRotation(currentPageIndex).Width, reader.GetPageSizeWithRotation(currentPageIndex).Height);
                            }
                            else if(pageOrientation == 90)
                            {
                                content.AddTemplate(importedPage, 0, -1f, 1f, 0, 0, reader.GetPageSizeWithRotation(currentPageIndex).Height);
                            }
                            else
                            {
                                content.AddTemplate(importedPage, 1f, 0, 0, 1f, 0, 0);
                            }
                        }
                    }
                    catch(Exception ex)
                    {
                        _log.Error($"{MethodBase.GetCurrentMethod().Name}()", ex);
                    }
                }
            }
            catch(Exception ex)
            {
                _log.Error($"{MethodBase.GetCurrentMethod().Name}()", ex);
            }
            //}////
            finally
            {
                document?.Close();
            }
            HeaderText = "";
            return output?.GetBuffer();
        }

        //adding only num pages from rawData pdf 
        internal static byte[] MergeFilesForServiceC3_ThenAddAnotherPages(byte[] byteArr, byte[] rawData, string v, int numPages)
        {
            byte[] retByts = null;
            byte[] onlyPages = GetOnlyNumberOfPages(rawData, "", numPages);

            if (onlyPages != null)
            {
                List<byte[]> mergeFiles = new List<byte[]>() { byteArr, onlyPages };
                retByts = MergeFilesForServiceC3(mergeFiles, "", 0);
            }

            return retByts;
        }

        //Merge pdf, jpg files modified for Cerebrum30 file merge with number of out pages -- gb --
        //numPages equals 0 or negative will merge all files, for the rest numPages
        public static byte[] MergeFilesForServiceC3(List<byte[]> sourceFiles, string headerText, int numPages, string officeName = "", PdfPTable pageHeader = null)
        {
            int numPages__ = 0;
            //when numPages = 0 add all pages
            if (numPages == 0 || numPages < 0)
            {
                numPages__ = 10000;
            }
            else
            {
                numPages__ = numPages;
            }
            int numberOfPages = 0;
            HeaderText = headerText;
            iTextSharp.text.Document document = new iTextSharp.text.Document();
            MemoryStream output = new MemoryStream();
            //try
            //{
            try
            {
                // Initialize pdf writer
                PdfWriter writer = PdfWriter.GetInstance(document, output);
                writer.PageEvent = new PdfPageEvents(officeName, pageHeader);

                // Open document to write
                document.Open();
                PdfContentByte content = writer.DirectContent;

                //numPages__ = numPages;
                // Iterate through all pdf documents
                for(int fileCounter = 0; fileCounter < sourceFiles.Count; fileCounter++)
                {
                    //stop adding pages when numPages__ negative
                    if(numPages__ < 0)
                    { continue; }

                    numberOfPages = 0;
                    // Create pdf reader
                    try
                    {
                        PdfReader reader = new PdfReader(sourceFiles[fileCounter]);
                        numberOfPages = reader.NumberOfPages;
                        // first file always full length
                        if(fileCounter > 0 && numPages__ > 0 && numPages__ <= numberOfPages)
                        {
                            numberOfPages = numPages__;
                        }

                        // Iterate through all pages
                        for(int currentPageIndex = 1; currentPageIndex <= numberOfPages; currentPageIndex++)
                        {
                            if((sourceFiles.Count == 1) || (fileCounter == 1 && currentPageIndex == 1))
                            {
                                HeaderText = "";
                            }
                            else
                            {
                                HeaderText = headerText;
                            }
                            // Determine page size for the current page
                            document.SetPageSize(reader.GetPageSizeWithRotation(currentPageIndex));
                            // Create page
                            document.NewPage();
                            PdfImportedPage importedPage = writer.GetImportedPage(reader, currentPageIndex);
                            // Determine page orientation
                            int pageOrientation = reader.GetPageRotation(currentPageIndex);
                            if(pageOrientation == 270)
                            {
                                content.AddTemplate(importedPage, 0, 1f, -1f, 0, reader.GetPageSizeWithRotation(currentPageIndex).Width, 0);
                            }
                            else if(pageOrientation == 180)
                            {
                                content.AddTemplate(importedPage, -1f, 0, 0, -1f, reader.GetPageSizeWithRotation(currentPageIndex).Width, reader.GetPageSizeWithRotation(currentPageIndex).Height);
                            }
                            else if(pageOrientation == 90)
                            {
                                content.AddTemplate(importedPage, 0, -1f, 1f, 0, 0, reader.GetPageSizeWithRotation(currentPageIndex).Height);
                            }
                            else
                            {
                                content.AddTemplate(importedPage, 1f, 0, 0, 1f, 0, 0);
                            }
                        }
                    }
                    catch(Exception ex)
                    {
                        _log.Error($"{MethodBase.GetCurrentMethod().Name}()", ex);
                    }

                    // Strange code
                    // numPages parameter doesn't mean total number of pages for output document
                    /*
                     change the number of pages required to merge after current file
                    if(numberOfPages > 0)
                    {
                        numPages__ = (numPages__ - numberOfPages) == 0 ? -1 : (numPages__ - numberOfPages);
                    }
                    */
                }
            }
            catch(Exception ex)
            {
                _log.Error($"{MethodBase.GetCurrentMethod().Name}()", ex);
            }
            //}////
            finally
            {
                document?.Close();
            }
            HeaderText = "";
            return output?.GetBuffer();
        }

        //Merge pdf's passed as a list of byte array 
        public static byte[] MergeFilesForServiceC3(List<byte[]> sourceFiles)
        {
            return MergeFilesForServiceC3(sourceFiles, "", 100000);
        }

        //Merge pdf, jpg files for service
        public static byte[] GetOnlyNumberOfPages(byte[] sourceFile, string headerText, int numPages)
        {

            if (sourceFile == null)
            { return null; }

            List<byte[]> sourseArr = new List<byte[]>() { sourceFile };
            isNotPaging = true;
            byte[] retBytes = null;
            try
            {
                retBytes = MergeFilesForService(sourseArr, headerText, numPages);
            }
            catch(Exception ex)
            {
                _log.Error($"{MethodBase.GetCurrentMethod().Name}()", ex);
            }
            finally
            {
                isNotPaging = false;
            }

            return retBytes;
        }
    }

    // Implements custom page events.
    internal class PdfPageEvents : IPdfPageEvent
    {
        #region members

        private BaseFont _baseFont = null;
        private PdfContentByte _content;
        string _officeName;
        private PdfPTable _pageHeader = null;

        #endregion

        public PdfPageEvents()
        {
            _pageHeader = null;
            _officeName = string.Empty;
        }
        public PdfPageEvents(string officeName, PdfPTable pageHeader)
        {
            _pageHeader = pageHeader;
            _officeName = officeName;
        }

        #region IPdfPageEvent Members

        public void OnOpenDocument(PdfWriter writer, iTextSharp.text.Document document)
        {
            _baseFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            _content = writer.DirectContent;
        }

        public void OnStartPage(PdfWriter writer, iTextSharp.text.Document document)
        {
        }

        public void OnEndPage(PdfWriter writer, iTextSharp.text.Document document)
        {
            // Write header text
            if (_pageHeader == null)
            {
                string headerText = PdfMerger.HeaderText;
                _content.BeginText();
                _content.SetFontAndSize(_baseFont, 8);
                _content.SetTextMatrix(GetCenterTextPosition(headerText, writer), writer.PageSize.Height - 10);
                _content.ShowText(headerText);
                _content.EndText();
            }
            else
            {
                if (!PdfMerger.isNotPaging && writer.PageNumber > 1)
                {
                    var cb = writer.DirectContent;
                    _pageHeader.WriteSelectedRows(0, -1, document.LeftMargin, writer.PageSize.Height - 10, cb);
                }
            }

            // Write footer text (page numbers)
            if (!PdfMerger.isNotPaging)
            {
                string text = string.IsNullOrWhiteSpace(_officeName) ? string.Empty : (_officeName + "        ") + "Page " + writer.PageNumber;
                _content.BeginText();
                _content.SetFontAndSize(_baseFont, 8);
                _content.SetTextMatrix(GetCenterTextPosition(text, writer), 10);
                _content.ShowText(text);
                _content.EndText();
            }
        }

        public void OnCloseDocument(PdfWriter writer, iTextSharp.text.Document document)
        {
        }

        public void OnParagraph(PdfWriter writer, iTextSharp.text.Document document, float paragraphPosition)
        {
        }

        public void OnParagraphEnd(PdfWriter writer, iTextSharp.text.Document document, float paragraphPosition)
        {
        }

        public void OnChapter(PdfWriter writer, iTextSharp.text.Document document, float paragraphPosition, iTextSharp.text.Paragraph title)
        {
        }

        public void OnChapterEnd(PdfWriter writer, iTextSharp.text.Document document, float paragraphPosition)
        {
        }

        public void OnSection(PdfWriter writer, iTextSharp.text.Document document, float paragraphPosition, int depth, iTextSharp.text.Paragraph title)
        {
        }

        public void OnSectionEnd(PdfWriter writer, iTextSharp.text.Document document, float paragraphPosition)
        {
        }

        public void OnGenericTag(PdfWriter writer, iTextSharp.text.Document document, iTextSharp.text.Rectangle rect, string text)
        {
        }

        #endregion

        private float GetCenterTextPosition(string text, PdfWriter writer)
        {
            return writer.PageSize.Width / 2 - _baseFont.GetWidthPoint(text, 8) / 2;
        }
    }
}
