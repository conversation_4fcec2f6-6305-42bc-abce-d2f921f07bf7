﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Xml;
using System.Xml.Serialization;
using SAML2;
using SAML2.Schema.Core;

namespace Cerebrum.BLL.Utility
{
    public class SignedAssertion
    {
        #region const
        const string uao_LIT = "urn:ehealth:names:idm:attribute:uao";                      // const
        const string uaoType_LIT = "urn:ehealth:names:idm:attribute:uaoType";                  // const
        const string grantByDelegateMeritOnly_LIT = "urn:ehealth:names:idm:attribute:grantByDelegateMeritOnly"; // const
        const string lastName_LIT = "urn:ehealth:names:idm:attribute:lastName";                 // attr
        const string firstName_LIT = "urn:ehealth:names:idm:attribute:firstName";                // attr
        const string rid_LIT = "urn:ehealth:names:idm:attribute:rid";                      // attr
        const string AuthenticationToken_LIT = "urn:ehealth:names:idm:attribute:AuthenticationToken";      // attr
        const string PrincipalFedKey_LIT = "urn:ehealth:names:idm:attribute:PrincipalFedKey";          // attr

        const string lastName_LLIT = "urn:ehealth:names:idm:attribute:lastname";
        const string firstName_LLIT = "urn:ehealth:names:idm:attribute:firstname";
        const string rid_LLIT = "urn:ehealth:names:idm:attribute:rid";
        const string AuthenticationToken_LLIT = "urn:ehealth:names:idm:attribute:authenticationtoken";
        const string PrincipalFedKey_LLIT = "urn:ehealth:names:idm:attribute:principalfedkey";
        const string IdentityProvider_LLIT = "urn:ehealth:names:idm:attribute:identityprovider";

        string IdentityProviderC3 = "2.16.840.1.113883.***********.1.41";
        const string template = "<ac:Identification xmlns:ac=\"urn:oasis:names:tc:SAML:2.0:ac\"><ac:Extension xmlns=\"urn:oasis:names:tc:SAML:2.0:ac\"><IdentityVerificationSchemeRef urn=\"AL2\" xmlns=\"urn:ehealth:names:idm:ac:extension\"/><UAOIdentityVerificationSchemeRef urn=\"AL2\" xmlns=\"urn:ehealth:names:idm:ac:extension\"/><DelegationManagementSchemeRef urn=\"AL2\" xmlns=\"urn:ehealth:names:idm:ac:extension\"/><CredentialManagementSchemeRef urn=\"AL2\" xmlns=\"urn:ehealth:names:idm:ac:extension\"/></ac:Extension></ac:Identification><ac:AuthnMethod xmlns=\"urn:oasis:names:tc:SAML:2.0:ac\" xmlns:ac=\"urn:oasis:names:tc:SAML:2.0:ac\"><ac:PrincipalAuthenticationMechanism><ac:Extension><PrimaryFactor type=\"password\" xmlns=\"urn:ehealth:names:idm:ac:extension\"/><CompensatingFactor xmlns=\"urn:ehealth:names:idm:ac:extension\" type=\"urn:ehealth:names:idm:compensatingFactor:extendedSessionToken\"></CompensatingFactor><ProtectedNetwork type=\"false\" xmlns=\"urn:ehealth:names:idm:ac:extension\"/></ac:Extension></ac:PrincipalAuthenticationMechanism><ac:Extension><IdentityProvider xmlns=\"urn:ehealth:names:idm:ac:extension\"><AuthenticatingAuthority xmlns=\"urn:oasis:names:tc:SAML:2.0:ac\">{0}</AuthenticatingAuthority></IdentityProvider><AssertingParty xmlns=\"urn:ehealth:names:idm:ac:extension\"><AuthenticatingAuthority xmlns=\"urn:oasis:names:tc:SAML:2.0:ac\">{1}</AuthenticatingAuthority></AssertingParty></ac:Extension></ac:AuthnMethod>";
        private readonly string UAOdata = "urn:ehealth:rid:upi:";
        private readonly string UAOtype = "org";    // Cerebrum

        #endregion

        #region props
        private string UPI = "160084815861";    // Cerebrum
        #endregion

        public string token2;
        public string SubjectNameId;
        public string UserLoginName;
        public string TransactionId;
        public string IssueInstant;

        private Saml20Assertion response;
        private string samlResponse;
        private X509Certificate2 x509Certificate2;
        private string FirstName;
        private string LastName;
        private string RID;
        private string AuthenticationToken;
        private string PrincipalFedKey;
        private string XML1FNAME = "f1.xml";
        private string XML2FNAME = "f2.xml";

        /// <summary>
        /// Generate signed token#2 using ONE ID SAML response
        /// </summary>
        /// <param name="text">ONE ID SAML decrypted response </param>
        public SignedAssertion(string text)
        {
            this.samlResponse = text;
            XML1FNAME = Path.GetTempFileName() + ".xml";
            XML2FNAME = Path.GetTempFileName() + "signed.xml";
        }

        internal void Create(string clientCertPath, string psw)
        {
            token2 = string.Empty;
            Import();
            if (response == null)
                return;
            Parse();
            x509Certificate2 = new X509Certificate2(clientCertPath, psw);
            Generate();
        }

        private void Parse()
        {
            var attributes = response.Assertion.Items.OfType<AttributeStatement>().FirstOrDefault();

            if (attributes == null) return;
            if (attributes.Items == null) return;

            var attrs = attributes.Items.OfType<SamlAttribute>();

            foreach (var attr in attrs)
            {
                switch (attr.Name.ToLower())
                {
                    case lastName_LLIT: LastName = attr.AttributeValue[0]; break;
                    case firstName_LLIT: FirstName = attr.AttributeValue[0]; break;
                    case rid_LLIT: RID = attr.AttributeValue[0]; break;
                    case AuthenticationToken_LLIT: AuthenticationToken = attr.AttributeValue[0]; break;
                    case PrincipalFedKey_LLIT: PrincipalFedKey = attr.AttributeValue[0]; break;
                    case IdentityProvider_LLIT: IdentityProviderC3 = attr.AttributeValue[0]; break;
                }
            }
        }

        private void Generate()
        {

            Assertion aa = GenAssertion();
            SignAssertion(aa);
        }

        private void SignAssertion(Assertion aa)
        {
            XmlSerializer responseSerializer = new XmlSerializer(aa.GetType());
            System.IO.FileStream file = System.IO.File.Create(XML1FNAME);

            responseSerializer.Serialize(file, aa);
            file.Close();

            // Adjust AuthnContextDecl - restore '<' and '>' symbols normalized by serializer
            AdjustAuthnContextDecl(XML1FNAME);

            SignVerifyEnvelope ve = new SignVerifyEnvelope();
            ve.ReferenceURI = aa.Id;
            ve.SignXmlFile(XML1FNAME, XML2FNAME, x509Certificate2);

            if (File.Exists(XML2FNAME))
            {
                token2 = File.ReadAllText(XML2FNAME);
            }
        }

        private void AdjustAuthnContextDecl(string xML1FNAME)
        {
            string text = File.ReadAllText(xML1FNAME);
            text = text.Replace("&lt;", "<");
            text = text.Replace("&gt;", ">");
            text = text.Replace("<AuthnContextDecl xsi:type=\"xsd:string\">", "<AuthnContextDecl>");
            text = text.Replace("<?xml version=\"1.0\"?>", "");
            File.WriteAllText(xML1FNAME, text);
        }

        private Assertion GenAssertion()
        {
            // commented by request
            // UAOdata = UPI;
            var assertion = new Assertion
            {
                Issuer = new NameId(),
                Id = Guid.NewGuid().ToString(),
                IssueInstant = DateTime.UtcNow,
                Version = "2.0"
            };

            assertion.Issuer.Value = x509Certificate2.Subject;

            NameId rnameid = response.Subject;

            NameId nameId = rnameid;
            nameId.NameQualifier = "https://federationbroker.ehealthontario.ca/fed/idp";
            //response.Subject.Value;
            SubjectNameId = nameId.Value;

            SamlAttribute attribute = response.Attributes.Where(at => at.Name == "urn:ehealth:names:idm:attribute:UserLoginName").FirstOrDefault();
            UserLoginName = attribute?.AttributeValue[0];

            TransactionId = assertion.Id;
            IssueInstant = assertion.IssueInstantString;
            // saml:Response/saml:Status/saml:StatusCode

            SubjectConfirmation subjectConfirmation = new SubjectConfirmation();
            subjectConfirmation.Method = "urn:oasis:names:tc:SAML:2.0:cm:sender-vouches";

            assertion.Subject = new Subject();

            assertion.Subject.Items = new object[] { nameId, subjectConfirmation };

            assertion.Conditions = new Conditions { NotBefore = DateTime.Now, NotOnOrAfter = DateTime.Now + new TimeSpan(0, 1, 0) };
            var audienceRestriction = new AudienceRestriction { Audience = new List<string>() { "http://www.ehealthontario.on.ca/UserRegistry" } };
            assertion.Conditions.Items = new List<ConditionAbstract>(new ConditionAbstract[] { audienceRestriction });

            string AuthnContextDeclText = GetAuthnContextDecl(IdentityProviderC3, x509Certificate2.Subject);

            AuthnStatement authnStatement;
            {
                authnStatement = new AuthnStatement();
                authnStatement.AuthnInstant = DateTime.Now;
                authnStatement.SubjectLocality = new SubjectLocality();
                authnStatement.SubjectLocality.Address = "*******";

                assertion.Items = new StatementAbstract[] { authnStatement };

                authnStatement.AuthnContext = new AuthnContext
                {
                    Items = new object[] { AuthnContextDeclText },
                    ItemsElementName = new[]{
                        AuthnContextType.AuthnContextDecl
                    }
                };
            }

            AttributeStatement attributeStatement;
            {
                attributeStatement = new AttributeStatement();
                var lastName = new SamlAttribute { Name = lastName_LIT, NameFormat = SamlAttribute.NameformatUri, AttributeValue = new[] { LastName } };
                var firstName = new SamlAttribute { Name = firstName_LIT, NameFormat = SamlAttribute.NameformatUri, AttributeValue = new[] { FirstName } };
                var UAO = new SamlAttribute { Name = uao_LIT, NameFormat = SamlAttribute.NameformatUri, AttributeValue = new[] { UAOdata + UPI } };
                var uaoType = new SamlAttribute { Name = uaoType_LIT, NameFormat = SamlAttribute.NameformatUri, AttributeValue = new[] { UAOtype } };
                //var rid = new SamlAttribute { Name = rid_LIT, NameFormat = SamlAttribute.NameformatUri, AttributeValue = new[] { RID } };
                var grantByDelegateMeritOnly = new SamlAttribute { Name = grantByDelegateMeritOnly_LIT, NameFormat = SamlAttribute.NameformatUri, AttributeValue = new[] { "false" } };

                var authenticationToken = new SamlAttribute { Name = AuthenticationToken_LIT, NameFormat = SamlAttribute.NameformatUri, AttributeValue = new[] { AuthenticationToken } };
                var principalFedKey = new SamlAttribute { Name = PrincipalFedKey_LIT, NameFormat = SamlAttribute.NameformatUri, AttributeValue = new[] { PrincipalFedKey } };

                attributeStatement.Items = new object[] {
                    lastName
                    ,firstName
                    ,UAO
                    ,uaoType
                    //,rid
                    ,grantByDelegateMeritOnly
                    ,authenticationToken
                    ,principalFedKey
                };
            }

            assertion.Items = new StatementAbstract[] { authnStatement, attributeStatement };

            return assertion;
        }

        private string GetAuthnContextDecl(string identityProviderC3, string subject)
        {

            string res = string.Format(template, identityProviderC3, subject);
            return res;
            //"<ac:Identification xmlns:ac=\"urn: oasis: names: tc: SAML: 2.0:ac\">
            //< ac:Extension xmlns=\"urn:oasis:names:tc:SAML:2.0:ac\">
            //<IdentityVerificationSchemeRef urn = \"AL2\" xmlns = \"urn:ehealth:names:idm:ac:extension\" />
            //< UAOIdentityVerificationSchemeRef urn = \"AL2\" xmlns = \"urn:ehealth:names:idm:ac:extension\" />
            //< DelegationManagementSchemeRef urn = \"AL2\" xmlns = \"urn:ehealth:names:idm:ac:extension\" />
            //< CredentialManagementSchemeRef urn = \"AL2\" xmlns = \"urn:ehealth:names:idm:ac:extension\" />
            //</ ac:Extension >
            //</ ac:Identification >
            //< ac:AuthnMethod xmlns=\"urn:oasis:names:tc:SAML:2.0:ac\" xmlns: ac = \"urn:oasis:names:tc:SAML:2.0:ac\" >
            //< ac:PrincipalAuthenticationMechanism >
            //< ac:Extension >
            //< PrimaryFactor type = \"password\" xmlns = \"urn:ehealth:names:idm:ac:extension\" />
            //< CompensatingFactor type = \"urn:ehealth:names:idm:compensatingFactor:extendedSessionToken\" xmlns = \"urn:ehealth:names:idm:ac:extension\" />
            //< ProtectedNetwork type = \"false\" xmlns = \"urn:ehealth:names:idm:ac:extension\" />
            //</ ac:Extension >
            //</ ac:PrincipalAuthenticationMechanism >
            //< ac:Extension >
            //< IdentityProvider xmlns = \"urn:ehealth:names:idm:ac:extension\" >
            //< AuthenticatingAuthority xmlns = \"urn:oasis:names:tc:SAML:2.0:ac\" >{1}</ AuthenticatingAuthority >
            //</ IdentityProvider >
            //< AssertingParty xmlns = \"urn:ehealth:names:idm:ac:extension\" >
            //< AuthenticatingAuthority xmlns = \"urn:oasis:names:tc:SAML:2.0:ac\" >{2}</ AuthenticatingAuthority >
            //</ AssertingParty >
            //</ ac:Extension >
            //</ ac:AuthnMethod >";
        }

        private void Import()
        {
            try
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(samlResponse);

                response = new Saml20Assertion(doc.DocumentElement, null, false, null);

                //SamlNameId = response.
            }
            catch (Exception x)
            {
                Log("Deserialize() Error:" + x.Message);
            }
        }
        private void Log(string v)
        {
            System.Diagnostics.Debug.WriteLine(v);
        }
    }
}
