﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using SAMLIdentityProvider.Library.Schema;
using System.Net.Http;
using System.Net;
using System.Collections.Generic;
using System.Security.Authentication;
using System.Net.Http.Headers;
using System.Configuration;
using AwareMD.Cerebrum.Shared.Enums;
using Microsoft.Office.Interop.Word;
using Task = System.Threading.Tasks.Task;

namespace Cerebrum.BLL.Utility
{
    public class SignVerifyEnvelope
    {
        readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private readonly string tempFolder = ConfigurationManager.AppSettings["eConsultTemp"];
        private readonly string prefix = "";
        private string SIGNED_FNAME = "SignedExample.xml"; // @"E:\emr\Tests\SignVerify\test\test.xml"
        private string FNAME = "Example.xml";
        // made public
        public String ReferenceURI; // = "dff71bf7-4f21-46b6-b049-fce22d48e07a";

        public string Certificate { get; set; } = ConfigurationManager.AppSettings["eConsultCertificate"];//@"C:\Certificate\86251072-combined.pfx";
        //public string Psw { get; set; } = "Cerebrum123";
        public string Psw { get; set; } = "123";
        public string DestURL { get; set; } = ConfigurationManager.AppSettings["DestURL"];// "https://federationbroker.ehealthontario.ca/fed/idp/samlv20"; //
        public string IssuerURL { get; set; } = ConfigurationManager.AppSettings["IssuerURL"];//"cerebrum.mycerebrum.com"; //

        public string SamlSentToFedBrokerDecoded { get; set; }
        public string AssertionConsumerServiceURL { get; set; }

        //protected static Logger _log = new Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public string GenerateSamlEncoded()
        {
            string base64EncodedRequest = string.Empty;
            string xml = GenerateSamlXml();

            //_log.Econsult("xml before Convert.ToBase64String:" + Environment.NewLine + xml);
            //UtilityHelper.WriteEconsultLog("xml before Convert.ToBase64String:" + Environment.NewLine + xml);
            SamlSentToFedBrokerDecoded = xml;

            if (!string.IsNullOrWhiteSpace(xml))
            {
                base64EncodedRequest = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(xml));
            }

            return base64EncodedRequest;
        }

        public string GenerateSamlXml()
        {
            UtilityHelper.WriteEconsultLog("if exists tempFolder: " + tempFolder);
            if (!Directory.Exists(tempFolder))
            {
                // do not need to check if exists
                Directory.CreateDirectory(tempFolder);
            }
            //FNAME = Path.GetTempFileName();
            //SIGNED_FNAME = Path.GetTempFileName() + "signed.xml";
            FNAME = tempFolder + Guid.NewGuid().ToString();//@"E:\econsult.mycerebrum.com\Certificate\" + Guid.NewGuid().ToString();
            SIGNED_FNAME = tempFolder + Guid.NewGuid().ToString() + "signed.xml"; //@"E:\econsult.mycerebrum.com\Certificate\" + Guid.NewGuid().ToString() + "signed.xml";
            FNAME += ".xml";
            string result = string.Empty;


            try
            {
                // Generate a signing key.

                ReferenceURI = Guid.NewGuid().ToString();

                UtilityHelper.WriteEconsultLog("FNAME: " + FNAME);
                // Create an XML file to sign.
                CreateSAMLXml(FNAME);
                Console.WriteLine("New XML file created.");

                // Sign the XML that was just created and save it in a 
                // new file.
                // SignXmlFile( "Example.xml", SIGNED_FNAME, Key );
                UtilityHelper.WriteEconsultLog("Certificate: " + Certificate);
                var c2 = new X509Certificate2(Certificate, Psw);
                SignXmlFile(FNAME, SIGNED_FNAME, c2);
                Console.WriteLine("XML file signed.");

                try
                {
                    UtilityHelper.WriteEconsultLog("SIGNED_FNAME: " + SIGNED_FNAME);
                    result = File.ReadAllText(SIGNED_FNAME);
                    UtilityHelper.WriteEconsultLog("result: " + result);
                }
                catch (Exception x) { }

#if true
                try
                {


                    // Verify the signature of the signed XML.
                    // verify created XML
                    Console.WriteLine("Verifying signature...");
                    bool result2 = VerifyXmlFile(SIGNED_FNAME);

                    // Display the results of the signature verification to \
                    // the console.
                    if (result2)
                    {
                        Console.WriteLine("The XML signature is valid.");
                    }
                    else
                    {
                        Console.WriteLine("The XML signature is not valid.");
                    }
                }
                catch { }
#endif
            }
            catch (CryptographicException e)
            {
                // set "Load User Profile" to True
                Console.WriteLine(e.Message);
                UtilityHelper.WriteEconsultError(e);
                _log.Error("Method CryptographicException GenerateSamlXml()", e);
            }
            catch (Exception e)
            {
                UtilityHelper.WriteEconsultError(e);
                _log.Error("Method GenerateSamlXml()", e);
            }

#if !debug
            //TryDeleteFile(FNAME);
            //TryDeleteFile(SIGNED_FNAME);
#endif
            return result;
        }

        private static void TryDeleteFile(string nm)
        {
            if (File.Exists(nm))
            {
                DeleteFile(nm);
            }
        }
        private static void DeleteFile(string nm)
        {
            try
            {
                File.Delete(nm);
            }
            catch (Exception x) { }
        }

        public void SignXmlFile(string FileName, string SignedFileName, X509Certificate2 cert)
        {
            // Create a new XML document.
            XmlDocument doc = new XmlDocument();

            // Format the document to ignore white spaces.
            doc.PreserveWhitespace = false;

            // Load the passed XML file using it's name.
            doc.Load(new XmlTextReader(FileName));

            // Create a SignedXml object.
            MySignedXml signedXml = new MySignedXml(doc);
            //SignedXmlWithId signedXml = new SignedXmlWithId(doc);

            // Add the key to the SignedXml document. 
            signedXml.SigningKey = cert.PrivateKey;
            signedXml.SignedInfo.CanonicalizationMethod = SignedXml.XmlDsigExcC14NTransformUrl;

            // Create a reference to be signed.
            Reference reference = new Reference();
            reference.Uri = "";
            reference.Uri = "#" + ReferenceURI;

            // Add an enveloped transformation to the reference.
            XmlDsigEnvelopedSignatureTransform env = new XmlDsigEnvelopedSignatureTransform();
            reference.AddTransform(env);
            reference.AddTransform(new XmlDsigExcC14NTransform());

            // Add the reference to the SignedXml object.
            signedXml.AddReference(reference);

            // Add an RSAKeyValue KeyInfo (optional; helps recipient find key to validate).
            KeyInfo keyInfo = new KeyInfo();
            keyInfo.AddClause(new KeyInfoX509Data(cert));
            signedXml.KeyInfo = keyInfo;

            // Compute the signature.
            signedXml.ComputeSignature();

            // Get the XML representation of the signature and save
            // it to an XmlElement object.
            XmlElement xmlDigitalSignature = signedXml.GetXml();

            // Append the element to the XML document.
            doc.DocumentElement.AppendChild(doc.ImportNode(xmlDigitalSignature, true));

            if (doc.FirstChild is XmlDeclaration)
            {
                doc.RemoveChild(doc.FirstChild);
            }

            // Save the signed XML document to a file specified
            // using the passed string.
            XmlTextWriter xmltw = new XmlTextWriter(SignedFileName, new UTF8Encoding(false));
            doc.WriteTo(xmltw);
            xmltw.Close();
        }
        // Verify the signature of an XML file and return the result.
        public Boolean VerifyXmlFile(String Name)
        {
            // Create a new XML document.
            XmlDocument xmlDocument = new XmlDocument();

            // Format using white spaces.
            xmlDocument.PreserveWhitespace = true;

            // Load the passed XML file into the document. 
            xmlDocument.Load(Name);

            // Create a new SignedXml object and pass it
            // the XML document class.
            SignedXml signedXml = new SignedXml(xmlDocument);

            // Find the "Signature" node and create a new
            // XmlNodeList object.
            XmlNodeList nodeList = xmlDocument.GetElementsByTagName(prefix + "Signature");

            // Load the signature node.
            signedXml.LoadXml((XmlElement)nodeList[0]);

            // Check the signature and return the result.
            return signedXml.CheckSignature();
        }

        // Create example data to sign.
        public void CreateSAMLXml(string FileName)
        {
            // try for test only!!!
            try
            {
                AuthnRequestType samlRequest = new AuthnRequestType()
                {
                    ForceAuthn = false,
                    Destination = DestURL,
                    ID = ReferenceURI,
                    IsPassive = false,
                    IsPassiveSpecified = true,
                    IssueInstant = DateTime.UtcNow,
                    Version = "2.0",
                    Issuer = new NameIDType
                    {
                        Value = IssuerURL,
                        Format = "urn:oasis:names:tc:SAML:2.0:nameid-format:entity"
                    },
                    RequestedAuthnContext = new SAMLIdentityProvider.Library.Schema.RequestedAuthnContextType
                    {
                        Comparison = AuthnContextComparisonType.exact
                    },
                    NameIDPolicy = new NameIDPolicyType
                    {
                        AllowCreate = true,
                        AllowCreateSpecified = true,
                        Format = "urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified"
                    }
                ,
                    AssertionConsumerServiceURL = AssertionConsumerServiceURL//ConfigurationManager.AppSettings["AssertionConsumerServiceURL"] //"https://econsult.mycerebrum.com/api/OneIdToken/" //
                ,
                    ProtocolBinding = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                };
                samlRequest.RequestedAuthnContext.Items = new string[] { "urn:oasis:names:tc:SAML:2.0:ac:classes:PasswordProtectedTransport" };
                samlRequest.RequestedAuthnContext.ItemsElementName = new ItemsChoiceType7[] { ItemsChoiceType7.AuthnContextClassRef };


                XmlSerializer responseSerializer = new XmlSerializer(samlRequest.GetType());
                System.IO.FileStream file = System.IO.File.Create(FileName);

                responseSerializer.Serialize(file, samlRequest);
                file.Close();

            }
            catch (Exception ex)
            {
                UtilityHelper.WriteEconsultError(ex);
            }
        }
        public class MySignedXml : SignedXml
        {
            public MySignedXml(XmlDocument document) : base(document) { }
            public MySignedXml(XmlElement document) : base(document) { }
            public override XmlElement GetIdElement(
                XmlDocument document, string idValue)
            {
                var res = (XmlElement)document.SelectSingleNode("//*[@ID='" + idValue + "']");
                if (res == null)
                {
                    //            res = (XmlElement)document.SelectSingleNode( "//*[@wsu:Id=\"" + idValue + "\"]" );
                    res = (XmlElement)document.LastChild;
                }
                return res;
            }
        }

        ///
        ///
        ///
        public class FhirSender
        {
            private string postData;
            private string url;

            public object StatusCode { get; private set; }
            public int StatusCodeInt { get; private set; }
            public string ReasonPhrase { get; private set; }
            public string ResultStr { get; private set; }

            public FhirSender()
            {
            }

            public Task Send(string url, string postData)
            {
                this.url = url;
                this.postData = postData;

                return Task.Run(() => Send());
            }

            private async Task Send()
            {
                if (string.IsNullOrWhiteSpace(url)) return;
                if (string.IsNullOrWhiteSpace(postData)) return;

                var content = new MultipartFormDataContent();

                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

                ServicePointManager.ServerCertificateValidationCallback =
                    (a, b, c, d) => true;

                var dict = new Dictionary<string, string>();
                dict.Add("SAMLRequest", postData);

                //var client = new HttpClient();
                //var res = await client.SendAsync(req);

                //content.Add( new StreamContent( new MemoryStream( Encoding.ASCII.GetBytes( "SAMLRequest=" + postData ) ) ) );

                var handler = new HttpClientHandler();
                handler.ClientCertificateOptions = ClientCertificateOption.Manual;
                //handler.SslProtocols = SslProtocols.Tls12;
                var client = new HttpClient(handler);
                client.MaxResponseContentBufferSize = 256000;
                client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0)");
                client.DefaultRequestHeaders.Add("Connection", "Keep-Alive");
                client.DefaultRequestHeaders.ExpectContinue = false;



                var req = new HttpRequestMessage(HttpMethod.Post, url) { Content = new FormUrlEncodedContent(dict) };

                //client.DefaultRequestHeaders.Accept.Add( new MediaTypeWithQualityHeaderValue( "application/x-www-form-urlencoded" ) );

                var uri = new Uri(url // + "?SAMLRequest=" + postData
                    );

                //var result = await client.PostAsync(url, req);
                var result = await client.SendAsync(req);
                //var result = await client.PostAsync(uri, content);

                if (result != null)
                {
                    StatusCode = result.StatusCode;
                    StatusCodeInt = (int)result.StatusCode;

                    ReasonPhrase = result.ReasonPhrase;

                    if (result.Content != null)
                    {
                        ResultStr = await result.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine("ok");
                    }
                }
                else
                {
                    ReasonPhrase = "null result";
                }
            }
        }
    }
}
