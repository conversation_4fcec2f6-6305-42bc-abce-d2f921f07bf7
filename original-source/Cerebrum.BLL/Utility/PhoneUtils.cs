﻿using System;
using System.Text.RegularExpressions;

namespace Cerebrum.BLL.Utility
{
    public static class PhoneUtils
    {
        public static string PhoneOnlyDigits(string oldstring)
        {
            return string.IsNullOrWhiteSpace(oldstring) ? string.Empty : Regex.Replace(oldstring, "[^.0-9]", "");
        }
        public static string FormatPhone(string phoneNumber)
        {
            var phone = PhoneOnlyDigits(phoneNumber);
            long p;

            bool isNumeric = long.TryParse(phone, out p);

            if (isNumeric)
            {
                return String.Format("{0:(###) ###-####}", Convert.ToInt64(phone));
            }
            else
            {
                return phoneNumber;
            }

        }
    }
}
