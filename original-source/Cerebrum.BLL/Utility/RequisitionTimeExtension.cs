﻿using Cerebrum.Data;
using Cerebrum.ViewModels.Schedule;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.BLL.Utility
{
    public static class RequisitionTimeExtension
    {
        
        public static DateTime? GetNextDate(this RequisitionTime t, VMAppointment a)
        {
            CerebrumContext _context = new CerebrumContext();

            switch (t.name)
            {
                case "ASAP":
                    return DateTime.Today;
                case "1 Month":
                    return a.AppointmentDate.AddMonths(1).AddDays(1);
                case "2 Months":
                    return a.AppointmentDate.AddMonths(2).AddDays(1);
                case "3 Months":
                    return a.AppointmentDate.AddMonths(3).AddDays(1);
                case "6 Months":
                    return a.AppointmentDate.AddMonths(6).AddDays(1);
                case "1 year":
                    return a.AppointmentDate.AddYears(1).AddDays(1);

                case "Prior Next Appointment":
                    {
                        var appointments = _context.Appointments.Where(w => w.PatientRecordId == a.PatientId).OrderByDescending(o => o.appointmentTime);
                        if (appointments != null && appointments.Count() > 0)
                        {
                            var nextApp = appointments.FirstOrDefault();
                            if (nextApp.appointmentTime > DateTime.Today)
                            {
                                return nextApp.appointmentTime.AddDays(-1);
                            }
                        }
                    }
                    break;
                case "At Next Appointment":
                    {
                        var appointments = _context.Appointments.Where(w => w.PatientRecordId == a.PatientId).OrderByDescending(o => o.appointmentTime);
                        if (appointments != null && appointments.Count() > 0)
                        {
                            var nextApp = appointments.FirstOrDefault();
                            return nextApp.appointmentTime;
                        }
                    }
                    break;
            }
            return null;
        }
    }
}
