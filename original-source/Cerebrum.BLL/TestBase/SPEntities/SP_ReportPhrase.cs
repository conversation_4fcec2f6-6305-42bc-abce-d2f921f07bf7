﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.BLL.TestBase.SPEntities
{
    
    public class SP_ReportPhrase
    {
        public int Id { get; set; } // vp report phrase id
        public int RootCategoryId { get; set; } // root id
        public string Name { get; set; }
        public string Value { get; set; }
        public int Parent { get; set; }
        public string ParentName { get; set; }
        public int Level { get; set; }
        public int Order { get; set; }
        public int Root { get; set; }
        public int Status { get; set; }
        public int Spec { get; set; }
        public int? DrID { get; set; }
        public bool HasChildren { get; set; }

    }
}
