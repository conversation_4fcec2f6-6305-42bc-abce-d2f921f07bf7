﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.BLL.TestBase.SPEntities
{
    public class SP_ReportMedication
    {
        public int PatientId { get; set; }
        public int PatientMedicationId { get; set; }
        public int MedicationSetId { get; set; }
        public string MedicationName { get; set; }
        public string MedicationNameConCat { get; set; }
        public string DIN { get; set; }
        public int? MedicationNoDinId { get; set; }
        public string Dose { get; set; }
        public string Strength { get; set; }
        public string Ingredients { get; set; }
        public string Form { get; set; }
        public string Route { get; set; }
        public string SIG { get; set; }        
        public DateTime DateStarted { get; set; }
        public DateTime? DateDiscontinued { get; set; }
        public int AppointmentId { get; set; }
        public int AppointmentTestId { get; set; }
        public DateTime AppointmentTime { get; set; }
        public string ActionType { get; set; }
        public int RowNum { get; set; }        
    }
}
