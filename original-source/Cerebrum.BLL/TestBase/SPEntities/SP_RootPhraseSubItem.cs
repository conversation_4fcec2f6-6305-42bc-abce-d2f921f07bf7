﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.BLL.TestBase.SPEntities
{
    public class SP_RootPhraseSubItem
    {
        public int PracRootCatPhraseId { get; set; }
        public int DoctorRootCategoryPhraseId { get; set; }
        public int RootCategoryPhraseId { get; set; }
        public int PracRootCategoryTempId { get; set; }
        public int ExternalDoctorId { get; set; }
        public int RootCategoryId { get; set; }        
        public int PracticeId { get; set; }
        public bool IsVisible { get; set; }        
        public int ParentId { get; set; }
        public string PhraseName { get; set; }
        public string PhraseValue { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsCategory { get; set; }


    }
}
