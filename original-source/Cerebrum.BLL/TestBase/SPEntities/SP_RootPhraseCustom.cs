﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.BLL.TestBase.SPEntities
{
    public class SP_RootPhraseCustom
    {

        public int RootCategoryCustomId { get; set; }
        public string CategoryNameCustom { get; set; }
        public int? ExternalDoctorId { get; set; }
        public int RootCategoryId { get; set; }
        public int GroupId { get; set; }
        public int? PracticeId { get; set; }
        public bool IsRootCategoryCustomVisible { get; set; }
        public int RootCategoryCustomPhraseId { get; set; }
        public int ParentId { get; set; }
        public string PhraseName { get; set; }
        public string PhraseValue { get; set; }
        public bool IsRootCategoryPhraseVisible { get; set; }
        public bool IsPracticeSetting { get; set; }
        public bool IsVisibleToolbar { get; set; }

        public string Breadcrum { get; set; }
        public int Level { get; set; }
        
    }
}
