﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.BLL.TestBase.SPEntities
{
    public class SP_RootCategoryCustom
    {        
        public int Id { get; set; } // root category custom id        
        public string CategoryNameOriginal { get; set; } // root name to display
        public string CategoryNameCustom { get; set; } // custom name to display
        public int? DisplayOrder { get; set; } // order of which to display
        public bool Accumulative { get; set; }               
        public int RootCategoryId { get; set; }
        public int GroupId { get; set; }
        public int? ExternalDoctorId { get; set; }
        public int? PracticeId { get; set; }        
        public bool IsVisible { get; set; } // will show the root category on page when true
        public bool IsVisibleInLetter { get; set; } // will show the root category in letter or report when true    
        public bool IsVisibleToolbar { get; set; } // will show the patient tool bar for when adding or editing phrases when true     
        public bool IsPracticeSetting { get; set; }
    }
}
