﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.BLL.TestBase.SPEntities
{
    public class SP_TestBase
    {
        public int AppointmentId { get; set; }
        public int PracticeId { get; set; }
        public int OfficeId { get; set; }
        public int PatientId { get; set; }
        public DateTime AppointmentTime { get; set; }
        public AppointmentStatus AppointmentStatus { get; set; }
        public int PracticeDoctorSpecialtyId { get; set; }
        public int PracticeDoctorUserId { get; set; }
        public int PracticeDoctorId { get; set; }
        public string PracticeDoctor { get; set; }
        public int ExternalDoctorId { get; set; } // external doctor id for the practice doctor        
        public int? BillStatusId { get; set; }
        public string BillStatus { get; set; }
        public string BillStatusColor { get; set; }
        public int AppointmentTypeId { get; set; }
        public string AppointmentType { get; set; }

        public int AppointmentTestId { get; set; }

        public int TestId { get; set; }
        public string TestName { get; set; }
        public string TestNameFull { get; set; }
        public int TestStatusId { get; set; }
        public string TestStatus { get; set; }
        public string TestStatusColor { get; set; }
        public string TestStatusCSS { get; set; }
        public DateTime TestDate { get; set; }

        public int TestGroupId { get; set; }
        public int ReportTemplate { get; set; }
        public bool IsVP { get; set; }
        public int AppointmentTestLogId { get; set; } // for echos, ecgs, h1 .. for new vp AppointmentTestSavedLogId
        public DateTime? AppointmentTestLogDate { get; set; }
        public int PrevAppTestLogIdDoctor { get; set; } // previous log id for the appointment doctor
        public int PrevAppointmentTestLogId { get; set; } // previous log id for the appointment test
        public int PracticeTemplateId { get; set; }
    }
}
