﻿using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;

namespace AwareServiceClient.Interceptors
{
    public class AwareHttpAuthInterceptor : DelegatingHandler
    {
        private readonly string _accessToken;

        public AwareHttpAuthInterceptor(string accessToken, HttpClientHandler innerHandler)
        {
            _accessToken = accessToken;
            InnerHandler = innerHandler;
        }

        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
            return base.SendAsync(request, cancellationToken);
        }
    }
}
