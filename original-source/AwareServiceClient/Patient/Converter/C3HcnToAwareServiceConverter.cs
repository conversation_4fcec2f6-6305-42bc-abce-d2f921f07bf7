﻿using System;
using AwareMD.Cerebrum.Shared.Enums;
using AwareServiceClient.Enums;

namespace AwareServiceClient.patient.Converter
{
    public class C3HcnToAwareServiceConverter
    {
        public static IdentifierType GetHCNByProvince(Province prov)
        {
            switch (prov)
            {
            case Province.CAON: return IdentifierType.OntarioHCN; // Ontario 
            case Province.CAAB: return IdentifierType.AlbertaHCN; // Alberta
            case Province.CABC: return IdentifierType.BritishColumbiaHCN; // British Columbia
            case Province.CAMB: return IdentifierType.ManitobaHCN; // Manitoba
            case Province.CANB: return IdentifierType.NewBrunswickHCN; // New Brunswick
            case Province.CANL: return IdentifierType.NewfoundlandAndLabradorHCN; // Newfoundland and Labrador
            case Province.CANS: return IdentifierType.NovaScotiaHCN; // Nova Scotia
            case Province.CANT: return IdentifierType.NorthwestTerritoriesHCN; // Northwest Territories
            case Province.CANU: return IdentifierType.NunavutHCN; // Nunavut
            case Province.CAPE: return IdentifierType.PrinceEdwardIslandHCN; // Prince Edward Island
            case Province.CAQC: return IdentifierType.QuebecHCN; // Quebec
            case Province.CASK: return IdentifierType.SaskatchewanHCN; // Saskatchewan
            case Province.CAYT: return IdentifierType.YukonHCN; // Yukon
            default: throw new Exception($"Province {prov} not found");
            }
        }
    }
}
