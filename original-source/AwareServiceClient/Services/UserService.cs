﻿using Cerebrum.Data;
using Cerebrum.Data.Repositories;

namespace AwareServiceClient.Services
{
    public class UserService
    {
        private readonly UserRepository _userRepository = new UserRepository();

        public ApplicationUser GetUserById(int userId)
        {
            return _userRepository.GetUserById(userId);
        }

        public void Update(ApplicationUser user)
        {
            _userRepository.UpdateUser(user);
        }
    }
}
