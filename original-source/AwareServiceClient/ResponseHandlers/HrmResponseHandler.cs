﻿using System;
using Newtonsoft.Json;

namespace AwareServiceClient.ResponseHandlers
{
    public class HrmResponseHandler : IResponseHandler<HrmResponseHandler>
    {
        public (System.Net.HttpStatusCode StatusCode, string Message, string MessageId) ProcessGenericResponse(
            string resultStr)
        {
            var statusCode = System.Net.HttpStatusCode.InternalServerError;
            var clinicResponse = JsonConvert.DeserializeObject<Models.HRMReportApiResponse>(resultStr);
            
            if (Enum.TryParse(clinicResponse.Data?.Status.ToString(), true, out HRMReportStatus statusEnum) &&
                statusEnum == HRMReportStatus.Ok) statusCode = System.Net.HttpStatusCode.OK;

            return (statusCode, clinicResponse.Data?.StatusMessage, clinicResponse.Data?.Id?.ToString());
        }
    }
}
