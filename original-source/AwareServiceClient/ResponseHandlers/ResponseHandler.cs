﻿using System;
using System.Reflection;
using System.Text;
using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace AwareServiceClient.ResponseHandlers
{
    public class ResponseHandler<T>
    {
        private static readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod()?.DeclaringType);
        public string Message { get; internal set; }
        public string MessageId { get; internal set; }
        public System.Net.HttpStatusCode StatusCode { get; internal set; }

        public static ResponseType GetResponseType(string resultStr)
        {
            var jsonObject = JObject.Parse(resultStr);
            if (jsonObject == null)
                throw new InvalidOperationException("Service invalid response");
            var typeStringValue = jsonObject["type"]?.Value<string>();
                
            var typeValue = Enum.TryParse(typeStringValue, true, out ResponseType resultType)
                ? resultType
                : throw new InvalidOperationException("Type response unknown");
            return typeValue;
        }

        private void ProcessErrorResponse(string resultStr)
        {
            Message += JsonConvert.DeserializeObject<ErrorResponseDtoApiResponse>(resultStr).Data.Details;
        }

        public ResponseHandler<T> ProcessResponse(string resultStr)
        {
            var typeValue = GetResponseType(resultStr);
            
            var specificHandler = Activator.CreateInstance<T>();
            var responseHandler = (specificHandler as IResponseHandler<T>);

            switch (typeValue)
            {
                case ResponseType.Generic:
                    var genericResponse = responseHandler?.ProcessGenericResponse(resultStr);
                    if (genericResponse != null)
                    {
                        Message = genericResponse.Value.Message;
                        StatusCode = genericResponse.Value.StatusCode;
                        MessageId = genericResponse.Value.MessageId;
                    }
                    break;
                case ResponseType.ValidationError:
                    ProcessValidationErrorResponse(resultStr);
                    break;
                case ResponseType.Error:
                case ResponseType.AuthenticationError:
                case ResponseType.AuthorizationError:
                    ProcessErrorResponse(resultStr);
                    break;
                default:
                    throw new InvalidOperationException($"Type response unknown: {resultStr}");
            }

            return this;
        }

        private void ProcessValidationErrorResponse(string resultStr)
        {
            var errorDto = JsonConvert.DeserializeObject<ValidationErrorDtoListApiResponse>(resultStr);
            var messageBuilder = new StringBuilder();
            messageBuilder.AppendLine($"{errorDto.StatusMessage}: ");
            foreach (var error in errorDto.Data) messageBuilder.AppendLine($" - {error.Field}: {error.Details}");
            Message += messageBuilder.ToString();
        }
    }
}
