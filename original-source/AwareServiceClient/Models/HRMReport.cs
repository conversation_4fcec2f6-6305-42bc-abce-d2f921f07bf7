﻿using System;
using System.Collections.Generic;

namespace AwareServiceClient.Models
{
    public class HRMReport
    {
        public Guid? Id { get; set; }
        public DateTimeOffset? CreatedAt { get; set; }
        public DateTimeOffset? UpdatedAt { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public DiagnosticReport Report { get; set; }
        public ProviderRecord Sender { get; set; }
        public ICollection<ProviderRecord> Recipients { get; set; }
        public string StatusCallBackUrl { get; set; }
        public int Retries { get; set; }
        public HRMReportStatus Status { get; set; }
        public string StatusMessage { get; set; }
        public DateTimeOffset SendAt { get; set; }
    }

    public class HRMReportApiResponse
    {
        public ResponseType Type { get; set; }
        public HRMReport Data { get; set; }
        public string StatusMessage { get; set; }
        public HttpStatusCode StatusCode { get; set; }
    }

    public class DiagnosticReport : IdentifiableRecordBase
    {
        public PatientRecord Patient { get; set; }
        public ProviderRecord PerformingPractitioner { get; set; }
        public ProviderRecord RequestingPractitioner { get; set; }
        public AppointmentRecord RelatedAppointment { get; set; }
        public string Conclusion { get; set; }
        public DiagnosticReportStatus Status { get; set; }
        public ICollection<DocumentDescriptor> Documents { get; set; }
        public Language ReportLanguage { get; set; }
        public DiagnosticCategory Category { get; set; }
        public string RequisitionId { get; set; }
        public string LoincCode { get; set; }
        public ICollection<string> SnomedConclusionCodes { get; set; }
        public bool IsUrgent { get; set; }
        public DateTimeOffset? ObservedAt { get; set; }
    }

    public class DocumentDescriptor : IdentifiableRecordBase
    {
        public string Name { get; set; }
        public string MimeType { get; set; }
    }
}
