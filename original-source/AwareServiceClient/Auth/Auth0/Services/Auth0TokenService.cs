﻿using System;
using System.Threading.Tasks;
using AwareServiceClient.Utilities;
using Cerebrum.Data;
using Jose;
using AwareServiceClient.Services;
using Auth0.ManagementApi.Models;
using Auth0.AuthenticationApi;
using Auth0.AuthenticationApi.Models;
using System.Collections.Generic;
using log4net;
using System.Text.RegularExpressions;

namespace AwareServiceClient.Auth.Auth0
{
    public class Auth0TokenService
    {
        private readonly ClinicService _clinicService;
        private readonly UserService _userService;
        private readonly Auth0Config _auth0Config;

        static readonly ILog Logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private const string _scopes = "openid";

        // ===========================================
        // Public Methods
        // ===========================================

        public Auth0TokenService()
        {
            this._auth0Config = System.Configuration.ConfigurationManager.GetSection("Auth0") as Auth0Config;
            this._userService = new UserService();
            this._clinicService = new ClinicService();
        }

        public ApplicationUser CreateAuth0User(ApplicationUser c3User, Guid clinicTenantId)
        {
            using (var managementApiClient = Auth0ManagementApiFactory.Build(_auth0Config))
            {
                c3User.IdpRandomPassword = RandomPasswordUtility.GenerateRandomPassword();
                var creationResult = Task.Run(async () => await managementApiClient.Users.CreateAsync(new UserCreateRequest
                {
                    Connection = Auth0UserConnection.UsernamePassword,
                    Email = FormatEmailForAuth0(c3User.Email),
                    EmailVerified = true,
                    FirstName = c3User.FirstName,
                    LastName = c3User.LastName,
                    Password = c3User.IdpRandomPassword,
                    AppMetadata = new
                    {
                        tenantId = clinicTenantId
                    }
                })).Result;

                c3User.IdpId = creationResult.UserId;
                _userService.Update(c3User);

                return c3User;
            }
        }

        public void AssignRoleToUser(string userId, string roleId)
        {
            using (var managementApiClient = Auth0ManagementApiFactory.Build(_auth0Config))
            {
                Task.Run(async () => await managementApiClient.Users.AssignRolesAsync(
                    userId,
                    new AssignRolesRequest
                    {
                        Roles = new string[] { roleId }
                    })).Wait();
            }
        }

        public string GetAccessToken(int userId, int practiceId)
        {
            return GetAccessTokenInternal(userId, practiceId, true);
        }

        /// <summary>
        /// Retrieves an access token using Client Credentials from Auth0.
        /// </summary>
        /// <returns>The access token as a string.</returns>
        /// <exception cref="Exception">Thrown if an error occurs while obtaining the token.</exception>
        public async Task<string> GetClientCredentialsTokenAsync()
        {
            using (var client = new AuthenticationApiClient(new Uri($"https://{_auth0Config.Tenant}")))
            {
                try
                {
                    Logger.Info("Requesting Auth0 token using client credentials...");

                    var tokenRequest = new ClientCredentialsTokenRequest
                    {
                        ClientId = _auth0Config.ClientId,
                        ClientSecret = _auth0Config.ClientSecret,
                        Audience = _auth0Config.AwareServicesApiAudience
                    };

                    var tokenResponse = await client.GetTokenAsync(tokenRequest);

                    if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.AccessToken))
                    {
                        throw new Exception("Error getting access token from Auth0 using client credentials. Token response was null.");
                    }

                    Logger.Info("Successfully obtained client credentials token.");
                    return tokenResponse.AccessToken;
                }
                catch (Exception ex)
                {
                    Logger.Error($"Auth0 Client Credentials Error: {ex.Message}", ex);
                    throw;
                }
            }
        }
        public string GetAccessTokenOrNull(int userId, int practiceId)
        {
            return GetAccessTokenInternal(userId, practiceId, false);
        }
        private string GetAccessTokenInternal(int userId, int practiceId, bool throwIfTenantNotFound)
        {
            var c3User = _userService.GetUserById(userId);
            var clinicTenant = _clinicService.GetClinicTenantByPracticeId(practiceId);

            if (clinicTenant == null)
            {
                if (throwIfTenantNotFound)
                {
                    throw new Exception("Missing practice TenantId value");
                }
                return null; 
            }
            if (c3User.IdpId == null)
            {
                Logger.Info($"Creating c3 user in Auth0 {c3User.UserName} in tenant {clinicTenant}");
                c3User = CreateAuth0User(c3User, (Guid)clinicTenant);

                Logger.Info($"Assigning role {_auth0Config.DefaultRoleId} to user {c3User.IdpId})");
                AssignRoleToUser(c3User.IdpId, _auth0Config.DefaultRoleId);
            }

            using (var client = new AuthenticationApiClient(new Uri($"https://{_auth0Config.Tenant}")))
            {
                AccessTokenResponse token;
                try {
                    // Try to get token normally
                    token = GetAccessToken(client, c3User);
                }
                catch (Exception e)
                {
                    var innerMessage = e.InnerException?.Message?.ToLower();
                    Logger.Warn($"Auth0 Login Error: '{e.Message}':'{innerMessage}'. Attempting to auto-resolve account differences.");
                    // If the error is due to wrong email, update the user and try again.
                    // this may occur if the users email was somehow updated in C3 but not in Auth0, for example.
                    if(innerMessage != null && innerMessage.Contains("wrong email"))
                    {
                        UpdateAuth0User(c3User, clinicTenant.Value);
                        token = GetAccessToken(client, c3User);
                        Logger.Warn($"Auth0 Account differences were automatically updated.");
                    }
                    else {
                        throw e;
                    }
                }

                if (token == null || token.AccessToken == null)
                {
                    throw new Exception("Error getting access token from Auth0 for AwareService API. Access token was null.");
                }

                dynamic auth0UserAppMetadata = GetAuth0User(c3User).AppMetadata;
                if (auth0UserAppMetadata.tenantId != clinicTenant)
                {
                    UpdateAuth0User(c3User, (Guid)clinicTenant);
                }
                return token.AccessToken;
            }
        }

        public User GetAuth0User(ApplicationUser c3User)
        {
            using (var managementApiClient = Auth0ManagementApiFactory.Build(_auth0Config))
            {
                return Task.Run(async () => await managementApiClient.Users.GetAsync(c3User.IdpId)).Result;
            }
        }

        public void UpdateAuth0User(ApplicationUser c3User)
        {
            var clinicTenant = _clinicService.GetClinicTenantByPracticeId(c3User.PracticeID);
            if (clinicTenant == null)
            {
                throw new Exception($"practice {c3User.PracticeID} missing TenantId value");
            }
            UpdateAuth0User(c3User, clinicTenant.Value);
        }

        public void UpdateAuth0User(ApplicationUser c3User, Guid clinicTenantId)
        {
            using (var managementApiClient = Auth0ManagementApiFactory.Build(_auth0Config))
            {
                _ = Task.Run(async () => await managementApiClient.Users.UpdateAsync(c3User.IdpId, new UserUpdateRequest
                {
                    Connection = Auth0UserConnection.UsernamePassword,
                    Email = c3User.Email,
                    FullName = c3User.Email,
                    EmailVerified = true,
                    FirstName = c3User.FirstName,
                    LastName = c3User.LastName,
                    AppMetadata = new
                    {
                        tenantId = clinicTenantId
                    }
                })).Result;
            }
        }

        public bool IsTokenValid(string accessToken)
        {
            if (string.IsNullOrEmpty(accessToken)) return false;
            var expirationTime = GetTokenExpirationTime(accessToken);
            return expirationTime > DateTime.UtcNow;
        }

        public DateTime GetTokenExpirationTime(string accessToken)
        {
            var payload = JWT.Payload<Dictionary<string, object>>(accessToken);
            var expUnixTime = Convert.ToInt64(payload["exp"]);
            return DateTimeOffset.FromUnixTimeSeconds(expUnixTime).UtcDateTime;
        }

        private string FormatEmailForAuth0(string email)
        {
            // Remove whitespace from email to prevent errors
            return Regex.Replace(email, @"\s+", "");
        }

        private AccessTokenResponse GetAccessToken(AuthenticationApiClient client, ApplicationUser c3User)
        {
            return Task.Run(async () => await client.GetTokenAsync(new ResourceOwnerTokenRequest()
                    {
                        ClientId = _auth0Config.ClientId,
                        ClientSecret = _auth0Config.ClientSecret,
                        Audience = _auth0Config.AwareServicesApiAudience,
                        Scope = _scopes,
                        Username = FormatEmailForAuth0(c3User.Email),
                        Password = c3User.IdpRandomPassword
                    })).Result;
        }
    }
}
