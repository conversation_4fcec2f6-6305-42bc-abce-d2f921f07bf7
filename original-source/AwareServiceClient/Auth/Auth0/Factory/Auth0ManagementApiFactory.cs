using System.Runtime.CompilerServices;
using Auth0.ManagementApi;
using Auth0.AuthenticationApi;
using Auth0.AuthenticationApi.Models;
using System;
using log4net;
using System.Reflection;
using System.Threading.Tasks;

namespace AwareServiceClient.Auth.Auth0
{
  public static class Auth0ManagementApiFactory
  {

    // how early in seconds should the management API token be refreshed.
    private const int PreemptiveRefreshTimeSeconds = 60;
    private static AccessTokenResponse _managementApiToken = null;
    private static DateTimeOffset? _managementApiTokenExpiresAt;
    private static readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

    // ===========================================
    // Class Methods 
    // ===========================================

    [MethodImpl(MethodImplOptions.Synchronized)]
    public static ManagementApiClient Build(Auth0Config config)
    {
      if (Auth0ManagementApiFactory.ManagementApiTokenNeedsRefresh)
      {
        _managementApiToken = GetAccessToken(config);
      }
      return new ManagementApiClient(_managementApiToken.AccessToken, new Uri(config.ManagementApiAudience));
    }


    // ===========================================
    // Getters
    // ===========================================

    /// <summary>
    /// Does the Auth0 Management API token need to be refreshed?
    /// </summary>
    /// <returns>True if the Auth0 Management API token needs to be refreshed, false otherwise</returns>
    public static bool ManagementApiTokenNeedsRefresh
    {
      get
      {
        return _managementApiToken == null ||
         !_managementApiTokenExpiresAt.HasValue ||
          DateTimeOffset.UtcNow.AddSeconds(PreemptiveRefreshTimeSeconds) >= _managementApiTokenExpiresAt;
      }
    }

    // ===========================================
    // Private Methods 
    // ===========================================

    /// <summary>
    /// Get an access token for the Auth0 Management API
    /// </summary>
    /// <param name="config"> Auth0 configuration </param>
    /// <returns> Access token for the Auth0 Management API </returns>
    /// <exception cref="Auth0Exception"></exception>
    private static AccessTokenResponse GetAccessToken(Auth0Config config)
    {
      using (var client = new AuthenticationApiClient(new Uri($"https://{config.Tenant}")))
      {
        var tokenResponse = Task.Run(async () => await client.GetTokenAsync(new ClientCredentialsTokenRequest
        {
          ClientId = config.ClientId,
          ClientSecret = config.ClientSecret,
          Audience = config.ManagementApiAudience
        })).Result;

        if (tokenResponse == null || tokenResponse.AccessToken == null)
        {
          throw new Auth0Exception("Error getting access token for Auth0 Management API. Access token was null.");
        }

        _managementApiTokenExpiresAt = DateTimeOffset.UtcNow.AddSeconds(tokenResponse.ExpiresIn);

        return tokenResponse;
      }
    }
  }
}