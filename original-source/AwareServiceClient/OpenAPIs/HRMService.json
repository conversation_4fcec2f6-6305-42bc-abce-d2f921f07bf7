{"openapi": "3.0.1", "info": {"title": "HRMService", "version": "1.0"}, "servers": [{"url": "/hrm", "description": "Server behind reverse proxy"}], "paths": {"/api/v1/report": {"post": {"tags": ["HRMReport"], "operationId": "HRMReportSendReport", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HRMReport"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorDtoListApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticationErrorDtoApiResponse"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorizationErrorDtoApiResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDtoApiResponse"}}}}, "200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HRMReportApiResponse"}}}}}}}}, "components": {"schemas": {"Address": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/AddressType"}, "use": {"$ref": "#/components/schemas/AddressUse"}, "lines": {"type": "array", "items": {"type": "string"}, "nullable": true}, "city": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "country": {"maxLength": 3, "type": "string", "nullable": true}}, "additionalProperties": false}, "AddressType": {"enum": ["Postal", "Physical", "Both"], "type": "string"}, "AddressUse": {"enum": ["Home", "Work", "Temp", "Old", "Billing", "Unspecified"], "type": "string"}, "AppointmentRecord": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"type": {"$ref": "#/components/schemas/AppointmentType"}, "participants": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/Person"}, {"$ref": "#/components/schemas/ProviderRecord"}, {"$ref": "#/components/schemas/PatientRecord"}]}, "nullable": true}, "status": {"$ref": "#/components/schemas/AppointmentStatus"}, "start": {"type": "string", "format": "date-time", "nullable": true}, "end": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "AppointmentStatus": {"enum": ["Scheduled", "Arrived", "InProgress", "Completed", "Cancelled"], "type": "string"}, "AppointmentType": {"enum": ["InOffice", "Virtual", "Home", "Other"], "type": "string"}, "AuditEvent": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"subject": {"$ref": "#/components/schemas/AuditEventSubject"}, "subjectId": {"type": "string", "format": "uuid", "nullable": true}, "action": {"$ref": "#/components/schemas/AuditEventAction"}, "outcome": {"$ref": "#/components/schemas/AuditEventOutcome"}, "details": {"type": "string", "nullable": true}, "jsonType": {"$ref": "#/components/schemas/AuditEventJsonType"}, "json": {"type": "string", "nullable": true}, "performedBy": {"type": "string", "nullable": true}, "tenantId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "AuditEventAction": {"enum": ["Create", "Read", "Update", "Delete"], "type": "string"}, "AuditEventJsonType": {"enum": ["Subject"], "type": "string"}, "AuditEventOutcome": {"enum": ["Success", "Failure", "Unknown"], "type": "string"}, "AuditEventSubject": {"enum": ["HRMReport", "HRMFhirReport", "OneAccessGateway", "Credential", "Clinic"], "type": "string"}, "AuthenticationErrorDto": {"type": "object", "properties": {"details": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthenticationErrorDtoApiResponse": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/ResponseType"}, "data": {"$ref": "#/components/schemas/AuthenticationErrorDto"}, "statusMessage": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "AuthorizationErrorDto": {"type": "object", "properties": {"details": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthorizationErrorDtoApiResponse": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/ResponseType"}, "data": {"$ref": "#/components/schemas/AuthorizationErrorDto"}, "statusMessage": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "ClinicRecord": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"name": {"type": "string", "nullable": true}, "oneIdSettings": {"$ref": "#/components/schemas/OneIdSettingsRecord"}}, "additionalProperties": false}, "CommunicationMethod": {"enum": ["Email", "Phone", "Fax", "Pager", "Other"], "type": "string"}, "ContactMethod": {"type": "object", "properties": {"method": {"$ref": "#/components/schemas/CommunicationMethod"}, "value": {"type": "string", "nullable": true}, "usage": {"$ref": "#/components/schemas/ContactMethodUse"}}, "additionalProperties": false}, "ContactMethodUse": {"enum": ["Home", "Work", "Mobile", "Temp", "Old", "Unspecified"], "type": "string"}, "CredentialBase": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"type": {"enum": ["Oidc", "OidcClient"], "type": "string"}, "identifiers": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/Identifier"}, {"$ref": "#/components/schemas/HealthCardIdentifier"}]}, "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false, "discriminator": {"propertyName": "type"}}, "DiagnosticCategory": {"enum": ["Audiology", "BloodGases", "BloodBank", "Cytogenetics", "Chemistry", "Cytopathology", "CATScan", "CardiacCatheterization", "CardiacUltrasound", "Electrocardiac", "Electroneuro", "Genetics", "Hematology", "BedsideICUMonitoring", "Immunology", "Laboratory", "Microbiology", "Mycobacteriology", "Mycology", "NuclearMagneticResonance", "NuclearMedicineScan", "NursingServiceMeasures", "OutsideLab", "OccupationalTherapy", "Other", "OBUltrasound", "PulmonaryFunction", "Pharmacy", "Physician", "PhysicalTherapy", "Radiology", "RespiratoryCare", "RadiationTherapy", "RadiologyUltrasound", "Radiograph", "SurgicalPathology", "Serology", "Toxicology", "Virology", "VascularUltrasound", "Cineradiograph"], "type": "string"}, "DiagnosticReport": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"patient": {"$ref": "#/components/schemas/PatientRecord"}, "performingPractitioner": {"$ref": "#/components/schemas/ProviderRecord"}, "requestingPractitioner": {"$ref": "#/components/schemas/ProviderRecord"}, "relatedAppointment": {"$ref": "#/components/schemas/AppointmentRecord"}, "conclusion": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/DiagnosticReportStatus"}, "documents": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/InMemoryDocumentDescriptor"}, {"$ref": "#/components/schemas/SADocumentDescriptor"}]}, "nullable": true}, "reportLanguage": {"$ref": "#/components/schemas/Language"}, "category": {"$ref": "#/components/schemas/DiagnosticCategory"}, "requisitionId": {"type": "string", "nullable": true}, "loincCode": {"type": "string", "nullable": true}, "snomedConclusionCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isUrgent": {"type": "boolean"}, "observedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "DiagnosticReportStatus": {"enum": ["Incomplete", "Complete", "Corrected", "Cancelled", "Unknown"], "type": "string"}, "DocumentDescriptor": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"storageType": {"enum": ["InMemoryDocumentDescriptor", "SADocumentDescriptor"], "type": "string"}, "name": {"type": "string", "nullable": true}, "mimeType": {"type": "string", "nullable": true}}, "additionalProperties": false, "discriminator": {"propertyName": "storageType"}}, "ErrorResponseDto": {"type": "object", "properties": {"details": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ErrorResponseDtoApiResponse": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/ResponseType"}, "data": {"$ref": "#/components/schemas/ErrorResponseDto"}, "statusMessage": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "HRMReport": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "report": {"$ref": "#/components/schemas/DiagnosticReport"}, "sender": {"$ref": "#/components/schemas/ProviderRecord"}, "recipients": {"type": "array", "items": {"$ref": "#/components/schemas/ProviderRecord"}, "nullable": true}, "sendingClinicId": {"type": "string", "format": "uuid", "nullable": true}, "statusCallBackUrl": {"type": "string", "nullable": true}, "retries": {"type": "integer", "format": "int32"}, "status": {"$ref": "#/components/schemas/HRMReportStatus"}, "statusMessage": {"type": "string", "nullable": true}, "sendAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "HRMReportApiResponse": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/ResponseType"}, "data": {"$ref": "#/components/schemas/HRMReport"}, "statusMessage": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}, "HRMReportStatus": {"enum": ["Pending", "Ok", "TransientError", "FatalE<PERSON>r"], "type": "string"}, "HealthCardIdentifier": {"type": "object", "allOf": [{"$ref": "#/components/schemas/Identifier"}], "properties": {"version": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/IdentifierType"}}, "additionalProperties": false}, "HttpStatusCode": {"enum": ["Continue", "SwitchingProtocols", "Processing", "EarlyHints", "OK", "Created", "Accepted", "NonAuthoritativeInformation", "NoContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PartialContent", "MultiStatus", "AlreadyReported", "IMUsed", "MultipleChoices", "MovedPermanently", "Found", "<PERSON><PERSON><PERSON>", "NotModified", "UseProxy", "Unused", "TemporaryRedirect", "PermanentRedirect", "BadRequest", "Unauthorized", "PaymentRequired", "Forbidden", "NotFound", "MethodNotAllowed", "NotAcceptable", "ProxyAuthenticationRequired", "RequestTimeout", "Conflict", "Gone", "LengthRequired", "PreconditionFailed", "RequestEntityTooLarge", "RequestUriTooLong", "UnsupportedMediaType", "RequestedRangeNotSatisfiable", "ExpectationFailed", "MisdirectedRequest", "UnprocessableEntity", "Locked", "FailedDependency", "UpgradeRequired", "PreconditionRequired", "TooManyRequests", "RequestHeaderFields<PERSON>ooLarge", "UnavailableForLegalReasons", "InternalServerError", "NotImplemented", "BadGateway", "ServiceUnavailable", "GatewayTimeout", "HttpVersionNotSupported", "VariantAlsoNegotiates", "InsufficientStorage", "LoopDetected", "NotExtended", "NetworkAuthenticationRequired"], "type": "string"}, "IdentifiableRecordBase": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Identifier": {"type": "object", "properties": {"implementationType": {"enum": ["HealthCardIdentifier", "Identifier"], "type": "string"}, "type": {"$ref": "#/components/schemas/IdentifierType"}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false, "discriminator": {"propertyName": "implementationType"}}, "IdentifierType": {"enum": ["TenantId", "ClinicId", "ApplicationId", "SecondLevelApplicationId", "Email", "Username", "MasterRecordNumber", "OntarioHCN", "AlbertaHCN", "BritishColumbiaHCN", "ManitobaHCN", "NewBrunswickHCN", "NewfoundlandAndLabradorHCN", "NovaScotiaHCN", "NorthwestTerritoriesHCN", "NunavutHCN", "PrinceEdwardIslandHCN", "QuebecHCN", "SaskatchewanHCN", "YukonHCN", "OHRid", "OHUao", "OHMD", "OHNP", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OidcSubject", "OidcJti"], "type": "string"}, "InMemoryDocumentDescriptor": {"type": "object", "allOf": [{"$ref": "#/components/schemas/DocumentDescriptor"}], "properties": {"base64DocumentData": {"type": "string", "nullable": true}}, "additionalProperties": false}, "JwtCredentialData": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "idToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Language": {"enum": ["English", "Spanish", "French", "German", "Italian"], "type": "string"}, "MedicalGender": {"enum": ["Male", "Female", "Other", "Unknown"], "type": "string"}, "OidcClientCredential": {"type": "object", "allOf": [{"$ref": "#/components/schemas/CredentialBase"}], "properties": {"identifiers": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/Identifier"}, {"$ref": "#/components/schemas/HealthCardIdentifier"}]}, "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "clientCredentials": {"$ref": "#/components/schemas/OidcClientData"}}, "additionalProperties": false}, "OidcClientData": {"type": "object", "properties": {"clientId": {"type": "string", "nullable": true}, "clientSecret": {"type": "string", "nullable": true}, "jwtAssertionPrivateKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OidcCredential": {"type": "object", "allOf": [{"$ref": "#/components/schemas/CredentialBase"}], "properties": {"identifiers": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/Identifier"}, {"$ref": "#/components/schemas/HealthCardIdentifier"}]}, "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "tokens": {"$ref": "#/components/schemas/JwtCredentialData"}}, "additionalProperties": false}, "OneIdSettingsRecord": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"oagClientId": {"type": "string", "nullable": true}, "uao": {"type": "string", "nullable": true}, "upi": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PatientRecord": {"type": "object", "allOf": [{"$ref": "#/components/schemas/Person"}], "properties": {"nextOfKin": {"oneOf": [{"$ref": "#/components/schemas/Person"}, {"$ref": "#/components/schemas/ProviderRecord"}, {"$ref": "#/components/schemas/PatientRecord"}], "nullable": true}, "dateTimeOfDeath": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Person": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "middleName": {"type": "string", "nullable": true}, "gender": {"$ref": "#/components/schemas/MedicalGender"}, "dateOfBirth": {"type": "string", "format": "date", "nullable": true}, "identifiers": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/Identifier"}, {"$ref": "#/components/schemas/HealthCardIdentifier"}]}, "nullable": true}, "contactMethods": {"type": "array", "items": {"$ref": "#/components/schemas/ContactMethod"}, "nullable": true}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/Address"}, "nullable": true}}, "additionalProperties": false}, "ProviderRecord": {"type": "object", "allOf": [{"$ref": "#/components/schemas/Person"}], "additionalProperties": false}, "ResponseType": {"enum": ["Generic", "Error", "AuthenticationError", "AuthorizationError", "ValidationError"], "type": "string"}, "SADocumentDescriptor": {"type": "object", "allOf": [{"$ref": "#/components/schemas/DocumentDescriptor"}], "properties": {"storageAccountName": {"type": "string", "nullable": true}, "container": {"type": "string", "nullable": true}, "key": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TenantRecord": {"type": "object", "allOf": [{"$ref": "#/components/schemas/IdentifiableRecordBase"}], "properties": {"name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ValidationErrorCode": {"enum": ["Unknown", "Required", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "InvalidFormat", "InvalidReference"], "type": "string"}, "ValidationErrorDto": {"type": "object", "properties": {"details": {"type": "string", "nullable": true}, "field": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/ValidationErrorCode"}}, "additionalProperties": false}, "ValidationErrorDtoListApiResponse": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/ResponseType"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationErrorDto"}, "nullable": true}, "statusMessage": {"type": "string", "nullable": true}, "statusCode": {"$ref": "#/components/schemas/HttpStatusCode"}}, "additionalProperties": false}}, "securitySchemes": {"Authorization": {"type": "http", "description": "Jwt Bearer Token", "scheme": "Bearer", "bearerFormat": "JWT"}}}, "security": [{"Authorization": []}]}