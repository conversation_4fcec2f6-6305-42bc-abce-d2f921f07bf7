﻿using System;
using System.Linq;

namespace AwareServiceClient.Utilities
{
    internal class RandomPasswordUtility
    {
        public static string GenerateRandomPassword(int length = 24)
        {
            const string lowercaseChars = "abcdefghijklmnopqrstuvwxyz";
            const string uppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string numericChars = "1234567890";
            const string specialChars = "!@#$%^&*()_-+=<>?";

            var random = new Random();

            var password =
                $"{lowercaseChars[random.Next(lowercaseChars.Length)]}" +
                $"{uppercaseChars[random.Next(uppercaseChars.Length)]}" +
                $"{numericChars[random.Next(numericChars.Length)]}" +
                $"{specialChars[random.Next(specialChars.Length)]}";

            for (var i = password.Length; i < length; i++)
            {
                var validChars = lowercaseChars + uppercaseChars + numericChars + specialChars;
                password += validChars[random.Next(validChars.Length)];
            }

            password = new string(password.ToCharArray().OrderBy(x => random.Next()).ToArray());

            return password;
        }
    }
}
