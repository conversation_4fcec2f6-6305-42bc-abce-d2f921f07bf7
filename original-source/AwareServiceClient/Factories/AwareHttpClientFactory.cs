﻿using System.Collections.Generic;
using System.Net.Http;
using AwareServiceClient.Interceptors;
using AwareServiceClient.Auth.Auth0;
using System.Threading.Tasks;
using System.Threading;
using System;
using System.IdentityModel.Tokens.Jwt;

namespace AwareServiceClient.Factories
{
    public class AwareHttpClientFactory
    {
        private static readonly Dictionary<(int userId, int practiceId), string> _accessTokenDictionary = new Dictionary<(int userId, int practiceId), string>();
        private static readonly SemaphoreSlim _tokenLock = new SemaphoreSlim(1, 1);
        private static string _cachedClientCredentialsToken;
        private static DateTime _clientTokenExpiration = DateTime.MinValue;
        private readonly Auth0TokenService _auth0TokenService = new Auth0TokenService();

        public HttpClient CreateClient(int userId, int practiceId)
        {
            string accessToken = null;
            lock (_accessTokenDictionary) 
            {
                if (!_accessTokenDictionary.TryGetValue((userId, practiceId), out accessToken) || !_auth0TokenService.IsTokenValid(accessToken))
                {
                    accessToken = _auth0TokenService.GetAccessToken(userId, practiceId);
                    _accessTokenDictionary[(userId, practiceId)] = accessToken;
                }
            }

            if (accessToken != null) 
            {
                var httpClientHandler = new HttpClientHandler();
                var httpClient = new HttpClient(new AwareHttpAuthInterceptor(accessToken, httpClientHandler));
                return httpClient;
            }
            else    
            {
                throw new Auth0Exception("Failed to get Auth0 Access token for user");
            }
        }

        /// <summary>
        /// Creates an HTTP client using client credentials authentication.
        /// </summary>
        /// <returns>An authenticated HttpClient instance.</returns>
        /// <exception cref="Auth0Exception">Thrown if unable to retrieve a client credentials token.</exception>
        public async Task<HttpClient> CreateClientWithClientCredentialsAsync()
        {
            await _tokenLock.WaitAsync();
            try
            {
                if (string.IsNullOrEmpty(_cachedClientCredentialsToken) || DateTime.UtcNow >= _clientTokenExpiration)
                {
                    _cachedClientCredentialsToken = await _auth0TokenService.GetClientCredentialsTokenAsync()
                                                  ?? throw new Auth0Exception("Failed to get Auth0 Client Credentials token.");

                    var handler = new JwtSecurityTokenHandler();

                    if (handler.ReadToken(_cachedClientCredentialsToken) is JwtSecurityToken tokenS && tokenS.Payload.Exp.HasValue)
                    {
                        _clientTokenExpiration = DateTimeOffset.FromUnixTimeSeconds(tokenS.Payload.Exp.Value).UtcDateTime;
                    }
                    else
                    {
                        throw new Auth0Exception("Failed to extract token expiration.");
                    }
                }
            }
            finally
            {
                _tokenLock.Release();
            }

            return new HttpClient(new AwareHttpAuthInterceptor(_cachedClientCredentialsToken, new HttpClientHandler()));

        }
    }
}
