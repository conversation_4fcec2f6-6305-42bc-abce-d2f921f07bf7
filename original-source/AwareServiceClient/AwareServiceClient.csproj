﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
	  <Configurations>Debug;Release;DebugCli;DebugSharedDb;DebugCliSharedDb</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <OpenApiReference Include="OpenAPIs\HRMService.json" CodeGenerator="NSwagCSharp" ClassName="HRMService">
      <SourceUri>http://services.localhost/hrm/swagger/v1/swagger.json</SourceUri>
    </OpenApiReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Auth0.AuthenticationApi" Version="7.25.1" />
    <PackageReference Include="Auth0.ManagementApi" Version="7.25.1" />
    <PackageReference Include="AwareMD.Cerebrum.Shared" Version="*******" />
    <PackageReference Include="jose-jwt" Version="5.0.0" />
    <PackageReference Include="log4net" Version="2.0.12" />
    <PackageReference Include="Microsoft.AspNet.Identity.EntityFramework" Version="2.2.1" />
    <PackageReference Include="Microsoft.Extensions.ApiDescription.Client" Version="7.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="NSwag.ApiDescription.Client" Version="13.18.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Include="System.Runtime.Serialization.Primitives" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Cerebrum.Data\Cerebrum.Data.csproj" />
  </ItemGroup>

</Project>
