﻿using System;
using System.IO;
using System.Security.Cryptography;

using log4net;

namespace Cerebrum.HRMImport
{
    public static class AESEncryption
    {
        static readonly ILog Logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static string ErrorMessage { get; private set; }

        public static bool Decrypt(string inFile, string outFile, byte[] KeyBytes)
        {
            if (string.IsNullOrEmpty(inFile))
                return false;
            if (!File.Exists(inFile))
                return false;

            // byte[] InitialVectorBytes = Encoding.ASCII.GetBytes(InitialVector);
            byte[] InitialVectorBytes = KeyBytes;

            RijndaelManaged SymmetricKey = new RijndaelManaged();
            SymmetricKey.Mode = CipherMode.ECB;
            SymmetricKey.KeySize = 128;
            SymmetricKey.Key = KeyBytes;

            FileInfo fi = new FileInfo(inFile);
            long sz = fi.Length;
            byte[] PlainTextBytes = new byte[sz * 2];
            int ByteCount = 0;

            bool res = true;
            try
            {
                using (ICryptoTransform Decryptor = SymmetricKey.CreateDecryptor(KeyBytes, InitialVectorBytes))
                {
                    using (FileStream fileStream = new FileStream(inFile, FileMode.Open))
                    {
                        using (CryptoStream CryptoStream = new CryptoStream(fileStream, Decryptor, CryptoStreamMode.Read))
                        {

                            ByteCount = CryptoStream.Read(PlainTextBytes, 0, PlainTextBytes.Length);
                            fileStream.Close();
                            CryptoStream.Close();

                            FileStream fs = new FileStream(outFile, FileMode.Create, FileAccess.ReadWrite);
                            BinaryWriter bw = new BinaryWriter(fs);
                            bw.Write(PlainTextBytes, 0, ByteCount);
                            bw.Close();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Exception during Decrypt():");
                Logger.Error(ex.Message);
                ErrorMessage = ex.Message;
                res = false;
            }

            SymmetricKey.Clear();
            return res;
            // return Encoding.UTF8.GetString(PlainTextBytes, 0, ByteCount);
        }

        public static bool Decrypt(string inFile, string outFile, String hexKey)
        {
            try
            {
                byte[] KeyBytes = StringToByteArray(hexKey);
                return Decrypt(inFile, outFile, KeyBytes);
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);
                return false;
            }
        }

        private static byte[] StringToByteArray(String hex)
        {
            int NumberChars = hex.Length;
            byte[] bytes = new byte[NumberChars / 2];
            for (int i = 0; i < NumberChars; i += 2)
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);

            return bytes;
        }
    }
}