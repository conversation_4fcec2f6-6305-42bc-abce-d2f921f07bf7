﻿using log4net;

namespace Cerebrum.HRMImport
{
    public class Log
    {
        public static string APPLICATION = "Application";
        public static string AUTOPOLL = "Auto";
        public static string MANUALPOLL = "Manual";

        private static readonly ILog Logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static void Debug(string message)
        {
            Debug(int.Parse(GlobalContext.Properties["practiceIdGlobal"].ToString()), message);
        }

        public static void Debug(int practiceId, string message)
        {
            //GlobalContext.Properties["practiceId"] = officeId;
            LogicalThreadContext.Properties["practiceId"] = practiceId;
            Logger.Debug(message);
        }

        public static void Info(string message)
        {
            Info(int.Parse(GlobalContext.Properties["practiceIdGlobal"].ToString()), message);
        }

        public static void Info(int practiceId, string message)
        {
            LogicalThreadContext.Properties["practiceId"] = practiceId;
            Logger.Info(message);
        }

        public static void Warn(string message)
        {
            Warn(int.Parse(GlobalContext.Properties["practiceIdGlobal"].ToString()), message);
        }

        public static void Warn(int practiceId, string message)
        {
            LogicalThreadContext.Properties["practiceId"] = practiceId;
            Logger.Warn(message);
        }

        public static void Error(string message)
        {
            Error(int.Parse(GlobalContext.Properties["practiceIdGlobal"].ToString()), message);
        }
        public static void Error(int practiceId, string message)
        {
            LogicalThreadContext.Properties["practiceId"] = practiceId;
            Logger.Error(message);
        }

        public static void ResetCustomProperties()
        {
            GlobalContext.Properties["user"] = Log.APPLICATION;
            GlobalContext.Properties["pollingType"] = Log.APPLICATION;
            GlobalContext.Properties["recipient"] = string.Empty;
            GlobalContext.Properties["practiceIdGlobal"] = 0;
        }
    }
}
