﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AwareMD.Cerebrum.Shared" version="1.1.3.0" targetFramework="net48" />
  <package id="EntityFramework" version="6.2.0" targetFramework="net461" />
  <package id="log4net" version="2.0.12" targetFramework="net461" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Http" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="6.0.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net461" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.4" targetFramework="net461" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="WinSCP" version="5.13.1" targetFramework="net461" />
</packages>