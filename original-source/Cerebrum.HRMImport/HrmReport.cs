﻿namespace Cerebrum.HRMImport
{
    public class HrmReport
    {
        public string url { get; set; }
        public string reportClass { get; set; }
        public string reportSubClass { get; set; }
        public string reportDate { get; set; }
        public string reportUid { get; set; }
        public string reportTypeMark { get; set; }
        public string reportDescription { get; set; }
        public string patientFirstName { get; set; }
        public string patientLastName { get; set; }
        public string ohip { get; set; }
        public string ohipVer { get; set; }
        public string birthday { get; set; }
        public string doctorFirstName { get; set; }
        public string doctorLastName { get; set; }
        public string cpso { get; set; }
        public string familyDoctor { get; set; }
        public string address1 { get; set; }
        public string address2 { get; set; }
        public string phone1 { get; set; }
        public string phone2 { get; set; }
        public string gender { get; set; }
        public string zip { get; set; }
        public string province { get; set; }
        public string country { get; set; }
        public string authorLastName { get; set; }
        public string authorFirstName { get; set; }
        public string authorCPSO { get; set; }
        public string receivedDateTime { get; set; }
        public string eventDateTime { get; set; }
        public string fileExtensionAndVersion { get; set; }
        public string reviewingOHIPPhysicianId { get; set; }
        public string reviewedDateTime { get; set; }
        public string sendingFacility { get; set; }
        public string sendingFacilityReportNumber { get; set; }
        public string accompanyingSubClass { get; set; }
        public string accompanyingMnemonic { get; set; }
        public string accompanyingDescription { get; set; }
        public string observationDateTime { get; set; }
        public string resultStatus { get; set; }
    }
}
