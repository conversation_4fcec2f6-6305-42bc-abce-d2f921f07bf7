﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity.SqlServer;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Schema;
using System.Xml.Serialization;

using log4net;
using Cerebrum.BLL.Utility;
using Cerebrum.ExternalDocument;
using Cerebrum.ViewModels.Common;
using AwareMD.Cerebrum.Shared.Enums;
using AwareMD.Cerebrum.Shared;
using Cerebrum.BLL.Requisition;

namespace Cerebrum.HRMImport
{
    public class HrmImport
    {
        private const string DATEFORMAT = "MM/dd/yyyy";
        private const string DATEFORMAT2 = "yyyy-M-d";
        //private const string DATEFORMATXML = "yyyyMMddhhmmssfff";
        private const string DATEFORMATXML = "yyyyMMddHHmm";
        private const string URLTYPE_UPLOADURL = "UploadURL";

        private const string TBDDOCTORFIRSTNAME = "TBD_Doctor";
        private const string TBDPATIENTFIRSTNAME = "TBD_Patient";
        private const string TBDREPORTSUBCLASS = "TBD_Report_Subclass";
        private const string TBDREPORTACCOMPANYINGSUBCLASS = "TBD_Report_Accompanying_Sub_Class";
        private const string TBDREPORTACCOMPANYINGMNEUMONIC = "TBD_Report_Accompanying_Mneumonic";

        //private int tbdPracticeDoctorId = -1;
        private int tbdPatientRecordId = -1;
        private string reportManagerSchema;
        private string reportManagerDtSchema;
        private List<Cerebrum.Data.HRMReportClass> hrmReportClasses = null;
        private List<Cerebrum.Data.HRMReportSubClass> hrmReportSubClasses = null;
        private List<Cerebrum.Data.ReportSendingFacility> hrmFacilities = null;
        //private List<Cerebrum.Data.ReportAccompanyingSubclass> reportAccompanyingSubclasses = null;
        //private List<Cerebrum.Data.ReportAccompanyingMnemonic> reportAccompanyingMnemonics = null;
        private List<Cerebrum.Data.ReportSubclassMap> reportSubClassMaps = null;
        private Cerebrum.Data.CerebrumContext context;
        private IRequisitionBLL _requisitionBll;

        public List<string> emailMessages = new List<string>();

        //import hrm files
        internal bool Import(IHttpClientFactory httpClientFactory, VMPracticePoll practice, HrmService.HRMRUNMETHOD runMethod)
        {
            bool result = true;
            try
            {
                string tmpFolder = ConfigurationManager.AppSettings["TmpFolder"];

                if (!Helper.CheckFolder(tmpFolder))
                {
                    Log.Error(practice.PracticeId, $"Tmp folder {tmpFolder} doesn't exist!");
                    emailMessages.Add($"Tmp folder {tmpFolder} doesn't exist!");
                    return false;
                }

                Log.Info(practice.PracticeId, $"Import start ...");
                context = new Cerebrum.Data.CerebrumContext();
                ReadXmlSchema();
                //CopyDefaultSubclassMapping(practice.PracticeId);
                try
                {
                    if (runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                        GlobalContext.Properties["PracticeIdGlobal"] = practice.PracticeId;

                    GlobalContext.Properties["recipient"] = string.Empty;
                    Log.Info(practice.PracticeId, $"Import start for practice (id: {practice.PracticeId}) ...");
                    int tbdPracticeDoctorId = LoadTbdDoctorPatient(practice.PracticeId);
                    string clinicTmpFolder = Path.Combine(tmpFolder, practice.PracticeId.ToString());

                    result = Import(httpClientFactory, runMethod, tbdPracticeDoctorId, practice.PracticeId, clinicTmpFolder);

                    Log.Info(practice.PracticeId, $"Import end for practice (id: {practice.PracticeId})");
                }
                catch (Exception ex)
                {
                    Log.Error(practice.PracticeId, ex.Message);
                    Log.Error(practice.PracticeId, $"Import end for practice (id: {practice.PracticeId}) with error");
                    emailMessages.Add($"Import end for practice (id: {practice.PracticeId}) with error");
                    result = false;
                }
                Log.Info(practice.PracticeId, "Import end.");
            }
            catch (Exception e)
            {
                Log.Error(practice.PracticeId, e.Message);
                Log.Error(practice.PracticeId, "Import end with error.");
                emailMessages.Add("Import end with error.");
                result = false;
            }
            return result;
        }

        public bool Import(IHttpClientFactory httpClientFactory, HrmService.HRMRUNMETHOD runMethod, int tbdPracticeDoctorId, int practiceId, string tmpFolder)
        {
            bool res = true;
            if (!Helper.CheckFolder(tmpFolder))
            {
                Log.Error(practiceId, $"Temp folder {tmpFolder} doesn't exist!");
                emailMessages.Add($"Temp folder {tmpFolder} doesn't exist!");
                return false;
            }

            string[] hrmFiles = Directory.GetFiles(tmpFolder, "*.xml");
            if (hrmFiles.Length == 0)
            {
                Log.Info(practiceId, $"No hrm xml file in folder {tmpFolder}");
                return false;
            }

            _requisitionBll = new RequisitionBLL(context);
            BLL.Documents.DocumentsBLL documentsBLL = new BLL.Documents.DocumentsBLL();
            foreach (var hrmFile in hrmFiles)
            {
                GlobalContext.Properties["recipient"] = string.Empty;
                Log.Info(practiceId, $"Start importing file {Path.GetFileName(hrmFile)}");
                try
                {
                    if (ImportFile(httpClientFactory, runMethod, tbdPracticeDoctorId, practiceId, hrmFile, documentsBLL))
                        Log.Info(practiceId, $"Importing file {Path.GetFileName(hrmFile)} end.");
                    else
                    {
                        Log.Error(practiceId, $"Importing file {Path.GetFileName(hrmFile)} end with error");
                        emailMessages.Add($"Importing file {Path.GetFileName(hrmFile)} end with error");
                    }
                    File.Delete(hrmFile);
                }
                catch (Exception ex)
                {
                    Log.Error(practiceId, ex.Message);
                    Log.Error(practiceId, $"Importing file {Path.GetFileName(hrmFile)} end with error");
                    emailMessages.Add($"Importing file {Path.GetFileName(hrmFile)} end with error");
                }
            }
            return res;
        }

        public bool ValidateXmlFile(HrmService.HRMRUNMETHOD runMethod, int practiceId, string hrmContent)
        {
            bool result = true;
            try
            {
                XmlSchemaSet schemas = new XmlSchemaSet();
                schemas.Add("cds", XmlReader.Create(new StringReader(reportManagerSchema)));
                //todo     need more xml schema in window service mode
                if (runMethod == HrmService.HRMRUNMETHOD.AUTOPOLL || runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                    schemas.Add("cds_dt", XmlReader.Create(new StringReader(reportManagerDtSchema)));

                XDocument hrm = XDocument.Parse(hrmContent, LoadOptions.PreserveWhitespace);
                hrm.Validate(schemas, (o, e) =>
                {
                    Log.Error(practiceId, $"{e.Message}");
                    result = false;
                });

                //hrm.
            }
            catch (Exception ex)
            {
                result = false;
                Log.Error(practiceId, ex.Message);
            }

            return result;
        }

        public bool ImportFile(IHttpClientFactory httpClientFactory, HrmService.HRMRUNMETHOD runMethod, int tbdPracticeDoctorId, int practiceId, string hrmFile, Cerebrum.BLL.Documents.DocumentsBLL documentsBLL)
        {
            string hrmContent = File.ReadAllText(hrmFile);
            if (!ValidateXmlFile(runMethod, practiceId, hrmContent))
                return false;

            XmlSerializer serializer = new XmlSerializer(typeof(OmdCds));
            //serializer.UnknownElement += new XmlElementEventHandler(Serializer_UnknownElement);

            OmdCds CdsReport = (OmdCds)serializer.Deserialize(new StringReader(hrmContent));

            if (CdsReport == null)
                return false;

            using (var transaction = context.Database.BeginTransaction())
            {
                try
                {
                    LoadReportClasses(practiceId);

                    var patientRecord = CdsReport.PatientRecord;
                    var reportsReceiveds = patientRecord.ReportsReceived;
                    var transactionInformations = patientRecord.TransactionInformation;

                    DateTime testDate = DateTime.MaxValue;
                    if (reportsReceiveds != null && reportsReceiveds.Length > 0)
                    {
                        if (reportsReceiveds[0].OBRContent != null && reportsReceiveds[0].OBRContent.Length > 0)
                            testDate = GetDateTime(reportsReceiveds[0].OBRContent[0].ObservationDateTime);
                    }
                    if (testDate == DateTime.MaxValue)
                        testDate = DateTime.Now;

                    int patientRecordId = -1;
                    bool assignReportToTbdPatient = false;
                    string hrmComment = string.Empty;
                    if (IsPatientDataMissing(practiceId, patientRecord.Demographics))
                    {
                        bool errorHrmPatientData = false;
                        patientRecordId = IsPatientDataMatched(practiceId, testDate, patientRecord.Demographics, ref errorHrmPatientData);
                        if (patientRecordId > 0)                //find patient
                        {
                            if (errorHrmPatientData) //but patient info doesn't match
                            {
                                hrmComment = "Patient information (last name, health card number, ...) doesn't match";
                                assignReportToTbdPatient = true;
                            }
                        }
                    }
                    else            //Missing patient info
                    {
                        hrmComment = "Missing patient information (last name, health card number, ...)";
                        assignReportToTbdPatient = true;
                    }

                    if (assignReportToTbdPatient)
                    {
                        if (tbdPatientRecordId == -1)
                        {
                            Log.Error(practiceId, $"Cannot assign HRM report to patient '{TBDPATIENTFIRSTNAME}' (patient '{TBDPATIENTFIRSTNAME}' doesn't exist in practice)");
                            return false;
                        }
                        else
                        {
                            patientRecordId = tbdPatientRecordId;
                            Log.Info(practiceId, $"HRM report will be assigned to patient '{TBDPATIENTFIRSTNAME}'");
                        }
                    }

                    bool isGoodImporting = true;
                    foreach (var reportsReceived in reportsReceiveds)
                    {
                        if (!isGoodImporting)
                            break;

                        foreach (var transactionInformation in transactionInformations)
                        {
                            //bool errorHrmData = false;
                            string hrmCommentNew = hrmComment;

                            if (!IsReportDataCorrect(practiceId, reportsReceived))
                                isGoodImporting = false;
                            if (!IsTransactionDataCorrect(practiceId, transactionInformation))
                                isGoodImporting = false;

                            if (!isGoodImporting)
                                break;

                            int practiceDoctorId = 0;
                            bool autoAddNewPatient = false;
                            bool autoMarkSeenHRMReport = true;
                            if (GetDoctorData(practiceId, transactionInformation, ref practiceDoctorId, ref autoAddNewPatient, ref autoMarkSeenHRMReport))
                            {
                                GlobalContext.Properties["recipient"] = (transactionInformation.Provider.FirstName ?? string.Empty) + " " + (transactionInformation.Provider.LastName ?? string.Empty);
                            }
                            else
                            {
                                if (tbdPracticeDoctorId == -1)
                                {
                                    Log.Error(practiceId, $"Cannot assign HRM report to doctor '{TBDDOCTORFIRSTNAME}' (Doctor '{TBDDOCTORFIRSTNAME}' doesn't exist in practice)");
                                    isGoodImporting = false;
                                    break;
                                }
                                else
                                {
                                    Log.Info(practiceId, $"HRM report will be assigned to doctor '{TBDDOCTORFIRSTNAME}'");
                                    GlobalContext.Properties["recipient"] = TBDDOCTORFIRSTNAME;
                                }

                                if (!string.IsNullOrWhiteSpace(hrmCommentNew))
                                    hrmCommentNew += Environment.NewLine;

                                if (practiceDoctorId == 0)
                                    hrmCommentNew += "Cannot find doctor";
                                else
                                    hrmCommentNew += "Doctor information (CPSO, ...) doesn't match";

                                practiceDoctorId = tbdPracticeDoctorId;
                            }

                            string reportFileNameClinic = Path.GetFileNameWithoutExtension(hrmFile);
                            string reportFileExtensionClinic = string.Empty;
                            byte[] reportContent = null;
                            if (reportsReceived.Format == reportFormat.Text)
                            {
                                reportFileExtensionClinic = ".txt";
                                reportContent = Encoding.UTF8.GetBytes(reportsReceived.Content.Item.ToString());
                            }
                            else
                            {
                                reportFileExtensionClinic = reportsReceived.FileExtensionAndVersion;
                                reportContent = (byte[])reportsReceived.Content.Item;
                            }
                            reportFileNameClinic = Normalize(reportFileNameClinic, reportFileExtensionClinic);

                            string messageUniqueId = transactionInformation.MessageUniqueID;
                            Data.ReportReceived hrmReport = new Data.ReportReceived();

                            bool isNewReport = true;
                            bool isNewVersion = true;
                            var messageIdInfo = messageUniqueId.Split('^');
                            //string sendingFacilityId = "^" + messageIdInfo[2] + "^";
                            //string reportNumber = "^" + messageIdInfo[4] + "^";
                            string sendingFacilityId = reportsReceived.SendingFacility;
                            string reportNumber = reportsReceived.SendingFacilityReportNumber;
                            string hrmContentCheckSum = GetCheckSum(reportContent);
                            DateTime eventDate = GetDateTime(reportsReceived.EventDateTime);
                            DateTime observationDate = testDate;

                            //var hrmReports = context.ReportsReceived.Where(a => a.sendingFacilityId == sendingFacilityId && a.sendingFacilityReport == reportNumber && a.reportType == ReportType.HRM && (a.status == null || (bool)a.status) && a.assignmentStatus != DocumentAssignmentStatuses.Ignore).ToList();
                            var hrmReports = (from a in context.ReportsReceived
                                              join b in context.Offices on a.officeId equals b.Id
                                              where b.PracticeId == practiceId && a.sendingFacilityId == sendingFacilityId && a.sendingFacilityReport == reportNumber && a.reportType == ReportType.HRM && (a.status == null || (bool)a.status) && a.assignmentStatus != DocumentAssignmentStatuses.Ignore
                                              select a).ToList();
                            if (hrmReports.Count > 0)
                            {
                                foreach (var hrmReport2 in hrmReports)
                                {
                                    if (IsSamePatient(hrmReport2.PatientRecordId, patientRecord))
                                    {
                                        isNewReport = false;
                                        if (IsSameVersion(hrmReport2, reportsReceived, hrmContentCheckSum, messageUniqueId))
                                        {
                                            isNewVersion = false;
                                            hrmReport = hrmReport2;
                                            break;
                                        }
                                    }
                                }
                            }

                            ////string hrmFileCheckSum = GetCheckSum(hrmContent);
                            //string hrmContentCheckSum = GetCheckSum(reportContent);
                            //hrmReport = context.ReportsReceived.Where(a => a.xmlFileCheckSum == hrmFileCheckSum
                            //                && a.messageUniqueID.Contains(sendingFacilityId)
                            //                && a.messageUniqueID.Contains(reportNumber)
                            //                && ((a.eventDateTime == null && eventDate == DateTime.MaxValue) || (a.eventDateTime != null && SqlFunctions.DateDiff("DAY", a.eventDateTime, eventDate) == 0))
                            //                && ((a.ObservationDateTime == null && observationDate == DateTime.MaxValue) || (a.ObservationDateTime != null && SqlFunctions.DateDiff("DAY", a.ObservationDateTime, observationDate) == 0))
                            //                && a.reportType == ReportType.HRM
                            //                && a.assignmentStatus != DocumentAssignmentStatuses.Ignore).FirstOrDefault();
                            //if (hrmReport != null)
                            //{
                            //    Log.Info(practiceId, $"Duplicate HRM report found (ID: {hrmReport.Id})");
                            //    break;
                            //}

                            //bool isNewReport = false;
                            //hrmReport = context.ReportsReceived.Where(a => a.xmlFileCheckSum == hrmFileCheckSum
                            //                && a.reportType == ReportType.HRM
                            //                && a.assignmentStatus != DocumentAssignmentStatuses.Ignore).FirstOrDefault();
                            //if (hrmReport != null)
                            //    isNewReport = true;
                            //else
                            //{
                            //    hrmReport = context.ReportsReceived.Where(a => a.messageUniqueID.Contains(sendingFacilityId)
                            //                    && a.messageUniqueID.Contains(reportNumber)
                            //                    && ((a.eventDateTime == null && eventDate == DateTime.MaxValue) || (a.eventDateTime != null && SqlFunctions.DateDiff("DAY", a.eventDateTime, eventDate) == 0))
                            //                    && ((a.ObservationDateTime == null && observationDate == DateTime.MaxValue) || (a.ObservationDateTime != null && SqlFunctions.DateDiff("DAY", a.ObservationDateTime, observationDate) == 0))
                            //                    && a.reportType == ReportType.HRM
                            //                    && a.assignmentStatus != DocumentAssignmentStatuses.Ignore).FirstOrDefault();
                            //    if (hrmReport == null)
                            //        isNewReport = true;
                            //    else
                            //    {
                            //        if (IsSamePatient(hrmReport.PatientRecordId, patientRecord))
                            //        {
                            //            if (!IsSameReportInfo(hrmReport, reportsReceived))
                            //                isNewReport = true;
                            //            if (hrmReport.hRMResultStatus.ToLower() != reportsReceived.ResultStatus.ToString().ToLower())
                            //                isNewReport = true;
                            //            if (!IsSameDate(hrmReport.eventDateTime, reportsReceived.EventDateTime))
                            //                isNewReport = true;
                            //            if (hrmReport.media == null || hrmReport.media.ToString().ToLower() != reportsReceived.Media.ToString().ToLower())
                            //                isNewReport = true;
                            //            if ((string.IsNullOrWhiteSpace(hrmReport.fileExtensionAndVersion) && !string.IsNullOrWhiteSpace(reportsReceived.FileExtensionAndVersion)) || (!string.IsNullOrWhiteSpace(hrmReport.fileExtensionAndVersion) && string.IsNullOrWhiteSpace(reportsReceived.FileExtensionAndVersion)) || hrmReport.fileExtensionAndVersion != reportsReceived.FileExtensionAndVersion)
                            //                isNewReport = true;
                            //            if (hrmReport.contentCheckSum != hrmContentCheckSum)
                            //                isNewReport = true;

                            //        if (!isNewReport)
                            //            Log.Info(practiceId, $"Not tracked change/Logical Duplicate");
                            //        }
                            //        else
                            //            isNewReport = true;
                            //    }
                            //}

                            if (!isNewReport && !isNewVersion)
                            {
                                Log.Info(practiceId, $"Duplicate HRM report found (Id: {hrmReport.Id})");
                                break;
                            }

                            int officeId = 0;
                            string returnFileName = SaveReportToClinicServer(httpClientFactory, reportFileNameClinic, reportContent, practiceId, context, documentsBLL, ref officeId);
                            if (string.IsNullOrEmpty(returnFileName))
                                return false;

                            if (patientRecordId <= 0)
                            {
                                bool assignToTbdPatient = true;
                                //if (practiceDoctorId != tbdDocPracticeId)
                                {
                                    patientRecordId = AddNewPatient(practiceId, patientRecord.Demographics, autoAddNewPatient);
                                    if (autoAddNewPatient)
                                    {
                                        assignToTbdPatient = false;
                                        string patientFirstName = patientRecord.Demographics.Names.LegalName.FirstName.Part;
                                        string patientLastName = patientRecord.Demographics.Names.LegalName.LastName.Part;
                                        Log.Info(practiceId, $"HRM report will be assigned to patient {patientLastName}, {patientFirstName}");
                                    }
                                }

                                if (assignToTbdPatient)
                                {
                                    patientRecordId = tbdPatientRecordId;
                                    Log.Info(practiceId, $"HRM report will be assigned to patient '{TBDPATIENTFIRSTNAME}'");
                                }
                            }

                            if (isNewReport)
                                Log.Info(practiceId, $"Add new HRM report");
                            else
                                Log.Info(practiceId, $"Add new version");

                            //sample <cdst:LastName>51733^Rinne^Claus^H.^</cdst:LastName>
                            string authorName = string.Empty;
                            if (reportsReceived.AuthorPhysician != null)
                                authorName = (reportsReceived.AuthorPhysician.LastName ?? string.Empty) + (string.IsNullOrEmpty(reportsReceived.AuthorPhysician.FirstName) ? string.Empty : ", " + reportsReceived.AuthorPhysician.FirstName);
                            authorName = Regex.Replace(authorName, @"[\d-]", string.Empty);
                            authorName = authorName.Replace("^", " ").Trim();

                            hrmReport = new Cerebrum.Data.ReportReceived();
                            if (testDate != DateTime.MaxValue)
                                hrmReport.testDate = testDate;
                            hrmReport.reportType = ReportType.HRM;
                            hrmReport.fileSize = 0;
                            hrmReport.fileType = "application/text";
                            //hrmReport.fileName = Path.GetFileName(returnFileName);
                            hrmReport.fileName = Path.GetFileName(hrmFile);
                            hrmReport.userIp = "HRM Import";
                            hrmReport.officeId = officeId;

                            Cerebrum.Data.ReportSendingFacility hrmFacility = null;
                            if (!string.IsNullOrWhiteSpace(reportsReceived.SendingFacility))
                            {
                                hrmFacility = hrmFacilities.Where(a => a.facilityId.ToLower().Replace(" ", string.Empty) == reportsReceived.SendingFacility.ToLower().Replace(" ", string.Empty)).FirstOrDefault();
                                if (hrmFacility == null)
                                {
                                    hrmFacility = new Data.ReportSendingFacility();
                                    hrmFacility.facilityId = reportsReceived.SendingFacility;
                                    hrmFacility.facilityName = reportsReceived.SendingFacility;
                                    hrmFacilities.Add(hrmFacility);
                                    context.ReportSendingFacilities.Add(hrmFacility);
                                    context.SaveChanges();
                                }
                            }

                            string description = string.Empty;
                            if (!string.IsNullOrEmpty(reportsReceived.Class.ToString()))
                            {
                                var reportClass = hrmReportClasses.Where(a => a.name != null && a.name.ToLower().Replace(" ", string.Empty) == reportsReceived.Class.ToString().ToLower().Replace(" ", string.Empty)).FirstOrDefault();
                                if (reportClass == null)
                                {
                                    reportClass = new Cerebrum.Data.HRMReportClass();
                                    reportClass.name = reportsReceived.Class.GetEnumDescription();
                                    hrmReportClasses.Add(reportClass);
                                    context.HRMReportClasses.Add(reportClass);
                                    context.SaveChanges();
                                }
                                hrmReport.reportClassId = reportClass.id;

                                if (!string.IsNullOrWhiteSpace(reportsReceived.SubClass))
                                {
                                    description = reportsReceived.SubClass;
                                    var reportSubclass = (from a in hrmReportSubClasses
                                                          where (a.hrmReportClassId == reportClass.id) && (hrmFacility == null || a.facilityId == hrmFacility.Id) && (a.subClassName != null && a.subClassName.ToLower().Replace(" ", string.Empty) == reportsReceived.SubClass.ToLower().Replace(" ", string.Empty))
                                                          select a).FirstOrDefault();
                                    if (reportSubclass == null)
                                    {
                                        reportSubclass = new Cerebrum.Data.HRMReportSubClass();
                                        reportSubclass.hrmReportClassId = reportClass.id;
                                        reportSubclass.facilityId = hrmFacility == null ? 0 : hrmFacility.Id;
                                        reportSubclass.subClassName = reportsReceived.SubClass;
                                        reportSubclass.accompanyingSubClassName = string.Empty;
                                        hrmReportSubClasses.Add(reportSubclass);
                                        context.HRMReportSubClasses.Add(reportSubclass);
                                        context.SaveChanges();
                                    }
                                    hrmReport.reportSubClassId = reportSubclass.id;
                                }
                            }

                            hrmReport.url = returnFileName;
                            hrmReport.userId = "HRM Import";
                            hrmReport.media = ReportMedia.Download;
                            string reportfileExtension = Path.GetExtension(reportFileNameClinic).Trim('.');
                            if (reportfileExtension.Contains("txt"))
                                hrmReport.reportFormat = ReportFormat.Text;
                            else
                                hrmReport.reportFormat = ReportFormat.Binary;
                            if (!string.IsNullOrWhiteSpace(reportsReceived.FileExtensionAndVersion))
                                hrmReport.fileExtensionAndVersion = reportsReceived.FileExtensionAndVersion;
                            if (eventDate != DateTime.MaxValue)
                                hrmReport.eventDateTime = eventDate;
                            hrmReport.receivedDateTime = DateTime.Now;
                            hrmReport.sourceAuthorPhysician = authorName;
                            //hrmReport.sendingFacilityId = sendingFacility;
                            hrmReport.sendingFacilityId = reportsReceived.SendingFacility;
                            //hrmReport.sendingFacilityReport = reportNumber;
                            hrmReport.sendingFacilityReport = reportsReceived.SendingFacilityReportNumber;
                            if (testDate != DateTime.MaxValue)
                                hrmReport.ObservationDateTime = testDate;
                            //if (reportsReceived.OBRContent != null && reportsReceived.OBRContent[0] != null)
                            //{
                            //    var reportOBRContent = reportsReceived.OBRContent[0];
                            //    if (reportOBRContent.ObservationDateTime != null)
                            //    {
                            //        switch (reportOBRContent.ObservationDateTime.ItemElementName)
                            //        {
                            //            case ItemChoiceType.DateTime:
                            //                DateTime observationDate;
                            //                //if (DateTime.TryParseExact(reportOBRContent.ObservationDateTime.Item.ToString(), DATEFORMATXML, CultureInfo.InvariantCulture, DateTimeStyles.None, out observationDate))
                            //                if (DateTime.TryParse(reportOBRContent.ObservationDateTime.Item.ToString(), out observationDate))
                            //                    hrmReport.ObservationDateTime = observationDate;
                            //                break;
                            //        }
                            //    }

                            //    if (hrmReport.reportClassId != null && hrmReport.reportClassId > 0)
                            //    {
                            //        if (!string.IsNullOrWhiteSpace(reportOBRContent.AccompanyingSubClass))
                            //        {
                            //            var reportAccompanyingSubclass = reportAccompanyingSubclasses.Where(a => a.ReportClassId == hrmReport.reportClassId && a.name != null && a.name.ToLower().Replace(" ", string.Empty) == reportOBRContent.AccompanyingSubClass.ToLower().Replace(" ", string.Empty)).FirstOrDefault();
                            //            if (reportAccompanyingSubclass == null)
                            //                reportAccompanyingSubclass = reportAccompanyingSubclasses.Where(a => a.ReportClassId == hrmReport.reportClassId && a.name != null && a.name.ToLower().Replace(" ", "") == TBDREPORTACCOMPANYINGSUBCLASS.ToLower().Replace(" ", "")).FirstOrDefault();

                            //            if (reportAccompanyingSubclass != null)
                            //            {
                            //                hrmReport.AccompanyingSubClass = reportAccompanyingSubclass.name;

                            //                if (!string.IsNullOrWhiteSpace(reportOBRContent.AccompanyingMnemonic))
                            //                {
                            //                    var reportAccompanyingMnemonic = reportAccompanyingMnemonics.Where(a => a.AccompanyingSubclassId == reportAccompanyingSubclass.Id && a.name != null && a.name.ToLower().Replace(" ", string.Empty) == reportOBRContent.AccompanyingMnemonic.ToLower().Replace(" ", string.Empty)).FirstOrDefault();
                            //                    if (reportAccompanyingMnemonic == null)
                            //                        reportAccompanyingMnemonic = reportAccompanyingMnemonics.Where(a => a.AccompanyingSubclassId == reportAccompanyingSubclass.Id && a.name != null && a.name.ToLower().Replace(" ", "") == TBDREPORTACCOMPANYINGMNEUMONIC.ToLower().Replace(" ", "")).FirstOrDefault();

                            //                    if (reportAccompanyingMnemonic != null)
                            //                        hrmReport.AccompanyingMnemonic = reportAccompanyingMnemonic.name;
                            //                }
                            //            }
                            //        }
                            //    }

                            //    if (!string.IsNullOrWhiteSpace(reportOBRContent.AccompanyingDescription))
                            //        hrmReport.AccompanyingDescription = reportOBRContent.AccompanyingDescription;
                            //}

                            hrmReport.hRMResultStatus = reportsReceived.ResultStatus.ToString();
                            hrmReport.messageUniqueID = messageUniqueId;
                            hrmReport.hrmVersion = messageUniqueId.Split('^')[5];
                            hrmReport.PatientRecordId = patientRecordId;

                            DateTime sentDateTime;

                            string[] msgData = messageUniqueId.Split('^');

                            if (msgData.Length > 5)
                            {
                                string dateStr = messageUniqueId.Split('^')[5];

                                int dateLen = DATEFORMATXML.Length;
                                if (dateStr.Length < dateLen)
                                {
                                    dateStr = dateStr + new string('0', dateLen - dateStr.Length);
                                }
                                if (dateStr.Length > dateLen)
                                {
                                    dateStr = dateStr.Substring(0, dateLen);
                                }

                                if (DateTime.TryParseExact(dateStr, DATEFORMATXML, CultureInfo.InvariantCulture, DateTimeStyles.None, out sentDateTime))
                                    hrmReport.SentDateTime = sentDateTime;
                            }
                            hrmReport.contentCheckSum = hrmContentCheckSum;
                            hrmReport.status = true;

                            DocumentAssignmentStatuses assignmentStatus = DocumentAssignmentStatuses.Unassigned;
                            if (!(patientRecordId == tbdPatientRecordId || practiceDoctorId == tbdPracticeDoctorId))
                            {
                                if (!_requisitionBll.RequisitionExist(patientRecordId))
                                {
                                    assignmentStatus = DocumentAssignmentStatuses.Assigned;
                                    Log.Info(practiceId, "HRM report assigned");
                                }
                            }
                            hrmReport.assignmentStatus = assignmentStatus;

                            if (reportsReceived.EventDateTime != null && !string.IsNullOrEmpty(reportsReceived.EventDateTime.Item.ToString()))
                                hrmReport.creationDate = (DateTime)reportsReceived.EventDateTime.Item;

                            hrmReport.description = description;

                            if (hrmReport.reportClassId == null)
                                hrmReport.looseReportCategoryId = 0;
                            else
                                hrmReport.looseReportCategoryId = GetReportCategoryId(hrmReport.reportClassId.Value, practiceId, hrmFacility == null ? 0 : hrmFacility.Id, reportsReceived);

                            bool isAuthorReceiver = IsAuthorReceiver(authorName, transactionInformation.Provider.FirstName, transactionInformation.Provider.LastName);
                            bool markSeen = (isAuthorReceiver && autoMarkSeenHRMReport);
                            hrmReport.markSeen = markSeen;
                            if (markSeen)
                                hrmReport.seenDateTime = DateTime.Now;

                            context.ReportsReceived.Add(hrmReport);
                            context.SaveChanges();
                            description = string.Empty;

                            if (reportsReceived.OBRContent != null && reportsReceived.OBRContent.Length > 0)
                            {
                                foreach (var reportOBRContent in reportsReceived.OBRContent)
                                {
                                    if (string.IsNullOrWhiteSpace(description))
                                    {
                                        if (string.IsNullOrWhiteSpace(hrmReport.description))
                                            description = reportOBRContent.AccompanyingSubClass;
                                        if (!string.IsNullOrWhiteSpace(reportOBRContent.AccompanyingMnemonic))
                                            description = reportOBRContent.AccompanyingMnemonic;
                                        if (!string.IsNullOrWhiteSpace(reportOBRContent.AccompanyingDescription))
                                            description = reportOBRContent.AccompanyingDescription;
                                    }

                                    Data.HRMReportSubClass reportAccompanyingSubclass = null;
                                    if (!string.IsNullOrWhiteSpace(reportOBRContent.AccompanyingSubClass) && hrmReport.reportClassId != null)
                                    {
                                        reportAccompanyingSubclass = (from a in hrmReportSubClasses
                                                                      where a.hrmReportClassId == hrmReport.reportClassId.Value && (hrmFacility == null || a.facilityId == hrmFacility.Id) && (a.accompanyingSubClassName != null && a.accompanyingSubClassName.ToLower().Replace(" ", string.Empty) == reportOBRContent.AccompanyingSubClass.ToLower().Replace(" ", string.Empty))
                                                                      select a).FirstOrDefault();
                                        if (reportAccompanyingSubclass == null)
                                        {
                                            reportAccompanyingSubclass = new Cerebrum.Data.HRMReportSubClass();
                                            reportAccompanyingSubclass.hrmReportClassId = hrmReport.reportClassId.Value;
                                            reportAccompanyingSubclass.facilityId = hrmFacility == null ? 0 : hrmFacility.Id;
                                            reportAccompanyingSubclass.subClassName = string.Empty;
                                            reportAccompanyingSubclass.accompanyingSubClassName = reportOBRContent.AccompanyingSubClass;
                                            hrmReportSubClasses.Add(reportAccompanyingSubclass);
                                            context.HRMReportSubClasses.Add(reportAccompanyingSubclass);
                                            context.SaveChanges();
                                        }
                                    }

                                    var obrContent = new Data.ReportOBRContent();
                                    obrContent.reportId = hrmReport.Id;
                                    obrContent.accompanyingSubClassId = reportAccompanyingSubclass == null ? 0 : reportAccompanyingSubclass.id;
                                    obrContent.accompanyingMnemonic = reportOBRContent.AccompanyingMnemonic;
                                    obrContent.accompanyingDescription = reportOBRContent.AccompanyingDescription;

                                    DateTime contentObservationDate = GetDateTime(reportOBRContent.ObservationDateTime);
                                    if (contentObservationDate != DateTime.MaxValue)
                                        obrContent.observationDate = contentObservationDate;

                                    context.ReportOBRContent.Add(obrContent);
                                }
                            }

                            if (!string.IsNullOrWhiteSpace(description))
                                hrmReport.description = description;

                            AssignHrmReport(true, practiceId, practiceDoctorId, tbdPracticeDoctorId, hrmReport.Id, hrmCommentNew, Path.GetFileName(hrmFile), assignmentStatus, markSeen);
                            //var doctorReport = context.DoctorsReportsReviewed.Where(a => a.practiceDoctorId == practiceDoctorId && a.ReportReceivedId == hrmReport.Id).FirstOrDefault();
                            //if (doctorReport == null)
                            //{
                            //    context.DoctorsReportsReviewed.Add(new Cerebrum.Data.DoctorsReportReviewed { practiceDoctorId = practiceDoctorId, ReportReceivedId = hrmReport.Id, ReportReviewedNotes = hrmCommentNew });
                            //    if (practiceDoctorId != tbdDocPracticeId)
                            //        Log.Info(practiceId, $"Assigned HRM report to doctor (practice doctor ID: {practiceDoctorId})");
                            //}
                            //else
                            //    Log.Info(practiceId, $"HRM report has been assigned to doctor (practice doctor ID: {practiceDoctorId})");
                        }
                    }

                    context.SaveChanges();
                    transaction.Commit();
                    GlobalContext.Properties["recipient"] = string.Empty;

                    return true;
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    Log.Error(practiceId, Cerebrum.BLL.Utility.UtilityHelper.ExceptionText(ex));

                    if (ex is System.Data.Entity.Validation.DbEntityValidationException)
                    {
                        foreach (var eve in ((System.Data.Entity.Validation.DbEntityValidationException)ex).EntityValidationErrors)
                        {
                            Log.Error(practiceId, $"Entity of type '{eve.Entry.Entity.GetType().Name}' in state '{eve.Entry.State}' has the following validation errors:");
                            foreach (var ve in eve.ValidationErrors)
                            {
                                Log.Error(practiceId, $"- Property: '{ve.PropertyName}', Error: '{ve.ErrorMessage}'");
                            }
                        }
                    }

                    GlobalContext.Properties["recipient"] = string.Empty;

                    return false;
                }
            }
        }

        private void AssignHrmReport(bool assignOnly, int practiceId, int practiceDoctorId, int tbdDocPracticeId, int hrmReportId, string hrmComment, string hrmXmlFileName, DocumentAssignmentStatuses assignmentStatus, bool markSeen = false)
        {
            Cerebrum.Data.DoctorsReportReviewed doctorReport = null;
            if (!assignOnly)
                doctorReport = context.DoctorsReportsReviewed.Where(a => a.practiceDoctorId == practiceDoctorId && a.ReportReceivedId == hrmReportId).FirstOrDefault();
            if (doctorReport == null)
            {
                var report = new Cerebrum.Data.DoctorsReportReviewed { practiceDoctorId = practiceDoctorId, ReportReceivedId = hrmReportId, ReportReviewedNotes = hrmComment, hrmXmlFileName = hrmXmlFileName, assignmentStatus = assignmentStatus };
                if (markSeen)
                    report.dateTimeReportReviewed = DateTime.Now;
                context.DoctorsReportsReviewed.Add(report);
                if (practiceDoctorId != tbdDocPracticeId)
                    Log.Info(practiceId, $"Assigned HRM report to doctor (practice doctor ID: {practiceDoctorId})");
            }
            else
                Log.Info(practiceId, $"HRM report has been assigned to doctor (practice doctor ID: {practiceDoctorId})");
        }

        private bool IsPatientDataMissing(int practiceId, Demographics demographic)
        {
            bool missingData = false;

            //CheckMissingData(officeId, demographic.Names.LegalName.FirstName.Part, "Missing first name", ref missingData);
            CheckMissingData(practiceId, demographic.Names.LegalName.LastName.Part, "Missing last name", ref missingData);
            CheckMissingData(practiceId, demographic.HealthCard.Number, "Missing health card number", ref missingData);
            //CheckMissingData(officeId, demographic.HealthCard.Version, "Missing health card version", ref missingData);
            CheckInvalidDate(practiceId, demographic.DateOfBirth, "Invalid DOB", ref missingData);

            return !missingData;
        }

        private bool IsReportDataCorrect(int practiceId, ReportsReceived reportsReceived)
        {
            var missingData = false;
            if (string.IsNullOrWhiteSpace(reportsReceived.SendingFacility))
                SetMissingData(practiceId, "Missing 'Sending Facility'", ref missingData);
            //else
            //CheckSendingFacility(practiceId, reportsReceived.SendingFacility.Trim(), "Invalid 'Sending Facility'", ref missingData);

            CheckMissingData(practiceId, reportsReceived.SendingFacilityReportNumber, "Missing 'Sending Facility Report Number'", ref missingData);


            //remove this checking as some facility doesn't include author's last name
            //if (reportsReceived.AuthorPhysician == null)
            //    SetMissingData(practiceId, "Missing 'Author Last Name'", ref missingData);
            //else
            //    CheckMissingData(practiceId, reportsReceived.AuthorPhysician.LastName, "Missing 'Author Last Name'", ref missingData);

            if (reportsReceived.EventDateTime != null)
                CheckMissingData(practiceId, reportsReceived.EventDateTime.Item.ToString(), "Missing 'Event DateTime'", ref missingData);

            if (reportsReceived.OBRContent != null && reportsReceived.OBRContent[0].ObservationDateTime != null)
                CheckMissingData(practiceId, reportsReceived.OBRContent[0].ObservationDateTime.Item.ToString(), "Missing 'Observation Date'", ref missingData);

            if (reportsReceived.Content == null)
                SetMissingData(practiceId, "Missing 'Report Content'", ref missingData);
            else
                CheckMissingData(practiceId, reportsReceived.Content.Item.ToString(), "Missing 'Report Content'", ref missingData);

            if (!reportsReceived.ResultStatusSpecified)
                SetMissingData(practiceId, "Missing 'Report Status'", ref missingData);

            CheckReportClass(practiceId, reportsReceived, "Unmatched Report Class", "Unmatched Report Subclass", "Unmatched Accompanying SubClass", "Unmatched Accompanying Mnemonic", ref missingData);
            CheckReportContentType(practiceId, reportsReceived, "Invalid Report Type/Content", ref missingData);

            return !missingData;
        }

        private bool IsTransactionDataCorrect(int practiceId, TransactionInformation transactionInformation)
        {
            bool missingData = false;
            CheckMissingData(practiceId, transactionInformation.MessageUniqueID, "Missing 'MessageUniqueID'", ref missingData);

            if (string.IsNullOrWhiteSpace(transactionInformation.DeliverToUserID))
                SetMissingData(practiceId, "Missing 'CPSO/CNO'", ref missingData);
            else
            {
                if (!(transactionInformation.DeliverToUserID.StartsWith("D") || transactionInformation.DeliverToUserID.StartsWith("N")))
                    SetMissingData(practiceId, "Invalid CPSO/CNO", ref missingData);
            }

            if (transactionInformation.Provider != null)
            {
                if (string.IsNullOrWhiteSpace(transactionInformation.Provider.FirstName))
                    SetMissingData(practiceId, "Missing Provider's FirstName", ref missingData);

                if (string.IsNullOrWhiteSpace(transactionInformation.Provider.LastName))
                    SetMissingData(practiceId, "Missing Provider's LastName", ref missingData);
            }

            return !missingData;
        }

        private bool isDoctorMatched(int practiceId, string doctorFirstName, string doctorLastName, string cpsoId, string cpsoFirstLetter, dynamic doctor)
        {
            bool isMatched = true;

            //if (doctor.firstName.ToLower() != doctorFirstName.ToLower())
            //{
            //    isMatched = false;
            //    Log.Info(practiceId, $"Provider's FirstName doesn't match");
            //}
            //if (doctor.lastName.ToLower() != doctorLastName.ToLower())
            //{
            //    isMatched = false;
            //    Log.Info(practiceId, $"Provider's LastName doesn't match");
            //}
            if (doctor.CPSO != cpsoId)
            {
                isMatched = false;
                Log.Info(practiceId, $"Provider's CPSO/CNO doesn't match");
            }
            if ((cpsoFirstLetter == "d" && doctor.userType != UserTypeEnum.Doctor) || (cpsoFirstLetter == "n" && doctor.userType != UserTypeEnum.Nurse))
            {
                isMatched = false;
                Log.Info(practiceId, $"Provider's type (Doctor/Nurse) doesn't match");
            }

            return isMatched;
        }

        private bool GetDoctorData(int practiceId, TransactionInformation transactionInformation, ref int practiceDoctorId, ref bool autoAddNewPatient, ref bool autoMarkSeenHRMReport)
        {
            string cpsoId = (transactionInformation.DeliverToUserID ?? string.Empty).Trim();
            string doctorFirstName = string.Empty;
            string doctorLastName = string.Empty;
            if (transactionInformation.Provider != null)
            {
                doctorFirstName = transactionInformation.Provider.FirstName;
                doctorLastName = transactionInformation.Provider.LastName;
            }
            string cpsoFirstLetter = cpsoId.Substring(0, 1).ToLower();
            cpsoId = Regex.Replace(cpsoId, "[^0-9.]", "");

            var doctor = (from a in context.ExternalDoctors
                          join f in context.PracticeDoctors on a.Id equals f.ExternalDoctorId
                          let u = context.Users.Where(a => a.Id == f.ApplicationUserId).FirstOrDefault()
                          where a.CPSO == cpsoId && f.PracticeId == practiceId
                          select new { practiceDoctorId = f.Id, f.PracticeId, a.CPSO, a.firstName, a.lastName, f.autoActivateHRMNewPatient, f.autoMarkSeenHRMReport, userType = u == null ? UserTypeEnum.Other : u.CerebrumUserType }).FirstOrDefault();
            if (doctor == null)
                Log.Info(practiceId, $"Cannot find doctor/nurse by CPSO/CNO: {cpsoId} in practice");
            else
            {
                practiceDoctorId = doctor.practiceDoctorId;
                Log.Info(practiceId, $"Doctor/nurse (CPSO/CNO: {cpsoId}) found in practice");
                if (isDoctorMatched(practiceId, doctorFirstName, doctorLastName, cpsoId, cpsoFirstLetter, doctor))
                {
                    autoAddNewPatient = doctor.autoActivateHRMNewPatient;
                    autoMarkSeenHRMReport = doctor.autoMarkSeenHRMReport;
                    return true;
                }
            }

            return false;
        }

        private int IsPatientDataMatched(int practiceId, DateTime testDate, Demographics patient, ref bool errorData)
        {
            int patientRecordId = 0;

            DateTime birthDate = (DateTime)patient.DateOfBirth.Item;
            string patientFirstName = (patient.Names.LegalName.FirstName.Part ?? string.Empty).Trim();
            string patientLastName = patient.Names.LegalName.LastName.Part ?? string.Empty;
            string patientHealthCardNumber = patient.HealthCard.Number;
            var gender = GetGender(patient.Gender.ToString());
            var demographic = new { demographicId = 0, patientRecordId = 0, lastName = string.Empty, dateOfBirth = (DateTime?)DateTime.Now, gender = Gender.O, number = string.Empty };

            if (!string.IsNullOrEmpty(patientHealthCardNumber))
            {
                demographic = (from a in context.HealthCards
                               join b in context.Demographics on a.DemographicId equals b.Id
                               join c in context.PatientRecords on b.PatientRecordId equals c.Id
                               where a.number == patientHealthCardNumber && c.PracticeId == practiceId && b.active == Active.Active
                               orderby a.dateIssued descending
                               select new { demographicId = b.Id, patientRecordId = b.PatientRecordId, b.lastName, b.dateOfBirth, b.gender, a.number }).FirstOrDefault();
                if (demographic != null)
                    patientRecordId = demographic.patientRecordId;
            }

            if (patientRecordId == 0)
            {
                var patients = (from a in context.Demographics
                                join c in context.PatientRecords on a.PatientRecordId equals c.Id
                                join h in context.HealthCards on a.Id equals h.DemographicId
                                where a.active == Active.Active && a.lastName.Equals(patientLastName) && (patientFirstName == string.Empty || a.firstName.Equals(patientFirstName)) && a.gender == gender
                                     && SqlFunctions.DateDiff("DAY", a.dateOfBirth, birthDate) == 0 && c.PracticeId == practiceId
                                select new { demographicId = a.Id, patientRecordId = c.Id, a.lastName, a.dateOfBirth, a.gender, h.number }).ToList();

                if (patients.Count >= 1)
                {
                    demographic = patients[0];
                    patientRecordId = demographic.patientRecordId;

                    if (patients.Count > 1)
                    {
                        var patientRecordIds = patients.Select(t => t.patientRecordId).ToList();
                        var appointment = (from a in context.Appointments
                                           where patientRecordIds.Contains(a.PatientRecordId) && a.appointmentTime < testDate
                                           orderby a.appointmentTime descending
                                           select new
                                           {
                                               a.PatientRecordId
                                           }).FirstOrDefault();
                        if (appointment != null)
                        {
                            var demographic2 = patients.Where(a => a.patientRecordId == appointment.PatientRecordId).FirstOrDefault();
                            if (demographic2 != null)
                            {
                                demographic = demographic2;
                                patientRecordId = demographic2.patientRecordId;
                            }
                        }
                    }
                }
            }

            if (patientRecordId == 0)
                return patientRecordId;

            if (demographic.lastName.ToLower() != patientLastName.ToLower())
                SetMissingData(practiceId, "Patient's lastname doesn't match", ref errorData);
            if (demographic.dateOfBirth != null && demographic.dateOfBirth.Value.Date != birthDate.Date)
                SetMissingData(practiceId, "Patient's birthday doesn't match", ref errorData);
            if (demographic.gender != gender)
                SetMissingData(practiceId, "Patient's gender doesn't match", ref errorData);
            if (demographic.number != patientHealthCardNumber)
                SetMissingData(practiceId, "Patient's healthcard number doesn't match", ref errorData);

            if (!errorData)
                Log.Info(practiceId, $"Patient ({patientLastName}, {patientFirstName}) found (Patient Record Id: {patientRecordId}; Demographic Id: {demographic.demographicId})");

            return patientRecordId;
        }

        private int AddNewPatient(int practiceId, Demographics patient, bool autoAddNewPatient)
        {
            DateTime birthDate = (DateTime)patient.DateOfBirth.Item;
            string patientFirstName = patient.Names.LegalName.FirstName.Part;
            string patientLastName = patient.Names.LegalName.LastName.Part;
            var gender = GetGender(patient.Gender.ToString());
            string patientHealthCardNumber = patient.HealthCard.Number;

            var patientRecordId = 0;
            Cerebrum.Data.Demographic demographic = null;
            if (!string.IsNullOrEmpty(patientHealthCardNumber))
            {
                demographic = (from a in context.HealthCards
                               join b in context.Demographics on a.DemographicId equals b.Id
                               join c in context.PatientRecords on b.PatientRecordId equals c.Id
                               where a.number == patientHealthCardNumber && c.PracticeId == practiceId && (b.active == Active.Active || b.active == Active.Other)
                               orderby a.dateIssued descending
                               select b).FirstOrDefault();
                if (demographic != null)
                    patientRecordId = demographic.PatientRecordId;
            }

            if (patientRecordId == 0)
            {
                demographic = (from a in context.Demographics
                               join c in context.PatientRecords on a.PatientRecordId equals c.Id
                               join h in context.HealthCards on a.Id equals h.DemographicId
                               where (a.active == Active.Active || a.active == Active.Other) && a.lastName.Equals(patientLastName) && (patientFirstName == string.Empty || a.firstName.Equals(patientFirstName)) && a.gender == gender
                                    && SqlFunctions.DateDiff("DAY", a.dateOfBirth, birthDate) == 0 && c.PracticeId == practiceId
                               select a).FirstOrDefault();

                if (demographic != null)
                    patientRecordId = demographic.PatientRecordId;
            }

            if (patientRecordId > 0)
            {
                if (autoAddNewPatient)
                {
                    if (demographic.active == Active.Other)
                        demographic.active = Active.Active;
                }
                Log.Info(practiceId, $"Found patient ({patientLastName}, {patientFirstName})");

                return patientRecordId;
            }

            Log.Info(practiceId, $"Add new patient ({patientLastName}, {patientFirstName})");
            demographic = new Cerebrum.Data.Demographic();
            var patientrecord = new Cerebrum.Data.PatientRecord { PracticeId = practiceId };
            if (birthDate != DateTime.MinValue)
                demographic.dateOfBirth = birthDate;
            demographic.firstName = patientFirstName;
            demographic.lastName = patientLastName;
            //demographic.middleName = MName;
            demographic.namePrefix = Salutation._;
            demographic.gender = gender;
            if (autoAddNewPatient)
                demographic.active = Active.Active;
            else
                demographic.active = Active.Other;

            var patientAddress = new Cerebrum.Data.DemographicsAddress();
            if (patient.Address != null)
            {
                addressstructured address = (addressstructured)patient.Address[0].Item;
                patientAddress.addressLine1 = address.Line1;
                patientAddress.addressLine2 = address.Line2;
                patientAddress.city = address.City;
                string postalCode = address.PostalZipCode.Item;
                if (!string.IsNullOrEmpty(postalCode))
                {
                    postalCode = postalCode.Replace("-", string.Empty);
                    if (postalCode.Length < 7)
                        patientAddress.postalCode = postalCode;
                    else
                        patientAddress.postalCode = postalCode.Substring(0, 6);    // truncate Zip code to 6 - OLIS has 
                }
                patientAddress.province = patient.HealthCard.ProvinceCode.ToString();
                patientAddress.province = patientAddress.province.Substring(patientAddress.province.Length - 2).ToUpper();
                patientAddress.IsActive = true;
                demographic.addresses.Add(patientAddress);
            }

            if (patient.HealthCard != null && !string.IsNullOrEmpty(patient.HealthCard.Number))
            {
                var healthCard = new Cerebrum.Data.DemographicsHealthCard();
                healthCard.number = patient.HealthCard.Number;
                healthCard.version = patient.HealthCard.Version;
                if (!string.IsNullOrEmpty(patientAddress.province))
                {
                    try
                    {
                        healthCard.provinceCode = (Province)Enum.Parse(typeof(Province), "CA" + patientAddress.province);
                    }
                    catch
                    {
                        try
                        {
                            healthCard.provinceCode = (Province)Enum.Parse(typeof(Province), patientAddress.province);
                        }
                        catch
                        {
                        }
                    }
                }
                demographic.healthcards.Add(healthCard);
            }

            if (patient.PhoneNumber != null && patient.PhoneNumber[0].Items != null && !string.IsNullOrEmpty(patient.PhoneNumber[0].Items[0]))
            {
                var phoneNumber = new Cerebrum.Data.DemographicsPhoneNumber();
                if (patient.PhoneNumber[0].Items[0].Length > 20)
                    phoneNumber.phoneNumber = patient.PhoneNumber[0].Items[0].Substring(0, 20);
                else
                    phoneNumber.phoneNumber = patient.PhoneNumber[0].Items[0];
                demographic.phoneNumbers.Add(phoneNumber);
            }

            patientrecord.Demographics.Add(demographic);
            context.PatientRecords.Add(patientrecord);
            context.SaveChanges();

            return patientrecord.Id;
        }

        private Gender GetGender(string genderData)
        {
            var gender = Gender.O;
            switch (genderData.ToUpper())
            {
                case "M":
                case "MALE":
                    gender = Gender.M;
                    break;
                case "F":
                case "FEMALE":
                    gender = Gender.F;
                    break;
                case "U":
                case "UNKNOWN":
                    gender = Gender.U;
                    break;
                case "O":
                case "OTHER":
                    gender = Gender.O;
                    break;
            }

            return gender;
        }

        private void CheckMissingData(int practiceId, string data, string errorMessage, ref bool missingData)
        {
            if (string.IsNullOrWhiteSpace(data))
                SetMissingData(practiceId, errorMessage, ref missingData);
        }

        private void CheckSendingFacility(int practiceId, string facilityId, string errorMessage, ref bool missingData)
        {
            var facility = context.ReportSendingFacilities.Where(a => a.facilityId.ToLower() == facilityId.ToLower()).FirstOrDefault();
            if (facility == null)
                SetMissingData(practiceId, errorMessage, ref missingData);
        }

        private void SetMissingData(int practiceId, string errorMessage, ref bool missingData)
        {
            Log.Error(practiceId, errorMessage);
            missingData = true;
        }

        private void CheckInvalidDate(int practiceId, dateFullOrPartial date, string errorMessage, ref bool missingData)
        {
            switch (date.ItemElementName)
            {
                case ItemChoiceType.FullDate:
                    DateTime dateOut;
                    //Log.Info($"DOB: {date.Item.ToString()}");
                    //if (DateTime.TryParseExact(date.Item.ToString(), DATEFORMATXML, CultureInfo.InvariantCulture, DateTimeStyles.None, out dateOut))
                    if (DateTime.TryParse(date.Item.ToString(), out dateOut))
                    {
                        if (!(dateOut < DateTime.Now.AddDays(1) && dateOut >= DateTime.Now.AddYears(-200)))
                            SetMissingData(practiceId, errorMessage, ref missingData);
                    }
                    else
                        SetMissingData(practiceId, errorMessage, ref missingData);
                    break;
            }
        }
        private void CheckReportClass(int practiceId, ReportsReceived report, string errorMessageClass, string errorMessageSubclass, string errorMessageAccompanyingSubClass, string errorMessageAccompanyingMnemonic, ref bool missingData)
        {
            if (!string.IsNullOrWhiteSpace(report.Class.ToString()))
            {
                var reportClass = hrmReportClasses.Where(a => a.name != null && a.name.ToLower().Replace(" ", string.Empty) == report.Class.ToString().ToLower().Replace(" ", string.Empty)).FirstOrDefault();
                if (reportClass == null)
                    Log.Info(practiceId, $"{errorMessageClass}: {report.Class.ToString()}");
                else
                {
                    if (!string.IsNullOrWhiteSpace(report.SubClass))
                    {
                        var reportSubclass = (from a in hrmReportSubClasses
                                              join b in hrmFacilities on a.facilityId equals b.Id
                                              where a.hrmReportClassId == reportClass.id && a.subClassName != null && a.subClassName.ToLower().Replace(" ", string.Empty) == report.SubClass.ToLower().Replace(" ", string.Empty) &&
                                                     b.facilityId != null && b.facilityId.ToLower().Replace(" ", string.Empty) == report.SendingFacility.ToLower().Replace(" ", string.Empty)
                                              select a).FirstOrDefault();
                        if (reportSubclass == null)
                            Log.Info(practiceId, $"{errorMessageSubclass}: {report.SubClass}");
                    }

                    if (report.OBRContent != null && report.OBRContent.Length > 0)
                    {
                        foreach (var reportOBRContent in report.OBRContent)
                        {
                            if (!string.IsNullOrWhiteSpace(reportOBRContent.AccompanyingSubClass))
                            {
                                var reportAccompanyingSubclass = (from a in hrmReportSubClasses
                                                                  join b in hrmFacilities on a.facilityId equals b.Id
                                                                  where a.hrmReportClassId == reportClass.id && a.accompanyingSubClassName != null && a.accompanyingSubClassName.ToLower().Replace(" ", string.Empty) == reportOBRContent.AccompanyingSubClass.ToLower().Replace(" ", string.Empty) &&
                                                                         b.facilityId != null && b.facilityId.ToLower().Replace(" ", string.Empty) == report.SendingFacility.ToLower().Replace(" ", string.Empty)
                                                                  select a).FirstOrDefault();
                                if (reportAccompanyingSubclass == null)
                                    Log.Info(practiceId, $"{errorMessageAccompanyingSubClass}: {reportOBRContent.AccompanyingSubClass}");
                            }
                        }
                    }
                }
            }
        }

        private void CheckReportContentType(int practiceId, ReportsReceived report, string errorMessage, ref bool missingData)
        {
            if (report.Content == null && report.Content.Item == null)
                return;

            string reportType = report.FileExtensionAndVersion.Replace(".", string.Empty).ToLower();
            string reportContentText = string.Empty;
            byte[] reportContentByte = null;
            try
            {
                if (report.Format == reportFormat.Text)
                {
                    reportContentText = report.Content.Item.ToString();
                    reportContentByte = Encoding.UTF8.GetBytes(reportContentText);
                }
                else
                {
                    reportContentByte = (byte[])report.Content.Item;
                    reportContentText = Encoding.UTF8.GetString(reportContentByte);
                }
            }
            catch (Exception ex)
            {
                Log.Info(practiceId, $"File reading error: {ex.Message}");
                SetMissingData(practiceId, errorMessage, ref missingData);
                return;
            }

            switch (reportType)
            {
                case "html":
                    if (!IsHtml(reportContentText))
                        SetMissingData(practiceId, errorMessage, ref missingData);
                    break;
                case "xml":
                    if (!IsXml(reportContentText))
                        SetMissingData(practiceId, errorMessage, ref missingData);
                    break;
                default:
                    var reportFormat = FormatDetector.Detect(reportContentByte);
                    switch (reportType)
                    {
                        case "pdf":
                            if (reportFormat != FormatDetector.Format.PDF)
                                SetMissingData(practiceId, errorMessage, ref missingData);
                            break;
                        case "tiff":
                            if (!(reportFormat == FormatDetector.Format.TIFF || reportFormat == FormatDetector.Format.TIFFb || reportFormat == FormatDetector.Format.TIFFl))
                                SetMissingData(practiceId, errorMessage, ref missingData);
                            break;
                        case "jpeg":
                            if (reportFormat != FormatDetector.Format.JPEG)
                                SetMissingData(practiceId, errorMessage, ref missingData);
                            break;
                        case "gif":
                            if (!(reportFormat == FormatDetector.Format.GIF || reportFormat == FormatDetector.Format.GIF87 || reportFormat == FormatDetector.Format.GIF89))
                                SetMissingData(practiceId, errorMessage, ref missingData);
                            break;
                        case "png":
                            if (reportFormat != FormatDetector.Format.PNG)
                                SetMissingData(practiceId, errorMessage, ref missingData);
                            break;
                        case "rtf":
                            if (reportFormat != FormatDetector.Format.RTF)
                                SetMissingData(practiceId, errorMessage, ref missingData);
                            break;
                    }
                    break;
            }
        }

        private bool IsHtml(string data)
        {
            string content = data.ToLower();
            if (!(content.Contains("<html>") && content.Contains("</html>")))
                return false;
            if (!(content.Contains("<body") && content.Contains("</body>")))
                return false;

            return true;
        }

        private bool IsXml(string data)
        {
            try
            {
                XDocument xd = new XDocument();
                xd = XDocument.Parse(data);
                return true;
            }
            catch
            {
                return false;
            }
        }

        //private bool IsPdf(string data)
        //{
        //    string content = data.ToLower();
        //    if (content.StartsWith("%pdf"))
        //        return true;

        //    return false;
        //}

        //private bool IsTiff(byte[] data)
        //{
        //    if (data.Length > 3 && data[0] == 73 && data[1] == 73 && data[2] == 42 && data[3] == 0)
        //        return true;

        //    return false;
        //}


        /* parse report filename
        * All files awaiting EMR downloading will take the following format:
        * <ProcessedDate>_<sendingFacility>_<reportType>_<reportNumber>_<messageDate>_<cpsoID>.xml
        */

        private void LoadReportClasses(int practiceId)
        {
            hrmReportClasses = context.HRMReportClasses.ToList();
            hrmReportSubClasses = context.HRMReportSubClasses.ToList();
            hrmFacilities = context.ReportSendingFacilities.ToList();
            reportSubClassMaps = context.ReportSubclassMap.Where(a => a.status && (a.practiceId == 0 || a.practiceId == practiceId)).ToList();
            //reportAccompanyingSubclasses = context.ReportAccompanyingSubclasses.ToList();
            //reportAccompanyingMnemonics = context.ReportAccompanyingMnemonics.ToList();
        }

        //private void CopyDefaultSubclassMapping(int practiceId)
        //{
        //    var defaultMappings = context.ReportSubclassMap.Where(a => a.practiceId == 0).ToList();
        //    if (defaultMappings.Count > 0)
        //    {
        //        var practiceMappings = context.ReportSubclassMap.Where(a => a.practiceId == practiceId && a.status).ToList();
        //        foreach (var defaultMapping in defaultMappings)
        //        {
        //            var practiceMapping = practiceMappings.Where(a => a.originalSubClassIds == defaultMapping.originalSubClassIds && a.status).FirstOrDefault();
        //            if (practiceMapping == null)
        //            {
        //                practiceMapping = new Data.ReportSubclassMap();
        //                practiceMapping.practiceId = practiceId;
        //                practiceMapping.originalSubClassIds = defaultMapping.originalSubClassIds;
        //                practiceMapping.looseReportCategoryId = defaultMapping.looseReportCategoryId;
        //                practiceMapping.status = true;
        //                context.ReportSubclassMap.Add(practiceMapping);
        //            }
        //        }

        //        context.SaveChanges();
        //    }
        //}

        private int LoadTbdDoctorPatient(int practiceId)
        {
            int tbdPracticeDoctorId = -1;
            tbdPatientRecordId = -1;

            var tbdDoctor = (from a in context.ExternalDoctors
                             join b in context.PracticeDoctors on a.Id equals b.ExternalDoctorId
                             where a.firstName == TBDDOCTORFIRSTNAME && b.PracticeId == practiceId
                             select new { practiceDoctorId = b.Id }
                             ).FirstOrDefault();
            if (tbdDoctor != null)
                tbdPracticeDoctorId = tbdDoctor.practiceDoctorId;

            var tbdPatient = (from a in context.Demographics
                              join b in context.PatientRecords on a.PatientRecordId equals b.Id
                              where a.firstName == TBDPATIENTFIRSTNAME && b.PracticeId == practiceId
                              select new { patientRecordId = b.Id }
                                    ).FirstOrDefault();
            if (tbdPatient != null)
                tbdPatientRecordId = tbdPatient.patientRecordId;

            return tbdPracticeDoctorId;
        }

        private void ReadXmlSchema()
        {
            string folder = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            //reportManagerSchema = File.ReadAllText("report_manager.xsd");
            //reportManagerDtSchema = File.ReadAllText("report_manager_dt.xsd");
            reportManagerSchema = File.ReadAllText(Path.Combine(folder, "report_manager.xsd"));
            reportManagerDtSchema = File.ReadAllText(Path.Combine(folder, "report_manager_dt.xsd"));
        }

        private string SaveReportToClinicServer(IHttpClientFactory httpClientFactory, string fileName, byte[] fileBytes, int practiceId, Cerebrum.Data.CerebrumContext context, Cerebrum.BLL.Documents.DocumentsBLL documentsBLL, ref int officeId)
        {
            Log.Info(practiceId, $"Saving hrm file to practice (id: {practiceId}) ...");
            try
            {
                var office = (from o in context.Offices
                              where o.PracticeId == practiceId
                              orderby o.status, o.Id
                              select new { o.Id }).FirstOrDefault();
                if (office == null)
                {
                    Log.Error(practiceId, $"Cannot find office for practice (id: {practiceId})");
                    emailMessages.Add($"Cannot find office for practice (id: {practiceId})");
                    return string.Empty;
                }
                officeId = office.Id;

                string clinicFileName = "HRM" + Path.DirectorySeparatorChar + DateTime.Now.Year.ToString() + Path.DirectorySeparatorChar + DateTime.Now.Month.ToString() + Path.DirectorySeparatorChar + DateTime.Now.Day.ToString() + Path.DirectorySeparatorChar + fileName;
                string fileNameSaved = documentsBLL.FileSendToClinic(httpClientFactory, 1, officeId, clinicFileName, fileBytes, false);

                if (!string.IsNullOrWhiteSpace(fileNameSaved))
                {
                    Log.Info(practiceId, $"Saving hrm file to office (id: {officeId}): done");
                    return fileNameSaved;
                }
            }
            catch (Exception ex)
            {
                Log.Error(practiceId, $"{ex.Message}");
            }

            Log.Error(practiceId, $"Cannot save hrm file to office (id: {officeId})");
            emailMessages.Add($"Cannot save hrm file to office (id: {officeId})");
            return string.Empty;
        }

        private string GetCheckSum(byte[] data)
        {
            using (var md5 = System.Security.Cryptography.MD5.Create())
            {
                byte[] checkSum = md5.ComputeHash(data);

                StringBuilder sBuilder = new StringBuilder();
                for (int i = 0; i < checkSum.Length; i++)
                    sBuilder.Append(checkSum[i].ToString("x2"));

                return sBuilder.ToString();
            }
        }

        private string GetCheckSum(string content)
        {
            string data = content;
            int pos1 = content.IndexOf("<MessageUniqueID>");
            int pos2 = content.IndexOf("</MessageUniqueID>");
            if (pos1 >= 0 && pos2 >= 0) //remove "MessageUniqueID" for calculating checksum
                data = content.Remove(pos1, pos2 - pos1 + "</MessageUniqueID>".Length);

            return GetCheckSum(Encoding.UTF8.GetBytes(data));
        }

        private DateTime GetDateTime(dateFullOrPartial data)
        {
            DateTime newDate = DateTime.MaxValue;
            if (data != null)
            {
                switch (data.ItemElementName)
                {
                    case ItemChoiceType.DateTime:
                        if (!DateTime.TryParse(data.Item.ToString(), out newDate))
                            newDate = DateTime.MaxValue;
                        break;
                    case ItemChoiceType.FullDate:
                        if (!DateTime.TryParse(data.Item.ToString(), out newDate))
                            newDate = DateTime.MaxValue;
                        break;
                }
            }

            return newDate;
        }

        private bool IsSameDate(DateTime? date, dateFullOrPartial data)
        {
            var date2 = GetDateTime(data);
            if (date == null)
            {
                if (date2 == DateTime.MaxValue)
                    return true;
                else
                    return false;
            }

            if (date.Value.Date == date2.Date)
                return true;

            return false;
        }

        private bool IsSamePatient(int patientRecordId, PatientRecord patientRecord)
        {
            if (patientRecord.Demographics == null)
                return false;

            var patient = (from a in context.Demographics
                           let b = context.HealthCards.Where(s => s.DemographicId == a.Id).OrderByDescending(s => s.dateIssued).FirstOrDefault()
                           where a.PatientRecordId == patientRecordId
                           select new
                           {
                               a.lastName,
                               healthNUmber = b == null ? string.Empty : b.number,
                               a.gender,
                               a.dateOfBirth
                           }).FirstOrDefault();
            if (patient == null)
                return false;

            var demographic = patientRecord.Demographics;
            if (string.IsNullOrWhiteSpace(patient.lastName) || demographic.Names == null || demographic.Names.LegalName == null || demographic.Names.LegalName.LastName == null || string.IsNullOrWhiteSpace(demographic.Names.LegalName.LastName.Part) || patient.lastName.ToLower() != demographic.Names.LegalName.LastName.Part.ToLower())
                return false;

            if (string.IsNullOrWhiteSpace(patient.healthNUmber) || demographic.HealthCard == null || patient.healthNUmber != demographic.HealthCard.Number)
                return false;

            if (patient.gender.ToString().ToLower() != demographic.Gender.ToString().ToLower())
                return false;

            if (!IsSameDate(patient.dateOfBirth, demographic.DateOfBirth))
                return false;

            return true;
        }

        private bool IsSameReportInfo(Cerebrum.Data.ReportReceived cerebrumHrmReport, ReportsReceived reportsReceived)
        {
            if (cerebrumHrmReport.reportClassId == null)
                return false;
            var reportClass = context.HRMReportClasses.Where(a => a.id == cerebrumHrmReport.reportClassId).FirstOrDefault();
            if (reportClass == null || reportClass.name.ToLower().Replace(" ", string.Empty) != reportsReceived.Class.ToString().ToLower().Replace(" ", string.Empty))
                return false;

            if (reportClass == null)
                return true;

            if ((cerebrumHrmReport.reportSubClassId == null && !string.IsNullOrWhiteSpace(reportsReceived.SubClass)) || (cerebrumHrmReport.reportSubClassId != null && string.IsNullOrWhiteSpace(reportsReceived.SubClass)))
                return false;
            if (cerebrumHrmReport.reportSubClassId != null)
            {
                var reportSubClass = context.HRMReportSubClasses.Where(a => a.hrmReportClassId == reportClass.id && a.id == cerebrumHrmReport.reportSubClassId).FirstOrDefault();
                if (reportSubClass == null || reportSubClass.subClassName.ToLower().Replace(" ", string.Empty) != reportsReceived.SubClass.ToString().ToLower().Replace(" ", string.Empty))
                    return false;
            }

            var obrContents = context.ReportOBRContent.Where(a => a.reportId == cerebrumHrmReport.Id).ToList();
            if ((reportsReceived.OBRContent == null && obrContents.Count > 0) || (reportsReceived.OBRContent != null && obrContents.Count == 0))
                return false;

            if (reportsReceived.OBRContent == null)
                return true;

            if (reportsReceived.OBRContent.Length != obrContents.Count)
                return false;

            bool allMatched = true;
            foreach (var obrContent in obrContents)
            {
                bool isMatched = false;
                foreach (var xmlObrContent in reportsReceived.OBRContent)
                {
                    if ((obrContent.accompanyingSubClassId == 0 && !string.IsNullOrWhiteSpace(xmlObrContent.AccompanyingSubClass)) || (obrContent.accompanyingSubClassId != 0 && string.IsNullOrWhiteSpace(xmlObrContent.AccompanyingSubClass)))
                        continue;
                    if (obrContent.accompanyingSubClassId > 0)
                    {
                        var accompanyingSubClass = context.HRMReportSubClasses.Where(a => a.hrmReportClassId == reportClass.id && a.id == obrContent.accompanyingSubClassId).FirstOrDefault();
                        if (accompanyingSubClass == null || accompanyingSubClass.accompanyingSubClassName.ToLower().Replace(" ", string.Empty) != xmlObrContent.AccompanyingSubClass.ToLower().Replace(" ", string.Empty))
                            continue;
                    }

                    if ((string.IsNullOrWhiteSpace(obrContent.accompanyingMnemonic) && !string.IsNullOrWhiteSpace(xmlObrContent.AccompanyingMnemonic)) || (!string.IsNullOrWhiteSpace(obrContent.accompanyingMnemonic) && string.IsNullOrWhiteSpace(xmlObrContent.AccompanyingMnemonic)))
                        continue;
                    if (!string.IsNullOrWhiteSpace(obrContent.accompanyingMnemonic) && obrContent.accompanyingMnemonic.ToLower().Replace(" ", string.Empty) != xmlObrContent.AccompanyingMnemonic.ToLower().Replace(" ", string.Empty))
                        continue;

                    if ((string.IsNullOrWhiteSpace(obrContent.accompanyingDescription) && !string.IsNullOrWhiteSpace(xmlObrContent.AccompanyingDescription)) || (!string.IsNullOrWhiteSpace(obrContent.accompanyingDescription) && string.IsNullOrWhiteSpace(xmlObrContent.AccompanyingDescription)))
                        continue;
                    if (!string.IsNullOrWhiteSpace(obrContent.accompanyingDescription) && obrContent.accompanyingDescription.ToLower().Replace(" ", string.Empty) != xmlObrContent.AccompanyingDescription.ToLower().Replace(" ", string.Empty))
                        continue;

                    if (!IsSameDate(obrContent.observationDate, xmlObrContent.ObservationDateTime))
                        continue;

                    isMatched = true;
                    break;
                }

                if (!isMatched)
                {
                    allMatched = false;
                    break;
                }
            }

            return allMatched;
        }

        private bool IsSameVersion(Cerebrum.Data.ReportReceived hrmReport, ReportsReceived reportsReceived, string hrmContentCheckSum, string messageUniqueId)
        {
            if (hrmReport.messageUniqueID.Split('^')[5] != messageUniqueId.Split('^')[5])
                return false;
            if (!IsSameReportInfo(hrmReport, reportsReceived))
                return false;
            if (hrmReport.hRMResultStatus.ToLower() != reportsReceived.ResultStatus.ToString().ToLower())
                return false;
            if (!IsSameDate(hrmReport.eventDateTime, reportsReceived.EventDateTime))
                return false;
            if (hrmReport.media == null || hrmReport.media.ToString().ToLower() != reportsReceived.Media.ToString().ToLower())
                return false;
            if ((string.IsNullOrWhiteSpace(hrmReport.fileExtensionAndVersion) && !string.IsNullOrWhiteSpace(reportsReceived.FileExtensionAndVersion)) || (!string.IsNullOrWhiteSpace(hrmReport.fileExtensionAndVersion) && string.IsNullOrWhiteSpace(reportsReceived.FileExtensionAndVersion)) || hrmReport.fileExtensionAndVersion != reportsReceived.FileExtensionAndVersion)
                return false;
            if (hrmReport.contentCheckSum != hrmContentCheckSum)
                return false;

            return true;
        }

        private bool IsAuthorReceiver(string authorName, string firstName, string lastName)
        {
            if (string.IsNullOrWhiteSpace(firstName) || string.IsNullOrWhiteSpace(lastName))
                return false;

            if (authorName.ToLower().Contains(firstName.ToLower()) && authorName.ToLower().Contains(lastName.ToLower()))
                return true;

            return false;
        }

        private int GetReportCategoryId(int hrmReportClassId, int practiceId, int facilityId, ReportsReceived reportsReceived)
        {
            int reportCategoryId = 0;

            if (!string.IsNullOrWhiteSpace(reportsReceived.SubClass))
            {
                reportCategoryId = GetReportCategoryId(hrmReportClassId, practiceId, facilityId, reportsReceived.SubClass);
                if (reportCategoryId <= 0)
                {
                    reportCategoryId = GetReportCategoryId(hrmReportClassId, practiceId, 0, reportsReceived.SubClass);
                }
            }
            else
            {
                if (reportsReceived.OBRContent != null && reportsReceived.OBRContent.Length > 0)
                {
                    reportCategoryId = GetReportCategoryId(hrmReportClassId, practiceId, facilityId, reportsReceived.OBRContent);
                    if (reportCategoryId <= 0)
                    {
                        reportCategoryId = GetReportCategoryId(hrmReportClassId, practiceId, 0, reportsReceived.OBRContent);
                    }
                }
            }

            return reportCategoryId;
        }

        private int GetReportCategoryId(int hrmReportClassId, int practiceId, int facilityId, ReportsReceivedOBRContent[] reportOBRContents)
        {
            bool notFoundAccompanySubClass = false;
            List<int> accompanySubClassIds = new List<int>();
            foreach (var reportOBRContent in reportOBRContents)
            {
                Data.HRMReportSubClass reportAccompanyingSubclass = null;
                if (!string.IsNullOrWhiteSpace(reportOBRContent.AccompanyingSubClass))
                {
                    reportAccompanyingSubclass = (from a in hrmReportSubClasses
                                                  where (a.hrmReportClassId == hrmReportClassId && a.facilityId == facilityId && (a.accompanyingSubClassName != null && a.accompanyingSubClassName.ToLower().Replace(" ", string.Empty) == reportOBRContent.AccompanyingSubClass.ToLower().Replace(" ", string.Empty)))
                                                  select a).FirstOrDefault();
                    if (reportAccompanyingSubclass == null)
                    {
                        notFoundAccompanySubClass = true;
                        break;
                    }
                    accompanySubClassIds.Add(reportAccompanyingSubclass.id);
                }
            }

            if (notFoundAccompanySubClass || accompanySubClassIds.Count == 0)
                return 0;

            accompanySubClassIds.Sort();
            return GetReportCategoryId(practiceId, string.Join(",", accompanySubClassIds));
        }

        private int GetReportCategoryId(int hrmReportClassId, int practiceId, int facilityId, string subClass)
        {
            var reportSubclass = (from a in hrmReportSubClasses
                                  where a.hrmReportClassId == hrmReportClassId && a.facilityId == facilityId && (a.subClassName != null && a.subClassName.ToLower().Replace(" ", string.Empty) == subClass.ToLower().Replace(" ", string.Empty))
                                  select a).FirstOrDefault();
            if (reportSubclass != null)
            {
                return GetReportCategoryId(practiceId, reportSubclass.id.ToString());
            }

            return 0;
        }

        private int GetReportCategoryId(int practiceId, string originalSubClassIds)
        {
            var reportSubclassMap = reportSubClassMaps.Where(a => a.practiceId == practiceId && a.originalSubClassIds == originalSubClassIds).FirstOrDefault();
            if (reportSubclassMap != null)
                return reportSubclassMap.looseReportCategoryId;

            reportSubclassMap = reportSubClassMaps.Where(a => a.practiceId == 0 && a.originalSubClassIds == originalSubClassIds).FirstOrDefault();
            if (reportSubclassMap != null)
                return reportSubclassMap.looseReportCategoryId;

            return 0;
        }

        private string Normalize(string fileName, string fileExtension)
        {
            if (string.IsNullOrWhiteSpace(fileName)) return "";
            Regex rgx = new Regex("[^a-zA-Z0-9_~-]");
            return rgx.Replace(fileName, "_") + fileExtension;
        }
    }
}