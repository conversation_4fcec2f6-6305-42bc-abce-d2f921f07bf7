﻿using Cerebrum.BLL.Utility;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Configuration;
using System.Configuration.Install;
using System.Net.Http;
using System.Reflection;
using System.ServiceProcess;

namespace Cerebrum.HRMImport
{
    static class Program
    {
        static private int queryPracticeId = -1;
        ///// <summary>
        ///// The main entry point for the application.
        ///// </summary>
        static void Main(string[] args)
        {
            try
            {
                Log.ResetCustomProperties();
                IHttpClientFactory httpClientFactory = new ServiceCollection()
                    .AddHttpClient()
                    .BuildServiceProvider()
                    .GetService<IHttpClientFactory>();
                string request = string.Empty;
                string serviceName = ConfigurationManager.AppSettings["ServiceName"];
                if(args.Length > 0)
                {
                    request = args[0].Trim().ToLower();
                }
                if(args.Length > 1)
                {
                    if (!int.TryParse(args[1], out queryPracticeId))
                    {
                        queryPracticeId = -1;
                    }
                }

                if(string.IsNullOrWhiteSpace(request))   // run as service
                {
                    ServiceBase[] ServicesToRun;
                    ServicesToRun = new ServiceBase[]
                    {
                        new Service()
                    };
                    ServiceBase.Run(ServicesToRun);
                    return;
                }

                if(request.Contains("uninstall") || request == "/u")
                {
                    UninstallService(serviceName);
                    return;
                }

                if(request.Contains("install") || request == "/i")
                {
                    InstallService(serviceName);
                    return;
                }

                if(request.Contains("singlerun") || request == "/s")   // one time run
                {
                    TestService(httpClientFactory, "download decrypt import", queryPracticeId);
                    return;
                }

                if(request.Contains("multiplerun") || request == "/m")   // multiple times run (loop)
                {
                    Log.Info("multiple times run (loop)");
                    Service service = new Service();
                    service.Test();
                    return;
                }

                if(request.Contains(HrmService.DOWNLOAD) || request == "/d")
                {
                    Worker.TestProcess(httpClientFactory, HrmService.DOWNLOAD, queryPracticeId);
                    return;
                }

                if(request.Contains(HrmService.DECRYPT) || request == "/c")
                {
                    Worker.TestProcess(httpClientFactory, HrmService.DECRYPT, queryPracticeId);
                    return;
                }

                if(request.Contains(HrmService.IMPORT) || request == "/p")
                {
                    Worker.TestProcess(httpClientFactory, HrmService.IMPORT, queryPracticeId);
                    return;
                }

                if(request.Contains(HrmService.HELP))
                {

                    Console.WriteLine("Error: missing/wrong parameter");
                    Console.WriteLine("Usage 1: Cerebrum.HRMImport.exe /Install (/i)");
                    Console.WriteLine("Usage 2: Cerebrum.HRMImport.exe /Uninstall (/u)");
                    Console.WriteLine("Usage 3: Cerebrum.HRMImport.exe /SingleRun (/s)");
                    Console.WriteLine("Usage 4: Cerebrum.HRMImport.exe /MultipleRun (/m)");
                    Console.WriteLine("Usage 6: Cerebrum.HRMImport.exe /Download 10 (/d)"); //for practice 10
                    Console.WriteLine("Usage 7: Cerebrum.HRMImport.exe /Decrypt 10 (/c)"); //for practice 10
                    Console.WriteLine("Usage 8: Cerebrum.HRMImport.exe /Import 10 (/p)"); //for practice 10
                }
                else
                {
                    Log.Error("Error: missing/wrong parameter");
                    Log.Error("Usage 1: Cerebrum.HRMImport.exe /Install");
                    Log.Error("Usage 2: Cerebrum.HRMImport.exe /Uninstall");
                    Log.Error("Usage 3: Cerebrum.HRMImport.exe /SingleRun");
                    Log.Error("Usage 4: Cerebrum.HRMImport.exe /MultipleRun");
                    Log.Error("Usage 6: Cerebrum.HRMImport.exe /Download");
                    Log.Error("Usage 7: Cerebrum.HRMImport.exe /Decrypt");
                    Log.Error("Usage 8: Cerebrum.HRMImport.exe /Import");
                }
            }
            catch(Exception ex)
            {
                Log.Error($"Error: {ex.Message}");
            }
        }

        private static void TestService(IHttpClientFactory httpClientFactory, string stepName, int queryPracticeId)
        {
            Log.Info("One time run");
            Worker.TestProcess(httpClientFactory, stepName, queryPracticeId);
        }

        private static void InstallService(string name)
        {
            ServiceParams.ServiceName = name;
            ManagedInstallerClass.InstallHelper(new string[] { Assembly.GetExecutingAssembly().Location });
        }
        private static void UninstallService(string name)
        {
            ServiceParams.ServiceName = name;
            ManagedInstallerClass.InstallHelper(new string[] { "/u", Assembly.GetExecutingAssembly().Location });
        }
    }
}
