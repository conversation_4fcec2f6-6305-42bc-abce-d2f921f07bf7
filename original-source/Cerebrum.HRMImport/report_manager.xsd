<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2006 rel. 3 sp2 (http://www.altova.com) by (OntarioMD) -->
<!-- HRM-XSD Schema v1.1.2 / EMR 4.1 / Publish Date: August 14, 2013 -->
<xs:schema xmlns="cds" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:cdsd="cds_dt" targetNamespace="cds" elementFormDefault="qualified" attributeFormDefault="qualified">
	<xs:import namespace="cds_dt" schemaLocation="report_manager_dt.xsd"/>
	<xs:element name="OmdCds">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="PatientRecord"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="PatientRecord">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="Demographics"/>
				<xs:element ref="ReportsReceived" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element ref="TransactionInformation" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="Demographics">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Names" type="cdsd:personNameStandard"/>
				<xs:element name="DateOfBirth" type="cdsd:dateFullOrPartial"/>
				<xs:element name="HealthCard" type="cdsd:healthCard" minOccurs="0"/>
				<xs:element name="ChartNumber" minOccurs="0">
					<xs:simpleType>
						<xs:restriction base="xs:token">
							<xs:maxLength value="15"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="Gender" type="cdsd:gender"/>
				<xs:element name="UniqueVendorIdSequence" type="cdsd:vendorId"/>
				<xs:element name="Address" type="cdsd:address" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="PhoneNumber" type="cdsd:phoneNumber" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="PreferredPhone" type="cdsd:phoneNumberType" minOccurs="0"/>
				<xs:element name="PreferredOfficialLanguage" type="cdsd:officialSpokenLanguageCode" minOccurs="0"/>
				<xs:element name="PreferredSpokenLanguage" type="cdsd:spokenLanguageCode" minOccurs="0"/>
				<xs:element name="Contact" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Name" type="cdsd:personNameSimpleWithMiddleName"/>
							<xs:element name="PhoneNumber" type="cdsd:phoneNumber" minOccurs="0" maxOccurs="unbounded"/>
							<xs:element name="EmailAddress" type="cdsd:emailAddress" minOccurs="0"/>
							<xs:element name="Note" minOccurs="0">
								<xs:simpleType>
									<xs:restriction base="cdsd:text">
										<xs:maxLength value="200"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="ContactPurpose" type="cdsd:contactPersonPurpose" use="required"/>
					</xs:complexType>
				</xs:element>
				<xs:element name="NoteAboutPatient" type="cdsd:text64K" minOccurs="0"/>
				<xs:element name="PatientWarningFlags" minOccurs="0">
					<xs:simpleType>
						<xs:restriction base="cdsd:patientWarningFlag"/>
					</xs:simpleType>
				</xs:element>
				<xs:element name="EnrollmentStatus" type="cdsd:enrollmentStatus" minOccurs="0"/>
				<xs:element name="EnrollmentDate" type="cdsd:dateFullOrPartial" minOccurs="0"/>
				<xs:element name="EnrollmentTerminationDate" type="cdsd:dateFullOrPartial" minOccurs="0"/>
				<xs:element name="TerminationReason" type="cdsd:terminationReasonCode" minOccurs="0"/>
				<xs:element name="PrimaryPhysician" minOccurs="0">
					<xs:complexType>
						<xs:all>
							<xs:element name="Name" type="cdsd:personNameSimple" minOccurs="0"/>
							<xs:element name="OHIPPhysicianId" minOccurs="0">
								<xs:simpleType>
									<xs:restriction base="cdsd:ohipPhysicianBillingNumber"/>
								</xs:simpleType>
							</xs:element>
						</xs:all>
					</xs:complexType>
				</xs:element>
				<xs:element name="Email" type="cdsd:emailAddress" minOccurs="0"/>
				<xs:element name="FamilyMemberLink" type="cdsd:vendorId" minOccurs="0"/>
				<xs:element name="PersonStatusCode" type="cdsd:personStatus"/>
				<xs:element name="PersonStatusDate" type="cdsd:dateFullOrPartial" minOccurs="0"/>
				<xs:element name="SIN" type="cdsd:socialInsuranceNumber" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ReportsReceived">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Media" type="cdsd:reportMedia" minOccurs="0"/>
				<xs:element name="Format" type="cdsd:reportFormat" minOccurs="0"/>
				<xs:element name="FileExtensionAndVersion" type="cdsd:reportFileTypeAndVersion"/>
				<xs:element name="Content" type="cdsd:reportContent" minOccurs="0"/>
				<xs:element name="Class" type="cdsd:reportClass" minOccurs="0"/>
				<xs:element name="SubClass" type="cdsd:reportSubClass" minOccurs="0"/>
				<xs:element name="EventDateTime" type="cdsd:dateFullOrPartial" minOccurs="0"/>
				<xs:element name="ReceivedDateTime" type="cdsd:dateFullOrPartial" minOccurs="0"/>
				<xs:element name="ReviewedDateTime" type="cdsd:dateFullOrPartial" minOccurs="0"/>
				<xs:element name="AuthorPhysician" type="cdsd:personNameSimple" minOccurs="0"/>
				<xs:element name="ReviewingOHIPPhysicianId" type="cdsd:ohipPhysicianBillingNumber" minOccurs="0"/>
				<xs:element name="SendingFacility" minOccurs="0">
					<xs:simpleType>
						<xs:restriction base="xs:token">
							<xs:maxLength value="4"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="SendingFacilityReportNumber" minOccurs="0">
					<xs:simpleType>
						<xs:restriction base="cdsd:text">
							<xs:maxLength value="75"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="OBRContent" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="AccompanyingSubClass" minOccurs="0">
								<xs:simpleType>
									<xs:restriction base="cdsd:text">
										<xs:maxLength value="60"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="AccompanyingMnemonic" minOccurs="0">
								<xs:simpleType>
									<xs:restriction base="cdsd:text">
										<xs:maxLength value="200"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="AccompanyingDescription" minOccurs="0">
								<xs:simpleType>
									<xs:restriction base="cdsd:text">
										<xs:maxLength value="200"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="ObservationDateTime" type="cdsd:dateFullOrPartial" minOccurs="0"/>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="ResultStatus" minOccurs="0">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:maxLength value="1"/>
							<xs:enumeration value="P"/>
							<xs:enumeration value="D"/>
							<xs:enumeration value="S"/>
							<xs:enumeration value="C"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="TransactionInformation">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="MessageUniqueID">
					<xs:simpleType>
						<xs:restriction base="cdsd:text">
							<xs:maxLength value="250"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="DeliverToUserID">
					<xs:simpleType>
						<xs:restriction base="cdsd:text">
							<xs:maxLength value="9"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="Provider" type="cdsd:personNameSimple"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
