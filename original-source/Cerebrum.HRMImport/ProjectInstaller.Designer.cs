﻿namespace Cerebrum.HRMImport
{
    partial class ProjectInstaller
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.GetHrmProcessInstaller = new System.ServiceProcess.ServiceProcessInstaller();
            this.GetHrmInstaller = new System.ServiceProcess.ServiceInstaller();
            // 
            // GetHrmProcessInstaller
            // 
            this.GetHrmProcessInstaller.Account = System.ServiceProcess.ServiceAccount.LocalSystem;
            this.GetHrmProcessInstaller.Password = null;
            this.GetHrmProcessInstaller.Username = null;
            this.GetHrmProcessInstaller.Committing += new System.Configuration.Install.InstallEventHandler(this.MeasServiceProcessInstaller_Committing);
            this.GetHrmProcessInstaller.BeforeInstall += new System.Configuration.Install.InstallEventHandler(this.MeasServiceProcessInstaller_BeforeInstall);
            this.GetHrmProcessInstaller.BeforeUninstall += new System.Configuration.Install.InstallEventHandler(this.MeasServiceProcessInstaller_BeforeUninstall);
            // 
            // GetHrmInstaller
            // 
            this.GetHrmInstaller.DisplayName = "GetHrm";
            this.GetHrmInstaller.ServiceName = "GetHrm";
            this.GetHrmInstaller.StartType = System.ServiceProcess.ServiceStartMode.Automatic;
            this.GetHrmInstaller.BeforeInstall += new System.Configuration.Install.InstallEventHandler(this.MeasServiceInstaller_BeforeInstall);
            this.GetHrmInstaller.BeforeUninstall += new System.Configuration.Install.InstallEventHandler(this.MeasServiceInstaller_BeforeUninstall);
            // 
            // ProjectInstaller
            // 
            this.Installers.AddRange(new System.Configuration.Install.Installer[] {
            this.GetHrmProcessInstaller,
            this.GetHrmInstaller});

        }

        #endregion

        private System.ServiceProcess.ServiceProcessInstaller GetHrmProcessInstaller;
        private System.ServiceProcess.ServiceInstaller GetHrmInstaller;
    }
}