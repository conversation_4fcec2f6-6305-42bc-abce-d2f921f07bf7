﻿using System.ServiceProcess;

namespace Cerebrum.HRMImport
{
    public partial class Service : ServiceBase
    {
        protected Worker m_worker;

        public Service()
        {
            InitializeComponent();
        }

        public void Test()
        {
            OnStart(null);
        }

        protected override void OnStart(string[] args)
        {
            m_worker = new Worker();
            m_worker.Start();
        }

        protected override void OnStop()
        {
            m_worker.Stop();
        }
    }
}