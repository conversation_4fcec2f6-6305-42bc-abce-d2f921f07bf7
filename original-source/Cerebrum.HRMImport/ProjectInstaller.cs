﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration.Install;
using System.Linq;


namespace Cerebrum.HRMImport
{
    [RunInstaller(true)]
    public partial class ProjectInstaller : System.Configuration.Install.Installer
    {
        public ProjectInstaller()
        {
            InitializeComponent();
            if (!string.IsNullOrEmpty(ServiceParams.ServiceName))
            {
                GetHrmInstaller.ServiceName = ServiceParams.ServiceName;
                GetHrmInstaller.DisplayName = ServiceParams.ServiceName;
            }
        }

        private void MeasServiceProcessInstaller_BeforeInstall(object sender, InstallEventArgs e)
        {
            if (!string.IsNullOrEmpty(ServiceParams.ServiceName))
            {
                GetHrmInstaller.ServiceName = ServiceParams.ServiceName;
                GetHrmInstaller.DisplayName = ServiceParams.ServiceName;
            }
        }

        private void MeasServiceInstaller_BeforeInstall(object sender, InstallEventArgs e)
        {

        }

        private void MeasServiceProcessInstaller_BeforeUninstall(object sender, InstallEventArgs e)
        {
            if (!string.IsNullOrEmpty(ServiceParams.ServiceName))
            {
                GetHrmInstaller.ServiceName = ServiceParams.ServiceName;
                GetHrmInstaller.DisplayName = ServiceParams.ServiceName;
            }
        }

        private void MeasServiceInstaller_BeforeUninstall(object sender, InstallEventArgs e)
        {

        }

        private void MeasServiceProcessInstaller_Committing(object sender, InstallEventArgs e)
        {

        }
    }
}
