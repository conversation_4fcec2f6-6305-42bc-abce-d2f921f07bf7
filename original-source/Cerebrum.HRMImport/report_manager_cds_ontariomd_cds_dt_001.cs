﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     xsd.exe report_manager.xsd report_manager_dt.xsd /c   (report_manager.xsd report_manager_dt.xsd: Nov 11, 2013)
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Xml.Serialization;

// 
// This source code was auto-generated by xsd, Version=4.6.1055.0.
// 


/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "cds", IsNullable = false)]
public partial class OmdCds
{

    private PatientRecord patientRecordField;

    /// <remarks/>
    public PatientRecord PatientRecord
    {
        get
        {
            return this.patientRecordField;
        }
        set
        {
            this.patientRecordField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "cds", IsNullable = false)]
public partial class PatientRecord
{

    private Demographics demographicsField;

    private ReportsReceived[] reportsReceivedField;

    private TransactionInformation[] transactionInformationField;

    /// <remarks/>
    public Demographics Demographics
    {
        get
        {
            return this.demographicsField;
        }
        set
        {
            this.demographicsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ReportsReceived")]
    public ReportsReceived[] ReportsReceived
    {
        get
        {
            return this.reportsReceivedField;
        }
        set
        {
            this.reportsReceivedField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("TransactionInformation")]
    public TransactionInformation[] TransactionInformation
    {
        get
        {
            return this.transactionInformationField;
        }
        set
        {
            this.transactionInformationField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "cds", IsNullable = false)]
public partial class Demographics
{

    private personNameStandard namesField;

    private dateFullOrPartial dateOfBirthField;

    private healthCard healthCardField;

    private string chartNumberField;

    private gender genderField;

    private string uniqueVendorIdSequenceField;

    private address[] addressField;

    private phoneNumber[] phoneNumberField;

    private phoneNumberType preferredPhoneField;

    private bool preferredPhoneFieldSpecified;

    private officialSpokenLanguageCode preferredOfficialLanguageField;

    private bool preferredOfficialLanguageFieldSpecified;

    private string preferredSpokenLanguageField;

    private DemographicsContact[] contactField;

    private string noteAboutPatientField;

    private string patientWarningFlagsField;

    private enrollmentStatus enrollmentStatusField;

    private bool enrollmentStatusFieldSpecified;

    private dateFullOrPartial enrollmentDateField;

    private dateFullOrPartial enrollmentTerminationDateField;

    private terminationReasonCode terminationReasonField;

    private bool terminationReasonFieldSpecified;

    private DemographicsPrimaryPhysician primaryPhysicianField;

    private string emailField;

    private string familyMemberLinkField;

    private personStatus personStatusCodeField;

    private dateFullOrPartial personStatusDateField;

    private string sINField;

    /// <remarks/>
    public personNameStandard Names
    {
        get
        {
            return this.namesField;
        }
        set
        {
            this.namesField = value;
        }
    }

    /// <remarks/>
    public dateFullOrPartial DateOfBirth
    {
        get
        {
            return this.dateOfBirthField;
        }
        set
        {
            this.dateOfBirthField = value;
        }
    }

    /// <remarks/>
    public healthCard HealthCard
    {
        get
        {
            return this.healthCardField;
        }
        set
        {
            this.healthCardField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string ChartNumber
    {
        get
        {
            return this.chartNumberField;
        }
        set
        {
            this.chartNumberField = value;
        }
    }

    /// <remarks/>
    public gender Gender
    {
        get
        {
            return this.genderField;
        }
        set
        {
            this.genderField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string UniqueVendorIdSequence
    {
        get
        {
            return this.uniqueVendorIdSequenceField;
        }
        set
        {
            this.uniqueVendorIdSequenceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Address")]
    public address[] Address
    {
        get
        {
            return this.addressField;
        }
        set
        {
            this.addressField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("PhoneNumber")]
    public phoneNumber[] PhoneNumber
    {
        get
        {
            return this.phoneNumberField;
        }
        set
        {
            this.phoneNumberField = value;
        }
    }

    /// <remarks/>
    public phoneNumberType PreferredPhone
    {
        get
        {
            return this.preferredPhoneField;
        }
        set
        {
            this.preferredPhoneField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PreferredPhoneSpecified
    {
        get
        {
            return this.preferredPhoneFieldSpecified;
        }
        set
        {
            this.preferredPhoneFieldSpecified = value;
        }
    }

    /// <remarks/>
    public officialSpokenLanguageCode PreferredOfficialLanguage
    {
        get
        {
            return this.preferredOfficialLanguageField;
        }
        set
        {
            this.preferredOfficialLanguageField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PreferredOfficialLanguageSpecified
    {
        get
        {
            return this.preferredOfficialLanguageFieldSpecified;
        }
        set
        {
            this.preferredOfficialLanguageFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string PreferredSpokenLanguage
    {
        get
        {
            return this.preferredSpokenLanguageField;
        }
        set
        {
            this.preferredSpokenLanguageField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Contact")]
    public DemographicsContact[] Contact
    {
        get
        {
            return this.contactField;
        }
        set
        {
            this.contactField = value;
        }
    }

    /// <remarks/>
    public string NoteAboutPatient
    {
        get
        {
            return this.noteAboutPatientField;
        }
        set
        {
            this.noteAboutPatientField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string PatientWarningFlags
    {
        get
        {
            return this.patientWarningFlagsField;
        }
        set
        {
            this.patientWarningFlagsField = value;
        }
    }

    /// <remarks/>
    public enrollmentStatus EnrollmentStatus
    {
        get
        {
            return this.enrollmentStatusField;
        }
        set
        {
            this.enrollmentStatusField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool EnrollmentStatusSpecified
    {
        get
        {
            return this.enrollmentStatusFieldSpecified;
        }
        set
        {
            this.enrollmentStatusFieldSpecified = value;
        }
    }

    /// <remarks/>
    public dateFullOrPartial EnrollmentDate
    {
        get
        {
            return this.enrollmentDateField;
        }
        set
        {
            this.enrollmentDateField = value;
        }
    }

    /// <remarks/>
    public dateFullOrPartial EnrollmentTerminationDate
    {
        get
        {
            return this.enrollmentTerminationDateField;
        }
        set
        {
            this.enrollmentTerminationDateField = value;
        }
    }

    /// <remarks/>
    public terminationReasonCode TerminationReason
    {
        get
        {
            return this.terminationReasonField;
        }
        set
        {
            this.terminationReasonField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TerminationReasonSpecified
    {
        get
        {
            return this.terminationReasonFieldSpecified;
        }
        set
        {
            this.terminationReasonFieldSpecified = value;
        }
    }

    /// <remarks/>
    public DemographicsPrimaryPhysician PrimaryPhysician
    {
        get
        {
            return this.primaryPhysicianField;
        }
        set
        {
            this.primaryPhysicianField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string Email
    {
        get
        {
            return this.emailField;
        }
        set
        {
            this.emailField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string FamilyMemberLink
    {
        get
        {
            return this.familyMemberLinkField;
        }
        set
        {
            this.familyMemberLinkField = value;
        }
    }

    /// <remarks/>
    public personStatus PersonStatusCode
    {
        get
        {
            return this.personStatusCodeField;
        }
        set
        {
            this.personStatusCodeField = value;
        }
    }

    /// <remarks/>
    public dateFullOrPartial PersonStatusDate
    {
        get
        {
            return this.personStatusDateField;
        }
        set
        {
            this.personStatusDateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string SIN
    {
        get
        {
            return this.sINField;
        }
        set
        {
            this.sINField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class personNameStandard
{

    private personNamePrefixCode namePrefixField;

    private bool namePrefixFieldSpecified;

    private personNameStandardLegalName legalNameField;

    private personNameStandardOtherNames[] otherNamesField;

    private personNameSuffixCode lastNameSuffixField;

    private bool lastNameSuffixFieldSpecified;

    /// <remarks/>
    public personNamePrefixCode NamePrefix
    {
        get
        {
            return this.namePrefixField;
        }
        set
        {
            this.namePrefixField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NamePrefixSpecified
    {
        get
        {
            return this.namePrefixFieldSpecified;
        }
        set
        {
            this.namePrefixFieldSpecified = value;
        }
    }

    /// <remarks/>
    public personNameStandardLegalName LegalName
    {
        get
        {
            return this.legalNameField;
        }
        set
        {
            this.legalNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("OtherNames")]
    public personNameStandardOtherNames[] OtherNames
    {
        get
        {
            return this.otherNamesField;
        }
        set
        {
            this.otherNamesField = value;
        }
    }

    /// <remarks/>
    public personNameSuffixCode LastNameSuffix
    {
        get
        {
            return this.lastNameSuffixField;
        }
        set
        {
            this.lastNameSuffixField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LastNameSuffixSpecified
    {
        get
        {
            return this.lastNameSuffixFieldSpecified;
        }
        set
        {
            this.lastNameSuffixFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum personNamePrefixCode
{

    /// <remarks/>
    Miss,

    /// <remarks/>
    Mr,

    /// <remarks/>
    Mssr,

    /// <remarks/>
    Mrs,

    /// <remarks/>
    Ms,

    /// <remarks/>
    Prof,

    /// <remarks/>
    Reeve,

    /// <remarks/>
    Rev,

    /// <remarks/>
    RtHon,

    /// <remarks/>
    Sen,

    /// <remarks/>
    Sgt,

    /// <remarks/>
    Sr,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds_dt")]
public partial class personNameStandardLegalName
{

    private personNameStandardLegalNameFirstName firstNameField;

    private personNameStandardLegalNameLastName lastNameField;

    private personNameStandardLegalNameOtherName[] otherNameField;

    private personNamePurposeCode namePurposeField;

    public personNameStandardLegalName()
    {
        this.namePurposeField = personNamePurposeCode.L;
    }

    /// <remarks/>
    public personNameStandardLegalNameFirstName FirstName
    {
        get
        {
            return this.firstNameField;
        }
        set
        {
            this.firstNameField = value;
        }
    }

    /// <remarks/>
    public personNameStandardLegalNameLastName LastName
    {
        get
        {
            return this.lastNameField;
        }
        set
        {
            this.lastNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("OtherName")]
    public personNameStandardLegalNameOtherName[] OtherName
    {
        get
        {
            return this.otherNameField;
        }
        set
        {
            this.otherNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public personNamePurposeCode namePurpose
    {
        get
        {
            return this.namePurposeField;
        }
        set
        {
            this.namePurposeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds_dt")]
public partial class personNameStandardLegalNameFirstName
{

    private string partField;

    private personNamePartTypeCode partTypeField;

    private personNamePartQualifierCode partQualifierField;

    private bool partQualifierFieldSpecified;

    public personNameStandardLegalNameFirstName()
    {
        this.partTypeField = personNamePartTypeCode.GIV;
    }

    /// <remarks/>
    public string Part
    {
        get
        {
            return this.partField;
        }
        set
        {
            this.partField = value;
        }
    }

    /// <remarks/>
    public personNamePartTypeCode PartType
    {
        get
        {
            return this.partTypeField;
        }
        set
        {
            this.partTypeField = value;
        }
    }

    /// <remarks/>
    public personNamePartQualifierCode PartQualifier
    {
        get
        {
            return this.partQualifierField;
        }
        set
        {
            this.partQualifierField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PartQualifierSpecified
    {
        get
        {
            return this.partQualifierFieldSpecified;
        }
        set
        {
            this.partQualifierFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum personNamePartTypeCode
{

    /// <remarks/>
    FAMC,

    /// <remarks/>
    GIV,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum personNamePartQualifierCode
{

    /// <remarks/>
    BR,

    /// <remarks/>
    SP,

    /// <remarks/>
    CL,

    /// <remarks/>
    IN,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class reportContent
{

    private object itemField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Media", typeof(byte[]), DataType = "base64Binary")]
    [System.Xml.Serialization.XmlElementAttribute("TextContent", typeof(string))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class personNameSimple
{

    private string firstNameField;

    private string lastNameField;

    /// <remarks/>
    public string FirstName
    {
        get
        {
            return this.firstNameField;
        }
        set
        {
            this.firstNameField = value;
        }
    }

    /// <remarks/>
    public string LastName
    {
        get
        {
            return this.lastNameField;
        }
        set
        {
            this.lastNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class personNameSimpleWithMiddleName
{

    private string firstNameField;

    private string middleNameField;

    private string lastNameField;

    /// <remarks/>
    public string FirstName
    {
        get
        {
            return this.firstNameField;
        }
        set
        {
            this.firstNameField = value;
        }
    }

    /// <remarks/>
    public string MiddleName
    {
        get
        {
            return this.middleNameField;
        }
        set
        {
            this.middleNameField = value;
        }
    }

    /// <remarks/>
    public string LastName
    {
        get
        {
            return this.lastNameField;
        }
        set
        {
            this.lastNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class phoneNumber
{

    private string[] itemsField;

    private ItemsChoiceType[] itemsElementNameField;

    private phoneNumberType phoneNumberTypeField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("areaCode", typeof(string), DataType = "token")]
    [System.Xml.Serialization.XmlElementAttribute("exchange", typeof(string), DataType = "token")]
    [System.Xml.Serialization.XmlElementAttribute("extension", typeof(string), DataType = "token")]
    [System.Xml.Serialization.XmlElementAttribute("number", typeof(string), DataType = "token")]
    [System.Xml.Serialization.XmlElementAttribute("phoneNumber", typeof(string))]
    [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
    public string[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public ItemsChoiceType[] ItemsElementName
    {
        get
        {
            return this.itemsElementNameField;
        }
        set
        {
            this.itemsElementNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public phoneNumberType phoneNumberType
    {
        get
        {
            return this.phoneNumberTypeField;
        }
        set
        {
            this.phoneNumberTypeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt", IncludeInSchema = false)]
public enum ItemsChoiceType
{

    /// <remarks/>
    areaCode,

    /// <remarks/>
    exchange,

    /// <remarks/>
    extension,

    /// <remarks/>
    number,

    /// <remarks/>
    phoneNumber,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum phoneNumberType
{

    /// <remarks/>
    R,

    /// <remarks/>
    C,

    /// <remarks/>
    W,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class postalZipCode
{

    private string itemField;

    private ItemChoiceType1 itemElementNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("PostalCode", typeof(string))]
    [System.Xml.Serialization.XmlElementAttribute("ZipCode", typeof(string), DataType = "token")]
    [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
    public string Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public ItemChoiceType1 ItemElementName
    {
        get
        {
            return this.itemElementNameField;
        }
        set
        {
            this.itemElementNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt", IncludeInSchema = false)]
public enum ItemChoiceType1
{

    /// <remarks/>
    PostalCode,

    /// <remarks/>
    ZipCode,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(TypeName = "address.structured", Namespace = "cds_dt")]
public partial class addressstructured
{

    private string line1Field;

    private string line2Field;

    private string line3Field;

    private string cityField;

    private string countrySubdivisionCodeField;

    private postalZipCode postalZipCodeField;

    /// <remarks/>
    public string Line1
    {
        get
        {
            return this.line1Field;
        }
        set
        {
            this.line1Field = value;
        }
    }

    /// <remarks/>
    public string Line2
    {
        get
        {
            return this.line2Field;
        }
        set
        {
            this.line2Field = value;
        }
    }

    /// <remarks/>
    public string Line3
    {
        get
        {
            return this.line3Field;
        }
        set
        {
            this.line3Field = value;
        }
    }

    /// <remarks/>
    public string City
    {
        get
        {
            return this.cityField;
        }
        set
        {
            this.cityField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string CountrySubdivisionCode
    {
        get
        {
            return this.countrySubdivisionCodeField;
        }
        set
        {
            this.countrySubdivisionCodeField = value;
        }
    }

    /// <remarks/>
    public postalZipCode PostalZipCode
    {
        get
        {
            return this.postalZipCodeField;
        }
        set
        {
            this.postalZipCodeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class address
{

    private object itemField;

    private addressType addressTypeField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Formatted", typeof(string))]
    [System.Xml.Serialization.XmlElementAttribute("Structured", typeof(addressstructured))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public addressType addressType
    {
        get
        {
            return this.addressTypeField;
        }
        set
        {
            this.addressTypeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum addressType
{

    /// <remarks/>
    M,

    /// <remarks/>
    R,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class healthCard
{

    private string numberField;

    private string versionField;

    private System.DateTime expirydateField;

    private bool expirydateFieldSpecified;

    private healthCardProvinceCode provinceCodeField;

    /// <remarks/>
    public string Number
    {
        get
        {
            return this.numberField;
        }
        set
        {
            this.numberField = value;
        }
    }

    /// <remarks/>
    public string Version
    {
        get
        {
            return this.versionField;
        }
        set
        {
            this.versionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
    public System.DateTime Expirydate
    {
        get
        {
            return this.expirydateField;
        }
        set
        {
            this.expirydateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ExpirydateSpecified
    {
        get
        {
            return this.expirydateFieldSpecified;
        }
        set
        {
            this.expirydateFieldSpecified = value;
        }
    }

    /// <remarks/>
    public healthCardProvinceCode ProvinceCode
    {
        get
        {
            return this.provinceCodeField;
        }
        set
        {
            this.provinceCodeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum healthCardProvinceCode
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-AB")]
    CAAB,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-BC")]
    CABC,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-MB")]
    CAMB,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-NB")]
    CANB,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-NL")]
    CANL,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-NS")]
    CANS,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-NT")]
    CANT,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-NU")]
    CANU,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-ON")]
    CAON,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-PE")]
    CAPE,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-QC")]
    CAQC,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-SK")]
    CASK,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("CA-YT")]
    CAYT,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-AK")]
    USAK,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-AL")]
    USAL,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-AR")]
    USAR,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-AZ")]
    USAZ,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-CA")]
    USCA,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-CO")]
    USCO,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-CT")]
    USCT,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-CZ")]
    USCZ,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-DC")]
    USDC,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-DE")]
    USDE,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-FL")]
    USFL,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-GA")]
    USGA,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-GU")]
    USGU,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-HI")]
    USHI,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-IA")]
    USIA,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-ID")]
    USID,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-IL")]
    USIL,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-IN")]
    USIN,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-KS")]
    USKS,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-KY")]
    USKY,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-LA")]
    USLA,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-MA")]
    USMA,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-MD")]
    USMD,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-ME")]
    USME,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-MI")]
    USMI,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-MN")]
    USMN,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-MO")]
    USMO,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-MS")]
    USMS,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-MT")]
    USMT,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-NC")]
    USNC,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-ND")]
    USND,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-NE")]
    USNE,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-NH")]
    USNH,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-NJ")]
    USNJ,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-NM")]
    USNM,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-NV")]
    USNV,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-NY")]
    USNY,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-OH")]
    USOH,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-OK")]
    USOK,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-OR")]
    USOR,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-PA")]
    USPA,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-PR")]
    USPR,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-RI")]
    USRI,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-SC")]
    USSC,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-SD")]
    USSD,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-TN")]
    USTN,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-TX")]
    USTX,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-UT")]
    USUT,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-VA")]
    USVA,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-VI")]
    USVI,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-VT")]
    USVT,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-WA")]
    USWA,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-WI")]
    USWI,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-WV")]
    USWV,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("US-WY")]
    USWY,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("-50")]
    Item50,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("-70")]
    Item70,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("-90")]
    Item90,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public partial class dateFullOrPartial
{

    private object itemField;

    private ItemChoiceType itemElementNameField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("DateTime", typeof(System.DateTime))]
    [System.Xml.Serialization.XmlElementAttribute("FullDate", typeof(System.DateTime), DataType = "date")]
    [System.Xml.Serialization.XmlElementAttribute("YearMonth", typeof(string), DataType = "gYearMonth")]
    [System.Xml.Serialization.XmlElementAttribute("YearOnly", typeof(string), DataType = "gYear")]
    [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public ItemChoiceType ItemElementName
    {
        get
        {
            return this.itemElementNameField;
        }
        set
        {
            this.itemElementNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt", IncludeInSchema = false)]
public enum ItemChoiceType
{

    /// <remarks/>
    DateTime,

    /// <remarks/>
    FullDate,

    /// <remarks/>
    YearMonth,

    /// <remarks/>
    YearOnly,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds_dt")]
public partial class personNameStandardLegalNameLastName
{

    private string partField;

    private personNamePartTypeCode partTypeField;

    private personNamePartQualifierCode partQualifierField;

    private bool partQualifierFieldSpecified;

    public personNameStandardLegalNameLastName()
    {
        this.partTypeField = personNamePartTypeCode.FAMC;
    }

    /// <remarks/>
    public string Part
    {
        get
        {
            return this.partField;
        }
        set
        {
            this.partField = value;
        }
    }

    /// <remarks/>
    public personNamePartTypeCode PartType
    {
        get
        {
            return this.partTypeField;
        }
        set
        {
            this.partTypeField = value;
        }
    }

    /// <remarks/>
    public personNamePartQualifierCode PartQualifier
    {
        get
        {
            return this.partQualifierField;
        }
        set
        {
            this.partQualifierField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PartQualifierSpecified
    {
        get
        {
            return this.partQualifierFieldSpecified;
        }
        set
        {
            this.partQualifierFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds_dt")]
public partial class personNameStandardLegalNameOtherName
{

    private string partField;

    private personNamePartTypeCode partTypeField;

    private personNamePartQualifierCode partQualifierField;

    private bool partQualifierFieldSpecified;

    /// <remarks/>
    public string Part
    {
        get
        {
            return this.partField;
        }
        set
        {
            this.partField = value;
        }
    }

    /// <remarks/>
    public personNamePartTypeCode PartType
    {
        get
        {
            return this.partTypeField;
        }
        set
        {
            this.partTypeField = value;
        }
    }

    /// <remarks/>
    public personNamePartQualifierCode PartQualifier
    {
        get
        {
            return this.partQualifierField;
        }
        set
        {
            this.partQualifierField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PartQualifierSpecified
    {
        get
        {
            return this.partQualifierFieldSpecified;
        }
        set
        {
            this.partQualifierFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum personNamePurposeCode
{

    /// <remarks/>
    HC,

    /// <remarks/>
    L,

    /// <remarks/>
    AL,

    /// <remarks/>
    C,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds_dt")]
public partial class personNameStandardOtherNames
{

    private personNameStandardOtherNamesOtherName[] otherNameField;

    private personNamePurposeCode namePurposeField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("OtherName")]
    public personNameStandardOtherNamesOtherName[] OtherName
    {
        get
        {
            return this.otherNameField;
        }
        set
        {
            this.otherNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public personNamePurposeCode namePurpose
    {
        get
        {
            return this.namePurposeField;
        }
        set
        {
            this.namePurposeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds_dt")]
public partial class personNameStandardOtherNamesOtherName
{

    private string partField;

    private personNamePartTypeCode partTypeField;

    private personNamePartQualifierCode partQualifierField;

    private bool partQualifierFieldSpecified;

    /// <remarks/>
    public string Part
    {
        get
        {
            return this.partField;
        }
        set
        {
            this.partField = value;
        }
    }

    /// <remarks/>
    public personNamePartTypeCode PartType
    {
        get
        {
            return this.partTypeField;
        }
        set
        {
            this.partTypeField = value;
        }
    }

    /// <remarks/>
    public personNamePartQualifierCode PartQualifier
    {
        get
        {
            return this.partQualifierField;
        }
        set
        {
            this.partQualifierField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PartQualifierSpecified
    {
        get
        {
            return this.partQualifierFieldSpecified;
        }
        set
        {
            this.partQualifierFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum personNameSuffixCode
{

    /// <remarks/>
    Jr,

    /// <remarks/>
    Sr,

    /// <remarks/>
    II,

    /// <remarks/>
    III,

    /// <remarks/>
    IV,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum gender
{

    /// <remarks/>
    M,

    /// <remarks/>
    F,

    /// <remarks/>
    O,

    /// <remarks/>
    U,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum officialSpokenLanguageCode
{

    /// <remarks/>
    ENG,

    /// <remarks/>
    FRE,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
public partial class DemographicsContact
{

    private personNameSimpleWithMiddleName nameField;

    private phoneNumber[] phoneNumberField;

    private string emailAddressField;

    private string noteField;

    private contactPersonPurpose contactPurposeField;

    /// <remarks/>
    public personNameSimpleWithMiddleName Name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("PhoneNumber")]
    public phoneNumber[] PhoneNumber
    {
        get
        {
            return this.phoneNumberField;
        }
        set
        {
            this.phoneNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string EmailAddress
    {
        get
        {
            return this.emailAddressField;
        }
        set
        {
            this.emailAddressField = value;
        }
    }

    /// <remarks/>
    public string Note
    {
        get
        {
            return this.noteField;
        }
        set
        {
            this.noteField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(Form = System.Xml.Schema.XmlSchemaForm.Qualified)]
    public contactPersonPurpose ContactPurpose
    {
        get
        {
            return this.contactPurposeField;
        }
        set
        {
            this.contactPurposeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum contactPersonPurpose
{

    /// <remarks/>
    EC,

    /// <remarks/>
    NK,

    /// <remarks/>
    AS,

    /// <remarks/>
    CG,

    /// <remarks/>
    PA,

    /// <remarks/>
    IN,

    /// <remarks/>
    GT,

    /// <remarks/>
    O,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum enrollmentStatus
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum terminationReasonCode
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("14")]
    Item14,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("24")]
    Item24,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("30")]
    Item30,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("32")]
    Item32,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("33")]
    Item33,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("35")]
    Item35,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("36")]
    Item36,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("37")]
    Item37,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("38")]
    Item38,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("39")]
    Item39,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("40")]
    Item40,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("41")]
    Item41,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("42")]
    Item42,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("44")]
    Item44,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("51")]
    Item51,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("53")]
    Item53,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("54")]
    Item54,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("56")]
    Item56,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("57")]
    Item57,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("59")]
    Item59,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("60")]
    Item60,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("61")]
    Item61,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("62")]
    Item62,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("73")]
    Item73,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("74")]
    Item74,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("82")]
    Item82,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("84")]
    Item84,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("90")]
    Item90,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("91")]
    Item91,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
public partial class DemographicsPrimaryPhysician
{

    private personNameSimple nameField;

    private string oHIPPhysicianIdField;

    /// <remarks/>
    public personNameSimple Name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string OHIPPhysicianId
    {
        get
        {
            return this.oHIPPhysicianIdField;
        }
        set
        {
            this.oHIPPhysicianIdField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum personStatus
{

    /// <remarks/>
    A,

    /// <remarks/>
    I,

    /// <remarks/>
    D,

    /// <remarks/>
    O,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "cds", IsNullable = false)]
public partial class ReportsReceived
{

    private reportMedia mediaField;

    private bool mediaFieldSpecified;

    private reportFormat formatField;

    private bool formatFieldSpecified;

    private string fileExtensionAndVersionField;

    private reportContent contentField;

    private reportClass classField;

    private bool classFieldSpecified;

    private string subClassField;

    private dateFullOrPartial eventDateTimeField;

    private dateFullOrPartial receivedDateTimeField;

    private dateFullOrPartial reviewedDateTimeField;

    private personNameSimple authorPhysicianField;

    private string reviewingOHIPPhysicianIdField;

    private string sendingFacilityField;

    private string sendingFacilityReportNumberField;

    private ReportsReceivedOBRContent[] oBRContentField;

    private ReportsReceivedResultStatus resultStatusField;

    private bool resultStatusFieldSpecified;

    /// <remarks/>
    public reportMedia Media
    {
        get
        {
            return this.mediaField;
        }
        set
        {
            this.mediaField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MediaSpecified
    {
        get
        {
            return this.mediaFieldSpecified;
        }
        set
        {
            this.mediaFieldSpecified = value;
        }
    }

    /// <remarks/>
    public reportFormat Format
    {
        get
        {
            return this.formatField;
        }
        set
        {
            this.formatField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FormatSpecified
    {
        get
        {
            return this.formatFieldSpecified;
        }
        set
        {
            this.formatFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string FileExtensionAndVersion
    {
        get
        {
            return this.fileExtensionAndVersionField;
        }
        set
        {
            this.fileExtensionAndVersionField = value;
        }
    }

    /// <remarks/>
    public reportContent Content
    {
        get
        {
            return this.contentField;
        }
        set
        {
            this.contentField = value;
        }
    }

    /// <remarks/>
    public reportClass Class
    {
        get
        {
            return this.classField;
        }
        set
        {
            this.classField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ClassSpecified
    {
        get
        {
            return this.classFieldSpecified;
        }
        set
        {
            this.classFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string SubClass
    {
        get
        {
            return this.subClassField;
        }
        set
        {
            this.subClassField = value;
        }
    }

    /// <remarks/>
    public dateFullOrPartial EventDateTime
    {
        get
        {
            return this.eventDateTimeField;
        }
        set
        {
            this.eventDateTimeField = value;
        }
    }

    /// <remarks/>
    public dateFullOrPartial ReceivedDateTime
    {
        get
        {
            return this.receivedDateTimeField;
        }
        set
        {
            this.receivedDateTimeField = value;
        }
    }

    /// <remarks/>
    public dateFullOrPartial ReviewedDateTime
    {
        get
        {
            return this.reviewedDateTimeField;
        }
        set
        {
            this.reviewedDateTimeField = value;
        }
    }

    /// <remarks/>
    public personNameSimple AuthorPhysician
    {
        get
        {
            return this.authorPhysicianField;
        }
        set
        {
            this.authorPhysicianField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string ReviewingOHIPPhysicianId
    {
        get
        {
            return this.reviewingOHIPPhysicianIdField;
        }
        set
        {
            this.reviewingOHIPPhysicianIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
    public string SendingFacility
    {
        get
        {
            return this.sendingFacilityField;
        }
        set
        {
            this.sendingFacilityField = value;
        }
    }

    /// <remarks/>
    public string SendingFacilityReportNumber
    {
        get
        {
            return this.sendingFacilityReportNumberField;
        }
        set
        {
            this.sendingFacilityReportNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("OBRContent")]
    public ReportsReceivedOBRContent[] OBRContent
    {
        get
        {
            return this.oBRContentField;
        }
        set
        {
            this.oBRContentField = value;
        }
    }

    /// <remarks/>
    public ReportsReceivedResultStatus ResultStatus
    {
        get
        {
            return this.resultStatusField;
        }
        set
        {
            this.resultStatusField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ResultStatusSpecified
    {
        get
        {
            return this.resultStatusFieldSpecified;
        }
        set
        {
            this.resultStatusFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum reportMedia
{

    /// <remarks/>
    Email,

    /// <remarks/>
    Download,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("Portable Media")]
    PortableMedia,

    /// <remarks/>
    Hardcopy,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum reportFormat
{

    /// <remarks/>
    Text,

    /// <remarks/>
    Binary,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "cds_dt")]
public enum reportClass
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("Diagnostic Imaging Report")]
    DiagnosticImagingReport,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("Diagnostic Test Report")]
    DiagnosticTestReport,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("Other Letter")]
    OtherLetter,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("Consultant Report")]
    ConsultantReport,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("Medical Records Report")]
    MedicalRecordsReport,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("Cardio Respiratory Report")]
    CardioRespiratoryReport,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
public partial class ReportsReceivedOBRContent
{

    private string accompanyingSubClassField;

    private string accompanyingMnemonicField;

    private string accompanyingDescriptionField;

    private dateFullOrPartial observationDateTimeField;

    /// <remarks/>
    public string AccompanyingSubClass
    {
        get
        {
            return this.accompanyingSubClassField;
        }
        set
        {
            this.accompanyingSubClassField = value;
        }
    }

    /// <remarks/>
    public string AccompanyingMnemonic
    {
        get
        {
            return this.accompanyingMnemonicField;
        }
        set
        {
            this.accompanyingMnemonicField = value;
        }
    }

    /// <remarks/>
    public string AccompanyingDescription
    {
        get
        {
            return this.accompanyingDescriptionField;
        }
        set
        {
            this.accompanyingDescriptionField = value;
        }
    }

    /// <remarks/>
    public dateFullOrPartial ObservationDateTime
    {
        get
        {
            return this.observationDateTimeField;
        }
        set
        {
            this.observationDateTimeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
public enum ReportsReceivedResultStatus
{

    /// <remarks/>
    P,

    /// <remarks/>
    D,

    /// <remarks/>
    S,

    /// <remarks/>
    C,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "cds")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "cds", IsNullable = false)]
public partial class TransactionInformation
{

    private string messageUniqueIDField;

    private string deliverToUserIDField;

    private personNameSimple providerField;

    /// <remarks/>
    public string MessageUniqueID
    {
        get
        {
            return this.messageUniqueIDField;
        }
        set
        {
            this.messageUniqueIDField = value;
        }
    }

    /// <remarks/>
    public string DeliverToUserID
    {
        get
        {
            return this.deliverToUserIDField;
        }
        set
        {
            this.deliverToUserIDField = value;
        }
    }

    /// <remarks/>
    public personNameSimple Provider
    {
        get
        {
            return this.providerField;
        }
        set
        {
            this.providerField = value;
        }
    }
}
