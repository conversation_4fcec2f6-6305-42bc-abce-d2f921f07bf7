using System;
using System.Collections.Generic;
using System.Configuration;
using System.Threading;

using log4net;
using Cerebrum.BLL.Utility;
using System.Net.Http;
using Microsoft.Extensions.DependencyInjection;

namespace Cerebrum.HRMImport
{
    public class Worker
    {
        private bool bStop = false;
        private int timeout = 60000;
        private Thread m_thread = null;
        private AutoResetEvent[] events;

        public Worker()
        {
            OnLoad();
        }

        public void Start()
        {
            Log.Info("HRM Import Start");

            events = new AutoResetEvent[] { new AutoResetEvent(false) };
            m_thread = new Thread( new ParameterizedThreadStart(Run) );
            m_thread.Start();
        }

        public void Stop()
        {
            Log.Info("HRM Import Stop");
            if (events == null)
                return;
            if (events.Length == 0)
                return;
            if (events[0] == null)
                return;

            events[0].Set();
            if (!m_thread.Join(10000))
            {
                m_thread.Join(10000);
            }
        }

        public void Run(object data)
        {
            // do work here - loop or event
            Log.Info("HRM Import Run");
            IHttpClientFactory httpClientFactory = new ServiceCollection()
                .AddHttpClient()
                .BuildServiceProvider()
                .GetService<IHttpClientFactory>();
            Process(httpClientFactory, HrmService.HRMRUNMETHOD.AUTOPOLL, $"{HrmService.DOWNLOAD} {HrmService.DECRYPT} {HrmService.IMPORT}", -1);
            while(!bStop)
            {
                int res = WaitHandle.WaitAny(events, timeout);
                switch(res)
                {
                case 0:
                    return;
                case 1:
                    break;
                case WaitHandle.WaitTimeout:
                    Process(httpClientFactory, HrmService.HRMRUNMETHOD.AUTOPOLL, $"{HrmService.DOWNLOAD} {HrmService.DECRYPT} {HrmService.IMPORT}", -1);
                    break;
                }
            }
        }

        private void OnLoad()
        {
            Log.Info( "Worker OnLoad()" );
            timeout = int.Parse(ConfigurationManager.AppSettings["Timeout"]) * 1000;
        }

        private void Process(IHttpClientFactory httpClientFactor, HrmService.HRMRUNMETHOD runMethod, string stepName, int queryPracticeId)
        {
            Log.Info("HRM Import Process ...");
            HRMqueryProcessor hrmProc = new HRMqueryProcessor();
            hrmProc.StepName = stepName;
            hrmProc.RunMethod = runMethod;
            hrmProc.QueryPracticeId = queryPracticeId;
            hrmProc.tryQuery(httpClientFactor); 
        }

        private void Process(IHttpClientFactory httpClientFactor, string stepName, int queryPracticeId)
        {
            Process(httpClientFactor, HrmService.HRMRUNMETHOD.APPLICATION, stepName, queryPracticeId);
        }

        internal static void TestProcess(IHttpClientFactory httpClientFactor, string stepName, int queryPracticeId)
        {
            Worker w = new Worker();
            w.Process(httpClientFactor, stepName, queryPracticeId);
        }
    }
}