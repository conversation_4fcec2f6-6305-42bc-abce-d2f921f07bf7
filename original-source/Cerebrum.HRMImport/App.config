﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <appSettings>
    <add key="DownloadFolder" value="C:\HRMFiles\Download" />
    <add key="TmpFolder" value="C:\HRMFiles\Tmp" />
    <add key="ArchiveFolder" value="C:\HRMFiles\Archive" />
    <add key="ServiceName" value="HRMImport" />
    <add key="Timeout" value="60" /> <!-- seconds          window service wake-up time -->
    <!-- email settings -->
    <add key="SMTPTelus" value="mail.exchange.telus.com" />
    <add key="SMTPpw_Telus" value="1chrc23" />
    <add key="SMTPun_Telus" value="<EMAIL>" />
    <add key="SMTPGoogle" value="smtp.gmail.com" />
    <add key="SMTPpw_Googl" value="chrc_2016" />
    <add key="SMTPun_Google" value="<EMAIL>" />
    <add key="SMTP_From" value="<EMAIL>" />
    <add key="AUTH_SERVER" value="http://c3authdev.mycerebrum.com/" />
    <add key="clientkey" value="MyCerebrum" />
    <add key="clientsecrect" value="12345" />
  </appSettings>
  <connectionStrings>
    <add name="C3Context" providerName="System.Data.SqlClient" connectionString="Server=c3-proddb-qa\cerebrum3,9667;Database=Cer30;User ID=cerebrum_sa;Password=**********;Application Name=C3Validation; MultipleActiveResultSets=true;" />
    <add name="C3InterfaceContext" providerName="System.Data.SqlClient" connectionString="Server=Cerebrum3dbtest\cerebrum3,7568;Database=TEST_AppsInterfaceLogs;User ID=cerebrum_sa;Password=***********; MultipleActiveResultSets=true;" />   
  </connectionStrings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <log4net>
    <appender name="ConsoleAppender" type="log4net.Appender.ColoredConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %level - %message%newline" />
      </layout>
      <mapping>
        <level value="WARN" />
        <foreColor value="Yellow, HighIntensity" />
      </mapping>
      <mapping>
        <level value="ERROR" />
        <foreColor value="Red, HighIntensity" />
      </mapping>
    </appender>
    <appender name="RollingFile" type="log4net.Appender.RollingFileAppender">
      <file type="log4net.Util.PatternString" value="./logs/HRMImport %date{yyyy-MM-dd}.txt" />
      <rollingStyle value="Date" />
      <appendToFile value="true" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <datePattern value="yyyyMMdd" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %level %property{officeId} - %message%newline" />
      </layout>
    </appender>
    <appender name="AdoNetAppender" type="log4net.Appender.AdoNetAppender">
      <bufferSize value="1" />
      <connectionType value="System.Data.SqlClient.SqlConnection, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
      <connectionString value="data source=Cerebrum3dbtest\cerebrum3,7568;Database=TEST_Cer30;User ID=cerebrum_sa;Password=***********; MultipleActiveResultSets=true;" />
      <commandText value="INSERT INTO HRMLogs ([practiceId],[messageType],[message],[user],[pollingType],[recipient],[dateCreated]) VALUES (@practiceId,@log_level,@message,@user,@pollingType,@recipient,@log_date)" />
      <parameter>
        <parameterName value="@practiceId" />
        <dbType value="Int32" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{practiceId}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_level" />
        <dbType value="String" />
        <size value="16" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%level" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@message" />
        <dbType value="String" />
        <size value="512" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%message" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@user" />
        <dbType value="String" />
        <size value="64" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{user}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@pollingType" />
        <dbType value="String" />
        <size value="16" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{pollingType}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@recipient" />
        <dbType value="String" />
        <size value="64" />
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%property{recipient}" />
        </layout>
      </parameter>
      <parameter>
        <parameterName value="@log_date" />
        <dbType value="DateTime" />
        <layout type="log4net.Layout.RawTimeStampLayout" />
      </parameter>
    </appender>
    <root>
      <level value="ALL" />
      <appender-ref ref="ConsoleAppender" />
      <appender-ref ref="RollingFile" />
      <appender-ref ref="AdoNetAppender" />
    </root>
  </log4net>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.1.0" newVersion="3.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
