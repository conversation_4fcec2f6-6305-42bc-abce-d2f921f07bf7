﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using Cerebrum.BLL.Common;
using Cerebrum.BLL.User;
using Cerebrum.BLL.Utility;
using Cerebrum.ViewModels.Common;
using Cerebrum.ViewModels.User;
using log4net;
using System.Net.Http;

namespace Cerebrum.HRMImport
{
    internal class HRMqueryProcessor : Cerebrum.BLL.Common.CommScheduler
    {
        public string StepName {  get; set;}
        public HrmService.HRMRUNMETHOD RunMethod {  get; set;}

        public HRMqueryProcessor()
        {
            ctype = CommunicationType.HRM;
        }

        //*
        public static void Unlock()
        {
            try
            {
                ExternalCommBLL ext = new ExternalCommBLL();
                ext.HRMLockRelease();
            }
            catch
            {
               
            }
        }
        //*/

        override protected bool ProcessPractice(IHttpClientFactory httpClientFactor, VMPracticePoll practice, DateTime lastPollingTime, DateTime pollingTime)
        {
            Log.Info("Process Practice ...");
            HrmService.HRMRUNMETHOD runMethod;
            if(RunMethod == HrmService.HRMRUNMETHOD.APPLICATION)
            {
                runMethod = HrmService.HRMRUNMETHOD.APPLICATION;
            }
            else if(string.IsNullOrWhiteSpace(practice.ManualPollUser))
            {
                runMethod = HrmService.HRMRUNMETHOD.AUTOPOLL;
            }
            else
            {
                runMethod = HrmService.HRMRUNMETHOD.MANUALPOLL;
            }

            bool res =  Process(httpClientFactor,
                            practice,
                            runMethod,
                            pollingTime,
                            StepName
                            );

            if(res) // update last Success Run Date when Ok
            {
                practice.LastSuccessRunDate = pollingTime;
            }
            return res;
        }

        override protected void OnQueryStart()
        {
            switch(RunMethod)
            {
            case HrmService.HRMRUNMETHOD.APPLICATION:
            case HrmService.HRMRUNMETHOD.MANUALPOLL:
                bForceRun = true;
                break;
            }
        }

//        private void Process(HrmService.HrmServiceData hrmServiceData, string stepName)
        private bool Process(IHttpClientFactory httpClientFactory, VMPracticePoll practice, HrmService.HRMRUNMETHOD runMethod, DateTime pollingTime, string stepName)
        {
            bool res = true;
            Log.ResetCustomProperties();
            List<string> emailMessages = new List<string>();
            try
            {
                //Log.Info($"Poll for practice {practice.PracticeId} Start ...");
                if (runMethod == HrmService.HRMRUNMETHOD.AUTOPOLL)
                {
                    GlobalContext.Properties["pollingType"] = Log.AUTOPOLL;
                    GlobalContext.Properties["practiceIdGlobal"] = practice.PracticeId;
                    Log.Info($"Auto for practice {practice.PracticeId} Poll Start ...");
                }
                if (runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                {
                     GlobalContext.Properties["user"] = practice.ManualPollUser;
                    GlobalContext.Properties["pollingType"] = Log.MANUALPOLL;
                    GlobalContext.Properties["practiceIdGlobal"] = practice.PracticeId;
                    Log.Info($"Manual for practice {practice.PracticeId} Poll Triggered by {practice.ManualPollUser}");
                    Log.Info($"Manual Poll Triggered");
                    Log.Info($"Manual Poll Start...");
                }

                Log.Info($"Processing request: {stepName} ...");
                stepName = stepName.ToLower();

                if (stepName.Contains(HrmService.DOWNLOAD))
                {
                    //if (runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                    //    GlobalContext.Properties["practiceIdGlobal"] = hrmServiceData.officeId;

                    HrmFile hrmFile = new HrmFile();
                    res = hrmFile.Download(practice, runMethod);
                    if (hrmFile.emailMessages.Count > 0)
                        emailMessages.AddRange(hrmFile.emailMessages);
                }

                if (stepName.Contains(HrmService.DECRYPT))
                {
                    if(runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                    {
                     //   GlobalContext.Properties["practiceIdGlobal"] = hrmServiceData.officeId;
                    }

                    HrmFile hrmFile = new HrmFile();
                    hrmFile.Decrypt(practice, runMethod);
                    if (hrmFile.emailMessages.Count > 0)
                        emailMessages.AddRange(hrmFile.emailMessages);
                }

                if (stepName.Contains(HrmService.IMPORT))
                {
                    if(runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                    {
                     //   GlobalContext.Properties["practiceIdGlobal"] = hrmServiceData.officeId;
                    }

                    HrmImport hrm = new HrmImport();
                    hrm.Import(httpClientFactory, practice, runMethod);
                    if (hrm.emailMessages.Count > 0)
                        emailMessages.AddRange(hrm.emailMessages);
                }

                if(runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                {
                    //GlobalContext.Properties["practiceIdGlobal"] = hrmServiceData.officeId;
                }

                if (emailMessages.Count > 0 && (runMethod == HrmService.HRMRUNMETHOD.AUTOPOLL || runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL))
                {

                    // !!!
                    // will be just a string
                    if (practice.Alert)
                        Helper.SendEmail(practice.Recipients, emailMessages, practice.ManualPollUser);
                }

                string msg = $"Poll End for Practice {practice.PracticeId}";
                switch(runMethod)
                {
                case HrmService.HRMRUNMETHOD.AUTOPOLL:
                    msg = "Auto " + msg; break;
                //     Log.Info($"Auto Poll End for Practice {practice.PracticeId}");
                case HrmService.HRMRUNMETHOD.MANUALPOLL:
                    //     Log.Info($"Manual Poll End.");
                    msg = "Manual " + msg; break;
                }
                Log.Info(msg);

                return res;
            }
            catch (Exception e)
            {
                Log.Error(e.Message);
                Log.Error($"Processing request: {stepName}: end with error");
                emailMessages.Add($"Processing request: {stepName}: end with error");

                // Log.Info($"Poll for practice {practice.PracticeId} End With Error.");

                if (runMethod == HrmService.HRMRUNMETHOD.AUTOPOLL)
                    Log.Info($"Auto Poll for practice {practice.PracticeId} End With Error.");
                if (runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                    Log.Info($"Manual Poll for practice {practice.PracticeId} End With Error.");
            }
            return false;
        }
    }
}