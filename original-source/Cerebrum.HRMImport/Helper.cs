﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;

namespace Cerebrum.HRMImport
{
    public class Helper
    {
        public const string HRMXMLFILEDOWNLOADFOLDER = "Download";
        public const string HRMXMLFILEDECRYPTFOLDER = "Decrypt";
        public const string HRMXMLFILEARCHIVEORIGINALFOLDER = "Archive Original";
        public const string HRMXMLFILEARCHIVEDECRYPTFOLDER = "Archive Decrypt";

        private const string HRMFTPFOLDER = "HrmFtpFolder";
        private const string HRMAESDECRYPTIONKEY = "HrmAesDecryptionkey";

        public static List<HRMClinic> GetClinics(int practiceId, CerebrumContext context)
        {
            List<HRMClinic> clinics = new List<HRMClinic>();
            var clinicSettings = (from a in context.OfficeSettings
                                  join b in context.Offices on a.officeId equals b.Id
                                  where a.isActive && a.settingType == OfficeSettingType.HRM && (practiceId == 0 || b.PracticeId == practiceId)
                                  orderby a.officeId
                                  select a).ToList();

            HRMClinic clinic = new HRMClinic(); 
            foreach (var clinicSetting in clinicSettings)
            {
                if (clinic.officeId != clinicSetting.officeId)
                {
                    clinic = new HRMClinic();
                    clinic.officeId = clinicSetting.officeId;
                    clinics.Add(clinic);
                }

                switch (clinicSetting.key)
                {
                    case HRMFTPFOLDER:
                        clinic.ftpFolder = clinicSetting.value;
                        break;
                    case HRMAESDECRYPTIONKEY:
                        clinic.aesDecryptionkey = clinicSetting.value;
                        break;
                }
            }

            return clinics.Where(a => !(string.IsNullOrEmpty(a.ftpFolder) || string.IsNullOrEmpty(a.aesDecryptionkey))).ToList();
        }

        internal static bool CheckFolder(string tmpFolder)
        {
            if(!Directory.Exists(tmpFolder))
            {
                try
                {
                    Directory.CreateDirectory(tmpFolder);
                }
                catch(Exception x)
                {
                    Log.Error($"Exception creating folder {tmpFolder}\n"+x.Message);
                }
            }
            return Directory.Exists(tmpFolder);
        }

        public static string GetHrmFolder(string parentFolder, int practiceId, string subFolder)
        {
            return Path.Combine(Path.Combine(parentFolder, practiceId.ToString()), subFolder);
        }

        static public void SendEmail(string emailTo, List<string> emailMessages, string manualPollUser = "")
        {
            try
            {
                string emailFrom = ConfigurationManager.AppSettings["SMTP_From"];
                string SMTPTelus = ConfigurationManager.AppSettings["SMTPTelus"];
                string SMTPpw_Telus = ConfigurationManager.AppSettings["SMTPpw_Telus"];
                string SMTPun_Telus = ConfigurationManager.AppSettings["SMTPun_Telus"];

                bool isMissingData = false;
                if(string.IsNullOrWhiteSpace(emailFrom))
                {
                    Log.Error("Missing SMTP_From");
                    isMissingData = true;
                }
                if(string.IsNullOrWhiteSpace(emailTo))
                {
                    Log.Error("Missing HRM Error Recipients");
                    isMissingData = true;
                }
                if(string.IsNullOrWhiteSpace(SMTPTelus))
                {
                    Log.Error("Missing SMTPTelus");
                    isMissingData = true;
                }
                if(string.IsNullOrWhiteSpace(SMTPpw_Telus))
                {
                    Log.Error("Missing SMTPpw_Telus");
                    isMissingData = true;
                }
                if(string.IsNullOrWhiteSpace(SMTPun_Telus))
                {
                    Log.Error("Missing SMTPun_Telus");
                    isMissingData = true;
                }

                if(!isMissingData)
                {
                    // add use here
                    if(!string.IsNullOrWhiteSpace(manualPollUser))
                    {
                        emailMessages.Insert(0, $"Manual Poll by {manualPollUser}");
                    }
                    else
                    {
                        emailMessages.Insert(0, $"Auto-poll");
                    }
                    emailMessages.Insert(0, DateTime.Now.ToString());
                    emailMessages.Insert(0, "HRM error:");
                    string emailBody = string.Join(Environment.NewLine, emailMessages);
                    string emailSubject = "HRM import error";

                    if(!UtilityHelper.SendMail(emailFrom, emailTo, emailBody, emailSubject, SMTPTelus, SMTPpw_Telus, SMTPun_Telus))
                        Log.Error("Email error");
                }
            }
            catch (Exception e)
            {
                Log.Error($"Exception Sending email: {emailTo}" + e.Message);
            }
        }
    }

    public class HRMClinic
    {
        public int officeId { get; set; }
        public string ftpFolder { get; set; } = string.Empty;
        public string aesDecryptionkey { get; set; } = string.Empty;
    }
}