﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;

using log4net;
using WinSCP;
using Cerebrum.Data;
using Cerebrum.ViewModels.Common;

namespace Cerebrum.HRMImport
{
    public class HrmFile
    {
        private const string HRMINTERVAL = "HrmInterval";
        private const string HRMFTPURL = "HrmFtpUrl";
        private const string HRMFTPPORT = "HrmFtpPort";
        private const string HRMUSERNAME = "HrmUsername";
        private const string HRMSSHCERTIFICATEFILENAME = "HrmSSHCertificateFileName";

        public List<string> emailMessages = new List<string>();

        internal bool Download(VMPracticePoll practice, HrmService.HRMRUNMETHOD runMethod)
        // public void Download(int practiceId, HrmService.HRMRUNMETHOD runMethod)
        {
            bool res = true;
            try
            {
                if(practice.Port == null)
                {
                    Log.Error(practice.PracticeId, $"Missing ftp port for practice {practice.PracticeId}");
                    return false;
                }
                string userName = practice.User;
                string ftpUrl = practice.Url;
                int portNumber = practice.Port.Value;
                string sshCertificateFile = practice.sshCertificateFile;

                string downloadFolder = ConfigurationManager.AppSettings["DownloadFolder"];
                string archiveFolder = ConfigurationManager.AppSettings["ArchiveFolder"];

                if(!CheckParameters(practice.PracticeId, userName, ftpUrl, portNumber, sshCertificateFile, downloadFolder))
                {
                    return false;
                }

                // Setup session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = ftpUrl,
                    PortNumber = portNumber,
                    UserName = userName,
                    //Password = password,
                    GiveUpSecurityAndAcceptAnySshHostKey = true,
                    SshPrivateKeyPath = sshCertificateFile
                };

                Log.Info(practice.PracticeId, $"Download start ...");

                if(runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                    GlobalContext.Properties["practiceIdGlobal"] = practice.PracticeId;

                Log.Info(practice.PracticeId, $"Download start for practice (id: {practice.PracticeId}) ...");
                string clinicArchiveFolder = Path.Combine(archiveFolder, practice.PracticeId.ToString());
                string clinicDownloadFolder = Path.Combine(downloadFolder, practice.PracticeId.ToString());
                if(!Helper.CheckFolder(clinicArchiveFolder))
                {
                    Log.Error(practice.PracticeId, $"Download failed. Missing clinic Archive Folder {clinicArchiveFolder}");
                    return false;
                }
                if(!Helper.CheckFolder(clinicDownloadFolder))
                {
                    Log.Error(practice.PracticeId, $"Download failed. Missing clinic Download Folder {clinicDownloadFolder}");
                    return false;
                }

                using(Session session = new Session())
                {
                    try
                    {
                        // Connect
                        session.Open(sessionOptions);
                        DownLoad(session, practice, clinicArchiveFolder, clinicDownloadFolder);
                        Log.Info(practice.PracticeId, $"Download end for practice (id: {practice.PracticeId})");

                        if (!practice.Alert)
                        {
                            practice.Alert = true;
                            Log.Info(practice.PracticeId, $"Update alert status to 'on'");
                        }
                        //if (!emailAlert)
                        //{
                        //    emailAlert = true;
                        //    using (var context = new Cerebrum.Data.CerebrumContext())
                        //    { 
                        //        var dbPracCommType = context.PracticeCommTypes.Where(p => p.Id == practice.PracticeCommTypeId).FirstOrDefault();
                        //        if (dbPracCommType != null)
                        //        {
                        //            dbPracCommType.Alert = true;
                        //            context.SaveChanges(practice.User);

                        //            Log.Info(practice.PracticeId, $"Update alert status to 'on'");
                        //        }
                        //    }
                        //}
                    }
                    catch(Exception ex)
                    {
                        Log.Error(practice.PracticeId, ex.Message);
                        Log.Error(practice.PracticeId, $"sFtp Download end for practice (id: {practice.PracticeId}) with error");
                        emailMessages.Add($"sFtp Download end for practice. (id: {practice.PracticeId}) with error:\n" + ex.Message);
                        res = false;
                    }
                    Log.Info(practice.PracticeId, "Download end.");
                }
            }
            catch(Exception e)
            {
                Log.Error(practice.PracticeId, e.Message);
                emailMessages.Add(e.Message);
                Log.Error(practice.PracticeId, "sFtp Download end with error.");
                //emailMessages.Add("sFtp Download end with error.");
                emailMessages.Add($"sFtp Download end for practice. (id: {practice.PracticeId}) with error");
                res = false;
            }
            return res;
        }

        private bool CheckParameters(int practiceId, string userName, string ftpUrl, int portNumber, string sshCertificateFile, string downloadFolder)
        {
            bool isSettingMissing = false;
            if(string.IsNullOrWhiteSpace(userName))
            {
                Log.Error(practiceId, "Missing FTP User Name");
                emailMessages.Add("Missing FTP User Name");
                isSettingMissing = true;
            }
            if(string.IsNullOrWhiteSpace(ftpUrl))
            {
                Log.Error(practiceId, "Missing FTP Url");
                emailMessages.Add("Missing FTP Url");
                isSettingMissing = true;
            }
            if(portNumber == 0)
            {
                Log.Error(practiceId, "Missing FTP Port Number");
                emailMessages.Add("Missing FTP Port Number");
                isSettingMissing = true;
            }
            if(string.IsNullOrWhiteSpace(sshCertificateFile))
            {
                Log.Error(practiceId, "Missing FTP Certificate File");
                emailMessages.Add("Missing FTP Certificate File");
                isSettingMissing = true;
            }
            if(!Helper.CheckFolder(downloadFolder))
            {
                Log.Error(practiceId, $"Downloaded folder {downloadFolder} doesn't exist!");
                emailMessages.Add($"Downloaded folder {downloadFolder} doesn't exist!");
                isSettingMissing = true;
            }

            return (!isSettingMissing);
        }

        private bool DownLoad(Session session, VMPracticePoll practice, string archiveFolder, string downloadFolder)
        {
            bool res = true;
            try
            {
                string ftpFolder = practice.ftpFolder;
                // list files
                RemoteDirectoryInfo directory = session.ListDirectory(ftpFolder);
                var files = directory.Files;
                foreach (RemoteFileInfo fileInfo in files)
                {
                    try
                    {
                        Log.Info(practice.PracticeId, $"File: {fileInfo.Name}");
                        if (string.IsNullOrWhiteSpace(fileInfo.Name.Trim().Replace(".", string.Empty))) //   . or .. folder
                            continue;
                        //if (IsFileDownloaded(downloadFolder, archiveFolder, fileInfo.Name))
                        //continue;

                        Log.Info(practice.PracticeId, $"Start downloading file {fileInfo.Name}");
                        // download files
                        TransferOptions transferOptions = new TransferOptions();
                        transferOptions.TransferMode = TransferMode.Binary;
                        string destFileName = Path.Combine(downloadFolder, fileInfo.Name);
                        //TransferOperationResult transferResult = session.GetFiles(fileInfo.FullName, destFileName, false, transferOptions);
                        TransferOperationResult transferResult = session.GetFiles(fileInfo.FullName, destFileName, true, transferOptions);

                        //CopyFile(destFileName, archiveFolder, Path.GetFileName(destFileName));

                        // Throw on any error
                        transferResult.Check();
                        Log.Info(practice.PracticeId, $"Start downloading file {fileInfo.Name}: done");
                    }
                    catch(Exception ex)
                    {
                        Log.Error(practice.PracticeId, ex.Message);
                        Log.Error(practice.PracticeId, $"Start downloading file {fileInfo.Name}: error");
                        emailMessages.Add(ex.Message);
                        emailMessages.Add($"Start downloading file {fileInfo.Name}: error");
                        res = false;
                    }
                }
            }
            catch(Exception ex)
            {
                Log.Error(practice.PracticeId, "Error downwloading file from ftp: "+ex.Message);
                emailMessages.Add("Error downwloading file from ftp: "+ex.Message);
                res = false;
            }
            return res;
        }

        internal void Decrypt(VMPracticePoll practice, HrmService.HRMRUNMETHOD runMethod)
        {
            try
            {
                string downloadFolder = ConfigurationManager.AppSettings["DownloadFolder"];
                string archiveFolder = ConfigurationManager.AppSettings["ArchiveFolder"];
                string tmpFolder = ConfigurationManager.AppSettings["TmpFolder"];

                bool error = false;
                if(!Helper.CheckFolder(downloadFolder))
                {
                    Log.Error(practice.PracticeId, $"Downloaded folder {downloadFolder} doesn't exist!");
                    emailMessages.Add($"Downloaded folder {downloadFolder} doesn't exist!");
                    error = true;
                }
                if(!Helper.CheckFolder(archiveFolder))
                {
                    Log.Error(practice.PracticeId, $"Archived folder {archiveFolder} doesn't exist!");
                    emailMessages.Add($"Archived folder {archiveFolder} doesn't exist!");
                    error = true;
                }
                if(!Helper.CheckFolder(tmpFolder))
                {
                    Log.Error(practice.PracticeId, $"Tmp folder {tmpFolder} doesn't exist!");
                    emailMessages.Add($"Tmp folder {tmpFolder} doesn't exist!");
                    error = true;
                }

                if(error)
                    return;

                Log.Info(practice.PracticeId, $"Decrypt start ...");
                try
                {
                    if(runMethod == HrmService.HRMRUNMETHOD.MANUALPOLL)
                        GlobalContext.Properties["PracticeIdGlobal"] = practice.PracticeId;

                    Log.Info(practice.PracticeId, $"Decrypt start for practice (id: {practice.PracticeId}) ...");
                    string practiceArchiveFolder = Path.Combine(archiveFolder, practice.PracticeId.ToString());
                    string practiceDownloadFolder = Path.Combine(downloadFolder, practice.PracticeId.ToString());
                    string practiceTmpFolder = Path.Combine(tmpFolder, practice.PracticeId.ToString());
                    if(!Helper.CheckFolder(practiceArchiveFolder))
                    {
                        Log.Error(practice.PracticeId, $"Download failed. Missing practice Archive Folder {practiceArchiveFolder}");
                        return;
                    }
                    if(!Helper.CheckFolder(practiceTmpFolder))
                    {
                        Log.Error(practice.PracticeId, $"Download failed. Missing practice Download Folder {practiceTmpFolder}");
                        return;
                    }

                    Decrypt(practice.PracticeId, practice.decryptionCertificateFile, practiceDownloadFolder, practiceTmpFolder, practiceArchiveFolder);
                    Log.Info(practice.PracticeId, $"Decrypt end for practice (id: {practice.PracticeId})");
                }
                catch(Exception ex)
                {
                    Log.Error(practice.PracticeId, ex.Message);
                    Log.Error(practice.PracticeId, $"Decrypt end for practice (id: {practice.PracticeId}) with error");
                    emailMessages.Add($"Decrypt end for practice (id: {practice.PracticeId}) with error");
                }
                Log.Info(practice.PracticeId, "Decrypt end.");
            }
            catch(Exception e)
            {
                Log.Error(practice.PracticeId, e.Message);
                Log.Error(practice.PracticeId, "Decrypt end with error.");
                emailMessages.Add(e.Message);
                emailMessages.Add("Decrypt end with error.");
            }
        }

        private void Decrypt(int PracticeId, string keyString, string downloadFolder, string tmpFolder, string archiveFolder)
        {
            if (!Helper.CheckFolder(downloadFolder))
            {
                Log.Error(PracticeId, $"Downloaded folder {downloadFolder} doesn't exist!");
                emailMessages.Add($"Downloaded folder {downloadFolder} doesn't exist!");
                return;
            }

            string[] hrmFiles = Directory.GetFiles(downloadFolder, "*.xml");
            if (hrmFiles.Length == 0)
            {
                Log.Info(PracticeId, $"No hrm xml file in downloaded folder {downloadFolder}");
                return;
            }

            Log.Info(PracticeId, $"Total hrm files: {hrmFiles.Length}");
            foreach (var hrmFile in hrmFiles)
            {
                string fileName = Path.GetFileName(hrmFile);
                Log.Info(PracticeId, $"Decrypt file {fileName} ...");
                try
                {
                    bool decryptionSucc = true;
                    string decryptedFileName = Path.Combine(tmpFolder, Path.GetFileNameWithoutExtension(fileName.Replace("_encrypted", string.Empty)) + "_decrypted.xml");
                    if (AESEncryption.Decrypt(hrmFile, decryptedFileName, keyString))
                        Log.Info(PracticeId, $"Decrypt file {fileName} end.");
                    else
                    {
                        decryptionSucc = false;
                        Log.Error(PracticeId, $"Decrypt file {fileName}: failed");
                        emailMessages.Add($"Decrypt file {fileName}: failed");
                        emailMessages.Add("Decryption error: " + AESEncryption.ErrorMessage);
                    }
                    //MoveFile(hrmFile, archiveFolder, fileName);
                    CopyFile(hrmFile, archiveFolder, Path.GetFileName(hrmFile));
                    File.Delete(hrmFile);
                    if (decryptionSucc)
                    {
                        string archivedFileName = CopyFile(decryptedFileName, archiveFolder, Path.GetFileName(decryptedFileName));
                        if (Path.GetFileName(archivedFileName).ToLower() != Path.GetFileName(decryptedFileName).ToLower())
                        {
                            File.Delete(decryptedFileName);
                            File.Copy(archivedFileName, Path.Combine(tmpFolder, Path.GetFileName(archivedFileName)), true);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(PracticeId, ex.Message);
                    Log.Error(PracticeId, $"Decrypt file {fileName} end with error");
                    emailMessages.Add($"Decrypt file {fileName} end with error");
                    emailMessages.Add(ex.Message);
                }
            }
        }

        private bool IsFileDownloaded(string downloadFolder, string archiveFolder, string fileName)
        {
            string fileFullName = Path.Combine(archiveFolder, fileName);
            if (File.Exists(fileFullName))
                return true;

            fileFullName = Path.Combine(downloadFolder, fileName);
            if (File.Exists(fileFullName))
                return true;

            return false;
        }

        /*
        private void CreateNewFolder(string folder)
        {
            if(!Directory.Exists(folder))
            {
                try
                {
                    Directory.CreateDirectory(folder);
                }
                catch(Exception x)
                {
                    Log.Error($"Error creating folder {folder}");
                    Log.Error(x.Message);
                }
            }
        }
        */

        private void MoveFile(string oldFileName, string folderName, string newFileName)
        {
            string fileExtension = Path.GetExtension(newFileName).Trim();
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(newFileName).Trim();
            string fileName = Path.Combine(folderName, newFileName);
            for (int n = 2; n < 500; n++)
            {
                if (!File.Exists(fileName))
                {
                    File.Move(oldFileName, fileName);
                    break;
                }

                fileName = Path.Combine(folderName, fileNameWithoutExtension + "_" + n.ToString() + fileExtension);
            }
        }

        private string CopyFile(string oldFileName, string folderName, string newFileName)
        {
            string fileExtension = Path.GetExtension(newFileName).Trim();
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(newFileName).Trim();
            string fileName = Path.Combine(folderName, newFileName);

            //File.Copy(oldFileName, fileName, true);
            for (int n = 2; n < 2000; n++)
            {
                if (!File.Exists(fileName))
                {
                    File.Copy(oldFileName, fileName);
                    break;
                }

                fileName = Path.Combine(folderName, fileNameWithoutExtension + "_" + n.ToString() + fileExtension);
            }

            return fileName;
        }
    }
}