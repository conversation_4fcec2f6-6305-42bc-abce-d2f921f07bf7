﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AwareMD.DHDR.Seedwork
{
    public interface IDrugDispense<P>
        where P : IPatient, new()
    {
        string DispensedDate { get; set; }
        string PickupDate { get; set; }
        string GenericNameOfTheDispensedDrug { get; set; }
        string BrandNameOfTheDispensedDrug { get; set; }
        string AmountOfMedicationperDose { get; set; }
        string Frequency { get; set; }
        string DispensedDrugStrength { get; set; }
        string DrugDosageForm { get; set; }
        decimal? DispensedQuantityValue { get; set; }
        string DispensedQuantityUnit { get; set; }
        decimal? EstimatedDaysSupply { get; set; }
        string RefillsRemaining { get; set; }
        string QuantityRemaining { get; set; }
        string PrescriberFirstName { get; set; }
        string PrescriberLastName { get; set; }
        string PrescriberPhoneNumber { get; set; }
        string DispensingPharmacy { get; set; }
        string DispensingPharmacyFaxNumber { get; set; }
        string DispensingPharmacyPhoneNumber { get; set; }
        int RxCount { get; set; }
        bool Show { get; set; }
        int Group { get; set; }
        string DIN { get; set; }
        string TherapeuticClass { get; set; }
        string TherapeuticSubClass { get; set; }
        string RxNumber { get; set; }
        string PharmacistLicense { get; set; }
        string PharmacistSystem { get; set; }
        string PharmacistDescription { get; set; }
        string PrescriberLicense { get; set; }
        string PrescriberSystem { get; set; }
        string PrescriberDescription { get; set; }
        string PharmacyId { get; set; }
        string PharmacistName { get; set; }
        string MedicationCondition { get; set; }
        ICollection<CategoryCoding> CategoryCoding { get; set; }
        P Patient { get; set; }
    }
}
