﻿using System.Collections.Generic;

namespace AwareMD.DHDR.Seedwork
{
    public class ErrorOperationOutcome
    {
        public ErrorOperationOutcome()
        {
            issue = new List<EmrIssue>();
            text = new Text();
        }
        public string resourceType { get; set; }
        public Text text { get; set; }
        public List<EmrIssue> issue { get; set; }
    }

    public class Text
    {
        public string status { get; set; }
        public string div { get; set; }
    }
}
