﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AwareMD.DHDR.Seedwork
{
    public interface IDrugDispenseSummary<D, P>
        where P : IPatient, new()
        where D : IDrugDispense<P>, new()
    {
        ICollection<D> DrugDispenseList { get; set; }
        P Patient { get; set; }
        int DrugDispenseTotal { get; set; }
        List<EmrIssue> OperationOutcomeIssues { get; set; }
        ErrorOperationOutcome ErrorOperationOutcome { get; set; }
    }
}
