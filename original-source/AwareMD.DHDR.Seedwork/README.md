﻿# AwareMD.DHDR.Seedwork

[Project link - DevOps 2465](https://dev.azure.com/chrcc/Cerebrum3/_workitems/edit/2465)



## Introduction ##

* See Microsoft architecture e-book [Seedwork](https://learn.microsoft.com/en-us/dotnet/architecture/microservices/microservice-ddd-cqrs-patterns/seedwork-domain-model-base-classes-interfaces)
* Base class of all DHDR related elements in Cerebrum side


## Notes ##

* Please keep this library **as simple as possible, avoid adding complex dependencies** - this is a base class (domain entities and value objects) to be reused everywhere, the more complex the dependencies, the lower the maintainability and harder to upgrade in the future
* Do not add methods to handle any business logic in this class - add them to somewhere else such that *Utility, Shared or Framework*
* However, you can put Value Objects basic utility here, see [Implement value objects](https://learn.microsoft.com/en-us/dotnet/architecture/microservices/microservice-ddd-cqrs-patterns/implement-value-objects)
* DTO (base class of how Cerebrum and DHDR API exchange data) is being managed differently, see [here](https://chrcc.visualstudio.com/AwareMD%20Utilities/_git/DTO?path=/AwareMD.DHDR.Dto) 


