﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Linq;

namespace AwareMD.DHDR.Seedwork
{
    public class Common
    {
        public static string GetEnumDescription(Enum enumVal)
        {
            MemberInfo[] memInfo = enumVal.GetType().GetMember(enumVal.ToString());
            DescriptionAttribute attribute = CustomAttributeExtensions.GetCustomAttribute<DescriptionAttribute>(memInfo[0]);
            return attribute.Description;
        }

        public static OhCmsIdentifier GetOhCmsIdentifierByUri(string uri, List<OhCmsIdentifier> list)
        {
            OhCmsIdentifier identifier = list.Where(item => item.URI == uri).FirstOrDefault();

            return identifier;
        }

        #region private methods
        public static List<OhCmsIdentifier> SetOhCmsIdentifiers()
        {
            List<OhCmsIdentifier> list = new List<OhCmsIdentifier>()
            {
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-midwife-org", Description = "CMO assigned identifiers to midwifery clinics"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-audiologist-speech-language-pathologist", Description = "College of Audiologists and Speech-Language Pathologists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-chiropodist", Description = "College of Chiropodists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-chiropractor", Description = "College of Chiropractors of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-dental-hygienist", Description = "College of Dental Hygienists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-dental-technologist", Description = "College of Dental Technologists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-denturist", Description = "College of Denturists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-dietitian", Description = "College of Dietitians of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-homeopath", Description = "College of Homeopaths of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-kinesiologist", Description = "College of Kinesiologists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-massage-therapist", Description = "College of Massage Therapist of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-medical-laboratory-technologist", Description = "College of Medical Laboratory Technologists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-medical-radiation-techologist", Description = "College of Medical Radiation Technologists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-midwife", Description = "College of Midwives of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-naturopath", Description = "College of Naturopaths of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-nurse", Description = "College of Nurses of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-occupational-therapist", Description = "College of Occupational Therapists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-optician", Description = "College of Opticians of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-optometrist", Description = "College of Optometrists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-physician", Description = "College of Physicians and Surgeons of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-physiotherapist", Description = "College of Physiotherapists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-psychologist", Description = "College of Psychologists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-psychotherapist", Description = "College of Registered Psychotherapists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-respiratory-therapist", Description = "College of Respiratory Therapists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-traditional-chinese-medicine-acupuncturist", Description = "College of Traditional Chinese Medicine Practitioners and Acupuncturists of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-patient-hcn", Description = "Ontario, Canada Personal Health Number"},
                new OhCmsIdentifier{URI = "http://ehealthontario.ca/fhir/NamingSystem/ca-on-panorama-immunization-id", Description = "Ontario, Canada Panorama Immunization ID (OIID)"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-pharmacist", Description = "Ontario College of Pharmacists"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-pharmacist-org", Description = "Ontario College of Pharmacists - Org"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-social-worker-social-service-worker", Description = "Ontario College of Social Workers and Social Service Workers"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-health-care-facility-id", Description = "Ontario Organization Identifiers assigned by the Ministry of Health"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-lab-license", Description = "Ontario Organization Identifiers assigned by the Ministry of Health lab branch (LAB)"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-osteopath", Description = "Osteopathic College of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-dental-surgeon", Description = "Royal College of Dental Surgeons of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-provider-upi", Description = "Unique Provider Identifier"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-dental-surgeon", Description = "Royal College of Dental Surgeons of Ontario"},
                new OhCmsIdentifier{URI = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-chiropodist", Description = "College of Chiropodists of Ontario"}
            };

            return list;
        }
        #endregion
    }
}
