﻿using System;
using System.Collections.Generic;
using System.Text;

namespace AwareMD.DHDR.Seedwork
{
    public interface IPatient
    {
        /// <summary>
        /// See <see cref="Cerebrum.Shared.Gender"/> and <see cref="Hl7.Fhir.Model.AdministrativeGender"/>
        /// For both of them, 0 = Male, 1 = Female, 2 = Other, 3 = Unknown
        /// </summary>
        int? Gender { get; set; }
        string BirthDate { get; set; }
        string HealthCardNumber { get; set; }
        string Name { get; set; }
    }
}
