﻿using System;
using log4net;
using BillingService.Classes;

namespace BillingService
{
    class Program
    {
        private static readonly ILog Logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        //entry
        static void Main(string[] args)
        {
            try
            {
                if (args.Length > 0)
                {

                    string argument = args[0].Trim().ToLower();

                    if (argument.Contains("create"))  //create claim files
                    {
                        ClaimFiles files = new ClaimFiles();
                        files.Create();
                        return;
                    }

                    if (argument.Contains("send"))  //send claim files
                    {
                        ClaimFiles files = new ClaimFiles();
                        files.Send(false);
                        return;
                    }

                    if (argument.Contains("marksent"))  //mark claim files as "sent"
                    {
                        ClaimFiles files = new ClaimFiles();
                        files.Send(true);
                        return;
                    }

                    if (argument.Contains("download"))  //download EDT files
                    {
                        ClaimFiles files = new ClaimFiles();
                        files.Download(false);
                        return;
                    }

                    if (argument.Contains("confirm"))  //processing personal confirmed files
                    {
                        ClaimFiles files = new ClaimFiles();
                        files.Confirm();
                        return;
                    }

                    if (argument.Contains("reconcile"))  //Reconcile EDT files
                    {
                        ClaimFiles files = new ClaimFiles();
                        files.Download(true);
                        return;
                    }

                    if (argument.Contains("radatafile"))  //create RA data files
                    {
                        string dateFrom = args[1].Trim().ToLower();
                        if (!string.IsNullOrWhiteSpace(dateFrom))
                        {
                            ClaimFiles files = new ClaimFiles();
                            files.CreateRADataFiles(dateFrom);
                            return;
                        }
                    }
                }

                ShowApplicationUsage();
            }
            catch (Exception ex)
            {
                Logger.ErrorFormat("Error: {0}", ex.Message);
            }
        }

        static void ShowApplicationUsage()
        {
            Logger.Error("Error: missing/wrong parameter");
            Logger.Error("Usage 1: BillingService /create");
            Logger.Error("Usage 2: BillingService /send");
            Logger.Error("Usage 3: BillingService /download");
            //Logger.Error("Usage 4: BillingService /marksent");
            //Logger.Error("Usage 5: BillingService /reconcile");
            //Logger.Error("Usage 1: BillingService /radatafile 20200125  (yyyymmdd)");
        }
    }
}