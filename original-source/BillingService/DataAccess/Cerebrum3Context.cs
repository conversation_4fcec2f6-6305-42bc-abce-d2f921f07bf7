﻿using System.Data.Entity;
using Cerebrum.Data;

namespace BillingService.DataAccess
{
    /// <summary>
    /// The Cerebrum3Context db context.
    /// </summary>
    public class Cerebrum3Context : DbContext
    {
        public Cerebrum3Context() : base("name=C3Context")
        {
        }

        public DbSet<Bill> Bills { get; set; }
        public DbSet<Billing_Heb> BillingHebs { get; set; }
        public DbSet<Billing_Heh> BillingHehs { get; set; }
        public DbSet<Billing_Het> BillingHets { get; set; }
        public DbSet<BillStatus> BillStatuses { get; set; }
        public DbSet<Billing_ConsultRare> BillingConsultRares { get; set; }
        public DbSet<Billing_DiagnoseRare> BillingDiagnoseRares { get; set; }
        public DbSet<Billing_Group> BillingGroups { get; set; }
        public DbSet<Billing_FileSequence> BillingFileSequences { get; set; }
        public DbSet<Billing_TestRare> BillingTestRares { get; set; }
        public DbSet<Billing_Type> BillingTypes { get; set; }
        public DbSet<Office> Offices { get; set; }
        public DbSet<Appointment> Appointments { get; set; }
    }
}
