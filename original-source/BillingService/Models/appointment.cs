﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum3DB.DBClasses;

namespace BillingService.Models
{
    /// <summary>
    /// Appointment
    /// </summary>
    [Table("Appointments")]
    public class Appointment
    {
        [Key]
        public int Id { get; set; }
        public int OfficeId { get; set; }
        public DateTime appointmentTime { get; set; }
        public int PracticeDoctorId { get; set; }
        public int referralDoctorId { get; set; }
        [Display(Name = "Type/Reason")]
        public int AppointmentTypeId { get; set; }
        public PaymentMethod appointmentPaymentMethod { get; set; } = PaymentMethod.OHIP;
        public int PatientRecordId { get; set; }
    }
}