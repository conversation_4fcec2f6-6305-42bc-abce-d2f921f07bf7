﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;

namespace BillingService.Models
{
    /// <summary>
    /// Represents an Office in a Practice
    /// </summary>
    [Table("Office")]
    public class Office
    {
        [Key]
        public int Id { get; set; }
        public string name { get; set; }
        public string businessName { get; set; }
        public string BillGrNum_Consulting { get; set; }
        public string BillGrNum_Tech { get; set; }
        public string BillGrNum_Prof { get; set; }
        public string BillGrNum_IHF { get; set; }
        public int OfficeSpeciality { get; set; }
    }
}