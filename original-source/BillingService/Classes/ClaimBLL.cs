﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.IO;
using log4net;
using Cerebrum.Data;

namespace BillingService.Classes
{
    //main function: GetDoctorEdtErrorData  (this function exists in ..\cerebrum.bll\bill\reportbll.cs)
    public class ClaimBLL
    {
        private const string DATEFORMAT = "MM/dd/yyyy";
        private const string DATEFORMAT_YYYYMMDD = "yyyyMMdd";

        CerebrumContext context;
        static readonly ILog Logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Constructor of ClaimBLL
        /// </summary>
        public ClaimBLL()
        {
            context = new CerebrumContext(0);
        }

        public void Dispose()
        {
            context = null;
        }

        public List<DoctorEdtErrorData> GetDoctorEdtErrorData(int practiceId, string edtErrorFileName)
        {
            DoctorEdtErrorData doctorEdtData = null;
            PatientEdtErrorData patientEdtData = null;
            PaymentEdtErrorData paymentEdtData = null;
            List<DoctorEdtErrorData> doctorEdtErrorData = new List<DoctorEdtErrorData>();
            string record;
            string[] records = File.ReadAllLines(edtErrorFileName);

            for (int n = 0; n < records.Count(); n++)
            {
                //record = records[n];
                record = records[n] + "".PadLeft(100, ' ');
                switch (record.Substring(0, 3).ToUpper())
                {
                    case "HX1":
                        doctorEdtData = ProcessHX1(practiceId, record);
                        doctorEdtErrorData.Add(doctorEdtData);
                        break;
                    case "HXH":
                        patientEdtData = ProcessHXH(record);
                        doctorEdtData.patientEdtErrorData.Add(patientEdtData);
                        break;
                    case "HX8":
                        paymentEdtData = ProcessHX8(record);
                        patientEdtData.paymentEdtErrorData.Add(paymentEdtData);
                        break;
                    case "HXT":
                        paymentEdtData = ProcessHXT(record, patientEdtData.errorCode);
                        patientEdtData.paymentEdtErrorData.Add(paymentEdtData);
                        break;

                }
            }

            return doctorEdtErrorData;
        }

        private DoctorEdtErrorData ProcessHX1(int practiceId, string data)
        {
            DoctorEdtErrorData doctorEdtData = new DoctorEdtErrorData();
            //string edtGroupId = data.Substring(23, 4);
            string billingNumber = data.Substring(27, 6);
            //string specialtyCode = data.Substring(33, 2);
            var externalDoctor = (from e in context.ExternalDoctors
                                  join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                                  where e.OHIPPhysicianId == billingNumber && p.PracticeId == practiceId
                                  select new { e.firstName, e.lastName, applicationUserId = p.ApplicationUserId }).FirstOrDefault();
            if (externalDoctor != null)
            {
                doctorEdtData.applicationUserId = externalDoctor.applicationUserId;
                doctorEdtData.doctorName = externalDoctor.firstName + " " + externalDoctor.lastName;
            }
            else
            {
                doctorEdtData.doctorName = string.Format("Unknown (who's billing number is: {0})", billingNumber);
            }
            doctorEdtData.billingNumber = billingNumber;
            doctorEdtData.ProcessDate = string.Format("{0}/{1}/{2}", data.Substring(42, 2), data.Substring(44, 2), data.Substring(38, 4));

            return doctorEdtData;
        }

        private PatientEdtErrorData ProcessHXH(string data)
        {
            PatientEdtErrorData patientEdtErrorData = new PatientEdtErrorData();
            patientEdtErrorData.healthCardNumber = data.Substring(3, 10);
            patientEdtErrorData.healthCardVersion = data.Substring(13, 2);
            patientEdtErrorData.dateOfBirth = string.Format("{0}/{1}/{2}", data.Substring(19, 2), data.Substring(21, 2), data.Substring(15, 4));
            patientEdtErrorData.errorCode = string.IsNullOrEmpty(data.Substring(64, 15)) ? string.Empty : data.Substring(64, 15).Trim();
            string serviceLocationIndicator = data.Substring(57, 3);
            if (serviceLocationIndicator == "HIP" || serviceLocationIndicator == "HOP")
                patientEdtErrorData.admissionActionId = int.Parse(data.Substring(23, 8));
            else
                patientEdtErrorData.appointmentId = int.Parse(data.Substring(23, 8));
            var patient = (from a in context.HealthCards join b in context.Demographics on a.DemographicId equals b.Id where a.number == patientEdtErrorData.healthCardNumber select b).FirstOrDefault();
            if (patient == null)
                patientEdtErrorData.patientName = "Unknown";
            else
            {
                patientEdtErrorData.patientRecordId = patient.PatientRecordId;
                patientEdtErrorData.patientName = patient.firstName + " " + patient.lastName;
            }

            string billingNumber = data.Substring(35, 6);
            var externalDoctor = context.ExternalDoctors.Where(a => a.OHIPPhysicianId == billingNumber).FirstOrDefault();
            if (externalDoctor != null)
            {
                patientEdtErrorData.referralDoctorId = externalDoctor.Id;
                patientEdtErrorData.referralDoctorName = externalDoctor.firstName + " " + externalDoctor.lastName;
            }
            else
                patientEdtErrorData.referralDoctorName = billingNumber;

            patientEdtErrorData.referralNumber = billingNumber;

            //int appointmentId = int.Parse(patientEdtErrorData.appointmentId);
            if (patientEdtErrorData.appointmentId > 0)
            {
                var appointment = context.Appointments.Where(a => a.Id == patientEdtErrorData.appointmentId).FirstOrDefault();
                if (appointment != null)
                {
                    patientEdtErrorData.officeId = appointment.OfficeId;
                    patientEdtErrorData.appointmentDate = appointment.appointmentTime.ToString(DATEFORMAT, CultureInfo.InvariantCulture);
                }
            }
            else if (patientEdtErrorData.admissionActionId > 0)
            {
                var admission = (from a in context.HDAdmissions
                                 join b in context.HDAdmissionActions on a.Id equals b.AdmissionId
                                 where b.Id == patientEdtErrorData.admissionActionId
                                 select new { a.DateAdmitted }).FirstOrDefault();
                if (admission != null)
                    patientEdtErrorData.appointmentDate = admission.DateAdmitted.ToString(DATEFORMAT, CultureInfo.InvariantCulture);
            }

            return patientEdtErrorData;
        }

        private PaymentEdtErrorData ProcessHX8(string data)
        {
            PaymentEdtErrorData paymentEdtErrorData = new PaymentEdtErrorData();
            paymentEdtErrorData.hx8Message = data.Substring(5, 55);
            return paymentEdtErrorData;
        }

        private PaymentEdtErrorData ProcessHXT(string data, string errorCode)
        {
            PaymentEdtErrorData paymentEdtErrorData = new PaymentEdtErrorData();
            paymentEdtErrorData.serviceCode = data.Substring(3, 5);
            paymentEdtErrorData.fee = string.Format("{0:C}", (float.Parse(data.Substring(10, 6)) / 100));
            paymentEdtErrorData.numberOfServices = data.Substring(16, 2);
            DateTime serviceDate = DateTime.MaxValue;
            if (!DateTime.TryParseExact(data.Substring(18, 8), DATEFORMAT_YYYYMMDD, CultureInfo.InvariantCulture, DateTimeStyles.None, out serviceDate))
                serviceDate = DateTime.MaxValue;
            //paymentEdtErrorData.serviceDate = DateTime.TryParseExact(); string.Format("{0}/{1}/{2}", data.Substring(22, 2), data.Substring(24, 2), data.Substring(18, 4));
            paymentEdtErrorData.serviceDate = serviceDate;
            paymentEdtErrorData.diagnoseCode = data.Substring(26, 4);
            paymentEdtErrorData.errorCode = string.IsNullOrEmpty(data.Substring(64, 15)) ? string.Empty : data.Substring(64, 15).Trim();
            if (string.IsNullOrEmpty(paymentEdtErrorData.errorCode))
                paymentEdtErrorData.errorCode = errorCode;
            if (!string.IsNullOrEmpty(paymentEdtErrorData.errorCode))
            {
                var edtError = context.BillingEDTErrorCodes.Where(a => a.errorCode == paymentEdtErrorData.errorCode).FirstOrDefault();
                if (edtError == null)
                    paymentEdtErrorData.errorMessage = "Cannot find the error message";
                else
                    paymentEdtErrorData.errorMessage = edtError.errorMessage;
            }

            return paymentEdtErrorData;
        }
    }

    /// <summary>
    /// Doctor Edt Error Data
    /// </summary>
    public class DoctorEdtErrorData
    {
        public string applicationUserId { get; set; } = string.Empty;
        public string doctorName { get; set; } = string.Empty; 
        public string billingNumber { get; set; } = string.Empty;
        public string ProcessDate { get; set; } = string.Empty;
        public List<PatientEdtErrorData> patientEdtErrorData { get; set; } = new List<PatientEdtErrorData>();
    }

    /// <summary>
    /// Patient Edt Error Data
    /// </summary>
    public class PatientEdtErrorData
    {
        public int patientRecordId { get; set; } = 0;
        public string patientName { get; set; } = string.Empty;
        public string healthCardNumber { get; set; } = string.Empty;
        public string healthCardVersion { get; set; } = string.Empty;
        public string dateOfBirth { get; set; } = string.Empty;
        public int referralDoctorId { get; set; } = 0;
        public string referralDoctorName { get; set; } = string.Empty;
        public string referralNumber { get; set; } = string.Empty;
        public int appointmentId { get; set; } = 0;
        public int admissionActionId { get; set; } = 0;
        public int officeId { get; set; } = 0;
        public string appointmentDate { get; set; } = string.Empty; 
        public string admissionDate { get; set; } = string.Empty;
        public string errorCode { get; set; } = string.Empty;
        public List<PaymentEdtErrorData> paymentEdtErrorData { get; set; } = new List<PaymentEdtErrorData>();
    }

    /// <summary>
    /// Payment Edt Error Data
    /// </summary>
    public class PaymentEdtErrorData
    {
        public string hx8Message { get; set; } = string.Empty;
        public string serviceCode { get; set; } = string.Empty;
        public string fee { get; set; } = string.Empty;
        public string numberOfServices { get; set; } = string.Empty;
        public DateTime serviceDate { get; set; }
        public string diagnoseCode { get; set; } = string.Empty;
        public string errorCode { get; set; } = string.Empty;
        public string errorMessage { get; set; } = string.Empty;
    }
}