﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.Entity.SqlServer;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.IO;
using System.Reflection;

using log4net;
using Newtonsoft.Json;

using Cerebrum.Data;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.ViewModels.Bill;

namespace BillingService.Classes
{
    public class ClaimFiles
    {
        private class RAPayment
        {
            public string billingNumber;
            public string ohipNumber;
            public DateTime serviceDate;
            public string serviceCode;
            public int amountPaid;
            public string explanatoryCode;
            public DateTime ohipPayDate;
            public string MOHId;
        }

        private class DownloadData
        {
            public string billingNumber;
            public int officeId;
            public string edtGroupId;
            public int PracticeId;
            public Billing_Group edtGroup;
            public ExternalDoctor doctor;
            public string MCEDTMailbox;
            public string MCEDTPassword;
            public int practiceDoctorId;
            public List<string> edtIds;
        }

        const string BILLSTATUS_BILLED = "billed";
        const string BILLSTATUS_FILED = "filed";
        const string BILLSTATUS_SENT = "sent";
        const string BILLSTATUS_CONFIRMED = "confirmed";
        const string BILLSTATUS_PAID = "paid";
        const string BILLSTATUS_REFUSED = "refused";
        const string BILLSTATUS_READJUSTED = "readjusted";
        const string BILLSTATUS_STALE = "stale";
        const string BILLSTATUS_MIXED = "mixed";

        const int EDTERRORSTATUS_FIXED = 1;
        const int EDTERRORSTATUS_NOTFIXED = 0;

        const string DATEFORMAT = "yyyyMMdd";
        const string DATEFORMAT2 = "MM/dd/yyyy";
        const string DATEFORMAT3 = "yyyyMM";
        const string FOLDER_GROUP = "Group";
        const string FOLDER_HOSPITAL = "Hospital";
        const string FOLDER_X = "X";
        const string FOLDER_RECONCILIATION = "Reconciliation";
        const string FOLDER_ERROR = "Error";
        const string FOLDER_CONFIRMATION = "Confirmation";
        const string FOLDER_ARCHIVE = "Archive";
        const string FOLDER_LOG = "Log";
        const string FOLDER_RA = "RA";
        const string FOLDER_NOTFOUND = "Notfound";

        const string RA_DATA_FILE_EXTENSION = ".data";

        private const string PERSONAL_GROUPID = "0000";
        private const string DEFAULT_MOHCODE = "F";
        private const string DEFAULT_SERVICELOCATIONINDICATOR = "OFF";

        const string BATCHTOTALS = "BATCH TOTALS";
        const string PARAMETERS_UPLOADFILE = "-u {0} CL {1} {2} {3} {4}";      //filename username password MOHID AuditID      //MOHID'length must be at lease 5 (pad left "0" if not)
        const string PARAMETERS_LISTFILE = "-l {0} {1} {2} {3}";      //username password MOHID AuditID
        const string PARAMETERS_DOWNLOADFILE = "-a {0} {1} {2} {3} {4} {5}";      //download_folder username password MOHID AuditID EdtId
        const string EBSCLIENT = "EBSClient.exe";
        const string OHIPFILE = "Ohipids.txt";

        int hCount;
        int rCount;
        int tCount;
        string claimFileOutgoingFolder;
        string edtFileOwner;
        string edtGroupId;
        string MCEDTMailbox;
        string MCEDTPassword;
        string MOHId;
        string MCEDTId;

        int billStatus_BilledId = 0;
        int billStatus_FiledId = 0;
        int billStatus_SentId = 0;
        int billStatus_ConfirmedId = 0;
        int billStatus_PaidId = 0;
        int billStatus_RefusedId = 0;
        int billStatus_ReadjustedId = 0;
        int billStatus_StaleId = 0;
        int billStatus_MixId = 0;

        Billing_EDTFile edtFile = null;
        List<int> billDetailIds;
        CerebrumContext context;
        static readonly ILog Logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Constructor of ClaimFiles
        /// </summary>
        public ClaimFiles()
        {
            context = new CerebrumContext(0);
        }

        public void Dispose()
        {
            context = null;
        }

        //create claim files
        public void Create()
        {
            Logger.Info("Start creating claim files ...");
            claimFileOutgoingFolder = ConfigurationManager.AppSettings["ClaimFileOutgoingFolder"];
            ReadBillStatusIDs();

            var billDetails = context.BillDetails.AsNoTracking()
                .Where(a => a.billStatusId == billStatus_BilledId && (a.payment == PaymentMethod.OHIP || a.payment == PaymentMethod.RMB || a.payment == PaymentMethod.WCB))
                .ToList()
                .OrderBy(a => a.date.Date)
                .ThenBy(a => a.officeId)
                .ThenBy(a => a.edtGroupId)
                .ThenBy(a => a.practiceDoctorId)
                .ThenBy(a => a.specialty)
                .ThenBy(a => a.PatientRecordId)
                .ThenBy(a => a.claimNumber)
                .ThenBy(a => a.referralDoctorId)
                .ThenBy(a => a.payment)
                .ThenBy(a => a.hospitalCode)
                .ThenBy(a => a.dateAdmitted)
                .ThenBy(a => a.manualReview)
                .ThenBy(a => a.serviceLocationIndicator)
                .ToList();

            Logger.InfoFormat("Total billing items (billed status): {0}", billDetails.Count());
            Create(true, billDetails);

            billDetails = context.BillDetails.AsNoTracking()
                .Where(a => a.billStatusId == billStatus_StaleId && (a.payment == PaymentMethod.OHIP || a.payment == PaymentMethod.RMB || a.payment == PaymentMethod.WCB) && a.manualReview == "Y")
                .ToList()
                .OrderBy(a => a.officeId)
                .ThenBy(a => a.edtGroupId)
                .ThenBy(a => a.practiceDoctorId)
                .ThenBy(a => a.specialty)
                .ThenBy(a => a.date.Date)
                .ThenBy(a => a.PatientRecordId)
                .ThenBy(a => a.claimNumber)
                .ThenBy(a => a.referralDoctorId)
                .ThenBy(a => a.payment)
                .ThenBy(a => a.hospitalCode)
                .ThenBy(a => a.dateAdmitted)
                .ThenBy(a => a.manualReview)
                .ThenBy(a => a.serviceLocationIndicator)
                .ToList();

            if (billDetails.Count > 0)
            {
                Logger.InfoFormat("Total billing items (stale status): {0}", billDetails.Count());
                Create(false, billDetails);
            }

            Logger.Info("Creating claim files --- Done");
        }

        private void Create(bool isBilledClaim, List<BillDetail> billDetails)
        {
            int officeId = -1;
            string edtGroupId = string.Empty;
            int practiceDoctorId = -1;
            int specialty = -1;
            int patientRecordId = -1;
            int referralDoctorId = -1;
            DateTime billingDate = DateTime.MinValue;
            int fileSequence = 0;
            string claimNumber = string.Empty;
            string claimFileName = string.Empty;
            PaymentMethod payment = PaymentMethod.OHIP;
            string hospitalCode = string.Empty;
            string dateAdmitted = string.Empty;
            string manualReview = string.Empty;
            string serviceLocationIndicator = string.Empty;

            Billing_FileSequence sequence = null;
            StreamWriter claimFile = null;

            if (billDetails.Count() > 0)
            {
                for (int n = 0; n < billDetails.Count(); n++)
                {
                    BillDetail billDetail = billDetails[n];
                    try
                    {
                        if (string.IsNullOrWhiteSpace(billDetail.edtGroupId))
                        {
                            Logger.ErrorFormat($"edtGroupId is null/empty for id: {billDetail.id}");
                            continue;
                        }
                        if (billDetail.edtGroupId == PERSONAL_GROUPID && string.IsNullOrWhiteSpace(billDetail.billingNumber))
                        {
                            Logger.ErrorFormat($"billingNumber is null/empty for id: {billDetail.id}");
                            continue;
                        }

                        if (Cerebrum.BLL.Utility.UtilityHelper.IsCovidAssessment(billDetail))
                        {
                            Logger.InfoFormat($"This is covid assessment. id: {billDetail.id}");
                            if (billDetail.numberOfServices == 0)
                            {
                                Logger.InfoFormat("numberOfServices is 0");
                                continue;
                            }
                            billDetail.healthCardNumber = string.Empty;
                            billDetail.healthCardVersion = string.Empty;
                            billDetail.dateOfBirth = DateTime.MinValue;
                            billDetail.serviceLocationIndicator = string.Empty;
                        }

                        bool newClaimFile = false;
                        if (isBilledClaim)
                        {
                            if (billingDate.Date != billDetail.date.Date || billDetail.officeId != officeId || billDetail.edtGroupId != edtGroupId || billDetail.practiceDoctorId != practiceDoctorId || billDetail.specialty != specialty)
                            {
                                newClaimFile = true;
                            }
                        }
                        else
                        {
                            if (billDetail.officeId != officeId || billDetail.edtGroupId != edtGroupId || billDetail.practiceDoctorId != practiceDoctorId || billDetail.specialty != specialty)
                            {
                                newClaimFile = true;
                            }
                        }

                        if (newClaimFile)
                        {
                            if (claimFile != null)
                            {
                                CloseClaimFile(sequence, hCount, rCount, tCount, claimFile);
                                if (isBilledClaim)
                                {
                                    Logger.InfoFormat($"Processing date={billingDate.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)}, office (id: {officeId}), edtGroup (id: {edtGroupId}), doctor (id: {practiceDoctorId}), specialty (id: {specialty}) --- Done");
                                }
                                else
                                {
                                    Logger.InfoFormat($"Processing office (id: {officeId}), edtGroup (id: {edtGroupId}), doctor (id: {practiceDoctorId}), specialty (id: {specialty}) --- Done");
                                }
                            }

                            billingDate = billDetail.date;
                            officeId = billDetail.officeId;
                            edtGroupId = billDetail.edtGroupId;
                            practiceDoctorId = billDetail.practiceDoctorId;
                            specialty = billDetail.specialty;
                            if (isBilledClaim)
                            {
                                Logger.InfoFormat($"Start Processing date={billingDate.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)}, office (id: {officeId}), edtGroup (id: {edtGroupId}), doctor (id: {practiceDoctorId}), specialty (id: {specialty}) ...");
                            }
                            else
                            {
                                Logger.InfoFormat($"Start Processing office (id: {officeId}), edtGroup (id: {edtGroupId}), doctor (id: {practiceDoctorId}), specialty (id: {specialty}) ...");
                            }

                            fileSequence = 0;
                            if (edtGroupId == PERSONAL_GROUPID)
                                sequence = context.BillingFileSequences.Where(a => a.edtGroupId == edtGroupId && a.billingNumber == billDetail.billingNumber).FirstOrDefault();
                            else
                                sequence = context.BillingFileSequences.Where(a => a.edtGroupId == edtGroupId).FirstOrDefault();
                            if (sequence == null)
                            {
                                sequence = new Billing_FileSequence { officeId = officeId, edtGroupId = edtGroupId, billingNumber = billDetail.billingNumber, specialty = specialty, sequenceNumber = 0 };
                                context.BillingFileSequences.Add(sequence);
                            }
                            else
                            {
                                fileSequence = sequence.sequenceNumber >= 999 ? 0 : sequence.sequenceNumber + 1;
                                sequence.sequenceNumber = fileSequence;
                            }

                            claimFile = CreateClaimFile(sequence, billDetail, out claimFileName);

                            WriteHEB(fileSequence, billDetail, claimFile);
                            hCount = 0;
                            rCount = 0;
                            tCount = 0;
                            patientRecordId = -1;
                            referralDoctorId = -1;
                        }

                        if (!(billDetail.PatientRecordId == patientRecordId && billDetail.claimNumber == claimNumber
                             && billDetail.referralDoctorId == referralDoctorId && billDetail.payment == payment
                             && billDetail.hospitalCode == hospitalCode && billDetail.dateAdmitted == dateAdmitted
                             && billDetail.manualReview == manualReview && billDetail.serviceLocationIndicator == serviceLocationIndicator))
                        {
                            patientRecordId = billDetail.PatientRecordId;
                            claimNumber = billDetail.claimNumber;
                            referralDoctorId = billDetail.referralDoctorId;
                            payment = billDetail.payment;
                            hospitalCode = billDetail.hospitalCode;
                            dateAdmitted = billDetail.dateAdmitted;
                            manualReview = billDetail.manualReview;
                            serviceLocationIndicator = billDetail.serviceLocationIndicator;
                            WriteHEH(billDetail, claimFile);
                        }

                        WriteHET(billDetail, claimFile);
                        billDetailIds.Add(billDetail.id);
                    }
                    catch (Exception ex)
                    {
                        claimFile = null;
                        LogException($"Error when creaing claim files for billdetail (id: {billDetail.id})", ex);
                    }
                }

                CloseClaimFile(sequence, hCount, rCount, tCount, claimFile);
                if (isBilledClaim)
                {
                    Logger.InfoFormat($"Processing date={billingDate.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)}, office (id: {officeId}), edtGroup (id: {edtGroupId}), doctor (id: {practiceDoctorId}), specialty (id: {specialty}) --- Done");
                }
                else
                {
                    Logger.InfoFormat($"Processing office (id: {officeId}), edtGroup (id: {edtGroupId}), doctor (id: {practiceDoctorId}), specialty (id: {specialty}) --- Done");
                }
            }
        }

        private StreamWriter CreateClaimFile(Billing_FileSequence sequence, BillDetail billDetail, out string claimFileName)
        {
            claimFileName = string.Empty;
            string folder = string.Empty;
            if (billDetail.edtGroupId == PERSONAL_GROUPID)
            {
                claimFileName = string.Format("H{0}{1}.{2}", ((char)('A' + DateTime.Now.Month - 1)).ToString(), billDetail.billingNumber, GetStringPadLeft(sequence.sequenceNumber, '0', 3));
                folder = Path.Combine(claimFileOutgoingFolder, billDetail.billingNumber.ToString());
            }
            else
            {
                claimFileName = string.Format("H{0}{1}.{2}", ((char)('A' + DateTime.Now.Month - 1)).ToString(), billDetail.edtGroupId, GetStringPadLeft(sequence.sequenceNumber, '0', 3));
                folder = Path.Combine(Path.Combine(claimFileOutgoingFolder, FOLDER_GROUP), billDetail.edtGroupId);
            }

            folder = Path.Combine(folder.Trim(), DateTime.Now.ToString(DATEFORMAT3));
            if (!Directory.Exists(folder))
            {
                //Logger.InfoFormat("Create new outgoing folder: {0}", folder);
                Directory.CreateDirectory(folder);
            }
            Logger.InfoFormat("New claim file: {0}", claimFileName);
            claimFileName = Path.Combine(folder, claimFileName);

            edtFile = new Billing_EDTFile();
            edtFile.officeId = billDetail.officeId;
            edtFile.edtGroupId = billDetail.edtGroupId;
            edtFile.practiceDoctorId = billDetail.practiceDoctorId;
            edtFile.billingNumber = billDetail.billingNumber;
            edtFile.specialty = billDetail.specialty;
            edtFile.sequenceNumber = sequence.sequenceNumber;
            edtFile.claimFileName = claimFileName;
            edtFile.billStatusId = billStatus_FiledId;

            billDetailIds = new List<int>();

            return new StreamWriter(claimFileName, false);
        }

        private void CloseClaimFile(Billing_FileSequence sequence, int hCount, int rCount, int tCount, StreamWriter claimFile)
        {
            if (claimFile == null)
                return;

            WriteHEE(hCount, rCount, tCount, claimFile);
            claimFile.Close();

            context.BillingEDTFiles.Add(edtFile);
            context.SaveChanges();

            UpdateBillStatus(edtFile.id, billDetailIds);
        }

        private void WriteHEB(int fileSequence, BillDetail billDetail, StreamWriter claimFile)
        {
            if (claimFile == null)
                return;

            string edtBatchId = DateTime.Now.ToString(DATEFORMAT, CultureInfo.InvariantCulture) + GetStringPadLeft(fileSequence, '0', 4);
            string data = "HEBV03"
                + GetStringPadLeft(billDetail.MOHCODE, ' ', 1)
                + edtBatchId
                + new string(' ', 6)
                + GetStringPadLeft((billDetail.officeId == 0 ? "0000" : billDetail.edtGroupId), '0', 4)
                + GetStringPadLeft(billDetail.billingNumber, '0', 6)
                + GetStringPadLeft(billDetail.specialty, '0', 2)
                + new string(' ', 42);

            claimFile.WriteLine(data);

            edtFile.edtBatchId = edtBatchId;
        }

        private void WriteHEH(BillDetail billDetail, StreamWriter claimFile)
        {
            if (claimFile == null)
                return;

            if (billDetail.payment == PaymentMethod.OHIP || billDetail.payment == PaymentMethod.RMB || billDetail.payment == PaymentMethod.WCB)
            {
                string payment = "HCPP";
                if (billDetail.payment == PaymentMethod.RMB)
                    payment = "RMBP";
                else if (billDetail.payment == PaymentMethod.WCB)
                    payment = "WCBP";

                string dateAdmitted = new string(' ', 8);
                if (billDetail.appointmentId > 0 && billDetail.edtGroupId != PERSONAL_GROUPID && !string.IsNullOrEmpty(billDetail.hospitalCode))
                {
                    dateAdmitted = billDetail.serviceDate.ToString(DATEFORMAT); ;
                }

                if (billDetail.serviceLocationIndicator == "HIP")
                    dateAdmitted = billDetail.dateAdmitted;

                string data = "HEH"
                                + (string.IsNullOrEmpty(billDetail.healthCardNumber) ? new string(' ', 10) : GetStringPadLeft(billDetail.healthCardNumber.ToUpper(), '0', 10))
                                + GetStringPadLeft(billDetail.healthCardVersion.ToUpper(), ' ', 2)
                                + (billDetail.dateOfBirth == DateTime.MinValue ? new string(' ', 8) : billDetail.dateOfBirth.ToString(DATEFORMAT, CultureInfo.InvariantCulture))
                                + GetStringPadLeft(billDetail.claimNumber, '0', 8)
                                + payment
                                + GetStringPadLeft(billDetail.referralBillingNumber, '0', 6)
                                //+ new string(' ', 17)
                                + GetStringPadLeft(billDetail.hospitalCode, ' ', 4) + GetStringPadLeft(dateAdmitted, ' ', 8) + new string(' ', 4) + GetStringPadLeft(billDetail.manualReview, ' ', 1)
                                + GetStringPadRight(billDetail.serviceLocationIndicator, ' ', 4)
                                + new string(' ', 17);

                claimFile.WriteLine(data);
                hCount++;

                if (billDetail.payment == PaymentMethod.RMB)
                {
                    data = "HER"
                            + (string.IsNullOrEmpty(billDetail.healthCardNumber) ? new string(' ', 12) : GetStringPadRight(billDetail.healthCardNumber.ToUpper(), ' ', 12))
                            + GetStringPadRight(billDetail.lastName, ' ', 9)
                            + GetStringPadRight(billDetail.firstName, ' ', 5)
                            + (billDetail.gender == Gender.M ? "1" : "2")
                            + GetStringPadLeft(billDetail.province, ' ', 2)
                            + new string(' ', 47);

                    claimFile.WriteLine(data);
                    rCount++;
                }
            }
        }

        private void WriteHET(BillDetail billDetail, StreamWriter claimFile)
        {
            if (claimFile == null)
                return;

            string data = "HET"
                            + GetStringPadLeft(billDetail.serviceCode, '0', 5)
                            + new string(' ', 2)
                            + GetStringPadLeft(billDetail.fee, '0', 6)
                            + GetStringPadLeft(billDetail.numberOfServices, '0', 2)
                            + billDetail.serviceDate.ToString(DATEFORMAT, CultureInfo.InvariantCulture)
                            + GetStringPadRight(GetStringPadLeft(billDetail.diagnoseCode, '0', 3), ' ', 4)
                            + new string(' ', 49);

            claimFile.WriteLine(data);
            tCount++;
        }

        private void WriteHEE(int hCount, int rCount, int tCount, StreamWriter claimFile)
        {
            if (claimFile == null)
                return;

            string data = "HEE"
                + GetStringPadLeft(hCount, '0', 4)
                + GetStringPadLeft(rCount, '0', 4)
                + GetStringPadLeft(tCount, '0', 5)
                + new string(' ', 63);

            claimFile.WriteLine(data);
        }

        private string GetStringPadLeft(int data, char padChar, int length)
        {
            return GetStringPadLeft(data.ToString(), padChar, length);
        }

        private string GetStringPadLeft(string data, char padChar, int length)
        {
            if (string.IsNullOrEmpty(data))
                return new string(padChar, length);
            if (data.Length >= length)
                return data.Substring(0, length);

            return data.PadLeft(length, padChar);
        }

        private string GetStringPadRight(string data, char padChar, int length)
        {
            if (string.IsNullOrEmpty(data))
                return new string(padChar, length);
            if (data.Length >= length)
                return data.Substring(0, length);

            return data.PadRight(length, padChar);
        }

        //send claim files
        public void Send(bool skipSending)
        {
            Logger.Info("Start sending claim files ...");
            ReadBillStatusIDs();
            var edtFiles = (from e in context.BillingEDTFiles
                            join d in context.ExternalDoctors on e.billingNumber equals d.OHIPPhysicianId
                            join pd in context.PracticeDoctors on d.Id equals pd.ExternalDoctorId
                            //let o = (from o in context.Offices where o.Id == e.officeId select o).FirstOrDefault()
                            let g = (from t in context.BillingGroups where t.edtGroupId == e.edtGroupId && t.officeId == e.officeId select t).FirstOrDefault()
                            where e.billStatusId == billStatus_FiledId
                            select new
                            {
                                e.id,
                                e.claimFileName,
                                e.billingNumber,
                                e.officeId,
                                e.edtGroupId,
                                //office = o,
                                edtGroup = g,
                                doctor = d,
                                MCEDTMailbox = pd.mc_un,
                                MCEDTPassword = pd.mc_pwd,
                                practiceDoctorId = pd.Id
                            }).ToList();

            var practiceDoctorIds = (from a in context.Billing_ExternalDoctorClaimSettings
                                     join pd in context.PracticeDoctors on a.ExternalDoctorId equals pd.ExternalDoctorId
                                     join d in context.ExternalDoctors on pd.ExternalDoctorId equals d.Id
                                     where a.isUploadEnabled && pd.IsActive && d.active
                                     select pd.Id).ToList();

            var edtGroupIds = (from a in context.BillingGroups
                             where a.isUploadEnabled
                             select a.edtGroupId).ToList();

            //edtFiles = edtFiles.Where((a => a.edtGroupId == PERSONAL_GROUPID && practiceDoctorIds.Contains(a.practiceDoctorId)) || (a => a.edtGroupId != PERSONAL_GROUPID && edtGroupIds.Contains(a.edtGroupId)));
            var edtDoctorFiles = edtFiles.Where(a => a.edtGroupId == PERSONAL_GROUPID && practiceDoctorIds.Contains(a.practiceDoctorId)).ToList();
            var edtEdtgroupFiles = edtFiles.Where(a => a.edtGroupId != PERSONAL_GROUPID && edtGroupIds.Contains(a.edtGroupId)).ToList();
            edtFiles = (edtDoctorFiles.Union(edtEdtgroupFiles)).OrderBy(a => a.edtGroupId).ThenBy(b => b.officeId).ThenBy(c => c.billingNumber).ToList();

            string fileName;
            Logger.InfoFormat("Total file(s) to send: {0}", edtFiles.Count());
            bool allFilesSent = true;
            foreach (var edtFile in edtFiles)
            {
                if (string.IsNullOrEmpty(edtFile.claimFileName))
                {
                    Logger.ErrorFormat("File name is empty");
                    continue;
                }

                fileName = Path.GetFileName(edtFile.claimFileName);
                if (!File.Exists(edtFile.claimFileName))
                {
                    Logger.ErrorFormat($"File does not exist: {fileName}, id: {edtFile.id}");
                    continue;
                }

                edtGroupId = edtFile.edtGroupId;
                edtFileOwner = string.Empty;
                MCEDTMailbox = string.Empty;
                MCEDTPassword = string.Empty;
                MOHId = string.Empty;
                MCEDTId = string.Empty;
                string claimFileArchiveFolder = ConfigurationManager.AppSettings["ClaimFileArchiveFolder"];

                if (edtFile.edtGroupId == PERSONAL_GROUPID)
                {
                    MCEDTMailbox = edtFile.MCEDTMailbox;
                    MCEDTPassword = edtFile.MCEDTPassword;
                    if (edtFile.doctor != null)
                    {
                        edtFileOwner = (edtFile.doctor.firstName ?? string.Empty) + " " + (edtFile.doctor.lastName ?? string.Empty) + string.Format(" (practice doctor ID: {0})", edtFile.practiceDoctorId);
                        MOHId = edtFile.doctor.OHIPPhysicianId;
                        MCEDTId = edtFile.doctor.MCEDTId;
                    }
                    claimFileArchiveFolder = Path.Combine(claimFileArchiveFolder, edtFile.billingNumber);
                }
                else
                {
                    MOHId = edtFile.edtGroupId;
                    if (edtFile.edtGroup != null)
                    {
                        edtFileOwner = string.Format("Billing group ({0})", edtFile.edtGroupId);
                        MCEDTMailbox = edtFile.edtGroup.MCEDTMailbox;
                        MCEDTPassword = edtFile.edtGroup.MCEDTPassword;
                        //MCEDTId = edtFile.office.MCEDTId;
                    }
                    claimFileArchiveFolder = Path.Combine(claimFileArchiveFolder, FOLDER_GROUP);
                    claimFileArchiveFolder = Path.Combine(claimFileArchiveFolder, edtGroupId);
                }

                if (!skipSending && (string.IsNullOrEmpty(MCEDTMailbox) || string.IsNullOrEmpty(MCEDTPassword)))
                {
                    if (string.IsNullOrEmpty(MCEDTMailbox))
                        Logger.ErrorFormat("Missing MCEDTMailbox for {0}", edtFileOwner);
                    if (string.IsNullOrEmpty(MCEDTPassword))
                        Logger.ErrorFormat("Missing MCEDTPassword for {0}", edtFileOwner);
                    Logger.ErrorFormat("Cannot send claim file: {0}", fileName);
                    continue;
                }

                if (string.IsNullOrEmpty(MCEDTId))
                    MCEDTId = DateTime.Now.ToString("mmss");

                claimFileArchiveFolder = Path.Combine(claimFileArchiveFolder.Trim(), DateTime.Now.ToString(DATEFORMAT3));
                if (!Directory.Exists(claimFileArchiveFolder))
                {
                    Logger.InfoFormat("Create new archive folder: {0}", claimFileArchiveFolder);
                    Directory.CreateDirectory(claimFileArchiveFolder);
                }

                Logger.InfoFormat("Start sending claim file: {0}", fileName);
                bool sentSucc = skipSending;
                if (!skipSending)
                {
                    string parameters = string.Format(PARAMETERS_UPLOADFILE, edtFile.claimFileName, MCEDTMailbox, MCEDTPassword, MOHId.PadLeft(5, '0'), MCEDTId);
                    Process process = new Process();
                    process.StartInfo.FileName = EBSCLIENT;
                    process.StartInfo.Arguments = parameters;
                    process.Start();
                    process.WaitForExit();
                    //Logger.InfoFormat("parameters: {0}", parameters);
                    Logger.InfoFormat("{0} exit code (error counter): {1}", EBSCLIENT, process.ExitCode);
                    if (process.ExitCode == 0)  //no error
                        sentSucc = true;
                }

                if (sentSucc)  //no error
                {
                    string newFileName = fileName;
                    string firstLine = File.ReadLines(edtFile.claimFileName).First();
                    if (!string.IsNullOrEmpty(firstLine))
                        newFileName = newFileName + "-" + firstLine.Substring(7, 12);

                    string archivedFileName = MoveFileReturnName(edtFile.claimFileName, claimFileArchiveFolder, newFileName);
                    if (string.IsNullOrEmpty(archivedFileName))
                        Logger.ErrorFormat("Cannot move claim file to archive folder");
                    else
                        Logger.InfoFormat("Moved claim file to archive folder");

                    var billingEdtFile = context.BillingEDTFiles.Where(a => a.id == edtFile.id).FirstOrDefault();
                    if (billingEdtFile != null)
                    {
                        billingEdtFile.billStatusId = billStatus_SentId;
                        billingEdtFile.DateSent = DateTime.Now;
                        billingEdtFile.DateSubmitted = DateTime.Now;
                        if (!string.IsNullOrEmpty(archivedFileName))
                            billingEdtFile.claimFileName = archivedFileName;

                        if (!string.IsNullOrWhiteSpace(archivedFileName))
                        {
                            Billing_File billingFile = new Billing_File();
                            billingFile.billingNumber = billingEdtFile.edtGroupId == PERSONAL_GROUPID ? billingEdtFile.billingNumber : string.Empty;
                            billingFile.edtGroupId = edtGroupId;
                            billingFile.fileName = archivedFileName;
                            billingFile.fileType = BillingFileType.Claim;
                            billingFile.officeId = billingEdtFile.edtGroupId == PERSONAL_GROUPID ? 0 : billingEdtFile.officeId;
                            billingFile.practiceDoctorId = billingEdtFile.edtGroupId == PERSONAL_GROUPID ? billingEdtFile.practiceDoctorId : 0;
                            billingFile.UniqueId = billingEdtFile.edtBatchId;
                            billingFile.DateEntered = DateTime.Now;

                            context.Billing_Files.Add(billingFile);
                        }

                        context.SaveChanges();

                        UpdateBillStatus(billingEdtFile.id, billStatus_SentId);
                    }

                    Logger.InfoFormat("Sending claim file: {0} --- Done", fileName);
                }
                else
                {
                    allFilesSent = false;
                    Logger.InfoFormat("Sending claim file: {0} --- Failed", fileName);
                }
            }

            if (allFilesSent)
                Logger.Info("Sending claim files --- Done");
            else
                Logger.Info("Sending claim files --- Done with failure");
        }

        //download ohip's response files
        public void Download(bool skipDownload)
        {
            Logger.Info("Start downloading files ...");
            ReadBillStatusIDs();

            var doctors = (from a in context.Billing_ExternalDoctorClaimSettings
                           join pd in context.PracticeDoctors on a.ExternalDoctorId equals pd.ExternalDoctorId
                           join d in context.ExternalDoctors on pd.ExternalDoctorId equals d.Id
                           let edtIds = context.BillingEDTIds.Where(i => i.billingGroup == PERSONAL_GROUPID && i.billingNumber == d.OHIPPhysicianId).Select(a => a.edtId)
                           where a.isDownloadEnabled && pd.IsActive && d.active
                           select new DownloadData
                           {
                               billingNumber = d.OHIPPhysicianId,
                               officeId = 0,
                               edtGroupId = PERSONAL_GROUPID,
                               PracticeId = pd.PracticeId,
                               edtGroup = null,
                               doctor = d,
                               MCEDTMailbox = pd.mc_un,
                               MCEDTPassword = pd.mc_pwd,
                               practiceDoctorId = pd.Id,
                               edtIds = edtIds.ToList()
                           }).ToList();

            var edtGroups = (from a in context.BillingGroups
                             let o = (from t in context.Offices where t.Id == a.officeId select t).FirstOrDefault()
                             let edtIds = context.BillingEDTIds.Where(i => i.billingGroup != PERSONAL_GROUPID && i.billingGroup == a.edtGroupId).Select(a => a.edtId)
                             where a.isDownloadEnabled
                             select new DownloadData
                             {
                                 billingNumber = string.Empty,
                                 officeId = a.officeId,
                                 edtGroupId = a.edtGroupId,
                                 PracticeId = o == null ? 0 : o.PracticeId,
                                 edtGroup = a,
                                 doctor = null,
                                 MCEDTMailbox = a.MCEDTMailbox,
                                 MCEDTPassword = a.MCEDTPassword,
                                 practiceDoctorId = 0,
                                 edtIds = edtIds.ToList()
                             }).ToList();

            var edtFiles = (doctors.Union(edtGroups)).OrderBy(a => a.edtGroupId).ThenBy(b => b.officeId).ThenBy(c => c.billingNumber).ToList();

            //int downloadedOfficeId = -1;
            string downloadedEdtGroupId = string.Empty;
            string downloadedBillingNumber = string.Empty;
            List<string> ids;

            Logger.InfoFormat("Total number of office/doctor for downloading file: {0}", edtFiles.Select(a => new { a.officeId, a.edtGroupId, a.billingNumber }).Distinct().Count());
            foreach (var edtFile in edtFiles)
            {
                try
                {
                    if (edtFile.edtGroupId == PERSONAL_GROUPID)
                    {
                        if (downloadedBillingNumber == edtFile.billingNumber)
                        {
                            //Logger.InfoFormat("Skiped downloading file for officeId({0}) edtGroupId({1}) billingNumber({2})", downloadedOfficeId, downloadedEdtGroupId, downloadedBillingNumber);
                            continue;
                        }
                        downloadedBillingNumber = edtFile.billingNumber;
                    }
                    else
                    {
                        if (downloadedEdtGroupId == edtFile.edtGroupId)
                        {
                            //Logger.InfoFormat("Skiped downloading file for officeId({0}) edtGroupId({1}) billingNumber({2})", downloadedOfficeId, downloadedEdtGroupId, downloadedBillingNumber);
                            continue;
                        }
                        downloadedBillingNumber = string.Empty;
                    }

                    //downloadedOfficeId = edtFile.officeId;
                    downloadedEdtGroupId = edtFile.edtGroupId;
                    Logger.InfoFormat("Downloading file for officeId({0}) edtGroupId({1}) billingNumber({2}) practiceID({3})", edtFile.officeId, downloadedEdtGroupId, downloadedBillingNumber, edtFile.PracticeId);
                    edtFileOwner = string.Empty;
                    edtGroupId = edtFile.edtGroupId;
                    MCEDTMailbox = string.Empty;
                    MCEDTPassword = string.Empty;
                    MOHId = string.Empty;
                    MCEDTId = string.Empty;
                    ids = edtFile.edtIds;
                    string downloadFolder = ConfigurationManager.AppSettings["DownloadFolder"];
                    string archiveFolder = Path.Combine(downloadFolder, FOLDER_RA);

                    if (edtFile.edtGroupId == PERSONAL_GROUPID)
                    {
                        downloadFolder = Path.Combine(downloadFolder, FOLDER_HOSPITAL);
                        archiveFolder = Path.Combine(archiveFolder, FOLDER_HOSPITAL);
                        MCEDTMailbox = edtFile.MCEDTMailbox;
                        MCEDTPassword = edtFile.MCEDTPassword;
                        if (edtFile.doctor != null)
                        {
                            edtFileOwner = (edtFile.doctor.firstName ?? string.Empty) + " " + (edtFile.doctor.lastName ?? string.Empty) + string.Format(" (practice doctor ID: {0})", edtFile.practiceDoctorId);
                            MOHId = edtFile.doctor.OHIPPhysicianId;
                            MCEDTId = edtFile.doctor.MCEDTId;
                            string doctorName = ((edtFile.doctor.firstName ?? string.Empty) + "_" + (edtFile.doctor.lastName ?? string.Empty)).Trim().Replace(" ", "_");
                            downloadFolder = Path.Combine(downloadFolder, doctorName);
                            archiveFolder = Path.Combine(archiveFolder, doctorName);
                        }
                    }
                    else
                    {
                        downloadFolder = Path.Combine(downloadFolder, FOLDER_GROUP);
                        archiveFolder = Path.Combine(archiveFolder, FOLDER_GROUP);
                        if (edtFile.edtGroup != null)
                        {
                            edtFileOwner = string.Format("Billing group ({0})", edtFile.edtGroupId);
                            MCEDTMailbox = edtFile.edtGroup.MCEDTMailbox;
                            MCEDTPassword = edtFile.edtGroup.MCEDTPassword;
                            MOHId = edtFile.edtGroupId;
                            //MCEDTId = edtFile.office.MCEDTId;
                            downloadFolder = Path.Combine(downloadFolder, edtGroupId);
                            archiveFolder = Path.Combine(archiveFolder, edtGroupId);
                        }
                    }

                    if (!skipDownload && (string.IsNullOrEmpty(MCEDTMailbox) || string.IsNullOrEmpty(MCEDTPassword)))
                    {
                        if (string.IsNullOrEmpty(MCEDTMailbox))
                            Logger.ErrorFormat("Missing MCEDTMailbox for {0}", edtFileOwner);
                        if (string.IsNullOrEmpty(MCEDTPassword))
                            Logger.ErrorFormat("Missing MCEDTPassword for {0}", edtFileOwner);
                        Logger.ErrorFormat("Cannot download file");
                        continue;
                    }

                    if (string.IsNullOrEmpty(MCEDTId))
                        MCEDTId = DateTime.Now.ToString("mmss");

                    downloadFolder = downloadFolder.Trim();
                    archiveFolder = archiveFolder.Trim();
                    DownloadFile(edtFile.PracticeId, edtFile.officeId, edtFile.edtGroupId, MOHId, edtFile.practiceDoctorId, edtFile.billingNumber, downloadFolder, archiveFolder, ids, skipDownload);

                    archiveFolder = Path.Combine(Path.Combine(archiveFolder, DateTime.Now.ToString("yyyy")), DateTime.Now.ToString("MM"));
                    Reconcile(archiveFolder, downloadFolder);

                    context.SaveChanges();
                }
                catch (Exception ex)
                {
                    LogException("Error", ex);
                }
            }
            Logger.Info("Downloading files --- Done");
        }

        public void Confirm()
        {
            edtFileOwner = string.Empty;
            string downloadFolder = Path.Combine(ConfigurationManager.AppSettings["DownloadFolder"], "TempConfirmedFiles");
            ReadBillStatusIDs();
            DownloadFile(0, 0, PERSONAL_GROUPID, string.Empty, 0, string.Empty, downloadFolder, string.Empty, null, true);
        }

        public void CreateRADataFiles(string dateFrom)
        {
            Logger.Info("Create RA data files --- Start");

            DateTime date;
            if (!DateTime.TryParseExact(dateFrom, DATEFORMAT, CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
            {
                Logger.Info("Invalid date");
                return;
            }

            //string downloadFolder = ConfigurationManager.AppSettings["DownloadFolder"];
            //string raFolder = Path.Combine(downloadFolder, FOLDER_RA);
            //var raFiles = new DirectoryInfo(raFolder).EnumerateFiles("p*.*", SearchOption.AllDirectories).Where(a => a.CreationTime >= date).ToList();
            var raFiles = context.Billing_Files.Where(a => a.DateEntered >= date && a.fileType == BillingFileType.Reconciliation).ToList();
            Logger.InfoFormat("Total files: {0}", raFiles.Count);

            RemittancePermission permission = new RemittancePermission();
            permission.isRASoloBillingAdmin = true;
            permission.isRAGroupBillingAdmin = true;
            var bll = new Cerebrum.BLL.Bill.ReportBLL(1, "Billing service");

            foreach (var raFile in raFiles)
            {
                try
                {
                    Logger.InfoFormat("Processing file: {0}", raFile.fileName);
                    //if (raFile.fileName.ToLower().EndsWith(RA_DATA_FILE_EXTENSION))
                    //{
                    //    Logger.Info("Not RA file. Skipped");
                    //    continue;
                    //}

                    string raFileData = raFile.fileName + RA_DATA_FILE_EXTENSION;
                    if (File.Exists(raFileData))
                    {
                        Logger.Info("RA data file exists. Skipped");
                        continue;
                    }

                    permission.practiceId = -1;
                    RemittanceDetailResponse response = bll.RemittanceDetail(raFile.id, permission);
                    if (string.IsNullOrWhiteSpace(response.errorMessage))
                    {
                        File.WriteAllText(raFileData, JsonConvert.SerializeObject(response));
                        Logger.InfoFormat("Saved RA data file: {0}", raFileData);
                    }
                    else
                        Logger.ErrorFormat("Error: {0}", response.errorMessage);
                }
                catch (Exception ex)
                {
                    LogException("Exception: ", ex);
                }
            }

            Logger.Info("Create RA data files --- Done");
        }

        private void DownloadFile(int practiceId, int edtOfficeId, string edtGroupId, string edtMOHId, int practiceDoctorId, string edtBillingNumber, string downloadFolder, string archiveFolder, List<string> edtIds, bool skipDownload)
        {
            Dictionary<string, string> nameIds = new Dictionary<string, string>();

            if (!skipDownload)
            {
                Logger.InfoFormat("Downloading file for {0} to folder {1} ...", edtFileOwner, downloadFolder);
                if (!Directory.Exists(downloadFolder))
                {
                    Logger.InfoFormat("Create new download folder: {0}", downloadFolder);
                    Directory.CreateDirectory(downloadFolder);
                }

                if (File.Exists(OHIPFILE))
                    File.Delete(OHIPFILE);

                string parameters = string.Format(PARAMETERS_LISTFILE, MCEDTMailbox, MCEDTPassword, edtMOHId.PadLeft(5, '0'), MCEDTId);
                Process process = new Process();
                process.StartInfo.FileName = EBSCLIENT;
                process.StartInfo.Arguments = parameters;
                process.Start();
                process.WaitForExit();
                Logger.InfoFormat("{0} (List file) exit code (error counter): {1}", EBSCLIENT, process.ExitCode);

                if (!File.Exists(OHIPFILE))
                {
                    Logger.ErrorFormat("File {0} doesn't exist. Stop downloading file for {1}", OHIPFILE, edtFileOwner);
                    return;
                }

                int n = 0;
                string[] ids = File.ReadAllLines(OHIPFILE);
                foreach (string edtId in ids)
                {
                    n++;
                    if (string.IsNullOrEmpty(edtId))
                        continue;

                    if (edtId.All(char.IsDigit) && !edtIds.Contains(edtId))
                    {
                        Logger.InfoFormat("Downloading file (for EDT id {0}; line in file {1}: {2} ) ...", edtId, OHIPFILE, n);
                        parameters = string.Format(PARAMETERS_DOWNLOADFILE, downloadFolder, MCEDTMailbox, MCEDTPassword, edtMOHId.PadLeft(5, '0'), MCEDTId, edtId);
                        process = new Process();
                        process.StartInfo.FileName = EBSCLIENT;
                        process.StartInfo.Arguments = parameters;
                        process.Start();
                        process.WaitForExit();
                        Logger.InfoFormat("Process exit code (error counter): {0}", process.ExitCode);
                        if (process.ExitCode == 0)
                        {
                            context.BillingEDTIds.Add(new Billing_EDTId { edtId = edtId, billingGroup = edtGroupId, billingNumber = (edtGroupId == PERSONAL_GROUPID ? edtBillingNumber : string.Empty) });
                            context.SaveChanges();
                            edtIds.Add(edtId);

                            string[] files = Directory.GetFiles(downloadFolder);
                            var newFileSaved = files.Where(a => !nameIds.ContainsKey(a)).FirstOrDefault();
                            if (!string.IsNullOrWhiteSpace(newFileSaved))
                            {
                                nameIds.Add(newFileSaved, edtId);
                                Logger.InfoFormat($"File: {newFileSaved}, EDT Id: {nameIds[newFileSaved]}");
                            }
                        }
                    }
                    //else
                    //    Logger.ErrorFormat("Skip downloading file (for EDT id {0}) : this EDT file downloaded already", edtId);
                }
            }

            string fileName;
            string fileNameNew;
            string firstLine;
            string year = DateTime.Now.ToString("yyyy");
            string errorFolder = Path.Combine(Path.Combine(ConfigurationManager.AppSettings["DownloadFolder"], FOLDER_ERROR), year);
            string[] filesDownloaded = Directory.GetFiles(downloadFolder);
            Logger.InfoFormat("Total file downloaded: {0}", filesDownloaded.Count());
            foreach (string fileDownloaded in filesDownloaded)
            {
                fileName = Path.GetFileName(fileDownloaded);
                fileNameNew = fileDownloaded;
                BillingFileType fileType = BillingFileType.Other;
                Logger.InfoFormat("Processing downloaded file: {0}", fileName);
                bool fileExist = DoesFileExist(fileName);
                try
                {
                    switch (fileName.Substring(0, 1).ToUpper())
                    {
                        case "P":
                            fileType = BillingFileType.Reconciliation;
                            if (fileExist)
                            {
                                CopyFile(fileDownloaded, Path.Combine(Path.Combine(archiveFolder, year), DateTime.Now.ToString("MM")), fileName);
                                fileNameNew = MoveFileReturnName(fileDownloaded, Path.Combine(Path.Combine(downloadFolder, FOLDER_ARCHIVE), DateTime.Now.ToString(DATEFORMAT3)), fileName);
                            }
                            else
                            {
                                fileNameNew = MoveFileReturnName(fileDownloaded, Path.Combine(downloadFolder, FOLDER_RECONCILIATION), fileName);
                            }
                            break;
                        case "X":
                            fileType = BillingFileType.Other;
                            fileNameNew = MoveFileReturnName(fileDownloaded, Path.Combine(downloadFolder, FOLDER_X), fileName);
                            break;
                        case "E":
                            fileType = BillingFileType.Error;
                            CopyFile(fileDownloaded, errorFolder, fileName);
                            if (fileExist)
                            {
                                fileNameNew = MoveFileReturnName(fileDownloaded, Path.Combine(Path.Combine(downloadFolder, FOLDER_ERROR), year), fileName);
                            }
                            else
                            {
                                firstLine = File.ReadLines(fileDownloaded).First();
                                fileNameNew = MoveFileReturnName(fileDownloaded, Path.Combine(Path.Combine(downloadFolder, FOLDER_ERROR), year), fileName);
                                if (!string.IsNullOrEmpty(firstLine))
                                {
                                    string billNumber = firstLine.Substring(27, 6);
                                    if (!string.IsNullOrEmpty(billNumber))
                                    {
                                        int externalDoctorId = 0;
                                        var externalDoctor = context.ExternalDoctors.Where(a => a.OHIPPhysicianId == billNumber).FirstOrDefault();
                                        if (externalDoctor != null)
                                            externalDoctorId = externalDoctor.Id;

                                        DateTime receivedDate;
                                        if (!DateTime.TryParseExact(firstLine.Substring(38, 8), DATEFORMAT, CultureInfo.InvariantCulture, DateTimeStyles.None, out receivedDate))
                                            receivedDate = DateTime.Now;

                                        Billing_EDTError edtError = new Billing_EDTError();
                                        edtError.externalDoctorId = externalDoctorId;
                                        edtError.status = EDTERRORSTATUS_NOTFIXED;
                                        edtError.officeId = edtOfficeId;
                                        edtError.edtGroupId = edtGroupId;
                                        edtError.fileName = fileNameNew;
                                        //edtError.DateDownloaded = receivedDate;
                                        edtError.DateDownloaded = DateTime.Now;
                                        context.BillingEDTErrors.Add(edtError);
                                        context.SaveChanges();

                                        ProcessErrorFile(practiceId, edtError.fileName);
                                    }
                                }
                            }
                            break;
                        case "B":
                            fileType = BillingFileType.Confirmation;
                            if (fileExist)
                            {
                                fileNameNew = MoveFileReturnName(fileDownloaded, Path.Combine(Path.Combine(downloadFolder, FOLDER_CONFIRMATION), year), fileName);
                            }
                            else
                            {
                                firstLine = File.ReadLines(fileDownloaded).First();
                                fileNameNew = MoveFileReturnName(fileDownloaded, Path.Combine(downloadFolder, FOLDER_CONFIRMATION), fileName);
                                if (!string.IsNullOrEmpty(firstLine))
                                {
                                    int error = 0;
                                    string edtBatchId = string.Empty;
                                    DateTime receivedDate;
                                    if (firstLine.IndexOf(BATCHTOTALS) == -1)
                                        error = 1;
                                    edtBatchId = firstLine.Substring(17, 12);
                                    if (!DateTime.TryParseExact(firstLine.Substring(73, 8), DATEFORMAT, CultureInfo.InvariantCulture, DateTimeStyles.None, out receivedDate))
                                        receivedDate = DateTime.Now;

                                    //var billingFile = context.BillingEDTFiles.Where(a => a.edtBatchId == edtBatchId && a.billStatusId == billStatus_SentId && a.officeId == edtOfficeId && a.edtGroupId == edtGroupId && a.billingNumber == edtBillingNumber).FirstOrDefault();
                                    Billing_EDTFile billingFile = null;
                                    if (edtGroupId == PERSONAL_GROUPID)
                                    {
                                        //billingFile = context.BillingEDTFiles.Where(a => a.edtBatchId == edtBatchId && a.billStatusId == billStatus_SentId && a.officeId == edtOfficeId && a.edtGroupId == edtGroupId && a.billingNumber == edtBillingNumber).FirstOrDefault();
                                        string billingNumberSearch = edtBillingNumber;
                                        if (string.IsNullOrWhiteSpace(billingNumberSearch))
                                            billingNumberSearch = firstLine.Substring(56, 6);
                                        billingFile = context.BillingEDTFiles.Where(a => a.edtBatchId == edtBatchId && a.billStatusId == billStatus_SentId && a.edtGroupId == edtGroupId && a.billingNumber == billingNumberSearch).FirstOrDefault();
                                    }
                                    else
                                    {
                                        billingFile = context.BillingEDTFiles.Where(a => a.edtBatchId == edtBatchId && a.billStatusId == billStatus_SentId && a.edtGroupId == edtGroupId).FirstOrDefault();
                                    }
                                    if (billingFile == null)
                                        Logger.ErrorFormat($"Cannot find edt file matching with downloaded file {fileNameNew} in database");
                                    else
                                    {
                                        Logger.InfoFormat(string.Format($"confirmed file matched with edt file (id: {billingFile.id})"));
                                        billingFile.Error = error;
                                        billingFile.confirmedFileName = fileName;
                                        billingFile.billStatusId = billStatus_ConfirmedId;
                                        billingFile.DateDownloaded = DateTime.Now;
                                        billingFile.DateReceived = receivedDate;
                                        context.SaveChanges();

                                        UpdateBillStatus(billingFile.id, billStatus_ConfirmedId);
                                    }
                                }
                            }
                            break;
                        default:
                            fileNameNew = MoveFileReturnName(fileDownloaded, Path.Combine(downloadFolder, FOLDER_X), fileName);
                            fileType = BillingFileType.Other;
                            break;
                    }


                    if (!fileExist && !string.IsNullOrWhiteSpace(fileNameNew))
                    {
                        string uniqueId = string.Empty; 
                        if (nameIds.ContainsKey(fileDownloaded))
                            uniqueId = nameIds[fileDownloaded];

                        Billing_File billingFile = new Billing_File();
                        billingFile.billingNumber = edtBillingNumber;
                        billingFile.edtGroupId = edtGroupId;
                        billingFile.fileName = fileNameNew;
                        billingFile.fileType = fileType;
                        billingFile.officeId = edtOfficeId;
                        billingFile.practiceDoctorId = edtGroupId == PERSONAL_GROUPID ? practiceDoctorId : 0;
                        billingFile.UniqueId = uniqueId;
                        billingFile.DateEntered = DateTime.Now;

                        context.Billing_Files.Add(billingFile);
                        context.SaveChanges();
                    }

                    Logger.InfoFormat("Processing downloaded file: {0}   ------ End", fileName);
                }
                catch (Exception ex)
                {
                    LogException(string.Format("Processing downloaded file: {0}  ------ Error", fileName), ex);
                }
            }
        }

        private void Reconcile(string archiveFolder, string downloadFolder)
        {
            string reconciliationFolder = Path.Combine(downloadFolder, FOLDER_RECONCILIATION);
            Logger.InfoFormat("Reconciling Files in folder: {0}", reconciliationFolder);
            if (!Directory.Exists(reconciliationFolder))
                return;

            string[] reconciliationFiles = Directory.GetFiles(reconciliationFolder);
            if (reconciliationFiles.Count() == 0)
                return;

            Logger.InfoFormat("Files for Reconciling: {0}", reconciliationFiles.Count());
            foreach (string reconciliationFile in reconciliationFiles)
            {
                string fileName = Path.GetFileName(reconciliationFile);
                try
                {
                    if (!fileName.StartsWith("P", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Logger.ErrorFormat("Skip file ({0}): file name doesn't start with 'P'", fileName);
                        continue;
                    }
                    ReconcileFile(archiveFolder, downloadFolder, reconciliationFile);
                }
                catch (Exception ex)
                {
                    LogException(string.Format("Reconcile file ({0}) Error", fileName), ex);
                }
                finally
                {
                    string subFolder = DateTime.Now.ToString("MM-dd-yyyy");
                    CopyFile(reconciliationFile, Path.Combine(Path.Combine(downloadFolder, FOLDER_ARCHIVE), subFolder), fileName);
                    string newRAFileName = MoveFileReturnName(reconciliationFile, archiveFolder, fileName);

                    if (!string.IsNullOrWhiteSpace(newRAFileName))
                    {
                        Billing_File billingFile = context.Billing_Files.Where(a => a.fileName == reconciliationFile && a.fileType == BillingFileType.Reconciliation).FirstOrDefault();
                        if (billingFile != null)
                        {
                            billingFile.fileName = newRAFileName;
                            context.SaveChanges();
                        }
                    }
                }
            }
        }

        private void ReconcileFile(string archiveFolder, string downloadFolder, string reconciliationFile)
        {
            string fileName = Path.GetFileName(reconciliationFile);
            Logger.InfoFormat("Reconcile file ({0}) ...", fileName);

            int acceptedCounter = 0;
            int adjustedCounter = 0;
            int errorCounter = 0;
            int notFoundCounter = 0;
            int hr1Counter = 0;
            int hr4Counter = 0;
            int hr5Counter = 0;
            string ohipNumber = string.Empty;
            string ohipVersion;
            string program;
            string billingNumber = string.Empty;
            string MOHId;
            string transactionType; //1 (original claim) or 2 (adjustment to original claim)
            DateTime serviceDate;
            string serviceCode;
            string amountSubmitted;
            int amountPaid;
            //int amountPaidSign;
            string explanatoryCode;
            string edtGroupId;
            DateTime ohipPayDate = DateTime.MaxValue;

            List<string> notFounds = new List<string>();
            List<string> countLogs = new List<string>();
            List<string> errorLogs = new List<string>();
            List<string> billingNumbers = new List<string>();
            List<string> ohipNumbers = new List<string>();
            List<DateTime> serviceDates = new List<DateTime>();
            List<int> appointmentIds = new List<int>();
            List<int> admissionIds = new List<int>();
            List<RAPayment> raPaymentAll = new List<RAPayment>();
            List<RAPayment> raPaymentNotFounds = new List<RAPayment>();

            string record;
            string[] records = File.ReadAllLines(reconciliationFile);
            for (int n = 0; n < records.Count(); n++)
            {
                //record = records[n];
                record = records[n] + "".PadLeft(100, ' ');
                switch (record.Trim().Substring(0, 3).ToUpper())
                {
                    case "HR4":
                        billingNumber = record.Substring(15, 6);
                        ohipNumber = record.Substring(52, 12);
                        billingNumbers.Add(billingNumber);
                        ohipNumbers.Add(ohipNumber);
                        break;
                    case "HR5":
                        if (!DateTime.TryParseExact(record.Substring(15, 8), DATEFORMAT, CultureInfo.InvariantCulture, DateTimeStyles.None, out serviceDate))
                            serviceDate = DateTime.MaxValue;
                        serviceDates.Add(serviceDate);
                        break;
                }
            }

            Uri cerebrum3Url = new Uri(new Uri(ConfigurationManager.AppSettings["Cerebrum3Url"]), "Schedule/daysheet");
            context.Database.CommandTimeout = 120;
            var billDetails = context.BillDetails.Where(a => billingNumbers.Contains(a.billingNumber) && ohipNumbers.Contains(a.healthCardNumber)
                                                        && serviceDates.Contains((DateTime)DbFunctions.TruncateTime(a.serviceDate))).ToList();

            for (int n = 0; n < records.Count(); n++)
            {
                //record = records[n];
                record = records[n] + "".PadLeft(100, ' ');
                switch (record.Trim().Substring(0, 3).ToUpper())
                {
                    case "HR1":
                        hr1Counter++;
                        if (!DateTime.TryParseExact(record.Substring(21, 8), DATEFORMAT, CultureInfo.InvariantCulture, DateTimeStyles.None, out ohipPayDate))
                            ohipPayDate = DateTime.MaxValue;
                        break;
                    case "HR4":
                        hr4Counter++;
                        billingNumber = record.Substring(15, 6);
                        ohipNumber = record.Substring(52, 12).Trim();
                        ohipVersion = record.Substring(64, 2).Trim();
                        program = record.Substring(66, 3);
                        edtGroupId = record.Substring(73, 4);
                        break;
                    case "HR5":
                        hr5Counter++;
                        MOHId = record.Substring(3, 11);
                        transactionType = record.Substring(14, 1);
                        if (!DateTime.TryParseExact(record.Substring(15, 8), DATEFORMAT, CultureInfo.InvariantCulture, DateTimeStyles.None, out serviceDate))
                            serviceDate = DateTime.MaxValue;
                        serviceCode = record.Substring(25, 5);
                        amountSubmitted = record.Substring(31, 6);
                        amountPaid = 0;
                        if (!string.IsNullOrEmpty(record.Substring(37, 6)))
                        {
                            if (!int.TryParse(record.Substring(37, 6), out amountPaid))
                                amountPaid = 0;
                        }
                        if (record.Substring(43, 1) == "-")
                            amountPaid = -1 * amountPaid;

                        explanatoryCode = record.Substring(44, 2).Trim();
                        RAPayment raPayment = new RAPayment
                        {
                            billingNumber = billingNumber,
                            ohipNumber = ohipNumber,
                            serviceDate = serviceDate,
                            serviceCode = serviceCode,
                            amountPaid = amountPaid,
                            explanatoryCode = explanatoryCode,
                            ohipPayDate = ohipPayDate,
                            MOHId = MOHId
                        };

                        raPaymentAll.Add(raPayment);

                        var billDetail = billDetails.Where(a => a.billingNumber != null && a.billingNumber.Trim() == billingNumber && a.healthCardNumber != null && a.healthCardNumber.Trim() == ohipNumber
                                                        && a.serviceCode != null && a.serviceCode.Trim() == serviceCode
                                                        && a.serviceDate.Date == serviceDate.Date).OrderByDescending(a => a.id).FirstOrDefault();
                        if (billDetail == null)
                        {
                            raPaymentNotFounds.Add(raPayment);
                            notFoundCounter++;
                            notFounds.Add(string.Format("line={0}, billingNumber={1}, OHIPNumber={2}, Date={3}", n, billingNumber, ohipNumber, serviceDate.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)));
                            notFounds.Add(record);
                            Logger.Info($"cannot find ra payment (billingNumber: {billingNumber}, OHIPNumber: {ohipNumber}, serviceDate: {serviceDate.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)}, serviceCode: {serviceCode})");
                        }
                        else
                        {
                            //if (explanatoryCode != "35")
                            {
                                billDetail.ohipExplanatoryCode = explanatoryCode;
                                if (ohipPayDate != DateTime.MaxValue)
                                    billDetail.ohipPayDate = ohipPayDate;
                                billDetail.reconciledDate = DateTime.Now;
                                billDetail.MOHId = MOHId;
                            }

                            //if ((string.IsNullOrEmpty(explanatoryCode) || explanatoryCode == "55" || explanatoryCode == "57") && billDetail.fee == int.Parse(amountSubmitted) && amountPaidSign == 1)
                            //{
                            //    if (int.Parse(amountSubmitted) <= int.Parse(amountPaid))
                            //    {
                            //        billDetail.billStatusId = billStatus_PaidId;
                            //        acceptedCounter++;
                            //    }
                            //    else
                            //    {
                            //        billDetail.billStatusId = billStatus_ReadjustedId;
                            //        adjustedCounter++;
                            //    }
                            //}
                            //else
                            //{
                            //    if (explanatoryCode != "35")
                            //    {
                            //        billDetail.billStatusId = billStatus_RefusedId;
                            //    }

                            //    errorLogs.Add(string.Format("line={0}, billingNumber={1}, OHIPNumber={2}, Date={3}", n, billingNumber, ohipNumber, serviceDate.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)));
                            //    errorLogs.Add(string.Format("Patient: <a href='{0}?OfficeId={1}&Date={2}'>Show Daysheet</a>", cerebrum3Url.ToString(), billDetail.officeId, billDetail.date.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)));
                            //    errorLogs.Add(record);
                            //    errorLogs.Add(string.Format("bill item id={0}, appointment id={1}", billDetail.id, billDetail.appointmentId));
                            //    if (!string.IsNullOrEmpty(explanatoryCode))
                            //        errorLogs.Add(string.Format("Error code={0}", explanatoryCode));
                            //    if (amountPaidSign == -1)
                            //        errorLogs.Add("Payment sign is negative");
                            //    if (int.Parse(amountSubmitted) != int.Parse(amountPaid) || billDetail.fee != int.Parse(amountPaid))
                            //        errorLogs.Add("Paid fee is not equal to submitted");

                            //    errorCounter++;
                            //}

                            billDetail.ohipAmountPaid = (billDetail.ohipAmountPaid ?? 0) + raPayment.amountPaid;

                            if (billDetail.fee <= billDetail.ohipAmountPaid)
                            {
                                billDetail.billStatusId = billStatus_PaidId;
                                acceptedCounter++;
                            }
                            else if (billDetail.ohipAmountPaid == 0)
                            {
                                if (explanatoryCode == "35")
                                {
                                    if (billDetail.billStatusId == billStatus_SentId || billDetail.billStatusId == billStatus_ConfirmedId)
                                        billDetail.billStatusId = billStatus_RefusedId;
                                }
                                else
                                {
                                    billDetail.billStatusId = billStatus_RefusedId;
                                }

                                errorLogs.Add(string.Format("line={0}, billingNumber={1}, OHIPNumber={2}, Date={3}", n, billingNumber, ohipNumber, serviceDate.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)));
                                errorLogs.Add(string.Format("Patient: <a href='{0}?OfficeId={1}&Date={2}'>Show Daysheet</a>", cerebrum3Url.ToString(), billDetail.officeId, billDetail.date.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)));
                                errorLogs.Add(record);
                                errorLogs.Add(string.Format("bill item id={0}, appointment id={1}", billDetail.id, billDetail.appointmentId));
                                if (!string.IsNullOrEmpty(explanatoryCode))
                                    errorLogs.Add(string.Format("Error code={0}", explanatoryCode));
                                //if (amountPaid < 0)
                                //    errorLogs.Add("Payment sign is negative");
                                //if (int.Parse(amountSubmitted) != int.Parse(amountPaid) || billDetail.fee != int.Parse(amountPaid))
                                //    errorLogs.Add("Paid fee is not equal to submitted");

                                errorCounter++;
                            }
                            else
                            {
                                billDetail.billStatusId = billStatus_ReadjustedId;
                                adjustedCounter++;
                            }

                            if (billDetail.appointmentId > 0)
                            {
                                if (!appointmentIds.Contains(billDetail.appointmentId))
                                    appointmentIds.Add(billDetail.appointmentId);
                            }
                            else if (billDetail.hdAdmissionId > 0)
                            {
                                if (!admissionIds.Contains(billDetail.hdAdmissionId))
                                    admissionIds.Add(billDetail.hdAdmissionId);
                            }
                        }

                        break;
                }
            }

            //ticket 1293
            for (int n = 0; n < raPaymentNotFounds.Count(); n++)
            {
                RAPayment raPaymentNotFound = raPaymentNotFounds[n];
                if (string.IsNullOrWhiteSpace(raPaymentNotFound.serviceCode) || !raPaymentNotFound.serviceCode.ToUpper().StartsWith("A"))
                {
                    continue;
                }

                List<RAPayment> raPaymentNotFound2 = raPaymentNotFounds.Where(a => a.billingNumber == raPaymentNotFound.billingNumber
                                                                                && a.ohipNumber == raPaymentNotFound.ohipNumber
                                                                                && a.serviceDate.Date == raPaymentNotFound.serviceDate.Date
                                                                                && (!string.IsNullOrWhiteSpace(a.serviceCode) && a.serviceCode.ToUpper().StartsWith("A"))).ToList();
                if (raPaymentNotFound2.Count > 1)
                {
                    continue;
                }

                List<BillDetail> billDetailReadjust = billDetails.Where(a => a.billingNumber != null && a.billingNumber.Trim() == raPaymentNotFound.billingNumber && a.healthCardNumber != null && a.healthCardNumber.Trim() == raPaymentNotFound.ohipNumber
                                                                    && a.serviceCode != null && a.serviceCode.Trim().ToUpper().StartsWith("A")
                                                                    && a.serviceDate.Date == raPaymentNotFound.serviceDate.Date).ToList();

                if (billDetailReadjust.Count != 1)
                {
                    continue;
                }

                BillDetail billDetail = billDetailReadjust[0];
                billDetail.ohipExplanatoryCode = raPaymentNotFound.explanatoryCode;
                if (raPaymentNotFound.ohipPayDate != DateTime.MaxValue)
                    billDetail.ohipPayDate = raPaymentNotFound.ohipPayDate;
                billDetail.reconciledDate = DateTime.Now;
                billDetail.MOHId = raPaymentNotFound.MOHId;

                billDetail.ohipAmountPaid = raPaymentNotFound.amountPaid;
                billDetail.billStatusId = billStatus_ReadjustedId;
                billDetail.errorCode = $"{billDetail.serviceCode} -> {raPaymentNotFound.serviceCode} ";
                adjustedCounter++;

                if (billDetail.appointmentId > 0)
                {
                    if (!appointmentIds.Contains(billDetail.appointmentId))
                        appointmentIds.Add(billDetail.appointmentId);
                }
                else if (billDetail.hdAdmissionId > 0)
                {
                    if (!admissionIds.Contains(billDetail.hdAdmissionId))
                        admissionIds.Add(billDetail.hdAdmissionId);
                }

                Logger.Info($"Readjust ra payment (billingNumber: {raPaymentNotFound.billingNumber}, OHIPNumber: {raPaymentNotFound.ohipNumber}, serviceDate: {raPaymentNotFound.serviceDate.ToString(DATEFORMAT2, CultureInfo.InvariantCulture)}, serviceCode: {raPaymentNotFound.serviceCode}) to {billDetail.serviceCode} (appointment id: {billDetail.appointmentId})");
            }

            context.SaveChanges();

            UpdateBillStatus(appointmentIds, admissionIds);

            countLogs.Add(string.Format("HR1(Office/Doctor) records={0}", hr1Counter));
            countLogs.Add(string.Format("HR4(Patient) records={0}", hr4Counter));
            countLogs.Add(string.Format("HR5(Payment) records={0}", hr5Counter));
            countLogs.Add(string.Format("Payment Records that have not been found={0}", notFoundCounter));
            countLogs.Add(string.Format("Payment Records that have errors={0}", errorCounter));
            countLogs.Add(string.Format("Payment Records that have been accepted={0}", acceptedCounter));
            countLogs.Add(string.Format("Payment Records that have been adjusted={0}", adjustedCounter));

            string subFolder = DateTime.Now.ToString("MM-dd-yyyy");
            WriteLogFile(countLogs, downloadFolder, FOLDER_LOG, subFolder, fileName, "Counts");
            WriteLogFile(errorLogs, downloadFolder, FOLDER_LOG, subFolder, fileName, "Errors");
            string newFileName = WriteLogFile(notFounds, downloadFolder, FOLDER_LOG, subFolder, fileName, "Notfound");
            if (!string.IsNullOrEmpty(newFileName))
                CopyFile(newFileName, Path.Combine(archiveFolder, FOLDER_NOTFOUND), Path.GetFileName(newFileName));
        }

        private void ProcessErrorFile(int practiceId, string errorFileName)
        {
            ClaimBLL bll = new ClaimBLL();
            List<DoctorEdtErrorData> doctorEdtErrorDatas = bll.GetDoctorEdtErrorData(practiceId, errorFileName);
            List<int> appointmentIds = new List<int>();
            List<int> admissionIds = new List<int>();

            foreach (var doctorEdtErrorData in doctorEdtErrorDatas)
            {
                string billingNumber = doctorEdtErrorData.billingNumber;
                foreach (var patientEdtErrorData in doctorEdtErrorData.patientEdtErrorData)
                {
                    string healthCardNumber = patientEdtErrorData.healthCardNumber;
                    foreach (var paymentEdtErrorData in patientEdtErrorData.paymentEdtErrorData)
                    {
                        if (string.IsNullOrEmpty(paymentEdtErrorData.errorCode))
                            continue;

                        string serviceCode = paymentEdtErrorData.serviceCode;
                        DateTime serviceDate = paymentEdtErrorData.serviceDate;
                        var billDetail = context.BillDetails.Where(a => a.billingNumber == billingNumber && a.healthCardNumber.Trim() == healthCardNumber
                                && a.serviceCode == serviceCode && SqlFunctions.DateDiff("DAY", a.serviceDate, serviceDate) == 0).OrderByDescending(a => a.id).FirstOrDefault();
                        if (billDetail == null)
                            continue;

                        billDetail.errorCode = paymentEdtErrorData.errorCode;
                        billDetail.billStatusId = billStatus_RefusedId;

                        if (billDetail.appointmentId > 0)
                        {
                            if (!appointmentIds.Contains(billDetail.appointmentId))
                                appointmentIds.Add(billDetail.appointmentId);
                        }
                        if (billDetail.hdAdmissionId > 0)
                        {
                            if (!admissionIds.Contains(billDetail.hdAdmissionId))
                                admissionIds.Add(billDetail.hdAdmissionId);
                        }
                    }
                }
            }
            context.SaveChanges();

            if (appointmentIds.Count > 0 || admissionIds.Count > 0)
                UpdateBillStatus(appointmentIds, admissionIds);
        }

        private void ReadBillStatusIDs()
        {
            var billStatuses = context.BillStatuses.AsNoTracking().ToList();

            billStatus_BilledId = GetBillStatusId(BILLSTATUS_BILLED, billStatuses);
            billStatus_FiledId = GetBillStatusId(BILLSTATUS_FILED, billStatuses);
            billStatus_SentId = GetBillStatusId(BILLSTATUS_SENT, billStatuses);
            billStatus_ConfirmedId = GetBillStatusId(BILLSTATUS_CONFIRMED, billStatuses);
            billStatus_PaidId = GetBillStatusId(BILLSTATUS_PAID, billStatuses);
            billStatus_RefusedId = GetBillStatusId(BILLSTATUS_REFUSED, billStatuses);
            billStatus_ReadjustedId = GetBillStatusId(BILLSTATUS_READJUSTED, billStatuses);
            billStatus_StaleId = GetBillStatusId(BILLSTATUS_STALE, billStatuses);
            billStatus_MixId = GetBillStatusId(BILLSTATUS_MIXED, billStatuses);
        }

        private string WriteLogFile(List<string> logs, string folder1, string folder2, string folder3, string fileName, string prefix)
        {
            if (logs.Count == 0)
                return string.Empty;

            string folder = Path.Combine(Path.Combine(folder1, folder2), folder3);
            string newFileName = Path.Combine(folder, prefix + "_" + fileName.Replace(".", "_") + ".htm");

            if (!Directory.Exists(folder))
            {
                Logger.InfoFormat("Create new folder: {0}", folder);
                Directory.CreateDirectory(folder);
            }

            File.WriteAllText(newFileName, string.Join("<br />", logs));
            return newFileName;
        }

        private string MoveFileReturnName(string oldFileName, string folderName, string newFileName)
        {
            if (!Directory.Exists(folderName))
            {
                Logger.InfoFormat("Create new folder: {0}", folderName);
                Directory.CreateDirectory(folderName);
            }

            string fileExtension = Path.GetExtension(newFileName).Trim();
            string fileExtension2 = fileExtension.ToLower();
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(newFileName).Trim();
            string fileName = Path.Combine(folderName, newFileName);
            for (int n = 2; n < 500; n++)
            {
                if (!File.Exists(fileName))
                {
                    File.Move(oldFileName, fileName);
                    return fileName;
                }

                if (fileExtension2 == ".txt" || fileExtension2 == ".htm" || fileExtension2 == ".html" || fileExtension2 == ".pdf")
                    fileName = Path.Combine(folderName, fileNameWithoutExtension + "_" + n.ToString() + fileExtension);
                else
                    fileName = Path.Combine(folderName, newFileName + "_" + n.ToString());
            }

            return string.Empty;
        }

        private bool MoveFile(string oldFileName, string folderName, string newFileName)
        {
            string fileName = MoveFileReturnName(oldFileName, folderName, newFileName);
            if (string.IsNullOrEmpty(fileName))
                return false;

            return true;
        }

        private bool CopyFile(string oldFileName, string folderName, string newFileName)
        {
            if (!Directory.Exists(folderName))
            {
                Logger.InfoFormat("Create new folder: {0}", folderName);
                Directory.CreateDirectory(folderName);
            }

            string fileExtension = Path.GetExtension(newFileName).Trim();
            string fileExtension2 = fileExtension.ToLower();
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(newFileName).Trim();
            string fileName = Path.Combine(folderName, newFileName);
            for (int n = 2; n < 500; n++)
            {
                if (!File.Exists(fileName))
                {
                    File.Copy(oldFileName, fileName);
                    return true;
                }

                if (fileExtension2 == ".txt" || fileExtension2 == ".htm" || fileExtension2 == ".html" || fileExtension2 == ".pdf")
                    fileName = Path.Combine(folderName, fileNameWithoutExtension + "_" + n.ToString() + fileExtension);
                else
                    fileName = Path.Combine(folderName, newFileName + "_" + n.ToString());
            }

            return false;
        }

        private int GetBillStatusId(string billStatusName, List<BillStatus> billStatuses)
        {
            var billStatus = billStatuses.Where(a => a.name.Trim().ToLower() == billStatusName).OrderBy(b => b.id).FirstOrDefault();
            if (billStatus == null)
                return 0;

            return billStatus.id;
        }

        private int? GetBillStatusId(int? currentBillStatusId, int? nextBillStatusId)
        {
            if (currentBillStatusId == -1)
                return nextBillStatusId;

            if (currentBillStatusId == null && nextBillStatusId == null)
                return null;

            if (currentBillStatusId == null || nextBillStatusId == null)
                return billStatus_MixId;

            if (currentBillStatusId == nextBillStatusId)
                return currentBillStatusId;

            return billStatus_MixId;
        }

        private void UpdateBillStatus(int edtFileId, int billStatusId)
        {
            List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("edtFileId", edtFileId),
                    new SqlParameter("billStatusId", billStatusId)
                };

            Logger.InfoFormat("UpdateBillStatus: edtFileId={0}  billStatusId={1}", edtFileId, billStatusId);
            context.ExecuteSqlStoredProcedure("dbo.UpdateBillStatusToSentConfirmed", parameters, 300);
        }

        private void UpdateBillStatus(int edtFileId, List<int> billDetailIds)
        {
            List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("edtFileId", edtFileId),
                    new SqlParameter("billDetailIds", string.Join(",", billDetailIds))
                };

            Logger.InfoFormat("UpdateBillStatus: edtFileId={0}  billDetailIds={1}", edtFileId, string.Join(",", billDetailIds));
            context.ExecuteSqlStoredProcedure("dbo.UpdateBillStatusToFiled", parameters, 300);
        }

        private void UpdateBillStatus(List<int> appointmentIds, List<int> admissionIds)
        {
            if ((appointmentIds == null || appointmentIds.Count == 0) && (admissionIds == null || admissionIds.Count == 0))
            {
                Logger.InfoFormat("UpdateBillStatus: appointmentIds & admissionIds are empty");
                return;
            }

            List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("appointmentIds", string.Join(",", appointmentIds)),
                    new SqlParameter("admissionIds", string.Join(",", admissionIds))
                };

            Logger.InfoFormat("UpdateBillStatus: appointmentIds={0}  admissionIds={1}", string.Join(",", appointmentIds), string.Join(",", admissionIds));
            context.ExecuteSqlStoredProcedure("dbo.UpdateBillStatusAppointmentHospital", parameters, 300);
        }

        private bool DoesFileExist(string fileName)
        {
            DateTime date = DateTime.Now.AddDays(-270);
            if (context.Billing_Files.Where(a => a.fileName.Contains(fileName) && a.DateEntered > date).Any())
            {
                Logger.Info($"Same file ({fileName} has been downloaded/processed in last 9 months). file will not be processed again");
                return true;
            }

            return false;
        }

        private void LogException(string message, Exception ex)
        {
            Logger.Error(message);

            string txt = "Exception in:" + ex.StackTrace;
            txt += (ex.Message);
            while (ex.InnerException != null)
            {
                txt += ToString(ex.InnerException);
                ex = ex.InnerException;
            }
            Logger.Error(txt);
        }

        private static string ToString(Exception ex)
        {
            string result = string.Empty;

            result += ex.Message;

            result += ExceptionInfo(ex);

            return result;
        }

        private static string ExceptionInfo(Exception ex)
        {
            MethodInfo method = FindMethod(ex);

            if (method != null)
            {
                return method.Invoke(null, new object[] { ex }).ToString();
            }
            return "";
        }

        private static MethodInfo FindMethod(Exception ex)
        {
            MethodInfo[] mds = typeof(ClaimFiles).GetMethods();
            foreach (MethodInfo m in mds)
            {
                if (m.Name != "ExceptionInfoEx") continue;      // method name

                if (!m.IsStatic) continue;                      // must be static

                ParameterInfo[] pars = m.GetParameters();
                if (pars.Length != 1) continue;                   // one parameter only

                if (pars[0].ParameterType == ex.GetType())       // check requested type
                {
                    return m;
                }
            }
            return null;
        }
    }
}