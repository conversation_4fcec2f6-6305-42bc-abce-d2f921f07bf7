﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Caching
{
    public class CacheKeys
    {
        public const string CEREBRUM_MEASUREMENT_RANGE = "Cerebrum.dbo.MeasurementRanges";
        public const string CEREBRUM_MEASUREMENT_BSA_RANGE = "Cerebrum.dbo.MeasurementBSARanges";
        public const string CEREBRUM_PERMISSION_DB = "CerebrumPermissionsDB";
        public static string GetAppointmentTypesKey()
        {
            return "AppointmentTypes";
        }

        public static string GetAppointmentTestStatusesKey()
        {
            return "AppointmentTestStatuses";
        }

        public static string GetRequistionStatusesKey()
        {
            return "RequistionStatuses";
        }

        public static string GetReportCategoriesKey()
        {
            return "ReportCategories";
        }

        public static string GetReportClassesKey()
        {
            return "ReportClasses";
        }

        public static string GetPracticeSchUsersKey(int practiceId, int officeId)
        {
            return BuildKey("PracticeSchUsers",practiceId,officeId);
        }

        public static string GetPracticeDoctorsKey(int practiceId)
        {
            return BuildKey("PracticeDoctors", practiceId); 
        }

        public static string GetPracticeTestsKey(int practiceId)
        {
            return BuildKey("PracticeTests", practiceId);
        }

        public static string GetOfficeDoctorsKey(int practiceId, int officeId)
        {
            return BuildKey("OfficeDoctors", practiceId, officeId);            
        }

        public static string GetOfficeTechsKey(int practiceId, int officeId)
        {
            return BuildKey("OfficeTechs", practiceId, officeId);
        }

        #region Private Methods
        private static string BuildKey(string key, int practiceId, int officeId)
        {
            return practiceId + "_" + officeId + "_" + key;
        }

        private static string BuildKey(string key, int practiceId)
        {
            return practiceId + "_" + key;
        } 
        #endregion


    }
}
