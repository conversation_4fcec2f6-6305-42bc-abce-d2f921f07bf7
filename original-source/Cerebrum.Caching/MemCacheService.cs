﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Runtime.Caching;
using System.Configuration;

namespace Cerebrum.Caching
{
    public class MemCacheService : IMemCacheService
    {
        private readonly MemoryCache _memoryCache;        
        private int defaultExpire = 180; // in minutes = 3 hours
        private bool _isForceCache = false; // some special case where we want to bypass the IsCachingOn()

        public MemCacheService()
        {
            _memoryCache = MemoryCache.Default;
        }

        public MemCacheService(bool isForceCache)
        {
            _memoryCache = MemoryCache.Default;
            _isForceCache = isForceCache;
        }

        //public T GetOrSet<T>(string key, Func<T> callBackData, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class
        //{
        //    var result = GetCerebrumCacheItem(key);

        //    if(result == null)
        //    {
        //        SetItem<T>(key, callBackData, expireMinutes);
        //        result = GetCerebrumCacheItem(key);
        //    }

        //    return result.GetItem<T>();           
        //}

        //public T GetOrSet<T>(string key, T data, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class
        //{

        //    var result = GetCerebrumCacheItem(key);

        //    if (result == null)
        //    {
        //        SetItem<T>(key, data, expireMinutes);
        //        result = GetCerebrumCacheItem(key);
        //    }

        //    return result.GetItem<T>();

        //}

        //public List<T> GetOrSetList<T>(string key, Func<List<T>> callBackData, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class
        //{

        //    var result = GetCerebrumCacheItem(key);

        //    if (result == null)
        //    {
        //        SetList<T>(key, callBackData, expireMinutes);
        //        result = GetCerebrumCacheItem(key);
        //    }

        //    return result.GetListItems<T>();            
        //}

        //public List<T> GetOrSetList<T>(string key, List<T> data, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class
        //{

        //    var result = GetCerebrumCacheItem(key);

        //    if (result == null)
        //    {
        //        SetList<T>(key, data, expireMinutes);
        //        result = GetCerebrumCacheItem(key);
        //    }

        //    return result.GetListItems<T>();           
        //}

        public T Get<T>(string key) where T : class
        {
            var result = GetFromCacheItem<T>(key);           

            // Return the cached object
            return result;
        }

        public List<T> GetList<T>(string key) where T : class
        {
            var result = GeFromCacheList<T>(key);           

            // Return the cached list
            return result;
        }

        //public void Set<T>(string key, Func<T> callBackData, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class
        //{
        //    SetItem<T>(key, callBackData, GetDefaultExpire(expireMinutes));           
        //}

        public void Set<T>(string key, T data, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class
        {
            SetItem<T>(key, data, GetDefaultExpire(expireMinutes));            
        }

        //public void SetList<T>(string key, Func<List<T>> callBackData, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class
        //{
        //    SetListItems<T>(key, callBackData, GetDefaultExpire(expireMinutes));            
        //}

        public void SetList<T>(string key, List<T> data, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class
        {
            SetListItems<T>(key, data, GetDefaultExpire(expireMinutes));            
        }

        public bool HasKey(string key)
        {
            return _memoryCache.Contains(key);
        }

        public bool RemoveKey(string key)
        {
            if(_memoryCache.Contains(key))
            {
                _memoryCache.Remove(key);
                return true;
            }
            else
            {
                return false;
            }

        }

        public int ExpireMinutes(int hours)
        {
            if (hours > 0)
            {
                return (60 * hours);
            }
            else
            {
                return defaultExpire; 
            }

        }

        //public MemoryCache MemoryCache
        //{
        //    get
        //    {
        //        return _memoryCache;
        //    }
        //}

        public List<VMCacheItem> GetCacheItems()
        {
            var cacheItems = new List<VMCacheItem>();
            foreach (var c in _memoryCache)
            {
                var key = c.GetType().GetProperty("Key");
                var keyName = key.GetValue(c);                

                if (keyName != null && !keyName.ToString().Contains("MetadataPrototypes"))
                {
                    var kVal = c.GetType().GetProperty("Value");
                    var keyValue = kVal.GetValue(c) as CerebrumCacheItem;

                    var cacheItem = new VMCacheItem();
                    cacheItem.Key = keyName.ToString();
                    cacheItem.Exiration = keyValue.ExpireDate;

                    cacheItems.Add(cacheItem);
                }

            }
            cacheItems = cacheItems.OrderBy(o => o.Key).ToList();

            return cacheItems;
        }

        //public List<VMCacheItem> GetCacheItems()
        //{
        //    var cacheItems = new List<VMCacheItem>();
        //    foreach (var c in _memoryCache)
        //    {
        //        var key = c.GetType().GetProperty("Key");
        //        var keyName = key.GetValue(c);

        //        if (keyName != null && !keyName.ToString().Contains("MetadataPrototypes"))
        //        {
        //            var kVal = c.GetType().GetProperty("Value");
        //            var keyValue = kVal.GetValue(c) as CerebrumCacheItem;

        //            var cacheItem = new VMCacheItem();
        //            cacheItem.Key = keyName.ToString();
        //            cacheItem.Exiration = keyValue.ExpireDate;

        //            cacheItems.Add(cacheItem);
        //        }

        //    }
        //    cacheItems = cacheItems.OrderBy(o => o.Key).ToList();

        //    return cacheItems;
        //}

        public List<VMCacheItem> GetCacheItemsByPractice(int practiceId)
        {
            List<VMCacheItem> cacheItems = GetCacheItems();
            List<VMCacheItem> practiceCache = new List<VMCacheItem>();
            foreach (var item in cacheItems)
            {
                string practiceIdStr = practiceId.ToString();
                if (item.Key.StartsWith(practiceIdStr + "_"))
                {
                    practiceCache.Add(item);
                }
            }
            

            return practiceCache;
        }


        public void Dispose()
        {

        }

        private int GetDefaultExpire(int expireMinutes)
        {
            if(expireMinutes > 0)
            {
                return expireMinutes;
            }

            else
            {
                return defaultExpire;
            }
        }

        //private void SetListItems<T>(string key, Func<List<T>> callBackData, int expireMinutes) where T : class
        //{           
        //    var result = _memoryCache[key] as CerebrumCacheItem;//List<T>;
        //    // Check whether the value exists
        //    if (result == null)
        //    {
        //        //lock (_lock)
        //        lock (TypeLock<T>.Lock)
        //        {
        //            // Try to get the object from the cache again
        //            result = _memoryCache[key] as CerebrumCacheItem; //List<T>;
        //            if (result == null)
        //            {
        //                var expireTime = DateTimeOffset.Now.AddMinutes(GetDefaultExpire(expireMinutes));
        //                var cacheItem = new CerebrumCacheItem();
        //                cacheItem.IsList = true;
        //                cacheItem.SetCacheItem(callBackData());
        //                cacheItem.ExpireDate = expireTime.DateTime;

        //                result = cacheItem;
        //                CacheItemPolicy policy = new CacheItemPolicy();                       
        //                policy.AbsoluteExpiration = expireTime;                       

        //                // Add to the cache
        //                _memoryCache.Set(key, result,policy);
        //            }
        //        }
        //    }
        //}

        private void SetListItems<T>(string key, List<T> data, int expireMinutes) where T : class
        {
            if (_isForceCache || IsCachingOn())
            {
                var result = _memoryCache[key] as CerebrumCacheItem;
                // Check whether the value exists
                if (result == null)
                {
                    //lock (_lock)
                    lock (TypeLock<T>.Lock)
                    {
                        // Try to get the object from the cache again
                        result = _memoryCache[key] as CerebrumCacheItem; //List<T>;
                        if (result == null)
                        {
                            var expireTime = DateTimeOffset.Now.AddMinutes(GetDefaultExpire(expireMinutes));
                            var cacheItem = new CerebrumCacheItem();
                            cacheItem.IsList = true;
                            cacheItem.SetCacheItem(data);
                            cacheItem.ExpireDate = expireTime.DateTime;

                            result = cacheItem;
                            CacheItemPolicy policy = new CacheItemPolicy();
                            policy.AbsoluteExpiration = expireTime;

                            // Add to the cache
                            _memoryCache.Set(key, result, policy);
                        }
                    }
                }
            }
        }

        //private void SetItem<T>(string key, Func<T> callBackData, int expireMinutes) where T : class
        //{
            
        //    var result = _memoryCache[key] as CerebrumCacheItem;
        //    // Check whether the value exists
        //    if (result == null)
        //    {
        //        //lock (_lock)
        //        lock (TypeLock<T>.Lock)
        //        {
        //            // Try to get the object from the cache again
        //            result = _memoryCache[key] as CerebrumCacheItem; //List<T>;
        //            if (result == null)
        //            {
        //                var expireTime = DateTimeOffset.Now.AddMinutes(GetDefaultExpire(expireMinutes));
        //                var cacheItem = new CerebrumCacheItem();
        //                cacheItem.IsList = false;
        //                cacheItem.SetCacheItem(callBackData());
        //                cacheItem.ExpireDate = expireTime.DateTime;

        //                result = cacheItem;
        //                CacheItemPolicy policy = new CacheItemPolicy();
        //                policy.AbsoluteExpiration = expireTime;

        //                // Add to the cache
        //                _memoryCache.Set(key, result, policy);
        //            }
        //        }
        //    }
           
        //}

        private void SetItem<T>(string key, T data, int expireMinutes) where T : class
        {
            if (_isForceCache || IsCachingOn())
            {
                var result = _memoryCache[key] as CerebrumCacheItem;
                                                                    
                if (result == null)
                {
                    //lock (_lock)
                    lock (TypeLock<T>.Lock)
                    {
                        // Try to get the object from the cache again
                        result = _memoryCache[key] as CerebrumCacheItem; //List<T>;
                        if (result == null)
                        {
                            var expireTime = DateTimeOffset.Now.AddMinutes(GetDefaultExpire(expireMinutes));
                            var cacheItem = new CerebrumCacheItem();
                            cacheItem.IsList = false;
                            cacheItem.SetCacheItem(data);
                            cacheItem.ExpireDate = expireTime.DateTime;

                            result = cacheItem;
                            CacheItemPolicy policy = new CacheItemPolicy();
                            policy.AbsoluteExpiration = expireTime;

                            // Add to the cache
                            _memoryCache.Set(key, result, policy);
                        }
                    }
                }
            }
            
        }

        private List<T> GeFromCacheList<T>(string key) where T : class
        {
            var keyData = _memoryCache[key] as CerebrumCacheItem;

            if(keyData != null)
            {
                var data = keyData.GetListItems<T>();
                if (data != null)
                {
                    return data;
                }               
            }

            return null;
        }

        private T GetFromCacheItem<T>(string key) where T : class
        {
            var keyData = _memoryCache[key] as CerebrumCacheItem;

            if (keyData != null)
            {
                var data = keyData.GetItem<T>();
                if (data != null)
                {
                    return data;
                }
            }

            return null;
        }

        private CerebrumCacheItem GetCerebrumCacheItem(string key)
        {
            return _memoryCache[key] as CerebrumCacheItem;
        }

        private bool KeyExists(string key)
        {
            return _memoryCache.Contains(key);
        }

        public static bool IsCachingOn()
        {
            bool cachingOn = false;
            string configCaching = ConfigurationManager.AppSettings ["CachingOn"];

            if(!String.IsNullOrWhiteSpace(configCaching))
            {
                try
                {                    
                    cachingOn = Convert.ToBoolean(configCaching);
                }
                catch
                {

                }
            }

            return cachingOn;
        }

        private static class TypeLock<T>
        {
            public static object Lock {get;} = new object();
        }

        private static class KeyLock
        {
            public static object Lock { get; } = new object();
        }
    }
}