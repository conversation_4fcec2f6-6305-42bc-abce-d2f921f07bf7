﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Web;

namespace Cerebrum.Caching
{
    public interface IMemCacheService : IDisposable
    {
        //MemoryCache MemoryCache { get; }
        //T GetOrSet<T>(string key, Func<T> callBackData, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class;

        //List<T> GetOrSetList<T>(string key, Func<List<T>> callBackData, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class;

        //T GetOrSet<T>(string key, T data, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class;

        //List<T> GetOrSetList<T>(string key, List<T> data, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class;

        T Get<T>(string key) where T : class;

        List<T> GetList<T>(string key) where T : class;

        List<VMCacheItem> GetCacheItems();

        List<VMCacheItem> GetCacheItemsByPractice(int practiceId);

        //void Set<T>(string key, Func<T> callBackData, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class;

        void Set<T>(string key, T data, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class;

        //void SetList<T>(string key, Func<List<T>> callBackData, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class;

        void SetList<T>(string key, List<T> data, int expireMinutes = 0, bool isAbsoluteExpiredation = true) where T : class;

        bool HasKey(string key);

        bool RemoveKey(string key);

        int ExpireMinutes(int hours); // hours to expire
    }
}