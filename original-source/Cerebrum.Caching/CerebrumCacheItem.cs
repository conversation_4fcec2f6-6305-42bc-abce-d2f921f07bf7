﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Caching
{
    public class CerebrumCacheItem//<T> where T : class
    {
        private object _cacheItem;
        public DateTime? ExpireDate { get; set; }
        public bool IsList { get; set; }       
        public List<T> GetListItems<T>() where T : class
        {
            return _cacheItem as List<T>;
        }

        public T GetItem<T>() where T : class
        {
            return _cacheItem as T;
        }

        public void SetCacheItem( object data)
        {
            _cacheItem = data;
        }


    }
}
