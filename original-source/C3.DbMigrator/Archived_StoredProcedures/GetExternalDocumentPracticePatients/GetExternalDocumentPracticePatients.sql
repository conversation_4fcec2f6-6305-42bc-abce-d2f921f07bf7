﻿
-- =============================================
-- Author:		<Author,,<PERSON>>
-- Create date: <Create July 12,2019>
-- Description:	<Find Patient,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetExternalDocumentPracticePatients]
	@PracticeId INT,	
	@LastName nvarchar(200)= Null,
	@FirstName nvarchar(200)= Null,
	@OHIP nvarchar(15)= Null,
	@TOPResult INT
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @query nvarchar(2000);
	DECLARE @param nvarchar(500);
	--Config strings to LIKE type search
	DECLARE @slastname nvarchar(100) = '"'+ltrim(rtrim(@LastName))+'*"'; 
	DECLARE @sfirstname nvarchar(100) = '"'+ltrim(rtrim(@FirstName))+'*"';

	SET @TOPResult = 100; -- Set static 100 since application calls for 20000 records for no good reason.

	IF (@OHIP <> '') 
			SELECT TOP (@TOPResult) 
				D.Id,
				D.PatientRecordId,
				(select top 1 PracticeDoctorId from DemographicsMainResponsiblePhysicians where DemographicId=d.id order by id desc) AS PracticeDoctorId,
				@PracticeId AS PracticeId,
				D.lastName AS LastName,
				D.firstName AS FirstName,
				D.dateOfBirth AS DateOfBirth,
				ISNULL(HC.number,'') AS OHIP,
				d.active
			FROM PatientRecords P
			JOIN Demographics D  ON P.Id= D.PatientRecordId
			JOIN ( select top 20000 hc.DemographicId, hc.number
			         from DemographicsHealthCards HC 
					where HC.number LIKE @OHIP+ '%' ) hc ON D.Id = HC.DemographicId
		   WHERE P.PracticeId=@PracticeId 
       ORDER BY d.lastname, d.firstname;

	ELSE IF ( ( @LastName IS NOT NULL AND ltrim(rtrim(@LastName)) <> '' )
	       OR ( @FirstName IS NOT NULL AND ltrim(rtrim(@FirstName)) <> '') )
	begin
		SET @query = 'SELECT TOP (@TOPResult) 
			  		 		 D.Id,
							 D.PatientRecordId,
							 (select top 1 PracticeDoctorId from DemographicsMainResponsiblePhysicians where DemographicId=d.id order by id desc) AS PracticeDoctorId,
							 @PracticeId AS PracticeId,
							 D.lastName AS LastName,
							 D.firstName AS FirstName,
							 D.dateOfBirth AS DateOfBirth,
							 ISNULL((select top 1 hc.number from DemographicsHealthCards hc where D.Id = HC.DemographicId order by hc.id desc ),'''') AS OHIP,
							 d.active
  						FROM PatientRecords P
						JOIN Demographics D  ON P.Id= D.PatientRecordId
					   WHERE P.PracticeId=@PracticeId ';

		IF LOWER(ltrim(rtrim(@LastName)))=LOWER(ltrim(rtrim(@FirstName)))
			SET @query = @query + ' and  contains ((lastname, firstname), @Slastname) ';
		ELSE
		begin
			if ltrim(rtrim(@LastName)) <> ''
				SET @query = @query + ' and  contains ((lastname), @Slastname) ';

			if ltrim(rtrim(@FirstName)) <> ''
				SET @query = @query + ' and  contains ((firstname), @Sfirstname) ';

		end;

		SET @query = @query + ' ORDER BY d.lastname, d.firstname ';

		SET @param = '@TOPResult int, @PracticeId int, @Slastname nvarchar(300), @Sfirstname nvarchar(300)';

--		print @query;

  	    EXEC sp_executesql @query, @param, @TOPResult = @TOPResult, @PracticeId = @PracticeId, @Slastname = @Slastname, @Sfirstname = @Sfirstname;
	end;
END
