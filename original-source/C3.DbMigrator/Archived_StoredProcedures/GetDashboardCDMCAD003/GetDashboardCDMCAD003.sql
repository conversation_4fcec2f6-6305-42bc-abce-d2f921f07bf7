﻿CREATE PROCEDURE [dbo].[GetDashboardCDMCAD003] (@PracticeDoctorId INT = NULL)
AS
BEGIN	
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @CADCoded AS TABLE (PatientRecordId INT)
	DECLARE @BPDocumented AS TABLE (PatientRecordId INT)
	DECLARE @LipidDocumented AS TABLE (PatientRecordID INT,testCodeIdentifier NVARCHAR(400),testResultFloat FLOAT,collectionDate DATETIME2)	
	DECLARE @ObesityDocumented AS TABLE (PatientRecordId INT,Name NVARCHAR(400))
	DECLARE @BPCompleted AS TABLE (PatientRecordId INT)
	DECLARE @LipidCompleted AS TABLE (PatientRecordId INT)
	DECLARE @ObesityCompleted AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE 
	D.active = 0														-- Pastient is active		
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) >= 18	-- Patient is 18 and over	
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	--- CAD Coded			
	INSERT INTO @CADCoded
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('D3-13040', 'D3-10030', 'D3-14016', 'D3-14017', '414545008', '413439005', '413838009', '194828000')
              OR CPP.DiagnosticCode LIKE '410%'
              OR CPP.DiagnosticCode LIKE '411%'
              OR CPP.DiagnosticCode LIKE '412%'
              OR CPP.DiagnosticCode LIKE '413%'
              OR CPP.DiagnosticCode LIKE '414%'
              OR CPP.DiagnosticCode LIKE '429%'
              OR CPP.DiagnosticCode LIKE 'I20%'
              OR CPP.DiagnosticCode LIKE 'I21%'
              OR CPP.DiagnosticCode LIKE 'I22%'
              OR CPP.DiagnosticCode LIKE 'I23%'
              OR CPP.DiagnosticCode LIKE 'I24%'
              OR CPP.DiagnosticCode LIKE 'I25%'
    )
	AND Deleted = 0 AND UpdateDate IS NULL

	--- Documented Blood Pressure (BP) measurement 
	INSERT INTO @BPDocumented
	SELECT CAD.PatientRecordId	
	FROM VPMeasurementSavedValues VPV
	JOIN VPUniqueMeasurements VPM on VPV.VPUniqueMeasurementId = VPM.ID
	JOIN AppointmentTestSaveLogs ATSL ON VPV.AppointmentTestSaveLogId = ATSL.Id
	JOIN AppointmentTests APPT ON APPT.Id = ATSL.AppointmentTestId
	JOIN Appointments APP ON APP.Id = APPT.AppointmentId
	JOIN @CADCoded CAD ON CAD.PatientRecordId = APP.PatientRecordId -- Only CAD Coded
	WHERE DATEDIFF(MONTH,APP.appointmentTime,GETDATE()) <= 12 -- Appointments in the last year
	AND APP.appointmentTime <= GETDATE() -- No future appointments
	AND APPT.TestId = 29 -- VP Only
	AND APPT.IsActive = 1 -- Not Canceled
	AND ATSL.IsActive = 1 AND APPT.IsActive = 1
	AND
	VPM.Name LIKE '%BP%'	

	--- Lipids
	INSERT INTO @LipidDocumented
	SELECT BP.PatientRecordId, r.testCodeIdentifier,  r.testResultFloat, r.collectionDate     
    FROM @BasePopulation BP
	JOIN HL7Patient P ON BP.PatientRecordId = P.PatientRecordId
    JOIN HL7Report RPT ON RPT.HL7PatientId = P.Id
    JOIN HL7ReportVersion V ON V.HL7ReportId = RPT.ID
    JOIN HL7Result R on R.HL7ReportVersionId = V.Id
	WHERE 
		(r.testCodeIdentifier LIKE '%TRIG%' 
			OR r.testCodeIdentifier LIKE '%TOTAL CHOL%'
			OR r.testCodeIdentifier LIKE '%ldl%'
			OR r.testCodeIdentifier LIKE '%hdl%')
    AND (r.testCodeIdentifier not LIKE '%NON-ldl%'
			OR r.testCodeIdentifier not LIKE '%TC/hdl%')
     AND R.collectionDate > DATEADD(MONTH,-12,GETDATE())
    AND r.resultStatus in ('F', 'A', 'C')

	--- Obesity Documented
	INSERT INTO @ObesityDocumented
	SELECT CAD.PatientRecordId, vpm.Name	
	FROM VPMeasurementSavedValues VPV
	JOIN VPUniqueMeasurements VPM on VPV.VPUniqueMeasurementId = VPM.ID
	JOIN AppointmentTestSaveLogs ATSL ON VPV.AppointmentTestSaveLogId = ATSL.Id
	JOIN AppointmentTests APPT ON APPT.Id = ATSL.AppointmentTestId
	JOIN Appointments APP ON APP.Id = APPT.AppointmentId
	JOIN @BasePopulation BP ON BP.PatientRecordId = APP.PatientRecordId -- Base Population
	JOIN @CADCoded CAD ON CAD.PatientRecordId = BP.PatientRecordId -- Only CAD Coded
	WHERE DATEDIFF(MONTH,APP.appointmentTime,GETDATE()) <= 12 -- Appointments in the last year
	AND APP.appointmentTime <= GETDATE() -- No future appointments
	AND APPT.TestId = 29 -- VP Only
	AND APPT.IsActive = 1 -- Not Canceled
	AND ATSL.IsActive = 1 AND APPT.IsActive = 1
	AND
	(VPM.Name LIKE '%BMI%' OR VPM.Name LIKE '%Height%' OR VPM.Name LIKE '%Weight%' OR VPM.Name LIKE '%Waist%')

	-- define completed suite of tests for blood pressure
		INSERT INTO @BPCompleted
		SELECT PatientRecordId		
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	  -- pt must be cad coded
		AND PatientRecordId IN (SELECT PatientRecordId FROM @BPDocumented)	-- pt must have at least one BP

	-- define completed suite of tests for lipids
	INSERT INTO @LipidCompleted
	SELECT PatientRecordId	
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	  -- pt must be cad coded
	-- Pt must have at least one trig, total, ldl, and hdl
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LipidDocumented where testCodeIdentifier LIKE '%TRIG%')
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LipidDocumented where testCodeIdentifier LIKE '%TOTAL CHOL%')
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LipidDocumented where testCodeIdentifier LIKE '%ldl%')
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LipidDocumented where testCodeIdentifier LIKE '%hdl%')

		
			-- define completed suite of tests
	INSERT INTO @ObesityCompleted
	SELECT PatientRecordId	
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	  -- pt must be cad coded
	-- pt must have a bmi, or a height and weight or a height and waist circumferance
	AND (	(PatientRecordId IN (SELECT PatientRecordId FROM @ObesityDocumented where Name LIKE '%BMI%'))
			or
			(PatientRecordId IN (SELECT PatientRecordId FROM @ObesityDocumented where Name LIKE '%Height%')
			and (PatientRecordId IN (SELECT PatientRecordId FROM @ObesityDocumented where Name LIKE '%Weight%')
						or PatientRecordId IN (SELECT PatientRecordId FROM @ObesityDocumented where Name LIKE '%Waist%')
				)
			)
		)

	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @BPCompleted)				
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	
		AND PatientRecordId IN (SELECT PatientRecordId FROM @BPCompleted)		
	
	INSERT INTO @Segments
	SELECT 3,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @LipidCompleted)		
	
	INSERT INTO @Segments
	SELECT 4,PatientRecordId		
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	
		AND PatientRecordId IN (SELECT PatientRecordId FROM @LipidCompleted)		
	
	INSERT INTO @Segments
	SELECT 5,PatientRecordId		
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @ObesityCompleted)			
	
	INSERT INTO @Segments
	SELECT 6,PatientRecordId		
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	
		AND PatientRecordId IN (SELECT PatientRecordId FROM @ObesityCompleted)			
	
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log


END

