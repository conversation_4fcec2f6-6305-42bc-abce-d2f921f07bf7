﻿
CREATE PROCEDURE [dbo].[GetInventoryItemHistory] 
	@inventoryId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @itemHistory TABLE 
	(
		DeviceNumberId int not null,
		OfficeId int not null,
		OfficeName nvarchar(300),
		DeviceTypeId int not null,
		<PERSON>ceNumber nvarchar(300),
		<PERSON>ce<PERSON><PERSON> nvarchar(300),
		PatientEquipmentId int not null,
		PatientId int not null,
		PatientFullName nvarchar(500),
		DateStarted datetime not null,
		DateExpectedReturn datetime null,
		DateCreated datetime not null,
		DateReturned datetime null,
		Notes nvarchar(500),
		AppointmentTestId int not null,
		AssignedByUserId int not null,
		AssignedByUser nvarchar(500)
	)

    INSERT INTO @itemHistory
	SELECT 
	i.id AS DeviceNumberId
	,i.officeId AS OfficeId
	,o.[name] as OfficeName
	,i.inventoryTypeId AS DeviceTypeId
	,i.code AS DeviceNumber
	,it.[name] AS DeviceType
	,pe.Id AS PatientEquipmentId
	,pe.PatientRecordId AS PatientId
	,ISNULL(d.lastName,'')+', '+ISNULL(d.firstName,'') AS PatientFullName
	,pe.DateStarted
	,pe.DateExpectedReturn
	,pe.DateCreated
	,pe.DateReturned
	,pe.Notes
	,pe.AppointmentTestId		
	,pe.AssignedByUserId	
	,ISNULL((SELECT TOP 1 ISNULL(u.lastName,'')+', '+ISNULL(u.firstName,'') FROM AspNetUsers u WHERE u.UserId = pe.AssignedByUserId),'') AS AssignedByUser
	FROM
	PatientEquipments pe
	JOIN Demographics d ON pe.PatientRecordId = d.PatientRecordId
	JOIN StoreInventories i ON pe.InventoryId = i.id
	JOIN StoreInventoryTypes it ON i.inventoryTypeId = it.id
	JOIN Office o ON i.officeId = o.Id	
	WHERE i.id = @inventoryId
	ORDER By pe.DateReturned DESC

	SELECT 
	ih.DeviceNumberId
	,ih.OfficeId
	,ih.OfficeName
	,ih.DeviceTypeId
	,ih.DeviceNumber
	,ih.DeviceType
	,ih.PatientEquipmentId
	,ih.PatientId
	,ih.PatientFullName
	,ih.DateStarted
	,ih.DateExpectedReturn
	,ih.DateCreated
	,ih.DateReturned 
	,ih.Notes
	,ih.AppointmentTestId
	,ih.AssignedByUserId
	,ih.AssignedByUser
	,apt.TestId
	,t.testShortName AS TestName
	,apt.AppointmentTestStatusId 
	,ats.Color AS AppointmentTestStatusColor
	,ats.CSSClass AS AppointmentTestStatusCSSClass
	,ats.Status AS AppointmentTestStatus
	FROM @itemHistory ih
	JOIN AppointmentTests apt ON ih.AppointmentTestId = apt.Id
	JOIN Tests t ON apt.TestId = t.Id
	JOIN AppointmentTestStatus ats ON apt.AppointmentTestStatusId = ats.Id

	
END



