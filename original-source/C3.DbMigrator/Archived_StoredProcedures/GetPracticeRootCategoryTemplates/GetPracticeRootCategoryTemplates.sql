﻿CREATE PROCEDURE [dbo].[GetPracticeRootCategoryTemplates] 
	@practiceId int,
	@groupId int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 
	prt.Id AS PracticeTemplateId
	,prt.PracticeId	
	,prt.DateCreated
	,prt.DateLastModified
	,prt.LastModifiedByUserId
	,prt.TemplateId
	,rt.TemplateName
	,rt.IsSystem
	,prt.IsActive
	FROM PracticeRootCategoryTemplates prt
	JOIN RootTemplates rt ON prt.TemplateId = rt.Id
	WHERE prt.PracticeId = @practiceId
	AND rt.IsActive = 1
	AND rt.GroupId = @groupId
END