﻿CREATE PROCEDURE [dbo].[GetReportPhraseSaveTextByLogIds]
	@appointmentTestLogIds AS dbo.IntegerList READONLY
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 
	   rpst.Id
      ,rpst.[Value]
      ,rpst.[AppointmentID] AS AppointmentId
      ,rpst.[TestID] As TestId
      ,rpst.[AppointmentTestLogID] AS AppointmentTestLogId
      ,rpst.[TopLevelReportPhraseID] AS TopLevelReportPhraseId
	  ,rp.[Name] AS TopLevelReportPhraseText
  FROM ReportPhraseSavedTexts rpst
  JOIN @appointmentTestLogIds l ON rpst.AppointmentTestLogID = l.IntegerValue
  JOIN ReportPhrases rp ON rpst.TopLevelReportPhraseID = rp.Id
  
  END

