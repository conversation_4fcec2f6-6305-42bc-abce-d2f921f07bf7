﻿CREATE PROCEDURE [dbo].[GetDaysheetCohorts]
	@patientIds AS dbo.IntegerList READONLY
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT
	  pc.[Id]
      ,pc.[PatientId]
      ,pc.[Started]
      ,pc.[Terminated]
      ,pc.[DoctorId]
      ,pc.[OfficeId]
      ,pc.[Notes]
      ,pc.[CohortId]
	  ,c.[Description] AS Cohort
  FROM PatientCohorts pc
  JOIN Cohorts c ON pc.CohortId = c.Id
  WHERE PatientId IN (SELECT DISTINCT(IntegerValue) FROM @patientIds)
  END
