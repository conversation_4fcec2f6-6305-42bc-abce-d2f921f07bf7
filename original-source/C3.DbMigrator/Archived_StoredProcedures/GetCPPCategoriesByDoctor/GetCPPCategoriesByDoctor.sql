﻿CREATE PROCEDURE [dbo].[GetCPPCategoriesByDoctor] 
	@externalDoctorId INT,
	@patientId INT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @group INT = 0;
    -- Insert statements for procedure here
	SELECT 
	g.Id
	,g.[Text]
	,g.[Group]
	,ISNULL(s.[Order],1000000) AS [Order]
	,ISNULL(s.[Visible],0) AS [Visible]
	,s.[Text] AS CustomText
	,CAST(ISNULL(sk.[Skip],1) AS BIT) AS Skipped -- 1 means that we dont want to see it in the letter
	,ISNULL(s.PatientRecordId,0) AS PatientId
	,@externalDoctorId AS ExternalDoctorId
	FROM VP_CPP_Category g
	LEFT JOIN VP_CPP_Setting s ON g.Id = s.VP_CPP_Category_Id AND s.DoctorID = @externalDoctorId
	LEFT JOIN VP_CPP_Skipped sk ON g.Id = sk.CPP_Category_ID AND sk.UserID = @externalDoctorId		
	ORDER BY [Order] ASC
		
END












