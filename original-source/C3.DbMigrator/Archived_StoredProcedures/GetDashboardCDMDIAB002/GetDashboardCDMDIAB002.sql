﻿CREATE PROCEDURE [dbo].[GetDashboardCDMDIAB002] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @DiabetesCoded AS TABLE (PatientRecordId INT)
	DECLARE @HbA1cDocumented AS TABLE (PatientRecordId INT)
	DECLARE @HbA1cMostRecentIn6Months AS TABLE (PatientRecordId INT)	
	DECLARE @HbA1cMostRecentMore6Months AS TABLE (PatientRecordId INT)	
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)


	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId		
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) >= 40	-- Patient is 40 and over		
	GROUP BY D.PatientRecordId

	--- Diabetes Coded 
	INSERT INTO @DiabetesCoded
	SELECT PatientRecordId	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('250'
							,'DB-610'
							,'46635009', '44054006', '73211009')
	OR CPP.DiagnosticCode LIKE 'E10%'
	OR CPP.DiagnosticCode LIKE 'E11%')	
	AND Deleted = 0 AND CPP.UpdateDate IS NULL

	--- 2 or more documented HbA1c screenings in the last 12 months 
	INSERT INTO @HbA1cDocumented
	SELECT P.PatientRecordId	
	FROM HL7Result R
	JOIN HL7ReportVersion V ON R.HL7ReportVersionId = V.Id
	JOIN HL7Report RPT ON RPT.Id = V.HL7ReportId
	JOIN HL7Patient P ON RPT.HL7PatientId = P.Id
	WHERE 
	--CONTAINS(testCodeIdentifier,'"hba1c" OR "gluc"')	
	(testCodeIdentifier LIKE '%hba1c%' OR testCodeIdentifier LIKE '%gluc%')
	AND R.collectionDate > DATEADD(MONTH,-12,GETDATE())
	AND R.resultStatus IN ('F', 'A', 'C')
	GROUP BY P.PatientRecordId
	HAVING COUNT(R.Id)>=2

	--- Most recent HbA1c screening documented <= 6 months ago  
	INSERT INTO @HbA1cMostRecentIn6Months
	SELECT P.PatientRecordId	
	FROM HL7Result R
	JOIN HL7ReportVersion V ON R.HL7ReportVersionId = V.Id
	JOIN HL7Report RPT ON RPT.Id = V.HL7ReportId
	JOIN HL7Patient P ON RPT.HL7PatientId = P.Id
	WHERE 
	--CONTAINS(testCodeIdentifier,'"hba1c" OR "gluc"')	
	(testCodeIdentifier LIKE '%hba1c%' OR testCodeIdentifier LIKE '%gluc%')
	AND R.resultStatus IN ('F', 'A', 'C')
	GROUP BY P.PatientRecordId
	HAVING MAX(R.collectionDate) >= DATEADD(MONTH,-6,GETDATE())

	--- Most recent HbA1c screening documented > 6 months ago  
	INSERT INTO @HbA1cMostRecentMore6Months
	SELECT P.PatientRecordId	
	FROM HL7Result R
	JOIN HL7ReportVersion V ON R.HL7ReportVersionId = V.Id
	JOIN HL7Report RPT ON RPT.Id = V.HL7ReportId
	JOIN HL7Patient P ON RPT.HL7PatientId = P.Id
	WHERE 
	--CONTAINS(testCodeIdentifier,'"hba1c" OR "gluc"')	
	(testCodeIdentifier LIKE '%hba1c%' OR testCodeIdentifier LIKE '%gluc%')
	AND R.resultStatus IN ('F', 'A', 'C')
	GROUP BY P.PatientRecordId
	HAVING MAX(R.collectionDate) < DATEADD(MONTH,-6,GETDATE())
	
	--- Segments
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesCoded)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @HbA1cDocumented)
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesCoded)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @HbA1cDocumented)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @HbA1cMostRecentIn6Months)
	
	INSERT INTO @Segments
	SELECT 3,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesCoded)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @HbA1cDocumented)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @HbA1cMostRecentMore6Months)
	
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments
	

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log
END
