﻿CREATE PROCEDURE [dbo].[GetPractice<PERSON>orkList] 
	-- Add the parameters for the stored procedure here
	@practiceId INT,
	@officeId INT,
	@practiceDoctorId INT,	
	@testGroupID INT,
	@testStatusIds  dbo.IntegerList READONLY,
	@appointmentType INT,
	@testSetForReview BIT,
	@IsAbnormal BIT,
	@testsOnlyExt BIT,
	@excludeECG BIT,	
	@fromDate DATETIME = NULL,
	@toDate DATETIME = NULL,
    @PageNum INT,
    @PageSize INT,
	@selectedPriorityIds dbo.IntegerList READONLY

AS
BEGIN

	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
    SET NOCOUNT ON;

	DECLARE @appointmentStatus AS INT
	DECLARE @cancelledStatus AS INT
	DECLARE @isActive AS BIT
--	DECLARE @dataTransferAppointmentType INT

    DECLARE @query nvarchar(4000);
    DECLARE @params nvarchar(1000);
	DECLARE @maxRank AS INT;
	DECLARE @defaultMaxRank AS INT;

	SET @appointmentStatus =1
	set @isActive=1
	SET @cancelledStatus=7
	SET @defaultMaxRank=10
--	SET @dataTransferAppointmentType=55
    SELECT @maxRank = IsNull(MAX([RANK]), @defaultMaxRank) FROM AppointmentPriority WHERE PracticeId = @practiceId AND IsActive = 1
    SET @maxRank = @maxRank + 1;
	DECLARE @appointments TABLE
	   ( id                        int identity(1,1) not null,
	     app_id                    int not null,
         officeid                  int null,
         practiceid                int null,
         officeName                nvarchar(200),
         AppointmentTypeId         int null,
         AppointmentTypeParentId   int null,
		 AppointmentTypeName       nvarchar(100) null,
         appointmentStatus         int null,
         PracticeDoctorId          int null,
         referralDoctorId          int null,
         PatientRecordId           int null,
         appTestID                 int null,
         starttime                 datetime null,
         AppointmentTestStatusId   int null,
		 IsAbnormal                bit null,
         SetForReview              bit null,
         ReassignDate              datetime null,
         ReassignDocID             int null,
         testid                    int null,
         IsVP                      bit null,
         PracDocDescr              int null,
		 AppointmentPriorityId     int null,
		 [Rank]					   int null,
		 primary key (id)
	   );
    
    SET @query = N'SELECT DISTINCT ap.[Id]
                         ,ap.OfficeId
	                     ,o.PracticeId AS PracticeId
	                     ,o.[name] AS OfficeName
	                     ,ap.[AppointmentTypeId] 
	                     ,ISNULL(typ.[AppointmentTypeId],0) AS AppointmentTypeParentId
	                     ,typ.[name] as AppointmentType
                      -- ,ISNULL((SELECT [name] FROM AppointmentTypes WHERE id=typ.AppointmentTypeId),'') AS AppointmentTypeParent */
	                     ,ap.[appointmentStatus] AS [AppointmentStatus]
	                     ,ap.[PracticeDoctorId]
	                     ,ap.referralDoctorId AS ReferralDoctorId
	                     ,ap.[PatientRecordId] AS PatienId
	                     ,apt.Id AS AppointmentTestId
	                     ,apt.[startTime] as TestDate
	                     ,apt.AppointmentTestStatusId AS TestStatusId
	                     ,apt.IsAbnormal AS IsAbnormal -- redmine #12567, cloned from SVN 10717
	                     ,apt.SetForReview
	                     ,apt.ReassignDate
	                     ,apt.ReassignDocID AS ReassignDocId
                         ,apt.TestID
                         ,CASE WHEN tg.GroupID = 101 THEN cast(1 as bit) ELSE cast(0 as bit) END AS IsVP
                         ,IsNull(atr.PracticeDoctorID, ap.[PracticeDoctorId]) as PracDocDescr
						 ,ap.AppointmentPriorityId
						 ,IsNull(appPrior.Rank, @maxRank) as [Rank]
                    FROM [AppointmentTests] as apt
                    JOIN [Appointments] as ap on (apt.AppointmentId = ap.Id )
                    JOIN [Office] o ON ( o.Id = ap.OfficeId )
                    JOIN [AppointmentTypes] as typ on ( ap.[AppointmentTypeId] = typ.Id )
                    JOIN [TestGroups] tg on (tg.testID = apt.testID )
               LEFT JOIN ( select atr.AppointmentTestId, pracDocs.id as PracticeDoctorID
                             from [AppointmentTestResources] atr
                             join [AspNetUsers] Users ON ( atr.assignedToUserId = Users.UserID )
                             join [PracticeDoctors] pracDocs ON (Users.Id = pracDocs.ApplicationUserId )
                            where pracDocs.PracticeID = @practiceId
--                            and   atr.isDoctorRequiredInOffice = 0 -- Ticket 6357
                            and   atr.isActive = 1 ) as atr ON (apt.ID = atr.AppointmentTestId )
               LEFT JOIN [AppointmentPriority] as appPrior on ( ap.AppointmentPriorityId=appPrior.Id ) 
                   WHERE o.PracticeId = @practiceId
		           AND   ap.[appointmentStatus] > @appointmentStatus 
		           AND   ap.[appointmentStatus]<> @cancelledStatus
                   AND   ap.[IsActive] = @isActive 
		           AND   apt.IsActive = @isActive ';

   	IF EXISTS(SELECT * FROM @selectedPriorityIds)
	   BEGIN
       SET @query = @query + N' AND ( ap.AppointmentPriorityId IN (SELECT IntegerValue FROM @selectedPriorityIds) ';
	   IF EXISTS (SELECT IntegerValue FROM @selectedPriorityIds WHERE IntegerValue IS NULL)
			BEGIN
			SET @query = @query + N' OR ap.AppointmentPriorityId IS NULL ) ';
			END
       ELSE
			SET @query = @query + N' ) ';
	   END

    IF @OfficeId > 0
       SET @query = @query + N' AND ap.OfficeId = @OfficeID ';


    IF @practiceDoctorId > 0
       SET @query = @query + N' AND ( ap.PracticeDoctorId = @practiceDoctorId OR apt.ReassignDocID = @practiceDoctorId OR atr.PracticeDoctorID = @practiceDoctorId ) ';

    IF @testGroupId > 0
       SET @query = @query + N' AND tg.GroupId = @testGroupID ';

	IF EXISTS(SELECT * FROM @testStatusIds ) AND (SELECT Count(*) FROM AppointmentTestStatus) <> (SELECT Count(*) FROM @testStatusIds )
       SET @query = @query + N' AND apt.AppointmentTestStatusId IN (SELECT IntegerValue FROM @testStatusIds ) ';

    IF @appointmentType > 0 
       SET @query = @query + N' AND ap.AppointmentTypeId = @appointmentType ';

    IF @testSetForReview = 1
       SET @query = @query + N' AND apt.SetForReview = @testSetForReview ';

	-- redmine 12567, cloned from svn 10717
    --IF @actionOnAbnormal = 1 
    --   SET @query = @query + N' AND ap.actionOnAbnormal = @actionOnAbnormal ';
	IF @IsAbnormal = 1 
       SET @query = @query + N' AND apt.IsAbnormal = @IsAbnormal ';

    IF @testsOnlyExt = 1 
       SET @query = @query + N' AND typ.Id = 6 '; -- appointment type: test only ext

    IF @excludeECG = 1
       SET @query = @query + N' AND apt.TestId <> 4 ' -- not 'ECG' 

    IF @fromDate IS NOT NULL AND @toDate IS NOT NULL 
       SET @query = @query + N' AND apt.[startTime]>=@fromDate and apt.[startTime]<=@toDate ';

    SET @query = @query + N' ORDER BY IsNull(appPrior.Rank, @maxRank), apt.[Starttime] ASC';

    SET @params = N'@practiceId int, @appointmentStatus int, @cancelledStatus int, @isActive int, @OfficeID int, @practiceDoctorId int,  @testGroupID int, @testStatusIds  IntegerList READONLY, @appointmentType int, @testSetForReview int, @IsAbnormal bit, @fromDate datetime, @toDate datetime, @maxRank int, @selectedPriorityIds IntegerList READONLY';

    print @query;
--    print @params;

    INSERT INTO @appointments(app_id                 
                             ,officeid               
                             ,practiceid             
                             ,officeName             
                             ,AppointmentTypeId      
                             ,AppointmentTypeParentId
                             ,AppointmentTypeName    
                             ,appointmentStatus      
                             ,PracticeDoctorId       
                             ,referralDoctorId       
                             ,PatientRecordId        
                             ,appTestID              
                             ,starttime              
                             ,AppointmentTestStatusId
                             ,IsAbnormal -- redmine #12567, cloned from svn 10717
                             ,SetForReview           
                             ,ReassignDate           
                             ,ReassignDocID          
                             ,TestID
                             ,IsVP
                             ,PracDocDescr
							 ,AppointmentPriorityId
							 ,[Rank])
          EXEC sp_executesql @query, @params, @practiceId = @practiceId, @appointmentStatus = @appointmentStatus, @cancelledStatus = @cancelledStatus,
                             @isActive = @isActive, @OfficeID = @OfficeID, @practiceDoctorId = @practiceDoctorId, @testGroupID = @testGroupID, 
                             @testStatusIds  = @testStatusIds , @appointmentType = @appointmentType, @testSetForReview = @testSetForReview, 
							 @IsAbnormal = @IsAbnormal, @fromDate = @fromDate, @toDate = @toDate, @maxRank = @maxRank, @selectedPriorityIds = @selectedPriorityIds; -- redmine #12567, cloned from svn 10717 to replace actionOnAbnormal with IsAbnormal
                             --@actionOnAbnormal = @actionOnAbnormal, @fromDate = @fromDate, @toDate = @toDate;

--    select * from @appointments;

    ;WITH AppTestResources AS (
        SELECT apps.id
              ,apps.officeid
              ,apps.practiceid
              ,apps.officeName
              ,apps.app_id as AppointmentId
              ,apps.AppointmentTypeId
              ,apps.AppointmentTypeParentId
              ,apps.AppointmentTypeName as AppointmentType
              ,apps.appointmentStatus
              ,apps.PracticeDoctorId as PracticeDoctorID
              ,apps.referralDoctorId
              ,apps.PatientRecordId
              ,apps.appTestID AS AppointmentTestId
              ,apps.starttime as TestDate
              ,apps.AppointmentTestStatusId AS TestStatusId
              ,apps.IsAbnormal AS IsAbnormal -- redmine #12567, cloned from svn 10717
              ,apps.SetForReview
              ,apps.ReassignDate
              ,apps.ReassignDocID
              ,apps.TestID
              ,apps.IsVP
              ,apps.PracDocDescr
			  ,apps.AppointmentPriorityId
          FROM @appointments apps
    )
    SELECT apps.officeid
          ,apps.practiceid
          ,apps.officeName
          ,apps.AppointmentId
          ,apps.AppointmentTypeId
          ,apps.AppointmentTypeParentId
          ,apps.AppointmentType
          ,apps.appointmentStatus
          ,apps.PracticeDoctorId
          ,IsNull(extDocs.lastName,'') +' '+IsNull(extDocs.firstName,'') AS DoctorName
          ,apps.referralDoctorId
          ,ISNULL((SELECT TOP 1 ext.firstName + ' ' + ext.lastName FROM ExternalDoctors ext WHERE ext.Id = apps.referralDoctorId),'') AS ReferralDoctor
          ,apps.PatientRecordId as PatienId
          ,demo.firstName AS PatientFirstName
          ,demo.lastName AS PatientLastName
          ,apps.AppointmentTestId
          ,tst.Id as TestId
          ,tst.[testShortName] as TestShortName
          ,apps.TestDate
          ,apps.TestStatusId
          ,ISNULL((SELECT TOP 1 [Status] FROM AppointmentTestStatus WHERE Id = apps.TestStatusId),'') AS TestStatus
          ,apps.IsAbnormal AS IsAbnormal -- redmine #12567, cloned from svn 10717
          ,apps.SetForReview
          ,apps.ReassignDate
          ,apps.ReassignDocID
          ,CASE
	          WHEN apps.ReassignDocID > 0 THEN (SELECT TOP 1 ext.firstName + ' ' + ext.lastName FROM ExternalDoctors ext JOIN PracticeDoctors pd ON ext.Id = pd.ExternalDoctorId WHERE pd.Id = apps.ReassignDocID)
	          ELSE ''
	      END AS ReassignDocName
          ,apps.IsVP
          ,CASE WHEN IsVP = 1 THEN -- VP
                   IsNull((select distinct case when qty_app = qty_sent and qty_app > 0 and qty_sent > 0 then [sent] else 2 end
                     from (select distinct sent, COUNT(*) over (partition by [appointmentid], [patientrecordid]) qty_app, COUNT(*) over (partition by [sent]) qty_sent
                             from VP_SendReport vp
                            where vp.appointmentid = apps.AppointmentId
                            and   vp.PatientRecordId = apps.PatientRecordId) r), 0)
                ELSE -- Worksheet
                   IsNull((select distinct case when qty_apt = qty_sent and qty_apt > 0 and qty_sent > 0 then [sent] else 2 end
                     from (select distinct sent, COUNT(*) over (partition by [appointmentid], [testid]) qty_apt, COUNT(*) over (partition by [sent]) qty_sent
                             from WS_SendReport ws
                            where ws.appointmentid = apps.AppointmentId
                            AND ws.SendTypeId <> 5-- DevOps 4312 - we want to ignore raw data regardless of configurations
                            and   ws.TestId = tst.Id) r), 0)
           END AS SentCompletely
		  ,appPrior.PriorityName
          ,CountTotalRecs.TotalRecords
        FROM AppTestResources apps
        JOIN [PracticeDoctors] pracDocs ON (apps.PracDocDescr = pracDocs.Id)
        JOIN [ExternalDoctors] extDocs ON (pracDocs.ExternalDoctorId = extDocs.Id )
        JOIN Demographics demo ON ( apps.PatientRecordId = demo.PatientRecordId )
        JOIN [Tests] as tst on ( apps.TestId=tst.Id )
		LEFT JOIN [AppointmentPriority] as appPrior on ( apps.AppointmentPriorityId=appPrior.Id )
        CROSS APPLY ( SELECT COUNT(*) as TotalRecords FROM AppTestResources ) CountTotalRecs
      ORDER BY apps.id
        OFFSET (@PageNum-1)*@PageSize ROWS
    FETCH NEXT @PageSize ROWS ONLY;

END