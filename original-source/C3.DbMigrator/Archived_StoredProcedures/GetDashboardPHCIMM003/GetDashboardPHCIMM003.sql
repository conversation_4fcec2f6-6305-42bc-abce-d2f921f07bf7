﻿CREATE PROCEDURE [dbo].[GetDashboardPHCIMM003] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @InfluenzaDocumented AS TABLE (PatientRecordId INT)
	DECLARE @InfluenzaRefused AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)


	-- Flu Season
	DECLARE @CurrentDate AS DATE,@FluStartDate AS DATE,@FluEndDate AS DATE
	SELECT @CurrentDate = GETDATE()	

	
	SELECT @FluStartDate = STR(IIF(MONTH(@CurrentDate)<10,YEAR(@CurrentDate)-1,YEAR(@CurrentDate))) + '-10-01'
	SELECT @FluEndDate = STR(IIF(MONTH(@CurrentDate)<10,YEAR(@CurrentDate),YEAR(@CurrentDate)+1)) + '-09-30'	



	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId		
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	AND DATEDIFF(YEAR,D.dateOfBirth,GETDATE()) >= 65 -- Active patients, 65 or older
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	-- Influenza documented between October 1 and January 31 in the current flu season 
	INSERT INTO @InfluenzaDocumented
	SELECT IM.PatientRecordId	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId in (1,24)							-- Type = Influenza
	AND ImmunizationDate BETWEEN @FluStartDate AND @FluEndDate
	UNION 
	SELECT PatientRecordId FROM BillDetails B					--  Q130A Tracking Code that has been billed for the patient
	WHERE B.serviceCode = 'Q130A'	
	AND b.serviceDate BETWEEN @FluStartDate AND @FluEndDate

	-- Influenza refused between October 1 and January 31 in the current flu season 
	INSERT INTO @InfluenzaRefused
	SELECT IM.PatientRecordId	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId in (1, 24)							-- Type = Influenza
	AND ImmunizationDate BETWEEN @FluStartDate AND @FluEndDate
	
	--- Segments	
		INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @InfluenzaDocumented)				

		INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @InfluenzaDocumented)	
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @InfluenzaRefused)
	
		INSERT INTO @Segments
	SELECT 3,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @InfluenzaDocumented)	
		AND PatientRecordId IN (SELECT PatientRecordId FROM @InfluenzaRefused)
		
		--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments	

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
