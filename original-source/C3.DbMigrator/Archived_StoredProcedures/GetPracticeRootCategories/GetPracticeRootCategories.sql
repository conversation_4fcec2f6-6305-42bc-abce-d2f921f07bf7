﻿CREATE PROCEDURE [dbo].[GetPracticeRootCategories]
	@groupId INT,	
	@practiceId INT,
	@practiceTemplateId INT,
	@rootCategoryId INT = 0	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here	
	SELECT	   
	   1000000 AS PracticeRootCategoryId
	  ,rc.CategoryName    
      ,1000000 AS DisplayOrder  
      ,rct.RootCategoryId
	  ,rc.GroupId
      ,pt.PracticeId	  
	  ,pt.Id AS PracRootCategoryTempId
	  ,rt.Id AS TemplateId
	  ,rt.TemplateName    
	  ,rc.IsActive  
  FROM 
  RootCategoryTemplates rct 
  JOIN RootTemplates rt ON rct.RootTemplateId = rt.Id
  JOIN PracticeRootCategoryTemplates pt ON rct.RootTemplateId = pt.TemplateId
  JOIN RootCategories rc ON rct.RootCategoryId = rc.Id
  WHERE rc.GroupId = @groupId
  AND rt.GroupId = @groupId
  AND pt.Id = @practiceTemplateId 
  AND pt.PracticeId = @practiceId
  AND pt.IsActive = 1  
  AND rc.Id = CASE WHEN @rootCategoryId > 0 THEN @rootCategoryId ELSE rct.RootCategoryId END  
  ORDER BY rc.CategoryName

END