﻿
-- =============================================
-- Author:		<Author,Divyesh>
-- Create date: <Create Date,2017-07-11>
-- Description:	<Description,Get Appointment not in waitlist or cancelled>
-- =============================================
CREATE PROCEDURE [dbo].[GetUnmappedLOINC]
  @PageNumber INT = 1,
  @PageSize   INT = 0,
  @p_rowcount int output
AS
BEGIN

    SET NOCOUNT ON;

    DECLARE @offset int;
	
	SET @offset = (@PageNumber-1) * @PageSize;

	IF @PageNumber = 1
	BEGIN
	   SELECT @p_rowcount = COUNT(*) 
	     FROM (SELECT DISTINCT hr.testCodeIdentifier as labCode, hr.testDescription as labTestName, id=0, m.sendingApp
				 FROM HL7Result hr JOIN HL7ReportVersion hrv on hr.HL7ReportVersionId = hrv.id JOIN 
				      HL7Message m on hrv.HL7MessageId = m.Id
				WHERE not exists ( select 1 from HL7Coding where labcode = hr.testCodeIdentifier)
			  ) z

	END;

    SELECT DISTINCT hr.testCodeIdentifier as labCode, hr.testDescription as labTestName, id=0, m.sendingApp
	  FROM HL7Result hr JOIN HL7ReportVersion hrv on hr.HL7ReportVersionId = hrv.id JOIN 
	       HL7Message m on hrv.HL7MessageId = m.Id
	 WHERE not exists ( select 1 from HL7Coding where labcode = hr.testCodeIdentifier)
  ORDER BY hr.testCodeIdentifier
    OFFSET @offset ROWS
FETCH NEXT @PageSize ROWS ONLY

END

