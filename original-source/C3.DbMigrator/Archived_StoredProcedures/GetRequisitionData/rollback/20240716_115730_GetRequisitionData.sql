﻿CREATE PROCEDURE [dbo].[GetRequisitionData]
	@practiceId int,
	@dateStart varchar(16),
	@dateEnd varchar(16),
	@practicedoctorIds varchar(512),
	@officeIds varchar(512),
	@requisitionTypeIds varchar(512),
	@requisitionStatusIds varchar(512),
	@rowStart int,
	@rowCount int,
	@totalRowRequest int,
	@sortByColumn varchar(32),
	@sortByOrder varchar(8)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @totalRow int
	DECLARE @params nvarchar(1000)
	DECLARE @sqlmain nvarchar(max)
	DECLARE @sqldetails nvarchar(4000)
	DECLARE @sqlmaincte nvarchar(4000)
	DECLARE @sqlreqPat nvarchar(4000)
	DECLARE @sqlreqPatInner nvarchar(1000)
	DECLARE @whereSqlMain varchar(512)
	DECLARE @whereSqlDetails varchar(512)
	DECLARE @orderBySql varchar(300)
	DECLARE @offsetFetchSql varchar(64)
	DECLARE @orderByColumn varchar(300)

	IF OBJECT_ID('tempdb..#requisitionPatients', 'U') IS NOT NULL
	BEGIN
		DROP TABLE #requisitionPatients
	END

	CREATE TABLE #requisitionPatients (
	    requisitionPatientId int not null,
		primary key clustered (requisitionPatientId)
	);

	IF OBJECT_ID('tempdb..#tempRequisitionData', 'U') IS NOT NULL
	BEGIN
		DROP TABLE #tempRequisitionData
	END

	CREATE TABLE #tempRequisitionData (
		requisitionPatientId int,
		appointmentId int,
		requisitionTime datetime2,
		patientRecordId int,
		practiceDoctorId int,
		doctorName varchar(100),
		officeId int,
		officeName varchar(100),
		status varchar(32),
		numRecs int,
		rowNum int
	)

	DECLARE @tempTestOrderedData TABLE (
		requisitionPatientId int,
		id int,
		[source] varchar(100),
		[name] varchar(100),
		testTime datetime2,
		[template] varchar(300),
		[status] int,
		[statusDescription] varchar(300),
		requisitionItems varchar(4000)
	)

	-- Set inverted order by to get the total count of the records
	SET @orderByColumn = (CASE WHEN @sortByColumn='appointment' THEN 'IsNull(f.appointmentTime, a.requisitionTime)'
								WHEN @sortByColumn='doctor' THEN '(case when d.lastName is null then '''' else UPPER(d.lastName) +'','' end)+ISNULL(d.firstName, '''')'
								WHEN @sortByColumn='office' THEN 'Isnull(e.name, '''')'
								ELSE 'IsNull(f.appointmentTime, a.requisitionTime)' END)
	SET @orderBySql = ' order by ' + @orderByColumn + (CASE WHEN @sortByOrder='asc' THEN ' desc' ELSE ' asc' END)

	SET @sqlmain = N';with reqs as (
	                 select a.id requisitionPatientId, 
	                    IsNuLL(f.id, 0) appointmentId, 
						IsNull(f.appointmentTime, a.requisitionTime) as requisitionTime,
						a.PatientRecordId,
						a.practiceDoctorId,
						(case when d.lastName is null then '''' else UPPER(d.lastName) +'','' end)+ISNULL(d.firstName, '''') doctorName, 
						ISNULL(f.officeId, 0) officeId,
						Isnull(e.name, '''') as OfficeName,
						'''' as status,'
						--Skips counter sort if total number of rows not requested
						+case when @totalRowRequest = 1 then 'row_number() over ('+@orderBySql+') as numRecs' else '0 as numRecs' end;

	-- Set now regular sort order
	SET @offsetFetchSql = ' offset @rowStart rows fetch next @rowCount rows only'
	SET @orderByColumn = (CASE WHEN @sortByColumn='appointment' THEN 'requisitionTime'
								WHEN @sortByColumn='doctor' THEN 'doctorName'
								WHEN @sortByColumn='office' THEN 'officeName'
								ELSE 'requisitionTime' END)
	SET @orderBySql = ' order by ' + @orderByColumn + (CASE WHEN @sortByOrder='asc' THEN ' asc' ELSE ' desc' END)

	--			    inner join #requisitionPatients rp on ( rp.requisitionPatientId = a.id)
	SET @sqlmain = @sqlmain +
               ' from RequisitionPatient a
				inner join PracticeDoctors c on a.practiceDoctorId=c.Id
				inner join ExternalDoctors d on c.ExternalDoctorId = d.id
				left join appointments f on f.Id=a.byAppointmentId
				left join office e on e.id = f.officeId'

	SET @sqlreqPat = 'SELECT o.requisitionPatientId FROM Requisition o WHERE o.isActive = 1 AND o.isActiveInSearch = 1 
					  AND exists ( select 1 from RequisitionPatient rp, PracticeDoctors pd 
					                 where rp.practiceDoctorId = pd.Id and pd.PracticeId = @practiceId and rp.id = o.requisitionPatientId '

	SET @sqldetails = N'select o.requisitionPatientId, o.id, ''requisition'' source, p.name, o.testTime, o.template, o.requisitionStatus status, q.text statusDescription, o.requisitionItems
						from Requisition o inner join RequisitionType p on p.Id=o.requisitionTypeId
						inner join RequisitionStatus q on q.Id=o.requisitionStatus
						where o.isActive=1 and o.isActiveInSearch=1'

    SET @whereSqlMain = ' where c.PracticeId = @practiceId and exists ( select 1 from #requisitionPatients rp where rp.requisitionPatientId = a.id ) '

	IF (@dateStart <> '')
		SET @whereSqlMain = @whereSqlMain + N' and ISNULL(f.appointmentTime, a.requisitionTime)>=@dateStart'
	IF (@dateEnd <> '')
		SET @whereSqlMain = @whereSqlMain + N' and ISNULL(f.appointmentTime, a.requisitionTime)<=@dateEnd'

	IF (@practicedoctorIds <> '')
	begin
        CREATE TABLE #tempPracticeDoctors(PracticeDoctorId int not null, primary key (PracticeDoctorId) )
		INSERT INTO #tempPracticeDoctors(PracticeDoctorId) select cast(value as int) from STRING_SPLIT(@practicedoctorIds, ',');
		SET @whereSqlMain = @whereSqlMain + N' and a.practiceDoctorId in (select PracticeDoctorID from #tempPracticeDoctors)'
		SET @sqlreqPat = @sqlreqPat + ' AND pd.id in (select PracticeDoctorID from #tempPracticeDoctors) ) '
	end;
	else SET @sqlreqPat = @sqlreqPat + ' )';

	IF (@officeIds <> '')
	begin
		CREATE TABLE #tempOffice(OfficeId int not null, primary key (officeId) )
		INSERT INTO #tempOffice(OfficeId) select cast(value as int) from STRING_SPLIT(@officeIds, ',');
		SET @whereSqlMain = @whereSqlMain + N' and f.officeId in (select OfficeId from #tempOffice)'
	end;

	IF (@requisitionTypeIds <> '')
	begin
		CREATE TABLE #tempRequisitionType(RequisitionTypeId int not null, primary key (RequisitionTypeId) )
		INSERT INTO #tempRequisitionType(RequisitionTypeId) select cast(value as int) from STRING_SPLIT(@requisitionTypeIds, ',');
		SET @sqldetails = @sqldetails + N' and o.requisitionTypeId in (select RequisitionTypeId from #tempRequisitionType)'
		SET @sqlreqPat = @sqlreqPat + N' AND o.requisitionTypeId in (select RequisitionTypeId from #tempRequisitionType)'
	end

	/* Add Requisition Status filtering - begin */
	SET @sqlreqPatInner = ' o.requisitionTypeId = 2 /*internal*/ '
	IF (@requisitionStatusIds = '') 
		SET @requisitionStatusIds = '1,2,3';
	ELSE
		-- Filters by internalTestStatus only when parameter @requisitionStatusIds is specified due the presence of NULL values in internalTestStatus column
		SET @sqlreqPatInner = @sqlreqPatInner + 'and o.internalTestStatus in (select RequisitionStatusIdVar from #tempRequisitionStatus) ';

	CREATE TABLE #tempRequisitionStatus(RequisitionStatusId int not null, RequisitionStatusIdVar nvarchar(32), primary key (RequisitionStatusId, RequisitionStatusIdVar));
	INSERT INTO #tempRequisitionStatus(RequisitionStatusId, RequisitionStatusIdVar) select cast(value as int), value from STRING_SPLIT(@requisitionStatusIds, ',');

	SET @whereSqlDetails = N'and (('+@sqlreqPatInner+') '
	SET @sqlreqPat = @sqlreqPat + N' and '+@sqlreqPatInner+N' UNION ALL '+@sqlreqPat

	SET @sqlreqPatInner = ' o.requisitionTypeId <> 2 /*not internal*/ and o.requisitionStatus in (select RequisitionStatusId from #tempRequisitionStatus) '
	SET @whereSqlDetails = @whereSqlDetails+ N' or ('+@sqlreqPatInner+') )'
	SET @sqlreqPat = @sqlreqPat + N' and '+@sqlreqPatInner

	/* Add Requisition Status filtering - end */

	SET @sqldetails = @sqldetails + @whereSqlDetails
	SET @sqlreqPat = 'SELECT distinct requisitionPatientId FROM ( '+ @sqlreqPat + ' ) r ORDER BY requisitionPatientId OPTION (RECOMPILE)'

	--Assembles main query footer, skipping counter sort if total number of rows not requested
	SET @sqlmaincte = '	) INSERT INTO #tempRequisitionData(requisitionPatientId,appointmentId,requisitionTime,patientRecordId,practiceDoctorId,doctorName,officeId,officeName,status,numRecs,rowNum)
	                      SELECT requisitionPatientId, appointmentId, requisitionTime, PatientRecordId, practiceDoctorId, doctorName, officeId, OfficeName, '''' status, numRecs,'
								+case when @totalRowRequest = 1 then 'row_number() over ('+@orderBySql+', numRecs DESC) as rowNum' else '0 as rowNum' end
						 +' FROM reqs '

    --Assembles main query
    SET @sqlmain = @sqlmain + @whereSqlMain + @sqlmaincte;

--	select @sqlreqPat

	--Populates #requisitionPatients table
	SET @params = N'@practiceId int, @dateStart datetime, @dateEnd datetime'
	--With Tablock option allows parallel processing in the temp table insert
	INSERT INTO #requisitionPatients WITH(TABLOCK) (requisitionPatientId) 
	EXEC sp_executesql @sqlreqPat, @params, @practiceId = @practiceId, @dateStart = @dateStart, @dateEnd = @dateEnd;

	--Refresh statistics of temp table for a more precise execution plan
	UPDATE Statistics #requisitionPatients;

	--Assemble main query, including pagination and sorting
	SET @sqlmain = @sqlmain + @orderBySql + @offsetFetchSql + ' OPTION (RECOMPILE) ';

--	select @sqlmain

	SET @params = N'@practiceId int, @dateStart datetime, @dateEnd datetime, @rowStart int, @rowCount int'

	EXEC sp_executesql @sqlmain, @params, @practiceId = @practiceId, @dateStart = @dateStart, @dateEnd = @dateEnd, @rowStart = @rowStart, @rowCount = @rowCount;

	-- Gets the total number of rows, if requested
	SET @totalRow = -1
	IF (@totalRowRequest = 1) SELECT @totalRow = numRecs FROM #tempRequisitionData WHERE rowNum = 1;

	IF (@totalRow <> 0)
	BEGIN
		SET @sqldetails = @sqldetails + ' and o.requisitionPatientId in (select requisitionPatientId from #tempRequisitionData)'
--		select @sqldetails
		insert into @tempTestOrderedData(requisitionPatientId,id,source,name,testTime,template,status,statusDescription,requisitionItems) 
		EXEC sp_executesql @sqldetails, @params, @practiceId = @practiceId, @dateStart = @dateStart, @dateEnd = @dateEnd, @rowStart = @rowStart, @rowCount = @rowCount;
	END

	select requisitionPatientId,appointmentId,requisitionTime,r.patientRecordId,(
			CASE 
				WHEN b.lastName IS NULL
					THEN ''
				ELSE UPPER(b.lastName) + ','
				END
			) + ISNULL(b.firstName, '') patientName,practiceDoctorId,doctorName,officeId,officeName,status 
	  from #tempRequisitionData r, Demographics b 
	 where r.PatientRecordId = b.PatientRecordId;

	select requisitionPatientId,id,source,name,testTime,template,status,statusDescription,requisitionItems from @tempTestOrderedData

	DROP TABLE #requisitionPatients;
	DROP TABLE #tempRequisitionData;

	IF OBJECT_ID('tempdb..#tempPracticeDoctors', 'U') IS NOT NULL
	BEGIN
		DROP TABLE #tempPracticeDoctors
	END

	IF OBJECT_ID('tempdb..#tempOffice', 'U') IS NOT NULL
	BEGIN
		DROP TABLE #tempOffice
	END

	IF OBJECT_ID('tempdb..#tempRequisitionType', 'U') IS NOT NULL
	BEGIN
		DROP TABLE #tempRequisitionType
	END

	IF OBJECT_ID('tempdb..#tempRequisitionStatus', 'U') IS NOT NULL
	BEGIN
		DROP TABLE #tempRequisitionStatus
	END	
	
	return @totalRow
END
GO