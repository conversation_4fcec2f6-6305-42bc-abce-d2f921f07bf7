﻿

CREATE PROCEDURE [dbo].[GetFormExternalDoctorInfo]
	@ExternalDoctorID INT
AS
BEGIN

	DECLARE @addressLine1 nvarchar(150);
	DECLARE @addressLine2 nvarchar(150);
	DECLARE @city nvarchar(100);
	DECLARE @postalCode nvarchar(10);
	DECLARE @province nvarchar(100);
	DECLARE @country nvarchar(100);

    SELECT TOP 1 @addressLine1 = da.addressLine1,
	       @addressLine2 = da.addressLine2,
		   @city = da.city,
		   @postalcode = da.postalCode,
  		   @province = da.province,
		   @country = da.country
	FROM ExternalDoctorAddresses da
    WHERE da.ExternalDoctorId = @ExternalDoctorID
    ORDER BY da.id DESC;

	SELECT isnull(ed.firstName, '') as FirstName
	    ,isnull(ed.lastName, '') as LastName
	    ,ed.OHIPPhysicianId
		,ed.emailAddress
		,ed.CPSO
		,@addressLine1 as AddressLine1
		,@addressLine2 as AddressLine2
		,@city as City
		,@postalCode as PostalCode
		,@province as Province
		,@country as Country
/*        ,(SELECT STUFF(  
				(
				SELECT ', '+ CAST(edpn.phoneNumber AS VARCHAR(500))
				FROM ExternalDoctorPhoneNumbers edpn
				WHERE phoneNumber IS NOT NULL AND ltrim(rtrim(phoneNumber)) <> ''
				AND   edpn.ExternalDoctorId = ed.id
			ORDER BY  Id DESC
			FOR XMl PATH('') 
				),1,1,''  )) as DocPhoneList -- Comment on Nov 30, 2018 as per Galina's request */
        ,'' as DocPhoneList
        ,(SELECT STUFF(  
				(
				SELECT ', '+ CAST(edpn.faxNumber AS VARCHAR(500))
				FROM ExternalDoctorPhoneNumbers edpn
				WHERE faxNumber IS NOT NULL AND ltrim(rtrim(FaxNumber)) <> ''
				AND   edpn.ExternalDoctorId = ed.id
			ORDER BY  Id DESC
			FOR XMl PATH('') 
				),1,1,''  )) as DocFaxList
	  FROM ExternalDoctors as ed
     WHERE ed.id = @ExternalDoctorID;

END;
