﻿
CREATE   PROCEDURE [dbo].[GetPracticeScheduledUsers] 
	@practiceId INT,
	@officeId INT = NULL,
	@userId INT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	
	DECLARE @filterCount INT = 0;

	DECLARE @tblOffices TABLE -- US 2312 search all Offices when Practice is null
	(
		OfficeId INT
	);
	IF @officeId IS NULL INSERT INTO @tblOffices SELECT Id FROM Office WHERE PracticeId = @practiceId
	ELSE INSERT INTO @tblOffices SELECT @officeId;

    DECLARE @tblActiveUserFilters TABLE -- for the user schedule view filters
	( 
        UserFilterId INT -- the user id from the users that the current user would like to see
	);
	INSERT INTO @tblActiveUserFilters(UserFilterId)
	SELECT uf.UserFilterId FROM UserScheduleViewFilters uf 
	WHERE uf.UserId = @userId 
	AND uf.IsActive = 1
	AND uf.OfficeId IN (SELECT OfficeId FROM @tblOffices);
		
	SET @filterCount = ISNULL((SELECT COUNT(UserFilterId) FROM @tblActiveUserFilters),0); 

	IF @userId > 0 AND @filterCount > 0
	BEGIN
		
		DECLARE @tblUserDoctorFilters dbo.IntegerList;	-- for filter practice doctor user ids
		DECLARE @tblUserTechFilters dbo.IntegerList;	-- for filter techs user ids

		INSERT INTO @tblUserTechFilters(IntegerValue)
		SELECT DISTINCT u.UserID FROM @tblActiveUserFilters uf 
		JOIN AspNetUsers u ON uf.UserFilterId = u.UserID
		JOIN dbo.fn_GetTechnicianTypes() tp ON u.CerebrumUserType = tp.Id				

		INSERT INTO @tblUserDoctorFilters(IntegerValue)
		SELECT DISTINCT u.UserID FROM @tblActiveUserFilters uf 
		JOIN AspNetUsers u ON uf.UserFilterId = u.UserID
		JOIN PracticeDoctors pd ON u.Id = pd.ApplicationUserId
		WHERE u.CerebrumUserType = 5 -- means a doctor
		AND pd.ApplicationUserId IS NOT NULL
				
		SELECT 
		su.PracticeId
		,ISNULL(pd.Id,0) AS PracticeDoctorId
		,su.UserId
		,u.Id AS ApplicationUserId
		,u.UserName
		,u.FirstName
		,u.LastName
		,u.MiddleName
		,u.CerebrumUserType
		,u.[Status]
		,su.startDate AS StartDate
		,su.endDate AS EndDate
		,su.updatedDate		
        ,su.permission as PermissionIds 
		FROM ScheduleUsers su
		JOIN AspNetUsers u ON su.UserId = u.UserId		
		JOIN UserOffices UO ON u.Id=UO.ApplicationUserId AND UO.OfficeId=@officeId
		LEFT JOIN PracticeDoctors pd ON (u.Id = pd.ApplicationUserId AND pd.PracticeId=@practiceId)
		WHERE su.PracticeId = @practiceId 
		AND u.[Status] = 0
        AND u.PracticeID = su.PracticeId
		AND (su.endDate IS NULL OR su.endDate>GETDATE())
		AND exists ( select 1 from ScheduleWeekDays sw 
					   where su.Id = sw.ScheduleUserId
					   AND sw.officeId IN (SELECT OfficeId FROM @tblOffices) )
		AND   1 = 
	   CASE 		
	    WHEN u.UserId IN (SELECT Integervalue FROM @tblUserDoctorFilters) THEN 1
		WHEN u.UserId IN (SELECT Integervalue FROM @tblUserTechFilters) THEN 1 		
		WHEN u.UserId NOT IN (SELECT Integervalue FROM @tblUserDoctorFilters) AND NOT EXISTS(SELECT * FROM @tblUserTechFilters) AND u.CerebrumUserType IN (SELECT Id FROM fn_GetTechnicianTypes()) THEN 1			   
		ELSE 0
		END
	END
	ELSE
	BEGIN
		SELECT 
		su.PracticeId
		,ISNULL(pd.Id,0) AS PracticeDoctorId
		,su.UserId
		,u.Id AS ApplicationUserId
		,u.UserName
		,u.FirstName
		,u.LastName
		,u.MiddleName
		,u.CerebrumUserType
		,u.[Status]
		,su.startDate AS StartDate
		,su.endDate AS EndDate
		,su.updatedDate		
        ,su.permission as PermissionIds 
		FROM ScheduleUsers su
		JOIN AspNetUsers u ON su.UserId = u.UserId
		JOIN UserOffices UO ON u.Id=UO.ApplicationUserId AND UO.OfficeId=@officeId
		LEFT JOIN PracticeDoctors pd ON (u.Id = pd.ApplicationUserId AND pd.PracticeId=@practiceId)
		WHERE su.PracticeId = @practiceId 
		AND u.[Status] = 0
        AND u.PracticeID = su.PracticeId
		AND (su.endDate IS NULL OR su.endDate>GETDATE())
		AND exists ( select 1 from ScheduleWeekDays sw 
					   where su.Id = sw.ScheduleUserId
					   AND sw.officeId IN (SELECT OfficeId FROM @tblOffices) )
	END
	
END