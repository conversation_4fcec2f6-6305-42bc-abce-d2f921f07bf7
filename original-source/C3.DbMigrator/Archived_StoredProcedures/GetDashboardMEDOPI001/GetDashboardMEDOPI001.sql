﻿
CREATE PROCEDURE [dbo].[GetDashboardMEDOPI001] (@PracticeDoctorId INT = NULL)
AS
BEGIN	
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @CurrentOpioid AS TABLE (PatientRecordId INT)	
	DECLARE @PastOpioid AS TABLE (PatientRecordId INT) 
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE 
	D.active = 0 -- Patient is active
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	--Non-Paliative 
	AND NOT EXISTS (SELECT 1 FROM BillDetails B WHERE B.PatientRecordId = D.PatientRecordId
					AND ( B.serviceCode like 'A945%' 
					or B.serviceCode like 'C945%' 
					or B.serviceCode like 'C882%'
					or B.serviceCode like 'C982%'
					or B.serviceCode like 'W872%'
					or B.serviceCode like 'W882%'
					or B.serviceCode like 'W972%'
					or B.serviceCode like 'W982%'
					or B.serviceCode like 'K023%'
					or B.serviceCode like 'B998%'
					or B.serviceCode like 'B966%'
					or B.serviceCode like 'B997%'
					or B.serviceCode like 'G511%'
					or B.serviceCode like 'G512%'))
	AND NOT EXISTS (SELECT 1 FROM VP_CPP_Problem_List CPP WHERE CPP.PatientRecordId = D.PatientRecordId
					AND CPP.DiagnosticCode = 'Z515')
	GROUP BY D.PatientRecordId

	--- Current opioid prescription
	INSERT INTO @CurrentOpioid
	SELECT bp.PatientRecordId	
	FROM @BasePopulation bp
	join PatientMedications M on m.PatientRecordId = bp.PatientRecordId 
	WHERE 
	(
	MedicationName like '%Alfentanil%' or 
	MedicationName like '%Buprenorphin%' or 
	MedicationName like '%Butorphanol%' or 
	MedicationName like '%Codeine%' or 
	MedicationName like '%Dihydrocodeine%' or 
	MedicationName like '%Fentanyl%' or 
	MedicationName like '%Hydrocodone%' or 
	MedicationName like '%Meperidin%' or 
	MedicationName like '%Hydromorphone%' or 
	MedicationName like '%Methadone%' or 
	MedicationName like '%Morphine%' or 
	MedicationName like '%Nalbuphine%' or 
	MedicationName like '%Opium%' or 
	MedicationName like '%Oxycodone%' or 
	MedicationName like '%Oxymorphone%' or 
	MedicationName like '%Pentazocine%' or 
	MedicationName like '%Pethidine%' or 
	MedicationName like '%Remifentanil%' or 
	MedicationName like '%Sufentanil%' or 
	MedicationName like '%Tapentadol%' or 
	MedicationName like '%Tramadol%'
	or m.Ingredients like '%Alfentanil%' 
	OR m.Ingredients like '%Buprenorphin%'
	OR m.Ingredients like '%Butorphanol%'
	OR m.Ingredients like '%Codeine%'
	OR m.Ingredients like '%Dihydrocodeine%'
	OR m.Ingredients like '%Fentanyl%'
	OR m.Ingredients like '%Hydrocodone%'
	OR m.Ingredients like '%Meperidine%'
	OR m.Ingredients like '%Hydromorphone%'
	OR m.Ingredients like '%Methadone%'
	OR m.Ingredients like '%Morphine%'
	OR m.Ingredients like '%Nalbuphine%'
	OR m.Ingredients like '%Opium%'
	OR m.Ingredients like '%Oxycodone%'
	OR m.Ingredients like '%Oxymorphone%'
	OR m.Ingredients like '%Pentazocine%'
	OR m.Ingredients like '%Pethidine%'
	OR m.Ingredients like '%Remifentanil%'
	OR m.Ingredients like '%Sufentanil%'
	OR m.Ingredients like '%Tapentadol%' 
	OR m.Ingredients like '%Tramadol%'
	)
	AND m.IsActive = 1
	AND (m.DateExpired IS NULL AND m.DateDiscontinued IS NULL)

	--- Past opioid prescription
	INSERT INTO @PastOpioid
	SELECT bp.PatientRecordId	
	FROM @BasePopulation bp
	JOIN PatientMedications M on m.PatientRecordId = bp.PatientRecordId 
	WHERE 
	(
	MedicationName like '%Alfentanil%' or 
	MedicationName like '%Buprenorphin%' or 
	MedicationName like '%Butorphanol%' or 
	MedicationName like '%Codeine%' or 
	MedicationName like '%Dihydrocodeine%' or 
	MedicationName like '%Fentanyl%' or 
	MedicationName like '%Hydrocodone%' or 
	MedicationName like '%Meperidin%' or 
	MedicationName like '%Hydromorphone%' or 
	MedicationName like '%Methadone%' or 
	MedicationName like '%Morphine%' or 
	MedicationName like '%Nalbuphine%' or 
	MedicationName like '%Opium%' or 
	MedicationName like '%Oxycodone%' or 
	MedicationName like '%Oxymorphone%' or 
	MedicationName like '%Pentazocine%' or 
	MedicationName like '%Pethidine%' or 
	MedicationName like '%Remifentanil%' or 
	MedicationName like '%Sufentanil%' or 
	MedicationName like '%Tapentadol%' or 
	MedicationName like '%Tramadol%'
	or m.Ingredients like '%Alfentanil%' 
	OR m.Ingredients like '%Buprenorphin%'
	OR m.Ingredients like '%Butorphanol%'
	OR m.Ingredients like '%Codeine%'
	OR m.Ingredients like '%Dihydrocodeine%'
	OR m.Ingredients like '%Fentanyl%'
	OR m.Ingredients like '%Hydrocodone%'
	OR m.Ingredients like '%Meperidine%'
	OR m.Ingredients like '%Hydromorphone%'
	OR m.Ingredients like '%Methadone%'
	OR m.Ingredients like '%Morphine%'
	OR m.Ingredients like '%Nalbuphine%'
	OR m.Ingredients like '%Opium%'
	OR m.Ingredients like '%Oxycodone%'
	OR m.Ingredients like '%Oxymorphone%'
	OR m.Ingredients like '%Pentazocine%'
	OR m.Ingredients like '%Pethidine%'
	OR m.Ingredients like '%Remifentanil%'
	OR m.Ingredients like '%Sufentanil%'
	OR m.Ingredients like '%Tapentadol%' 
	OR m.Ingredients like '%Tramadol%'
	)
	AND m.IsActive = 1
	AND (m.DateExpired IS NOT NULL OR m.DateDiscontinued IS NOT NULL)
	
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE
		PatientRecordId IN (SELECT PatientRecordId FROM @CurrentOpioid)
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @CurrentOpioid)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PastOpioid)
	
	INSERT INTO @Segments
	SELECT 3,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @CurrentOpioid)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PastOpioid)		
	
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log
END


