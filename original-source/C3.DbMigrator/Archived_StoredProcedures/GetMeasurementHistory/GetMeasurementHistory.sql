﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetMeasurementHistory] 
	@measurementId INT,
	@appointmentId INT,
	@testId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

   DECLARE @patientId INT; 
   DECLARE @appDate DATETIME;

   SELECT TOP 1 @patientId = PatientRecordId, @appDate = appointmentTime FROM Appointments WHERE Id = @appointmentId;	

   DECLARE @includedAppStatuses TABLE
	( 
        appointmentStatus int 		 
	);
	


	DECLARE @previousAppsTests TABLE
	( 
        AppointmentId int,
		AppointmentDate DATETIME,	
		AppTestId int,
		TestDate DATETIME,	 
		TestId int
		 
	);

	INSERT INTO @includedAppStatuses VALUES (2); -- booked
	INSERT INTO @includedAppStatuses VALUES (3); -- notarrived	
	INSERT INTO @includedAppStatuses VALUES (4); -- arrived
	INSERT INTO @includedAppStatuses VALUES (5); -- left
	INSERT INTO @includedAppStatuses VALUES (8); -- doingtests
	INSERT INTO @includedAppStatuses VALUES (9); -- readyfordoctor
	INSERT INTO @includedAppStatuses VALUES (10); -- ordersreadytotranscribe
	INSERT INTO @includedAppStatuses VALUES (11); -- ordersdone
	INSERT INTO @includedAppStatuses VALUES (12); -- PatientLeftAndOrdersDone
	INSERT INTO @includedAppStatuses VALUES (13); -- OrdersToBeDone
	INSERT INTO @includedAppStatuses VALUES (14); -- FlowSheetCompleted
	INSERT INTO @includedAppStatuses VALUES (15); -- ReadyToLeave	
	
	

	INSERT INTO @previousAppsTests(AppointmentId,AppointmentDate,AppTestId,TestDate,TestId)
	SELECT apps.Id, apps.appointmentTime, apptests.Id,apptests.startTime, apptests.TestId
	FROM AppointmentTests apptests
	JOIN Appointments apps ON apps.Id = apptests.AppointmentId
	WHERE apps.PatientRecordId = @patientId AND apps.appointmentTime <= @appDate
	AND apps.appointmentStatus IN (SELECT appointmentStatus FROM @includedAppStatuses)
	AND apptests.TestId = @testId
	AND apps.IsActive = 1 
	AND apptests.IsActive = 1

	SELECT 
	prt.AppointmentId,
	prt.AppointmentDate,
	prt.AppTestId,
	prt.TestDate,
	prt.TestId,
	(SELECT TOP 1 [name] FROM Measurements WHERE Id = @measurementId) AS MeasurementName,
	(SELECT TOP 1 s.[Value] FROM MeasurementSavedValues s JOIN AppointmentTestLogs l ON s.AppointmentTestLogID = l.Id
	WHERE s.MeasurementId = @measurementId ANd l.AppointmentID = prt.AppointmentId ORDER By l.[Date] DESC) AS MeasurementValue
	FROM @previousAppsTests prt
	ORDER BY prt.TestDate DESC
	--JOIN MeasurementSavedValues ms ON ms.Id = (
	--SELECT TOP 1 Id FROM MeasurementSavedValues s JOIN AppointmentTestLogs l ON s.AppointmentTestLogID = l.Id
	--WHERE s.MeasurementId = @measurementId ANd l.AppointmentID = prt.AppointmentId ORDER By l.[Date] DESC)
	
	
END
