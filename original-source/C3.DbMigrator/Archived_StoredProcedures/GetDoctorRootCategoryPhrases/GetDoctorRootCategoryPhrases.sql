﻿CREATE PROCEDURE [dbo].[GetDoctorRootCategoryPhrases]
	@groupId INT,
	@externalDoctorId INT,
	@practiceId INT,
	@practiceTemplateId INT,
	@rootCategoryId INT = 0	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @tblPhrases table
	(
		PracRootCategoryPhraseId int NOT NULL,
		PracRootCategoryTempId int NOT NULL,
		RootCategoryPhraseId int NOT NULL,
		PhraseName nvarchar(500) null,
		PhraseValue nvarchar(max),
		IsVisible bit not null,
		DisplayOrder int not null,
		ParentId int NULL,	
		RootCategoryId int not null,
		GroupId int not null,
		IsSubCategory bit not null,
		IsActiceForDoctor bit not null,
		primary key (PracRootCategoryPhraseId)
	)

	INSERT INTO @tblPhrases
	SELECT 
	prcp.Id
	,prcp.PracRootCategoryTempId
	,prcp.RootCategoryPhraseId    
	,CAST((CASE WHEN drp.PhraseName IS NULL THEN rcp.PhraseName ELSE drp.PhraseName END) AS nvarchar(500)) AS PhraseName
	,CAST((CASE WHEN drp.PhraseValue IS NULL THEN rcp.PhraseValue ELSE drp.PhraseValue END) AS nvarchar(Max)) AS PhraseValue
	,CAST((CASE WHEN drp.IsVisible IS NULL THEN 1 ELSE drp.IsVisible END) AS BIT) AS IsVisible	
	,ISNULL(drp.DisplayOrder,1000000)
	,rcp.ParentId	
	,rc.Id
	,rc.GroupId
	,rcp.IsSubCategory
	,CAST((CASE WHEN drp.IsActive IS NULL THEN 1 ELSE drp.IsActive END) AS BIT)
	FROM PracticeRootCategoryPhrases prcp
	JOIN RootCategoryPhrases rcp ON prcp.RootCategoryPhraseId = rcp.Id
	JOIN RootCategories rc ON rcp.RootCategoryId = rc.Id
	JOIN DoctorRootCategoryPhrases drp ON drp.RootCategoryPhraseId = prcp.RootCategoryPhraseId AND drp.PracRootCategoryTempId = @practiceTemplateId 
	AND drp.ExternalDoctorId = @externalDoctorId
	WHERE prcp.PracRootCategoryTempId = @practiceTemplateId	
	AND prcp.IsActive = 1
	AND rc.GroupId = @groupId;

	WITH CustomPhraseCTE
	(
		 PracRootCatPhraseId
		,RootCategoryPhraseId
		,PracRootCategoryTempId
		,RootTemplateId
		,RootCategoryId
		,CategoryName		
		,GroupId
		,PracticeId
		,ExternalDoctorId
		,IsVisibleToolbar
		,IsVisible
		,PhraseName
		,PhraseValue
		,ParentId				
		,CategoryDisplayOrder
		,PhraseDisplayOrder
		,Breadcrum 
		,IsSubCategory
		,[Level]					
	)
	AS(
		SELECT 
		 phrases.PracRootCategoryPhraseId
		,phrases.RootCategoryPhraseId
		,drc.PracRootCategoryTempId
		,drc.TemplateId AS RootTemplateId
		,drc.RootCategoryId
		,CASE WHEN drc.CategoryNameCustom IS NULL THEN drc.CategoryNameOriginal ELSE drc.CategoryNameCustom END
		,drc.GroupId
		,drc.PracticeId
		,drc.ExternalDoctorId
		,drc.IsVisibleToolbar
		,phrases.IsVisible
		,phrases.PhraseName
		,phrases.PhraseValue
		,phrases.ParentId		
		,ISNULL(drc.DisplayOrder,1000000)
		,phrases.DisplayOrder
		,CAST((CASE WHEN drc.CategoryNameCustom IS NULL THEN drc.CategoryNameOriginal ELSE drc.CategoryNameCustom END) + ' -> '+
		(phrases.PhraseName) AS nvarchar(2000))
		,phrases.IsSubCategory
		,0 	
		FROM @tblPhrases phrases
		JOIN fn_GetDoctorRootCategories(@groupId,@externalDoctorId,@practiceId,@practiceTemplateId,@rootCategoryId) drc ON phrases.PracRootCategoryTempId = drc.PracRootCategoryTempId					
		WHERE drc.IsVisible = 1
		AND phrases.IsVisible = 1
		AND phrases.ParentId IS NULL
		AND phrases.RootCategoryId = drc.RootCategoryId
		AND phrases.IsActiceForDoctor = 1
		UNION ALL
	--recursive select
		SELECT 
		 phrases.PracRootCategoryPhraseId
		,phrases.RootCategoryPhraseId
		,cte.PracRootCategoryTempId
		,cte.RootTemplateId
		,cte.RootCategoryId
		,cte.CategoryName		
		,cte.GroupId
		,cte.PracticeId
		,cte.ExternalDoctorId
		,cte.IsVisibleToolbar
		,phrases.IsVisible
		,phrases.PhraseName
		,phrases.PhraseValue
		,phrases.ParentId			
		,cte.CategoryDisplayOrder
		,phrases.DisplayOrder
		,CAST(cte.Breadcrum + ' -> '+phrases.PhraseName AS nvarchar(2000))
		,phrases.IsSubCategory
		,cte.[level] + 1						
		FROM @tblPhrases phrases
		JOIN CustomPhraseCTE cte ON phrases.ParentId = cte.RootCategoryPhraseId				
		WHERE phrases.IsVisible = 1	AND cte.RootCategoryId = phrases.RootCategoryId
		AND phrases.IsActiceForDoctor = 1
	)
	SELECT 
	cte.PracRootCatPhraseId
	,cte.RootCategoryPhraseId
	,cte.PracRootCategoryTempId
	,cte.RootTemplateId
	,cte.RootCategoryId
	,cte.CategoryName		
	,cte.GroupId
	,cte.PracticeId
	,cte.ExternalDoctorId
	,cte.IsVisibleToolbar
	,cte.IsVisible
	,cte.PhraseName
	,cte.PhraseValue
	,ISNULL(cte.ParentId,-1) AS ParentId	
	,cte.CategoryDisplayOrder
	,cte.PhraseDisplayOrder
	,cte.Breadcrum
	,cte.IsSubCategory
	,cte.[Level]					
	FROM CustomPhraseCTE cte 
	WHERE cte.IsVisible = 1
	ORDER BY cte.CategoryDisplayOrder ASC, cte.PhraseDisplayOrder ASC, cte.CategoryName ASC, cte.PhraseName ASC
		
END
