﻿CREATE PROCEDURE [dbo].[GetReportSaveType] 
	@appointmentTestId INT,
	@appointmentTestLogId INT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @saveTypeId INT;
	DECLAR<PERSON> @saveType nvarchar(100);
	<PERSON><PERSON>AR<PERSON> @logDate datetime2;
	

	DECLARE @saveTypes TABLE
	(	
		Id int NOT NULL,
		SaveType nvarchar(100) NULL    
	)

	INSERT INTO @saveTypes(Id,SaveType)VALUES(1,'Save');
	INSERT INTO @saveTypes(Id,SaveType)VALUES(2,'SaveAndChangeStatus');
	INSERT INTO @saveTypes(Id,SaveType)VALUES(3,'SendLetter');
	INSERT INTO @saveTypes(Id,SaveType)VALUES(4,'FinalizeNote');
	INSERT INTO @saveTypes(Id,SaveType)VALUES(5,'Preview');

	IF @appointmentTestLogId = 0
	BEGIN
		SELECT TOP 1 @appointmentTestLogId = ISNULL(sl.Id,0),
		@saveTypeId = ISNULL(sl.SaveType,'')
		FROM AppointmentTestSaveLogs sl
		WHERE sl.AppointmentTestId = @appointmentTestId
		ORDER By sl.Id DESC
	END
	ELSE
	BEGIN
		SELECT TOP 1 @appointmentTestLogId = ISNULL(sl.Id,0),
		@saveTypeId = ISNULL(sl.SaveType,'')
		FROM AppointmentTestSaveLogs sl
		WHERE sl.AppointmentTestId = @appointmentTestId
		AND sl.Id = @appointmentTestLogId
		ORDER By sl.Id DESC
	END;

	SET @saveType = ISNULL((SELECT TOP 1 SaveType FROM @saveTypes WHERE Id = @saveTypeId),1);

    -- Insert statements for procedure here
	SELECT @saveTypeId AS SaveTypeId, @saveType AS SaveTypeDescription
END