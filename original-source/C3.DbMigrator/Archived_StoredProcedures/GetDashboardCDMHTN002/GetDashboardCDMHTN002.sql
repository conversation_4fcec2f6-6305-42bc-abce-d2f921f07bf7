﻿CREATE PROCEDURE [dbo].[GetDashboardCDMHTN002] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @BPDocumented AS TABLE (PatientRecordId INT)
	DECLARE @GlucoseOrHbA1c AS TABLE (PatientRecordId INT,testCodeIdentifier NVARCHAR(300),testResultFloat FLOAT, collectionDate DATETIME2, id INT )
	DECLARE @HTNCoded AS TABLE (PatientRecordId INT)
	DECLARE @ObesityDocumented AS TABLE (PatientRecordId INT,Name NVARCHAR(300))
	DECLARE @TestsComplete AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)
	
	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) >= 18	-- Patient is 18 and over		
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	--- HTN Coded
	INSERT INTO @HTNCoded
	SELECT PatientRecordId	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('401', '402', '403', '404', '405'							
							,'D3-02120','D3-02010','D3-02100','D3-020000'
							, '1201005', '10725009', '59621000', '38341003')
							OR CPP.DiagnosticCode LIKE 'I10%')
	AND Deleted = 0

	--- Blood Pressure
	INSERT INTO @BPDocumented
	SELECT BP.PatientRecordId	
	FROM VPMeasurementSavedValues VPV
	JOIN VPUniqueMeasurements VPM on VPV.VPUniqueMeasurementId = VPM.ID
	JOIN AppointmentTestSaveLogs ATSL ON VPV.AppointmentTestSaveLogId = ATSL.Id
	JOIN AppointmentTests APPT ON APPT.Id = ATSL.AppointmentTestId
	JOIN Appointments APP ON APP.Id = APPT.AppointmentId
	JOIN @BasePopulation BP ON BP.PatientRecordId = APP.PatientRecordId -- Base Population
	WHERE DATEDIFF(MONTH,APP.appointmentTime,GETDATE()) <= 12 -- Appointments in the last year
	AND APP.appointmentTime <= GETDATE() -- No future appointments
	AND APPT.TestId = 29 -- VP Only
	AND APPT.IsActive = 1 -- Not Canceled
	AND ATSL.IsActive = 1 AND APPT.IsActive = 1
	AND
	VPM.Name LIKE '%BP%'

	--select * from #BPDocumented

	--- Blood Glucose or HbA1c 
	INSERT INTO @GlucoseOrHbA1c
	SELECT BP.PatientRecordId , r.testCodeIdentifier,  r.testResultFloat, r.collectionDate, r.id     
    FROM @BasePopulation BP
	JOIN HL7Patient P ON BP.PatientRecordId = P.PatientRecordId
    JOIN HL7Report RPT ON RPT.HL7PatientId = P.Id
    JOIN HL7ReportVersion V ON V.HL7ReportId = RPT.ID
    JOIN HL7Result R on R.HL7ReportVersionId = V.Id
	WHERE 
	--CONTAINS(testCodeIdentifier,'"hba1c" OR "gluc"')	
	(testCodeIdentifier LIKE '%hba1c%' OR testCodeIdentifier LIKE '%gluc%')
    AND R.collectionDate > DATEADD(MONTH,-12,GETDATE())
    AND r.resultStatus in ('F', 'A', 'C')
	--GROUP BY P.PatientRecordId

	--select * from #GlucoseOrHbA1c

	--- Obesity Documented
	INSERT INTO @ObesityDocumented
	SELECT BP.PatientRecordId, vpm.Name	
	FROM VPMeasurementSavedValues VPV
	JOIN VPUniqueMeasurements VPM on VPV.VPUniqueMeasurementId = VPM.ID
	JOIN AppointmentTestSaveLogs ATSL ON VPV.AppointmentTestSaveLogId = ATSL.Id
	JOIN AppointmentTests APPT ON APPT.Id = ATSL.AppointmentTestId
	JOIN Appointments APP ON APP.Id = APPT.AppointmentId
	JOIN @BasePopulation BP ON BP.PatientRecordId = APP.PatientRecordId -- Base Population
	WHERE DATEDIFF(MONTH,APP.appointmentTime,GETDATE()) <= 12 -- Appointments in the last year
	AND APP.appointmentTime <= GETDATE() -- No future appointments
	AND APPT.TestId = 29 -- VP Only
	AND APPT.IsActive = 1 -- Not Canceled
	AND ATSL.IsActive = 1 AND APPT.IsActive = 1
	AND
	(VPM.Name LIKE '%BMI%' OR VPM.Name LIKE '%Height%' OR VPM.Name LIKE '%Weight%' OR VPM.Name LIKE '%Waist%')

	-- define completed suite of tests
		INSERT INTO @TestsComplete
		SELECT bp.PatientRecordId		
		FROM @BasePopulation bp
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @HTNCoded)	  -- pt must be cad coded
		AND (PatientRecordId IN (SELECT PatientRecordId FROM @BPDocumented)	-- pt must have at least one BP
		-- Pt must have at least one HbA1c or glucose
		or PatientRecordId IN (SELECT PatientRecordId FROM @GlucoseOrHbA1c)
		-- pt must have a bmi, or a height and weight or a height and waist circumferance
		or (	(PatientRecordId IN (SELECT PatientRecordId FROM @ObesityDocumented where Name LIKE '%BMI%'))
				or
				(PatientRecordId IN (SELECT PatientRecordId FROM @ObesityDocumented where Name LIKE '%Height%')
				and (PatientRecordId IN (SELECT PatientRecordId FROM @ObesityDocumented where Name LIKE '%Weight%')
						 or PatientRecordId IN (SELECT PatientRecordId FROM @ObesityDocumented where Name LIKE '%Waist%')
					)
				)
			)
			)
-- select * from #testscomplete

	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @HTNCoded)		
		AND PatientRecordId IN (SELECT PatientRecordId FROM @testscomplete	)

	INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @HTNCoded)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @testscomplete)

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments	

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log
END
