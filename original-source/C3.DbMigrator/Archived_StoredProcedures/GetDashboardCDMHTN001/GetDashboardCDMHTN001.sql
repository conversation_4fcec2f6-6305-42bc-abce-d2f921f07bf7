﻿CREATE PROCEDURE [dbo].[GetDashboardCDMHTN001] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @HTNCoded AS TABLE (PatientRecordId INT)
	DECLARE @HTNDocumented AS TABLE (PatientRecordId INT)
	DECLARE @Bills AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) >= 18	-- Patient is 18 and over		
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId)-- OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	--- HTN Coded
	INSERT INTO @HTNCoded
	SELECT PatientRecordId	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('401', '402', '403', '404', '405'
							,'I10'
							,'D3-02120','D3-02010','D3-02100','D3-020000'
							, '1201005', '10725009', '59621000', '38341003')
							OR CPP.DiagnosticCode LIKE 'I10%')
	AND Deleted = 0

	--- HTN Documented
	INSERT INTO @HTNDocumented
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   				
		CPP.Problem_Description LIKE '%HTN%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%HPT%'
		OR CPP.Problem_Description LIKE '%Hypertensive%'
		OR CPP.Problem_Description LIKE '%Hypertension%'		
		UNION ALL
		SELECT PatientRecordId 
		FROM VP_CPP_RiskFactor CPP
		WHERE 			   								 
		CPP.RiskFactor LIKE '%HTN%'					-- Excluded Text Snippets
		OR CPP.RiskFactor LIKE '%HPT%'
		OR CPP.RiskFactor LIKE '%Hypertensive%'
		OR CPP.RiskFactor LIKE '%Hypertension%'	
	AND Deleted = 0
	--- Consider HTN: Dx in 2+ bills
	-- Fixed Alex 2021-03-17
	INSERT INTO @Bills
	SELECT PatientRecordId 
	--INTO #Bills
	FROM BillDetails B
	WHERE B.diagnoseCode IN ('401','402','403')
	GROUP BY PatientRecordId
	HAVING COUNT(B.id)>=2
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @HTNCoded)		

	INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @HTNCoded)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @HTNDocumented)
	
	INSERT INTO @Segments
	SELECT 3,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @HTNCoded)		
		AND PatientRecordId IN (SELECT PatientRecordId FROM @Bills)

	INSERT INTO @Segments
	SELECT 4,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @HTNCoded)		
		AND PatientRecordId IN (
			SELECT PatientRecordId FROM @HTNDocumented
			UNION ALL
			SELECT PatientRecordId FROM @Bills
		)
	
	INSERT INTO @Segments
	SELECT 5,PatientRecordId		
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @HTNCoded)		
		
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
