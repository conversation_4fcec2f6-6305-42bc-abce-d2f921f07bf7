﻿CREATE PROCEDURE [dbo].[GetWaitlistAppointments] 
	@practiceId INT,
	@officeId INT = NULL,
	@startDate DATETIME = NULL, 
	@endDate DATETIME = NULL, 
	@practiceDoctorId INT = NULL,		
	@appointmentStatus INT = NULL,	
	@appointmentId INT = NULL,
	@filterPatient NVARCHAR(50) = NULL,
	@cancellationList BIT = 0,
	@triageUrgencyId INT = 0,
	@triageStatusId INT = 0,
	@testGroupId INT = 0,
	@PageNum INT = NULL,
	@PageSize INT = NULL
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;	

	DECLARE @sDate datetime2;
	DECLARE @eDate datetime2;
	DECLARE @query nvarchar(4000);
    DECLARE @params nvarchar(1000);
	DECLARE @offset int;

	SET @offset = (@PageNum-1)*@PageSize;

	IF(@appointmentId IS NULL OR @appointmentId = 0)
	BEGIN		
		SET @sDate = CAST(CONVERT(char(8), @startDate, 112) as datetime);
		SET @eDate = CAST(CONVERT(char(8), @endDate, 112) as datetime);
	END
	ELSE
	BEGIN		
		SET @officeId = (SELECT OfficeId FROM Appointments WHERE Id = @appointmentId);
	END

	DECLARE @appointments TABLE
		( id                        int identity(1,1) not null,
			app_id                    int not null,
			practiceid                int null,
			officeid                  int null,
			officeName                nvarchar(500) null,
			appointmentTime           datetime2 null,
			arrivedTime               nvarchar(10) null,
			leftTime                  nvarchar(10) null,
			appointmentPurpose        nvarchar(500) null,
			appointmentStatus         int null,
			appointmentNotes          nvarchar(1000) null,
			appointmentRegistrar      int null,
			MWLUrl                    nvarchar(500) null,
			MWLSentFlag               bit null,
			actionOnAbnormal          bit null,
			bookingConfirmation       bit null,
			roomNumber                nvarchar(10) null,
			PracticeDoctorId          int null,
			ExternalDoctorId		   int null,
			billStatusId              int null,
			OpeningStatement          nvarchar(500) null,
			referralDoctorId          int null,
			AppointmentTypeId         int null,
			AppointmentType           nvarchar(100) null,
			AppointmentTypeParentId   int null,
			appointmentConfirmation   int null,
			appointmentPaymentMethod  int null,
			TriageUrgencyId           int null,
			TriageUrgency			   nvarchar(500) null,
			TriageStatusId            int null,
			TriageStatus			   nvarchar(500) null,
			IsActive                  bit null,
			PatientRecordId           int null,
			DateCreated               datetime2 null,
			LastModified              datetime2 null,
			PracticeDoctor            nvarchar(100) null,
			cancellationList          bit null,
			IsImported                bit null,
			RecordType				  smallint,
			TotalRecords              int not null
			,primary key (id)
		);

	SET @query = N'	;WITH AppPaged AS (
					 SELECT apps.[Id]
						  ,apps.[OfficeId]
						  ,apps.[appointmentTime]
						  ,apps.[appointmentStatus]
						  ,apps.[PracticeDoctorId]
						  ,apps.[AppointmentTypeId]
						  ,apps.[IsActive]
						  ,apps.[PatientRecordId]
						FROM [dbo].[Appointments] apps
					    JOIN PatientRecords precs ON apps.PatientRecordId =precs.Id '+
						CASE WHEN @filterPatient IS NOT NULL THEN ' JOIN Demographics demo ON (apps.PatientRecordId = demo.PatientRecordId) ' ELSE '' END+
					' WHERE apps.IsActive = 1
					  AND   precs.PracticeId=@practiceId ';

	   IF @appointmentId > 0
		  SET @query = @query + N' AND apps.Id = @appointmentId ';
	   ELSE
		  SET @query = @query + N' AND apps.appointmentTime between @sDate AND @eDate ';	

	   IF @officeId > 0
		  SET @query = @query + N' AND apps.OfficeId = @officeId ';

	   IF @practiceDoctorId > 0
		  SET @query = @query + N' AND apps.PracticeDoctorId = @practiceDoctorId ';

   	   IF @testGroupId > 0  
		  SET @query = @query + N' AND EXISTS (SELECT TOP 1 1 FROM AppointmentTests appt JOIN TestGroups apptg ON apptg.testId = appt.testId WHERE appt.AppointmentId = apps.id AND apptg.GroupId = @testGroupId AND appt.IsActive = 1) '; 

	   IF @cancellationList = 1
		  SET @query = @query + N' AND apps.cancellationList = 1 '
	   ELSE
	   begin
		   IF @appointmentStatus IS NULL
			  SET @query = @query + N' AND apps.appointmentStatus in (0,1,16) '
		   ELSE 
			  SET @query = @query + N' AND apps.appointmentStatus = @appointmentStatus ';

		  IF @triageStatusId > 0
			SET @query = @query + N' AND apps.TriageStatusId = @triageStatusId ';  

		  IF @triageUrgencyId > 1
			SET @query = @query + N' AND apps.TriageUrgencyId = @triageUrgencyId ';
	  end;

	  IF @filterPatient IS NOT NULL
        SET @query = @query + N' AND ( lower(demo.lastName) LIKE '''+@filterPatient+'%'''+' OR lower(demo.firstName) LIKE '''+@filterPatient+'%'' ) ';

       SET @query = @query + N' )
							   SELECT AppPaged.[Id]
									  ,O.[PracticeId]	  
									  ,AppPaged.[OfficeId]
									  ,O.[name] AS officeName
									  ,AppPaged.[appointmentTime]
									  ,a.[ArrivedTime]
									  ,a.[LeftTime]
									  ,a.[appointmentPurpose]
									  ,AppPaged.[appointmentStatus]
									  ,a.[appointmentNotes]
									  ,a.[appointmentRegistrar]
									  ,a.[MWLUrl]
									  ,a.[MWLSentFlag]
									  ,a.[actionOnAbnormal]
									  ,a.[bookingConfirmation]
									  ,a.[roomNumber]
									  ,AppPaged.[PracticeDoctorId]
									  ,pracDocs.[ExternalDoctorId]
									  ,a.[billStatusId]
									  ,a.[OpeningStatement]
									  ,a.[referralDoctorId]
									  ,AppPaged.[AppointmentTypeId]
									  ,apptype.[name] AS AppointmentType
									  ,ISNULL(apptype.AppointmentTypeId,0) AS AppointmentTypeParentId
									  ,a.[appointmentConfirmation]
									  ,a.[appointmentPaymentMethod]
									  ,a.[TriageUrgencyId]
									  ,triageUrg.[description] AS TriageUrgency
									  ,a.[TriageStatusId]
									  ,triageStatus.[description] AS TriageStatus
									  ,AppPaged.[IsActive]
									  ,AppPaged.[PatientRecordId]
									  ,a.[DateCreated]
									  ,a.[LastModified]
									  ,isnull(extDocs.firstName,'''') +'' ''+isnull(extDocs.lastName,'''') AS PracticeDoctor
									  ,a.cancellationList
									  ,a.IsImported
									  ,a.RecordType
									  ,CountTotalRecs.TotalRecords
								 FROM AppPaged
								 JOIN Appointments a ON (AppPaged.id = a.id)
								 JOIN Office O ON AppPaged.OfficeId=O.Id
								 JOIN AppointmentTypes apptype ON AppPaged.AppointmentTypeId = apptype.Id
								 JOIN PracticeDoctors pracDocs ON AppPaged.PracticeDoctorId = pracDocs.Id
								 JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
							     LEFT JOIN TriageUrgencies triageUrg ON a.TriageUrgencyId = triageUrg.Id
								 LEFT JOIN TriageStatus triageStatus ON a.TriageStatusId = triageStatus.Id
								 CROSS APPLY ( SELECT COUNT(*) as TotalRecords FROM AppPaged ) CountTotalRecs
							 ORDER BY AppPaged.appointmentTime
							   OFFSET @OffSet ROWS
						   FETCH NEXT @PageSize ROWS ONLY '

       SET @params = N'@practiceId int, @appointmentID int, @appointmentStatus int, @OfficeID int, @practiceDoctorId int,  @sDate datetime, @eDate datetime, @cancellationList BIT, @triageUrgencyId INT, @triageStatusId int, @testGroupId int,@OffSet int,  @PageSize int'

--	   print @query;

       INSERT INTO @appointments(app_id       
								,practiceid             
								,officeid
								,officeName             
								,appointmentTime           
								,arrivedTime               
								,leftTime                  
								,appointmentPurpose        
								,appointmentStatus         
								,appointmentNotes          
								,appointmentRegistrar      
								,MWLUrl                    
								,MWLSentFlag               
								,actionOnAbnormal          
								,bookingConfirmation       
								,roomNumber                
								,PracticeDoctorId
								,ExternalDoctorId          
								,billStatusId              
								,OpeningStatement          
								,referralDoctorId          
								,AppointmentTypeId
								,AppointmentType
								,AppointmentTypeParentId
								,appointmentConfirmation   
								,appointmentPaymentMethod  
								,TriageUrgencyId  
								,TriageUrgency         
								,TriageStatusId   
								,TriageStatus         
								,IsActive                  
								,PatientRecordId           
								,DateCreated               
								,LastModified
								,PracticeDoctor
								,cancellationList
								,IsImported
								,RecordType
								,TotalRecords)
				 EXEC sp_executesql @query, @params, @practiceId = @practiceId, @appointmentId = @appointmentId,
									 @officeId = @officeId, @practiceDoctorId = @practiceDoctorId, @appointmentStatus = @appointmentStatus, 
									 @sDate = @sDate, @eDate = @eDate, @cancellationList = @cancellationList, @triageUrgencyId = @triageUrgencyId, 
									 @triageStatusId = @triageStatusId, @testGroupId = @testGroupId,@OffSet = @OffSet, @PageSize = @PageSize;

    SELECT apps.[app_id] as [Id]	    
		,apps.[practiceid] AS PracticeId
		,apps.[OfficeId]
		,apps.officeName
		,apps.[appointmentTime]
		,apps.[ArrivedTime]
		,apps.[LeftTime]
		,apps.[appointmentPurpose]
		,apps.[appointmentStatus]
		,apps.[appointmentNotes]
		,apps.[appointmentRegistrar]
		,ISNULL((SELECT TOP 1 isnull(u.FirstName,'')+' '+isnull(u.LastName,'')		               
			            FROM AspNetUsers u 						
						WHERE u.UserID = apps.appointmentRegistrar
						),'') AS AppointmentRegistrarName
		,apps.[MWLUrl]
		,apps.[MWLSentFlag]
		,apps.[actionOnAbnormal]
		,apps.[bookingConfirmation]
		,apps.[roomNumber]
		,apps.[PracticeDoctorId]
		,apps.[ExternalDoctorId]
		,apps.[referralDoctorId] AS ReferralDoctorId
		,extDocs.ReferralDoctor -- referral doctor
		,extDocs.RefDocOHIPPhysicianId -- referral doctor
		,extDocs.CPSO AS RefDocCPSO -- referral doctor
		,ISNULL((SELECT TOP 1 isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')		               
			            FROM ExternalDoctors as ed 
						JOIN DemographicsFamilyDoctors fd ON ed.Id = fd.ExternalDoctorId
						WHERE fd.DemographicId = demo.Id
						ORDER BY fd.Id DESC),'') AS FamilyDoctor
		,patPhoneNums.phoneNumbers AS PatientPhoneNumbers
		,apps.[AppointmentTypeId]
		,apps.AppointmentType
		,apps.AppointmentTypeParentId
		,ISNULL((SELECT [name] FROM AppointmentTypes WHERE id=apps.AppointmentTypeParentId),'') AS AppointmentTypeParent 
		,apps.[appointmentConfirmation]
		,apps.[appointmentPaymentMethod]
		,apps.[TriageUrgencyId]
		,apps.[TriageUrgency]
		,apps.[TriageStatusId]
		,apps.[TriageStatus]
		,apps.[IsActive]
		,apps.[PatientRecordId]
		,apps.[DateCreated]
		,apps.[LastModified]
		,apps.[PracticeDoctor]
		,demo.firstName AS PatientFirstName
		,demo.lastName AS PatientLastName
		,demo.dateOfBirth AS DateOfBirth
		,apps.cancellationList		
		,apps.IsImported
		,apps.RecordType
		,STUFF( 
					  (SELECT ', '+ CAST(dis.[message] AS VARCHAR(500))						  
						  FROM AppointmentTriageDispositions appDis
						  JOIN TriageDispositions dis ON appDis.TriageDispositionId = dis.Id   
						  WHERE appDis.AppointmentId = apps.[app_id] AND appDis.isActive = 1
						  ORDER BY dis.[message] ASC
						  FOR XMl PATH('')  
						),1,1,''  
					)  AS TriageDisposition
		,apps.TotalRecords
	FROM @appointments apps
	        JOIN Demographics demo ON apps.PatientRecordId = demo.PatientRecordId
		    LEFT JOIN ( SELECT ed.id, isnull(ed.firstName,'')+' '+isnull(ed.lastName,'') AS ReferralDoctor, 
			                ed.OHIPPhysicianId as RefDocOHIPPhysicianId, ed.CPSO, pn.TotalReferralDocFax, pn.TotalReferralDocNumbers
			            FROM ExternalDoctors as ed 
						    CROSS APPLY (select MAX(CASE WHEN phoneNumber IS NOT NULL THEN 1 ELSE 0 END) as TotalReferralDocNumbers, MAX(CASE WHEN faxnumber IS NOT NULL THEN 1 ELSE 0 END) as TotalReferralDocFax 
								            from ExternalDoctorPhoneNumbers edpn 
								            where edpn.ExternalDoctorId = ed.id ) as pn
					) as extDocs ON apps.referralDoctorId = extDocs.Id 			
			LEFT JOIN (
				SELECT DemographicId AS PatDemoId, phoneNumbers=STUFF  
				(  
					(  
					  SELECT TOP 3 ', '+ CAST(e.phoneNumber AS VARCHAR(500)) + ' '+ 
						  (CASE e.typeOfPhoneNumber
							WHEN 0 THEN 'H'
							WHEN 1 THEN 'C'
							WHEN 2 THEN 'W'
						   ELSE ''
						   END
						  )
						  FROM DemographicsPhoneNumbers e   
						  WHERE e.DemographicId = d.DemographicId AND e.phoneNumber IS NOT NULL AND e.phoneNumber != ''
						  ORDER BY e.DemographicId, e.typeOfPhoneNumber, e.Id DESC
						  FOR XMl PATH('')  
						),1,1,''  
					)  
				FROM DemographicsPhoneNumbers d 
				GROUP BY d.DemographicId
			) AS patPhoneNums ON demo.Id = patPhoneNums.PatDemoId
ORDER BY apps.id ASC
END
