﻿
-- =============================================
-- Author:		Michael
-- Create date: 2022-01-06
-- Description:	Patient Appointment Tests
-- =============================================
CREATE   PROCEDURE [dbo].[GetPatientAppointmentTests]
	@practiceId INT,
	@patientRecordId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	SELECT a.ID appointmentId,
		a.appointmentTime appointmentTime,
		O.name office,
		b.Id appointmentTestId,
		b.TestId testId,
		c.testShortName testName
	FROM Appointments A
		JOIN AppointmentTests b on a.Id=b.AppointmentId
		JOIN Tests c on b.TestId=c.Id
		JOIN Office O ON A.OfficeId=O.Id
	WHERE b.IsActive=1
		AND A.PatientRecordId=@patientRecordId 
		AND O.PracticeId=@practiceId
	ORDER BY a.appointmentTime DESC
END