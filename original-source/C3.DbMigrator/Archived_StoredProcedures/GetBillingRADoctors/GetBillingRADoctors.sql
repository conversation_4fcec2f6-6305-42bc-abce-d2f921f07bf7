﻿CREATE PROCEDURE [dbo].[GetBillingRADoctors] 
	@practiceId int,
	@billingNumbers nvarchar(max)
AS
BEGIN
	SET NOCOUNT ON;

	if (@billingNumbers = '')
		return;

	select a.OHIPPhysicianId billingNumber,a.firstName,a.lastName,b.ExternalDoctorId externalDoctorId,b.Id practiceDoctorId 
	from ExternalDoctors a inner join PracticeDoctors b on a.Id=b.ExternalDoctorId
	where b.PracticeId=@practiceId and ltrim(rtrim(a.OHIPPhysicianId)) in (SELECT value FROM STRING_SPLIT(@billingNumbers, ','))
END