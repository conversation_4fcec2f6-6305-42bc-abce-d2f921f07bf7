﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetReportStressEchoPhrases]
	@AppointmentTestsId int,
	@appointmentTestLogId INT = 0
AS
BEGIN
	SET NOCOUNT ON;

   IF @appointmentTestLogId <=0
   BEGIN
		SET @appointmentTestLogId = (SELECT MAX(L.Id) FROM AppointmentTestLogs L JOIN AppointmentTests T ON L.AppointmentId = T.AppointmentId WHERE T.Id = @AppointmentTestsId AND L.Status = 0)
   END  


CREATE TABLE #Phrases
(
	AppointmentTestsId int,
	value nvarchar(Max),
	text nVARCHAR (100),
	orderNumber int
)

CREATE TABLE #PhrasesReduced
(
	AppointmentTestsId int,
		sequence int,
   value nvarchar(Max),
   text nvarchar(100),
    description nVARCHAR(20),
	Excercise nvarchar(Max),
	Resting nvarchar(Max),


)

CREATE TABLE #PhrasesGeneral
(
	AppointmentTestsId int,	
	sequence int,
    description VARCHAR(20),
	Excercise nvarchar(Max),
	Resting nvarchar(Max)
)


insert into #Phrases
SELECT   distinct    B.AppointmentTestId as AppointmentTestsId, B.Value as value,  B.name AS text,  B.ordernumber AS ordernumber
FROM      (
SELECT        ReportPhraseSavedTexts.TopLevelReportPhraseID AS ReportPhraseId, ReportPhrases.name, 
						ReportPhraseSavedTexts.Value, AppointmentTestLogs.Id, AppointmentTests.Id AS AppointmentTestId, ReportPhrases.ordernumber, 
                         ReportPhrases.Id AS rpid, TestGroups.GroupId
FROM            ReportPhraseSavedTexts INNER JOIN
                         ReportPhrases ON ReportPhraseSavedTexts.TopLevelReportPhraseID = ReportPhrases.Id INNER JOIN
                         AppointmentTestLogs ON ReportPhraseSavedTexts.AppointmentTestLogID = AppointmentTestLogs.Id INNER JOIN
                         AppointmentTests ON AppointmentTestLogs.AppointmentID = AppointmentTests.AppointmentId AND AppointmentTestLogs.TestID = AppointmentTests.TestId INNER JOIN
                         Tests ON AppointmentTests.TestId = Tests.Id INNER JOIN
                         TestGroups ON Tests.Id = TestGroups.TestId
--WHERE        (AppointmentTestLogs.Status = 0) AND (AppointmentTests.Id = @AppointmentTestsId) 
WHERE AppointmentTestLogs.Id = @appointmentTestLogId AND AppointmentTests.Id = @AppointmentTestsId
and  (iif(ReportPhrases.name = 'Questionnaire' and TestGroups.GroupId = 50, 0,1)=1 )
) B


insert into #PhrasesReduced
select AppointmentTestsId, orderNumber,  value,text,

(case when text in('Resting ECG', 'Stress ECG', 'Persantine ECG') then 'ECG' else 'ECHO' END) AS Description,
(case when text in ('Stress ECG', 'Stress echo', 'Stress Echo Findings', 'Persantine ECG') then VALUE else 'XXX'  END) AS Excercise,
(case when text in ('Resting ECG',  'RESTING LV Systolic function', 'LV/RV' ) then VALUE else  'XXX'  END) AS Resting

from #Phrases 
where text in ('Resting ECG', 'Stress ECG', 'RESTING LV Systolic function',  'LV/RV', 'Stress echo','Stress Echo Findings', 'Persantine ECG')


insert into #PhrasesGeneral

SELECT 
(Case when a.AppointmentTestsId is null then b.AppointmentTestsId else a.AppointmentTestsId end) as AppointmentTestsId, 
  (Case when a.sequence is null then b.sequence else a.sequence end) as sequence, 
 (Case when a.Description is null then b.description else a.description end) as description, 
A.EXCERCISE, B.RESTING
FROM
(select * from #PhrasesReduced WHERE DESCRIPTION = 'ECG'  AND RESTING = 'XXX' ) A
full outer join 
(select * from #PhrasesReduced WHERE DESCRIPTION = 'ECG'  AND EXCERCISE = 'XXX') B
on a.description=b.description  

insert into #PhrasesGeneral
SELECT 
 (Case when a.AppointmentTestsId is null then b.AppointmentTestsId else a.AppointmentTestsId end) as AppointmentTestsId, 
  (Case when a.sequence is null then b.sequence else a.sequence end) as sequence, 
 (Case when a.Description is null then b.description else a.description end) as description, 
 A.EXCERCISE, B.RESTING
FROM
(select  top 1 * from #PhrasesReduced WHERE DESCRIPTION = 'ECHO'  
AND TEXT IN ('Stress Echo','Stress Echo Findings')
 ) A
full outer join 
(select * from #PhrasesReduced WHERE DESCRIPTION =  'ECHO' 
AND TEXT IN ('RESTING LV Systolic function', 'LV/RV')
  AND EXCERCISE = 'XXX') B
on a.description=b.description  


SELECT AppointmentTestsId, Sequence, description, Rtrim(Excercise) as Excercise, Rtrim(Resting) as Resting FROM #PhrasesGeneral  



drop table #PhrasesGeneral
drop table #PhrasesReduced
drop table #Phrases
END