﻿CREATE PROCEDURE [dbo].[GetDashboardCAREBNS005] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @CurrentDate AS DATE,@FYDate AS DATE,@FluSeasonStartDate AS DATE,@FluSeasonEndDate AS Date, @AgeofDate as Date, @FYStartDate as  Date, @FYEndDate as Date
	SELECT @CurrentDate = GETDATE()

	SELECT @AgeofDate = STR(IIF(MONTH(@CurrentDate)<4,YEAR(@CurrentDate)-1,YEAR(@CurrentDate))) + '-12-31' -- Age CutOffDate

	--SELECT @FYStartDate = STR(IIF(MONTH(@CurrentDate)<3,YEAR(@CurrentDate)-1,YEAR(@CurrentDate))) + '-04-01' 
	--SELECT @FYEndDate = STR(IIF(MONTH(@CurrentDate)<3,YEAR(@CurrentDate),YEAR(@CurrentDate)+1)) + '-04-31' 

	SELECT @FluSeasonStartDate = STR(IIF(MONTH(@CurrentDate)<4,YEAR(@CurrentDate)-1,YEAR(@CurrentDate))) + '-09-30' 
	SELECT @FluSeasonEndDate = STR(IIF(MONTH(@CurrentDate)<4,YEAR(@CurrentDate),YEAR(@CurrentDate)+1)) + '-04-30' 	

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)	
	DECLARE @InfluenzaDocumented AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)
	
	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	JOIN DemographicsEnrollments ROSTER ON MRP.id = ROSTER.DemographicsMRPId -- this checks for roster status, active if no termination date
	WHERE 
	D.active = 0	
	AND MRP.IsActive = 1 -- Rostered ( Source: sp_GetRecallList)
	AND (DATEDIFF(DD,D.dateOfBirth,@AgeofDate) / 365.5) >= 65
	--AND D.gender = 1
	AND (MRP.PracticeDoctorId = @PracticeDoctorId)-- OR @PracticeDoctorId IS NULL)
	AND ROSTER.enrollmentTerminationDate IS NULL -- Roster not terminated
	GROUP BY D.PatientRecordId

	--- Influenza immunization documented between September 30 and January 31 of the current flu season 
	INSERT INTO @InfluenzaDocumented
	SELECT PatientRecordId	
	FROM BillDetails B
	WHERE B.serviceCode = 'Q130A'		
	AND B.serviceDate BETWEEN @FluSeasonStartDate AND @FluSeasonEndDate	
	UNION ALL
	SELECT IM.PatientRecordId
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId in (1,24)							-- Type = Influenza
	AND DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay) BETWEEN @FluSeasonStartDate AND @FluSeasonEndDate	

	/* Refused status is not tracked under current explanation code is included in case changes are required 
	--- Influenza immunization refusal documented between September 30 and January 31 of the current flu season 
	SELECT PatientRecordId
	INTO #InfluenzaDocumented
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=24							-- Type = Influenza
	AND DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay) BETWEEN @FluSeasonStartDate AND @FluSeasonEndDate	
*/
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @InfluenzaDocumented)

	INSERT INTO @Segments
	SELECT 2,PatientRecordId	
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @InfluenzaDocumented)	
	
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments	

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
