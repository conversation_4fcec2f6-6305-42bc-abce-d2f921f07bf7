﻿CREATE PROCEDURE [dbo].[GetVPLogsByLogIds]
	@vpAppointmentTestLogIds AS dbo.IntegerList READONLY
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 

	v.Id as Id,
    v.[Date] as [Date],
    v.[Status] as [Status],
    v.AppointmentId as AppointmentId,
    v.PatientRecordId as PatientId,
    v.IP as IP,
    v.UserId as UserId,
	v.Finalized,
    u.UserName as UserName,
	u.FirstName,
	u.LastName	
FROM 
	VP_AppointmentTestLog v  
	JOIN @vpAppointmentTestLogIds l ON v.Id = l.IntegerValue
	left join AspNetUsers u on v.UserId = u.UserID	
END
