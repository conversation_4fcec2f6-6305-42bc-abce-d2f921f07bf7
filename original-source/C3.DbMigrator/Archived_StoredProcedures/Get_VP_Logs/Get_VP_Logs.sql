﻿CREATE PROCEDURE [dbo].[Get_VP_Logs]

@appointmentID int ,
@patientID int 

AS

SELECT 

	x.Id as Id,
    x.Date as Date,
    x.Status as Status,
    x.AppointmentId as AppointmentId,
    x.PatientRecordId as PatientRecordId,
    x.IP as IP,
    x.UserId as UserId,
    u.UserName as UserName,
	u.FirstName,u.LastName 
	,ISNULL( (SELECT TOP 1 d.Id FROM PracticeDoctors  d WHERE d.ApplicationUserId=u.Id),0) AS  PracticeDoctorId
FROM 

	VP_AppointmentTestLog x  
	left join AspNetUsers u on x.UserId = u.UserID
	--LEFT JOIN  PracticeDoctors D ON u.Id=D.ApplicationUserId
	JOIN Appointments a ON x.AppointmentId=a.Id
	JOIN PatientRecords P ON x.PatientRecordId=P.Id
	
WHERE
	a.PatientRecordId=p.Id AND
     x.AppointmentId = @appointmentID AND 
     x.PatientRecordId	 = @patientID  ;

