﻿CREATE PROCEDURE [dbo].[GetReportRootCustomCategories]
	@appointmentTestId INT,
	@appointmentTestLogId INT = 0	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @practiceId INT;
	DECLARE @officeId INT;	
	DECLARE @appointmentId INT;
	DECLARE @appointmentTime Datetime2;
	DECLARE @patientId INT;	
	DECLARE @testId INT;	
	DECLARE @groupId INT; -- test group id
	DECLARE @practiceDoctorId INT;
	DECLARE @externalDoctorId INT; -- ExternalDoctorId for main appointment doctor
	DECLARE @practiceTemplateId INT; 
	DECLARE @customType INT = 0; -- vitals
	DECLARE @visibleOnly BIT = 1; -- only visible vitals categories
	DECLARE @hasPhysicalExam BIT = 0;
	DECLARE @physicalExam nvarchar(50) = 'Physical Exam'; -- root category physical exam, this is needed for vitals
	DECLARE @physicalExamId INT;
	DECLARE @physicalExamDisplayOrder INT = 0;
	DECLARE @ADDEDMED nvarchar(100) = 'Added';
	DECLARE @PRIORMED nvarchar(100) = 'Prior';
	DECLARE @DISCONTINUEDMED nvarchar(100) = 'Discontinued';
	DECLARE @DOSECHANGEMED nvarchar(100) = 'Dose Changed';
	DECLARE @ACTIVEMED nvarchar(100) = 'Active';
	DECLARE @MedCategoryLabel nvarchar(100) = 'Medications';
	DECLARE @MedChangesCategoryLabel nvarchar(100) = 'Medication Changes';
	DECLARE @closingStatement nvarchar(50) = 'Closing Statement';
	DECLARE @closingStatementId int = 0;
	DECLARE @hasClosingStatment BIT = 0;
	DECLARE @closingStatementDisplayOrder int = 0;
	DECLARE @mostRecent nvarchar(50) = 'Most recent investigations';
	DECLARE @mostRecentId int = 0;
	DECLARE @hasmostRecent BIT = 0;
	DECLARE @mostRecentDisplayOrder int = 0;
	DECLARE @isVisibleAllegies BIT = 0;
	DECLARE @isVisibleMeds BIT = 0;
	DECLARE @medsCPPCategoryId INT = 7;
	DECLARE @allergiesCPPCategoryId INT = 8;
	DECLARE @activeAllergies NVARCHAR(4000);
	DECLARE @activeMedsDisplay INT = 3; -- as requested by Galina, always number 3
	DECLARE @patientOnMeds NVARCHAR(100) = 'Patient presented today on the following medications';	
	DECLARE @hasPatientMeds BIT = 0;
	DECLARE @medsSeparator NVARCHAR(100) = ',  ';
	DECLARE @addedDisplay INT = 2000000;
	DECLARE @isVP BIT = 0;
	DECLARE @logDate DATETIME2;
	DECLARE @shortNoteTemplateId INT = ISNULL((SELECT TOP 1 Id FROM RootTemplates WHERE TemplateName = 'Short Note'),0);
	DECLARE @isShortNote BIT = 0;
	DECLARE @rootTemplateId INT = 0;
	DECLARE @splitByNewLine NVARCHAR(100) = ''+ char(10);
	DECLARE @splitByComma NVARCHAR(100) = ',  ';
	DECLARE @medsDisplayTypeId INT = 2; -- 1 = list, 2 = comma separate paragraph
	DECLARE @isPace BIT = 0;
	DECLARE @totalRootCategories INT = 0;
	DECLARE @docMedsDisplayOrder INT = 0;
	DECLARE @docRootCategories TABLE
	(        
		RootCategoryId int NOT NULL,
		CategoryNameOriginal nvarchar(1500) NULL,
		CategoryNameCustom nvarchar(1500) NULL,
		DisplayOrder int NULL,				
		IsVisibleLabel bit NOT NULL,					
        primary key (RootCategoryId)
	)
	DECLARE @tempCategories TABLE
	(			
	Id int identity(1,1) not null,
	RootCategoryId int NOT NULL,
	CategoryNameCustom nvarchar(1500),
	CategoryNameOriginal nvarchar(1500),
	SavedValue nvarchar(max),
	IsVisibleLabel bit,
	IsCustomInsert bit, -- this is for when we insert a custom root category for ssrs only
	DisplayOrder int NOT NULL,
	OriginalDisplayOrder int NOT NULL		
	primary key(Id)
	);
	DECLARE @tempMeds TABLE
	(	
	MedicationSetId int NOT NULL,
	PatientMedicationId int NOT NULL,	
	MedicationName nvarchar(500) NULL,
	DIN nvarchar(50) NULL,
	Dose nvarchar(50) NULL,
	Strength nvarchar(50) NULL,
	Form nvarchar(100) NULL,
	Ingredients nvarchar(1500) NULL,
	SIG nvarchar(50) NULL,
	[Route] nvarchar(100) NULL,
	ActionType nvarchar(100) NULL,
	RowNum INT NOT NULL,
	DateStarted datetime2 NOT NULL,
	DateDiscontinued datetime2 NULL			
	);
	DECLARE @tempAllergies TABLE
	(	
	PatientAllergyId int null,
	PatientId int NOT NULL,	
	MedicationName nvarchar(500) NULL,
	Ingredient nvarchar(500) NULL,
	DisplayName nvarchar(500) NULL,
	Severity nvarchar(50) NULL,	
	DateStarted datetime2 NOT NULL	
	);
	DECLARE @tempVitalsAndLabs TABLE
	(	
	Id int not null,-- measurement id
	[Name] nvarchar(500) null,
	[ShortName] nvarchar(500) null,
	Units nvarchar(500) null,
	SavedValue nvarchar(600),
	CustomType int not null, -- 0 - vitals,  1 - labs
	DisplayOrder int not null
	);
	
	SELECT TOP 1 
	@practiceId = o.PracticeId,
	@officeId = o.Id,	
	@appointmentId = appTest.appointmentId,	
	@appointmentTime = app.appointmentTime,
	@patientId = app.PatientRecordId,	
	@practiceDoctorId = app.PracticeDoctorId,
	@testId = appTest.TestId,
	@isVP = CAST(ISNULL(CASE WHEN appTest.TestId = 29 THEN 1 ELSE 0 END,0) as bit)
	FROM AppointmentTests appTest
	JOIN Appointments app ON appTest.AppointmentId = app.Id	
	JOIN Office o ON app.OfficeId = o.Id
	WHERE appTest.Id = @appointmentTestId;		
	
	SET @externalDoctorId = ISNULL((SELECT TOP 1 ExternalDoctorId FROM PracticeDoctors WHERE Id = @practiceDoctorId),0);	
	SET @groupId = ISNULL((SELECT TOP 1 GroupId FROM TestGroups WHERE TestId = @testId ),0);
	SET @physicalExamId = ISNULL((SELECT TOP 1 r.Id FROM RootCategories r WHERE r.CategoryName = @physicalExam AND r.GroupId = @groupId),0);
	SET @closingStatementId = ISNULL((SELECT TOP 1 r.Id FROM RootCategories r WHERE r.CategoryName = @closingStatement AND r.GroupId = @groupId),0);
	SET @mostRecentId = ISNULL((SELECT TOP 1 r.Id FROM RootCategories r WHERE r.CategoryName = @mostRecent AND r.GroupId = @groupId),0);
	SET @isVisibleAllegies = ISNULL((SELECT TOP 1 CASE WHEN cs.[Skip] = 0 THEN 1 ELSE 0 END 
									FROM VP_CPP_Skipped cs 
									JOIN VP_CPP_Setting st ON cs.CPP_Category_Id = st.VP_CPP_Category_Id 
									WHERE cs.UserID = @externalDoctorId AND cs.CPP_Category_Id = @allergiesCPPCategoryId
									AND st.DoctorID = @externalDoctorId AND st.Visible = 1),0);
	SET @isVisibleMeds = ISNULL((SELECT TOP 1 CASE WHEN cs.[Skip] = 0 THEN 1 ELSE 0 END 
									FROM VP_CPP_Skipped cs 
									JOIN VP_CPP_Setting st ON cs.CPP_Category_Id = st.VP_CPP_Category_Id 
									WHERE cs.UserID = @externalDoctorId AND cs.CPP_Category_Id = @medsCPPCategoryId
									AND st.DoctorID = @externalDoctorId AND st.Visible = 1),0);

	-- check if its pace or cmd
	IF EXISTS (SELECT * FROM Practices WHERE Id = @practiceId AND PracticeName IN ('PACE','CMD')) -- #13354 CMD Included
	BEGIN
		SET @medsDisplayTypeId = 1;
		SET @medsSeparator = @splitByNewLine;
		--SET @isPace = 1;
	END

   IF @appointmentTestLogId <=0
   BEGIN
		SET @appointmentTestLogId = ISNULL((SELECT TOP 1 Id FROM AppointmentTestSaveLogs WHERE AppointmentTestId = @appointmentTestId AND IsActive = 1 ORDER BY LogDate DESC),0);
   END  

   SELECT TOP 1 @practiceTemplateId = sl.PracRootCategoryTempId, @logDate = sl.LogDate , @rootTemplateId = pt.TemplateId
   FROM AppointmentTestSaveLogs sl 
   JOIN PracticeRootCategoryTemplates pt ON sl.PracRootCategoryTempId = pt.Id
   WHERE sl.Id = @appointmentTestLogId
   AND pt.PracticeId = @practiceId;
   
   --check if this is a shortnote template
   IF @rootTemplateId > 0 AND @shortNoteTemplateId > 0 AND @rootTemplateId = @shortNoteTemplateId
   BEGIN
		SET @isShortNote = 1;
   END   
		
   INSERT INTO @docRootCategories
   SELECT    
   drcc.RootCategoryId,   
   drcc.CategoryNameOriginal,  
   drcc.CategoryNameCustom,
   drcc.DisplayOrder,   
   drcc.IsVisibleLabel  
   FROM dbo.fn_GetDoctorRootCategories(@groupId,@externalDoctorId,@practiceId,@practiceTemplateId,0) drcc  
   WHERE drcc.IsVisibleInLetter = 1   
   ORDER BY drcc.DisplayOrder;
   
   INSERT INTO @tempCategories
   SELECT    
   rcc.RootCategoryId,
   CASE WHEN rcc.IsVisibleLabel = 1THEN rcc.CategoryNameCustom ELSE '' END,
   rcc.CategoryNameOriginal,   
   rs.SavedValue + char(10),
   rcc.IsVisibleLabel,
   0,
   ROW_NUMBER() OVER(ORDER BY rcc.DisplayOrder),
   rcc.DisplayOrder AS OriginalDisplayOrder      
   FROM @docRootCategories rcc
   JOIN RootCategorySavedValues rs ON rcc.RootCategoryId = rs.RootCategoryId
   JOIN AppointmentTestSaveLogs sv ON rs.AppointmentTestSaveLogId = sv.Id   
   AND sv.Id = @appointmentTestLogId
   AND sv.PracRootCategoryTempId = @practiceTemplateId
   AND ISNULL(LTRIM(RTRIM(rs.SavedValue)),'') <> ''
   ORDER BY rcc.DisplayOrder;
   
   INSERT INTO @tempVitalsAndLabs
   SELECT
	 um.Id -- measurement id
	,um.[Name]
	,um.[ShortName]	
	,um.Units	
	,sv.[Value] AS SavedValue
	,um.[Type]
	,cm.[Rank] As DisplayOrder
	FROM VPUniqueMeasurements um
	JOIN VP_Measurements_Patient cm ON um.Id = cm.MeasurementID
	JOIN VPMeasurementSavedValues sv ON cm.MeasurementID = sv.VPUniqueMeasurementId		
	JOIN AppointmentTestSaveLogs l ON sv.AppointmentTestSaveLogId =l.Id	
	WHERE l.AppointmentTestId = @appointmentTestId AND l.Id = @appointmentTestLogId
	AND cm.DocID = @externalDoctorId AND cm.Visible = 1
	AND ISNULL(LTRIM(RTRIM(sv.[Value])),'') <> ''
	AND um.[Status] = 0
	AND um.[Spec] = 0
	ORDER BY cm.[Rank], um.[Name];

	SET @totalRootCategories = (SELECT COUNT(*) FROM @tempCategories);
 
	-- only get medications and allergies if its not a short not template
	IF @isShortNote = 0
	BEGIN

	IF @isVisibleMeds = 1
	BEGIN
	INSERT INTO @tempMeds
	SELECT
	meds.MedicationSetId,
	meds.PatientMedicationId,	
	dbo.fn_UpperCaseAllFirstLetters(ISNULL(meds.MedicationName,'')),
	LOWER(ISNULL(meds.DIN,'')),
	LOWER(ISNULL(meds.Dose,'')),
	LOWER(ISNULL(meds.Strength,'')),
	meds.Form,
	meds.Ingredients,
	LOWER(ISNULL(meds.SIG,'')),
	meds.[Route],
	meds.ActionType,
	meds.RowNum,	
	meds.DateStarted,
	meds.DateDiscontinued	
	FROM fn_GetReportMedications(@practiceId,@patientId,@externalDoctorId,@appointmentTime,@isVP,1) meds;
	END

	IF @isVisibleAllegies = 1
	BEGIN
		INSERT INTO @tempAllergies
		SELECT 	
		 pa.Id	 
		,pa.PatientRecordId		
		,pa.MedicationName
		,pal.Ingredient
		,dbo.fn_UpperCaseAllFirstLetters(CASE WHEN pal.Ingredient IS NULL THEN ISNULL(pa.MedicationName,'') ELSE pal.Ingredient END)
		--,LOWER(ISNULL(s.[Description],''))	
		,CASE WHEN pa.SeverityId = 6 THEN '' ELSE LOWER(ISNULL(s.[Description],'')) END AS Severity
		,pa.DateStarted				
		FROM PatientAllergies pa
		JOIN Severity s ON pa.SeverityId = s.Id
		LEFT JOIN PatientAllergyIngredients pal ON pa.Id = pal.PatientAllergyId
		WHERE pa.PatientRecordId = @patientId 
		AND CONVERT(char(8), pa.DateStarted, 112) <= CONVERT(char(8), @appointmentTime, 112)
		AND pa.IsActive = 1
		AND pa.AllergyStatusId = 1
		ORDER BY pa.DateStarted DESC;

		SELECT @activeAllergies = COALESCE(@activeAllergies + @medsSeparator , '') + DisplayName + ' ' + Severity FROM @tempAllergies;
	END
	END; -- end short note check

	IF EXISTS (SELECT * FROM @tempCategories WHERE RootCategoryId = @closingStatementId)
	BEGIN				
		SET @hasClosingStatment = 1;
		SET @closingStatementDisplayOrder = ISNULL((SELECT TOP 1 tr.DisplayOrder FROM @tempCategories tr WHERE tr.RootCategoryId = @closingStatementId),0);		
	END;
		
	-- we start to customize and rearrange the categories

	IF EXISTS (SELECT * FROM @tempMeds where ActionType = @ACTIVEMED or ActionType = @PRIORMED 
	or (ActionType = @DISCONTINUEDMED AND CONVERT(char(8), DateDiscontinued, 112) = CONVERT(char(8), @appointmentTime, 112)))
	BEGIN
		SET @hasPatientMeds = 1;
		DECLARE @activeMeds NVARCHAR(4000);			
		DECLARE @addMeds BIT = 1;
				
		IF NOT EXISTS(SELECT * FROM @tempCategories)
		BEGIN		
			SET @activeMedsDisplay = 1;		
		END		
		ELSE
		BEGIN			
			SET @docMedsDisplayOrder  = ISNULL((SELECT TOP 1 DisplayOrder FROM @docRootCategories WHERE CategoryNameOriginal = @MedCategoryLabel),0);
			IF @docMedsDisplayOrder > 0
			BEGIN				
				DECLARE @disPlM INT = ISNULL((SELECT TOP 1 t.DisplayOrder 
				FROM @tempCategories t 
				WHERE t.OriginalDisplayOrder >= @docMedsDisplayOrder
				ORDER BY t.OriginalDisplayOrder
				),0);			

				IF @disPlM > @totalRootCategories OR @disPlM = 0
				BEGIN
					SET @disPlM = (SELECT COUNT(Id) FROM @tempCategories);
				END

				SET @activeMedsDisplay = @disPlM;						
			END
			ELSE IF @docMedsDisplayOrder = 0
			BEGIN
				SET @addMeds = 0;
			END
		END

		IF @addMeds = 1
		BEGIN
		SELECT @activeMeds = COALESCE(@activeMeds + @medsSeparator, '') + REPLACE(MedicationName, ' ' + Strength, '') + ' ' +  Strength + ' ' + Dose + ' ' + SIG
		FROM @tempMeds  where ActionType = @ACTIVEMED or ActionType = @PRIORMED 
		or (CONVERT(char(8), DateDiscontinued, 112) = CONVERT(char(8), @appointmentTime, 112));		
		
		IF @medsDisplayTypeId = 1 SELECT @activeMeds = @activeMeds + @splitByNewLine + @splitByNewLine;
		
		UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
		WHERE DisplayOrder >= @activeMedsDisplay;	

		INSERT INTO @tempCategories(CategoryNameOriginal,CategoryNameCustom,RootCategoryId,IsVisibleLabel,IsCustomInsert,SavedValue,DisplayOrder, OriginalDisplayOrder)
		VALUES(@patientOnMeds,@patientOnMeds, -1,1,1,@activeMeds,@activeMedsDisplay,@addedDisplay + @activeMedsDisplay);		
		END
	END

	-- setup vitals for physical exam
	IF EXISTS (SELECT * FROM @tempCategories WHERE RootCategoryId = @physicalExamId)	
	BEGIN
		SET @hasPhysicalExam = 1;
		SET @physicalExamDisplayOrder = (SELECT TOP 1 tr.DisplayOrder FROM @tempCategories tr WHERE tr.RootCategoryId = @physicalExamId);
		
	END
	ELSE IF EXISTS (SELECT * FROM @tempVitalsAndLabs where CustomType = 0) -- for vitals
	BEGIN
		
		SET @hasPhysicalExam = 1;
		DECLARE @originalExamDisplayOrder INT =ISNULL((SELECT TOP 1 dr.DisplayOrder FROM DoctorRootCategories dr WHERE dr.RootCategoryId = @physicalExamId),0);
		SET @physicalExamDisplayOrder = @originalExamDisplayOrder;
		
		IF @physicalExamDisplayOrder = 0
		BEGIN
			SET @physicalExamDisplayOrder = ISNULL((SELECT COUNT(Id) FROM @tempCategories),0);
			IF @hasClosingStatment = 1
			BEGIN 
				UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
				WHERE DisplayOrder >= @closingStatementDisplayOrder;	
				SET @physicalExamDisplayOrder = @closingStatementDisplayOrder;					

				-- reset closing statement
				SET @closingStatementDisplayOrder = ISNULL((SELECT DisplayOrder FROM @tempCategories WHERE RootCategoryId = @closingStatementId),0);
			END				
		END
		ELSE
		BEGIN			
			DECLARE @display INT = ISNULL((SELECT TOP 1 t.DisplayOrder 
				FROM @tempCategories t 
				WHERE t.RootCategoryId > 0 AND t.OriginalDisplayOrder >= @originalExamDisplayOrder
				ORDER BY t.OriginalDisplayOrder
				),@physicalExamDisplayOrder);

			SET @physicalExamDisplayOrder = @display;
			IF @hasClosingStatment = 1 AND @physicalExamDisplayOrder >= @closingStatementDisplayOrder
			BEGIN
				SET @physicalExamDisplayOrder = @closingStatementDisplayOrder;
			END;

			UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
			WHERE DisplayOrder >= @physicalExamDisplayOrder;		
			
			-- reset closing statement
			SET @closingStatementDisplayOrder = ISNULL((SELECT DisplayOrder FROM @tempCategories WHERE RootCategoryId = @closingStatementId),0);			
		END;				
		
		INSERT INTO @tempCategories(CategoryNameOriginal,CategoryNameCustom,RootCategoryId,IsVisibleLabel,IsCustomInsert,SavedValue,DisplayOrder,OriginalDisplayOrder)
		VALUES(@physicalExam,@physicalExam, @physicalExamId,1,1,'',@physicalExamDisplayOrder, @originalExamDisplayOrder);
				
	END
	
	IF EXISTS (SELECT * FROM @tempVitalsAndLabs WHERE CustomType = 0)
		BEGIN
			DECLARE @vitalsValues nvarchar(max) 
			SELECT @vitalsValues = COALESCE(@vitalsValues + '; ', '') + tv.[Name] + ' ' + ISNULL(tv.SavedValue,'') + ' ' + ISNULL(tv.Units,'')
			FROM @tempVitalsAndLabs tv WHERE tv.CustomType = 0 ORDER BY tv.DisplayOrder;

			DECLARE @currentPhyisical nvarchar(max) = 
			(SELECT TOP 1 tr.SavedValue FROM @tempCategories tr WHERE tr.RootCategoryId = @physicalExamId);

			Update tc 
			SET tc.SavedValue = 'Vitals: '+char(10)+@vitalsValues + char(10) + char(10)+ @currentPhyisical
			FROM @tempCategories tc
			WHERE tc.RootCategoryId = @physicalExamId;
			 
	END;
	
	IF EXISTS (SELECT * FROM @tempAllergies)
	BEGIN
		DECLARE @activeAllergiesDisplay INT = ISNULL((SELECT COUNT(Id) FROM @tempCategories),0);

		IF @hasPatientMeds = 1
		BEGIN
			SET @activeAllergiesDisplay = @activeMedsDisplay + 1;
			UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
			WHERE DisplayOrder >= @activeAllergiesDisplay;
		END
		ELSE IF @hasPhysicalExam = 1
		BEGIN
			UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
			WHERE DisplayOrder >= @physicalExamDisplayOrder;
			SET @activeAllergiesDisplay = @physicalExamDisplayOrder;		
			SET @physicalExamDisplayOrder = (SELECT DisplayOrder FROM @tempCategories WHERE RootCategoryId = @physicalExamId);				
		END
		ELSE IF @hasClosingStatment = 1
		BEGIN
			UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
			WHERE DisplayOrder >= @closingStatementDisplayOrder;
			SET @activeAllergiesDisplay = @closingStatementDisplayOrder;				
		END;	
		-- reset closing statement
		SET @closingStatementDisplayOrder = ISNULL((SELECT DisplayOrder FROM @tempCategories WHERE RootCategoryId = @closingStatementId),0);

		INSERT INTO @tempCategories(CategoryNameOriginal,CategoryNameCustom,RootCategoryId,IsVisibleLabel,IsCustomInsert,SavedValue,DisplayOrder, OriginalDisplayOrder)
		VALUES('Allergies','Allergies', -1,1,1,@activeAllergies,@activeAllergiesDisplay,@addedDisplay + @activeAllergiesDisplay);	
	END;

	IF EXISTS (SELECT * FROM @tempMeds)
	BEGIN	
		
		DECLARE @doseChangeMeds NVARCHAR(4000);
		DECLARE @disContinuedMeds NVARCHAR(4000);  		
		DECLARE @doseChangeMedsDisplay INT = 0;  
		DECLARE @disConMedsDisplay INT = 0;		
		DECLARE @docsMedsChangeDisplay INT = 0;
		DECLARE @addMedChanges BIT = 1;
		

		SET @docsMedsChangeDisplay  = ISNULL((SELECT TOP 1 DisplayOrder FROM @docRootCategories WHERE CategoryNameOriginal = @MedChangesCategoryLabel),0);
		IF @docsMedsChangeDisplay > 0
		BEGIN	

			DECLARE @disPlMchanges INT = ISNULL((SELECT TOP 1 t.DisplayOrder 
				FROM @tempCategories t 
				WHERE t.OriginalDisplayOrder >= @docsMedsChangeDisplay
				ORDER BY t.OriginalDisplayOrder
				),0);

				IF @disPlMchanges > @totalRootCategories OR @disPlMchanges = 0
				BEGIN
					IF @docMedsDisplayOrder > 0 AND @docMedsDisplayOrder > @docsMedsChangeDisplay
						SET @disPlMchanges = (SELECT COUNT(*) FROM @tempCategories) -2;
					ELSE
						SET @disPlMchanges = (SELECT COUNT(*) FROM @tempCategories);
				END

				SET @docsMedsChangeDisplay = @disPlMchanges;

			SET @doseChangeMedsDisplay = @docsMedsChangeDisplay;			
			UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
			WHERE DisplayOrder >= @doseChangeMedsDisplay;
			
			SET @disConMedsDisplay = (@doseChangeMedsDisplay + 1);
			UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
			WHERE DisplayOrder >= @disConMedsDisplay;			
		END
		ELSE IF @docsMedsChangeDisplay = 0
		BEGIN
			SET @addMedChanges = 0;
		END
		ELSE
		BEGIN
			SET @doseChangeMedsDisplay = (ISNULL((SELECT Max(DisplayOrder) FROM @tempCategories),0) + 1);
			SET @disConMedsDisplay = (@doseChangeMedsDisplay + 1);	
		END		
				
		IF @addMedChanges = 1
		BEGIN
			SELECT @doseChangeMeds = COALESCE(@doseChangeMeds + @medsSeparator, '') + REPLACE(MedicationName, ' ' + Strength, '') + ' ' +  Strength + ' ' + Dose + ' ' + SIG
			FROM @tempMeds  where ActionType = @ADDEDMED or ActionType = @DOSECHANGEMED;			

			INSERT INTO @tempCategories(CategoryNameOriginal,CategoryNameCustom,RootCategoryId,IsVisibleLabel,IsCustomInsert,SavedValue,DisplayOrder, OriginalDisplayOrder)
			VALUES('Medications added or dose changed at this visit','Medications added or dose changed at this visit', -1,1,1,ISNULL(@doseChangeMeds,'None'),@doseChangeMedsDisplay,@addedDisplay + @doseChangeMedsDisplay);	
				
			SELECT @disContinuedMeds = COALESCE(@disContinuedMeds + @medsSeparator, '') + REPLACE(MedicationName, ' ' + Strength, '') + ' ' +  Strength + ' ' + Dose + ' ' + SIG  
			FROM @tempMeds  where ActionType = @DISCONTINUEDMED AND CONVERT(char(8), DateDiscontinued, 112) = CONVERT(char(8), @appointmentTime, 112)
			AND MedicationSetId NOT IN(SELECT MedicationSetId FROM @tempMeds WHERE ActionType = @DOSECHANGEMED);

			INSERT INTO @tempCategories(CategoryNameOriginal,CategoryNameCustom,RootCategoryId,IsVisibleLabel,IsCustomInsert,SavedValue,DisplayOrder, OriginalDisplayOrder)
			VALUES('Discontinued Medications','Discontinued Medications', -1,1,1,ISNULL(@disContinuedMeds,'None'),@disConMedsDisplay,@addedDisplay + @disConMedsDisplay);	
		END
	END

	IF EXISTS(SELECT * FROM @tempVitalsAndLabs WHERE CustomType = 1)
	BEGIN				
		DECLARE @labResultDate DATETIME2 = null;
		DECLARE @collectionDate DATETIME2 = null;

		SELECT TOP 1 @labResultDate = l.LabResultDate, @collectionDate = r.collectionDateTime
		FROM VPLabResults l 
		LEFT JOIN HL7Report r ON l.HL7ReportId = r.Id
		WHERE l.AppointmentTestSaveLogId = @appointmentTestLogId

		IF @labResultDate IS NOT NULL --OR @collectionDate IS NOT NULL
		BEGIN

			DECLARE @labDate DATETIME2 = CASE WHEN @labResultDate IS NOT NULL THEN @labResultDate ELSE @collectionDate END;
			DECLARE @labsValues nvarchar(max);
			DECLARE @labsDisplayOrder int = (ISNULL((SELECT Max(DisplayOrder) FROM @tempCategories),0) + 1);
			SELECT @labsValues = COALESCE(@labsValues + @splitByComma, '') + tv.[Name] + ' ' + ISNULL(tv.SavedValue,'') + ' ' + ISNULL(tv.Units,'')
			FROM @tempVitalsAndLabs tv WHERE tv.CustomType =1 ORDER BY tv.DisplayOrder;

			UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
			WHERE DisplayOrder >= @labsDisplayOrder;		

			INSERT INTO @tempCategories(CategoryNameOriginal,CategoryNameCustom,RootCategoryId,IsVisibleLabel,IsCustomInsert,SavedValue,DisplayOrder, OriginalDisplayOrder)
			VALUES('Labs','Labs (Result Date: '+ cAST(FORMAT(@labDate, 'd', 'en-US' ) AS nvarchar(50))+')', -1,1,1,@labsValues+@splitByNewLine,@labsDisplayOrder,@addedDisplay + @labsDisplayOrder);	
		END
	END
	ELSE -- for checking unsaved hl7
	BEGIN
		
		DECLARE @hl7labResultDate DATETIME2 = null;
		DECLARE @hl7collectionDate DATETIME2 = null;
		DECLARE @hl7ReportId INT;

		SELECT TOP 1 @hl7labResultDate = l.LabResultDate, @hl7collectionDate = r.collectionDateTime, @hl7ReportId = ISNULL(l.HL7ReportId,0)
		FROM VPLabResults l 
		LEFT JOIN HL7Report r ON l.HL7ReportId = r.Id
		WHERE l.AppointmentTestSaveLogId = @appointmentTestLogId

		IF @hl7labResultDate IS NOT NULL AND @hl7ReportId > 0
		BEGIN
			DECLARE @hl7labsValues nvarchar(max);
			DECLARE @hl7labsDisplayOrder int = (ISNULL((SELECT Max(DisplayOrder) FROM @tempCategories),0) + 1);

			SELECT @hl7labsValues = COALESCE(@hl7labsValues + @splitByComma, '') + cl.[Name] + ' ' + ISNULL(cl.testResult,'') + ' ' + ISNULL(cl.Units,'')
			FROM
			(
				SELECT DISTINCT cm.[Name], cm.Units, cm.DisplayOrder, RES.testResult
				FROM fn_GetCustomMeasurements(@externalDoctorId,1,1) cm
				JOIN HL7Coding C ON LOWER(LTRIM(RTRIM(cm.Testcode)))=LOWER(LTRIM(RTRIM(C.LOINC)))
				JOIN HL7Result RES ON LOWER(LTRIM(RTRIM(C.labCode)))=LOWER(LTRIM(RTRIM(RES.testCodeIdentifier))) 
											OR LOWER(LTRIM(RTRIM(cm.Testcode)))=LOWER(LTRIM(RTRIM(RES.testCodeIdentifier)))		
				JOIN HL7ReportVersion rv ON  RES.HL7ReportVersionId=rv.Id 
				WHERE  ( ISNUMERIC(RES.testResult)=1 AND LTRIM(RTRIM(RES.testResult))<>'' AND RES.resultStatus='F') 
				AND rv.HL7ReportId=@hl7ReportId
								
			) cl
			ORDER BY cl.DisplayOrder

			UPDATE @tempCategories SET DisplayOrder = DisplayOrder + 1 
			WHERE DisplayOrder >= @hl7labsDisplayOrder;		

			INSERT INTO @tempCategories(CategoryNameOriginal,CategoryNameCustom,RootCategoryId,IsVisibleLabel,IsCustomInsert,SavedValue,DisplayOrder, OriginalDisplayOrder)
			VALUES('Labs','Labs (Result Date: '+ cAST(FORMAT(@hl7labResultDate, 'd', 'en-US' ) AS nvarchar(50))+')', -1,1,1,@hl7labsValues+@splitByNewLine,@hl7labsDisplayOrder,@addedDisplay + @hl7labsDisplayOrder);
		END
	END



	-- cpp
	IF @hasPhysicalExam = 1
	BEGIN		
		DECLARE @cppDisplay INT;
		DECLARE @totalCPP INT;
		DECLARE @tempCPP TABLE
		(
			Id int identity(1,1) not null,
			CategoryId int not null,
			CategoryName nvarchar(1500),	
			SavedValue varchar(8000)	
		);

		DECLARE @tempCPPSaved TABLE
		(
			Id int identity(1,1) not null,			
			CategoryName nvarchar(1500),	
			SavedValue varchar(8000)	
		);

		SET @physicalExamDisplayOrder = (SELECT DisplayOrder FROM @tempCategories WHERE RootCategoryId = @physicalExamId);	

		INSERT INTO @tempCPP
		SELECT cpp.CategoryId, dbo.fn_UpperCaseFirstLetter(ISNULL(cpp.CategorynName,'')), cpp.SavedValue 
		FROM dbo.fn_GetReportCPPData(@appointmentTestId,@practiceId,@officeId,@appointmentId,@patientId,@appointmentTime,@practiceDoctorId,@externalDoctorId) cpp
		JOIN VP_CPP_Setting st ON cpp.CategoryId = st.VP_CPP_Category_Id
		JOIN VP_CPP_Skipped sk ON cpp.CategoryId = sk.CPP_Category_ID
		WHERE cpp.SavedValue IS NOT NULL AND cpp.SavedValue != ''
		AND sk.UserID = @externalDoctorId AND sk.[Skip] = 0
		AND st.DoctorID = @externalDoctorId AND st.Visible = 1
		ORDER By st.[Order]
		
		INSERT INTO @tempCPPSaved(CategoryName,SavedValue)
		SELECT cpp.CategoryName, SavedValues = STUFF((SELECT SavedValue +char(10)--STUFF((SELECT N', ' + SavedValue
		  FROM @tempCPP AS cpp2
		   WHERE cpp2.CategoryName = cpp.CategoryName		   
		   FOR XML PATH(N''), TYPE).value(N'.[1]', N'nvarchar(max)'), 1, 0, N'')
		   --FOR XML PATH(N''), TYPE).value(N'.[1]', N'nvarchar(max)'), 1, 2, N'')
		FROM @tempCPP AS cpp
		GROUP BY cpp.CategoryName
				
		SET @totalCPP =ISNULL((SELECT COUNT(*) FROM @tempCPPSaved tp),0);
		IF @totalCPP > 0
		BEGIN

		UPDATE @tempCategories SET DisplayOrder = DisplayOrder + @totalCPP
			WHERE DisplayOrder >= @physicalExamDisplayOrder;
			SET @cppDisplay = @physicalExamDisplayOrder;

		INSERT INTO @tempCategories(CategoryNameOriginal,CategoryNameCustom,RootCategoryId,IsVisibleLabel,IsCustomInsert,SavedValue,DisplayOrder, OriginalDisplayOrder)
		SELECT
		cpp.CategoryName,cpp.CategoryName,-1,1,1,cpp.SavedValue,(@cppDisplay + cpp.Id)-1, @addedDisplay + (@cppDisplay + cpp.Id)-1
		FROM @tempCPPSaved cpp;		
			
		END;
	END;

	IF @hasClosingStatment = 1
	BEGIN
		SET @closingStatementDisplayOrder = (ISNULL((SELECT Max(DisplayOrder) FROM @tempCategories),0) + 1);
		UPDATE @tempCategories SET DisplayOrder = @closingStatementDisplayOrder WHERE RootCategoryId = @closingStatementId;

	END


	SELECT 
	trc.CategoryNameCustom,
	trc.CategoryNameOriginal,
	trc.SavedValue,
	trc.RootCategoryId,
	trc.IsVisibleLabel,
	trc.IsCustomInsert,	
	trc.DisplayOrder
	FROM
	@tempCategories trc
	ORDER BY trc.DisplayOrder
END