﻿

CREATE PROCEDURE [dbo].[GetExternalDocumentList_old_ticket12103]
	@patientRecordId int,
	@practiceDoctorId int,
	@practiceId int,
	@rowStart int,
	@rowCount int,
	@totalRowRequest int,
	@sortByColumn varchar(32),
	@sortByOrder varchar(8),
	@filterIncludeHL7 bit,
	@filterIncludeFax bit,
	@filterIncludeHRM bit,
	@filterIncludeScan bit,
	@filterClassId int,
	@filterCategoryId int,
	@filterIncludeMarkseen bit,
	@filterMarkseenStart varchar(16),
	@filterMarkseenEnd varchar(16),
	@filterReceivedDate varchar(16),
	@filterTestDate varchar(16),
	@filterPatient varchar(256),
	@filterDescription varchar(256),
	@filterComment varchar(256)
AS
BEGIN
	SET NOCOUNT ON;

	SET @filterMarkseenStart = ''	--remove start date for markseen

	DECLARE @totalRow int
	DECLARE @sql varchar(8000)
	DECLARE @sqlCounter nvarchar(max)
	DECLARE @selectSql varchar(4000)
	DECLARE @tableSql varchar(2048)
	DECLARE @whereSql varchar(2048)
	DECLARE @orderBySql varchar(64)
	DECLARE @offsetFetchSql varchar(64)
	DECLARE @orderByColumn varchar(64)

	IF OBJECT_ID('tempdb..##tempDocumentList') IS NOT NULL
		DROP TABLE #tempDocumentList

	Create table #tempDocumentList(
		id int,
		arrivedAfterLastAppointment bit,
		abnormal bit,
		markSeen bit,
		lastAppointmentDate datetime2,
		receivedDateTime datetime2,
		testDateTime datetime2,
		patientFirstName varchar(100),
		patientLastName varchar(100),
		demographicId int,
		patientRecordId int,
		description nvarchar(1024),
		comment nvarchar(1024),
		documentType nvarchar(10),
		hrmFacilityId nvarchar(128),
		hrmReportNumber nvarchar(512),
		isActive bit
	)

	SET @sql = ''
	IF (@filterIncludeFax = 1)
	BEGIN
		SET @selectSql = 'select aa.id,(cast(case when aa.abnormal is null then 0 else aa.abnormal end as bit)) abnormal,aa.receivedDateTime,(case when aa.testDate is null then aa.ObservationDateTime else aa.testDate end) testDateTime'
		SET @selectSql = @selectSql + ',ac.lastName patientLastName,ac.firstName patientFirstName,ac.id demographicId,ac.patientRecordId,aa.description'
		SET @selectSql = @selectSql + ',''fax'' documentType,aa.looseReportCategoryId categoryId,aa.reportClassId classId,aa.sendingFacilityId hrmFacilityId'
		SET @selectSql = @selectSql + ',aa.sendingFacilityReport hrmReportNumber,(case when aa.status is null then 1 else aa.status end) isActive'

		SET @tableSql = ' from ReportReceiveds aa inner join PatientRecords ab on aa.PatientRecordId=ab.id inner join Demographics ac on aa.PatientRecordId=ac.PatientRecordId'
		SET @whereSql = FORMATMESSAGE(' where aa.reportType=0 and (aa.status is null or aa.status=1) and aa.assignmentStatus <>2 and ab.practiceid=%d',@practiceId)

		IF (@patientRecordId = 0)
		BEGIN
			SET @selectSql = @selectSql + ',(case when ad.dateTimeReportReviewed is null then 0 else 1 end) markSeen,ad.dateTimeReportReviewed markSeenDateTime,ad.ReportReviewedNotes comment'
			SET @tableSql = @tableSql + ' inner join DoctorsReportRevieweds ad on ad.ReportReceivedId=aa.id inner join PracticeDoctors ae on ad.practiceDoctorId=ae.id'
			SET @whereSql =  @whereSql + FORMATMESSAGE(' and ad.practiceDoctorId=%d and ae.practiceid=%d',@practiceDoctorId,@practiceId)
		END
		ELSE
		BEGIN
			SET @selectSql = @selectSql + ',(case when ad.dateTimeReportReviewed is null then 0 else 1 end) markSeen,ad.dateTimeReportReviewed markSeenDateTime,ad3.ReportReviewedNotes comment'
			SET @tableSql = @tableSql + ' left join DoctorsReportRevieweds ad on aa.id=ad.ReportReceivedId and ad.dateTimeReportReviewed is not null and ad.id=(select top 1 id from DoctorsReportRevieweds ad2 where aa.id=ad2.ReportReceivedId and ad2.dateTimeReportReviewed is not null order by ad2.dateTimeReportReviewed desc)'
			SET @tableSql = @tableSql + ' left join DoctorsReportRevieweds ad3 on aa.id=ad3.ReportReceivedId and ad3.ReportReviewedNotes is not null and ad3.id=(select top 1 id from DoctorsReportRevieweds ad4 where aa.id=ad4.ReportReceivedId and ad4.ReportReviewedNotes is not null order by ad4.dateTimeReportReviewed desc)'
			SET @whereSql = @whereSql + FORMATMESSAGE(' and aa.PatientRecordId=%d',@patientRecordId)
		END
		
		IF (@filterClassId <> 0)
			SET @whereSql = @whereSql + FORMATMESSAGE(' and aa.reportClassId=%d',@filterClassId)
		IF (@filterCategoryId <> 0)
			SET @whereSql = @whereSql + FORMATMESSAGE(' and aa.looseReportCategoryId=%d',@filterCategoryId)

		SET @sql = @selectSql + @tableSql + @whereSql
	END

	IF (@filterIncludeHL7 = 1)
	BEGIN
		SET @selectSql = 'select ba.id,cast ((case when bi.abnormalFlag is null then 0 else 1 end)as bit) abnormal,ba.createdDate receivedDateTime,ba.collectionDateTime testDateTime'
		SET @selectSql = @selectSql + ',bd.lastName patientLastName,bd.firstName patientFirstName,bd.id demographicId,bd.patientRecordId,''Lab HL7'' description'
		SET @selectSql = @selectSql + ',''hl7'' documentType,0 categoryId,0 classId,'''' hrmFacilityId'
		SET @selectSql = @selectSql + ','''' hrmReportNumber,1 isActive'

		SET @tableSql = ' from HL7Report ba inner join HL7Patient bb on ba.HL7PatientId=bb.id inner join PatientRecords bc on bb.PatientRecordId=bc.id inner join Demographics bd on bc.Id=bd.PatientRecordId'
		SET @whereSql = FORMATMESSAGE(' where bc.practiceid=%d',@practiceId)
		IF (@patientRecordId = 0)
		BEGIN
			SET @tableSql = @tableSql + ' inner join HL7ReportDoctor be on ba.Id=be.HL7ReportId inner join PracticeDoctors bf on be.practiceDoctorId=bf.Id'
			SET @whereSql = @whereSql + FORMATMESSAGE(' and bf.Id=%d and bf.practiceid=%d',@practiceDoctorId,@practiceId)
		END
		ELSE
			SET @whereSql = @whereSql + FORMATMESSAGE(' and bc.Id=%d',@patientRecordId)

		SET @tableSql = @tableSql + ' left join (select HL7ReportId,abnormalFlag, bh.id HL7ResultId from HL7ReportVersion bg inner join HL7Result bh on bg.Id=bh.HL7ReportVersionId where bh.abnormalFlag<>''N'' and bh.abnormalFlag<>''No'' and bh.abnormalFlag<>''Normal'') bi on bi.HL7ReportId=ba.Id and bi.HL7ResultId=(select top 1 bhh.id HL7ResultId from HL7ReportVersion bgg inner join HL7Result bhh on bgg.Id=bhh.HL7ReportVersionId where bgg.HL7ReportId=ba.id and bhh.abnormalFlag<>''N'' and bhh.abnormalFlag<>''No'' and bhh.abnormalFlag<>''Normal'')'

		SET @selectSql = @selectSql + ',(case when bj.seenAt is null then 0 else 1 end) markSeen,bj.seenAt markSeenDateTime'

		IF (@patientRecordId = 0)
		BEGIN
			SET @selectSql = @selectSql + ',bj.comment'
			SET @tableSql = @tableSql + FORMATMESSAGE(' left join HL7MarkedSeen bj on bj.HL7ReportId=ba.Id and bj.practiceDoctorId=%d and bj.id=(select top 1 id from HL7MarkedSeen bk where bk.HL7ReportId=ba.Id and bk.practiceDoctorId=%d order by bk.seenAt desc)',@practiceDoctorId,@practiceDoctorId)
		END	
		ELSE
		BEGIN
			SET @selectSql = @selectSql + ',bl.comment'
			SET @tableSql = @tableSql + ' left join HL7MarkedSeen bj on bj.HL7ReportId=ba.Id and bj.id=(select top 1 id from HL7MarkedSeen bk where bk.HL7ReportId=ba.Id order by bk.seenAt desc)'
			SET @tableSql = @tableSql + ' left join HL7MarkedSeen bl on bl.HL7ReportId=ba.Id and bl.comment is not null and bl.comment<>'''' and bl.id=(select top 1 id from HL7MarkedSeen bm where bm.HL7ReportId=ba.Id order by bm.seenAt desc)'
		END	

		IF (@sql <> '')
			SET @sql = @sql + ' UNION ALL '
		SET @sql = @sql + @selectSql + @tableSql + @whereSql
	END

	IF (@filterIncludeHRM = 1)
	BEGIN
		SET @selectSql = 'select ca.id,(cast(case when ca.abnormal is null then 0 else ca.abnormal end as bit)) abnormal,ca.receivedDateTime,(case when ca.testDate is null then ca.ObservationDateTime else ca.testDate end) testDateTime'
		SET @selectSql = @selectSql + ',cc.lastName patientLastName,cc.firstName patientFirstName,cc.id demographicId,cc.patientRecordId,ca.description'
		SET @selectSql = @selectSql + ',''hrm'' documentType,ca.looseReportCategoryId categoryId,ca.reportClassId classId,ca.sendingFacilityId hrmFacilityId'
		SET @selectSql = @selectSql + ',ca.sendingFacilityReport hrmReportNumber,(case when ca.status is null then 1 else ca.status end) isActive,hrmVersion'

		SET @tableSql = ' from ReportReceiveds ca inner join PatientRecords cb on ca.PatientRecordId=cb.id inner join Demographics cc on ca.PatientRecordId=cc.PatientRecordId'
		SET @whereSql = FORMATMESSAGE(' where ca.reportType=2 and (ca.status is null or ca.status=1) and ca.assignmentStatus<>2 and cb.practiceid=%d',@practiceId)

		IF (@patientRecordId = 0)
		BEGIN
			SET @selectSql = @selectSql + ',(case when cd.dateTimeReportReviewed is null then 0 else 1 end) markSeen,cd.dateTimeReportReviewed markSeenDateTime,cd.ReportReviewedNotes comment'
			SET @tableSql = @tableSql + ' inner join DoctorsReportRevieweds cd on cd.ReportReceivedId=ca.id inner join PracticeDoctors ce on cd.practiceDoctorId=ce.id'
			SET @whereSql =  @whereSql + FORMATMESSAGE(' and cd.practiceDoctorId=%d and ce.practiceid=%d',@practiceDoctorId,@practiceId)
		END
		ELSE
		BEGIN
			SET @selectSql = @selectSql + ',(case when cd.dateTimeReportReviewed is null then 0 else 1 end) markSeen,cd.dateTimeReportReviewed markSeenDateTime,cd3.ReportReviewedNotes comment'
			SET @tableSql = @tableSql + ' left join DoctorsReportRevieweds cd on ca.id=cd.ReportReceivedId and cd.dateTimeReportReviewed is not null and cd.id=(select top 1 id from DoctorsReportRevieweds cd2 where ca.id=cd2.ReportReceivedId and cd2.dateTimeReportReviewed is not null order by cd2.dateTimeReportReviewed desc)'
			SET @tableSql = @tableSql + ' left join DoctorsReportRevieweds cd3 on ca.id=cd3.ReportReceivedId and cd3.ReportReviewedNotes is not null and cd3.id=(select top 1 id from DoctorsReportRevieweds cd4 where ca.id=cd4.ReportReceivedId and cd4.ReportReviewedNotes is not null order by cd4.dateTimeReportReviewed desc)'
			SET @whereSql = @whereSql + FORMATMESSAGE(' and ca.PatientRecordId=%d',@patientRecordId)
		END

		SET @selectSql = 'select ROW_NUMBER() over (partition by hrmFacilityId,hrmReportNumber,patientRecordId order by hrmVersion desc) as rn,* from (' + @selectSql + @tableSql + @whereSql + ') hrm '
		SET @whereSql = ''

		IF (@filterIncludeMarkseen = 0)
			SET @whereSql = @whereSql +' and (hrm.markSeen is null or hrm.markSeen=0)'
		ELSE
		BEGIN
			IF (@filterMarkseenStart <> '')
				SET @whereSql = @whereSql + FORMATMESSAGE(' and ((hrm.markSeen is null or hrm.markSeen=0) or (convert(varchar(8),hrm.markSeenDateTime,112)>=''%s''))',@filterMarkseenStart)
			IF (@filterMarkseenEnd <> '')
				SET @whereSql = @whereSql + FORMATMESSAGE(' and ((hrm.markSeen is null or hrm.markSeen=0) or (convert(varchar(8),hrm.markSeenDateTime,112)<=''%s''))',@filterMarkseenEnd)
		END

		IF (@filterClassId <> 0)
			SET @whereSql = @whereSql + FORMATMESSAGE(' and hrm.classId=%d',@filterClassId)
		IF (@filterCategoryId <> 0)
			SET @whereSql = @whereSql + FORMATMESSAGE(' and hrm.categoryId=%d',@filterCategoryId)

		IF ( @whereSql <> '')
		BEGIN
			SET @whereSql = STUFF(@whereSql, CHARINDEX(' and', @whereSql), LEN(' and'), ' where')
			SET @selectSql = @selectSql + @whereSql
		END
		SET @selectSql = 'select id,abnormal,receivedDateTime,testDateTime,patientLastName,patientFirstName,demographicId,patientRecordId,description,documentType,categoryId,classId,hrmFacilityId,hrmReportNumber,isActive,markSeen,markSeenDateTime,comment from (' + @selectSql + ') hrm2 where rn=1'

		IF (@sql <> '')
			SET @sql = @sql + ' UNION ALL '
		SET @sql = @sql + @selectSql
	END

	SET @sql = '(' + @sql + ') z'
	SET @whereSql = ''

	--filter by mark seen
	IF (@filterIncludeMarkseen = 0)
		SET @whereSql = @whereSql +' and (markSeen is null or markSeen=0)'
	ELSE
	BEGIN
		IF (@filterMarkseenStart <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and ((markSeen is null or markSeen=0) or (convert(varchar(8),markSeenDateTime,112)>=''%s''))',@filterMarkseenStart)
		IF (@filterMarkseenEnd <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and ((markSeen is null or markSeen=0) or (convert(varchar(8),markSeenDateTime,112)<=''%s''))',@filterMarkseenEnd)
	END

	IF (@filterReceivedDate <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(' and convert(varchar(8),receivedDateTime,112)=''%s''',@filterReceivedDate)
	IF (@filterTestDate <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(' and convert(varchar(8),testDateTime,112)=''%s''',@filterTestDate)
	IF (@filterPatient <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(' and (patientFirstName like ''%s'' or patientLastName like ''%s'')','%' + @filterPatient + '%','%' + @filterPatient + '%')
	IF (@filterDescription <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(' and description like ''%s''','%' + @filterDescription + '%')
	IF (@filterComment <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(' and comment like ''%s''','%' + @filterComment + '%')
					
	IF ( @whereSql <> '')
	BEGIN
		SET @whereSql = STUFF(@whereSql, CHARINDEX(' and', @whereSql), LEN(' and'), ' where')
		SET @sql = @sql + @whereSql
	END

	SET @totalRow = -1
	IF (@totalRowRequest = 1)
	BEGIN
		SET @sqlCounter = 'select @total=count(*) from ' + @sql
		EXEC sp_executesql @sqlCounter, N'@total int OUTPUT', @total = @totalRow OUTPUT
	END

	IF (@totalRow <> 0)
	BEGIN
		SET @sql = 'select id,abnormal,markSeen,receivedDateTime,testDateTime,patientFirstName,patientLastName,demographicId,patientRecordId,description,comment,documentType,hrmFacilityId,hrmReportNumber,isActive from ' + @sql
		SET @offsetFetchSql = FORMATMESSAGE(' offset %d rows fetch next %d rows only',@rowStart,@rowCount)
		SET @orderByColumn = (CASE WHEN @sortByColumn='receiveddate' THEN 'receivedDateTime'
									WHEN @sortByColumn='abnormal' THEN 'abnormal'
									WHEN @sortByColumn='patient' THEN 'patientName'
									WHEN @sortByColumn='description' THEN 'description'
									ELSE 'testDateTime' END)
		SET @orderBySql = ' order by ' + @orderByColumn + (CASE WHEN @sortByOrder='asc' THEN ' asc' ELSE ' desc' END) + ', id ' + (CASE WHEN @sortByOrder='asc' THEN ' asc' ELSE ' desc' END)
		SET @sql = @sql + @orderBySql + @offsetFetchSql
		EXEC ('insert into #tempDocumentList(id,abnormal,markSeen,receivedDateTime,testDateTime,patientFirstName,patientLastName,demographicId,patientRecordId,description,comment,documentType,hrmFacilityId,hrmReportNumber,isActive) ' + @sql)

		update #tempDocumentList set lastAppointmentDate=a.appointmentTime from (select max(appointmentTime) appointmentTime, PatientRecordId from (select appointmentTime,PatientRecordId from appointments where appointmentTime<getdate() and PatientRecordId in (select patientRecordId from #tempDocumentList)) f group by PatientRecordId) a inner join #tempDocumentList b on a.PatientRecordId=b.patientRecordId
		update #tempDocumentList set arrivedAfterLastAppointment=(case when DATEDIFF(day,lastAppointmentDate,receivedDateTime)>0 then 1 else 0 end)
	END

	select id,arrivedAfterLastAppointment,abnormal,markSeen,receivedDateTime,testDateTime,patientFirstName,patientLastName,demographicId,patientRecordId,description,comment,documentType,hrmFacilityId,hrmReportNumber,isActive from #tempDocumentList
	DROP TABLE #tempDocumentList

	return @totalRow
END
