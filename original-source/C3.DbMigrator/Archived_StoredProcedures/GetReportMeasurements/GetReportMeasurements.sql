﻿CREATE PROCEDURE [dbo].[GetReportMeasurements]
	@AppointmentTestsId int,
	@appointmentTestLogId INT = 0
AS
BEGIN
	SET NOCOUNT ON;

   IF @appointmentTestLogId <= 0
   BEGIN
		SET @appointmentTestLogId = (SELECT MAX(L.Id) FROM AppointmentTestLogs L JOIN AppointmentTests T ON L.AppointmentId = T.AppointmentId WHERE T.Id = @AppointmentTestsId AND L.Status = 0)
   END  

SET ANSI_WARNINGS  OFF;
CREATE TABLE #Measurements
(
	MeasurementCategoriesId int,
	AppointmentTestsId int,
	MeasurementCategoriesName VARCHAR (100),
	units varchar(20),
	MeasurementsName VARCHAR (100),
	CategoryOrder int,
	MeasurementOrder int,
	Visible bit,
	GenderStr varchar(20),
	Value varchar(100),
	measurementCode varchar(100),
	categoryCode varchar(100),
	Range varchar(30),
	gender int,
	Range1 decimal(10,3),
	Range2 decimal(10,3)
)


CREATE TABLE #MeasurementsCounter
(
	MeasurementCategoriesId int,
	AppointmentTestsId int,
	MeasurementCategoriesName VARCHAR (100),
	units varchar(8) ,
	MeasurementsName VARCHAR (100),
	CategoryOrder int,
	MeasurementOrder int,
	Visible bit,
	GenderStr varchar(20),
	Value varchar(100),
	measurementCode varchar(100),
	categoryCode varchar(100),
	Range varchar(30),
	gender int,
	Range1 decimal(10,3),
	Range2 decimal(10,3),
	Counter int,
	rn int
)

INSERT INTO #Measurements
         SELECT dbo.MeasurementCategories.Id AS MeasurementCategoriesId
			    , dbo.AppointmentTests.Id AS AppointmentTestsId
			    , dbo.MeasurementCategories.name AS MeasurementCategoriesName
			    , dbo.Measurements.units, dbo.Measurements.name AS MeasurementsName
			    , dbo.MeasurementCategories.[order] AS CategoryOrder
			    , dbo.Measurements.[order] AS MeasurementOrder
			    , dbo.MeasurementByPractices.Visible
			    , (CASE WHEN g.gender = 0 THEN 'M' ELSE 'F' END) AS GenderStr
				 , (case when isNumeric(Try_convert(DECIMAL(10,3), dbo.MeasurementSavedValues.Value))=1 then 
				      format(Try_convert(DECIMAL(10,3), dbo.MeasurementSavedValues.Value), coalesce(mask,'0.00'))   
				  else dbo.MeasurementSavedValues.Value end) as Value
			    --, dbo.MeasurementSavedValues.Value
			    , dbo.Measurements.measurementCode
			    , dbo.MeasurementCategories.categoryCode
--                ,IsNull((CASE WHEN rn.MeasurementRangeTypeId is not null THEN
	            , IsNull(
                    '(' + CAST(CAST(rn.Range1 AS DECIMAL(7 , 2)) AS VARCHAR(20)) + '-' + CAST(CAST(rn.Range2 AS DECIMAL(7 , 2)) AS VARCHAR(20)) + ')' 
--                       ELSE '' END)
                  , '') as Range
                ,g.gender
--                ,(CASE WHEN rn.Range1 <= Try_convert(DECIMAL(10,3), dbo.MeasurementSavedValues.Value) AND rn.Range2 > Try_convert(DECIMAL(10,3), dbo.MeasurementSavedValues.Value) THEN
--                ,(CASE WHEN rn.MeasurementRangeTypeId is not null THEN
                , IsNull(
	                   CAST(CAST(rn.Range1 AS DECIMAL(7 , 2)) AS VARCHAR(20))
--                        ELSE '' END)
                  , '') as Range1
--                ,(CASE WHEN rn.Range1 <= Try_convert(decimal(10,3), dbo.MeasurementSavedValues.Value) AND rn.Range2 > Try_convert(DECIMAL(10,3), dbo.MeasurementSavedValues.Value) THEN
--                ,(CASE WHEN rn.MeasurementRangeTypeId is not null THEN
                , IsNull(
	                    CAST(CAST(rn.Range2 AS DECIMAL(7 , 2)) AS VARCHAR(20)) 
--                        ELSE '' END) Range2
                  , '') as Range2
           FROM dbo.AppointmentTests 
     INNER JOIN dbo.Appointments ON dbo.AppointmentTests.AppointmentId = dbo.Appointments.Id 
     --INNER JOIN (SELECT MAX(Id) AS AppointmentTestLogsId, AppointmentID, TestID
			  --     FROM dbo.AppointmentTestLogs
	    --       GROUP BY Status, AppointmentID, TestID
		   --      HAVING (Status = 0)) as P ON dbo.AppointmentTests.AppointmentId = P.AppointmentID AND dbo.AppointmentTests.TestId = P.TestID 
	 INNER JOIN dbo.AppointmentTestLogs AS P ON dbo.AppointmentTests.AppointmentId = P.AppointmentID
     INNER JOIN dbo.MeasurementSavedValues ON P.Id = dbo.MeasurementSavedValues.AppointmentTestLogID 
     INNER JOIN dbo.Measurements ON dbo.MeasurementSavedValues.MeasurementId = dbo.Measurements.Id 
     INNER JOIN dbo.MeasurementCategories ON dbo.Measurements.MeasurementCategoryID = dbo.MeasurementCategories.Id 
     INNER JOIN dbo.MeasurementByPractices ON dbo.Measurements.Id = dbo.MeasurementByPractices.MeasurementID 
     INNER JOIN dbo.PatientRecords ON dbo.Appointments.PatientRecordId = dbo.PatientRecords.Id AND dbo.MeasurementByPractices.PracticeID = dbo.PatientRecords.PracticeId 
     INNER JOIN dbo.Demographics as G ON dbo.Appointments.PatientRecordId = G.PatientRecordId
      LEFT JOIN MeasurementRanges rn ON (dbo.Measurements.measurementCode = rn.measurementCode AND  dbo.MeasurementCategories.categoryCode = rn.categoryCode AND (CASE WHEN g.gender = 0 THEN 'M' ELSE 'F' END) = rn.gender AND rn.MeasurementRangeTypeId = 1)
          WHERE
		   (dbo.AppointmentTests.Id =  @AppointmentTestsId) AND
		   (dbo.MeasurementByPractices.Visible = 1) and
		   (dbo.Measurements.status = 0) and
		   (P.Id = @appointmentTestLogId)
--		   (dbo.MeasurementSavedValues.Value is not null) and
--		   (replace(dbo.MeasurementSavedValues.Value,' ','')) and
    UNION
         SELECT  dbo.MeasurementCategories.Id AS MeasurementCategoriesId, 
		         dbo.AppointmentTests.Id AS AppointmentTestsId, 
		         dbo.MeasurementCategories.name AS MeasurementCategoriesName, 
		         dbo.Measurements.units + '/' + V.units, 
                 dbo.Measurements.name + N' Index' AS MeasurementsName, 
		         dbo.MeasurementCategories.[order] AS CategoryOrder, 
		         dbo.Measurements.[order] AS MeasurementOrder, 
		         dbo.MeasurementByPractices.Visible, 
		         G.GenderStr,
		         Cast(Cast(Try_convert(decimal(6,2),dbo.MeasurementSavedValues.Value)/Try_convert(decimal(6,2),V.Val)as decimal(6,2))as varchar(20)) AS Value, 
		         dbo.Measurements.measurementCode + 'I', 
		         dbo.MeasurementCategories.categoryCode, 
		         '( ' + CAST(dbo.MeasurementBSARanges.IndexedRange1 AS VARCHAR(5)) + '-' + CAST(dbo.MeasurementBSARanges.IndexedRange2 AS VARCHAR(5)) + ')' AS Range, 
		         G.gender,
		         dbo.MeasurementBSARanges.IndexedRange1 AS Range1, 
		         dbo.MeasurementBSARanges.IndexedRange2 AS Range2
            FROM dbo.AppointmentTests 
      INNER JOIN dbo.Appointments ON dbo.AppointmentTests.AppointmentId = dbo.Appointments.Id 
      --INNER JOIN (SELECT MAX(Id) AS AppointmentTestLogsId, 
				 	--     AppointmentID, 
					 --    TestID
      --              FROM dbo.AppointmentTestLogs
      --          GROUP BY Status, AppointmentID, TestID
      --            HAVING (Status = 0)) AS P  ON	dbo.AppointmentTests.AppointmentId = P.AppointmentID AND dbo.AppointmentTests.TestId = P.TestID 
	  INNER JOIN dbo.AppointmentTestLogs AS P ON dbo.AppointmentTests.AppointmentId = P.AppointmentID
      INNER JOIN dbo.MeasurementSavedValues ON P.Id = dbo.MeasurementSavedValues.AppointmentTestLogID 
      INNER JOIN dbo.Measurements ON dbo.MeasurementSavedValues.MeasurementId = dbo.Measurements.Id
      INNER JOIN dbo.MeasurementCategories ON dbo.Measurements.MeasurementCategoryID = dbo.MeasurementCategories.Id 
      INNER JOIN dbo.MeasurementByPractices ON dbo.Measurements.Id = dbo.MeasurementByPractices.MeasurementID 
      INNER JOIN dbo.PatientRecords ON dbo.Appointments.PatientRecordId = dbo.PatientRecords.Id AND dbo.MeasurementByPractices.PracticeID = dbo.PatientRecords.PracticeId 
      INNER JOIN (SELECT (CASE WHEN gender = 0 THEN 'M' ELSE 'F' END) AS GenderStr, gender, PatientRecordId
                    FROM dbo.Demographics) AS G ON dbo.Appointments.PatientRecordId = G.PatientRecordId 
      INNER JOIN (SELECT DISTINCT dbo.MeasurementSavedValues.AppointmentID, dbo.MeasurementSavedValues.AppointmentTestLogID,
                                  CAST(dbo.MeasurementSavedValues.Value AS DECIMAL(12, 2)) AS Val, units
                    FROM dbo.MeasurementSavedValues 
              INNER JOIN dbo.Measurements ON dbo.Measurements.Id = dbo.MeasurementSavedValues.MeasurementId 
                   WHERE (dbo.Measurements.name = N'BSA') AND (IsNumeric(dbo.MeasurementSavedValues.Value) = 1)) as V  ON (dbo.MeasurementSavedValues.AppointmentID = V.AppointmentID AND dbo.MeasurementSavedValues.AppointmentTestLogID = V.AppointmentTestLogID )
      INNER JOIN dbo.MeasurementBSARanges ON dbo.Measurements.measurementCode = dbo.MeasurementBSARanges.measurementCode AND dbo.MeasurementCategories.categoryCode = dbo.MeasurementBSARanges.categoryCode AND G.GenderStr = dbo.MeasurementBSARanges.gender
           WHERE (dbo.AppointmentTests.Id = @AppointmentTestsId) AND (dbo.MeasurementByPractices.Visible = 1) AND (dbo.MeasurementBSARanges.MeasurementRangeTypeId = 1)
		   AND (P.Id = @appointmentTestLogId)

INSERT INTO  #MeasurementsCounter
Select
    MeasurementCategoriesId ,
	AppointmentTestsId ,
	MeasurementCategoriesName,
	units ,
	MeasurementsName,
	CategoryOrder ,
	MeasurementOrder,
	Visible ,
	GenderStr ,
	Value ,
	measurementCode,
	categoryCode,
	Range,
	gender,
	Range1,
	Range2 , 						 
    COUNT(*) OVER (partition by MeasurementCategoriesName) Counter,
	ROW_NUMBER() over (partition by MeasurementCategoriesName order by MeasurementCategoriesName) rn
from (
    Select
        MeasurementCategoriesId ,
	    AppointmentTestsId ,
	    MeasurementCategoriesName,
	    units ,
	    MeasurementsName,
	    CategoryOrder ,
	    MeasurementOrder,
	    Visible ,
	    GenderStr ,
	    Value ,
	    measurementCode,
	    categoryCode,
	    Range,
	    gender,
	    Range1,
	    Range2 , 						
        ROW_NUMBER() over (partition by measurementCode, categoryCode order by Try_convert(decimal(6,2),[Value]) desc) rnum
    from  #Measurements
    where [Value] is not null and len(replace([Value],' ', ''))>0) z
where rnum = 1

SELECT *,  (Case when Range1 > 0 and Range2 > 0 then 1 else 0 end) as HasRanges 
  FROM  #MeasurementsCounter
 UNION
SELECT MeasurementCategoriesId, 
       AppointmentTestsId, 
       MeasurementCategoriesName, 
       '' AS units, 
       '' AS MeasurementsName, 
       CategoryOrder, 
       '99999999' AS MeasurementOrder,	-- to have it always last
       '' AS Visible, 
       '' AS GenderStr, 
       '' AS Value,
       '' AS measurementCode, 
       categoryCode, 
       '' AS Range, 
       '' AS gender,  
       0 as Range1, 
       0 as Range2,  
       Counter + 1 AS Counter, 
       MAX(rn + 1) AS rn, 
       0
  FROM ( select  * from  #MeasurementsCounter) as M
GROUP BY  MeasurementCategoriesName,categoryCode,CategoryOrder, 
          Counter + 1, 
          Counter % 2,
          MeasurementCategoriesId, 
          AppointmentTestsId
   HAVING (Counter % 2 <> 0)
 ORDER BY CategoryOrder, MeasurementOrder ;

SET ANSI_WARNINGS  ON;

drop table  #Measurements
drop table  #MeasurementsCounter
	
End
