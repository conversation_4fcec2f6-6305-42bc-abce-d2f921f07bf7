﻿CREATE PROCEDURE [dbo].[GetDashboardMEDOPI003] (@PracticeDoctorId INT = NULL)
AS
BEGIN	
		SET NOCOUNT ON

		       --Declare @PracticeDoctorId as int = 480
       --Declare @SegmentID as int = 3
	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @Medications AS TABLE (PatientRecordId INT, MedicationName NVARCHAR(500),Ingredients NVARCHAR(500),Strength NVARCHAR(300), Dose NVARCHAR(300), Form NVARCHAR(300), SIG NVARCHAR(300), hasRequiredInformation BIT, StrengthNumberic FLOAT,DoseNumeric FLOAT)	
	DECLARE @MorphineEquivalent AS TABLE (PatientRecordId INT, ConversionFactor FLOAT, StrengthNumberic FLOAT, DoseNumeric FLOAT, DailyFrequency FLOAT, DailyDosage FLOAT, DailyMorphineEquivalent FLOAT)	
	DECLARE @MorphineEquivalentGroup AS TABLE (PatientRecordId INT, ConversionFactor FLOAT,DailyMorphineEquivalent FLOAT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)
	DECLARE @RxIncomplete AS TABLE (PatientRecordId INT)
	DECLARE @RxNoConvert AS TABLE (PatientRecordId INT)
              

              DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

              -- Loading Tables
              --- Base Population
              INSERT INTO @BasePopulation
			  SELECT        
              D.PatientRecordId              
              FROM Demographics D
              JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
              WHERE 
              D.active = 0 -- Patient is active
              AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
              --- Non-Paliative
              AND NOT EXISTS (SELECT 1 FROM BillDetails B WHERE B.PatientRecordId = D.PatientRecordId
                                         AND B.serviceCode IN ('A945', 'C945', 'C882', 'C982', 'W872', 'W882', 'W972', 'W982', 'K023', 'B998', 'B966', 'B997', 'G511', 'G512'))
              AND NOT EXISTS (SELECT 1 FROM VP_CPP_Problem_List CPP WHERE CPP.PatientRecordId = D.PatientRecordId
                                         AND CPP.DiagnosticCode = 'Z515' AND CPP.DiagnosticCode = 'Z515')
              GROUP BY D.PatientRecordId
       
-- select * from @BasePopulation order by PatientRecordId

-- Get all scripts that are incomplete (Segment 4)
   INSERT INTO @RxIncomplete
			  SELECT pm.PatientRecordId
              FROM @BasePopulation bp
              join [PatientMedications] PM on bp.PatientRecordId = pm.PatientRecordId
              WHERE
              	(
				MedicationName like '%Alfentanil%' or 
				MedicationName like '%Buprenorphin%' or 
				MedicationName like '%Butorphanol%' or 
				MedicationName like '%Codeine%' or 
				MedicationName like '%Dihydrocodeine%' or 
				MedicationName like '%Fentanyl%' or 
				MedicationName like '%Hydrocodone%' or 
				MedicationName like '%Meperidin%' or 
				MedicationName like '%Hydromorphone%' or 
				MedicationName like '%Methadone%' or 
				MedicationName like '%Morphine%' or 
				MedicationName like '%Nalbuphine%' or 
				MedicationName like '%Opium%' or 
				MedicationName like '%Oxycodone%' or 
				MedicationName like '%Oxymorphone%' or 
				MedicationName like '%Pentazocine%' or 
				MedicationName like '%Pethidine%' or 
				MedicationName like '%Remifentanil%' or 
				MedicationName like '%Sufentanil%' or 
				MedicationName like '%Tapentadol%' or 
				MedicationName like '%Tramadol%'
				or PM.Ingredients like '%Alfentanil%' 
				OR PM.Ingredients like '%Buprenorphin%'
				OR PM.Ingredients like '%Butorphanol%'
				OR PM.Ingredients like '%Codeine%'
				OR PM.Ingredients like '%Dihydrocodeine%'
				OR PM.Ingredients like '%Fentanyl%'
				OR PM.Ingredients like '%Hydrocodone%'
				OR PM.Ingredients like '%Meperidine%'
				OR PM.Ingredients like '%Hydromorphone%'
				OR PM.Ingredients like '%Methadone%'
				OR PM.Ingredients like '%Morphine%'
				OR PM.Ingredients like '%Nalbuphine%'
				OR PM.Ingredients like '%Opium%'
				OR PM.Ingredients like '%Oxycodone%'
				OR PM.Ingredients like '%Oxymorphone%'
				OR PM.Ingredients like '%Pentazocine%'
				OR PM.Ingredients like '%Pethidine%'
				OR PM.Ingredients like '%Remifentanil%'
				OR PM.Ingredients like '%Sufentanil%'
				OR PM.Ingredients like '%Tapentadol%' 
				OR PM.Ingredients like '%Tramadol%'
				)
              AND IsActive = 1
              AND (DateExpired IS NULL AND DateDiscontinued IS NULL)
			  and (pm.Strength  is null or pm.strength ='' or 
			  pm.dose  is null or pm.dose ='' or 
			  pm.form  is null or pm.form ='' or
			  pm.sig  is null or pm.sig ='')
			  group by pm.PatientRecordId

                           
              --- Current Opioid Prescription and documented medication information for calculating a medication daily dosage (hasRequiredInformation)
              INSERT INTO @Medications
			  SELECT pm.PatientRecordId,MedicationName,Ingredients, Strength,Dose,Form,SIG,
              IIF(
              (NULLIF(Strength,'')) IS NOT NULL
              AND (NULLIF(Dose,'')) IS NOT NULL
              AND (NULLIF(Form,'')) IS NOT NULL
              AND (NULLIF(SIG,'')) IS NOT NULL
              ,1,0) AS hasRequiredInformation,
              TRY_CONVERT(FLOAT,SUBSTRING(Strength,0,CHARINDEX('MG',REPLACE(Strength,'MCG','MG'),0))) AS StrengthNumberic,
			  TRY_CONVERT(FLOAT,SUBSTRING(Dose, PATINDEX('%[0-9]%', Dose), PATINDEX('%[0-9][^0-9]%', Dose + 't') - PATINDEX('%[0-9]%', 
                    Dose) + 1)) AS DoseNumeric
              FROM @BasePopulation bp
              join [PatientMedications] PM on bp.PatientRecordId = pm.PatientRecordId
              WHERE
              	(
				MedicationName like '%Alfentanil%' or 
				MedicationName like '%Buprenorphin%' or 
				MedicationName like '%Butorphanol%' or 
				MedicationName like '%Codeine%' or 
				MedicationName like '%Dihydrocodeine%' or 
				MedicationName like '%Fentanyl%' or 
				MedicationName like '%Hydrocodone%' or 
				MedicationName like '%Meperidin%' or 
				MedicationName like '%Hydromorphone%' or 
				MedicationName like '%Methadone%' or 
				MedicationName like '%Morphine%' or 
				MedicationName like '%Nalbuphine%' or 
				MedicationName like '%Opium%' or 
				MedicationName like '%Oxycodone%' or 
				MedicationName like '%Oxymorphone%' or 
				MedicationName like '%Pentazocine%' or 
				MedicationName like '%Pethidine%' or 
				MedicationName like '%Remifentanil%' or 
				MedicationName like '%Sufentanil%' or 
				MedicationName like '%Tapentadol%' or 
				MedicationName like '%Tramadol%'
				or PM.Ingredients like '%Alfentanil%' 
				OR PM.Ingredients like '%Buprenorphin%'
				OR PM.Ingredients like '%Butorphanol%'
				OR PM.Ingredients like '%Codeine%'
				OR PM.Ingredients like '%Dihydrocodeine%'
				OR PM.Ingredients like '%Fentanyl%'
				OR PM.Ingredients like '%Hydrocodone%'
				OR PM.Ingredients like '%Meperidine%'
				OR PM.Ingredients like '%Hydromorphone%'
				OR PM.Ingredients like '%Methadone%'
				OR PM.Ingredients like '%Morphine%'
				OR PM.Ingredients like '%Nalbuphine%'
				OR PM.Ingredients like '%Opium%'
				OR PM.Ingredients like '%Oxycodone%'
				OR PM.Ingredients like '%Oxymorphone%'
				OR PM.Ingredients like '%Pentazocine%'
				OR PM.Ingredients like '%Pethidine%'
				OR PM.Ingredients like '%Remifentanil%'
				OR PM.Ingredients like '%Sufentanil%'
				OR PM.Ingredients like '%Tapentadol%' 
				OR PM.Ingredients like '%Tramadol%'
				)
              AND IsActive = 1
              AND (DateExpired IS NULL AND DateDiscontinued IS NULL) 
              
       --select * from @Medications


              --- Conversion Factor Table
              DECLARE @ConversionFactor AS TABLE (Opioid VARCHAR(100), ConversionFactor FLOAT,isFentanyl BIT DEFAULT 0,FentanylStrength FLOAT NULL)
              INSERT INTO @ConversionFactor SELECT 'Morphine',1,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Codeine',0.15,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Oxycodone',1.5,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Hydrocodone',1,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Hydromorphone',5,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Meperidine',0.1,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Tramadol',0.1,0,NULL
              --INSERT INTO @ConversionFactor SELECT 'Transfermal Fentanyl Patch - 12.5 g/h',67,1
              --INSERT INTO @ConversionFactor SELECT 'Transfermal Fentanyl Patch - 25 mcg/h',134,1
              --INSERT INTO @ConversionFactor SELECT 'Transfermal Fentanyl Patch - 37.5 mcg/h',179,1
              --INSERT INTO @ConversionFactor SELECT 'Transfermal Fentanyl Patch - 50 mcg/h',224,1
              --INSERT INTO @ConversionFactor SELECT 'Transfermal Fentanyl Patch - 75 mcg/h',314,1
              --INSERT INTO @ConversionFactor SELECT 'Transfermal Fentanyl Patch - 100 mcg/h',404,1
			  INSERT INTO @ConversionFactor SELECT 'Fentanyl',67,1,12.5
              INSERT INTO @ConversionFactor SELECT 'Fentanyl',134,1,25
              INSERT INTO @ConversionFactor SELECT 'Fentanyl',179,1,37.5
              INSERT INTO @ConversionFactor SELECT 'Fentanyl',224,1,50
              INSERT INTO @ConversionFactor SELECT 'Fentanyl',314,1,75
              INSERT INTO @ConversionFactor SELECT 'Fentanyl',404,1,100
              INSERT INTO @ConversionFactor SELECT 'Butorphanol',7,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Dihydrocodeine',0.25,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Opium',1,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Oxymorphone',3,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Pentazocine',0.37,0,NULL
              INSERT INTO @ConversionFactor SELECT 'Tapentadol',0.4,0	,NULL	  
			  
insert into @RxNoConvert select M.patientrecordid from @Medications M   
LEFT JOIN @ConversionFactor CF ON (M.MedicationName LIKE '%' + CF.Opioid + '%' OR M.Ingredients LIKE '%' + CF.Opioid + '%') 
where cf.Opioid is null or CF.Opioid = ''
group by M.patientrecordid


              ---  Has an existing Morphine Equivalent conversion factor and Daily Morphine Equivalent  
              ---- *** Must have MedicationFrequencyMapping created and mapped properly
			  -- For a Transdermal Fentanyl Patch, opioid daily Morphine Equivalent = medication dosage X Morphine Equivalent conversion factor. 
			  -- For all other opioids, opioid daily Morphine Equivalent = medication daily dosage X Morphine Equivalent conversion factor. 

              INSERT INTO @MorphineEquivalent
			  SELECT        
              M.PatientRecordId,
              CF.ConversionFactor,
              StrengthNumberic,
			  DoseNumeric,
              DailyFrequency,
              (M.StrengthNumberic * FM.DailyFrequency * DoseNumeric) AS DailyDosage,
              CF.ConversionFactor * (M.StrengthNumberic * FM.DailyFrequency * DoseNumeric) AS DailyMorphineEquivalent               
              FROM @Medications M
              LEFT JOIN MedicationFrequencyMapping FM ON FM.SIG = M.SIG
              LEFT JOIN @ConversionFactor CF ON (M.MedicationName LIKE '%' + CF.Opioid + '%' OR M.Ingredients LIKE '%' + CF.Opioid + '%') AND CF.isFentanyl = 0	-- opioid component could be in name or ingredients
              WHERE hasRequiredInformation = 1
			  AND (M.MedicationName NOT LIKE '%Fentanyl%' OR M.Ingredients NOT LIKE '%Fentanyl%') -- Fentanyl has a different conversion factor rule.
			  --AND CF.isFentanyl = 0	
			  
			-- Fentanyl convertion factor
			  INSERT INTO @MorphineEquivalent
			  SELECT        
              M.PatientRecordId,
              CF.ConversionFactor,
              StrengthNumberic,
			  DoseNumeric,
              DailyFrequency,
              (M.StrengthNumberic * DoseNumeric) AS DailyDosage,
              (M.StrengthNumberic * DoseNumeric) * CF.ConversionFactor AS DailyMorphineEquivalent               
              FROM @Medications M
              LEFT JOIN MedicationFrequencyMapping FM ON FM.SIG = M.SIG
              LEFT JOIN @ConversionFactor CF ON (M.MedicationName LIKE '%' + CF.Opioid + '%' OR M.Ingredients LIKE '%' + CF.Opioid + '%') 
			  AND (FentanylStrength = M.StrengthNumberic) AND (CF.isFentanyl = 1)	 -- opioid component could be in name or ingredients
              WHERE hasRequiredInformation = 1
			  AND (M.MedicationName LIKE '%Fentanyl%' OR M.Ingredients LIKE '%Fentanyl%') -- Fentanyl has a different conversion factor rule.
			  --AND CF.isFentanyl = 1			

			 -- Calcuting when patient has more than 1 opioid
			 INSERT INTO @MorphineEquivalentGroup
             SELECT PatientRecordId,MAX(ConversionFactor)/* just check is nullable */,SUM(DailyMorphineEquivalent)
			 FROM @MorphineEquivalent			
			 GROUP BY PatientRecordId
			  
			  --- Segments	
			INSERT INTO @Segments
			SELECT 1,BP.PatientRecordId
					 FROM @BasePopulation BP
					 JOIN @MorphineEquivalentGroup M ON M.PatientRecordId = Bp.PatientRecordId
					 WHERE ConversionFactor IS NOT NULL AND DailyMorphineEquivalent < 50
					 and bp.PatientRecordId not in (Select PatientRecordID from @RxIncomplete)
					 and bp.PatientRecordId not in (Select PatientRecordID from @RxNoConvert)
					 GROUP BY BP.PatientRecordId                     
            
			INSERT INTO @Segments
			SELECT 2,BP.PatientRecordId FROM @BasePopulation BP
					 JOIN @MorphineEquivalentGroup M ON M.PatientRecordId = Bp.PatientRecordId
					 WHERE ConversionFactor IS NOT NULL AND DailyMorphineEquivalent >= 50 AND DailyMorphineEquivalent < 90
					 and bp.PatientRecordId not in (Select PatientRecordID from @RxIncomplete)
					 and bp.PatientRecordId not in (Select PatientRecordID from @RxNoConvert)
					 GROUP BY BP.PatientRecordId           
					 
            INSERT INTO @Segments
			SELECT 3,BP.PatientRecordId FROM @BasePopulation BP
					 JOIN @MorphineEquivalentGroup M ON M.PatientRecordId = Bp.PatientRecordId
					 WHERE ConversionFactor IS NOT NULL AND DailyMorphineEquivalent >= 90
					 and bp.PatientRecordId not in (Select PatientRecordID from @RxIncomplete)
					 and bp.PatientRecordId not in (Select PatientRecordID from @RxNoConvert)
					 GROUP BY BP.PatientRecordId 
              
			INSERT INTO @Segments
			SELECT 4,RxI.PatientRecordId			  FROM @RxIncomplete RxI
					                   
              
			INSERT INTO @Segments
			SELECT 5,BP.PatientRecordId			  FROM @RxNoConvert bp
					WHERE  bp.PatientRecordId not in (Select PatientRecordID from @RxIncomplete)
					GROUP BY BP.PatientRecordId 
					
            --- Final Select
			SELECT SegmentId,PatientRecordId FROM @Segments

              PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END


