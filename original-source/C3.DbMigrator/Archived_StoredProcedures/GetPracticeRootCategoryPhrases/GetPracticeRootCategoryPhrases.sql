﻿CREATE PROCEDURE [dbo].[GetPracticeRootCategoryPhrases]
	@groupId INT,	
	@practiceId INT,
	@practiceTemplateId INT,
	@rootCategoryId INT = 0	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    DECLARE @tblPhrases table
	(
		PracRootCategoryPhraseId int NOT NULL,
		PracRootCategoryTempId int NOT NULL,
		RootCategoryPhraseId int NOT NULL,
		PhraseName nvarchar(500) null,
		PhraseValue nvarchar(max),		
		ParentId int NULL,	
		IsSubCategory bit not null,
		RootCategoryId int not null,
		CategoryName nvarchar(1500) null,
		GroupId int not null,
		RootTemplateId int not null,	
		primary key (PracRootCategoryPhraseId)
	)

	INSERT INTO @tblPhrases
	SELECT 
	prcp.Id
	,prcp.PracRootCategoryTempId
	,prcp.<PERSON>ate<PERSON><PERSON>hraseId    
	,rcp.<PERSON>rase<PERSON>ame
	,rcp.PhraseValue
	,rcp.ParentId	
	,rcp.IsSubCategory
	,rc.Id
	,rc.CategoryName
	,rc.GroupId
	,pt.TemplateId
	FROM PracticeRootCategoryPhrases prcp
	JOIN PracticeRootCategoryTemplates pt ON prcp.PracRootCategoryTempId = pt.Id
	JOIN RootCategoryPhrases rcp ON prcp.RootCategoryPhraseId = rcp.Id
	JOIN RootCategories rc ON rcp.RootCategoryId = rc.Id	
	WHERE prcp.PracRootCategoryTempId = @practiceTemplateId		
	AND rc.GroupId = @groupId
	AND rcp.RootCategoryId = CASE WHEN @rootCategoryId > 0 THEN @rootCategoryId ELSE rcp.RootCategoryId END;

	WITH CustomPhraseCTE
	(
		 PracRootCatPhraseId
		,RootCategoryPhraseId
		,PracRootCategoryTempId
		,RootTemplateId
		,RootCategoryId
		,CategoryName		
		,GroupId		
		,PhraseName
		,PhraseValue
		,ParentId	
		,IsSubCategory	
		,Breadcrum 
		,[Level]					
	)
	AS(
		SELECT 
		 phrases.PracRootCategoryPhraseId
		,phrases.RootCategoryPhraseId
		,phrases.PracRootCategoryTempId
		,phrases.RootTemplateId
		,phrases.RootCategoryId
		,phrases.CategoryName
		,phrases.GroupId		
		,phrases.PhraseName
		,phrases.PhraseValue
		,phrases.ParentId	
		,phrases.IsSubCategory	
		,CAST(phrases.CategoryName + ' -> '+phrases.PhraseName AS nvarchar(2000))
		,0 	
		FROM @tblPhrases phrases		
		WHERE phrases.ParentId IS NULL		
		UNION ALL
	--recursive select
		SELECT 
		 phrases.PracRootCategoryPhraseId
		,phrases.RootCategoryPhraseId
		,cte.PracRootCategoryTempId
		,cte.RootTemplateId
		,cte.RootCategoryId
		,cte.CategoryName		
		,cte.GroupId		
		,phrases.PhraseName
		,phrases.PhraseValue
		,phrases.ParentId	
		,phrases.IsSubCategory	
		,CAST(cte.Breadcrum + ' -> '+phrases.PhraseName AS nvarchar(2000))
		,cte.[level] + 1						
		FROM @tblPhrases phrases
		JOIN CustomPhraseCTE cte ON phrases.ParentId = cte.RootCategoryPhraseId				
		WHERE cte.RootCategoryId = phrases.RootCategoryId
	)
	SELECT 
	cte.PracRootCatPhraseId
	,cte.RootCategoryPhraseId
	,cte.PracRootCategoryTempId
	,cte.RootTemplateId
	,cte.RootCategoryId
	,cte.CategoryName		
	,cte.GroupId	
	,cte.PhraseName
	,cte.PhraseValue
	,ISNULL(cte.ParentId,0) AS ParentId	
	,cte.IsSubCategory	
	,cte.Breadcrum
	,cte.[Level]					
	FROM CustomPhraseCTE cte 
	ORDER BY cte.ParentId ASC, cte.CategoryName ASC, cte.PhraseName ASC
END