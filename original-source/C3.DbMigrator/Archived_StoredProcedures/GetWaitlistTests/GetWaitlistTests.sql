﻿

CREATE PROCEDURE [dbo].[GetWaitlistTests] 
	@appointmentIds AS dbo.IntegerList READONLY
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;   

   SELECT apptest.[Id]
      ,apptest.[TestId]
      ,apptest.[startTime]
      ,apptest.[AppointmentTestStatusId]
      ,apptest.[testDuration]
      ,apptest.[billStatusId]
      ,apptest.[referralDoctorId]
      ,apptest.[AppointmentId]
      ,apptest.[AccessionNumber]
      ,apptest.[PhysicianComments]
      ,apptest.[TechnicianComments]
      ,apptest.[IsActive]
      ,apptest.[DateCreated]
      ,apptest.[DateUpdated]
      ,apptest.[SetForReview]
      ,apptest.[ReassignDocID]
      ,apptest.[ReassignDate]	  
	  ,test.testFullName
	  ,test.testShortName
	  ,test.RequireDevice
	  ,teststatus.Color AS TestStatusColor
	  ,teststatus.Status AS TestStatus	  
  FROM [dbo].[AppointmentTests] apptest
  JOIN @appointmentIds appIds ON apptest.AppointmentId = appIds.IntegerValue
  JOIN AppointmentTestStatus teststatus ON apptest.AppointmentTestStatusId = teststatus.Id
  JOIN Tests test ON apptest.TestId = test.Id  
  WHERE apptest.IsActive = 1    
END

