﻿CREATE PROCEDURE [dbo].[GetReportVascular] 
	@AppointmentTestId INT,
	@appointmentTestLogId INT = 0

AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @GroupIdVascular INT = 110;--Vascular

	DECLARE @Measurements AS TABLE (
		MeasurementId INT,
		MeasurementCategoryName VARCHAR (100),
		MeasurementCategoryId INT,
		MeasurementName VARCHAR (100),
		MeasurementValue VARCHAR(50),
		MeasurementCode VARCHAR(100),
		CategoryCode VARCHAR(100)
	)

	IF @appointmentTestLogId <=0
	BEGIN
		SET @appointmentTestLogId = (SELECT MAX(L.Id) FROM AppointmentTestLogs L JOIN AppointmentTests T ON L.AppointmentId = T.AppointmentId WHERE T.Id = @AppointmentTestId AND L.Status = 0)
	END     

	INSERT INTO @Measurements
	SELECT DISTINCT
	 dbo.Measurements.Id AS MeasurementId,
	 dbo.MeasurementCategories.name AS MeasurementCategoryName,	
	 dbo.MeasurementCategories.Id AS MeasurementCategoryId,
	 dbo.Measurements.name AS MeasurementsName,		  	 
	 (CASE WHEN ISNUMERIC(TRY_CONVERT(DECIMAL(10,3), dbo.MeasurementSavedValues.Value))=1 THEN FORMAT(TRY_CONVERT(DECIMAL(10,3), dbo.MeasurementSavedValues.Value), COALESCE(mask,'0.00'))   
	 ELSE dbo.MeasurementSavedValues.Value END) + ' ' + ISNULL(dbo.Measurements.units,'') AS MeasurementValue,
	 dbo.Measurements.measurementCode,
	 dbo.MeasurementCategories.categoryCode				 
     FROM dbo.AppointmentTests 
     INNER JOIN dbo.Appointments ON dbo.AppointmentTests.AppointmentId = dbo.Appointments.Id      
	 INNER JOIN dbo.AppointmentTestLogs AS P ON dbo.AppointmentTests.AppointmentId = P.AppointmentID
     INNER JOIN dbo.MeasurementSavedValues ON P.Id = dbo.MeasurementSavedValues.AppointmentTestLogID 
     INNER JOIN dbo.Measurements ON dbo.MeasurementSavedValues.MeasurementId = dbo.Measurements.Id 
     INNER JOIN dbo.MeasurementCategories ON dbo.Measurements.MeasurementCategoryID = dbo.MeasurementCategories.Id 
     INNER JOIN dbo.MeasurementByPractices ON dbo.Measurements.Id = dbo.MeasurementByPractices.MeasurementID 
	 INNER JOIN dbo.TestGroups ON dbo.TestGroups.TestId = dbo.AppointmentTests.TestId
     WHERE (dbo.AppointmentTests.Id =  @AppointmentTestId) AND (dbo.MeasurementByPractices.Visible = 1) 
	 AND dbo.TestGroups.GroupId = @GroupIdVascular	 
	 AND P.Id = @appointmentTestLogId;

	 --SELECT * from @Measurements

	 DECLARE @MeasurementsTable AS TABLE (
		[MeasurementCategoryId] INT NOT NULL,
		[PSVRightValue] VARCHAR(100) NULL,
		[EDVRightValue] VARCHAR(100) NULL,
		[PressureRightValue] VARCHAR(100) NULL,
		[SizeRightValue] VARCHAR(100) NULL,
		[WaveformRightValue] VARCHAR(100) NULL,
		[PSVLeftValue] VARCHAR(100) NULL,
		[EDVLeftValue] VARCHAR(100) NULL,
		[PressureLeftValue] VARCHAR(100) NULL,
		[SizeLeftValue] VARCHAR(100) NULL,
		[WaveformLeftValue] VARCHAR(100) NULL,
		[ThrombusRightValue] VARCHAR(100) NULL,
		[RefluxRightValue] VARCHAR(100) NULL,
		[ThrombusLeftValue] VARCHAR(100) NULL,
		[RefluxLeftValue] VARCHAR(100) NULL,
		[GeneralVascularRight] VARCHAR(100) NULL,
		[GeneralVascularLeft] VARCHAR(100) NULL,
		[MeasurementType] VARCHAR(100) NULL,				
		[MeasurementTableType] VARCHAR(20),
		[DisplayOrder] INT NULL
	)
	
	/* PAD Measurements */
	INSERT INTO @MeasurementsTable ([MeasurementCategoryId],[PSVRightValue],[EDVRightValue],[PressureRightValue],[SizeRightValue],[WaveformRightValue],[PSVLeftValue],[EDVLeftValue],[PressureLeftValue],[SizeleftValue],[WaveformLeftValue],[MeasurementType],[MeasurementTableType],[DisplayOrder])
	SELECT 
	*,'' AS [MeasurementType],'PAD',1 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([PSV(Right)], [EDV(Right)], [Pressure(Right)], [Size(Right)], [Waveform(Right)], [PSV(Left)], [EDV(Left)], [Pressure(Left)], [Size(Left)], [Waveform(Left)])
	) PIV
	-- Proximal
	UNION ALL
	SELECT 
	*,' Proximal' AS [MeasurementType],'PAD',2 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([PSV(Proximal;Right)], [EDV(Proximal;Right)], [Pressure(Proximal;Right)], [Size(Proximal;Right)], [Waveform(Proximal;Right)], [PSV(Proximal;Left)], [EDV(Proximal;Left)], [Pressure(Proximal;Left)], [Size(Proximal;Left)], [Waveform(Proximal;Left)])
	) PIV
	-- Mid
	UNION ALL
	SELECT 
	*,' Mid' AS [MeasurementType],'PAD',3 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId, MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)		  
		  FOR MeasurementName IN ([PSV(Mid-longitudinal;Right)], [EDV(Mid-longitudinal;Right)], [Pressure(Mid-longitudinal;Right)], [Size(Mid-longitudinal;Right)], [Waveform(Mid-longitudinal;Right)], [PSV(Mid-longitudinal;Left)], [EDV(Mid-longitudinal;Left)], [Pressure(Mid-longitudinal;Left)], [Size(Mid-longitudinal;Left)], [Waveform(Mid-longitudinal;Left)])
	) PIV
	-- Distal
	UNION ALL
	SELECT 
	*,' Distal' AS [MeasurementType],'PAD',4 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId, MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([PSV(Distal;Right)], [EDV(Distal;Right)], [Pressure(Distal;Right)], [Size(Distal;Right)], [Waveform(Distal;Right)], [PSV(Distal;Left)], [EDV(Distal;Left)], [Pressure(Distal;Left)], [Size(Distal;Left)], [Waveform(Distal;Left)])
	) PIV	
	/* PVD Measurements */	
	INSERT INTO @MeasurementsTable ([MeasurementCategoryId],[ThrombusRightValue],[RefluxRightValue],[ThrombusLeftValue],[RefluxLeftValue],[MeasurementType],[MeasurementTableType],[DisplayOrder])
	SELECT 
	*,'' AS [MeasurementType],'PVD',1 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Thrombus(Right)], [Reflux(Right)], [Thrombus(Left)], [Reflux(Left)])
	) PIV
	-- Proximal
	UNION ALL
	SELECT 
	*,' Proximal' AS [MeasurementType],'PVD',2 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Thrombus(Proximal;Right)], [Reflux(Proximal;Right)], [Thrombus(Proximal;Left)], [Reflux(Proximal;Left)])
	) PIV
	-- Mid
	UNION ALL
	SELECT 
	*,' Mid' AS [MeasurementType],'PVD',3 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId, MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Thrombus(Mid-longitudinal;Right)], [Reflux(Mid-longitudinal;Right)], [Thrombus(Mid-longitudinal;Left)], [Reflux(Mid-longitudinal;Left)])
	) PIV
	-- Distal
	UNION ALL
	SELECT 
	*,' Distal' AS [MeasurementType],'PVD',4 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId, MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Thrombus(Distal;Right)], [Reflux(Distal;Right)], [Thrombus(Distal;Left)], [Reflux(Distal;Left)])
	) PIV
	-- Thigh
	UNION ALL
		SELECT 
	*,' Thigh' AS [MeasurementType],'PVD',5 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId, MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Thrombus(Thigh;Right)], [Reflux(Thigh;Right)], [Thrombus(Thigh;Left)], [Reflux(Thigh;Left)])
	) PIV
	--Calf
	UNION ALL
	SELECT 
	*,' Calf' AS [MeasurementType],'PVD',6 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId, MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Thrombus(Calf;Right)], [Reflux(Calf;Right)], [Thrombus(Calf;Left)], [Reflux(Calf;Left)])
	) PIV
	-- GeneralVascular
	INSERT INTO @MeasurementsTable ([MeasurementCategoryId],[GeneralVascularRight],[GeneralVascularLeft],[MeasurementType],[MeasurementTableType],[DisplayOrder])
	SELECT 
	*,'ICA/CCA Ratio' AS [MeasurementType],'GeneralVascular',1 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([ICA/CCA Ratio(Right)], [ICA/CCA Ratio(Left)])
	) P
	UNION ALL
	SELECT 
	*,'% ICA Stenosis' AS [MeasurementType],'GeneralVascular',2 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([% ICA Stenosis(Right)], [% ICA Stenosis(Left)])
	) P
	UNION ALL
	SELECT 
	*,'Vertebral Flow Direction' AS [MeasurementType],'GeneralVascular',3 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Vertebral Flow Direction(Right)], [Vertebral Flow Direction(Left)])
	) P
	UNION ALL
	SELECT 
	*,'ABI (Ankle Brachial Index)' AS [MeasurementType],'GeneralVascular',4 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([ABI (Ankle Brachial Index;Right)], [ABI (Ankle Brachial Index;Left)])
	) P
	UNION ALL
	SELECT 
	*,'TBI (Toe Brachial Index)' AS [MeasurementType],'GeneralVascular',5 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([TBI (Toe Brachial Index;Right)], [TBI (Toe Brachial Index;Left)])
	) P
	UNION ALL
	SELECT 
	*,'Brachial Pressure' AS [MeasurementType],'GeneralVascular',6 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Brachial Pressure(Right)], [Brachial Pressure(Left)])
	) P
	UNION ALL
	SELECT 
	*,'Wrist Pressure' AS [MeasurementType],'GeneralVascular',7 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Wrist Pressure(Right)], [Wrist Pressure(Left)])
	) P
	UNION ALL
	SELECT 
	*,'Digital Pressure' AS [MeasurementType],'GeneralVascular',8 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Digital Pressure(Right)], [Digital Pressure(Left)])
	) P
	UNION ALL
	SELECT 
	*,'WBI (Wrist Brachial Index)' AS [MeasurementType],'GeneralVascular',9 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([WBI (Wrist Brachial Index;Right)], [WBI (Wrist Brachial Index;Left)])
	) P
	UNION ALL
	SELECT 
	*,'DBI (Digital Brachial Index)' AS [MeasurementType],'GeneralVascular',10 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([DBI (Digital Brachial Index;Right)], [DBI (Digital Brachial Index;Left)])
	) P
	UNION ALL
	SELECT 
	*,'Dorsalis Pedis Pressure' AS [MeasurementType],'GeneralVascular',11 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Dorsalis Pedis Pressure(Right)], [Dorsalis Pedis Pressure(Left)])
	) P
	UNION ALL
	SELECT 
	*,'Posterior Tibial Pressure' AS [MeasurementType],'GeneralVascular',12 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Posterior Tibial Pressure(Right)], [Posterior Tibial Pressure(Left)])
	) P
	UNION ALL
	SELECT 
	*,'Toe Pressure' AS [MeasurementType],'GeneralVascular',13 AS [DisplayOrder]
	FROM
	(
		  SELECT MeasurementCategoryId AS CategoryId,MeasurementValue, MeasurementName
		  FROM @Measurements		  
	) T
	PIVOT
	(
		  MAX(MeasurementValue)
		  FOR MeasurementName IN ([Toe Pressure(Right)], [Toe Pressure(Left)])
	) P

	DECLARE @VascularMeasurements AS TABLE (
		[Description] VARCHAR(100) NOT NULL,
		[PSVRightValue] VARCHAR(100) NULL,
		[EDVRightValue] VARCHAR(100) NULL,
		[PressureRightValue] VARCHAR(100) NULL,
		[SizeRightValue] VARCHAR(100) NULL,
		[WaveformRightValue] VARCHAR(100) NULL,
		[PSVLeftValue] VARCHAR(100) NULL,
		[EDVLeftValue] VARCHAR(100) NULL,
		[PressureLeftValue] VARCHAR(100) NULL,
		[SizeLeftValue] VARCHAR(100) NULL,
		[WaveformLeftValue] VARCHAR(100) NULL,
		[ThrombusRightValue] VARCHAR(100) NULL,
		[RefluxRightValue] VARCHAR(100) NULL,
		[ThrombusLeftValue] VARCHAR(100) NULL,
		[RefluxLeftValue] VARCHAR(100) NULL,
		[GeneralVascularRight] VARCHAR(100) NULL,
		[GeneralVascularLeft] VARCHAR(100) NULL,
		[CategoryDisplayOrder] INT NULL,
		[MeasurementTableType] VARCHAR(20) NULL,
		[TypeDisplayOrder] INT NULL
	)

	INSERT INTO @VascularMeasurements
	SELECT
	IIF([MeasurementTableType] = 'GeneralVascular',T.MeasurementType,MeasurementCategories.name + T.MeasurementType),
	T.[PSVRightValue],
	T.[EDVRightValue],
	T.[PressureRightValue],
	T.[SizeRightValue],
	T.[WaveformRightValue],
	T.[PSVLeftValue],
	T.[EDVLeftValue],
	T.[PressureLeftValue],
	T.[SizeLeftValue],
	T.[WaveformLeftValue],
	T.[ThrombusRightValue],
	T.[RefluxRightValue],
	T.[ThrombusLeftValue],
	T.[RefluxLeftValue],
	T.[GeneralVascularRight],
	T.[GeneralVascularLeft],
	MeasurementCategories.[order],
	T.[MeasurementTableType],
	T.[DisplayOrder]
	FROM
	@MeasurementsTable T
	INNER JOIN MeasurementCategories ON T.MeasurementCategoryId = MeasurementCategories.Id
	WHERE COALESCE([PSVRightValue],[EDVRightValue],[PressureRightValue],[SizeRightValue],[WaveformRightValue],[PSVLeftValue],[EDVLeftValue],[PressureLeftValue],[SizeLeftValue],[WaveformLeftValue],[ThrombusRightValue],[RefluxRightValue],[ThrombusLeftValue],[RefluxLeftValue],[GeneralVascularRight],[GeneralVascularLeft]) IS NOT NULL

	SELECT [Description],[PSVRightValue],[EDVRightValue],[PressureRightValue],[SizeRightValue],[WaveformRightValue],[PSVLeftValue],[EDVLeftValue],[PressureLeftValue],[SizeLeftValue],[WaveformLeftValue],[ThrombusRightValue],[RefluxRightValue],[ThrombusLeftValue],[RefluxLeftValue],[GeneralVascularRight],[GeneralVascularLeft],[MeasurementTableType]
	FROM @VascularMeasurements
	ORDER BY [CategoryDisplayOrder] ASC,[TypeDisplayOrder] ASC
END
