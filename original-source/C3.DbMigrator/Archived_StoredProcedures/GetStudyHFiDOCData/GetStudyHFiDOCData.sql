﻿-- =============================================
-- Author:		<Michael>
-- Create date: <Nov 23,2020>
-- Description:	
-- =============================================
CREATE   PROCEDURE [dbo].[GetStudyHFiDOCData]  
	@practiceDoctorId int
AS   
BEGIN
	SET NOCOUNT on;

	SELECT Id,PracticeDoctorId,PatientRecordId,FirstName,MiddleName,LastName,Gender,Age,LastAppointmentDate,NextAppointmentDate,
			DiagnoseCodes,HL7ReportId,PotassiumValue,PotassiumDate,CreatinineValue,CreatinineDate,eGRFValue,eGRFDate,
			BloodPressureValue,BloodPressureDate,HeartRateValue,HeartRateDate,EFValue
	FROM HFiDOCPatients
	WHERE PracticeDoctorId = @practiceDoctorId

	SELECT Id,PracticeDoctorId,PatientRecordId,IsDin,IsVPRootCategory,Class,MedicationName,Dose,Strength,SIG
	FROM HFiDOCPatientMedications
	WHERE PracticeDoctorId = @practiceDoctorId
END