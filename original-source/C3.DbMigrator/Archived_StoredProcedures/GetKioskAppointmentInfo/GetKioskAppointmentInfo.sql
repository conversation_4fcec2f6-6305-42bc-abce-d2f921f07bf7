﻿CREATE PROCEDURE [dbo].[GetKioskAppointmentInfo] 
	@healthcard nvarchar(100),
	@ipAddress nvarchar(100)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @currentDate datetime2 = GETDATE();
	DECLARE @expiryDate datetime2 = null;
	DECLARE @kioskMessageServerErrorCode int = 5;
	DECLARE @practiceId int = 0;		
	DECLARE @patientId int = 0;		
	DECLARE @demographicId int = 0;		
	DECLARE @addressLine1 nvarchar(200);
	DECLARE @addressLine2 nvarchar(200);
	DECLARE @city nvarchar(100);
	DECLARE @postalCode nvarchar(10);
	DECLARE @province nvarchar(100);
	DECLARE @country nvarchar(100);
	DECLARE @familyDoctor nvarchar(200);


	SELECT TOP 1 @patientId = isnull(d.PatientRecordId,0)	   
	FROM DemographicsHealthCards hc JOIN Demographics d ON hc.DemographicId = d.id
    WHERE hc.[number] = @healthcard   

	SELECT TOP 1 @addressLine1 = info.AddressLine1,
	       @addressLine2 = info.AddressLine2,
		   @city = info.City,
		   @postalcode = info.PostalCode,
  		   @province = info.Province,
		   @country = info.Country,
		   @demographicId = info.DemographicId,
		   @expiryDate = info.HealthCardExpiryDate,
		   @practiceId = info.PracticeId
	FROM fn_GetPatientInfo(@patientId,@currentDate) info
    WHERE info.PatientId = @patientId
    
	
	SELECT
	 apps.Id AS AppointmentId
	,apps.appointmentTime AS AppointmentTime
	,apps.OfficeId
	,@practiceId AS PracticeId
	,@patientId AS PatientId	
	,@demographicId AS DemographicId
	,@addressLine1 AS AddressLine1
	,@addressLine2 AS AddressLine2
	,@city AS City
	,@postalCode AS PostalCode
	,@province AS Province
	,@country AS Country 
	,@healthcard AS HealthCard
	,@expiryDate AS HealthCardExpiryDate
	,CAST((CASE WHEN @addressLine1 IS NOT NULL AND @addressLine1 != ''  THEN 1 ELSE 0 END) AS BIT) AS HasAddress
	,ISNULL((SELECT TOP 1 isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')		               
			            FROM ExternalDoctors as ed 
						JOIN DemographicsFamilyDoctors fd ON ed.Id = fd.ExternalDoctorId
						WHERE fd.DemographicId =@demographicId AND fd.IsActive = 1 AND fd.IsRemoved=0
						ORDER BY fd.Id DESC),'') AS FamilyDoctor
	,CAST ((CASE WHEN k.KioskMessageId > 0  THEN ISNULL((SELECT TOP 1 m.Code FROM KioskMessages m WHERE m.Id = k.KioskMessageId),0) ELSE 0 END) AS INT) AS MessageCode		
	FROM 
	Appointments apps		
	JOIN KioskIpAddresses kip ON apps.OfficeId = kip.OfficeId
	LEFT JOIN KioskCheckins k ON apps.Id = k.AppointmentId
	WHERE CONVERT(char(8), apps.appointmentTime, 112) = CONVERT(char(8), @currentDate, 112)
	AND apps.PatientRecordId = @patientId
	AND kip.IpAddress = @ipAddress
	AND apps.IsActive = 1 
	

END