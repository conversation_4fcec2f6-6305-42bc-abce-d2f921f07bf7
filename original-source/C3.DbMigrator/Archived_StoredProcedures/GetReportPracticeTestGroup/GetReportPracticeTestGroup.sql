﻿CREATE PROCEDURE [dbo].[GetReportPracticeTestGroup]
	@AppointmentTestId INT
AS
BEGIN
	SELECT TOP 1 APT.Id AS AppointmentTestId,RS.[Image] AS ReportSeal
	FROM AppointmentTests APT
	JOIN Appointments A ON A.Id = APT.AppointmentId
	JOIN Office O ON O.Id = A.OfficeId
	JOIN TestGroups TG ON TG.TestId = APT.TestId
	JOIN PracticeTestGroups PTG ON PTG.GroupId = TG.GroupId AND PTG.PracticeId = O.PracticeId
	JOIN PracticeTestGroupReportSeal RS ON RS.Id = PTG.ReportSealId
	WHERE APT.Id = @AppointmentTestId;
END