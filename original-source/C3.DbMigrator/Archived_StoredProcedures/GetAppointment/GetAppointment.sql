﻿CREATE PROCEDURE [dbo].[GetAppointment] 	
	@appointmentId INT	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	
	DECLARE @isActive bit = 1;
	DECLARE @patientId int = ISNULL((SELECT PatientRecordId FROM Appointments WHERE Id= @appointmentId),0)

	SELECT apps.[Id]
      ,apps.[OfficeId]
      ,apps.[appointmentTime]
      ,apps.[ArrivedTime]
      ,apps.[LeftTime]
      ,apps.[appointmentPurpose]
      ,apps.[appointmentStatus]
      ,apps.[appointmentNotes]
      ,apps.[appointmentRegistrar]
      ,apps.[MWLUrl]
      ,apps.[MWLSentFlag]
      ,apps.[actionOnAbnormal]
      ,apps.[bookingConfirmation]
      ,apps.[cancellationList]
      ,apps.[phoneReminder]
      ,apps.[emailReminder]
      ,apps.[roomNumber]
      ,apps.[PracticeDoctorId]
      ,apps.[billStatusId]
      ,apps.[OpeningStatement]
      ,isnull(refDoc.Id,0) AS referralDoctorId
      ,apps.[AppointmentTypeId]
      ,apps.[appointmentConfirmation]
      ,apps.[appointmentPaymentMethod]
      ,apps.[TriageUrgencyId]
      ,apps.[TriageStatusId]
      ,apps.[IsImported]
      ,apps.[IsActive]
      ,apps.[PatientRecordId]
      ,apps.[DateCreated]
      ,apps.[LastModified]
      ,apps.[ResidualData]
      ,apps.[ReferralDoctorAddressId]
      ,apps.[ReferralDoctorPhoneNumberId]
	  ,apps.[RecordType]
	  ,apps.AppointmentPriorityId	 
	  ,appPrior.PriorityName
	  ,refDoc.ReferralDoctor
	  ,refDoc.RefDocOHIPPhysicianId
	  ,refDoc.RefDocCPSO
	  ,apptype.[name] AS AppointmentType
	  ,ISNULL(apptype.AppointmentTypeId,0) AS AppointmentTypeParentId
	  ,ISNULL((SELECT TOP 1 [name] FROM AppointmentTypes WHERE Id = appType.AppointmentTypeId),'') AS AppointmentTypeParentName
	  ,ISNULL(extDocs.firstName,'') +' '+isnull(extDocs.lastName,'') AS PracticeDoctor
	  ,extDocs.OHIPPhysicianId AS PracticeDocOHIPPhysicianId
	  ,extDocs.CPSO AS PracticeDocCPSO
	  ,extDocs.Id AS PracticeDocExternalDocId	  	  
	  ,patient.AliasFirstName
	  ,patient.AliasLastName
	  ,patient.AliasMiddleName
	  ,patient.DemographicId
	  ,patient.FirstName
	  ,patient.LastName
	  ,patient.MiddleName	
	  ,patient.DateOfBirth
	  ,patient.Gender
	  ,patient.HealthCard
	  ,patient.HealthCardCode
	  ,patient.PhoneNumbers AS PatientPhoneNumbers
	  ,patient.MiddleName
	  ,patient.UseAliases 
	  ,patient.PreferredName	  
	  ,billstat.[name] As BillStatus
	  ,billstat.color As BillStatusColor
	  ,office.[name] As OfficeName
	  ,office.PracticeId AS PracticeId
	  ,ISNULL(apps.CancellationReasonId,0) AS CancellationReasonId
	  ,cancel.[Description] AS CancellationReasonDescription
	  ,apps.CancellationReasonNotes
  FROM Appointments apps
  JOIN fn_GetPatientInfo(@patientId,null) patient ON patient.PatientId = apps.PatientRecordId  
  JOIN Office office ON office.Id = apps.OfficeId
  JOIN AppointmentTypes apptype ON apps.AppointmentTypeId = apptype.Id
  JOIN PracticeDoctors pracDocs ON apps.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  LEFT JOIN BillStatus billstat ON apps.billStatusId = billstat.id
  LEFT JOIN CancellationReasons cancel ON apps.CancellationReasonId = cancel.Id
  LEFT JOIN (SELECT ed.id, isnull(ed.firstName,'')+' '+isnull(ed.lastName,'') AS ReferralDoctor, 
			                ed.OHIPPhysicianId as RefDocOHIPPhysicianId, ed.CPSO AS RefDocCPSO
			            FROM ExternalDoctors as ed 	WHERE ed.active=1			    
			 ) as refDoc ON apps.referralDoctorId = refDoc.id   
  LEFT JOIN AppointmentPriority appPrior ON apps.AppointmentPriorityId = appPrior.id			 
  WHERE apps.Id = @appointmentId  
  AND patient.PracticeId = office.PracticeId
  AND apps.IsActive = @isActive
	
	
END
