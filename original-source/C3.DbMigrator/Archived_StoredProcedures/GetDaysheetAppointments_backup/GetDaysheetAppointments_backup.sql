﻿


CREATE PROCEDURE [dbo].[GetDaysheetAppointments_backup] 
	@officeId INT = NULL,
	@selectedDate DATETIME = NULL, 
	@practiceDoctorId INT = NULL,
	@showExpected BIT = 0,
	@excludeTestOnly BIT = 0,
	@excludeCancelled BIT = 0, 
	@appointmentStatus INT = NULL,	
	@appointmentId INT = NULL,
	@filterPatient NVARCHAR(50) = NULL
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	--DECLARE @appointmentStatus int = 1;
	DECLARE @isActive int = 1;
	--DECLARE @practiceId int;-- = (SELECT PracticeId FROM Office WHERE Id = @officeId)
	DECLARE @cancelFlag smallint = 1;
	DECLARE @triageAppStatus smallint = 16; -- Triage status

	DECLARE @lookupDate datetime2;-- = CONVERT(char(8), @selectedDate, 112);

	IF(@appointmentId IS NULL OR @appointmentId = 0)
	BEGIN
		--SET @practiceId = (SELECT PracticeId FROM Office WHERE Id = @officeId);
		SET @lookupDate = CONVERT(char(8), @selectedDate, 112);
	END
	ELSE
	BEGIN
		SET @lookupDate = CONVERT(char(8), (SELECT appointmentTime FROM Appointments WHERE Id = @appointmentId), 112);
		SET @officeId = (SELECT OfficeId FROM Appointments WHERE Id = @appointmentId);
	END

	DECLARE @excludeAppStatuses TABLE
	   ( 
         appointmentStatus int 
	   );
	INSERT INTO @excludeAppStatuses VALUES (7); -- cancelled
	INSERT INTO @excludeAppStatuses VALUES (1); -- waitlist
	INSERT INTO @excludeAppStatuses VALUES (0); -- cancellationlist
	INSERT INTO @excludeAppStatuses VALUES (5); -- left
	INSERT INTO @excludeAppStatuses VALUES (12); -- patientLeftAndOrdersDone

	DECLARE @appTestResources TABLE
	   ( id                        int identity(1,1) not null,
	     appId                     int not null,
		 testId                    int not null,
		 appointmentTestId         int not null,		     
		 appointmentTestTime       datetime2 null,
		 assignedUserId            int not null,		 
		 practiceDoctorId          int not null
		 ,primary key (id)
	   );

	DECLARE @appointments TABLE
	   ( id                        int identity(1,1) not null,
	     app_id                    int not null,
		 practiceid                int null,
	     officeid                  int null,
		 appointmentTime           datetime2 null,
		 arrivedTime               nvarchar(10) null,
		 leftTime                  nvarchar(10) null,
         appointmentPurpose        nvarchar(500) null,
         appointmentStatus         int null,
         appointmentNotes          nvarchar(500) null,
         appointmentRegistrar      int null,
         MWLUrl                    nvarchar(500) null,
         MWLSentFlag               bit null,
         actionOnAbnormal          bit null,
         bookingConfirmation       bit null,
         roomNumber                nvarchar(10) null,
         PracticeDoctorId          int null,
         billStatusId              int null,
         OpeningStatement          nvarchar(500) null,
         referralDoctorId          int null,
		 AppointmentTypeId         int null,
		 AppointmentType           nvarchar(100) null,
		 AppointmentTypeParentId   int null,
	     appointmentConfirmation   int null,
         appointmentPaymentMethod  int null,
         TriageUrgencyId           int null,
         TriageStatusId            int null,
         IsActive                  bit null,
         PatientRecordId           int null,
         DateCreated               datetime2 null,
         LastModified              datetime2 null,
		 PracticeDoctor            nvarchar(100) null,
		 IsImported                  bit null 
		 ,primary key (id)
	   );
    INSERT INTO @appointments(app_id       
							,practiceid             
							,officeid                 
							,appointmentTime           
							,arrivedTime               
							,leftTime                  
							,appointmentPurpose        
							,appointmentStatus         
							,appointmentNotes          
							,appointmentRegistrar      
							,MWLUrl                    
							,MWLSentFlag               
							,actionOnAbnormal          
							,bookingConfirmation       
							,roomNumber                
							,PracticeDoctorId          
							,billStatusId              
							,OpeningStatement          
							,referralDoctorId          
							,AppointmentTypeId
							,AppointmentType
                            ,AppointmentTypeParentId
							,appointmentConfirmation   
							,appointmentPaymentMethod  
							,TriageUrgencyId           
							,TriageStatusId            
							,IsActive                  
							,PatientRecordId           
							,DateCreated               
							,LastModified
							,PracticeDoctor,
							IsImported)
  SELECT apps.[Id]
      ,O.[PracticeId]
      ,apps.[OfficeId]
      ,apps.[appointmentTime]
      ,apps.[ArrivedTime]
      ,apps.[LeftTime]
      ,apps.[appointmentPurpose]
      ,apps.[appointmentStatus]
      ,apps.[appointmentNotes]
      ,apps.[appointmentRegistrar]
      ,apps.[MWLUrl]
      ,apps.[MWLSentFlag]
      ,apps.[actionOnAbnormal]
      ,apps.[bookingConfirmation]
      ,apps.[roomNumber]
      ,apps.[PracticeDoctorId]
      ,apps.[billStatusId]
      ,apps.[OpeningStatement]
      ,apps.[referralDoctorId]
      ,apps.[AppointmentTypeId]
	  ,apptype.[name] AS AppointmentType
	  ,ISNULL(apptype.AppointmentTypeId,0) AS AppointmentTypeParentId
      ,apps.[appointmentConfirmation]
      ,apps.[appointmentPaymentMethod]
      ,apps.[TriageUrgencyId]
      ,apps.[TriageStatusId]
      ,apps.[IsActive]
      ,apps.[PatientRecordId]
      ,apps.[DateCreated]
      ,apps.[LastModified]
	  ,isnull(extDocs.firstName,'') +' '+isnull(extDocs.lastName,'') AS PracticeDoctor	,
	   apps.IsImported 
	FROM [dbo].[Appointments] apps
		JOIN PatientRecords precs ON apps.PatientRecordId =precs.Id
		 JOIN Office O ON apps.OfficeId=O.Id
	     JOIN AppointmentTypes apptype ON apps.AppointmentTypeId = apptype.Id
	     JOIN PracticeDoctors pracDocs ON apps.PracticeDoctorId = pracDocs.Id
	     JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  WHERE 
  apps.IsActive = @isActive AND ((@appointmentId IS NOT NULL AND @appointmentId > 0 AND apps.Id = @appointmentId) 
  OR (  
	  CONVERT(char(8), apps.appointmentTime, 112) = @lookupDate 
	  AND   apps.IsActive = @isActive
	  AND   apps.OfficeId = @officeId 
	  AND   1 = 
	   CASE 
		WHEN @showExpected = 1 AND apps.appointmentStatus NOT IN (SELECT appointmentStatus FROM @excludeAppStatuses) THEN 1 
		WHEN @appointmentStatus IS NULL AND @showExpected = 0 AND @excludeCancelled = 1 AND apps.appointmentStatus != 7 AND apps.appointmentStatus > 1 THEN 1 
		WHEN @appointmentStatus IS NULL AND @showExpected = 0 AND @excludeCancelled = 0 AND apps.appointmentStatus > 1 THEN 1
		WHEN @appointmentStatus IS NOT NULL AND apps.appointmentStatus = @appointmentStatus THEN 1  
		ELSE 0
		END 
	  AND   apps.Id = (CASE WHEN @appointmentId IS NULL THEN apps.Id ELSE @appointmentId END)
	  AND   apps.PracticeDoctorId = (CASE WHEN @practiceDoctorId IS NULL THEN apps.PracticeDoctorId ELSE @practiceDoctorId END)
	  AND   1 = CASE 
				WHEN @excludeTestOnly = 1 AND apptype.AppointmentTypeId != 1 THEN 1 
				WHEN @excludeTestOnly = 0 OR @excludeTestOnly IS NULL THEN 1 
				ELSE 0 
				END
  --AND   apps.cancellationList <> @cancelFlag
  AND   apps.appointmentStatus <> @triageAppStatus
  AND O.PracticeId=precs.PracticeId))    
ORDER BY apps.appointmentTime;

INSERT INTO @appTestResources(appId,appointmentTestId,testId,appointmentTestTime,assignedUserId,practiceDoctorId)
SELECT att.AppointmentId, att.Id, att.TestId,att.startTime, atr.assignedToUserId, apps.PracticeDoctorId  FROM AppointmentTestResources atr 
			JOIN AppointmentTests att ON atr.AppointmentTestId = att.Id
			JOIN Appointments apps ON att.AppointmentId = apps.Id
			WHERE apps.IsActive = 1 AND att.TestId = 29 AND att.IsActive = 1 AND atr.assignedToUserId IS NOT NULL
			AND  CONVERT(char(8), att.startTime, 112) = @lookupDate  AND apps.OfficeId = @officeId

    SELECT apps.[app_id] as [Id]	    
		,apps.[practiceid] AS PracticeId
		,apps.[OfficeId]
		,apps.[appointmentTime]
		,apps.[ArrivedTime]
		,apps.[LeftTime]
		,apps.[appointmentPurpose]
		,apps.[appointmentStatus]
		,apps.[appointmentNotes]
		,apps.[appointmentRegistrar]
		,apps.[MWLUrl]
		,apps.[MWLSentFlag]
		,apps.[actionOnAbnormal]
		,apps.[bookingConfirmation]
		,apps.[roomNumber]
		,apps.[PracticeDoctorId]
		--,apps.[billStatusId]
		,isnull(apps.[billStatusId],0) AS billStatusId
	    ,billstat.[name] AS BillStatus
	    ,billstat.color AS BillStatusColor
		,apps.[OpeningStatement]
		,apps.[referralDoctorId]
		,extDocs.ReferralDoctor -- referral doctor
		,extDocs.RefDocOHIPPhysicianId -- referral doctor
		,extDocs.CPSO AS RefDocCPSO -- referral doctor
		,ISNULL((SELECT TOP 1 isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')		               
			            FROM ExternalDoctors as ed 
						JOIN DemographicsFamilyDoctors fd ON ed.Id = fd.ExternalDoctorId
						WHERE fd.DemographicId = demo.Id
						ORDER BY fd.IsActive DESC, fd.Id DESC),'') AS FamilyDoctor
		,patPhoneNums.phoneNumbers AS PatientPhoneNumbers
		,apps.[AppointmentTypeId]
		,apps.AppointmentType
		,apps.AppointmentTypeParentId
		,ISNULL((SELECT [name] FROM AppointmentTypes WHERE id=apps.AppointmentTypeParentId),'') AS AppointmentTypeParent 
		,apps.[appointmentConfirmation]
		,apps.[appointmentPaymentMethod]
		,apps.[TriageUrgencyId]
		,apps.[TriageStatusId]
		,apps.[IsActive]
		,apps.[PatientRecordId]
		,apps.[DateCreated]
		,apps.[LastModified]
		,apps.[PracticeDoctor]
		,demo.firstName AS PatientFirstName
		,demo.lastName AS PatientLastName
		,demo.dateOfBirth AS DateOfBirth
		,isnull(bill.ConsultCodeId,0) AS ConsultCodeId
		,isnull(bill.DiagnosticCodeId,0) AS DiagnosticCodeId
		,isnull(bill.DiagnosticCodeId2,0) AS DiagnosticCodeId2
		,isnull(bill.DiagnosticCodeId3,0) AS DiagnosticCodeId3
        ,isnull(bill.ConsultCode, '') AS ConsultCode
		,isnull(bill.DiagnosticCode, '') AS DiagnosticCode
		,isnull(bill.DiagnosticCode2, '') AS DiagnosticCode2
		,isnull(bill.DiagnosticCode3, '') AS DiagnosticCode3
		,isnull((SELECT top 1 1  FROM DemographicsHealthCards WHERE DemographicId = demo.Id AND RTRIM(LTRIM(number))<>''),0) AS TotalHealthCards
		,isnull((SELECT top 1 1 FROM ReportReceiveds WHERE appointmentId = apps.app_Id),0) AS TotalReferralDocuments
		,isnull((SELECT top 1 1 FROM DoctorComments WHERE PatientRecordId = apps.PatientRecordId),0) AS TotalDoctorComments
        ,isnull(extDocs.TotalReferralDocFax,0) as TotalReferralDocFax
		,isnull(extDocs.TotalReferralDocNumbers,0) as TotalReferralDocNumbers
		,cast(isnull((SELECT top 1 1 FROM Appointments WHERE OfficeId = apps.officeid AND apps.appointmentTime = apps.appointmentTime AND PracticeDoctorId = apps.PracticeDoctorId AND Id != apps.app_id),0) AS bit) AS IsDoctorDoubleBook	
		,isnull((
			SELECT COUNT(atr.appointmentTestId) FROM @appTestResources atr WHERE 
			(SELECT COUNT(*)
			   FROM @appTestResources atr2
			   WHERE 
				atr.appointmentTestTime = atr2.appointmentTestTime
			    AND atr.practiceDoctorId = atr2.practiceDoctorId						   
			   ) > 1 AND atr.practiceDoctorId = apps.PracticeDoctorId AND atr.appId = apps.app_id
			   
		),0) as TotalVPBooked,
		apps.IsImported 
	FROM @appointments apps
	        JOIN Demographics demo ON apps.PatientRecordId = demo.PatientRecordId
		    LEFT JOIN ( SELECT ed.id, isnull(ed.firstName,'')+' '+isnull(ed.lastName,'') AS ReferralDoctor, 
			                ed.OHIPPhysicianId as RefDocOHIPPhysicianId, ed.CPSO, pn.TotalReferralDocFax, pn.TotalReferralDocNumbers
			            FROM ExternalDoctors as ed 
						    CROSS APPLY (select MAX(CASE WHEN phoneNumber IS NOT NULL THEN 1 ELSE 0 END) as TotalReferralDocNumbers, MAX(CASE WHEN faxnumber IS NOT NULL THEN 1 ELSE 0 END) as TotalReferralDocFax 
								            from ExternalDoctorPhoneNumbers edpn 
								            where edpn.ExternalDoctorId = ed.id ) as pn
					) as extDocs ON apps.referralDoctorId = extDocs.Id 
			LEFT JOIN ( SELECT AppointmentID
			                ,bill1.ConsultCode as ConsultCodeID
			                ,bill1.DiagnosticCode AS DiagnosticCodeID
	                        ,bill1.DiagnosticCode2 AS DiagnosticCodeID2
	                        ,bill1.DiagnosticCode3 AS DiagnosticCodeID3
  							,CONVERT(NVARCHAR(50),
								(CASE  
  								    WHEN isnull(bill1.ConsultCode,0) > 0
								    THEN (SELECT TOP 1 Code FROM ConsultCodes WHERE Id = bill1.ConsultCode)
								    ELSE ''
	  						    END)) as ConsultCode
							    ,CONVERT(NVARCHAR(50),
								(CASE  
								    WHEN isnull(bill1.DiagnosticCode,0) > 0
								    THEN (SELECT TOP 1 Diagnosis FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode)
								    ELSE ''
							    END)) as DiagnosticCode
							    ,CONVERT(NVARCHAR(50),
								(CASE  
								    WHEN isnull(bill1.DiagnosticCode,0) > 0
								    THEN (SELECT TOP 1 Diagnosis FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode2)
								    ELSE ''
							    END)) as DiagnosticCode2
							    ,CONVERT(NVARCHAR(50),
								(CASE  
								    WHEN isnull(bill1.DiagnosticCode,0) > 0
								    THEN (SELECT TOP 1 Diagnosis FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode3)
								    ELSE ''
							    END)) as DiagnosticCode3
			                FROM AppointmentBills as bill1 ) as bill ON apps.app_Id = bill.AppointmentID
			LEFT JOIN BillStatus billstat ON apps.billStatusId = billstat.id
			LEFT JOIN (
				SELECT DemographicId AS PatDemoId, phoneNumbers=STUFF  
				(  
					(  
					  SELECT TOP 3 ', '+ CAST(e.phoneNumber AS VARCHAR(500)) + ' '+ 
						  (CASE e.typeOfPhoneNumber
							WHEN 0 THEN 'H'
							WHEN 1 THEN 'C'
							WHEN 2 THEN 'W'
						   ELSE ''
						   END
						  )
						  FROM DemographicsPhoneNumbers e   
						  WHERE e.DemographicId = d.DemographicId AND e.phoneNumber IS NOT NULL AND e.phoneNumber != '' and e.IsRemoved = 0
						  ORDER BY e.DemographicId, e.typeOfPhoneNumber, e.IsActive DESC, e.Id DESC
						  FOR XMl PATH('')  
						),1,1,''  
					)  
				FROM DemographicsPhoneNumbers d 
				GROUP BY d.DemographicId
			) AS patPhoneNums ON demo.Id = patPhoneNums.PatDemoId
			WHERE  1 = 
			   CASE 					
					WHEN @filterPatient IS NOT NULL AND LOWER(demo.lastName) LIKE @filterPatient+'%' OR LOWER(demo.firstName) LIKE @filterPatient+'%' THEN 1  
					WHEN @filterPatient IS NULL THEN 1 
				ELSE 0
				END
ORDER BY apps.id ASC
END
