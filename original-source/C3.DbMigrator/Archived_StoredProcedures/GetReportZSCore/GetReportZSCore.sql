﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- Author:		<<PERSON>,Correa>
-- Modified date: <2022-06-08>
-- Description:	<US 1828 - Fix ZScore data and calculate>
-- =============================================
CREATE PROCEDURE [dbo].[GetReportZSCore]
	  @AppointmentTestId int,
	  @appointmentTestLogId INT = 0	
AS
BEGIN
	SET NOCOUNT ON;

  
--declare @AppointmenttestLogId int

   IF @appointmentTestLogId <=0
   BEGIN
		SET @appointmentTestLogId = (SELECT MAX(L.Id) FROM AppointmentTestLogs L JOIN AppointmentTests T ON L.AppointmentId = T.AppointmentId WHERE T.Id = @AppointmentTestId AND L.Status = 0)
   END  


--Set @AppointmenttestLogId =(SELECT DISTINCT TOP (1)   MeasurementSavedValues.AppointmentTestLogID
--FROM            Measurements INNER JOIN
--                         Appointments INNER JOIN
--                         AppointmentTests ON Appointments.Id = AppointmentTests.AppointmentId INNER JOIN
--                         AppointmentTestLogs INNER JOIN
--                         MeasurementSavedValues ON AppointmentTestLogs.Id = MeasurementSavedValues.AppointmentTestLogID ON AppointmentTests.AppointmentId = AppointmentTestLogs.AppointmentID AND 
--                         AppointmentTests.TestId = AppointmentTestLogs.TestID ON Measurements.Id = MeasurementSavedValues.MeasurementId
						
--WHERE
--        (AppointmentTests.Id = @AppointmentTestId) 
-- AND 
--AppointmentTestLogs.Status = 0)
 



declare @BsaValue numeric(18,17)

Set @BsaValue = (SELECT top 1  TRY_CONVERT(numeric(18,17),value)
FROM            Measurements INNER JOIN
                         MeasurementSavedValues ON Measurements.Id = MeasurementSavedValues.MeasurementId
WHERE        (MeasurementSavedValues.AppointmentTestLogID = @AppointmentTestLogID) 
and measurementcode ='8277-6'
) 

DECLARE @MeasurementsZScore AS TABLE (
	[Name] NVARCHAR(50),
	[MeasurementId] INT,
	[Value] NVARCHAR(512),
	[Zscore] FLOAT,
	[Mean] FLOAT,
	[Min] FLOAT,
	[Max] FLOAT,
	[Sequence] FLOAT,
	[Abnormal] FLOAT,
	[Units] NVARCHAR(50)
)

if @BsaValue > 0
BEGIN
INSERT INTO @MeasurementsZScore
SELECT	distinct		Z_1.Name,
				Z_1.MeasurementId,	
				MeasurementSavedValues.Value, 
				(LOG (dbo.fn_ConvertUnits(MeasurementSavedValues.Value,Measurements.units)) - LOG (Z_1.MeanVal))/ dbo.fn_GetZSCoreValue(Z_1.MeasurementId, 1) AS Zscore, 
				case when Measurements.units = 'mm' then Z_1.MeanVal*10 else Z_1.MeanVal end AS Mean, 
				case when Measurements.units = 'mm' then Z_1.MinVal*10 else Z_1.MinVal end AS Min, 
				case when Measurements.units = 'mm' then Z_1.MaxVal*10 else Z_1.MaxVal end AS Max,  
				Measurements.Id AS [Sequence],
				(Case when ABS((LOG (dbo.fn_ConvertUnits(MeasurementSavedValues.Value,Measurements.units)) - LOG (Z_1.MeanVal))/ dbo.fn_GetZSCoreValue(Z_1.MeasurementId, 1))> 2 then 1 else 0 end) as Abnormal,
				Measurements.units
FROM            Measurements INNER JOIN
                MeasurementSavedValues ON Measurements.Id = MeasurementSavedValues.MeasurementId INNER JOIN
                (SELECT	Name, 
						MeasurementId, Sequence, Id,
						EXP(dbo.fn_GetZSCoreValue(MeasurementId, 3)) * POWER(@BsaValue, dbo.fn_GetZSCoreValue(MeasurementId, 2)) AS MeanVal, 
						EXP(dbo.fn_GetZSCoreValue(MeasurementId, 3)) * POWER(@BsaValue, dbo.fn_GetZSCoreValue(MeasurementId, 2)) * EXP(- 1.96 * dbo.fn_GetZSCoreValue(MeasurementId, 1)) AS MinVal, 
						EXP(dbo.fn_GetZSCoreValue(MeasurementId, 3)) * POWER(@BsaValue, dbo.fn_GetZSCoreValue(MeasurementId, 2)) * EXP(1.96 * dbo.fn_GetZSCoreValue(MeasurementId, 1)) AS MaxVal
				 FROM       ZScoreConst AS Z) AS Z_1 
ON				Measurements.ZScoreMethod2 = Z_1.Id
WHERE			(MeasurementSavedValues.AppointmentTestLogID = @AppointmentTestLogID)
order by [Sequence]
END

SELECT
[Name],[MeasurementId],[Value],[Zscore],[Mean],[Min],[Max],[Sequence],[Abnormal],[Units]
FROM @MeasurementsZScore
ORDER BY [Sequence]

END