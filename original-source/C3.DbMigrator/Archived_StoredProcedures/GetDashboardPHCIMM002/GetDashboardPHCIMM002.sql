﻿CREATE PROCEDURE [dbo].[GetDashboardPHCIMM002] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @DTapPIPVHibDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @DTapPIPVHibRefused AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @MeningococcalDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @MeningococcalRefused AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @MMRDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @MMRRefused AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @PneumococcalDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @PneumococcalRefused AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @VaricellaDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @VaricellaRefused AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @AllComplete AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @AllRefused AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @Complete AS TABLE (PatientRecordId INT)
	DECLARE @Refused AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation	
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	AND DATEDIFF(MONTH,D.dateOfBirth,GETDATE()) BETWEEN 18 AND 29 -- Active patients, age 30-42 months 
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

/*	SELECT		
	D.PatientRecordId, d.dateOfBirth, DATEDIFF(MONTH,D.dateOfBirth,GETDATE()), MRP.PracticeDoctorId
	--INTO @BasePopulation	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId
	-- AND DATEDIFF(MONTH,D.dateOfBirth,GETDATE()) BETWEEN 30 AND 42 -- Active patients, age 30-42 months 
	WHERE D.active = 0
	--AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	and PatientRecordId between 1105930 and 1105941
	--GROUP BY D.PatientRecordId 
	*/

	/* Cerebrum supports both individual immunizations for childern as well as a single immunization type "All Childhood Immunizations".  
	Both must be considered for this inidcatior to be correct */

	--  DTaPIPV-Hib documented 			
	INSERT INTO @DTapPIPVHibDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId=5							-- Type = DTaP-IPV-Hib	
	GROUP BY IM.PatientRecordId

	--  Pneumococcal documented 			
	INSERT INTO @PneumococcalDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId=13							-- Type = Pneumococcal
	GROUP BY IM.PatientRecordId

	--  Meningococcal documented 			
	INSERT INTO @MeningococcalDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM	
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId=11							-- Type = Men-C
	GROUP BY IM.PatientRecordId

	--  MMR documented 			
	INSERT INTO @MMRDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId=3							-- Type = MMR
	GROUP BY IM.PatientRecordId

	--  Varicella documented 			
	INSERT INTO @VaricellaDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId=9							-- Type = Var
	GROUP BY IM.PatientRecordId

	--  DTaPIPV-Hib refused last 12 months 			
	INSERT INTO @DTapPIPVHibRefused
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=5							-- Type = DTaP-IPV-Hib	
	AND DATEDIFF(MONTH,ImmunizationDate,GETDATE()) <= 12
	GROUP BY IM.PatientRecordId

	--  Pneumococcal refused last 12 months 			
	INSERT INTO @PneumococcalRefused
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=13							-- Type = Pneumococcal 
	AND DATEDIFF(MONTH,ImmunizationDate,GETDATE()) <= 12
	GROUP BY IM.PatientRecordId

	--  Meningococcal refused last 12 months 			 			
	INSERT INTO @MeningococcalRefused
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM	
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=11							-- Type = Men-C
	AND DATEDIFF(MONTH,ImmunizationDate,GETDATE()) <= 12
	GROUP BY IM.PatientRecordId

	--  MMR refused last 12 months 			 			
	INSERT INTO @MMRRefused
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=3							-- Type = MMR
	AND DATEDIFF(MONTH,ImmunizationDate,GETDATE()) <= 12
	GROUP BY IM.PatientRecordId

	--  Varicella refused last 12 months 			 			
	INSERT INTO @VaricellaRefused
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=9							-- Type = Var
	AND DATEDIFF(MONTH,ImmunizationDate,GETDATE()) <= 12
	GROUP BY IM.PatientRecordId

		--  All Childhood immunizations documented 			
	INSERT INTO @AllComplete
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId=17 						-- Type = All childhood immunizations documented	
	GROUP BY IM.PatientRecordId
	
			--  All Childhood immunizations Refused 			
	INSERT INTO @AllRefused
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=17 						-- Type = All childhood immunizations documented	
	GROUP BY IM.PatientRecordId


	-- All immunizations complete
		INSERT into @Complete
		SELECT PatientRecordId		
		FROM @BasePopulation
		WHERE  (PatientRecordId IN (SELECT PatientRecordId FROM @DTapPIPVHibDocumented WHERE ImmCount >= 4)		
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PneumococcalDocumented WHERE ImmCount >= 3)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @MeningococcalDocumented WHERE ImmCount >= 1)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @MMRDocumented WHERE ImmCount >= 1)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @VaricellaDocumented WHERE ImmCount >= 1))
		or PatientRecordID in (Select patientrecordid from @AllComplete)

	-- One of more immunizations refused
		INSERT into @Refused
		SELECT PatientRecordId		
		FROM @BasePopulation
		WHERE 
		PatientRecordId IN (SELECT PatientRecordId FROM @DTapPIPVHibRefused)
		OR
		PatientRecordId IN (SELECT PatientRecordId FROM @PneumococcalRefused)
		OR
		PatientRecordId IN (SELECT PatientRecordId FROM @MeningococcalRefused)
		OR
		PatientRecordId IN (SELECT PatientRecordId FROM @MMRRefused)
		OR
		PatientRecordId IN (SELECT PatientRecordId FROM @VaricellaRefused)
		or 
		PatientRecordId IN (SELECT PatientRecordId FROM @AllRefused)

	
	--- Segments	
		INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @Complete

		INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId not in (SELECT PatientRecordId FROM @Complete)
		and PatientRecordID not in (SELECT PatientRecordId FROM @Refused)
	
		INSERT INTO @Segments
	SELECT 3,PatientRecordId FROM @Refused
	
			--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments
	
	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
