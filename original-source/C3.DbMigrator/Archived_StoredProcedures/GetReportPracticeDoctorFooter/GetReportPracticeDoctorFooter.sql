﻿CREATE   PROCEDURE [dbo].[GetReportPracticeDoc<PERSON><PERSON>ooter] (@AppointmentTestId INT)
AS
BEGIN

DECLARE @PracticeDoctorId INT

SELECT @PracticeDoctorId = PracticeDoctorId 
FROM Appointments A
JOIN AppointmentTests T ON A.Id = T.AppointmentId
WHERE T.Id = @AppointmentTestId

SELECT Id AS PracticeDoctorReportFooterId,
	   PracticeDoctorId,	   	   
	   F.Image AS PracticeDoctorFooterImage
FROM PracticeDoctorReportFooter F WHERE PracticeDoctorId = @PracticeDoctorId

END