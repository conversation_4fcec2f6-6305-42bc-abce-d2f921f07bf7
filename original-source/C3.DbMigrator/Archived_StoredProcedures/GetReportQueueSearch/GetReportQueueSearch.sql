﻿CREATE PROCEDURE GetReportQueueSearch
	@QueueDate Date,
	@ReportQueueStatus INT,
	@PracticeId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	IF @ReportQueueStatus > 0
	BEGIN
	  SELECT  t5.Id as ReportQueueId,
			  t1.TestId as TestId, 
			  t1.startTime as TestDate, 
			  t2.testFullName as TestFullName, 
			  t2.testShortName as TestShortName, 
			  t3.PatientRecordId as PatientRecordId, 
			  t4.firstName as PatientFirstName, 
			  t4.lastName as PatientLastName,
			  t5.DateCreated as DateInQueue,
			  t5.UserFullName as UserFullName,
			  t5.NumberOfAttempts as NumberOfAttempts,
			  t5.DateLastModified as DateLastModified,
			  t5.SendStatusId as SendStatusId,
			  t5.AppointmentTestLogId as AppointmentTestLogId,
			  o.[Name] AS OfficeName
	  FROM [dbo].[AppointmentTests] t1
	  INNER JOIN [dbo].[Tests] t2
	  ON t1.TestId = t2.id
	  INNER JOIN [dbo].[Appointments] t3
	  ON t1.AppointmentId = t3.id
	  JOIN Office o ON o.Id = t3.OfficeId
	  INNER JOIN [dbo].[Demographics] t4
	  ON t3.PatientRecordId = t4.PatientRecordId
	  INNER JOIN [dbo].[ReportQueues] t5
	  ON t5.AppointmentTestId = t1.Id
	  WHERE convert(date,t5.DateCreated,101) = convert(date,@QueueDate,101)
	  AND t5.SendStatusId = @ReportQueueStatus
	  AND o.PracticeId = @PracticeId
	  ORDER BY t5.DateCreated
   END
   ELSE
   BEGIN
	  SELECT  t5.Id as ReportQueueId,
			  t1.TestId as TestId, 
			  t1.startTime as TestDate, 
			  t2.testFullName as TestFullName, 
			  t2.testShortName as TestShortName, 
			  t3.PatientRecordId as PatientRecordId, 
			  t4.firstName as PatientFirstName, 
			  t4.lastName as PatientLastName,
			  t5.DateCreated as DateInQueue,
			  t5.UserFullName as UserFullName,
			  t5.NumberOfAttempts as NumberOfAttempts,
			  t5.DateLastModified as DateLastModified,
			  t5.SendStatusId as SendStatusId,
			  t5.AppointmentTestLogId as AppointmentTestLogId,
			  o.[Name] AS OfficeName
	  FROM [dbo].[AppointmentTests] t1
	  INNER JOIN [dbo].[Tests] t2
	  ON t1.TestId = t2.id
	  INNER JOIN [dbo].[Appointments] t3
	  ON t1.AppointmentId = t3.id
	  JOIN Office o ON o.Id = t3.OfficeId
	  INNER JOIN [dbo].[Demographics] t4
	  ON t3.PatientRecordId = t4.PatientRecordId
	  INNER JOIN [dbo].[ReportQueues] t5
	  ON t5.AppointmentTestId = t1.Id
	  WHERE convert(date,t5.DateCreated,101) = convert(date,@QueueDate,101)
	  AND o.PracticeId = @PracticeId
	  ORDER BY t5.DateCreated
   END

END
