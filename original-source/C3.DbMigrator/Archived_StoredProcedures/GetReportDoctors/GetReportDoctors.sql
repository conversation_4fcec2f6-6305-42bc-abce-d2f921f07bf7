﻿CREATE PROCEDURE [dbo].[GetReportDoctors] 
	@AppointmentTestsId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 	
	rp.ExternalDoctorId
	,rp.PracticeDoctorId
	,rp.DoctorSignature
	,rp.Email
	,rp.Fax
	,rp.HRM 
	,rp.Mail			
	,rp.FirstName		
	,rp.LastName
	,rp.EmailAddress
	,rp.[Address]
	,rp.City
	,rp.PostalCode
	,rp.Province
	,rp.FaxNumber
	,rp.PhoneNumber
	,rp.PhoneNumberExtension
	,rp.ReportResourceType
	,rp.ResourceTypeDescription
	,rp.LetterHead
	,rp.[Degrees]
	,rp.CPSO
	,rp.OHIPId
	,rp.AppointmentTime
	,rp.AppointmentId
	,rp.AppointmentTestId 
	,rp.Is<PERSON>ainReportDoctor 
	,rp.IsMainResponsibleDoctor	
	,rp.IsMainReferral<PERSON><PERSON><PERSON>
	,rp.IsSendToSelf
	,rp.PhysicianType
	,rp.HrmMnemonic			
	FROM dbo.fn_GetReportDoctors(@AppointmentTestsId) rp
END


