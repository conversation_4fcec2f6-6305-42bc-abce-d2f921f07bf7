﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetReportCategories]
	-- Add the parameters for the stored procedure here
	
	@id int,
	@appointmentTestLogId INT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

   IF @appointmentTestLogId <=0
   BEGIN
		SET @appointmentTestLogId = (SELECT MAX(L.Id) FROM AppointmentTestLogs L JOIN AppointmentTests T ON L.AppointmentId = T.AppointmentId WHERE T.Id = @id AND L.Status = 0)
   END  

SELECT   distinct    B.AppointmentTestId ,b.ReportPhraseId as TopLevelReportPhraseID, dbo.fn_SuperTrimRight(B.Value) as value, b.name as text,
(case when a.name  is not null then a.name else b.name end) as Customtext,
(case when a.rank  is not null then a.rank else b.ordernumber end) as ordernumber

FROM      (

SELECT        ReportPhraseSavedTexts.TopLevelReportPhraseID as ReportPhraseId , ReportPhrases.name, 
						ReportPhraseSavedTexts.Value, AppointmentTestLogs.Id, AppointmentTests.Id AS AppointmentTestId, ReportPhrases.ordernumber, 
                         ReportPhrases.Id AS rpid, TestGroups.GroupId
FROM            ReportPhraseSavedTexts INNER JOIN
                         ReportPhrases ON ReportPhraseSavedTexts.TopLevelReportPhraseID = ReportPhrases.Id INNER JOIN
                         AppointmentTestLogs ON ReportPhraseSavedTexts.AppointmentTestLogID = AppointmentTestLogs.Id INNER JOIN
                         AppointmentTests ON AppointmentTestLogs.AppointmentID = AppointmentTests.AppointmentId AND AppointmentTestLogs.TestID = AppointmentTests.TestId INNER JOIN
                         Tests ON AppointmentTests.TestId = Tests.Id INNER JOIN
                         TestGroups ON Tests.Id = TestGroups.TestId
--WHERE        (AppointmentTestLogs.Status = 0) AND (AppointmentTests.Id = @id) 
WHERE AppointmentTestLogs.Id = @appointmentTestLogId AND AppointmentTests.Id = @id
and  (iif(ReportPhrases.name = 'Questionnaire' and TestGroups.GroupId = 50, 0,1)=1 )

)    B 
LEFT OUTER JOIN
                     (SELECT        PC.ReportPhraseId, PC.Text AS Name, APTS.Id AS AppointmentTestId, pc.Rank, pc.id, pd.id as pfid
FROM            dbo.Appointments AS APS INNER JOIN
                         dbo.AppointmentTests AS APTS ON APS.Id = APTS.AppointmentId INNER JOIN
                         dbo.PracticeDoctors AS PD ON APS.PracticeDoctorId = PD.Id INNER JOIN
                         dbo.ReportPhrase_Custom AS PC ON PD.ExternalDoctorId = PC.UserID AND APTS.TestId = PC.TestID
WHERE        (APTS.Id = @id) 
AND (PC.Visible = 1) AND (PC.PracticeID = PD.PracticeId)
) A 
ON B.AppointmentTestId = A.AppointmentTestId AND B.ReportPhraseId = A.ReportPhraseId
order by ordernumber

END
