﻿CREATE PROCEDURE [dbo].[GetMeasurementsSavedValues] 
	@appointmentTestId INT = 0,	
	@appointmentTestLogIds IntegerList readonly	 

AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	DECLAR<PERSON> @hasLogIds BIT = 0;
	DECLAR<PERSON> @patientId INT = 0;
	DECLAR<PERSON> @appointmentId INT = 0;
	DECLAR<PERSON> @appointmentTestLogId INT = 0;
	DECLARE @testId INT = 0;
	DECLARE @logsIds IntegerList;	
	DECLARE @savedValues TABLE
	(	
		SavedValueId int NOT NULL, 
		PatientId int NOT NULL,
		AppointmentId int NOT NULL,
		AppointmentTestId int NOT NULL,
		TestId int NOT NULL,
		AppointmentTestLogId int NOT NULL,
		MeasurementId int not NULL,
		MeasurementName nvarchar(500) NULL,
		MeasurementValue nvarchar(4000) NULL,
		IsCalculatedMeasurment bit not null,
		MeasurementOperatorId int not null,
		MeasurementOperator nvarchar(50) NULL,
		ValueType int not null,
		UserId int null,
		UserName nvarchar(500) NULL
	)

	IF NOT EXISTS(SELECT * FROM @appointmentTestLogIds)
	BEGIN		
		SELECT TOP 1 @appointmentTestLogId = ISNULL(Id,0) FROM AppointmentTestSaveLogs WHERE AppointmentTestId = @appointmentTestId AND IsActive = 1 ORDER BY LogDate DESC
		INSERT INTO @logsIds(IntegerValue)Values(@appointmentTestLogId);
	END
	ELSE
	BEGIN
		INSERT INTO @logsIds
		SELECT IntegerValue FROM @appointmentTestLogIds
	END

	SELECT TOP 1 @patientId = a.PatientRecordId, @appointmentId = a.Id, @testId = t.TestId 
	FROM AppointmentTests t 
	JOIN Appointments a ON t.AppointmentId = a.Id 
	WHERE t.Id = @appointmentTestId;	

    INSERT INTO @savedValues
	SELECT
	sv.Id
	,@patientId
	,@appointmentId
	,@appointmentTestId
	,@testId
	,sv.AppointmentTestSaveLogId
	,sv.VPUniqueMeasurementId
	,um.[Name]
	,sv.[Value]
	,0 -- IsCalculatedMeasurment
	,0 -- MeasurementOperatorId
	,null -- MeasurementOperator
	,um.ValueType
	,u.UserID
	,ISNULL(u.LastName+' '+u.FirstName,'') AS UserName	
	FROM VPMeasurementSavedValues sv
	JOIN VPUniqueMeasurements um ON sv.VPUniqueMeasurementId =um.Id
	JOIN AppointmentTestSaveLogs l ON sv.AppointmentTestSaveLogId =l.Id
	JOIN @logsIds lds ON l.Id = lds.IntegerValue
	LEFT JOIN AspNetUsers u ON l.SavedByUserId=u.UserID
	WHERE l.AppointmentTestId = CASE WHEN @appointmentTestId > 0 THEN @appointmentTestId ELSE l.AppointmentTestId END
	

	SELECT
	sv.SavedValueId
	,sv.PatientId
	,sv.AppointmentId
	,sv.AppointmentTestId
	,sv.TestId
	,sv.AppointmentTestLogId
	,sv.MeasurementId
	,sv.MeasurementName
	,sv.MeasurementValue
	,sv.IsCalculatedMeasurment
	,sv.MeasurementOperatorId
	,sv.MeasurementOperator
	,sv.ValueType
	,sv.UserId
	,sv.UserName
	FROM @savedValues sv
END