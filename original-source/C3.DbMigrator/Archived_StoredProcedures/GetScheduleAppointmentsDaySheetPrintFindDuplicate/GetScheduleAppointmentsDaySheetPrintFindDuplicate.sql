﻿CREATE PROCEDURE [dbo].[GetScheduleAppointmentsDaySheetPrintFindDuplicate] 	-- Add the parameters for the stored procedure here
	@officeId INT,
	@practiceDoctorId INT,
	@practiceid INT,
	@fromDate DATETIME,
	@toDate DATETIME
AS


BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	DECLARE @appointmentStatus AS INT
	DECLARE @cancelledStatus AS INT
	DECLARE @isActive AS BIT

	SET NOCOUNT ON;
	SET @appointmentStatus =1
	set @isActive=1
	SET @cancelledStatus=7
    -- Insert statements for procedure here

IF @practiceDoctorId > 0  and  @officeId > 0

Begin            	
SELECT count(*) as totalcount ,
tst.testShortName , 
res.assignedToUserId ,
apt.startTime
  FROM [AppointmentTestResources] as res
  join [AppointmentTests] as apt on res.AppointmentTestId=apt.id  
  join [Appointments] as ap on apt.AppointmentId=ap.Id
  join [Office] as office on ap.OfficeId=office.Id
  join [Practices] as prac on  office.PracticeId = prac.Id
  right join [AppointmentTypes] as typ on ap.[AppointmentTypeId]=typ.Id
  --join [AppointmentTypes] as aptyp on typ.[AppointmentTypeId]=aptyp.Id
  JOIN [PracticeDoctors] pracDocs ON ap.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  JOIN Demographics demo ON ap.PatientRecordId = demo.PatientRecordId
  right join [Tests] as tst on apt.TestId=tst.Id
  
  where(prac.Id=@practiceid)   
  and ap.PracticeDoctorId = @practiceDoctorId 
  and ap.OfficeId = @officeId
  and (ap.[appointmentStatus] >@appointmentStatus AND ap.[appointmentStatus]<> @cancelledStatus) 
  and (ap.appointmentTime>@fromDate and ap.appointmentTime<@toDate) 
  and ap.[IsActive]=@isActive 
  and apt.IsActive=@isActive
  and LOWER( tst.testShortName )  =  'vp'
  --and res.assignedToUserId is not null 
  group by tst.testShortName  , res.assignedToUserId ,   apt.startTime
  HAVING count(*) > 1		 
END
 ELSE IF  @practiceDoctorId > 0 and  @officeId < 1

 Begin 
SELECT count(*) as totalcount,
tst.testShortName , 
res.assignedToUserId ,
apt.startTime
  FROM [AppointmentTestResources] as res
  join [AppointmentTests] as apt on res.AppointmentTestId=apt.id  
  join [Appointments] as ap on apt.AppointmentId=ap.Id
  join [Office] as office on ap.OfficeId=office.Id
  join [Practices] as prac on  office.PracticeId = prac.Id
  right join [AppointmentTypes] as typ on ap.[AppointmentTypeId]=typ.Id
  --join [AppointmentTypes] as aptyp on typ.[AppointmentTypeId]=aptyp.Id
  JOIN [PracticeDoctors] pracDocs ON ap.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  JOIN Demographics demo ON ap.PatientRecordId = demo.PatientRecordId
  right join [Tests] as tst on apt.TestId=tst.Id
  
  where(prac.Id=@practiceid)  and 
  ap.PracticeDoctorId = @practiceDoctorId 
  and (ap.[appointmentStatus] >@appointmentStatus AND ap.[appointmentStatus]<> @cancelledStatus) 
  and (ap.appointmentTime>@fromDate and ap.appointmentTime<@toDate) 
  and ap.[IsActive]=@isActive 
  and apt.IsActive=@isActive
    and LOWER( tst.testShortName )  =  'vp'
    --and res.assignedToUserId is not null 
   group by tst.testShortName  , res.assignedToUserId ,   apt.startTime

  HAVING count(*) > 1
END             
ELSE IF @practiceDoctorId < 1  and  @officeId > 0

begin               
SELECT count(*) as totalcount,
tst.testShortName , 
res.assignedToUserId ,
apt.startTime
  FROM [AppointmentTestResources] as res
  join [AppointmentTests] as apt on res.AppointmentTestId=apt.id  
  join [Appointments] as ap on apt.AppointmentId=ap.Id
  join [Office] as office on ap.OfficeId=office.Id
  join [Practices] as prac on  office.PracticeId = prac.Id
  right join [AppointmentTypes] as typ on ap.[AppointmentTypeId]=typ.Id
  --join [AppointmentTypes] as aptyp on typ.[AppointmentTypeId]=aptyp.Id
  JOIN [PracticeDoctors] pracDocs ON ap.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  JOIN Demographics demo ON ap.PatientRecordId = demo.PatientRecordId
  right join [Tests] as tst on apt.TestId=tst.Id
  
  where(prac.Id=@practiceid)  and 
  ap.OfficeId = @officeId
  and (ap.[appointmentStatus] >@appointmentStatus AND ap.[appointmentStatus]<> @cancelledStatus) 
  and (ap.appointmentTime>@fromDate and ap.appointmentTime<@toDate) 
  and ap.[IsActive]=@isActive 
  and apt.IsActive=@isActive
    and LOWER( tst.testShortName )  =  'vp'
 --and res.assignedToUserId is not null 
   group by tst.testShortName  , res.assignedToUserId ,   apt.startTime

  HAVING count(*) > 1
end 
ELSE

begin             
SELECT count(*) as totalcount,
tst.testShortName , 
res.assignedToUserId ,
apt.startTime
  FROM [AppointmentTestResources] as res
  join [AppointmentTests] as apt on res.AppointmentTestId=apt.id  
  join [Appointments] as ap on apt.AppointmentId=ap.Id
  join [Office] as office on ap.OfficeId=office.Id
  join [Practices] as prac on  office.PracticeId = prac.Id
  right join [AppointmentTypes] as typ on ap.[AppointmentTypeId]=typ.Id
  --join [AppointmentTypes] as aptyp on typ.[AppointmentTypeId]=aptyp.Id
  JOIN [PracticeDoctors] pracDocs ON ap.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  JOIN Demographics demo ON ap.PatientRecordId = demo.PatientRecordId
  right join [Tests] as tst on apt.TestId=tst.Id
  
  where(prac.Id=@practiceid) 
  and (ap.[appointmentStatus] >@appointmentStatus AND ap.[appointmentStatus]<> @cancelledStatus) 
  and (ap.appointmentTime>@fromDate and ap.appointmentTime<@toDate) 
  and ap.[IsActive]=@isActive 
  and apt.IsActive=@isActive
   and LOWER( tst.testShortName )  =  'vp'
    --and res.assignedToUserId is not null 
 group by tst.testShortName  , res.assignedToUserId ,   apt.startTime
  HAVING count(*) > 1		  
end 
END


