﻿CREATE PROCEDURE [dbo].[GetDashboardMEDOPI002] (@PracticeDoctorId INT = NULL)
AS
BEGIN	
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @CurrentBenzodiazepine AS TABLE (PatientRecordId INT)
	DECLARE @CurrentOpioid AS TABLE (PatientRecordId INT)
	DECLARE @Paliative AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE 
	D.active = 0 -- Patient is active
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	--- Paliative care patients	
	INSERT INTO @Paliative
	SELECT PatientRecordId 	
	FROM BillDetails B
	WHERE  ( B.serviceCode like 'A945%' 
					or B.serviceCode like 'C945%' 
					or B.serviceCode like 'C882%'
					or B.serviceCode like 'C982%'
					or B.serviceCode like 'W872%'
					or B.serviceCode like 'W882%'
					or B.serviceCode like 'W972%'
					or B.serviceCode like 'W982%'
					or B.serviceCode like 'K023%'
					or B.serviceCode like 'B998%'
					or B.serviceCode like 'B966%'
					or B.serviceCode like 'B997%'
					or B.serviceCode like 'G511%'
					or B.serviceCode like 'G512%')
	UNION ALL 
	SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	CPP.DiagnosticCode like 'Z515%'
	AND Deleted = 0 AND CPP.UpdateDate IS NULL

	--- Current opioid prescription
	INSERT INTO @CurrentOpioid
	SELECT bp.PatientRecordId	
	FROM @BasePopulation bp
	join PatientMedications M on m.PatientRecordId = bp.PatientRecordId 
	WHERE 
	(
	MedicationName like '%Alfentanil%' or 
	MedicationName like '%Buprenorphin%' or 
	MedicationName like '%Butorphanol%' or 
	MedicationName like '%Codeine%' or 
	MedicationName like '%Dihydrocodeine%' or 
	MedicationName like '%Fentanyl%' or 
	MedicationName like '%Hydrocodone%' or 
	MedicationName like '%Meperidin%' or 
	MedicationName like '%Hydromorphone%' or 
	MedicationName like '%Methadone%' or 
	MedicationName like '%Morphine%' or 
	MedicationName like '%Nalbuphine%' or 
	MedicationName like '%Opium%' or 
	MedicationName like '%Oxycodone%' or 
	MedicationName like '%Oxymorphone%' or 
	MedicationName like '%Pentazocine%' or 
	MedicationName like '%Pethidine%' or 
	MedicationName like '%Remifentanil%' or 
	MedicationName like '%Sufentanil%' or 
	MedicationName like '%Tapentadol%' or 
	MedicationName like '%Tramadol%'
	or m.Ingredients like '%Alfentanil%' 
	OR m.Ingredients like '%Buprenorphin%'
	OR m.Ingredients like '%Butorphanol%'
	OR m.Ingredients like '%Codeine%'
	OR m.Ingredients like '%Dihydrocodeine%'
	OR m.Ingredients like '%Fentanyl%'
	OR m.Ingredients like '%Hydrocodone%'
	OR m.Ingredients like '%Meperidine%'
	OR m.Ingredients like '%Hydromorphone%'
	OR m.Ingredients like '%Methadone%'
	OR m.Ingredients like '%Morphine%'
	OR m.Ingredients like '%Nalbuphine%'
	OR m.Ingredients like '%Opium%'
	OR m.Ingredients like '%Oxycodone%'
	OR m.Ingredients like '%Oxymorphone%'
	OR m.Ingredients like '%Pentazocine%'
	OR m.Ingredients like '%Pethidine%'
	OR m.Ingredients like '%Remifentanil%'
	OR m.Ingredients like '%Sufentanil%'
	OR m.Ingredients like '%Tapentadol%' 
	OR m.Ingredients like '%Tramadol%'
	)
	AND m.IsActive = 1
	AND (m.DateExpired IS NULL and m.DateDiscontinued IS NULL)
	

	-- Current benzodiazepine prescriptions 
	INSERT INTO @CurrentBenzodiazepine
	SELECT bp.PatientRecordId	
	FROM @BasePopulation bp
	join PatientMedications M on m.PatientRecordId = bp.PatientRecordId 
	WHERE 
	(
	m.MedicationName like '%Alprazolam%' 
	OR m.MedicationName like '%Bromazepam%'
	OR m.MedicationName like '%Clobazam%'
	OR m.MedicationName like '%Clorazepate%'
	OR m.MedicationName like '%Chlordiazepoxide%'
	OR m.MedicationName like '%Clonazepam%'
	OR m.MedicationName like '%Diazepam%'
	OR m.MedicationName like '%Flurazepam%'
	OR m.MedicationName like '%Lorazepam%'
	OR m.MedicationName like '%Midazolam%'
	OR m.MedicationName like '%Oxazepam%'
	OR m.MedicationName like '%Quazepam%'
	OR m.MedicationName like '%Temazepam%'
	OR m.MedicationName like '%Triazolam%'
	or m.Ingredients like '%Alprazolam%' 
	OR m.Ingredients like '%Bromazepam%'
	OR m.Ingredients like '%Clobazam%'
	OR m.Ingredients like '%Clorazepate%'
	OR m.Ingredients like '%Chlordiazepoxide%'
	OR m.Ingredients like '%Clonazepam%'
	OR m.Ingredients like '%Diazepam%'
	OR m.Ingredients like '%Flurazepam%'
	OR m.Ingredients like '%Lorazepam%'
	OR m.Ingredients like '%Midazolam%'
	OR m.Ingredients like '%Oxazepam%'
	OR m.Ingredients like '%Quazepam%'
	OR m.Ingredients like '%Temazepam%'
	OR m.Ingredients like '%Triazolam%'
	)
	AND m.IsActive = 1
	AND (m.DateExpired IS NULL and m.DateDiscontinued IS NULL)
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId not IN (SELECT PatientRecordId FROM @Paliative)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @CurrentOpioid)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @CurrentBenzodiazepine)
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId not IN (SELECT PatientRecordId FROM @Paliative)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @CurrentOpioid)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @CurrentBenzodiazepine)
	
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments
	
	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log
	
END


