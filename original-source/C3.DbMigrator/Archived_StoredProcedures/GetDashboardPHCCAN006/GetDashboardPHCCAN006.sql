﻿CREATE PROCEDURE [dbo].[GetDashboardPHCCAN006] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @ColoExclusion AS TABLE (PatientRecordId INT)	
	DECLARE @ColoScreening AS TABLE (PatientRecordId INT, AgeMonths INT)
	DECLARE @ColoScreeningPending AS TABLE (PatientRecordId INT, AgeMonths INT)
	DECLARE @ColoScreeningDeclined AS TABLE (PatientRecordId INT, AgeMonths INT)
	DECLARE @Sigmoidoscopy AS TABLE (PatientRecordId INT, AgeMonths INT)
	DECLARE @Colonoscopy AS TABLE (PatientRecordId INT, Age<PERSON>onths INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientR<PERSON>ordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId  and MRP.IsActive = 1
	WHERE 
	D.active = 0														-- Pastient is active	
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) BETWEEN 50 AND 74	-- Patient is between 50 and 74
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	-- Colo Exclusion 
	INSERT INTO @ColoExclusion
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q142A'
	UNION ALL 
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('154','153','555','556','V10.05') 
		OR CPP.Problem_Description LIKE '%Colon ca%' 
		OR CPP.Problem_Description LIKE '%colorectal ca%'
		OR CPP.Problem_Description LIKE '%bowel ca%'
		OR CPP.Problem_Description LIKE '%Crohn%'
		OR CPP.Problem_Description LIKE '%Colitis%'
		OR CPP.Problem_Description LIKE '%Inflammatory Bowel Disease%'
		OR CPP.Problem_Description LIKE '%IBD%'
		OR CPP.Problem_Description LIKE '%colectomy%')
		AND Deleted = 0 AND CPP.UpdateDate IS NULL

	-- select * from @ColoExclusion where PatientRecordId in (select PatientRecordId from @BasePopulation)

	--- Colo Screening 

	--- Colo Screening in the last 24 months inclusive  plus overdue
	INSERT INTO @ColoScreening
	SELECT IM.PatientRecordId, DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) as AgeMonths	
	FROM @BasePopulation bp
	Join [VP_CPP_Immunization] IM on bp.PatientRecordId = im.PatientRecordId
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId in (4,21,27)							-- Type = Colo
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) <= 27
	UNION
	SELECT PatientRecordId, DATEDIFF(MONTH,b.serviceDate,GETDATE()) as AgeMonths
	FROM BillDetails B					--
	WHERE B.serviceCode = 'Q133A'
	AND DATEDIFF(MONTH,b.serviceDate,GETDATE()) <= 27
	
	
	--- Sigmoidoscopy
	INSERT into @Sigmoidoscopy
	SELECT PL.PatientRecordId, DATEDIFF(month,DATEFROMPARTS( iif(PL.ProcDate_Year=0, 1900,PL.ProcDate_Year) , iif(PL.ProcDate_Month=0, 01,PL.ProcDate_Month), iif(PL.ProcDate_day=0, 01,PL.ProcDate_day)),GETDATE()) as AgeMonths	
	FROM @BasePopulation bp
	Join VP_CPP_Problem_List PL on bp.PatientRecordId = pl.PatientRecordId
 	WHERE PL.Problem_Description like '%Sigmoidoscopy%'
	AND DATEDIFF(month,DATEFROMPARTS(iif(PL.ProcDate_Year=0, 1900,PL.ProcDate_Year), iif(PL.ProcDate_Month=0, 01,PL.ProcDate_Month), iif(PL.ProcDate_day=0, 01,PL.ProcDate_day)),GETDATE()) <= 63


	--- Colonoscopy
	INSERT into @Colonoscopy
	SELECT IM.PatientRecordId, DATEDIFF(month,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) as AgeMonths	
	FROM @BasePopulation bp
	Join [VP_CPP_Immunization] IM on bp.PatientRecordId = im.PatientRecordId
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId=18							-- Type = Colonoscopy
	AND DATEDIFF(month,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) <= 123
	Union
	SELECT PL.PatientRecordId, DATEDIFF(month,DATEFROMPARTS(iif(PL.ProcDate_Year=0, 1900,PL.ProcDate_Year), iif(PL.ProcDate_Month=0, 01,PL.ProcDate_Month), iif(PL.ProcDate_day=0, 01,PL.ProcDate_day)),GETDATE()) as AgeMonths
	FROM VP_CPP_Problem_List PL
	WHERE PL.Problem_Description like '%Colonoscopy%'
	AND DATEDIFF(YEAR,DATEFROMPARTS(iif(PL.ProcDate_Year=0, 1900,PL.ProcDate_Year), iif(PL.ProcDate_Month=0, 01,PL.ProcDate_Month), iif(PL.ProcDate_day=0, 01,PL.ProcDate_day)),GETDATE()) <= 123
	 
	 --select * from @ColoScreening where PatientRecordId in (select PatientRecordId from @BasePopulation) 



	--- Colo Screening Declined
	INSERT INTO @ColoScreeningDeclined
	SELECT IM.PatientRecordId,DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) AS AgeMonths	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId in (4,21,27)							-- Type = Colo
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) <= 36
	
  -- select * from @ColoScreeningDeclined where PatientRecordId in (select PatientRecordId from @BasePopulation)

	--- Colo Screening Pending
	INSERT INTO @ColoScreeningPending
	SELECT bp.PatientRecordId,DATEDIFF(MONTH,req.testTime,GETDATE()) AS AgeMonths	
	FROM @BasePopulation bp
	join RequisitionPatient rp on bp.PatientRecordId = rp.PatientRecordId
	join Requisition req on rp.id = req.requisitionPatientId
	where req.isActive = 1
	and req.requisitionStatus = 2
	and (req.requisitionItems like '%"text":"Colonscopy","value":"other5"%'
		or req.requisitionItems like '%"text":"FOBT","value":"other5"%'
		or req.requisitionItems like '%"text":"FIT","value":"other5"%')

	--- Segments	
		INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation bp
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoExclusion)											-- not excluded
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths <= 24)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths <= 60)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths <= 120)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreeningDeclined WHERE AgeMonths<=24)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreeningPending WHERE AgeMonths<=24)
		AND 
		(bp.PatientRecordId IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths BETWEEN 25 AND 27)
		OR bp.PatientRecordId IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths BETWEEN 121 AND 123)
		OR bp.PatientRecordId IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths BETWEEN 61 AND 64))
		
		INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation bp
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoExclusion)											-- not excluded
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths <= 27)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths <= 63)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths <= 123)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreeningDeclined WHERE AgeMonths<=24)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreeningPending WHERE AgeMonths<=24)

		INSERT INTO @Segments
	SELECT 3,PatientRecordId  -- results pending in last two months
		FROM @BasePopulation bp
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoExclusion)											-- not excluded
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths <= 24)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths <= 60)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths <= 120)		
		AND bp.PatientRecordId IN (SELECT PatientRecordId FROM @ColoScreeningPending WHERE AgeMonths<=2)

		INSERT INTO @Segments
	SELECT 4,PatientRecordId -- results pending in 3-24 months
		FROM @BasePopulation bp
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoExclusion)											-- not excluded
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths <= 24)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths <= 60)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths <= 120)		
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreeningPending WHERE AgeMonths<=2)
		AND bp.PatientRecordId IN (SELECT PatientRecordId FROM @ColoScreeningPending WHERE AgeMonths BETWEEN 3 AND 24)

		INSERT INTO @Segments
	SELECT 5,PatientRecordId
		FROM @BasePopulation bp
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoExclusion)											-- not excluded
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths <= 24)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths <= 60)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths <= 120)		
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreeningPending WHERE AgeMonths<=24)
		AND bp.PatientRecordId IN (SELECT PatientRecordId FROM @ColoScreeningDeclined WHERE AgeMonths <=24)

		INSERT INTO @Segments
	SELECT 6,PatientRecordId
		FROM @BasePopulation bp
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoExclusion)											-- not excluded
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths <= 20)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths <= 56)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths <= 116)		
		AND (bp.PatientRecordId IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths BETWEEN 21 AND 24)
			 OR bp.PatientRecordId IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths BETWEEN 117 AND 120)
			 OR bp.PatientRecordId IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths BETWEEn 57 AND 60))

		INSERT INTO @Segments
	SELECT 7,PatientRecordId
		FROM @BasePopulation bp
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoExclusion)											-- not excluded
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths <= 16)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths <= 52)
		AND bp.PatientRecordId NOT IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths <= 112)		
		AND (bp.PatientRecordId IN (SELECT PatientRecordId FROM @ColoScreening WHERE AgeMonths BETWEEN 17 AND 20)
			 OR bp.PatientRecordId IN (SELECT PatientRecordId FROM @Colonoscopy WHERE AgeMonths BETWEEN 113 AND 116)
			 OR bp.PatientRecordId IN (SELECT PatientRecordId FROM @Sigmoidoscopy WHERE AgeMonths BETWEEn 53 AND 56))

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
