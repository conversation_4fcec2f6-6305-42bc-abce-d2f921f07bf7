﻿CREATE PROCEDURE [dbo].[GetAppointmentModifiers]
	@appointmentIds AS dbo.IntegerList READONLY
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT
	  [Id]
      ,[AppointmentId]
      ,[userId]
      ,[reasonforChange]
      ,[changes]
      ,[createDate]
  FROM AppointmentModifiers appMods
  JOIN @appointmentIds appIds ON appMods.AppointmentId = appIds.IntegerValue
  ORDER BY createDate ASC

  END
