﻿CREATE PROCEDURE [dbo].[GetAppointmentTestSavedLogs] 
	@appointmentTestId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 
	 sl.Id
	,sl.Appointment<PERSON><PERSON>Id
	,sl.<PERSON>
	,sl.<PERSON><PERSON><PERSON>	
	,sl.SavedByUserId
	,sl.SaveType
	,sl.PracRootCategoryTempId AS PracticeTemplateId
	,rt.TemplateName
	,u.UserName
	,u.FirstName
	,u.LastName 
	FROM AppointmentTestSaveLogs sl
	JOIN PracticeRootCategoryTemplates pt ON sl.PracRootCategoryTempId = pt.Id
	JOIN RootTemplates rt ON rt.Id = pt.TemplateId
	LEFT JOIN AspNetUsers u on sl.SavedByUserId = u.UserID
	WHERE sl.AppointmentTestId = @appointmentTestId
	ORDER By sl.LogDate DESC
END