﻿CREATE PROCEDURE [dbo].[GetDashboardPHCSMK001] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @Smoking AS TABLE (PatientRecordId INT,Status NVARCHAR(10))
	DECLARE @MammogramScreening AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation	
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) >= 12	-- Patient is 12 and over		
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	--- Smoking Status
    INSERT INTO @Smoking
	SELECT M.PatientRecordId,UPPER(M.Value) AS [Status]    
    FROM VP_MeasurementSavedValue M  
    JOIN VPUniqueMeasurements UM ON M.VPUniqueMeasurementId =UM.Id 
    JOIN VP_AppointmentTestLog L ON M.VP_AppointmentTestLogId =L.Id  
    WHERE UM.[Name] = 'Smoking Status (Yes/No)'

    INSERT INTO @Smoking (PatientrecordId,[Status])
    SELECT PatientrecordId, 'Y' AS [Status]
    FROM VP_CPP_RiskFactor
    WHERE (RiskFactor LIKE'%smoker%' OR RiskFactor LIKE '%Tobacco%')
    AND NOT (RiskFactor LIKE'%ex%' OR RiskFactor LIKE '%prev%' OR 
	RiskFactor LIKE '%non%' OR ExposureDetails LIKE'%ex%' OR ExposureDetails LIKE '%prev%')
	AND Enddate IS NULL AND deleted = 0
       
	Insert into @Smoking (PatientrecordId,[Status])
    SELECT PatientrecordId, 'N' AS [Status]
    FROM VP_CPP_RiskFactor
    WHERE (RiskFactor LIKE '%smoker%' OR RiskFactor LIKE '%Tobacco%')
    AND   (RiskFactor LIKE '%ex%' OR RiskFactor LIKE '%prev%' OR 
	RiskFactor LIKE '%non%' OR ExposureDetails LIKE '%ex%' OR ExposureDetails LIKE '%prev%')
	AND Enddate IS NULL AND deleted = 0
	
	--- Segments	
		INSERT INTO @Segments
	SELECT 1,PatientRecordId
	
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @Smoking WHERE Status = 'Y')
	
		INSERT INTO @Segments
	SELECT 2,PatientRecordId
	
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @Smoking WHERE Status = 'N')

	
		INSERT INTO @Segments
	SELECT 3,PatientRecordId
	
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @Smoking)

			--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
