﻿CREATE PROCEDURE [dbo].[GetDashboardPHCOBES001] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @ObesityBMI AS TABLE (PatientRecordId INT,BMI FLOAT,appointmentTime DATETIME2)
	DECLARE @ObesityHeight AS TABLE (PatientRecordId INT,Height FLOAT,appointmentTime DATETIME2)
	DECLARE @LastObesityBMI AS TABLE (PatientRecordId INT,BMI FLOAT)
	DECLARE @LastObesityHeight AS TABLE (PatientRecordId INT,Height FLOAT)
	DECLARE @Pregnancy AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation	
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) >= 18	-- Patient is 18 and over		
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	--- Current Pregnancy
	INSERT INTO @Pregnancy
	SELECT PatientRecordId	
	FROM BillDetails B
	WHERE B.serviceCode = 'P003A'
	AND DATEDIFF(MONTH,B.date,GETDATE()) <= 9

	--- Obesity/Overweight Height
	--- Documented height between 91.4 cm and 210.8 cm 
	INSERT INTO @ObesityHeight
	SELECT BP.PatientRecordId,TRY_CONVERT(FLOAT,VPV.Value) AS Height,appointmentTime --CAD.PatientRecordId	
	FROM VPMeasurementSavedValues VPV
	JOIN VPUniqueMeasurements VPM on VPV.VPUniqueMeasurementId = VPM.ID
	JOIN AppointmentTestSaveLogs ATSL ON VPV.AppointmentTestSaveLogId = ATSL.Id
	JOIN AppointmentTests APPT ON APPT.Id = ATSL.AppointmentTestId
	JOIN Appointments APP ON APP.Id = APPT.AppointmentId
	JOIN @BasePopulation BP ON BP.PatientRecordId = APP.PatientRecordId -- Base Population
	--JOIN @CADCoded CAD ON CAD.PatientRecordId = BP.PatientRecordId -- Only CAD Coded
	WHERE DATEDIFF(MONTH,APP.appointmentTime,GETDATE()) <= 12 -- Appointments in the last year
	AND APP.appointmentTime <= GETDATE() -- No future appointments
	AND APPT.TestId = 29 -- VP Only
	AND APPT.IsActive = 1 -- Not Canceled
	AND ATSL.IsActive = 1 AND APPT.IsActive = 1	
	AND (VPM.Name LIKE '%Height%')
	AND ISNUMERIC(VPV.Value) = 1

	--- Documented BMI
	INSERT INTO @ObesityBMI
	SELECT BP.PatientRecordId,TRY_CONVERT(FLOAT,VPV.Value) AS BMI,appointmentTime
	FROM VPMeasurementSavedValues VPV
	JOIN VPUniqueMeasurements VPM on VPV.VPUniqueMeasurementId = VPM.ID
	JOIN AppointmentTestSaveLogs ATSL ON VPV.AppointmentTestSaveLogId = ATSL.Id
	JOIN AppointmentTests APPT ON APPT.Id = ATSL.AppointmentTestId
	JOIN Appointments APP ON APP.Id = APPT.AppointmentId
	JOIN @BasePopulation BP ON BP.PatientRecordId = APP.PatientRecordId -- Base Population
	--JOIN @CADCoded CAD ON CAD.PatientRecordId = BP.PatientRecordId -- Only CAD Coded
	WHERE DATEDIFF(MONTH,APP.appointmentTime,GETDATE()) <= 12 -- Appointments in the last year
	AND APP.appointmentTime <= GETDATE() -- No future appointments
	AND APPT.TestId = 29 -- VP Only
	AND APPT.IsActive = 1 -- Not Canceled
	AND ATSL.IsActive = 1 AND APPT.IsActive = 1	
	AND (VPM.Name LIKE '%BMI%')	
	AND ISNUMERIC(VPV.Value) = 1

	-- Only the latest BMI/Height
	INSERT INTO @LastObesityBMI
	SELECT T1.PatientRecordId,T1.BMI
	FROM @ObesityBMI T1 
	JOIN ( SELECT PatientRecordId,MAX(appointmentTime) AS LastAppTime FROM @ObesityBMI GROUP BY PatientRecordId) T2
	ON T1.PatientRecordId = T2.PatientRecordId AND T1.appointmentTime = T2.LastAppTime 

	INSERT INTO @LastObesityHeight
	SELECT T1.PatientRecordId,T1.Height
	FROM @ObesityHeight T1 
	JOIN ( SELECT PatientRecordId,MAX(appointmentTime) AS LastAppTime FROM @ObesityHeight GROUP BY PatientRecordId) T2
	ON T1.PatientRecordId = T2.PatientRecordId AND T1.appointmentTime = T2.LastAppTime
	WHERE T1.Height BETWEEN 91.4 AND 210.8	
	
	--- Segments	
		INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @Pregnancy)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityHeight)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityBMI WHERE BMI >= 40)

		INSERT INTO @Segments
	SELECT 2,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @Pregnancy)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityHeight)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityBMI WHERE BMI > 35 AND BMI < 40)
	
		INSERT INTO @Segments
	SELECT 3,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @Pregnancy)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityHeight)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityBMI WHERE BMI >= 30 AND BMI < 35)
	
		INSERT INTO @Segments
	SELECT 4,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @Pregnancy)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityHeight)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityBMI WHERE BMI >= 25 AND BMI < 30)
	
		INSERT INTO @Segments
	SELECT 5,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @Pregnancy)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityHeight)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityBMI WHERE BMI >= 18.5 AND BMI < 25)
	
		INSERT INTO @Segments
	SELECT 6,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @Pregnancy)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityHeight)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityBMI WHERE BMI < 18.5)
	
		INSERT INTO @Segments
	SELECT 7,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @Pregnancy)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @LastObesityHeight)
	AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @LastObesityBMI)
	
			--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log	

END
