﻿CREATE PROCEDURE [dbo].[GetDaysheetPreconditions]
	@appointmentIds AS dbo.IntegerList READONLY
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 
	  [Id]
      ,[Type]
      ,[Status]
      ,[AppointmentId]
  FROM AppointmentPreconditons
  WHERE AppointmentId IN (SELECT IntegerValue FROM @appointmentIds)
  END
