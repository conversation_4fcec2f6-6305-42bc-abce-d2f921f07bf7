﻿CREATE PROCEDURE [dbo].[GetExternalDoctorLocations]
	@externalDoctorId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 	
	 ea.Id AS ExternalDoctorAddressId
	,ea.ExternalDoctorId As ExternalDoctorId
	,ea.addressName AS AddressName
	,ea.addressLine1 AS AddressLine1
	,ea.addressLine2 AS AddressLine2
	,ea.addressType AS AddressType
	,ea.city AS City
	,ea.postalCode AS PostalCode
	,ea.province AS Province
	,ea.country AS Country
	,ea.IsActive AS IsActiveAddress
	,ISNULL(ph.ExternalDoctorPhoneNumberId,0) AS ExternalDoctorPhoneNumberId
	,ph.faxNumber AS FaxNumber
	,ph.phoneNumber AS PhoneNumber
	,ISNULL(ph.typeOfPhoneNumber,0) AS TypeOfPhoneNumber
	,ph.extention AS PhoneNumberExtension
	,ph.IsActive As IsActivePhone
	,ph.IsActiveLocation
	,ISNULL(ph.ExternalDoctorLocationId,0) AS ExternalDoctorLocationId
	FROM ExternalDoctorAddresses ea 
	LEFT JOIN (
		SELECT el.ExternalDoctorAddressId
		,el.Id AS ExternalDoctorLocationId
		,el.ExternalDoctorPhoneNumberId 
		,ep.phoneNumber
		,ep.faxNumber
		,ep.typeOfPhoneNumber
		,ep.extention
		,ep.IsActive
		,el.IsActive As IsActiveLocation
		FROM ExternalDoctorLocations el 
		JOIN ExternalDoctorPhoneNumbers ep ON ep.Id = el.ExternalDoctorPhoneNumberId
		WHERE ep.ExternalDoctorId = @externalDoctorId)
	 AS ph ON ph.ExternalDoctorAddressId = ea.Id -- select the phone numbers
	WHERE ea.ExternalDoctorId = @externalDoctorId
	
END