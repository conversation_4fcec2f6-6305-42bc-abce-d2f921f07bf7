﻿CREATE PROCEDURE [dbo].[GetDashboardCAREBNS003] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @CurrentDate AS DATE,@FYDate AS DATE
	SELECT @CurrentDate = GETDATE()
	SELECT @FYDate = STR(IIF(MONTH(GETDATE())<4,YEAR(GETDATE()),YEAR(GETDATE()+1))) + '-03-31' -- FiscalYear CutOffDate

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @CancerExclusion AS TABLE (PatientRecordId INT)
	DECLARE @FOBTorFIT AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)
	
	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	JOIN DemographicsEnrollments ROSTER ON MRP.id = ROSTER.DemographicsMRPId -- this checks for roster status, active if no termination date
	WHERE 
	D.active = 0	
	AND MRP.IsActive = 1 -- Rostered ( Source: sp_GetRecallList)
	AND (DATEDIFF(DD,D.dateOfBirth,@FYDate) / 365.5) BETWEEN 50 AND 74	
	AND (MRP.PracticeDoctorId = @PracticeDoctorId) --OR @PracticeDoctorId IS NULL)
	AND ROSTER.enrollmentTerminationDate IS NULL -- Roster not terminated
	GROUP BY D.PatientRecordId

	--- Colorectal Cancer Screening Exclusion
	INSERT INTO @CancerExclusion
	SELECT PatientRecordId 	
	FROM BillDetails B
	WHERE B.serviceCode = 'Q142A'
	AND DATEDIFF(MONTH,b.serviceDate,@FYDate) <= 30		-- date of service within 30 months
	UNION ALL 
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('154','153','555','556','V10.05') 
		OR CPP.Problem_Description LIKE '%Colon ca%' 
		OR CPP.Problem_Description LIKE '%colorectal ca%'
		OR CPP.Problem_Description LIKE '%bowel ca%'
		OR CPP.Problem_Description LIKE '%Crohn%'
		OR CPP.Problem_Description LIKE '%Colitis%'
		OR CPP.Problem_Description LIKE '%Inflammatory Bowel Disease%'
		OR CPP.Problem_Description LIKE '%IBD%'
		OR CPP.Problem_Description LIKE '%colectomy%')
		AND Deleted = 0 AND UpdateDate IS NULL
	UNION ALL
	SELECT IM.PatientRecordId
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId IN (18, 21)							-- Type = colonoscopy
	AND DATEDIFF(YEAR,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),@FYDate) <= 10

	--- FOBT or FIT
	---- FOBT
	INSERT INTO @FOBTorFIT
	SELECT PatientRecordId	
	FROM BillDetails B
	WHERE B.serviceCode = 'Q133A'	
	AND DATEDIFF(MONTH,b.serviceDate,@FYDate) <= 30		-- date of service within 30 months
	UNION ALL
	SELECT IM.PatientRecordId
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	and IM.VP_CPP_ImmunizationTypeId=4							-- Type = FOBT
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),@FYDate) <= 30
	---- FIT
	UNION ALL
	SELECT IM.PatientRecordId
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	and IM.VP_CPP_ImmunizationTypeId=27							-- Type = FIT
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),@FYDate) <= 30
	---- Colorectal Cancer Screening
	UNION ALL
	SELECT IM.PatientRecordId
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	and IM.VP_CPP_ImmunizationTypeId=21							-- Type = Colocteral Cancer Screening
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),@FYDate) <= 30
		
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @CancerExclusion)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @FOBTorFIT)	
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId	
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @CancerExclusion)
	AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @FOBTorFIT)	

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments		

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
