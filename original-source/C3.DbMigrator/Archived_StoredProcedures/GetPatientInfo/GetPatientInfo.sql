﻿CREATE  PROCEDURE [dbo].[GetPatientInfo]
	@patientId INT,
	@appointmentId INT = NULL,
	@testId INT = NULL,
	@activeStatus INT = 0  -- 0 = Active, 1 = NotActive, 2 = Other, 3 = Deceased
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @AppointmentDate AS DATETIME;
	IF @appointmentId IS NOT NULL SELECT @AppointmentDate = appointmentTime FROM Appointments WHERE Id = @appointmentId;

    -- Insert statements for procedure here
	SELECT 
	d.PracticeId
	,d.DemographicId
	,d.<PERSON>tion
	,d.PatientId AS PatientRecordId
	,d.<PERSON>astName AS aliasLastName
	,d.<PERSON>asFirstName AS aliasFirstName
	,d.AliasMiddleName AS aliasMiddleName
	,d.UseAliases AS useAliases
	,ISNULL(d.PreferredName,'') AS preferredName
	,d.FirstName AS firstName
	,d.MiddleName AS middleName
	,d.LastName AS lastName	
	,d.DateOfBirth AS dateOfBirth
	,d.<PERSON>
	,d.Gender AS gender
	,d.HealthCard AS OHIP
	,d.HealthCardCode AS OHIPVersionCode
	,d.HealthCardProvince
	,d.PhoneNumbers AS PatientPhoneNumbers
	,d.DefaultPaymentMethod
	,app.appointmentTime
	,app.actionOnAbnormal
	,appPriority.PriorityName
	,ISNULL((SELECT TOP 1 isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')		               
			            FROM ExternalDoctors ed 
						WHERE Id = app.referralDoctorId),'') AS AppointmentReferralDoctor
	,ISNULL((SELECT TOP 1 isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')		               
			            FROM ExternalDoctors ed 
						JOIN AppointmentTests appTest ON ed.Id=appTest.referralDoctorId
						WHERE appTest.AppointmentId=app.Id AND appTest.TestId=@testId AND ed.Id = appTest.referralDoctorId),'') AS TestReferralDoctor
	,ISNULL((SELECT TOP 1 t.testFullName FROM Tests t WHERE Id = @testId),'') AS TestName
	,ISNULL((SELECT TOP 1 isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')		               
			            FROM ExternalDoctors as ed 
						JOIN DemographicsMainResponsiblePhysicians mrp ON ed.Id = mrp.ExternalDoctorId
						WHERE mrp.DemographicId = d.DemographicId AND mrp.IsActive = 1
						ORDER BY mrp.Id DESC),'') AS MRP
	,ISNULL((SELECT TOP 1 isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')		               
			            FROM ExternalDoctors as ed 
						JOIN DemographicsFamilyDoctors fd ON ed.Id = fd.ExternalDoctorId
						WHERE fd.DemographicId = d.DemographicId AND fd.IsActive = 1 AND fd.IsRemoved=0
						ORDER BY fd.Id DESC),'') AS FamilyDoctor
	,ISNULL((SELECT TOP 1 isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')		               
			            FROM ExternalDoctors as ed 
						JOIN DemographicsDefaultReferralDoctors rd ON ed.Id = rd.ExternalDoctorId
						WHERE rd.DemographicId = d.DemographicId AND rd.IsActive = 1
						ORDER BY rd.Id DESC),'') AS DefaultReferralDoctor	
	FROM 
	fn_GetPatientInfo(@patientId,@AppointmentDate) d
	LEFT JOIN (SELECT TOP 1 * FROM Appointments WHERE Id = @appointmentId AND IsActive = 1) app ON d.PatientId = app.PatientRecordId
	LEFT JOIN AppointmentPriority appPriority ON app.AppointmentPriorityId = appPriority.Id	
	
END


