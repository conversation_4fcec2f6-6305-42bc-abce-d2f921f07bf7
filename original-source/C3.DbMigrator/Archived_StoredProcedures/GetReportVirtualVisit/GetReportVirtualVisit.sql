﻿CREATE PROCEDURE [dbo].[GetReportVirtualVisit]
	@PracticeId INT,
	@StartDate DATETIME,
	@EndDate DATETIME,
	@includeVideoVisit BIT = 1,
	@includeSecureMessage BIT = 1,
	@AuditDBName NVARCHAR(50)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @fullStatementSql NVARCHAR(MAX);  
	DECLARE @querySql NVARCHAR(4000);
	DECLARE @whereSql NVARCHAR(1000);
    DECLARE @paramsSql NVARCHAR(1000);  
	DECLARE @groupBySql NVARCHAR(300);	

	DECLARE @splitByNewLine NVARCHAR(100) = ''+ '<BR>';

	DECLARE @PracticeName NVARCHAR(100) = (SELECT PracticeName FROM Practices WHERE Id = @PracticeId);

	DECLARE @IsAuditAvailable BIT = ISNULL((SELECT TOP 1 1 FROM sys.databases WHERE name = @AuditDBName),0); /* When <PERSON> is not hosted in the same server, might need a liked server */

	DECLARE @SolutionId NVARCHAR(30) = 'Cerebrum';
	DECLARE @Physician BIT = 1;

	SET @StartDate = CAST(CONVERT(VARCHAR, @StartDate, 112)+' 00:00:00' AS DATETIME);
	SET @EndDate = CAST(CONVERT(VARCHAR, @EndDate, 112)+' 23:59:59' AS DATETIME);	
	
	DECLARE @EventDetailsTable AS TABLE (
		EventId NVARCHAR(100),
		AppointmentId INT,
		PracticeId INT NOT NULL,
		PracticeName NVARCHAR(50) NOT NULL,				
		EventActivity NVARCHAR(100),
		EventDetails NVARCHAR(4000),
		EventUrl NVARCHAR(400),
		EventStartDate DATETIME2,
		EventEndDate DATETIME2,
		EventType NVARCHAR(100),
		EventModality NVARCHAR(100),
		UserLogin NVARCHAR(100),
		UserFirstName NVARCHAR(100),
		UserLastName NVARCHAR(100),
		UserLocation NVARCHAR(100),
		PatientFirstName NVARCHAR(100),
		PatientLastName NVARCHAR(100),
		PatientLocation NVARCHAR(100),
		IsPatient BIT DEFAULT 0
	)

	IF @includeVideoVisit = 1
	BEGIN
		DECLARE @VirtualVisitRooms AS TABLE (
			Id UNIQUEIDENTIFIER NOT NULL PRIMARY KEY,
			PracticeId INT NOT NULL,
			AppointmentId INT NOT NULL,
			HasScreenSharing BIT NOT NULL,
			DurationInMinute INT NOT NULL,
			RoomOpenedDateTime DATETIME2,
			RoomCompletedDateTime DATETIME2
		)

		SET @querySql = 
		N'SELECT Id,PracticeId,AppointmentId,HasScreenSharing,DurationInMinute,RoomOpenedDateTime,RoomCompletedDateTime
		FROM VirtualVisitRooms VVR
		WHERE VVR.PracticeId = @PracticeId AND VVR.RoomOpenedDateTime BETWEEN @StartDate AND @EndDate ';		

		SET @fullStatementSql = @querySql;

		SET @paramsSql = N'@PracticeId INT, @StartDate DATETIME, @EndDate DATETIME';

		INSERT INTO @VirtualVisitRooms (Id,PracticeId,AppointmentId,HasScreenSharing,DurationInMinute,RoomOpenedDateTime,RoomCompletedDateTime)
		EXEC sp_executesql @fullStatementSql, @paramsSql, @PracticeId = @PracticeId, @StartDate = @StartDate, @EndDate = @EndDate;
		
		IF (@IsAuditAvailable=1)
		BEGIN
			DECLARE @VirtualVisitLogs AS TABLE (
					Id bigint NOT NULL,
					VirtualVisitRoomId nvarchar(256) NULL,
					EventStartDate datetime NOT NULL,
					EventTypeId int NULL,
					EventActivity nvarchar(256) NULL,
					EventModality nvarchar(50) NULL,
					PatientId int NULL,
					IpAddress nvarchar(50) NULL,
					EventDetails nvarchar(256) NULL,
					PracticeId int NULL,
					AppointmentId int NULL
			)

			SET @querySql = 
			N'SELECT VVL.Id,VirtualVisitRoomId,EventStartDate,EventTypeId,EventActivity,EventModality,PatientId,IpAddress,EventDetails,VVR.PracticeId,VVR.AppointmentId
			FROM '+ @AuditDBName+ '.dbo.VirtualVisitLogs VVL
			JOIN VirtualVisitRooms VVR ON VVR.Id = VVL.VirtualVisitRoomId
			WHERE VVR.PracticeId = @PracticeId AND VVL.EventStartDate BETWEEN @StartDate AND @EndDate ';	

			SET @fullStatementSql = @querySql;

			SET @paramsSql = N'@PracticeId INT, @StartDate DATETIME, @EndDate DATETIME';

			INSERT INTO @VirtualVisitLogs (Id,VirtualVisitRoomId,EventStartDate,EventTypeId,EventActivity,EventModality,PatientId,IpAddress,EventDetails,PracticeId,AppointmentId)
			EXEC sp_executesql @fullStatementSql, @paramsSql, @PracticeId = @PracticeId, @StartDate = @StartDate, @EndDate = @EndDate;

--			select * from @VirtualVisitLogs
		
		END		

		INSERT INTO @EventDetailsTable (EventId,AppointmentId,PracticeId,PracticeName,EventActivity,EventDetails,EventStartDate,EventEndDate,EventType,EventModality,UserLogin,UserFirstName,UserLastName,PatientFirstName,PatientLastName,PatientLocation,IsPatient)

		SELECT VVR.Id,VVR.AppointmentId,@PracticeId,@PracticeName,EventActivity,EventDetails AS EventDetails,EventStartDate,EventEndDate,
		'Clinical-Direct' AS EventType,'Video Conference' AS EventModality,U.UserName,ED.firstName AS UserFirstName,ED.lastName AS UserLastName,D.firstName AS PatientFirstName,D.lastName AS PatientLastName,PatientLocation,IsPatient
		FROM
		(
			SELECT Id,PracticeId,AppointmentId,'Started Video' AS EventActivity,NULL AS EventDetails,RoomOpenedDateTime AS EventStartDate,RoomCompletedDateTime AS EventEndDate,NULL AS PatientLocation,0 AS IsPatient
			FROM @VirtualVisitRooms

			UNION ALL

			SELECT VRR.Id,Practiceid,VRR.AppointmentId,
			'Saved Visit Page' AS EventActivity,
			--LEFT(ROO.CategoryName + ': ' + NULLIF(LTRIM(RTRIM(SavedValue)),'') ,4000) AS EventDetails,
			LEFT(STUFF((SELECT @splitByNewLine + '<B>' + ROO.CategoryName + ':' + '</B>' + @splitByNewLine + NULLIF(LTRIM(RTRIM(SavedValue)),'') + @splitByNewLine
					   FROM
					   RootCategorySavedValues RC
					   JOIN RootCategories ROO ON ROO.Id = RC.RootCategoryId
					   WHERE RC.AppointmentTestSaveLogId = SL.Id
                       FOR XML PATH(''),TYPE).value('.','NVARCHAR(4000)'),1,4,''),4000)/* Fixed HTML Tags */ AS EventDetails,			
			SL.DateCreated AS EventStartDate,
			NULL AS EventEndDate,
			NULL AS PatientLocation,
			0 AS IsPatient
			FROM @VirtualVisitRooms VRR
			JOIN AppointmentTests APT ON APT.AppointmentId = VRR.AppointmentId AND APT.TestId = 29
			JOIN AppointmentTestSaveLogs SL ON SL.AppointmentTestId = APT.Id
			--JOIN RootCategorySavedValues RC ON RC.AppointmentTestSaveLogId = SL.Id
			--JOIN RootCategories ROO ON ROO.Id = RC.RootCategoryId
			WHERE LogDate BETWEEN VRR.RoomOpenedDateTime AND VRR.RoomCompletedDateTime
			
			UNION ALL

			SELECT VVL.VirtualVisitRoomId,VVL.PracticeId,VVL.AppointmentId,VVL.EventActivity,VVL.EventDetails,VVL.EventStartDate,NULL AS EventEndDate,VVL.IpAddress AS PatientLocation,1 AS IsPatient
			FROM @VirtualVisitLogs VVL
			
		) AS VVR
		JOIN Appointments A ON A.Id = VVR.AppointmentId
		JOIN PracticeDoctors PD ON PD.Id = A.PracticeDoctorId
		JOIN ExternalDoctors ED ON ED.Id = PD.ExternalDoctorId
		JOIN AspNetUsers U ON U.Id = PD.applicationUserId
		JOIN Demographics D ON D.PatientRecordId = A.PatientRecordId		
		
		--SELECT * FROM @VirtualVisitRooms
	END

	IF @includeSecureMessage = 1
	BEGIN
		/* Secure message module store data in contact manager */		

		DECLARE @CM_TaskMessage AS TABLE (
			CM_TaskDefinitionId INT NOT NULL,
			CM_TaskMessageId INT NOT NULL,
			message NVARCHAR(MAX),
			messageCreateDate DATETIME2,
			messageCreator NVARCHAR(256),			
			ADB2CUserPatientsId NVARCHAR(256),
			PatientRecordId INT
		)

		/* From Patient to Doctor */
		SET @querySql = 
		N'SELECT TM.CM_TaskDefinitionId,TM.Id,TM.message,TM.messageCreateDate,TM.messageCreator,TM.ADB2CUserPatientsId,TD.PatientRecordId
		FROM CM_TaskDefinition TD
		JOIN CM_TaskMessage TM ON TM.CM_TaskDefinitionId = TD.id 
		JOIN PatientRecords P ON TD.PatientRecordId = P.Id
		WHERE P.PracticeId = @PracticeId AND TM.messageCreatedate BETWEEN @StartDate AND @EndDate '

		SET @whereSql = N' AND TM.messageCreator IN (SELECT Id FROM AspNetUsers WHERE UserName IN (''<EMAIL>'',''<EMAIL>'') ) ' 
		SET @whereSql = @whereSql + N' AND ADB2CUserPatientsId IS NOT NULL '
		SET @fullStatementSql = @querySql + @whereSql;

		SET @paramsSql = N'@PracticeId INT, @StartDate DATETIME, @EndDate DATETIME';

		INSERT INTO @CM_TaskMessage (CM_TaskDefinitionId,CM_TaskMessageId,message,messageCreateDate,messageCreator,ADB2CUserPatientsId,PatientRecordId)
		EXEC sp_executesql @fullStatementSql, @paramsSql, @PracticeId = @PracticeId, @StartDate = @StartDate, @EndDate = @EndDate;
		
		/* From Doctor to Patient - Admin to Admin - Admin to Doctor */
		SET @querySql = 
		N'SELECT TM.CM_TaskDefinitionId,TM.Id,TM.message,TM.messageCreateDate,TM.messageCreator,TM.ADB2CUserPatientsId,TD.PatientRecordId
		FROM CM_TaskDefinition TD
		JOIN CM_TaskMessage TM ON TM.CM_TaskDefinitionId = TD.id 		
		WHERE TD.PracticeId = @PracticeId AND TM.messageCreatedate BETWEEN @StartDate AND @EndDate '
		
		SET @whereSql = ''
		SET @whereSql = @whereSql + N' AND ADB2CUserPatientsId IS NULL '
		SET @fullStatementSql = @querySql + @whereSql;

		SET @paramsSql = N'@PracticeId INT, @StartDate DATETIME, @EndDate DATETIME';

		INSERT INTO @CM_TaskMessage (CM_TaskDefinitionId,CM_TaskMessageId,message,messageCreateDate,messageCreator,ADB2CUserPatientsId,PatientRecordId)
		EXEC sp_executesql @fullStatementSql, @paramsSql, @PracticeId = @PracticeId, @StartDate = @StartDate, @EndDate = @EndDate;

		--SELECT * FROM @CM_TaskMessage

		INSERT INTO @EventDetailsTable (EventId,PracticeId,PracticeName,EventActivity,EventDetails,EventStartDate,EventEndDate,EventType,EventModality,UserFirstName,UserLastName,PatientFirstName,PatientLastName)

		SELECT CM_TaskMessageId AS EventId,@PracticeId,@PracticeName,EventActivity,message AS EventDetails,EventStartDate,EventEndDate,
		'Clinical-Direct' AS EventType,'Secure Message' AS EventModality,CM.UserFirstName AS UserFirstName,CM.UserLastName AS UserLastName,D.firstName AS PatientFirstName,D.lastName AS PatientLastName
		FROM
		(
			SELECT CM_TaskMessageId,'Added secure message' AS EventActivity,TM.message,messageCreateDate AS EventStartDate,NULL AS EventEndDate,PatientRecordId,U.FirstName AS UserFirstName,U.LastName AS UserLastName
			FROM @CM_TaskMessage TM
			LEFT JOIN AspNetUsers U ON U.Id = TM.messageCreator
			
		) AS CM
		LEFT JOIN Demographics D ON D.PatientRecordId = CM.PatientRecordId

		/* Add attachments */
		INSERT INTO @EventDetailsTable (EventId,PracticeId,PracticeName,EventActivity,EventDetails,EventStartDate,EventEndDate,EventType,EventModality,UserFirstName,UserLastName,PatientFirstName,PatientLastName)
		SELECT DISTINCT TM.CM_TaskMessageId,PracticeId,PracticeName,'Added attachment' AS EventActivity,RR.fileName,EventStartDate,EventEndDate,EventType,EventModality,UserFirstName,UserLastName,PatientFirstName,PatientLastName
		FROM @EventDetailsTable E
		JOIN @CM_TaskMessage TM ON TM.CM_TaskMessageId = E.EventId AND E.EventModality = 'Secure Message'
		JOIN CM_TaskReport TR ON TR.CM_TaskDefinitionId = TM.CM_TaskDefinitionId
		JOIN ReportReceiveds RR ON RR.Id = TR.reportId		

	END

	--> Get IPAddress from LoginHistory
	DECLARE @EMRUsersAddresses AS TABLE (
		IdIndex INT IDENTITY(1,1),
		EventId NVARCHAR(50),
		UserLogin NVARCHAR(50),
		LoginDate DATETIME2,
		LoginMaxDate DATETIME2,
		IpAddress NVARCHAR(50)
	)

	INSERT INTO @EMRUsersAddresses (EventId,UserLogin,LoginDate,LoginMaxDate)
	SELECT NULL AS EventId,UserLogin,CAST(EventStartDate AS DATE),MAX(EventStartDate)
	FROM @EventDetailsTable T
	GROUP BY T.UserLogin,CAST(EventStartDate AS DATE)
	
	DECLARE @IdIndex INT = 1;

	DECLARE @LogUserName NVARCHAR(50);
	DECLARE @LogStartDate DATETIME2;
	DECLARE @LogEndDate DATETIME2;
	DECLARE @LogIpAddress NVARCHAR(50);

	--SELECT * FROM @EMRUsersAddresses

	WHILE EXISTS (SELECT * FROM @EMRUsersAddresses WHERE IdIndex = @IdIndex)
	BEGIN
		SELECT @LogUserName = UserLogin, @LogStartDate = LoginDate, @LogEndDate = LoginMaxDate FROM @EMRUsersAddresses WHERE IdIndex = @IdIndex;

		SET @LogIpAddress = NULL;

		IF (@LogUserName IS NOT NULL AND @LogStartDate IS NOT NULL AND @LogEndDate IS NOT NULL)
		BEGIN				

			SELECT TOP 1 @LogIpAddress = IpAddress
			FROM LoginHistory WHERE UserName = @LogUserName
			AND DateCreated BETWEEN @LogStartDate AND @LogEndDate
			ORDER BY DateCreated DESC;

			IF @LogIpAddress IS NOT NULL
			BEGIN
				UPDATE @EventDetailsTable SET UserLocation = @LogIpAddress
				WHERE UserLogin = @LogUserName
				AND EventStartDate BETWEEN @LogStartDate AND @LogEndDate
			END				
		END			

		SET @IdIndex = @IdIndex + 1
	END				

	--> Final Select
	SELECT @SolutionId AS SolutionId,PracticeName AS OrgId,AppointmentId AS EventId,IIF(IsPatient = 1,PatientFirstName,UserFirstName) AS UserFirstName,IIF(IsPatient = 1,PatientLastName,UserLastName) AS UserLastName,
	IIF(IsPatient = 1,CAST(0 AS BIT),@Physician) AS Physician,EventActivity,EventDetails,EventStartDate,EventEndDate,EventType,EventModality,PatientFirstName,PatientLastName,
	UserLocation AS EMRUserLocation,IIF(IsPatient = 1,PatientLocation,NULL) AS PatientLocation,UserLogin
	FROM
	@EventDetailsTable
	ORDER BY EventStartDate ASC

END