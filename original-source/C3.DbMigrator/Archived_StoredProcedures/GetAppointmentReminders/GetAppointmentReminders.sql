﻿CREATE PROCEDURE [dbo].[GetAppointmentReminders]
	@reminderType NVARCHAR(100),
	@officeId int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLAR<PERSON> @reminderTypeId int = ISNULL((SELECT TOP 1 id FROM ReminderTypes WHERE [name]=@reminderType),0);
    DECLARE @isActive int = 1;	
	DECLAR<PERSON> @toBeConfirmed int = 0;
	DECLARE @cancelFlag int = 1;	
	DECLARE @waitListStatusId int = 1; -- waitlist status
	DECLARE @triageStatusId int = 16; -- Triage status
	DECLARE @cancelledStatusId int = 7; -- cancelled status
	DECLARE @cancellListStatusId int = 7; -- cancelation list status
	DECLARE @currentDate datetime = GETDATE();
    DECLARE @typeOfPhoneNumber bit;
    DECLARE @typeOfPhoneNumberCell bit;
	DECLARE @PracticeID int;

	<PERSON><PERSON><PERSON><PERSON> @reminderOffices TABLE
	   (  
		  id                        int identity(1,1) not null,
	      officeId                  int not null,
		  practiceId                int not null,
		  officeName                nvarchar(200) not null,
		  officeBusinessName        nvarchar(200) not null,
		  address1                  nvarchar(100) null,
		  address2                  nvarchar(100) null,
		  city                      nvarchar(100) null,
		  phone                     nvarchar(100) null,
		  fax                       nvarchar(100) null,
		  reminderTypeId            int not null,
		  reminderType              nvarchar(100) null,
		  serverUrl                 nvarchar(100) null,
		  serverPort                nvarchar(100) null,
		  userName                  nvarchar(100) null,
		  [password]                nvarchar(100) null,
		  [subject]                 nvarchar(256) null,
		  body                      nvarchar(3000) null,
		  scheduleMessage           int not null,
		  scheduleMessage2          int not null,
		  enableSsl                 bit not null,
		  officeAppointmentDate          datetime null, -- for scheduleMessage 1 
--		  officeAppointmentDate2          datetime null -- for scheduleMessage 2
		  primary key (id)
	   );
	   
	   DECLARE @appointments TABLE
	   ( 
	     id                              int identity(1,1) not null,
	     appointmentId                   int not null,
		 officeId                        int not null,	
		 appointmentTime                 datetime null,	  
		 patientRecordId                 int not null,			 
		 demodgraphicId                  int not null,		
		 patientFirstName               nvarchar(200) null,
		 patientLastName                 nvarchar(200) null,
		 patientEmail                    nvarchar(200) null,
		 patientPhoneNumbers			 nvarchar(500) null,
		 practiceDoctorId                int not null,
		 practiceDoctorExternalDoctorId  int not null,
		 practiceDoctorFirstName         nvarchar(200) null,
		 practiceDoctorLastName          nvarchar(200) null,
		 appointmentTypeId				 int not null
		 ,primary key (id)
	   );

	  select @PracticeID = PracticeID from Office where id = @officeId;

	  IF @reminderType in ('emailreminder', 'textreminder')
	  begin
		   -- get the offices
		  INSERT INTO @reminderOffices
		  SELECT 
		  o.Id
		 ,o.PracticeId   
		 ,o.[name]            
		 ,o.businessName              
		 ,o.address1                  
		 ,o.address2                  
		 ,o.city                     
		 ,o.phone                     
		 ,o.fax                       
		 ,r.reminderTypeId   
		 ,@reminderType         
		 ,e.serverUrl                 
		 ,e.serverPort                
		 ,e.userName                  
		 ,e.[password]                
		 ,r.[subject]                 
		 ,r.body                      
		 ,r.scheduleMessage           
		 ,r.scheduleMessage2          
		 ,e.enableSsl                 
		 ,DATEADD(day,r.scheduleMessage,@currentDate) AS appointmentDate
		  FROM office o
		  JOIN OfficeEmails e ON o.Id = e.officeId
		  JOIN ReminderRules r ON o.Id = r.officeId
		  WHERE r.reminderTypeId = @reminderTypeId 
		  AND r.scheduleMessage > 0
		  UNION
		  SELECT 
		  o.Id
		 ,o.PracticeId   
		 ,o.[name]            
		 ,o.businessName              
		 ,o.address1                  
		 ,o.address2                  
		 ,o.city                     
		 ,o.phone                     
		 ,o.fax                       
		 ,r.reminderTypeId   
		 ,@reminderType         
		 ,e.serverUrl                 
		 ,e.serverPort                
		 ,e.userName                  
		 ,e.[password]                
		 ,r.[subject]                 
		 ,r.body                      
		 ,r.scheduleMessage           
		 ,r.scheduleMessage2          
		 ,e.enableSsl                 
		 ,DATEADD(day,r.scheduleMessage2,@currentDate) AS appointmentDate
		  FROM office o
		  JOIN OfficeEmails e ON o.Id = e.officeId
		  JOIN ReminderRules r ON o.Id = r.officeId
		  WHERE r.reminderTypeId = @reminderTypeId 
		  AND   r.scheduleMessage2 > 0 
		  AND r.scheduleMessage != r.scheduleMessage2
	  END;
	  ELSE
	  BEGIN
	      -- IF VoiceRemider, adds filter per officeID
		   -- get the offices
		  INSERT INTO @reminderOffices
		  SELECT 
		  o.Id
		 ,o.PracticeId   
		 ,o.[name]            
		 ,o.businessName              
		 ,o.address1                  
		 ,o.address2                  
		 ,o.city                     
		 ,o.phone                     
		 ,o.fax                       
		 ,r.reminderTypeId   
		 ,@reminderType         
		 ,e.serverUrl                 
		 ,e.serverPort                
		 ,e.userName                  
		 ,e.[password]                
		 ,r.[subject]                 
		 ,r.body                      
		 ,r.scheduleMessage           
		 ,r.scheduleMessage2          
		 ,e.enableSsl                 
		 ,DATEADD(day,r.scheduleMessage,@currentDate) AS appointmentDate
		  FROM office o
		  JOIN OfficeEmails e ON o.Id = e.officeId
		  JOIN ReminderRules r ON o.Id = r.officeId
		  WHERE r.reminderTypeId = @reminderTypeId 
		  AND r.scheduleMessage > 0
		  AND  o.id = @officeID
		  UNION
		  SELECT 
		  o.Id
		 ,o.PracticeId   
		 ,o.[name]            
		 ,o.businessName              
		 ,o.address1                  
		 ,o.address2                  
		 ,o.city                     
		 ,o.phone                     
		 ,o.fax                       
		 ,r.reminderTypeId   
		 ,@reminderType         
		 ,e.serverUrl                 
		 ,e.serverPort                
		 ,e.userName                  
		 ,e.[password]                
		 ,r.[subject]                 
		 ,r.body                      
		 ,r.scheduleMessage           
		 ,r.scheduleMessage2          
		 ,e.enableSsl                 
		 ,DATEADD(day,r.scheduleMessage2,@currentDate) AS appointmentDate
		  FROM office o
		  JOIN OfficeEmails e ON o.Id = e.officeId
		  JOIN ReminderRules r ON o.Id = r.officeId
		  WHERE r.reminderTypeId = @reminderTypeId 
		  AND   r.scheduleMessage2 > 0 
		  AND r.scheduleMessage != r.scheduleMessage2
		  AND  o.id = @officeID;
	  END;

      IF @reminderType = 'emailreminder'
      begin
	      -- get the appointments 
	      INSERT INTO @appointments
	      SELECT 
	      a.Id 
	      ,a.officeId
	      ,a.appointmentTime
	      ,a.PatientRecordId
	      ,d.Id
	      ,d.firstName
	      ,d.lastName
	      ,d.email
	      ,(SELECT STUFF(  
		          (
			        SELECT ', '+ CAST(dp.phoneNumber AS VARCHAR(500)) + ' '+ 
							    (CASE dp.typeOfPhoneNumber
								    WHEN 0 THEN 'H'
								    WHEN 1 THEN 'C'
								    WHEN 2 THEN 'W'
								    ELSE '' END)
				    FROM DemographicsPhoneNumbers dp
			       WHERE dp.phoneNumber IS NOT NULL AND ltrim(rtrim(dp.phoneNumber)) <> '' 
			       AND   dp.DemographicId = d.Id AND dp.IsRemoved = 0 
		       ORDER BY  dp.DemographicId, dp.typeOfPhoneNumber, dp.isActive DESC, dp.Id DESC
		       FOR XMl PATH('') 
                  ),1,1,''  )) as patientPhoneNumbers
	      ,a.PracticeDoctorId
	      ,pd.ExternalDoctorId
	      ,ed.firstName AS DocFirstName 
	      ,ed.lastName AS DocLastName 
		  , a.AppointmentTypeId
	      FROM Appointments a
          JOIN @reminderOffices ro ON (ro.officeId = a.OfficeId )
	      JOIN PracticeDoctors pd ON a.PracticeDoctorId = pd.Id
	      JOIN ExternalDoctors ed ON pd.ExternalDoctorId = ed.Id
	      JOIN Demographics d ON d.PatientRecordId = a.PatientRecordId
	      WHERE a.IsActive = @isActive
	      AND a.appointmentConfirmation = @toBeConfirmed
	      AND a.appointmentStatus != @cancelledStatusId 
	      AND a.appointmentStatus != @cancellListStatusId
	      AND a.appointmentStatus != @triageStatusId
	      AND a.appointmentStatus!= @waitListStatusId
          AND a.AppointmentTypeId not in ( select id from AppointmentTypes where AppointmentTypeId = 4 ) --Excludes No Visit Appointments
	  	  AND a.appointmentTime between cast(CONVERT(date, ro.officeAppointmentDate) as datetime) and dateadd(ss, -1, dateadd(dd, 1, cast(CONVERT(date, ro.officeAppointmentDate) as datetime)))
	      AND d.email IS NOT NULL 
          AND d.consentEmail = 1
      end;
      else IF @reminderType = 'voicereminder'
      begin
            SET @typeOfPhoneNumber = 0; -- Home phone 
            SET @typeOfPhoneNumberCell = 1; -- Cell phone

			-- get the appointments 
			INSERT INTO @appointments
			SELECT 
			a.Id as AppId
			,a.officeId
			,a.appointmentTime
			,a.PatientRecordId
			,d.Id
			,d.firstName
			,d.lastName
			,d.email
			,(SELECT TOP 1 CAST(dp.phoneNumber AS VARCHAR(500)) + ' '+ 
					(CASE dp.typeOfPhoneNumber
						WHEN 0 THEN 'H'
						WHEN 1 THEN 'C'
						WHEN 2 THEN 'W'
						ELSE '' END)
				FROM DemographicsPhoneNumbers dp
				WHERE dp.phoneNumber IS NOT NULL AND ltrim(rtrim(dp.phoneNumber)) <> '' 
				AND   dp.DemographicId = d.Id AND dp.IsRemoved = 0 
				AND   ( dp.typeOfPhoneNumber = @typeOfPhoneNumber
				OR     dp.typeOfPhoneNumber = @typeOfPhoneNumberCell ) 
			ORDER BY  dp.DemographicId, dp.typeOfPhoneNumber, dp.isActive DESC, dp.Id DESC ) as patientPhoneNumbers
			,a.PracticeDoctorId
			,pd.ExternalDoctorId
			,ed.firstName AS DocFirstName 
			,ed.lastName AS DocLastName 
			, a.AppointmentTypeId
			FROM Appointments a
			JOIN @reminderOffices ro ON (ro.officeId = a.OfficeId )
			JOIN PracticeDoctors pd ON a.PracticeDoctorId = pd.Id
			JOIN ExternalDoctors ed ON pd.ExternalDoctorId = ed.Id
			JOIN Demographics d ON d.PatientRecordId = a.PatientRecordId
			WHERE a.IsActive = @isActive
			AND a.appointmentConfirmation = @toBeConfirmed
			AND a.appointmentStatus != @cancelledStatusId 
			AND a.appointmentStatus != @cancellListStatusId
			AND a.appointmentStatus != @triageStatusId
			AND a.appointmentStatus!= @waitListStatusId
			AND a.AppointmentTypeId not in ( select id from AppointmentTypes where AppointmentTypeId = 4 ) --Excludes No Visit Appointments
			AND a.appointmentTime between cast(CONVERT(date, ro.officeAppointmentDate) as datetime) and dateadd(ss, -1, dateadd(dd, 1, cast(CONVERT(date, ro.officeAppointmentDate) as datetime))) 
			AND a.officeID = @officeId
			AND ro.PracticeID = @PracticeID
			AND exists ( SELECT 1 FROM DemographicsPhoneNumbers dp
						WHERE dp.phoneNumber IS NOT NULL AND ltrim(rtrim(dp.phoneNumber)) <> '' 
						AND   dp.DemographicId = d.Id AND dp.IsRemoved = 0 
						AND   ( dp.typeOfPhoneNumber = @typeOfPhoneNumber
						OR      dp.typeOfPhoneNumber = @typeOfPhoneNumberCell ) )

        end
        else if @reminderType = 'textreminder' 
		begin
		  
			SET @typeOfPhoneNumber = 1;

			-- get the appointments 
			INSERT INTO @appointments
			SELECT 
			a.Id 
			,a.officeId
			,a.appointmentTime
			,a.PatientRecordId
			,d.Id
			,d.firstName
			,d.lastName
			,d.email
			,(SELECT TOP 1 CAST(dp.phoneNumber AS VARCHAR(500)) + ' '+ 
					(CASE dp.typeOfPhoneNumber
						WHEN 0 THEN 'H'
						WHEN 1 THEN 'C'
						WHEN 2 THEN 'W'
						ELSE '' END)
				FROM DemographicsPhoneNumbers dp
				WHERE dp.phoneNumber IS NOT NULL AND ltrim(rtrim(dp.phoneNumber)) <> '' 
				AND   dp.DemographicId = d.Id AND dp.IsRemoved = 0 
				AND   ( dp.typeOfPhoneNumber = @typeOfPhoneNumber
				OR     dp.typeOfPhoneNumber = @typeOfPhoneNumberCell )
			ORDER BY  dp.DemographicId, dp.typeOfPhoneNumber, dp.isActive DESC, dp.Id DESC ) as patientPhoneNumbers
			,a.PracticeDoctorId
			,pd.ExternalDoctorId
			,ed.firstName AS DocFirstName 
			,ed.lastName AS DocLastName 
			, a.AppointmentTypeId
			FROM Appointments a
			JOIN @reminderOffices ro ON (ro.officeId = a.OfficeId)
			JOIN PracticeDoctors pd ON a.PracticeDoctorId = pd.Id
			JOIN ExternalDoctors ed ON pd.ExternalDoctorId = ed.Id
			JOIN Demographics d ON d.PatientRecordId = a.PatientRecordId
			WHERE a.IsActive = @isActive
			AND a.appointmentConfirmation = @toBeConfirmed
			AND a.appointmentStatus != @cancelledStatusId 
			AND a.appointmentStatus != @cancellListStatusId
			AND a.appointmentStatus != @triageStatusId
			AND a.appointmentStatus!= @waitListStatusId
			AND a.AppointmentTypeId not in ( select id from AppointmentTypes where AppointmentTypeId = 4 ) --Excludes No Visit Appointments
			AND a.appointmentTime between cast(CONVERT(date, ro.officeAppointmentDate) as datetime) and dateadd(ss, -1, dateadd(dd, 1, cast(CONVERT(date, ro.officeAppointmentDate) as datetime)))
			AND exists ( SELECT 1 FROM DemographicsPhoneNumbers dp
						WHERE dp.phoneNumber IS NOT NULL AND ltrim(rtrim(dp.phoneNumber)) <> '' 
						AND   dp.DemographicId = d.Id AND dp.IsRemoved = 0 
						AND   ( dp.typeOfPhoneNumber = @typeOfPhoneNumber
						OR      dp.typeOfPhoneNumber = @typeOfPhoneNumberCell ) );
      end;
 
	  select 

	  DISTINCT  
	  ro.address1,
	  ro.address2,
	  ro.body,
	  ro.city,
	  ro.enableSsl,
	  ro.officeBusinessName,
	  ro.officeId,
	  ro.officeName,
	  ro.password,
	  ro.phone,
	  ro.practiceId,
	  ro.reminderType,
	  ro.scheduleMessage,
	  ro.scheduleMessage2,
	  ro.serverPort,
	  ro.serverUrl,
	  ro.subject,
	  ro.userName,
	  apps.appointmentTime 
	  ,apps.appointmentId
	  ,apps.patientRecordId
	  ,apps.demodgraphicId
	  ,apps.patientFirstName
	  ,apps.patientLastName
	  ,apps.patientEmail  
	  ,apps.patientPhoneNumbers
	  ,apps.practiceDoctorId
	  ,apps.practiceDoctorExternalDoctorId
	  ,apps.practiceDoctorFirstName
	  ,apps.practiceDoctorLastName
	  ,appTests.Id as AppointmentTestId
	  ,appTests.startTime AS testStartTime
	  ,appTests.TestId
	  ,t.testShortName
	  ,t.testFullName
	  ,pt.TestInstruction
	  ,aptt.name as appointmentTypeName -- for 13492
	  FROM @reminderOffices ro
	  JOIN @appointments apps ON ro.officeId = apps.officeId
	  JOIN AppointmentTests appTests ON appTests.AppointmentId = apps.appointmentId
	  JOIN AppointmentTypes aptt ON aptt.Id = apps.AppointmentTypeId
	  JOIN PracticeTests pt ON pt.TestId = appTests.TestId
	  JOIN Tests t ON t.Id = appTests.TestId
	  WHERE appTests.IsActive = @isActive
	  AND pt.isActive = @isActive
	  AND pt.PracticeId = ro.practiceId
	  order by apps.appointmentTime

END