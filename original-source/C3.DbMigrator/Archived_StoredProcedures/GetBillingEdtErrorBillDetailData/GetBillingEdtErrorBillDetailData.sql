﻿CREATE PROCEDURE [dbo].[GetBillingEdtErrorBillDetailData] 
	@practiceId int,
	@claimNumbers nvarchar(max)
AS
BEGIN
	SET NOCOUNT ON;

	if (@claimNumbers = '')
		return;

	declare @claims table( ClaimId int not null, primary key(ClaimId) );

	insert into @claims(ClaimId) SELECT value FROM STRING_SPLIT(@claimNumbers, ',') WHERE NOT EXISTS ( SELECT 1 from @claims where ClaimId = value);

	select a.healthCardNumber,a.firstName,a.lastName,a.PatientRecordId patientRecordId,a.appointmentId,a.hdAdmissionActionId admissionActionId
	  from BillDetails a inner join PracticeDoctors b on a.PracticeDoctorId=b.Id
	 where b.PracticeId=@practiceId and a.appointmentId in (SELECT ClaimId FROM @claims)
    union
    select a.healthCardNumber,a.firstName,a.lastName,a.PatientRecordId patientRecordId,a.appointmentId,a.hdAdmissionActionId admissionActionId
	  from BillDetails a inner join PracticeDoctors b on a.PracticeDoctorId=b.Id
	 where b.PracticeId=@practiceId and a.hdAdmissionActionId in (SELECT ClaimId FROM @claims)
	option (recompile)
END