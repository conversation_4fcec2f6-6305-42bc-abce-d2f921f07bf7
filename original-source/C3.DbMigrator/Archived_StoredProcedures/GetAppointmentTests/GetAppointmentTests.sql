﻿CREATE PROCEDURE GetAppointmentTests
	@appointmentId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @isActive bit = 1;
    
	SELECT apptest.[Id]
			,apptest.[TestId]
			,apptest.[startTime]
			,apptest.[AppointmentTestStatusId]
			,apptest.[testDuration]
			,apptest.[billStatusId]
			,apptest.[referralDoctorId]
			,apptest.[AppointmentId]
			,apptest.[AccessionNumber]
			,apptest.[PhysicianComments]
			,apptest.[TechnicianComments]
			,apptest.[IsActive]
			,apptest.[DateCreated]
			,apptest.[DateUpdated]
			,apptest.[SetForReview]
			,apptest.[ReassignDocID]
			,apptest.[ReassignDate]	  
			,ISNULL(res.assignedToUserId,0) AS assignedToUserId
			,ISNULL(res.performedByUserId,0) AS performedByUserId
			,res.permissionId
			,res.isDoctorRequiredInOffice			
			,ISNULL(u.FirstName,'') + ' '+ ISNULL(u.LastName,'') AS ResourceFullName
			,test.testFullName
			,test.testShortName
			,test.RequireDevice
			,test.IsRadiology
			,teststatus.Color AS TestStatusColor
			,teststatus.Status AS TestStatus	  
		FROM [dbo].[AppointmentTests] apptest
		JOIN AppointmentTestStatus teststatus ON apptest.AppointmentTestStatusId = teststatus.Id
		JOIN Tests test ON apptest.TestId = test.Id
		JOIN AppointmentTestResources res ON apptest.Id = res.AppointmentTestId 
		LEFT JOIN AspNetUsers u ON res.assignedToUserId = u.UserID
		WHERE apptest.AppointmentId = @appointmentId
		AND apptest.IsActive = @isActive 
		AND res.isActive = @isActive
END
