﻿CREATE PROCEDURE [dbo].[GetDoctorInfo] 
	@UserId INT
AS
BEGIN

	SET NOCOUNT ON;

	select  pDoc.Id as PracticeDoctorId, 
			pDoc.ExternalDoctorId as ExternalDoctorId, 
			exDoc.firstName as FirstName, 
			exDoc.lastName as LastName,
			exDoc.lastName + ' ' + exDoc.firstName as FullName
	from PracticeDoctors pDoc
	INNER JOIN ExternalDoctors exDoc on pDoc.ExternalDoctorId = exDoc.Id
	INNER JOIN AspNetUsers apu ON apu.id = pDoc.ApplicationUserId
	where userId = @userId 

END