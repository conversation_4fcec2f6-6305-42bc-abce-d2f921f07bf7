﻿CREATE PROCEDURE [dbo].[GetFormAppointmentInfo]
    @PracticeID int,
	@AppointmentId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @OHIPID nvarchar(20);
	DECLAR<PERSON> @ProvinceOHIP nvarchar(2);
    <PERSON><PERSON><PERSON><PERSON> @OHIPVersionCode nvarchar(20);
	<PERSON><PERSON>AR<PERSON> @addressLine1 nvarchar(200);
	DECLARE @addressLine2 nvarchar(200);
	DECLARE @city nvarchar(100);
	DECLARE @postalCode nvarchar(10);
	DECLARE @province nvarchar(100);
	DECLARE @country nvarchar(100);

	DECLARE @PatientInfo TABLE
	   ( DemographicId               int not null,
	     PatientRecordID             int not null,
         LastName                    nvarchar(100) null,
		 MiddleName                  nvarchar(100) null,
		 FirstName                   nvarchar(100) null,
         dateOfBirth                 date null,
		 gender                      int null,
         email                       nvarchar(50) null,      
		 PracticeDoctorID            int null,
		 ExternalDocIDforPracticeDoc int null,
		 CPSO                        nvarchar(50) null,  
		 OHIPPhysicianId             nvarchar(50) null,  
		 FamilyDoctorID              int null,
		 ReferralDoctorID            int null,
         PatientPhoneNumbers         nvarchar(500) null,
         OfficeAddress               nvarchar(500) null,
         OfficePhoneNumber           nvarchar(20) null,
         OfficeFaxNumber             nvarchar(20) null,
         AppointmentTime             datetime null
	   );
	
    -- Insert statements for procedure here
	INSERT INTO @PatientInfo(DemographicId
	                        ,PatientRecordID
							,LastName
							,MiddleName
							,FirstName
							,dateOfBirth
							,gender
							,email
							,PracticeDoctorID
							,ExternalDocIDforPracticeDoc
							,CPSO
							,OHIPPhysicianId
							,FamilyDoctorID
							,ReferralDoctorID
							,PatientPhoneNumbers
                            ,OfficeAddress
                            ,OfficePhoneNumber
                            ,OfficeFaxNumber
                            ,AppointmentTime)
					SELECT TOP 1 d.Id AS DemographicId
					        ,d.PatientRecordID
							,isnull(d.aliasLastName,d.lastName) as LastName
							,isnull(d.aliasMiddleName, d.middleName) as MiddleName
							,isnull(d.aliasFirstName, d.firstName) as FirstName
							,d.dateOfBirth
							,d.gender
							,d.email
							,a.PracticeDoctorId
							,Doctor.ExternalDoctorId as ExternalDocIDforPracticeDoc
							,Doctor.CPSO
							,Doctor.OHIPPhysicianId
							,(SELECT TOP 1 fd.ExternalDoctorId
								FROM DemographicsFamilyDoctors fd
								WHERE fd.DemographicId = d.Id
								AND   fd.IsActive = 1
                                AND   fd.IsRemoved = 0 -- Ticket #6190
								ORDER BY fd.Id DESC) AS FamilyDoctorID
							,a.referralDoctorId
							,(SELECT STUFF(  
									(
									SELECT ', '+ CAST(pn.phoneNumber AS VARCHAR(500)) + ' '+ 
												(CASE pn.typeOfPhoneNumber
													WHEN 0 THEN 'H'
													WHEN 1 THEN 'C'
													WHEN 2 THEN 'W'
													ELSE '' END)
									FROM DemographicsPhoneNumbers pn
									WHERE phoneNumber IS NOT NULL AND ltrim(rtrim(phoneNumber)) <> '' and isRemoved = 0
									AND   pn.DemographicId = d.Id
								ORDER BY  DemographicId, typeOfPhoneNumber, isActive DESC, Id DESC
								FOR XMl PATH('') 
									),1,1,''  )) as PatientPhoneNumbers
                            ,CONCAT(o.address1, o.address2, ', ', o.city,', ', 
                                       CASE WHEN o.province = 0 then 'AB'
	                                        WHEN o.province = 1 then 'BC'
	                                        WHEN o.province = 2 then 'MB'
	                                        WHEN o.province = 3 then 'NB'
	                                        WHEN o.province = 4 then 'NL'
	                                        WHEN o.province = 5 then 'NS'
	                                        WHEN o.province = 6 then 'NT'
	                                        WHEN o.province = 7 then 'NU'
	                                        WHEN o.province = 8 then 'ON'
	                                        WHEN o.province = 9 then 'PE'
	                                        WHEN o.province = 10 then 'QC'
	                                        WHEN o.province = 11 then 'SK'
	                                        WHEN o.province = 12 then 'YT'
	                                   END,' ', o.zip) as OfficeAddress
                            ,o.phone as OfficePhoneNumber
                            ,o.fax as OfficeFaxNumber
                            ,a.appointmentTime
					FROM PatientRecords pr
					JOIN Demographics d ON (pr.Id = d.PatientRecordId)
					JOIN Appointments a ON (a.PatientRecordId = d.PatientRecordId)
					JOIN (SELECT pcd.Id AS PracticeDoctorId, ed.CPSO,ed.OHIPPhysicianId,ed.Id AS ExternalDoctorId FROM PracticeDoctors pcd
							JOIN ExternalDoctors ed on pcd.ExternalDoctorId = ed.Id
						 ) AS Doctor ON a.PracticeDoctorId = Doctor.PracticeDoctorId
                    JOIN Office o ON (a.OfficeId = o.id)
					WHERE pr.PracticeId = @PracticeID
					AND   a.id = @AppointmentId
					AND   d.active = 0;
	
    SELECT TOP 1 @OHIPID = isnull(hc.[number],''),
		   @ProvinceOHIP = case when hc.provinceCode = 0 then 'AB'
							when hc.provinceCode = 9 then 'PE'
							when hc.provinceCode = 4 then 'NF'
							when hc.provinceCode = 1 then 'BC'
							when hc.provinceCode = 8 then 'ON'
							when hc.provinceCode = 2 then 'MB'
							when hc.provinceCode = 5 then 'NS'
							else 'ON' end,
           @OHIPVersionCode = ISNULL(hc.[version],'')
	FROM DemographicsHealthCards hc JOIN Demographics d ON (hc.DemographicId = d.id)
    WHERE d.patientrecordid = (select PatientRecordID from @PatientInfo)
    ORDER BY hc.id DESC;

    SELECT TOP 1 @addressLine1 = da.addressLine1,
	       @addressLine2 = da.addressLine2,
		   @city = da.city,
		   @postalcode = da.postalCode,
  		   @province = da.province,
		   @country = da.country
	FROM DemographicsAddresses da JOIN Demographics d ON (da.DemographicId = d.id)
    WHERE d.patientrecordid = (select PatientRecordID from @PatientInfo)
	AND da.IsActive = 1
    ORDER BY da.id DESC;

	SELECT DemographicId
			,LastName
			,MiddleName
			,FirstName
			,dateOfBirth
			,gender
			,email
			,@OHIPID as OHIPID
            ,@OHIPVersionCode as OHIPVersionCode
			,@ProvinceOHIP as ProvinceOHIP
			,@addressLine1 as AddressLine1
			,@addressLine2 as AddressLine2
			,@city as City
			,@postalCode as PostalCode
			,@province as Province
			,@country as Country
			,PracticeDoctorID
			,ExternalDocIDforPracticeDoc
			,CPSO
			,OHIPPhysicianId
			,FamilyDoctorID
			,ReferralDoctorID
			,PatientPhoneNumbers
            ,OfficeAddress
            ,OfficePhoneNumber
            ,OfficeFaxNumber
            ,AppointmentTime
	  FROM @PatientInfo
END
