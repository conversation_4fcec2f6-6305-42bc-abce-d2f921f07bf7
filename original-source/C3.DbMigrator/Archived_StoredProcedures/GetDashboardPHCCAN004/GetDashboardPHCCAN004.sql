﻿CREATE PROCEDURE [dbo].[GetDashboardPHCCAN004] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @PAPExclusion AS TABLE (PatientRecordId INT)
	DECLARE @PAPScreening AS TABLE (PatientRecordId INT)
	DECLARE @PAPScreeningCurrent AS TABLE (PatientRecordId INT, AgeMonths INT)
	DECLARE @PAPScreeningDeclined AS TABLE (PatientRecordId INT, AgeMonths INT)
	DECLARE @PAPScreeningPending AS TABLE (PatientRecordId INT, AgeMonths INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId  and MRP.IsActive = 1
	WHERE 
	D.active = 0														-- Pastient is active	
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) BETWEEN 21 AND 69	-- Patient is between 21 and 69
	AND D.gender = 1		
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)-- Patient is Female
	GROUP BY D.PatientRecordId

	-- PAP Exclusion 
	INSERT INTO @PAPExclusion
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q140A'
	UNION ALL 
	SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('V45.77','68.3','68.4','68.5','68.6','68.7','68.8','68.9')						-- Excluded Diagnosis codes
		OR CPP.Problem_Description LIKE '%hysterectomy%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%hysterosal%'
		OR CPP.Problem_Description LIKE '%Cervical Ca%'
		OR CPP.Problem_Description LIKE '%TVHt%'
		OR CPP.Problem_Description LIKE '%TABH%'
		OR CPP.Problem_Description LIKE '%TAH%'
		)
		AND Deleted = 0 and cpp.UpdateDate is null

	-- select * from @PAPExclusion where PatientRecordId in (select PatientRecordId from @BasePopulation)

	--- PAP Screening 
	INSERT INTO @PAPScreeningCurrent
	SELECT IM.PatientRecordId,DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) AS AgeMonths	
	FROM @BasePopulation bp
	join [VP_CPP_Immunization] IM on BP.PatientRecordId = im.PatientRecordId
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId=2							-- Type = Pap
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) < 39  -- documente PAP within 39 months
																												      -- used for indicator 1 and 2
	 
	 --select * from @PAPScreening where PatientRecordId in (select PatientRecordId from @BasePopulation) 



	--- PAP Screening Declined
	INSERT INTO @PAPScreeningDeclined
	SELECT IM.PatientRecordId,DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) AS AgeMonths	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=2							-- Type = Pap
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) <= 36
	
  -- select * from @PAPScreeningDeclined where PatientRecordId in (select PatientRecordId from @BasePopulation)

	--- PAP Screening Pending
	INSERT INTO @PAPScreeningPending
	SELECT bp.PatientRecordId,DATEDIFF(MONTH,req.testTime,GETDATE()) AS AgeMonths	
	FROM @BasePopulation bp
	join RequisitionPatient rp on bp.PatientRecordId = rp.PatientRecordId
	join Requisition req on rp.id = req.requisitionPatientId
	where req.isActive = 1
	and req.requisitionStatus = 2
	and req.requisitionItems like '%"text":"PAP","value":"other5"%'
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)											-- not excluded
		AND PatientRecordId not IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths <= 36)				-- not current
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths BETWEEN 37 AND 39)	    -- over due by 1- 3 months 
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningPending WHERE AgeMonths <= 36)				-- not pending for 3 years
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningDeclined WHERE AgeMonths <= 36)				-- not declined in 3 years

	INSERT INTO @Segments
	SELECT 2,PatientRecordId	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths <= 39)				-- not documented in last 27 months
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningDeclined WHERE AgeMonths <= 36)				-- not declined in 3 years
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningPending WHERE AgeMonths <= 36)				-- not pending in 3 years

	INSERT INTO @Segments
	SELECT 3,PatientRecordId
	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths <= 24)						-- not documented in two years
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PAPScreeningPending WHERE AgeMonths < 2)						-- pending for 1 months

		INSERT INTO @Segments
	SELECT 4,PatientRecordId
	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths <= 24)						-- not documented in two years
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PAPScreeningPending WHERE AgeMonths between 2 and 36)		-- pending 

		INSERT INTO @Segments
	SELECT 5,PatientRecordId	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths <= 36)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningPending WHERE AgeMonths <= 36)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PAPScreeningDeclined WHERE AgeMonths <= 36)

		INSERT INTO @Segments
	SELECT 6,PatientRecordId
	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths <= 32)				-- not current
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths BETWEEN 33 AND 36)		-- will be over due in 1-3 months

		INSERT INTO @Segments
	SELECT 7,PatientRecordId
	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths <= 28)				-- not current in 28 months
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PAPScreeningCurrent WHERE AgeMonths BETWEEN 29 AND 32)		-- will be over due in 4-6 months

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
