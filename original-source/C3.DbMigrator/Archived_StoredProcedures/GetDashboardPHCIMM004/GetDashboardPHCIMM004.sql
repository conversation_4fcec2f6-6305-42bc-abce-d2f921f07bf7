﻿CREATE PROCEDURE [dbo].[GetDashboardPHCIMM004] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @PneumococcalDocumented AS TABLE (PatientRecordId INT)
	DECLARE @PneumococcalRefused AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation	
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	AND (DATEDIFF(DD,D.dateOfBirth,getdate()) / 365.5) >= 65 -- Active patients, 65 or older
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId


	-- Pneumococcal documented
	INSERT INTO @PneumococcalDocumented
	SELECT IM.PatientRecordId	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId=13							-- Type = Pneumococcal
	AND DATEDIFF(MONTH,DATEFROMPARTS ( im.ImmunizationYear, im.ImmunizationMonth, im.ImmunizationDay ) ,GETDATE()) <= 12

	-- Pneumococcal refused
	INSERT INTO @PneumococcalRefused
	SELECT IM.PatientRecordId	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=13							-- Type = Pneumococcal	
	AND DATEDIFF(MONTH,DATEFROMPARTS ( im.ImmunizationYear, im.ImmunizationMonth, im.ImmunizationDay ) ,GETDATE()) <= 12
	
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @PneumococcalDocumented)				

		
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PneumococcalDocumented)	
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PneumococcalRefused)
	
		
	INSERT INTO @Segments
	SELECT 3,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PneumococcalDocumented)	
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PneumococcalRefused)
		
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
