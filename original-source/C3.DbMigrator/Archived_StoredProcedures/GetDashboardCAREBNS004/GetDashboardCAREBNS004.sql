﻿CREATE PROCEDURE [dbo].[GetDashboardCAREBNS004] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON
	
	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @CurrentDate AS DATE,@FYDate AS DATE
	SELECT @CurrentDate = GETDATE()
	SELECT @FYDate = STR(IIF(MONTH(GETDATE())<4,YEAR(GETDATE()),YEAR(GETDATE()+1))) + '-03-31' -- FiscalYear CutOffDate

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @Billed AS TABLE (PatientRecordId INT)
	DECLARE @DTaPIPVHibDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @PneumococcalDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @MeningococcalDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @MMRDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @VaricellaDocumented AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @CompleteImmunePackage AS TABLE (PatientRecordId INT,ImmCount INT)
	DECLARE @Complete AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId  and MRP.IsActive = 1
	JOIN DemographicsEnrollments ROSTER ON MRP.id = ROSTER.DemographicsMRPId -- this checks for roster status, active if no termination date
	WHERE 
	D.active = 0	
	AND MRP.IsActive = 1 -- Rostered ( Source: sp_GetRecallList)
	AND (DATEDIFF(MONTH,D.dateOfBirth,@FYDate)) BETWEEN 30 AND 42
	AND (MRP.PracticeDoctorId = @PracticeDoctorId)-- OR @PracticeDoctorId IS NULL)
	AND ROSTER.enrollmentTerminationDate IS NULL -- Roster not terminated
	GROUP BY D.PatientRecordId
	
	--- Tracking Code Q132A billed for patient 
	INSERT INTO @Billed
	SELECT PatientRecordId 	
	FROM BillDetails B
	WHERE B.serviceCode = 'Q132A'	
	
	--- DTaP-IPV-Hib documented 
	INSERT INTO @DTaPIPVHibDocumented
	SELECT PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3 -- Status = Complete
	AND (IM.VP_CPP_ImmunizationTypeId = 5) -- DTaP-IPV-Hib
	GROUP BY PatientRecordId

	--- Pneumococcal documented
	INSERT INTO @PneumococcalDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3 -- Status = Complete
	AND (IM.VP_CPP_ImmunizationTypeId = 13) -- Pneumococcal
	GROUP BY PatientRecordId

	--- Meningococcal documented
	INSERT INTO @MeningococcalDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3 -- Status = Complete
	AND (IM.VP_CPP_ImmunizationTypeId = 11) -- Meningococcal
	GROUP BY PatientRecordId

	--  MMR documented 			
	INSERT INTO @MMRDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3 -- Status = Completed
	AND IM.VP_CPP_ImmunizationTypeId=3 -- Type = MMR
	GROUP BY IM.PatientRecordId

	--- Varicella documented
	INSERT INTO @VaricellaDocumented
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3 -- Status = Complete
	AND (IM.VP_CPP_ImmunizationTypeId = 9) -- Varicella
	GROUP BY PatientRecordId
	
	--- Complete Immune Package documented
	INSERT INTO @CompleteImmunePackage
	SELECT IM.PatientRecordId,COUNT(IM.Id) AS ImmCount	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3 -- Status = Complete
	AND (IM.VP_CPP_ImmunizationTypeId = 17) -- Complete Immune Package
	GROUP BY PatientRecordId

	INSERT INTO @Complete
	SELECT PatientRecordId	
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @Billed)
	OR 
	(PatientRecordId IN	 (SELECT PatientRecordId FROM @DTaPIPVHibDocumented WHERE ImmCount >= 4)
	and PatientRecordId IN (SELECT PatientRecordId FROM @PneumococcalDocumented WHERE ImmCount >= 3)
	and PatientRecordId IN (SELECT PatientRecordId FROM @MeningococcalDocumented WHERE ImmCount >= 1)
	and PatientRecordId IN (SELECT PatientRecordId FROM @MMRDocumented WHERE ImmCount >= 1)
	and PatientRecordId IN (SELECT PatientRecordId FROM @VaricellaDocumented WHERE ImmCount >= 1)
	)
	OR PatientRecordId IN	(SELECT PatientRecordId FROM @CompleteImmunePackage)

	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId from @Complete
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
	 FROM @BasePopulation
	WHERE PatientRecordId NOT IN ( SELECT PatientRecordId FROM @Complete)

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
