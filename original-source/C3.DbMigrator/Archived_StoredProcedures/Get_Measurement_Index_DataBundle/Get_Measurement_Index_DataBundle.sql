﻿-- =============================================
-- Author:		<PERSON> Lau
-- Create date: 2021-01-21
-- Description: 
--	returning multiple result sets for MeasurementController.GenerateWorkSheet
--  all SELECT statements in this SP must be returned, and must be in correct order, so do not mix them up
-- =============================================
CREATE   PROCEDURE Get_Measurement_Index_DataBundle
	@appointmentId int,
	@testId int
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @practiceDoctorId int;
	DECLARE @groupId int;

	-- 0. GetAppointment
	SELECT TOP 1 @practiceDoctorId = PracticeDoctorId FROM Appointments WHERE id = @appointmentId;
	SELECT TOP 1 * FROM Appointments WHERE id = @appointmentId;

	-- 1. GetTestGroupByTest
	SELECT TOP 1 @groupId=GroupId FROM TestGroups WHERE TestId = @testId;
	SELECT TOP 1 * FROM TestGroups WHERE TestId = @testId;

	-- 2. GetTest
	SELECT TOP 1 * FROM Tests WHERE Id = @testId;

	-- 3. GetPracticeDoctor
	SELECT TOP 1 p.* FROM ExternalDoctors e
    join PracticeDoctors p on e.Id = p.ExternalDoctorId
    WHERE p.Id = @practiceDoctorId;

	-- 4. GetMainDoctor
	SELECT TOP 1 e.* FROM ExternalDoctors e
    join PracticeDoctors p on e.Id = p.ExternalDoctorId
    WHERE p.Id = @practiceDoctorId;

	-- 5. For GetReportTemplate(int apptID), note: ReportTemplate already returned in ExternalDoctors above
	SELECT TOP 1 * FROM AppointmentProviders WHERE AppointmentId = @appointmentId;

	-- 6. HasRawData->GetRawFiles
	DECLARE @hasRawData bit;
	SET @hasRawData = 0;
	IF EXISTS (SELECT TOP 1 * FROM WS_SendReport WHERE AppointmentId=@appointmentId AND TestId=@testId AND Active=1 AND SendTypeId=5) 
		SET @hasRawData = 1;

	SELECT @hasRawData;

	-- 7. GetCategoriesAndMeasurements
	SELECT DISTINCT c.* FROM MeasurementCategories c
    JOIN Measurements m on c.Id = m.MeasurementCategoryID
    JOIN CategoryByGroups g on c.Id = g.CatgeoryID
    WHERE g.Visible = 1 AND g.GroupID = @groupId
    ORDER BY c.[order];

	-- 8. GetAllMeasurementOperators
	SELECT * FROM MeasurementOperators;

	-- 9. GetMeasurementRangeTypes
	SELECT * FROM MeasurementRangeTypes;

	-- 10. GetReportPhrasesByGroup
	SELECT * FROM ReportPhrases 
    WHERE parent = -1 AND status = 0 AND GroupID = @groupId
    ORDER BY ordernumber;
END
