﻿CREATE PROCEDURE [dbo].[GetReportVitals]
	@appointmentTestId INT,
	@appointmentTestLogId INT = 0	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @practiceId INT;
	DECLARE @officeId INT;	
	DECLARE @appointmentId INT;
	DECLARE @patientId INT;	
	DECLARE @testId INT;		
	DECLARE @practiceDoctorId INT;
	DECLARE @externalDoctorId INT; 
	DECLARE @customType INT = 0; -- vitals
	DECLARE @visibleOnly BIT = 1; -- only visible vitals categories
	
	
	SELECT TOP 1 
	@practiceId = o.PracticeId,
	@officeId = o.Id,	
	@appointmentId = appTest.appointmentId,	
	@patientId = app.PatientRecordId,	
	@practiceDoctorId = app.PracticeDoctorId,
	@testId = appTest.TestId
	FROM AppointmentTests appTest
	JOIN Appointments app ON appTest.AppointmentId = app.Id	
	JOIN Office o ON app.OfficeId = o.Id
	WHERE appTest.Id = @appointmentTestId;		
	
	SET @externalDoctorId = ISNULL((SELECT TOP 1 ExternalDoctorId FROM PracticeDoctors WHERE Id = @practiceDoctorId),0);
	

   IF @appointmentTestLogId <=0
   BEGIN
		SET @appointmentTestLogId = ISNULL((SELECT TOP 1 Id FROM AppointmentTestSaveLogs WHERE AppointmentTestId = @appointmentTestId ORDER BY LogDate DESC),0);
   END;  

   SELECT
	 cm.Id -- measurement id
	,cm.[Name]
	,cm.[ShortName]	
	,cm.Units
	,cm.Normal
	,cm.Range1
	,cm.Range2	
	,cm.Testcode
	,sv.[Value] AS SavedValue	
	FROM fn_GetCustomMeasurements(@externalDoctorId,@customType,@visibleOnly) cm
	JOIN VPMeasurementSavedValues sv ON cm.Id = sv.VPUniqueMeasurementId	
	JOIN AppointmentTestSaveLogs l ON sv.AppointmentTestSaveLogId =l.Id	
	WHERE l.AppointmentTestId = @appointmentTestId AND l.Id = @appointmentTestLogId
	AND ISNULL(LTRIM(RTRIM(sv.[Value])),'') <> ''
	ORDER BY cm.DisplayOrder



END