﻿CREATE PROCEDURE [dbo].[GetPracticePhrasesAdmin]
	@groupId INT,	
	@practiceId INT,
	@practiceTemplateId INT,
	@rootCategoryId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    DECLARE @tblPhrases table
	(
		PracRootCategoryPhraseId int NOT NULL,
		PracRootCategoryTempId int NOT NULL,
		RootCategoryPhraseId int NOT NULL,
		PhraseName nvarchar(500) null,
		PhraseValue nvarchar(max),		
		ParentId int NULL,	
		IsSubCategory bit not null,
		RootCategoryId int not null,
		CategoryName nvarchar(1500) null,
		GroupId int not null,
		RootTemplateId int not null,	
		IsActivePracticePhrase bit not null		
		primary key (RootCategoryPhraseId)
	)

	INSERT INTO @tblPhrases
	SELECT 
	ISNULL(prcp.PracRootCategoryPhraseId,0) AS PracRootCategoryPhraseId
	,@practiceTemplateId
	,rcp.Id    
	,rcp.PhraseName
	,rcp.PhraseValue
	,rcp.ParentId	
	,rcp.IsSubCategory
	,rc.Id
	,rc.CategoryName
	,rc.GroupId
	,ISNULL(prcp.TemplateId,0) AS TemplateId
	,ISNULL(prcp.IsActivePracticePhrase,0) AS IsActivePracticePhrase
	FROM RootCategoryPhrases rcp 
	JOIN RootCategories rc ON rcp.RootCategoryId = rc.Id
	LEFT JOIN (
		SELECT prp.Id AS PracRootCategoryPhraseId,
		prp.PracRootCategoryTempId,
		pt.TemplateId,
		prp.IsActive AS IsActivePracticePhrase,
		prp.RootCategoryPhraseId FROM PracticeRootCategoryPhrases prp 
		JOIN PracticeRootCategoryTemplates pt ON prp.PracRootCategoryTempId = pt.Id
		WHERE prp.PracRootCategoryTempId = @practiceTemplateId
	) prcp ON prcp.RootCategoryPhraseId = rcp.Id
	WHERE rc.GroupId = @groupId
	AND rcp.RootCategoryId = @rootCategoryId
	AND rcp.IsActive = 1;

	WITH CustomPhraseCTE
	(
		 PracRootCatPhraseId
		,RootCategoryPhraseId
		,PracRootCategoryTempId
		,RootTemplateId
		,RootCategoryId
		,CategoryName		
		,GroupId		
		,PhraseName
		,PhraseValue
		,ParentId	
		,IsSubCategory	
		,Breadcrum 
		,IsActivePracticePhrase
		,[Level]					
	)
	AS(
	
		SELECT 
		 phrases.PracRootCategoryPhraseId
		,phrases.RootCategoryPhraseId
		,phrases.PracRootCategoryTempId
		,phrases.RootTemplateId
		,phrases.RootCategoryId
		,phrases.CategoryName
		,phrases.GroupId		
		,phrases.PhraseName
		,phrases.PhraseValue
		,phrases.ParentId	
		,phrases.IsSubCategory	
		,CAST(phrases.CategoryName + ' -> '+phrases.PhraseName AS nvarchar(2000))
		,phrases.IsActivePracticePhrase
		,0 	
		FROM @tblPhrases phrases		
		WHERE phrases.ParentId IS NULL		
		UNION ALL
		--recursive select
		SELECT 
		 phrases.PracRootCategoryPhraseId
		,phrases.RootCategoryPhraseId
		,cte.PracRootCategoryTempId
		,cte.RootTemplateId
		,cte.RootCategoryId
		,cte.CategoryName		
		,cte.GroupId		
		,phrases.PhraseName
		,phrases.PhraseValue
		,phrases.ParentId	
		,phrases.IsSubCategory	
		,CAST(cte.Breadcrum + ' -> '+phrases.PhraseName AS nvarchar(2000))
		,phrases.IsActivePracticePhrase
		,cte.[level] + 1						
		FROM @tblPhrases phrases
		JOIN CustomPhraseCTE cte ON phrases.ParentId = cte.RootCategoryPhraseId				
		WHERE cte.RootCategoryId = phrases.RootCategoryId
	)
	SELECT 
	cte.PracRootCatPhraseId
	,cte.RootCategoryPhraseId
	,cte.PracRootCategoryTempId
	,cte.RootTemplateId
	,cte.RootCategoryId
	,cte.CategoryName		
	,cte.GroupId	
	,cte.PhraseName
	,cte.PhraseValue
	,ISNULL(cte.ParentId,0) AS ParentId		
	,cte.IsSubCategory
	,cte.Breadcrum
	,cte.IsActivePracticePhrase
	,cte.[Level]					
	FROM CustomPhraseCTE cte 
	ORDER BY cte.ParentId ASC, cte.CategoryName ASC, cte.PhraseName ASC
END