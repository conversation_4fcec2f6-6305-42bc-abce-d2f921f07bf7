﻿CREATE PROCEDURE [dbo].[GetRootCategorySavedValuesLogIds]
	@appointmentTestLogIds AS dbo.IntegerList READONLY
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 
	   rcs.Id
      ,rcs.SavedValue
	  ,rcs.RootCategoryId
	  ,rcs.AppointmentTestSaveLogId
	  ,ats.AppointmentTestId	  
	  ,ats.LogDate
	  ,ats.PracRootCategoryTempId 	  
  FROM RootCategorySavedValues rcs  
  JOIN AppointmentTestSaveLogs ats ON rcs.AppointmentTestSaveLogId = ats.Id
  JOIN @appointmentTestLogIds l ON rcs.AppointmentTestSaveLogId = l.IntegerValue  
  
  END
