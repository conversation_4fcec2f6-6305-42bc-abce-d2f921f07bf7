CREATE OR ALTER PROCEDURE [dbo].[GetReportsSent_V2] 
	@patientId INT,
	@isVP BIT
AS
BEGIN
	
IF @isVP = 1
	BEGIN
		SELECT
			CAST(ROW_NUMBER() OVER (ORDER BY AppointmentDate DESC) AS INT) AS Id
		   ,SendId
           ,PatientId  
           ,AppointmentId
           ,AppointmentDate
           ,OfficeId
           ,ISNULL((SELECT TOP 1 ou.[url] FROM OfficeUrls ou WHERE sr.OfficeId = ou.officeId AND ou.urlTypeId = 5),'') as OfficeUrl 
           ,DocName
           ,[Url]
           ,ISNULL((SELECT TOP 1 Id FROM Tests t WHERE t.testShortName = 'VP'),29) as TestId
           ,DateSent 
           ,[Sent]
           ,Amended
        FROM (SELECT s.Id as SendId
                ,a.PatientRecordId as PatientId
                ,a.Id as AppointmentId
                ,a.appointmentTime as AppointmentDate
                ,a.OfficeId
                ,s.<PERSON>ame
                ,s.[Url]
                ,s.DateEntered AS DateSent
                ,s.[Sent]
                ,s.Amended
                ,ROW_NUMBER() OVER ( PARTITION BY s.url ORDER BY a.appointmentTime DESC, s.Id DESC ) seq
                FROM VP_SendReport s
                JOIN Appointments a ON a.Id = s.AppointmentId
                WHERE s.PatientRecordId = @patientId 
                AND s.SendTypeId = 6 
                AND s.[Sent] = 1
                AND a.IsActive = 1
        ) sr where seq = 1
        ORDER BY sr.AppointmentDate DESC, sr.SendId DESC
	END
	ELSE 
	BEGIN
		SELECT 
		 CAST(ROW_NUMBER() OVER (ORDER BY a.appointmentTime DESC, s.Id DESC) AS INT) AS Id
        ,s.Id as SendId		
        ,a.PatientRecordId as PatientId
        ,a.Id as AppointmentId
        ,a.appointmentTime as AppointmentDate
        ,a.OfficeId
        ,ISNULL((SELECT TOP 1 ou.[url] FROM OfficeUrls ou WHERE a.OfficeId = ou.officeId AND ou.urlTypeId = 5),'') as OfficeUrl 
        ,s.DocName
        ,s.[Url]
        ,s.TestId
        ,s.DateEntered as DateSent
        ,s.[Sent]
        ,s.Amended
		FROM WS_SendReport s
		JOIN Appointments a ON a.Id = s.AppointmentId
		WHERE a.PatientRecordId = @patientId
		AND s.SendTypeId = 6 
		AND s.[Sent] = 1
		AND s.Active = 1
        AND a.IsActive = 1
		ORDER BY a.appointmentTime DESC, s.Id DESC
	END
    
END
GO