﻿
CREATE PROCEDURE [dbo].[GetReportsSent] 
	@patientId INT,
	@isVP BIT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @sentReport TABLE
	   ( 
		 Id                        int identity(1,1) not null,
		 SendId                 int not null,
		 PatientId                 int not null,
	     AppointmentId             int not null,	
		 AppointmentDate		   datetime2 not null,	 
	     OfficeId                  int not null,
		 OfficeUrl                 nvarchar(500) null,
		 DocName                   nvarchar(500) null,
		 [Url]				       nvarchar(500) null,
		 TestId					   int null,		 	
         DateSent                  datetime2 null, 		 
		 [Sent]                    bit null,
		 Amended                   bit null 
		 ,primary key (id)
	   );

	DECLARE @patientAppointments TABLE
	( 		  
		PatientRecordId           int not null,
	    AppointmentId             int not null,	
		AppointmentDate		      datetime2 not null,	 
	    OfficeId                  int not null		 	 
	);

	INSERT INTO @patientAppointments(PatientRecordId,AppointmentId,AppointmentDate,OfficeId)
	SELECT a.PatientRecordId, a.Id,a.appointmentTime, a.OfficeId
	FROM Appointments a
	WHERE a.PatientRecordId = @patientId AND a.IsActive = 1

	IF @isVP = 1
	BEGIN
		INSERT INTO @sentReport(SendId,PatientId,AppointmentId,AppointmentDate,OfficeId,OfficeUrl,DocName,[Url],TestId,DateSent,[Sent],Amended)
		SELECT 
		 s.Id
		,a.PatientRecordId 
		,a.AppointmentId
		,a.AppointmentDate
		,a.OfficeId
		,ISNULL((SELECT TOP 1 ou.[url] FROM OfficeUrls ou WHERE a.OfficeId = ou.officeId AND ou.urlTypeId = 5),'') -- office url 5 = clinic server url
		,s.DocName
		,s.[URL]
		,ISNULL((SELECT TOP 1 Id FROM Tests t WHERE t.testShortName = 'VP'),29)
		,s.DateEntered
		,s.[Sent]
		,s.Amended
		FROM VP_SendReport s
		JOIN @patientAppointments a ON a.AppointmentId = s.AppointmentId
		WHERE s.PatientRecordId = @patientId 
		AND s.SendTypeId = 6 -- sent to clinic server
		AND s.[Sent] = 1
		ORDER BY a.AppointmentDate DESC, s.Id DESC
	END
	ELSE -- work sheet
	BEGIN
		INSERT INTO @sentReport(SendId,PatientId,AppointmentId,AppointmentDate,OfficeId,OfficeUrl,DocName,[Url],TestId,DateSent,[Sent],Amended)
		SELECT 
		 s.Id		
		,a.PatientRecordId 
		,a.AppointmentId
		,a.AppointmentDate
		,a.OfficeId
		,ISNULL((SELECT TOP 1 ou.[url] FROM OfficeUrls ou WHERE a.OfficeId = ou.officeId AND ou.urlTypeId = 5),'') -- office url 5 = clinic server url
		,s.DocName
		,s.[URL]
		,s.TestId
		,s.DateEntered
		,s.[Sent]
		,s.Amended
		FROM WS_SendReport s
		JOIN @patientAppointments a ON a.AppointmentId = s.AppointmentId
		WHERE s.SendTypeId = 6 -- sent to clinic server
		AND s.[Sent] = 1
		AND s.Active = 1
		ORDER BY a.AppointmentDate DESC, s.Id DESC
	END

	SELECT * FROM @sentReport
    
END

