﻿CREATE PROCEDURE [dbo].[GetDaysheetTests] 
	@officeId INT = NULL,
	@selectedDate DATETIME = NULL,
	@appointmentId INT = NULL,	
	@testId INT = NULL,
	@testGroupId INT = NULL,
	@userId INT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

   DECLARE @initdt datetime;
   DECLARE @enddt datetime;
   DECLARE @query nvarchar(4000);
   DECLARE @params nvarchar(1000);

   set @initdt = cast(convert(varchar, @selectedDate, 112)+' 00:00:00' as datetime)
   set @enddt = cast(convert(varchar, @selectedDate, 112)+' 23:59:59' as datetime)

   IF @appointmentId IS NOT NULL AND @appointmentId > 0
   BEGIN
		SELECT @officeId = OfficeId FROM Appointments WHERE Id = @appointmentId
   END

   DECLARE @testtab dbo.IntegerList;
   DECLARE @tblActiveUserFilters dbo.IntegerList;	-- for filter techs user ids

   IF @userId > 0
	BEGIN
		-- get only the practice techs user ids from the user filters
		INSERT INTO @tblActiveUserFilters(IntegerValue)
		SELECT DISTINCT u.UserID FROM UserScheduleViewFilters uf 
		JOIN AspNetUsers u ON uf.UserFilterId = u.UserID
		JOIN dbo.fn_GetTechnicianTypes() tp ON u.CerebrumUserType = tp.Id		
		WHERE uf.UserId = @userId 
		AND uf.IsActive = 1
		AND uf.OfficeId = @officeId					
	END

   
	 SET @query = 'SELECT apptest.[Id]
						  ,apptest.[TestId]
						  ,apptest.[startTime]
						  ,apptest.[AppointmentTestStatusId]
						  ,apptest.[testDuration]
						  ,apptest.[billStatusId]
						  ,apptest.[referralDoctorId]
						  ,apptest.[AppointmentId]
						  ,apptest.[AccessionNumber]
						  ,apptest.[PhysicianComments]
						  ,apptest.[TechnicianComments]
						  ,apptest.[IsActive]
						  ,apptest.[DateCreated]
						  ,apptest.[DateUpdated]
						  ,apptest.[SetForReview]
						  ,apptest.[ReassignDocID]
						  ,apptest.[ReassignDate]	  
						  ,ISNULL(res.assignedToUserId,0) AS assignedToUserId
						  ,ISNULL(res.performedByUserId,0) AS performedByUserId
						  ,res.permissionId
						  ,res.isDoctorRequiredInOffice
						  ,ISNULL(u.FirstName,'''') + '' ''+ ISNULL(u.LastName,'''') AS UserFullName
						  ,test.testFullName
						  ,test.testShortName
						  ,test.RequireDevice
						  ,teststatus.Color AS TestStatusColor
						  ,teststatus.Status AS TestStatus	  
					  FROM [dbo].[AppointmentTests] apptest
					  JOIN AppointmentTestStatus teststatus ON apptest.AppointmentTestStatusId = teststatus.Id
					  JOIN Tests test ON apptest.TestId = test.Id
					  JOIN AppointmentTestResources res ON apptest.Id = res.AppointmentTestId ' +
					  --+ CASE WHEN @appointmentId IS NULL OR @appointmentId = 0 THEN 'LEFT' ELSE '' END +
					  'LEFT JOIN AspNetUsers u ON res.assignedToUserId = u.UserID
					  WHERE apptest.IsActive = 1 AND res.isActive = 1 ';

	  if @appointmentId is null or @appointmentId = 0
	  begin		
	     
		 SET @query = @query + N'  AND apptest.AppointmentId IN (SELECT Id FROM Appointments WHERE OfficeId = @officeId AND isActive = 1 AND appointmenttime between @initdt and @enddt ) '

		 SET @params = N'@officeid int, @initdt datetime, @enddt datetime, @testtab IntegerList READONLY, @testid int, @tblActiveUserFilters IntegerList READONLY';

		 IF IsNull(@testid,0) > 0 
		 begin
			SET @query = @query + N'   AND apptest.TestId = @testId ';
		 end;

		 ELSE IF IsNull(@testGroupId,0) > 0
		 begin
			INSERT INTO @testtab
			SELECT DISTINCT TestId FROM TestGroups WHERE GroupId = @testGroupId;			
						 
			SET @query = @query + N'  AND apptest.TestId IN (select IntegerValue from @testtab)';
		 end;

		 IF EXISTS(SELECT * FROM @tblActiveUserFilters)
		 BEGIN
			SET @query = @query + N'  AND (apptest.TestId = 29 OR u.UserID IN (SELECT IntegerValue FROM @tblActiveUserFilters)) ';		 
		 END		 	 
		 
		 SET @query = @query + N' ORDER BY apptest.startTime ';

--		 print @query;
--       print @params;
	 
		 EXEC sp_executesql @query, @params, @officeId = @officeId, @initdt = @initdt, @enddt = @enddt, @testtab = @testtab, @testid = @testid, @tblActiveUserFilters = @tblActiveUserFilters;
	 
	  end
	  else
	  begin
	     
		 SET @query = @query + N'  AND apptest.AppointmentId = @appointmentId ';
		 IF EXISTS(SELECT * FROM @tblActiveUserFilters)
		 BEGIN
			SET @query = @query + N' AND (apptest.TestId = 29 OR u.UserID IN (SELECT IntegerValue FROM @tblActiveUserFilters)) ';		 
		 END	
		 
		 SET @query = @query + N' ORDER BY apptest.startTime ';		 		 
		 SET @params = N'@appointmentId int, @tblActiveUserFilters IntegerList READONLY'; 

		 print @query;
         print @params;

		 EXEC sp_executesql @query, @params, @appointmentId = @appointmentId, @tblActiveUserFilters = @tblActiveUserFilters;
	  end;
END