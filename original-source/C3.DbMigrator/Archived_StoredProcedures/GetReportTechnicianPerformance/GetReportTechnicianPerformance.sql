﻿CREATE PROCEDURE [dbo].[GetReportTechnicianPerformance] 
	@PracticeId INT,
	@OfficeId INT = NULL,
	@PracticeDoctorId INT = NULL,
	@StartDate DATETIME,
	@EndDate DATETIME,
	@GroupByOffice BIT = 0,
	@GroupByTest BIT = 0,	
	@TestGroupIds dbo.IntegerList READONLY,
	@TestIds dbo.IntegerList READONLY,
	@TechnicianUserIds dbo.IntegerList READONLY
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @fullStatementSql NVARCHAR(MAX);  
	DECLARE @querySql NVARCHAR(4000);
	DECLARE @whereSql NVARCHAR(1000);
    DECLARE @paramsSql NVARCHAR(1000);  
	DECLARE @groupBySql NVARCHAR(300);

	SET @StartDate = CAST(CONVERT(VARCHAR, @StartDate, 112)+' 00:00:00' AS DATETIME);
	SET @EndDate = CAST(CONVERT(VARCHAR, @EndDate, 112)+' 23:59:59' AS DATETIME);	

	DECLARE @ResultSetTable AS TABLE (
		UserId INT NOT NULL,
		FirstName NVARCHAR(100),
		LastName NVARCHAR(100),
		OfficeName NVARCHAR(100),
		TestShortName VARCHAR(100),
		TotalTests INT
	)

	DECLARE @AppointmentTestStatus AS dbo.IntegerList;
	INSERT INTO @AppointmentTestStatus (IntegerValue)
	VALUES (4),(5),(6),(7),(8),(9),(10),(11),(18);
	/* Arrived, Test started, Test completed, Test ready to start,	Images / data transferred, Ready for doctor, Trainee report ready, Report Completed, Being Sent	*/

	SET @querySql = 
	'SELECT APTR.assignedToUserId,USR.FirstName,USR.LastName,'+IIF(@GroupByOffice = 1,'O.Name','NULL')+','+IIF(@GroupByTest = 1,'T.testShortName','NULL')+',COUNT(DISTINCT APT.Id)
	FROM Appointments AP
	JOIN Office O ON O.Id = AP.OfficeId
	JOIN AppointmentTests APT ON APT.AppointmentId = AP.Id
	JOIN Tests T ON T.Id = APT.TestId
	JOIN AppointmentTestResources APTR ON APTR.AppointmentTestId = APT.Id
	JOIN AspNetUsers USR ON USR.UserID = APTR.assignedToUserId
	JOIN dbo.fn_GetTechnicianTypes() TT ON TT.Id = USR.CerebrumUserType ';

	SET @whereSql = N' WHERE O.PracticeId = @PracticeId AND AP.appointmentTime BETWEEN @StartDate AND @EndDate ';
	SET @whereSql = @whereSql + N' AND AP.appointmentStatus <> 7 AND AP.IsActive = 1 AND APT.IsActive = 1 AND APTR.IsActive = 1 '
	SET @whereSql = @whereSql + N' AND APT.AppointmentTestStatusId IN (SELECT IntegerValue FROM @AppointmentTestStatus) '

	IF (@OfficeId IS NOT NULL)
	BEGIN
		SET @whereSql = @whereSql + N' AND AP.OfficeId = @OfficeId ';
	END

	IF (@PracticeDoctorId IS NOT NULL)
	BEGIN
		SET @whereSql = @whereSql + N' AND AP.PracticeDoctorId = @PracticeDoctorId ';
	END

	IF EXISTS(SELECT TOP 1 1 FROM @TestGroupIds)
	BEGIN
		SET @whereSql = @whereSql + N' AND EXISTS (SELECT TOP 1 1 FROM TestGroups TG, @TestGroupIds TGI WHERE TG.GroupId = TGI.IntegerValue AND TG.TestId = APT.TestId ) ';
	END

	IF EXISTS(SELECT TOP 1 1 FROM @TestIds)
	BEGIN
		SET @whereSql = @whereSql + N' AND EXISTS (SELECT TOP 1 1 FROM @TestIds WHERE IntegerValue = APT.TestId ) ';
	END

	IF EXISTS (SELECT TOP 1 1 FROM @TechnicianUserIds)
	BEGIN
		SET @whereSql = @whereSql + N' AND EXISTS (SELECT TOP 1 1 FROM @TechnicianUserIds WHERE IntegerValue = APTR.assignedToUserId ) ';
	END	

	SET @groupBySql = N' GROUP BY APTR.assignedToUserId,USR.FirstName,USR.LastName ';
	IF @GroupByOffice = 1
	BEGIN
		SET @groupBySql = @groupBySql + N',O.Name ';
	END
	IF @GroupByTest = 1
	BEGIN
		SET @groupBySql = @groupBySql + N',T.testShortName ';
	END

	SET @fullStatementSql = @querySql + @whereSql + @groupBySql;

	--PRINT @fullStatementSql;

	SET @paramsSql = N'@PracticeId INT, @OfficeId INT, @PracticeDoctorId INT, @StartDate DATETIME, @EndDate DATETIME, @TestGroupIds IntegerList READONLY, @TestIds IntegerList READONLY, @TechnicianUserIds IntegerList READONLY, @AppointmentTestStatus IntegerList READONLY';

	INSERT INTO @ResultSetTable (UserId,FirstName,LastName,OfficeName,TestShortName,TotalTests)
	EXEC sp_executesql @fullStatementSql, @paramsSql, @PracticeId = @PracticeId, @OfficeId = @OfficeId, @PracticeDoctorId = @PracticeDoctorId, @StartDate = @StartDate, @EndDate = @EndDate, @TestGroupIds = @TestGroupIds, @TestIds = @TestIds, @TechnicianUserIds = @TechnicianUserIds, @AppointmentTestStatus = @AppointmentTestStatus;

	SELECT UserId,FirstName,LastName,OfficeName,TestShortName,TotalTests
	FROM @ResultSetTable;
END