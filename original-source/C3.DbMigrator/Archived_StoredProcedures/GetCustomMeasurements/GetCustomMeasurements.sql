﻿CREATE PROCEDURE [dbo].[GetCustomMeasurements]
	@externalDoctorId INT,
	@customType INT,
	@visibleOnly BIT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT
	cm.Id -- measurement id
	,cm.[Name]
	,cm.[ShortName]
	,cm.[Order]
	,cm.Units
	,cm.Normal
	,cm.Range1
	,cm.Range2
	,cm.Spec
	,cm.[Type]
	,cm.ExternalDoctorId
	,cm.[Status]
	,cm.Options
	,cm.Testcode
	,cm.Cdid
	,cm.ValueType
	,cm.Visible
	,cm.DisplayOrder
	FROM fn_GetCustomMeasurements(@externalDoctorId,@customType,@visibleOnly) cm
	ORDER BY cm.DisplayOrder
	, cm.[Name]
END