﻿CREATE PROCEDURE [dbo].[GetInventoryOverDue]
	@practiceId INT,
	@officeId INT,
	@selectedDate DATETIME
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    
	SELECT 
	i.id AS DeviceNumberId
	,i.officeId AS OfficeId
	,o.[name] as OfficeName
	,i.inventoryTypeId AS DeviceTypeId
	,i.code AS DeviceNumber
	,it.[name] AS DeviceType
	,pe.Id AS PatientEquipmentId
	,pe.PatientRecordId AS PatientId
	,ISNULL(d.lastName,'')+', '+ISNULL(d.firstName,'') AS PatientFullName
	,pe.DateStarted
	,pe.DateExpectedReturn
	,pe.Notes
	,pe.AppointmentTestId	
	,pe.AssignedByUserId
	,ISNULL((SELECT TOP 1 ISNULL(u.lastName,'')+', '+ISNULL(u.firstName,'') FROM AspNetUsers u WHERE u.UserId = pe.AssignedByUserId),'') AS AssignedByUser
	FROM
	PatientEquipments pe
	JOIN Demographics d ON pe.PatientRecordId = d.PatientRecordId
	JOIN StoreInventories i ON pe.InventoryId = i.id
	JOIN StoreInventoryTypes it ON i.inventoryTypeId = it.id
	JOIN Office o ON i.officeId = o.Id
	WHERE
	pe.DateExpectedReturn IS NOT NULL
	AND pe.DateExpectedReturn < @selectedDate
	AND pe.DateReturned IS NULL
	AND i.statusId = 2 -- in use
	AND d.active = 0
	AND i.practiceId=@practiceId
	AND i.officeId = @officeId
	
END


