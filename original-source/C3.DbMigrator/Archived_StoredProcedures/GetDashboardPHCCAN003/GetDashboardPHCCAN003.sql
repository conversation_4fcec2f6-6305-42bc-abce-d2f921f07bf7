﻿CREATE PROCEDURE [dbo].[GetDashboardPHCCAN003] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @PAPExclusion AS TABLE (PatientRecordId INT)
	DECLARE @PAPScreening AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId  and MRP.IsActive = 1
	WHERE 
	D.active = 0														-- Pastient is active	
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) BETWEEN 21 AND 69	-- Patient is between 21 and 69
	AND D.gender = 1													-- Patient is Female
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId


	-- PAP Exclusion 
	INSERT INTO @PAPExclusion
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q140A'
	UNION ALL 
	SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('V45.77','68.3','68.4','68.5','68.6','68.7','68.8','68.9')						-- Excluded Diagnosis codes
		OR CPP.Problem_Description LIKE '%hysterectomy%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%hysterosal%'
		OR CPP.Problem_Description LIKE '%Cervical Ca%'
		OR CPP.Problem_Description LIKE '%TVHt%'
		OR CPP.Problem_Description LIKE '%TABH%'
		OR CPP.Problem_Description LIKE '%TAH%'
		)
		AND Deleted = 0 and cpp.UpdateDate is null

	--- PAP Screening in the last 36 months inclusive  
	INSERT INTO @PAPScreening
	SELECT IM.PatientRecordId	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId=2							-- Type = PAP
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) <= 36
	UNION
	SELECT PatientRecordId 
	FROM BillDetails B					--
	WHERE B.serviceCode = 'Q011A'
	AND DATEDIFF(MONTH,b.serviceDate,GETDATE()) <= 36

	--- Segments	

		INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @PAPScreening)
	
		INSERT INTO @Segments
	SELECT 2,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPExclusion)
	AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PAPScreening)

		INSERT INTO @Segments
	SELECT 3,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @PAPExclusion)	

		
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
