﻿CREATE PROCEDURE [dbo].[GetUserOffices]
	@userId  INT,
	@practiceId INT,
	@isAdmin BIT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @offices TABLE
	   ( Id                         int identity(1,1) not null,	     
		 PracticeId                 int not null,
	     OfficeId                   int not null,		 
		 OfficeName                 nvarchar(200) null,
         BusinessName               nvarchar(200) null,
         OfficeUrl                  nvarchar(250) null                
		 ,primary key (Id)
	   );

	INSERT INTO @offices  
	SELECT  
	 o.PracticeId
	,o.Id
	,o.[name] 
	,o.businessName 
	,ISNULL(
	(SELECT TOP 1 l.[url] FROM OfficeUrls l JOIN OfficeUrlTypes t ON t.Id = l.urlTypeId WHERE l.officeId = o.Id AND t.urlType = 'FaxSentReport'
	)
	,'')
	FROM Office o 	
	WHERE o.PracticeId = @practiceId 


	--IF @isAdmin = 1
	--BEGIN
	--	SELECT 
	--	 o.PracticeId
	--	,o.OfficeId
	--	,o.OfficeName
	--	,o.BusinessName 
	--	,o.OfficeUrl
	--	 FROM @offices o
	--END
	--ELSE
	--BEGIN
		SELECT 
		 o.PracticeId
		,o.OfficeId
		,o.OfficeName
		,o.BusinessName 
		,o.OfficeUrl
		 FROM @offices o
		 JOIN UserOffices uo ON o.OfficeId = uo.OfficeId
		 WHERE uo.ApplicationUserId = (SELECT TOP 1 Id FROM AspNetUsers WHERE UserId = @userId)

	--END

END

