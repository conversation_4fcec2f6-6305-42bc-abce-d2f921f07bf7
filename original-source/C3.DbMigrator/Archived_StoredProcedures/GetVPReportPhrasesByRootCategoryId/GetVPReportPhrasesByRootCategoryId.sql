﻿CREATE PROCEDURE [dbo].[GetVPReportPhrasesByRootCategoryId] 	
	@rootCategoryId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	-- this is for import only
	SET NOCOUNT ON;
	
	DECLARE @usePracticeSettings BIT = 0;
	DECLAR<PERSON> @isActive BIT = 1;
	DECLARE @visible BIT = 1;
	DECLARE @status INT = 0;
	DECLARE @statusSt INT = 2; -- status for opening statement
	--DECLARE @spec INT = 0;
	DECLARE @openingSt Varchar(30) = 'Opening Statement'; -- opening statement
	DECLARE @openingStId INT = ISNULL(
	(SELECT top 1 Id FROM VPReportPhrases WHERE [Status] = 2 AND [Parent] = -1 
	AND [Name] = @openingSt),0);

			-- get all phrases for the doctor
		WITH ReportPhraseCTE
		(
			Id, 
			RootCategoryId,
			[Name], 
			[Value], 
			Parent, 
			ParentName,
			[Level],
			[Order],
			[Root],
			[Status],
			[Spec],			
			DrID									
		)
		AS
		( 
			SELECT p.Id, p.Id, p.[Name], p.[Value], p.Parent, p.[Name],0, p.[Order], p.[Root],p.[status],p.[Spec],p.DrID
				
			FROM VPReportPhrases p 			
			WHERE p.Parent = -1 			
			--AND p.Spec = 0 
			AND (p.[Status] = @status OR p.[Status] = @statusSt)				
			AND p.[Name] IS NOT NULL AND RTRIM(LTRIM(p.[Name])) != '' 			
			AND p.Id = @rootCategoryId
			UNION ALL
			--recursive select
			SELECT vpr.Id, cte.RootCategoryId, vpr.[Name], vpr.[Value], vpr.Parent, cte.[Name],cte.[level] + 1, vpr.[Order], vpr.[Root],vpr.[status],vpr.[Spec],vpr.DrID
			
			FROM VPReportPhrases vpr 	
			JOIN ReportPhraseCTE cte ON vpr.Parent = cte.Id		
			WHERE
			--vpr.Spec = 0 
			(vpr.[Status] = @status OR vpr.[Status] = @statusSt)	
			AND vpr.[Name] IS NOT NULL AND RTRIM(LTRIM(vpr.[Name])) != '' 			
		) 

		SELECT 
		vpr.Id
		,vpr.RootCategoryId
		,vpr.[Name]
		,vpr.[Value]
		,vpr.Parent		
		,vpr.ParentName
		,vpr.[Order]
		,vpr.[Root]						
		,vpr.[Status]
		,vpr.Spec
		,vpr.[Level]
		,vpr.DrID
		,CAST(ISNULL((SELECT TOP 1 1 FROM VPReportPhrases WHERE Parent = vpr.Id),0) AS BIT) AS HasChildren
		FROM ReportPhraseCTE vpr					
        ORDER BY vpr.[Level];
	
END



