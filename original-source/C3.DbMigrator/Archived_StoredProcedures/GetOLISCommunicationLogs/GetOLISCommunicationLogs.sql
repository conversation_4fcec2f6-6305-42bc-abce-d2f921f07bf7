﻿CREATE PROCEDURE [dbo].[GetOLISCommunicationLogs]
	@practiceId INT,
	@startDate DATETIME, 	
	@endDate DATETIME,
	@userId INT,
	@practiceDoctorId INT = 0,	
    @PageNum int,
    @PageSize int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @totalRecords INT = 0;
	DECLARE @reportReceived INT = 0;
	DECLARE @requestMSGType NVARCHAR(10) = 'Request';

	DECLARE @cpsoList TABLE
	   ( id                        int identity(1,1) not null,
	     externalDoctorId          int not null,
		 practiceDoctorId          int not null,		 
	     doctorCPSO                nvarchar(200),		
		 primary key (id)
	   );

	DECLARE @olisRequests TABLE
	( 
		id                 int identity(1,1) not null,
	    logId              int not null,
		msgId              uniqueidentifier NOT NULL,	 
		createdDateTime    datetime2(7) NOT NULL,  		
		primary key (id)
	);

	DECLARE @olisCommLogs TABLE
	   ( 
	    id int identity(1,1) not null,
	    logId                     int not null,
		userId                    int NOT NULL,		
		consentViewOverride       bit NOT NULL,
		MSH10ClientTransactionID  uniqueidentifier NOT NULL,
		MessageType               nvarchar(10) NULL,
		IsSuccessStatusCode       bit NOT NULL,
		[Message]                 nvarchar(4000) NULL,
		createdDateTime           datetime2(7) NOT NULL,
		OLISCommunicationLogId    int NULL,
		PracticeId                int NOT NULL,
		QueryType                 nvarchar(3) NULL,
		PatientRecordId           int NULL,
		FromDate                  datetime2(7) NULL,
		ToDate                    datetime2(7) NULL,
		RequestingHIC             nvarchar(10) NULL,		
		OLISTranscationID         nvarchar(128) NULL,
		EMRQueryType              int NOT NULL,
		Error                     nvarchar(4000) NULL,
		ConsentComment            nvarchar(500) NULL,		
		OfficeId                  int NULL,	
		OfficeName                nvarchar(200) NULL,
		userName                  nvarchar(200) NULL,	
		DoctorFirstName           nvarchar(200) NULL,
		DoctorLastName            nvarchar(200) NULL,
		TotalRecords			  int not null
		 primary key (id)
	   );
	
	--get doctors cpso's
	INSERT INTO @cpsoList(externalDoctorId,practiceDoctorId,doctorCPSO)
	SELECT e.Id, pd.Id,e.CPSO 
	FROM ExternalDoctors e
	JOIN PracticeDoctors pd ON e.Id = pd.ExternalDoctorId
	JOIN AspNetUsers u ON u.Id = pd.ApplicationUserId
	JOIN UserDoctors ud ON u.Id = ud.ApplicationUserId
	WHERE u.UserID = @userId
	AND pd.IsActive = 1
	AND e.active = 1 
	AND e.CPSO IS NOT NULL

	-- check if the practice doctor is already in the list, if not add
	IF @practiceDoctorId > 0 AND NOT EXISTS (SELECT * FROM @cpsoList WHERE practiceDoctorId = @practiceDoctorId)
	BEGIN
		INSERT INTO @cpsoList(externalDoctorId,practiceDoctorId,doctorCPSO)
		SELECT e.Id, pd.Id,e.CPSO 
		FROM ExternalDoctors e
		JOIN PracticeDoctors pd ON e.Id = pd.ExternalDoctorId		
		WHERE pd.Id = @practiceDoctorId
		AND pd.IsActive = 1
		AND e.active = 1 
		AND e.CPSO IS NOT NULL
	END

	--get only the reuqest
	INSERT INTO @olisRequests(logId,msgId,createdDateTime)
	SELECT l.Id, l.MSH10ClientTransactionID,createdDateTime
	FROM OLISCommunicationLogs l
	JOIN Office o ON l.OfficeId = o.Id
	JOIN AspNetUsers u ON l.userId = u.UserID
	JOIN ExternalDoctors e ON l.RequestingHIC = e.CPSO	
	WHERE l.PracticeId = @practiceId
	AND l.createdDateTime >= @startDate 
	AND l.createdDateTime <= @endDate
	AND e.CPSO IS NOT NULL
	AND l.MessageType = @requestMSGType
	AND l.ContinuousPointer = 0
	AND (o.ShowInfo = 1 OR e.CPSO IN (SELECT doctorCPSO FROM @cpsoList));	

	SET @totalRecords = (SELECT COUNT(Id) FROM @olisRequests)	

	INSERT INTO @olisCommLogs
	(
		logId,
		userId,		
		consentViewOverride,
		MSH10ClientTransactionID,
		MessageType,
		IsSuccessStatusCode,
		[Message],
		createdDateTime,
		OLISCommunicationLogId ,
		PracticeId,
		QueryType,
		PatientRecordId,
		FromDate,
		ToDate,
		RequestingHIC,		
		OLISTranscationID,
		EMRQueryType,
		Error,
		ConsentComment,		
		OfficeId,
		OfficeName,
		userName,	
		DoctorFirstName,
		DoctorLastName,
		TotalRecords
	)
	SELECT 
	l.Id,
	l.userId,		
	l.consentViewOverride,
	l.MSH10ClientTransactionID,
	l.MessageType,
	l.IsSuccessStatusCode,
	l.[Message],
	l.createdDateTime,
	l.OLISCommunicationLogId ,
	l.PracticeId,
	l.QueryType,
	l.PatientRecordId,
	l.FromDate,
	l.ToDate,
	l.RequestingHIC,		
	l.OLISTranscationID,
	l.EMRQueryType,
	l.Error,
	l.ConsentComment,		
	l.OfficeId,
	o.[name],
	u.UserName,	
	e.firstName,
	e.lastName,	
	@totalRecords 
	FROM 
	(
		SELECT * FROM @olisRequests r ORDER BY r.createdDateTime DESC, r.logId ASC
		OFFSET (@PageNum-1)*@PageSize ROWS FETCH NEXT @PageSize ROWS ONLY
	) AS req
	JOIN OLISCommunicationLogs l ON req.msgId = l.MSH10ClientTransactionID	
	JOIN Office o ON l.OfficeId = o.Id
	JOIN AspNetUsers u ON l.userId = u.UserID
	JOIN ExternalDoctors e ON l.RequestingHIC = e.CPSO	   

	SELECT 
	l.logId,
	l.userId,		
	l.consentViewOverride,
	l.MSH10ClientTransactionID,
	l.MessageType,
	l.IsSuccessStatusCode,
	l.[Message],
	l.createdDateTime,
	l.OLISCommunicationLogId ,
	l.PracticeId,
	l.QueryType,
	l.PatientRecordId,
	l.FromDate,
	l.ToDate,
	l.RequestingHIC,		
	l.OLISTranscationID,
	l.EMRQueryType,
	l.Error,
	l.ConsentComment,		
	l.OfficeId,
	l.OfficeName,
	l.userName,	
	l.DoctorFirstName,
	l.DoctorLastName,
	l.TotalRecords,
	r.Id AS ReportId,
	p.PatientName,
	p.OLISAccessionNumber,
	p.CerebrumAccessionNumber,
	ISNULL(p.ReportStatus,0) AS ReportStatus,
	p.RejectReason,
	CAST(
		(CASE WHEN p.ReportStatus = @reportReceived AND EXISTS( SELECT TOP 1* FROM HL7Report WHERE CerebrumAccession = p.CerebrumAccessionNumber)THEN 1 
		ELSE 0 END) AS BIT) AS IsReceived	
	FROM @olisCommLogs l	
	LEFT JOIN OLISReceivedReports r ON l.logId = r.OLISCommunicationLogId
	LEFT JOIN OLISReceivedReportPatients p ON r.Id = p.OLISReceivedReportId	
	
END