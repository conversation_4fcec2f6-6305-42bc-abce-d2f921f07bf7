﻿
CREATE PROCEDURE [dbo].[GetInventoryItems]
	@practiceId INT	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;


   SELECT 
	i.id AS InventoryId
	,i.officeId AS OfficeId
	,o.[name] as OfficeName
	,i.inventoryTypeId AS DeviceTypeId
	,i.code AS DeviceNumber
	,it.[name] AS DeviceType
	,i.statusId AS StatusId
	,s.[name] AS StatusType
	,i.notes AS Notes
	,i.DateCreated
	,ISNULL((SELECT COUNT(Id) FROM PatientEquipments WHERE InventoryId = i.id),0) AS HistoryCount	
	FROM	
	StoreInventories i 
	JOIN StoreInventoryTypes it ON i.inventoryTypeId = it.id
	JOIN Office o ON i.officeId = o.Id
	JOIN StoreInventoryStatus s ON i.statusId = s.id
	WHERE o.PracticeId = @practiceId AND i.IsActive = 1
	

	
END


