﻿CREATE PROCEDURE [dbo].[GetBillingEdtErrorAdmissionActions] 
	@practiceId int,
	@admissionActionIds nvarchar(max)
AS
BEGIN
	SET NOCOUNT ON;

	if (@admissionActionIds = '')
		return;

	select b.Id admissionActionId,a.DateAdmitted dateAdmitted
	from HDAdmissions a inner join HDAdmissionActions b on a.Id=b.AdmissionId inner join PracticeDoctors c on a.PracticeDoctorId=c.Id
	where c.PracticeId=@practiceId and b.id in (SELECT value FROM STRING_SPLIT(@admissionActionIds, ','))
END