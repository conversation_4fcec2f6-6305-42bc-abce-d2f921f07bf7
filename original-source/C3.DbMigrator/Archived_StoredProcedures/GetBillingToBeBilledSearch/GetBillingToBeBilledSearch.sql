﻿CREATE PROCEDURE [dbo].[GetBillingToBeBilledSearch] 
	@isBillingAdmin int,
	@practiceId int,
	@practiceDoctorIds varchar(256),
	@billingType int,
	@rowStart int,
	@rowCount int,
	@totalRowRequest int,
	@sortByColumn varchar(32),
	@sortByOrder varchar(8),
	@columnPatient int,
	@filterArrived int,
	@filterNotArrived int,
	@filterPaymentMethods varchar(256),
	@filterNotFinished int,
	@filterBilled int,
	@filterNotBilled int,
	@filterPatientName varchar(32),
	@filterAppointmentStart varchar(16),
	@filterAppointmentEnd varchar(16),
	@filterFamilyDoctorName varchar(32),
	@filterReferralDoctorName varchar(32),
	@filterPracticeDoctorIds varchar(256),
	@filterOfficeIds varchar(256)
AS
BEGIN
	SET NOCOUNT ON;
	
	DECLARE @AppointmentStatus_WaitList int = 1
	DECLARE @AppointmentStatus_Triage int = 16
	DECLARE @AppointmentStatus_Cancelled int = 7
	DECLARE @billStatusId_NotBilled int = 1
	DECLARE @testId_VP int = 29

	DECLARE @paymentMethod_OHIP int = 0
	DECLARE @paymentMethod_Cash int = 1
	DECLARE @paymentMethod_WCB int = 2
	DECLARE @paymentMethod_RMB int = 3
	DECLARE @paymentMethod_BlueCross int = 4
	DECLARE @paymentMethod_OtherPayors int = 5
	DECLARE @paymentMethod_CANNAMM int = 6
	DECLARE @paymentMethod_test int = 7
	
	DECLARE @sql nvarchar(max)
	DECLARE @sqlCounter nvarchar(max)
	DECLARE @selectSql nvarchar(4000)
	DECLARE @tableSql nvarchar(4000)
	DECLARE @whereSql nvarchar(4000)
	DECLARE @orderBySql nvarchar(64)
	DECLARE @offsetFetchSql nvarchar(64)
	DECLARE @orderByColumn nvarchar(64)
	DECLARE @params nvarchar(500)
	DECLARE @Vtables nvarchar(2000)
	DECLARE @BilledSql nvarchar(1000)
	DECLARE @isUnbilled smallint;
	DECLARE @AppStartDt datetime;
	DECLARE @AppEndDt datetime;

	SET @Vtables = '';

	IF @filterAppointmentStart is not null and ltrim(rtrim(@filterAppointmentStart)) <> '' SET @AppStartDt = convert(datetime, @filterAppointmentStart+' 00:00:00', 120);
	IF @filterAppointmentEnd is not null and ltrim(rtrim(@filterAppointmentEnd)) <> '' SET @AppEndDt = convert(datetime, @filterAppointmentEnd+' 23:59:59', 120);

	IF @billingType = 1
	BEGIN

		SET @BilledSql = N'	CASE WHEN (aa.billStatusId IS NULL OR aa.billStatusId = @billStatusId_NotBilled ) THEN 1
								 WHEN EXISTS (SELECT top 1 1 FROM AppointmentTests h1 WHERE h1.appointmentId = aa.appointmentId AND h1.IsActive = 1
						                         AND CASE WHEN ( h1.billStatusId IS NULL OR h1.billStatusId = @billStatusId_NotBilled ) AND h1.TestId <> @testId_VP THEN 1 
														  WHEN h1.TestId = @testId_VP AND EXISTS (	SELECT top 1 1 FROM AppointmentBills n1 WHERE n1.appointmentId = aa.appointmentId AND (n1.billStatusId IS NULL OR n1.billStatusId = @billStatusId_NotBilled)) THEN 1
													 ELSE 0 END = 1 ) THEN 1 ELSE 0 END';

		SET @selectSql = N'select a.Id appointmentId, a.appointmentTime, 0 admissionId, null admissionDate, a.ArrivedTime,
								a.PracticeDoctorId, (COALESCE(NULLIF(c.lastName, ''''), ''''))+'', ''+(COALESCE(NULLIF(c.firstName, ''''), '''')) doctorName, 
								(select top 1 (COALESCE(NULLIF(lastName, ''''), ''''))+'', ''+(COALESCE(NULLIF(firstName, ''''), '''')) from ExternalDoctors where Id = a.referralDoctorId) referralDoctorName,
								a.PatientRecordId, (COALESCE(NULLIF(e.lastName, ''''), ''''))+'', ''+(COALESCE(NULLIF(e.firstName, ''''), '''')) patientName, 
								a.OfficeId, d.name office, a.billStatusId, (CASE WHEN EXISTS (SELECT Id FROM BillDetails g WHERE g.appointmentId = a.ID) THEN 1 ELSE 0 END) hasClaim';

		SET @tableSql = N' from Appointments a inner join PracticeDoctors b on a.PracticeDoctorId=b.Id 
										inner join ExternalDoctors c on b.ExternalDoctorId=c.Id
										inner join Office d on a.OfficeId=d.Id
										inner join Demographics e on a.PatientRecordId=e.PatientRecordId'

        SET @whereSql = N' where a.appointmentStatus > @AppointmentStatus_WaitList and a.appointmentStatus <> @AppointmentStatus_Triage and a.appointmentStatus <> @AppointmentStatus_Cancelled 
												and a.IsActive = 1 and b.PracticeId = @practiceId';
		IF (@isBillingAdmin <> 1)
		BEGIN
			SET @Vtables = @Vtables + formatmessage(N'DECLARE @PracDocs TABLE (PracticeDoctorId int not null, primary key(PracticeDoctorId)); INSERT INTO @PracDocs(PracticeDoctorId) SELECT distinct value FROM STRING_SPLIT(''%s'', '',''); ', @practiceDoctorIds);
			SET @whereSql = @whereSql + N' and a.PracticeDoctorId in (SELECT PracticeDoctorId FROM @PracDocs) '
		END;

		IF (@filterArrived = @filterNotArrived)
		BEGIN
			IF (@filterArrived = 0)
				SET @whereSql = N' where 1 = 0'
		END
		ELSE
		BEGIN
			IF (@filterArrived = 1)
				SET @whereSql = @whereSql + N' and (not (a.ArrivedTime is null or ltrim(rtrim(a.ArrivedTime)) = ''''))'
			ELSE
				SET @whereSql = @whereSql + N' and (a.ArrivedTime is null or ltrim(rtrim(a.ArrivedTime)) = '''')'
		END

		IF (@filterPaymentMethods is not null and @filterPaymentMethods <> '')
		BEGIN
			SET @Vtables = @Vtables + formatmessage(N'DECLARE @PaymentMethod TABLE (paymentMethod int not null, primary key (paymentMethod)); INSERT INTO @PaymentMethod(paymentMethod) SELECT distinct value FROM STRING_SPLIT(''%s'', '',''); ', @filterPaymentMethods);
			SET @whereSql = @whereSql + N' and a.appointmentPaymentMethod in (SELECT paymentMethod FROM @PaymentMethod) ';
		END
		ELSE
			SET @whereSql = N' where 1 = 0'

		IF @AppStartDt is not null
			SET @whereSql = @whereSql + N' and a.appointmentTime >= @AppStartDt';

		IF @AppEndDt is not null
			SET @whereSql = @whereSql + N' and a.appointmentTime <= @AppEndDt';
	END
	ELSE
	BEGIN
		SET @BilledSql = N'	CASE WHEN aa.billStatusId = @billStatusId_NotBilled THEN 1 
								  WHEN (EXISTS (SELECT top 1 1 FROM HDAdmissionActions m1 inner join HDServices m2 on m1.Id=m2.AdmissionActionId inner join HDServiceCodes m3 on m2.Id=m3.HDServiceId WHERE m1.AdmissionId=aa.AdmissionId and m1.IsActive=1 and m2.isActive=1 and m3.isActive=1 and m3.BillStatusId = @billStatusId_NotBilled)) THEN 1 ELSE 0 END '

		SET @selectSql = N'select 0 appointmentId, null appointmentTime, a.Id admissionId, a.DateAdmitted admissionDate, null ArrivedTime,
												a.PracticeDoctorId, (COALESCE(NULLIF(c.lastName, ''''), ''''))+'', ''+(COALESCE(NULLIF(c.firstName, ''''), '''')) doctorName, 
												(select top 1 (COALESCE(NULLIF(lastName, ''''), ''''))+'', ''+(COALESCE(NULLIF(firstName, ''''), '''')) from ExternalDoctors where Id = a.referralDoctorId) referralDoctorName,
												a.PatientRecordId, (COALESCE(NULLIF(e.lastName, ''''), ''''))+'', ''+(COALESCE(NULLIF(e.firstName, ''''), '''')) patientName, 
												0 OfficeId, '''' office, a.billStatusId, (CASE WHEN EXISTS (SELECT Id FROM BillDetails g WHERE g.hdAdmissionId = a.ID) THEN 1 ELSE 0 END) hasClaim';

		SET @tableSql = N' from HDAdmissions a inner join PracticeDoctors b on a.PracticeDoctorId=b.Id 
										inner join ExternalDoctors c on b.ExternalDoctorId=c.Id
										inner join Demographics e on a.PatientRecordId=e.PatientRecordId'

        SET @whereSql = N' where a.IsActive = 1 and b.PracticeId = @practiceId'

		IF (@isBillingAdmin <> 1)
		BEGIN
			SET @Vtables = @Vtables + formatmessage(N'DECLARE @PracDocs TABLE (PracticeDoctorId int not null, primary key(PracticeDoctorId) ); INSERT INTO @PracDocs(PracticeDoctorId) SELECT distinct value FROM STRING_SPLIT(''%s'', '',''); ', @practiceDoctorIds);
			SET @whereSql = @whereSql + N' and a.PracticeDoctorId in (SELECT PracticeDoctorId FROM @PracDocs) '
		END;

		IF (@filterPaymentMethods is not null and @filterPaymentMethods <> '')
		BEGIN
			/* If OHIP payment method ID is selected, for performance reasons, do not filter the data by payment method as nearly all payments are OHIP  */
			if ( SELECT 1 from ( SELECT distinct value PaymentMethod FROM STRING_SPLIT(@filterPaymentMethods, ',') ) p where p.PaymentMethod = 0 ) <> 1
			begin
				SET @Vtables = @Vtables + formatmessage(N'DECLARE @PaymentMethod TABLE (paymentMethod int not null, primary key(paymentMethod)); INSERT INTO @PaymentMethod(paymentMethod) SELECT distinct value FROM STRING_SPLIT(''%s'', '',''); ', @filterPaymentMethods);
				SET @whereSql = @whereSql + N' and EXISTS (select n1.Id from HDAdmissionActions n1 join HDServices n2 on n1.Id=n2.AdmissionActionId where n1.IsActive=1 and n2.IsActive=1 and n1.AdmissionId=a.Id and n2.PaymentMethod in (SELECT paymentMethod FROM @PaymentMethod)) ';
			end;
		END
		ELSE
			SET @whereSql = N' where 1 = 0'

		IF @AppStartDt is not null
			SET @whereSql = @whereSql + N' and a.DateAdmitted >= @AppStartDt';

		IF @AppEndDt is not null
			SET @whereSql = @whereSql + N' and a.DateAdmitted <= @AppEndDt';

	END

	SET @tableSql = N' from (' + @selectSql + @tableSql + @whereSql+ ') aa'
	SET @selectSql = N'select appointmentId,appointmentTime,admissionId,admissionDate,PracticeDoctorId,doctorName,referralDoctorName,PatientRecordId,patientName,OfficeId,office,hasClaim,'+@BilledSql+N' as isUnbilled'
	SET @whereSql = N' where'

	IF (@filterBilled = @filterNotBilled)
	BEGIN
		IF (@filterBilled = 0)
			SET @whereSql = @whereSql + N' and 1 = 0'
		ELSE
			SET @whereSql = @whereSql + N' and 1 = 1'
	END
	ELSE
	BEGIN
		SET @whereSql = @whereSql + N' and ' + @BilledSql + N' = @isUnbilled';
		IF (@filterBilled = 1)
			SET @isUnbilled = 0;
		ELSE
			SET @isUnbilled = 1;
	END
	
	IF (@filterPatientName is not null and @filterPatientName <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(N' and patientName like ''%%%s%%''',@filterPatientName)

	IF (@filterFamilyDoctorName is not null and @filterFamilyDoctorName <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(N' and doctorName like ''%%%s%%''',@filterFamilyDoctorName)

	IF (@filterReferralDoctorName is not null and @filterReferralDoctorName <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(N' and referralDoctorName like ''%%%s%%''',@filterReferralDoctorName)

	IF (@filterPracticeDoctorIds is not null and @filterPracticeDoctorIds <> '')
	BEGIN
		SET @Vtables = @Vtables + formatmessage(N'DECLARE @filterPracDocs TABLE (PracticeDoctorId int not null); INSERT INTO @filterPracDocs(PracticeDoctorId) SELECT value FROM STRING_SPLIT(''%s'', '',''); ', @filterPracticeDoctorIds);
		SET @whereSql = @whereSql + N' and PracticeDoctorId in (SELECT PracticeDoctorId FROM @filterPracDocs) '
	END

	IF (@filterOfficeIds is not null and @filterOfficeIds <> '')
	BEGIN
		SET @Vtables = @Vtables + formatmessage(N'DECLARE @Offices TABLE (officeId int not null); INSERT INTO @Offices(officeId) SELECT value FROM STRING_SPLIT(''%s'', '',''); ', @filterOfficeIds);
		SET @whereSql = @whereSql + N' and OfficeId in (SELECT officeId FROM @Offices) '
	END

	SET @whereSql = replace(@whereSql, N' where and ', ' where ')

	SET @orderByColumn = (CASE WHEN @sortByColumn='appointmenttime' THEN 'appointmentTime'
								WHEN @sortByColumn='doctorname' THEN 'doctorname'
								WHEN @sortByColumn='patientname' THEN 'patientname'
								WHEN @sortByColumn='office' THEN 'office'
								WHEN @sortByColumn='hasclaim' THEN 'hasclaim'
								WHEN @sortByColumn='admissiondate' THEN 'admissiondate'
								ELSE 'otherColumnotherColumn' END)
	IF (CHARINDEX(@orderByColumn, @selectSql) <= 0)
	BEGIN
		IF (@billingType = 1)
			SET @orderByColumn = 'appointmentTime'
		ELSE
			SET @orderByColumn = 'admissiondate'
	END

	SET @orderBySql = ' order by ' + @orderByColumn + ' ' + (CASE WHEN @sortByOrder='asc' THEN ' asc' ELSE ' desc' END)
	SET @offsetFetchSql = N' offset @rowStart rows fetch next @rowCount rows only';

	SET @params = N'@billStatusId_NotBilled int, @testId_VP int, @AppointmentStatus_WaitList int, @AppointmentStatus_Triage int, @AppointmentStatus_Cancelled int, @practiceId int, @isUnbilled smallint, @AppStartDt datetime, @AppEndDt datetime, @rowStart int, @rowCount int';
		
	SET @sql = @VTables+' select appointmentId,(case when appointmentTime is null then '''' else convert(varchar, appointmentTime, 101) end) appointmentTime,
									admissionId,(case when admissionDate is null then '''' else convert(varchar, admissionDate, 101) end) admissionDate,
									PracticeDoctorId,doctorName,PatientRecordId,patientName,OfficeId,office,(cast(hasClaim as bit)) hasClaim,  (cast(isUnbilled as bit)) isUnbilled
							from (' + @selectSql + @tableSql + @whereSql + @orderBySql + @offsetFetchSql + ') tobebilled';

--	select @sql
	EXEC sp_executesql @sql, @params, @billStatusId_NotBilled = @billStatusId_NotBilled, @testId_VP = @testId_VP, @AppointmentStatus_WaitList = @AppointmentStatus_WaitList, 
		    @AppointmentStatus_Triage = @AppointmentStatus_Triage, @AppointmentStatus_Cancelled = @AppointmentStatus_Cancelled, @practiceId = @practiceId, @isUnbilled = @isUnbilled,
			@AppStartDt = @AppStartDt, @AppEndDt = @AppEndDt, @rowStart = @rowStart, @rowCount = @rowCount;
END
GO