﻿CREATE PROCEDURE [dbo].[GetDoctorRootCategories]
	@groupId INT,
	@externalDoctorId INT,
	@practiceId INT,
	@practiceTemplateId INT = 0,
	@rootCategoryId INT = 0	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here	
	SELECT	   
       drc.CategoryNameOriginal
      ,drc.CategoryNameCustom
      ,drc.Accumulative
      ,drc.ExternalDoctorId
      ,drc.RootCategoryId
	  ,drc.GroupId
      ,drc.PracticeId	  
	  ,drc.PracRootCategoryTempId
	  ,drc.TemplateId
	  ,drc.TemplateName
      ,drc.IsVisible
	  ,drc.IsVisibleLabel
      ,drc.IsVisibleInLetter
      ,drc.IsVisibleToolbar	  
	  ,drc.DoctorRootCategoryId 
	  ,drc.DisplayOrder 
	FROM dbo.fn_GetDoctorRootCategories(@groupId,@externalDoctorId,@practiceId,@practiceTemplateId,@rootCategoryId) drc	
	ORDER BY drc.DisplayOrder
END