﻿
CREATE PROCEDURE [dbo].[GetPreviousAppointments] 
	@appointmentIds AS dbo.IntegerList READONLY
AS
BEGIN
	
	SET NOCOUNT ON;

	DECLARE @excludeAppStatuses TABLE
	   ( 
         appointmentStatus int 
	   );
	INSERT INTO @excludeAppStatuses VALUES (7); -- cancelled
	INSERT INTO @excludeAppStatuses VALUES (1); -- waitlist	

	SELECT apps.PatientRecordId AS PatientId,
	 appIds.IntegerValue AS AppointmentId,
	(SELECT TOP 1 apps2.Id FROM Appointments apps2 WHERE apps2.appointmentTime < apps.appointmentTime AND apps2.PatientRecordId = apps.PatientRecordId AND apps2.appointmentStatus NOT IN (SELECT appointmentStatus FROM @excludeAppStatuses) ORDER BY apps2.appointmentTime DESC) AS PreviousAppointmentId
	FROM Appointments apps 
	JOIN @appointmentIds appIds ON apps.Id = appIds.IntegerValue		
	
END
