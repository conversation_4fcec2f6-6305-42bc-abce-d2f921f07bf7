﻿CREATE   PROCEDURE [dbo].[GetEconsultMetadata]
	@PracticeId INT = NULL,
	@PatientLastName NVARCHAR (100) = NULL,
	@RequesterName NVARCHAR (100) = NULL,
	@RecipientName NVARCHAR (100) = NULL,
	@ConsultState NVARCHAR (100) = NULL,
	@DateSubmitted DATETIME2 = NULL,
	@ConsultFlag NVARCHAR (100) = NULL,
	@PatientNeedsToBeSeen INT = 0,--0 - all, 1 - PatientNeedsToBeSeen = true, 2 - PatientNeedsToBeSeen = false 
	@SavedCompletedFlag INT = 0,-- = 0 - all, 1 - SavedCompletedFlag = true, 2 - SavedCompletedFlag = false
	@PatientAssociationFlag INT = 0-- 0 - all, 1 - PatientAssociationFlag = true, 2 - PatientAssociationFlag = false
AS
BEGIN

-- SET NOCOUNT ON added to prevent extra result sets from
-- interfering with SELECT statements.
	SET NOCOUNT ON;	

	DECLARE @query NVARCHAR(4000);
	DECLARE @params NVARCHAR(1000);

	DECLARE @sDate DATETIME;
	DECLARE @eDate DATETIME;

	SET @query = N'	SELECT 
					M.Id,
					M.PracticeId,
					M.CaseId,
					M.DateSaved,
					M.DateLastUpdated,
					M.DateSubmitted,
					M.RequesterName,
					M.RecipientName,
					M.RequesterPractitionerId,
					M.IssuerName,
					M.IssuerGroup,
					M.CoverageId,
					M.Title,
					M.Description,
					M.EmrInitialRequest,
					M.UseId,
					M.RespondentSpecialty,
					M.RespondentSubSpecialty,
					M.RespondentGroupOrOrganizationName,
					M.PatientFamily,
					M.PatientGiven,
					M.PatientGender,
					M.PatientBirthDate,
					M.Patient_HCN,
					M.Patient_HCN_VC,
					M.AuthorName,
					M.PatientNeedsToBeSeen,
					M.ConsultState,
					M.SavedCompletedFlag,
					ISNULL(M.ConsultFlag,'''+'NOFLAG'+''') AS ConsultFlag,
					A.PatientId,
					CAST(IIF(A.PatientId IS NOT NULL,1,0) AS BIT) AS PatientAssociationFlag
					FROM EconsultMetadata M
					LEFT JOIN EconsultPatientAssociations A ON M.CaseId = A.CaseId
					WHERE 1=1 ';
	
	IF @PracticeId IS NOT NULL
		SET @query = @query + N' AND M.PracticeId = @PracticeId ';	

	IF @PatientLastName IS NOT NULL AND LTRIM(RTRIM(@PatientLastName)) <> ''
		SET @query = @query + N' AND M.PatientFamily LIKE ''%'+ @PatientLastName +'%'' ';	

	IF @RequesterName IS NOT NULL AND LTRIM(RTRIM(@RequesterName)) <> ''
		SET @query = @query + N' AND M.RequesterName LIKE ''%'+ @RequesterName +'%'' ';	

	IF @RecipientName IS NOT NULL AND LTRIM(RTRIM(@RecipientName)) <> ''
		SET @query = @query + N' AND M.RecipientName LIKE ''%'+ @RecipientName +'%'' ';	

	IF @ConsultState IS NOT NULL AND LTRIM(RTRIM(@ConsultState)) <> ''
		SET @query = @query + N' AND M.ConsultState LIKE ''%'+ @ConsultState +'%'' ';	

	IF @ConsultFlag IS NOT NULL AND LTRIM(RTRIM(@ConsultFlag)) <> ''
		SET @query = @query + N' AND M.ConsultFlag LIKE ''%'+ @ConsultFlag +'%'' ';	

	IF @PatientNeedsToBeSeen = 1
		SET @query = @query + N' AND M.PatientNeedsToBeSeen = 1 ';	
	ELSE IF @PatientNeedsToBeSeen = 2
		SET @query = @query + N' AND M.PatientNeedsToBeSeen = 0 ';	

	IF @SavedCompletedFlag = 1
		SET @query = @query + N' AND M.SavedCompletedFlag = 1 ';	
	ELSE IF @SavedCompletedFlag = 2
		SET @query = @query + N' AND M.SavedCompletedFlag = 0 ';	

	IF @PatientAssociationFlag = 1
		SET @query = @query + N' AND A.PatientId IS NOT NULL ';	
	ELSE IF @PatientAssociationFlag = 2
		SET @query = @query + N' AND A.PatientId IS NULL ';	

	IF @DateSubmitted IS NOT NULL
	BEGIN		
		SET @sDate = CAST(CONVERT(VARCHAR, DATEADD(MONTH,-3,@DateSubmitted), 112)+' 00:00:00' AS DATETIME);		
		SET @eDate = CAST(CONVERT(VARCHAR, @DateSubmitted, 112)+' 23:59:59' AS DATETIME);		
		SET @query = @query + N' AND M.DateSubmitted BETWEEN @sDate and @eDate';	
	END			

	--PRINT @query

	SET @params = N'@PracticeId INT,@sDate DATETIME, @eDate DATETIME'

	EXEC sp_executesql @query,@params,@PracticeId = @PracticeId,@sDate = @sDate,@eDate = @eDate

END