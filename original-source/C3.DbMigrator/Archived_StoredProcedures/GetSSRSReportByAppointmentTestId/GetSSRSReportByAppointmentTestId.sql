﻿
CREATE PROCEDURE [dbo].[GetSSRSReportByAppointmentTestId]
	@appointmentTestId INT,
	@appointmentTestLogId INT = 0 -- when test is vp we use AppointmentTestSaveLogs and when its worksheet we use AppointmentTestLogs
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	
	DECLARE @patientId INT;		
	DECLARE @appointmentId INT;	
	DECLARE @testId INT;
	DECLARE @urlTypeIdOffice INT = 5; -- office download url type
	DECLARE @urlTypeIdOfficeUpload INT = 6; -- office upload url type
	DECLARE @urlTypeIdImage INT = 3; -- office download url type
	DECLARE @vpTestId INT = 29; 
	DECLARE @completedTestStatusId INT = 11; 
	DECLARE @beingSentTestStatusId INT = 18;
	DECLARE @isAmendedVP BIT = 0;	
	DECLARE @isAmendedWS BIT = 0;
	DECLARE @clinicSendType INT = 6;	
	DE<PERSON>AR<PERSON> @saveType INT = 3 -- send letter for vp
	DECLARE @completedlogId INT = 0 -- send letter for vp
	DECLARE @firstReportQueueId INT = 0 -- for the first queue item for this test
	DECLARE @reportQueueChangeStatus BIT = 0; -- for report queue preliminary report
	DECLARE @reportQueueId INT = 0; -- for the current queue item for this test

	DECLARE @appTest TABLE
	(	
	AppointmentId int NOT NULL,
	AppointmentTime DATETIME2 NOT NULL,	
	PatientId int NOT NULL,
	PracticeId int NOT NULL,
	PracticeTestReportId int NOT NULL,-- test group report id for practicetestgroups
	AttachRawData bit NOT NULL, --per practice
	RawDataPages int NOT NULL, -- per practice
	GenericReportId int NOT NULL, -- per practice
	GenericHeaderHeight int NOT NULL, -- per practice
	OfficeId int NOT NULL,	
	OfficeName nvarchar(100) NULL, 
    OfficeBusinessName nvarchar(500) NULL, 
	OfficeAddress1 nvarchar(500) NULL,
	OfficeAddress2 nvarchar(500) NULL, 
    OfficeCity nvarchar(500) NULL, 
    OfficePhone nvarchar(500) NULL, 
	OfficeFax nvarchar(100) NULL, 
	OfficeCountry nvarchar(100) NULL, 
	OfficeZip nvarchar(100) NULL, 
	OfficeState nvarchar(100) NULL, 
	OfficeUrl nvarchar(500) NULL,  
	OfficeProvince int NULL, 
    OfficePostalCode nvarchar(100) NULL,  
	OfficeStatus int NULL,  
	OfficeHRMId nvarchar(100) NULL, 
	OfficeDownloadUrl nvarchar(500) NULL,
	OfficeUploadUrl nvarchar(500) NULL,
	ImageDownloadUrl nvarchar(500) NULL,
	AppointmentTestId int NOT NULL,
	TestTime DATETIME2 NOT NULL,	
	TestId int NOT NULL,
	TestShortName nvarchar(500) NULL,
	TestLongName nvarchar(500) NULL,
	TestAccessionNumber VARCHAR(100),
	TestHrmTypeLong VARCHAR(100),
	TestHrmTypeShort VARCHAR(100),
	TestHRMModality nvarchar(100) NULL,
	TestModalities nvarchar(500) NULL,
	TestGroupId int NOT NULL,
	TestGroupName nvarchar(100) NULL,
	TestReportId int NOT NULL,-- test group report id
	IsVP bit NOT NULL,
	IsAmended bit NOT NULL,
	LoincCode nvarchar(30) NULL,
	HRM_UPI nvarchar(30) NULL,
	ReportQueueChangeStatus BIT
	)

	SELECT TOP 1 @patientId = a.PatientRecordId, @appointmentId = a.Id, @testId = t.TestId 
	FROM AppointmentTests t 
	JOIN Appointments a ON t.AppointmentId = a.Id 
	WHERE t.Id = @appointmentTestId;	

	-- VP - we need to check if its amended
	IF @testId = @vpTestId
	BEGIN
		SELECT TOP 1 @completedlogId = ISNULL(sl.Id,0) FROM AppointmentTestSaveLogs sl WHERE sl.AppointmentTestId = @appointmentTestId AND sl.SaveType = @saveType ORDER BY sl.LogDate ASC;
		IF @completedlogId > 0 
		BEGIN
			IF @appointmentTestLogId > 0 
			BEGIN
				IF @completedlogId != @appointmentTestLogId
				BEGIN
					SET @isAmendedVP = 1;
				END
			END
			ELSE
			BEGIN
				SET @isAmendedVP = 1;
			END		
		END		
	END
	ELSE -- when its WS
	BEGIN
		-- check if any was in the report queue
		SET @firstReportQueueId = ISNULL((SELECT TOP 1 rq.Id FROM ReportQueues rq WHERE rq.AppointmentTestId = @appointmentTestId AND rq.ChangeStatus=1 ORDER BY rq.DateCreated ASC),0);

		IF @appointmentTestLogId <=0
		BEGIN
			SET @appointmentTestLogId = (SELECT TOP 1 l.Id FROM AppointmentTestLogs l JOIN AppointmentTests t ON l.AppointmentId = t.AppointmentId WHERE t.Id = @appointmentTestId AND l.[Status] = 0 ORDER BY l.Date DESC)
		END  
		
		SELECT TOP 1 @reportQueueId = ISNULL(rq.Id,0), @reportQueueChangeStatus = ISNULL(rq.ChangeStatus,0) FROM ReportQueues rq WHERE rq.AppointmentTestId = @appointmentTestId AND rq.AppointmentTestLogId = @appointmentTestLogId;
		
		print @reportQueueId
		-- check amended
		IF @reportQueueChangeStatus = 1 
		BEGIN
			IF @reportQueueId > @firstReportQueueId
			BEGIN
				SET @isAmendedWS = 1;
			END  
		END
	   
	END

	INSERT INTO @appTest
	SELECT 
	 app.Id
	,app.appointmentTime
	,app.PatientRecordId
	,o.PracticeId
	,ISNULL(ptg.SSRSReportId,0)
	,ISNULL(ptg.attachRawData, CAST(0 AS BIT))
	,ISNULL(ptg.RawDataPages,0)
	,ISNULL(ptg.GenericReportId,0)
	,ISNULL(ptg.GenericHeaderHeight,0)
	,o.Id
	,o.[name]
	,o.businessName
	,o.address1
	,o.address2
	,o.city	
	,o.phone
	,o.fax
	,o.country
	,o.zip
	,o.[state]
	,o.[url]
	,ISNULL(o.province,0)
	,o.postalCode
	,ISNULL(o.[status],0)
	,o.HRM_id
	,ISNULL((SELECT TOP 1 [url] FROM OfficeUrls WHERE officeId = o.Id AND urlTypeId = @urlTypeIdOffice),'')
	,ISNULL((SELECT TOP 1 [url] FROM OfficeUrls WHERE officeId = o.Id AND urlTypeId = @urlTypeIdOfficeUpload),'')
	,ISNULL((SELECT TOP 1 [url] FROM OfficeUrls WHERE officeId = o.Id AND urlTypeId = @urlTypeIdImage),'')
	,appTest.Id
	,appTest.startTime
	,appTest.TestId
	,t.testShortName
	,t.testFullName
	,appTest.AccessionNumber
	,t.HrmTypeLong
	,t.HrmTypeShort
	,t.HrmModality
	,(SELECT STUFF(  
		    (
			SELECT ', '+ CAST(m.modalityName AS VARCHAR(500)) 
			FROM TestModalities tm
			JOIN Modalities m ON tm.ModalityId = m.Id
			WHERE tm.TestId = appTest.TestId
		ORDER BY  m.modalityName
		FOR XMl PATH('') 
            ),1,1,''  )) As TestModalities
	,g.Id
	,g.[Name]
	,g.SSRSReportId
	,CASE WHEN appTest.TestId = @vpTestId THEN 1 ELSE 0 END AS BIT 
	,CAST(
			CASE 
			WHEN appTest.TestId = @vpTestId THEN @isAmendedVP			
			ELSE @isAmendedWS END AS BIT) AS IsAmended,
	t.LoincCode,
	o.HRM_UPI,
	@reportQueueChangeStatus AS ReportQueueChangeStatus
	FROM AppointmentTests appTest
	JOIN Appointments app ON appTest.AppointmentId = app.Id
	JOIN Office o ON app.OfficeId = o.Id	
	JOIN Tests t ON appTest.TestId = t.Id
	JOIN TestGroups tg ON appTest.TestId = tg.TestId
	JOIN Groups g ON g.Id = tg.GroupId	
	LEFT JOIN PracticeTestGroups ptg ON g.Id = ptg.GroupId	AND o.PracticeId = ptg.PracticeId
	WHERE appTest.Id = @appointmentTestId;	

	SELECT 
	apt.AppointmentId
	,apt.AppointmentTime
	,apt.PatientId
	,apt.PracticeId	
	,apt.AttachRawData
	,apt.RawDataPages
	,apt.GenericReportId
	,apt.GenericHeaderHeight
	,apt.OfficeId
	,apt.OfficeName
    ,apt.OfficeBusinessName 
	,apt.OfficeAddress1
	,apt.OfficeAddress2
    ,apt. OfficeCity
    ,apt.OfficePhone
	,apt.OfficeFax
	,apt.OfficeCountry
	,apt.OfficeZip
	,apt.OfficeState
	,apt.OfficeUrl
	,apt.OfficeProvince 
    ,apt.OfficePostalCode 
	,apt.OfficeStatus  
	,apt.OfficeHRMId
	,apt.OfficeDownloadUrl
	,apt.OfficeUploadUrl
	,apt.ImageDownloadUrl
	,apt.AppointmentTestId
	,apt.TestTime
	,apt.TestId
	,apt.TestShortName
	,apt.TestLongName
	,apt.TestAccessionNumber
	,apt.TestHrmTypeLong
	,apt.TestHrmTypeShort
	,apt.TestHRMModality
	,apt.TestModalities
	,apt.TestGroupId
	,apt.TestGroupName
	,apt.IsVP
	,apt.IsAmended
	,demo.DemographicId
	,demo.FirstName
	,demo.LastName
	,demo.MiddleName 
    ,demo.DateOfBirth
	,demo.AgeAccurate
	,demo.Gender	 
	,demo.HealthCard
    ,demo.HealthCardCode
	,demo.AddressLine1
	,demo.AddressLine2
	,demo.City
	,demo.PostalCode
	,demo.Province
	,demo.Country
	,demo.PhoneNumbers
	,sr.Id AS SSRSReportId
	,sr.ReportName
	,sr.ReportSSRSName
	,sr. ReportDescription
	,sr.ReportPath
	,sr.IsActive
	,sr.DateCreated
	,sr.DateLastModified
	,apt.LoincCode
	,apt.HRM_UPI
	,apt.ReportQueueChangeStatus
	FROM @appTest apt
	JOIN SSRSReports sr ON sr.Id = CASE WHEN apt.PracticeTestReportId > 0 THEN apt.PracticeTestReportId ELSE apt.TestReportId END
	JOIN dbo.fn_GetPatientInfo(@patientId,null) demo ON apt.PatientId = demo.PatientId		
		
END