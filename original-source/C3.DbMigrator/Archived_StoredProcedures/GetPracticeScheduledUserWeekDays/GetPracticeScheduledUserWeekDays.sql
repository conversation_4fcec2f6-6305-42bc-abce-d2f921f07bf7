﻿CREATE PROCEDURE [dbo].[GetPracticeScheduledUserWeekDays] 
	@practiceId INT,
	@officeId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	
	SELECT 
	u.UserId,
	S.Id AS ScheduleWeekDayId,
	S.officeId AS OfficeId,
	S.startTime AS StartTime,
	S.finishTime AS FinishTime,
	S.reservedTime AS ReservedTime,
	S.absentTime AS AbsentTime,
	S.date AS Date,
	S.dayOfWeek AS DayOfWeek,
	S.dayOff,
	S.comment AS Comment
	From ScheduleWeekDays S
	JOIN ScheduleUsers U ON S.ScheduleUserId=U.Id
	WHERE S.officeId=@officeId
		--AND (U.endDate IS NULL OR U.endDate>GETDATE())
END




