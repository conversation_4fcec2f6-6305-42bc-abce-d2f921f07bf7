﻿
CREATE PROCEDURE [dbo].[GetUserMenuCount]
	@userId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	DECLARE @cmCount INT = 0; -- contact manager count
	DECLARE @exDocumentCount INT = 0; -- external documents count	
	DECLARE @triageCount INT = 0; -- triage count
	DECLARE @wlCount INT = 0; -- worklist count
	DECLARE @TotalToDO INT = 0; -- total count
	DECLARE @practiceDoctorId INT = 0; -- total count
	DECLARE @practiceId INT = 0; -- total count
	DECLARE @appUserId NVARCHAR(500); -- GUID application userId
	DECLARE @forTriageStatusId INT = 0;
	DECLARE @triagedStatusId INT = 0;	
	DECLARE @doctorUserType INT =  5;	
	DECLARE @userType INT = 0;

	SELECT TOP 1 @appUserId = Id, @practiceId = PracticeID, @userType = CerebrumUserType  FROM AspNetUsers WHERE UserID = @userId;		
	SET @practiceDoctorId = ISNULL((SELECT TOP 1 Id FROM PracticeDoctors WHERE ApplicationUserId = @appUserId AND ApplicationUserId IS NOT NULL),0);
	SET @forTriageStatusId = ISNULL((SELECT TOP 1 Id FROM TriageStatus WHERE [Description] = 'For Triage'),0);
	SET @triagedStatusId = ISNULL((SELECT TOP 1 Id FROM TriageStatus WHERE [Description] = 'Triaged'),0);

	SET @cmCount =	Isnull((SELECT count(*)
 							  FROM CM_TaskDefinition ts
							 WHERE exists ( select 1
											  from CM_TaskMessage tm with (index([PK_dbo.CM_TaskMessage]))
											  join CM_TaskMessageRecipient tr ON tm.id = tr.CM_TaskMessageId
											 where tr.userid = @appUserId
											 and   tr.seen = 0 
											 and   tr.hideFromUnseen = 0
											 and   ts.id = tm.CM_TaskDefinitionId)
							 and   ts.taskStatus = 0),0);

	IF @practiceDoctorId > 0 AND @userType = @doctorUserType
	BEGIN
		DECLARE @faxHrmCount INT = 0;
		DECLARE @hl7Count INT = 0;

		-- for worklist, no datatransfer appointment types
		SET @wlCount = Isnull((SELECT COUNT(t.Id) FROM AppointmentTests t with (nolock) 
						JOIN Appointments a with (nolock) ON a.Id = t.AppointmentId
						WHERE a.appointmentStatus NOT IN (0,1,7) -- cancellation list, waitlist and cancelled /* AND a.AppointmentTypeId != 55 -- Redmine Ticket # 4969 */
						AND a.PracticeDoctorId = @practiceDoctorId 
						AND (t.ReassignDocId = @practiceDoctorId OR t.ReassignDocID = 0)
						AND t.AppointmentTestStatusId = 9 -- ready for doctor					 
					--	AND a.OfficeId IN (SELECT Id FROM Office WHERE PracticeId = @practiceId) -- Commented by Cristian - ticket #12209
						AND a.IsActive = 1
						AND t.IsActive = 1),0)


		SET @triageCount = Isnull((SELECT COUNT(a.Id) FROM Appointments a with (nolock) 
								WHERE a.IsActive = 1
								AND a.PracticeDoctorId = @practiceDoctorId
								AND a.TriageStatusId = @forTriageStatusId 
								AND a.appointmentStatus = 16 -- traige status							
								--AND a.OfficeId IN (SELECT Id FROM Office WHERE PracticeId = @practiceId) -- Commented by Cristian - ticket #12209
								),0);

		SET @faxHrmCount = Isnull((SELECT COUNT(rr.Id) FROM ReportReceiveds rr 
									JOIN DoctorsReportRevieweds rv ON rr.Id = rv.ReportReceivedId
									WHERE rv.practiceDoctorId = @practiceDoctorId AND rv.dateTimeReportReviewed IS NULL
									AND rr.assignmentStatus != 2 -- ignore
									AND (rr.[status] IS NULL OR rr.[status] = 1)),0);
		
/*      -- Commented as per Galina's request - 2019-09-10 - Cristian - Ticket #10174
		SET @hl7Count = Isnull((SELECT COUNT(r.Id) FROM HL7Report r 
						JOIN HL7ReportDoctor rd ON r.Id = rd.HL7ReportId 
						JOIN PracticeDoctors pd ON rd.practiceDoctorId = pd.Id 
						JOIN HL7Patient hp ON r.HL7PatientId = hp.Id 
						JOIN PatientRecords pr ON hp.PatientRecordId = pr.Id 
						LEFT JOIN (SELECT ms.HL7ReportId, ms.seenAt FROM HL7ReportVersion rv 
									JOIN HL7MarkedSeen ms ON rv.HL7ReportId = ms.HL7ReportId AND rv.Id = ms.HL7ReportVersionId) ms ON r.Id = ms.HL7ReportId 
						WHERE rd.practiceDoctorId = @practiceDoctorId AND pd.PracticeId = pr.PracticeId AND ms.seenAt IS NULL),0);
*/

		SET @exDocumentCount = (@faxHrmCount + @hl7Count);
	
	END 

	SET @TotalToDO = (@exDocumentCount + @triageCount + @wlCount);

	SELECT 
		 @cmCount AS ContactManagerCount
		,@exDocumentCount AS ExternalDocumentsCount
		,@wlCount AS WorklistCount
		,@triageCount AS TriageCount
		,@TotalToDO AS TotalToDo
	
END




