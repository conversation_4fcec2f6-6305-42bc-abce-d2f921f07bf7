﻿CREATE PROCEDURE [dbo].[GetReportStressEcho]
	@AppointmentTestsId int,
	@appointmentTestLogId INT = 0

AS
BEGIN
	SET NOCOUNT ON;

   IF @appointmentTestLogId <=0
   BEGIN
		SET @appointmentTestLogId = (SELECT MAX(L.Id) FROM AppointmentTestLogs L JOIN AppointmentTests T ON L.AppointmentId = T.AppointmentId WHERE T.Id = @AppointmentTestsId AND L.Status = 0)
   END  


SET ANSI_WARNINGS  OFF;

		CREATE TABLE #Measurements
(

	AppointmentTestsId int,
	MeasurementCategoriesName VARCHAR (100),
	units varchar(20),
	MeasurementsName VARCHAR (100),
	value varchar(20),
	measurementCode varchar(100),
	categoryCode varchar(100),
	sequence int,
    description VARCHAR(20),
	Resting varchar(20),
	Excercise varchar(20), 


)

CREATE TABLE #MeasurementsGeneral
(
	AppointmentTestsId int,	
	sequence int,
    description VARCHAR(20),
	Resting varchar(20),
	Excercise varchar(20), 
	Units varchar(20)
)


INSERT INTO #Measurements
         SELECT
			     dbo.AppointmentTests.Id AS AppointmentTestsId
			    , dbo.MeasurementCategories.name AS MeasurementCategoriesName
			    , dbo.Measurements.units,
				 dbo.Measurements.name AS MeasurementsName,
			  
				dbo.MeasurementSavedValues.Value,
			    dbo.Measurements.measurementCode,
			     dbo.MeasurementCategories.categoryCode,
			
				(CASE WHEN dbo.Measurements.measurementCode='RestingHR' OR dbo.Measurements.measurementCode='MAXHR' then 1 ELSE
				(CASE WHEN dbo.Measurements.measurementCode='RestingBloodPressure'  OR dbo.Measurements.measurementCode='MAXBP' then 2 ELSE 
				(CASE WHEN dbo.Measurements.measurementCode='RestingSaO2'  OR dbo.Measurements.measurementCode='PEAKXSA02' then 3 ELSE 
				(CASE WHEN dbo.Measurements.measurementCode='RestingRVSP'  OR dbo.Measurements.measurementCode='PeakExerciseRVSP' then 4 ELSE 
				null END) END) END) END) AS Sequence,
				 
				 
				(CASE WHEN dbo.Measurements.measurementCode='RestingHR' OR dbo.Measurements.measurementCode='MAXHR' then 'HEART RATE' ELSE
				(CASE WHEN dbo.Measurements.measurementCode='RestingBloodPressure'  OR dbo.Measurements.measurementCode='MAXBP' then 'BLOOD PRESSURE' ELSE 
				(CASE WHEN dbo.Measurements.measurementCode='RestingSaO2'  OR dbo.Measurements.measurementCode='PEAKXSA02' then 'Sa02' ELSE 
				(CASE WHEN dbo.Measurements.measurementCode='RestingRVSP'  OR dbo.Measurements.measurementCode='PeakExerciseRVSP' then 'RVSP' ELSE 
				null END) END) END) END) AS DESCRIPTION,

				 
				 (CASE WHEN dbo.Measurements.measurementCode ='RestingHR'  then dbo.MeasurementSavedValues.Value ELSE
				 (CASE WHEN dbo.Measurements.measurementCode ='RestingBloodPressure'  then dbo.MeasurementSavedValues.Value ELSE 
				 (CASE WHEN dbo.Measurements.measurementCode ='RestingSaO2'  then dbo.MeasurementSavedValues.Value  ELSE 
				 (CASE WHEN dbo.Measurements.measurementCode ='RestingRVSP'  then dbo.MeasurementSavedValues.Value  ELSE 
				 NULL END) END) END) END) AS RESTING,

				
				 (CASE WHEN dbo.Measurements.measurementCode='MAXHR'  then dbo.MeasurementSavedValues.Value ELSE
				 (CASE WHEN dbo.Measurements.measurementCode='MAXBP'  then dbo.MeasurementSavedValues.Value ELSE 
				(CASE WHEN dbo.Measurements.measurementCode='PEAKXSA02'  then dbo.MeasurementSavedValues.Value ELSE 
				 (CASE WHEN dbo.Measurements.measurementCode ='PeakExerciseRVSP'  then dbo.MeasurementSavedValues.Value ELSE 
				 NULL END) END) END) END) AS EXCERCISE

					          
			          
           FROM dbo.AppointmentTests 
     INNER JOIN dbo.Appointments ON dbo.AppointmentTests.AppointmentId = dbo.Appointments.Id 
     --INNER JOIN (SELECT MAX(Id) AS AppointmentTestLogsId, AppointmentID, TestID
			  --     FROM dbo.AppointmentTestLogs
	    --       GROUP BY Status, AppointmentID, TestID
		   --      HAVING (Status = 0)) as P ON dbo.AppointmentTests.AppointmentId = P.AppointmentID AND dbo.AppointmentTests.TestId = P.TestID 
	 INNER JOIN dbo.AppointmentTestLogs AS P ON dbo.AppointmentTests.AppointmentId = P.AppointmentID
     INNER JOIN dbo.MeasurementSavedValues ON P.Id = dbo.MeasurementSavedValues.AppointmentTestLogID 
     INNER JOIN dbo.Measurements ON dbo.MeasurementSavedValues.MeasurementId = dbo.Measurements.Id 
     INNER JOIN dbo.MeasurementCategories ON dbo.Measurements.MeasurementCategoryID = dbo.MeasurementCategories.Id 
     INNER JOIN dbo.MeasurementByPractices ON dbo.Measurements.Id = dbo.MeasurementByPractices.MeasurementID 
     INNER JOIN dbo.PatientRecords ON dbo.Appointments.PatientRecordId = dbo.PatientRecords.Id AND dbo.MeasurementByPractices.PracticeID = dbo.PatientRecords.PracticeId 
     INNER JOIN dbo.Demographics as G ON dbo.Appointments.PatientRecordId = G.PatientRecordId
      LEFT JOIN MeasurementRanges rn ON (dbo.Measurements.measurementCode = rn.measurementCode AND  dbo.MeasurementCategories.categoryCode = rn.categoryCode AND (CASE WHEN g.gender = 0 THEN 'M' ELSE 'F' END) = rn.gender AND rn.MeasurementRangeTypeId = 1)
          WHERE (dbo.AppointmentTests.Id =  @AppointmentTestsId) AND (dbo.MeasurementByPractices.Visible = 1) and
		   dbo.MeasurementCategories.categoryCode in ('General','RESTSC', 'PEAKSC') 
		   and P.Id = @appointmentTestLogId

INSERT INTO #MeasurementsGeneral
select distinct 
		(Case when a.AppointmentTestsId is not null then a.AppointmentTestsId else b.AppointmentTestsId end) AppointmentTestsId, 
	    (Case when a.sequence is not null then a.sequence else b.sequence end) Sequence, 
		(Case when a.description is not null then a.description else b.description end) description, 
		 a.Resting, b.Excercise ,
		 (Case when a.units is not null then a.units else b.units end) units 
		 from
		 (  SELECT  sequence, CategoryCode, Description, Resting, Excercise, AppointmentTestsId, units
			FROM    [#Measurements]
			WHERE   (Description IS NOT NULL) AND (Excercise IS NULL))  A
full outer join 
		(	SELECT    sequence,CategoryCode, Description, Resting, Excercise, AppointmentTestsId, units
			FROM      [#Measurements]
			WHERE     (Description IS NOT NULL) AND (Resting IS NULL))  B
			on a.description=b.description  

select AppointmentTestsId, sequence, description, CONVERT(varchar(10), Resting) as Resting, CONVERT(varchar(10), Excercise) as Excercise, units from #MeasurementsGeneral
union
SELECT    AppointmentTestsId, 9 as sequence,  MeasurementsName as description, CONVERT(varchar(10), Value)  as Resting, CONVERT(varchar(10), AppointmentTestsId)  as Excercise, units
			FROM      [#Measurements] WHERE  CategoryCode = 'General' 


			select * from #Measurements

 drop table  #Measurements
 drop table  #MeasurementsGeneral
	
End