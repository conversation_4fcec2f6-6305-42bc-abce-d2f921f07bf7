﻿CREATE PROCEDURE [dbo].[GetPatientLocations]
	@patientId INT,
	@externalDoctorId INT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 
	 pl.PatientRecordId AS PatientId
	,pl.Id AS PatientLocationId
	,pl.IsActive AS IsActivePatientLocation
	,pl.ExternalDoctorLocationId
	,ea.Id AS ExternalDoctorAddressId
	,ea.ExternalDoctorId As ExternalDoctorId
	,ea.addressName AS AddressName
	,ea.addressLine1 AS AddressLine1
	,ea.addressLine2 AS AddressLine2
	,ea.addressType AS AddressType
	,ea.city AS City
	,ea.postalCode AS PostalCode
	,ea.province AS Province
	,ea.country AS Country
	,ea.IsActive AS IsActiveAddress
	,ph.Id AS ExternalDoctorPhoneNumberId 
	,ph.faxNumber AS FaxNumber
	,ph.phoneNumber AS PhoneNumber
	,ph.typeOfPhoneNumber AS TypeOfPhoneNumber
	,ph.extention AS PhoneNumberExtension
	,ph.IsActive As IsActivePhone
	,edl.IsActive AS IsActiveLocation -- external doctor location active
	FROM PatientLocations pl
	JOIN ExternalDoctorLocations edl ON pl.ExternalDoctorLocationId = edl.Id
	JOIN ExternalDoctorAddresses ea ON edl.ExternalDoctorAddressId = ea.Id	
	JOIN ExternalDoctorPhoneNumbers ph ON edl.ExternalDoctorPhoneNumberId = ph.Id	
	WHERE pl.PatientRecordId = @patientId	
	AND ea.ExternalDoctorId = CASE WHEN @externalDoctorId > 0 THEN @externalDoctorId ELSE ea.ExternalDoctorId END
	AND ph.ExternalDoctorId = CASE WHEN @externalDoctorId > 0 THEN @externalDoctorId ELSE ph.ExternalDoctorId END

END