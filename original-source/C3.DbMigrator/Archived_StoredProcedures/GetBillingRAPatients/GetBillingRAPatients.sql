﻿CREATE PROCEDURE [dbo].[GetBillingRAPatients] 
	@practiceId int,
	@healthCardNumbers nvarchar(max)
AS
BEGIN
	SET NOCOUNT ON;

	if (@healthCardNumbers = '')
		return;

    select healthCardNumber,firstName,lastName,demographicId,patientRecordId from (
            select a.number healthCardNumber,b.firstName,b.lastName,b.Id demographicId,c.Id patientRecordId, COUNT(*) q
	          from DemographicsHealthCards a 
              join (SELECT @practiceID as PracticeID, cast(value as nvarchar(20)) as number 
                      FROM STRING_SPLIT(@healthCardNumbers, ',')) hc on (a.number = hc.number )
              join Demographics b on a.DemographicId=b.Id 
              join PatientRecords c on (b.PatientRecordId=c.Id and hc.practiceID = c.PracticeId )
	         where hc.PracticeId = @practiceId
            group by a.number,b.firstName,b.lastName,b.Id,c.Id
           ) r
--    option (recompile);
    
END