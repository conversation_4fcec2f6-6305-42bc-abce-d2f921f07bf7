﻿
CREATE PROCEDURE [dbo].[GetHospitalDaysheet]	
	@selectedDate DATETIME = NULL, 
	@practiceDoctorId INT = NULL,	
	@hdAdmissionId INT = NULL	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	DECLARE @isActive int = 1;	
	DECLAR<PERSON> @lookupDate datetime2;-- = CONVERT(char(8), @selectedDate, 112);

	IF(@hdAdmissionId IS NULL OR @hdAdmissionId= 0)
	BEGIN
		--SET @practiceId = (SELECT PracticeId FROM Office WHERE Id = @officeId);
		SET @lookupDate = CONVERT(char(8), @selectedDate, 112);
	END	

	DECLARE @admissions TABLE
	   ( Id int identity(1,1) not null,
	     AdmissionId int not null,			 
		 PracticeId  int null,	     
		 [PatientRecordId] [int] NOT NULL,
		 [AdmissionTypeId] [int] NOT NULL,
		 AdmissionType nvarchar(100) null,
		 [DateAdmitted] [datetime2](7) NOT NULL,
		 [DateDischarge] [datetime2](7) NOT NULL,
		 [ReferralDoctorId] [int] NULL,
		 DiagnosisCodeId INT NULL,
		 [HospitalId] [int] NOT NULL,
		 Hospital nvarchar(100) null,
		 HospitalCode nvarchar(100) null,
		 [StatusId] [int] NOT NULL,
		 [BillStatusId] [int] NOT NULL,
		 [IsActive] [bit] NOT NULL,
		 [PracticeDoctorId] [int] NOT NULL,
		 PracticeDoctor nvarchar(100) null,
		 PaymentMethod int not null,	
		 [DateCreated] [datetime2](7) NOT NULL,
		 [DateLastModified] [datetime2](7) NULL,
		 [LastModifiedBy] [int] NULL		 
		 ,primary key (Id)
	   );
    INSERT INTO @admissions(
	     AdmissionId,
		 PracticeId,	  
		 PatientRecordId,	
		 [AdmissionTypeId],
		 AdmissionType,	 
		 [DateAdmitted],
		 [DateDischarge],
		 [ReferralDoctorId] ,
		 DiagnosisCodeId,
		 [HospitalId],
		 Hospital,
		 HospitalCode,
		 [StatusId],
		 [BillStatusId],
		 [IsActive],
		 [PracticeDoctorId],
		 PracticeDoctor,
		 PaymentMethod,
		 [DateCreated],
		 [DateLastModified],
		 [LastModifiedBy]
		 )
  SELECT 
	   adm.[Id]
      ,precs.[PracticeId]
      ,adm.PatientRecordId
	  ,adm.AdmissionTypeId
	  ,admType.[Description]
	  ,adm.DateAdmitted
	  ,adm.DateDischarge
	  ,adm.ReferralDoctorId
	  ,adm.DiagnosisCodeId
	  ,adm.HospitalId
	  ,hos.[name]
	  ,hos.code
	  ,adm.StatusId
	  ,adm.BillStatusId	  
      ,adm.[IsActive]   
	  ,adm.PracticeDoctorId      
	  ,isnull(extDocs.firstName,'') +' '+isnull(extDocs.lastName,'') AS PracticeDoctor	   
	  ,adm.PaymentMethod
      ,adm.[DateCreated]
	  ,adm.DateLastModified
	  ,adm.LastModifiedBy 
	FROM [dbo].[HDAdmissions] adm
		 JOIN PatientRecords precs ON adm.PatientRecordId =precs.Id
	     JOIN PracticeDoctors pracDocs ON adm.PracticeDoctorId = pracDocs.Id
	     JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
		 JOIN Hospitals hos on adm.HospitalId = hos.Id
		 JOIN HDAdmissionTypes admType on adm.AdmissionTypeId = admType.Id
  WHERE 
  adm.IsActive = @isActive AND ((@hdAdmissionId IS NOT NULL AND @hdAdmissionId > 0 AND adm.Id = @hdAdmissionId) 
  OR (  
	  @lookupDate >= CONVERT(char(8), adm.DateAdmitted, 112) AND @lookupDate <=  CONVERT(char(8), adm.DateDischarge, 112)
	  AND   adm.IsActive = @isActive		  
	  AND   adm.Id = (CASE WHEN @hdAdmissionId IS NULL THEN adm.Id ELSE @hdAdmissionId END)
	  AND   adm.PracticeDoctorId = (CASE WHEN @practiceDoctorId IS NULL THEN adm.PracticeDoctorId ELSE @practiceDoctorId END)
	  ))    
ORDER BY adm.DateAdmitted;


SELECT 
	   adm.AdmissionId AS Id
	   --,ISNULL((SELECT TOP 1 Id FROM HDAdmissionActions WHERE AdmissionId = adm.AdmissionId AND IsActive = 1 AND @lookupDate = CONVERT(char(8), DateServiced, 112)  ORDER BY Id),0) AS AdmissionActionId
	  ,ISNULL((SELECT TOP 1 Id FROM HDAdmissionActions WHERE AdmissionId = adm.AdmissionId AND IsActive = 1 AND CONVERT(char(8), adm.DateAdmitted, 112) = CONVERT(char(8), DateServiced, 112)  ORDER BY Id),0) AS AdmissionActionId
      ,adm.[PracticeId]
      ,adm.PatientRecordId
	  ,demo.firstName AS PatientFirstName
	  ,demo.lastName AS PatientLastName
	  ,demo.dateOfBirth AS DateOfBirth
	  ,demo.gender AS Gender
	  ,ISNULL((SELECT TOP 1 number+' '+ISNULL([version],'') FROM DemographicsHealthCards WHERE DemographicId = demo.Id ORDER BY Id DESC),'') AS PatientOHIP
	  ,adm.AdmissionTypeId
	  ,adm.AdmissionType
	  ,adm.DateAdmitted
	  ,adm.DateDischarge
	  ,adm.ReferralDoctorId
	  ,isnull(extDocs.lastName,'') +', '+isnull(extDocs.firstName,'') AS ReferralDoctor	 
	  ,extDocs.OHIPPhysicianId AS RefDocOHIPPhysicianId -- referral doctor
	  ,extDocs.CPSO AS RefDocCPSO -- referral doctor
	  ,ISNULL((SELECT TOP 1 isnull(ed.lastName,'')+', '+isnull(ed.firstName,'')		               
			        FROM ExternalDoctors as ed 
					JOIN DemographicsFamilyDoctors fd ON ed.Id = fd.ExternalDoctorId
					WHERE fd.DemographicId = demo.Id
					ORDER BY fd.Id DESC),'') AS FamilyDoctor
	  ,adm.HospitalId
	  ,adm.Hospital
	  ,adm.HospitalCode
	  ,adm.StatusId
	  ,adm.BillStatusId	  
      ,adm.[IsActive]   
	  ,adm.PracticeDoctorId  
	  ,adm.PracticeDoctor	 
	  ,adm.PaymentMethod 	   
      ,adm.[DateCreated]
	  ,adm.DateLastModified
	  ,adm.LastModifiedBy AS ModifiedById
	  ,users.FirstName AS ModifiedByFirstName
	  ,users.LastName AS ModifiedByLastName
	  ,adm.DiagnosisCodeId
	  ,diag.Diagnosis
	  ,diag.Code AS DiagnosisCode	    
	FROM @admissions adm
	JOIN Demographics demo ON adm.PatientRecordId = demo.PatientRecordId
	LEFT JOIN ExternalDoctors extDocs ON adm.ReferralDoctorId = extDocs.Id
	LEFT JOIN DiagnoseCodes diag ON adm.DiagnosisCodeId = diag.Id
	LEFT JOIN AspNetUsers users ON adm.LastModifiedBy = users.UserID
    ORDER BY demo.lastName
END
