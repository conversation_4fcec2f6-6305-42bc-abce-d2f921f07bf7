﻿CREATE PROCEDURE [dbo].[GetMainDoctorInfo]
	@patienRecordId  INT
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @Day int = 01;
	DECLARE @Month int  = 01 ;
	DECLARE @YearMin int = 0001;
	DECLARE @YearMax int = 9999;

	DECLARE @MinDate Date;
	DECLARE @MaxDate Date;

	SET @MinDate = (SELECT datefromparts(@YearMin, @month, @day));
	SET @MaxDate = (SELECT datefromparts(@YearMax, @month, @day));

	SELECT
	 dmm.PracticeDoctorId as mainDoctorId,
	 ex.lastName + ', ' + ex.firstName as docFullName,
	 (SELECT top 1 ISNULL(extDoc.lastName, '') +', ' + ISNULL(extDoc.firstName,'')  FROM DemographicsEnrollments as enr  
	 join DemographicsMainResponsiblePhysicians as mainDoc on enr.DemographicsMRPId = mainDoc.Id
	 join ExternalDoctors as extDoc on mainDoc.ExternalDoctorId = extDoc.Id
	 WHERE enr.DemographicsMRPId = @patienRecordId 
	 and (enr.enrollmentTerminationDate is not null 
	 and enr.enrollmentTerminationDate > @MinDate and  enr.enrollmentTerminationDate <@MaxDate
	)
	 order by enr.Id desc) EnrolledDocName

	 FROM Demographics as d
	 join PatientRecords as pr on d.PatientRecordId = pr.Id
	 join DemographicsMainResponsiblePhysicians as dmm on d.id = dmm.DemographicId
	 join ExternalDoctors as ex on dmm.ExternalDoctorId = ex.Id
	WHERE d.PatientRecordId= @patienRecordId and dmm.IsActive = 1
	order by dmm.PracticeDoctorId desc

END