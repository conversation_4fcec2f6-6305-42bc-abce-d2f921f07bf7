﻿-- =============================================
CREATE PROCEDURE [dbo].[GetPatientPreviousTests]
	@appointmentTestId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	
	DECLARE @practiceId INT = 0;
	DECLARE @patientId INT = 0;
	DECLARE @appointmentTime DATETIME2;
	DECLARE @testId INT = 0;

	DECLARE @excludePrevAppStatuses TABLE  -- excluded appointment status for previous tests
	( 
        appointmentStatus int 
	);	

	INSERT INTO @excludePrevAppStatuses VALUES (0); -- cancellationlist	
	INSERT INTO @excludePrevAppStatuses VALUES (1); -- waitlist
	INSERT INTO @excludePrevAppStatuses VALUES (2); -- Booked	
	INSERT INTO @excludePrevAppStatuses VALUES (3); -- NotArrived	
	INSERT INTO @excludePrevAppStatuses VALUES (6); -- Missed	
	INSERT INTO @excludePrevAppStatuses VALUES (7); -- Cancelled	
	INSERT INTO @excludePrevAppStatuses VALUES (16); -- triage	

	IF @appointmentTestId > 0 
	BEGIN
		SELECT TOP 1 
		@practiceId = ISNULL(o.PracticeId,0),		
		@patientId = ISNULL(app.PatientRecordId,0),
		@testId = ISNULL(appTest.TestId,0),
		@appointmentTime = app.appointmentTime	
		FROM AppointmentTests appTest
		JOIN Appointments app ON appTest.AppointmentId = app.Id	
		JOIN Office o ON app.OfficeId = o.Id
		WHERE appTest.Id = @appointmentTestId
	END

    -- Insert statements for procedure here
	SELECT 
	apt.PatientId
	,apt.PracticeId
	,apt.OfficeId
	,apt.AppointmentTestId
	,apt.AppointmentId
	,apt.AppointmentTypeId
	,apt.AppointmentStatus
	,apt.AppointmentTime,NULL
	,apt.PracticeDoctorId
	,apt.PracticeDoctorSpecialtyId
	,apt.PracticeDoctorUserId
	,apt.TestId
	,apt.TestGroupId
	,apt.TestName
	,apt.TestNameFull
	,apt.TestStatusId
	,apt.TestStatus
	,apt.TestStatusColor
	,apt.TestStatusCSS
	,apt.TestDate
	,apt.IsVP		
	,apt.PracticeDoctor
	,apt.ExternalDoctorId
	,apt.BillStatusId	
	,apt.BillStatus
	,apt.BillStatusColor
	,apt.AppointmentTestLogId
	,apt.PrevAppointmentTestLogId   -- for previous test log id 
	,apt.PrevAppTestLogIdDoctor	
	FROM fn_GetPatientTests(@patientId,@practiceId,0) apt --we need all the previous test back
	WHERE apt.AppointmentTime < @appointmentTime
	AND apt.AppointmentStatus NOT IN (select appointmentStatus from @excludePrevAppStatuses)
	AND apt.TestId = @testId
	ORDER BY apt.AppointmentTime DESC

END