﻿



CREATE PROCEDURE [dbo].[GetFormPatientInfo]
    @PracticeID int,
	@patientId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @OHIPID nvarchar(20);
    DECLARE @OHIPVersionCode nvarchar(20);
	DECLARE @ProvinceOHIP nvarchar(2);
	DECLARE @addressLine1 nvarchar(200);
	DECLARE @addressLine2 nvarchar(200);
	DECLARE @city nvarchar(100);
	DECLARE @postalCode nvarchar(10);
	DECLARE @province nvarchar(100);
	DECLARE @country nvarchar(100);

    SELECT TOP 1 @OHIPID = isnull(hc.[number],''),
		   @ProvinceOHIP = case when hc.provinceCode = 0 then 'AB'
							when hc.provinceCode = 9 then 'PE'
							when hc.provinceCode = 4 then 'NF'
							when hc.provinceCode = 1 then 'BC'
							when hc.provinceCode = 8 then 'ON'
							when hc.provinceCode = 2 then 'MB'
							when hc.provinceCode = 5 then 'NS'
							else 'ON' end,
           @OHIPVersionCode = ISNULL(hc.[version],'')
	FROM DemographicsHealthCards hc JOIN Demographics d ON (hc.DemographicId = d.id)
    WHERE d.patientrecordid = @patientId
    ORDER BY hc.id DESC;

    SELECT TOP 1 @addressLine1 = da.addressLine1,
	       @addressLine2 = da.addressLine2,
		   @city = da.city,
		   @postalcode = da.postalCode,
  		   @province = da.province,
		   @country = da.country
	FROM DemographicsAddresses da JOIN Demographics d ON (da.DemographicId = d.id)
    WHERE d.patientrecordid = @patientId
	AND da.IsActive = 1
    ORDER BY da.id DESC;

    -- Insert statements for procedure here
	SELECT 
	d.Id AS DemographicId
	,isnull(d.aliasLastName,d.lastName) as LastName
	,isnull(d.aliasMiddleName, d.middleName) as MiddleName
	,isnull(d.aliasFirstName, d.firstName) as FirstName
	,d.dateOfBirth
	,d.gender
	,d.email
	,@OHIPID as OHIPID
    ,@OHIPVersionCode as OHIPVersionCode
	,@ProvinceOHIP as ProvinceOHIP
	,@addressLine1 as AddressLine1
	,@addressLine2 as AddressLine2
	,@city as City
	,@postalCode as PostalCode
	,@province as Province
	,@country as Country
	,(SELECT TOP 1 fd.ExternalDoctorId
		FROM DemographicsFamilyDoctors fd
		WHERE fd.DemographicId = d.Id
		AND   fd.IsActive = 1
        AND   fd.IsRemoved = 0 -- Ticket #6190
		ORDER BY fd.Id DESC) AS FamilyDoctorID
	,(SELECT TOP 1 rd.ExternalDoctorId
		FROM DemographicsDefaultReferralDoctors rd
		WHERE rd.DemographicId = d.Id
		AND   rd.IsActive = 1
        AND   rd.IsRemoved = 0 -- Ticket #6190
		ORDER BY rd.Id DESC) AS DefaultReferralDoctorID
    ,(SELECT STUFF(  
		    (
			SELECT ', '+ CAST(d.phoneNumber AS VARCHAR(500)) + ' '+ 
						(CASE d.typeOfPhoneNumber
							WHEN 0 THEN 'H'
							WHEN 1 THEN 'C'
							WHEN 2 THEN 'W'
							ELSE '' END)
			FROM DemographicsPhoneNumbers d
			WHERE phoneNumber IS NOT NULL AND ltrim(rtrim(phoneNumber)) <> '' and isRemoved = 0
			AND   d.DemographicId = d.Id
		ORDER BY  DemographicId, typeOfPhoneNumber, isActive DESC, Id DESC
		FOR XMl PATH('') 
            ),1,1,''  )) as PatientPhoneNumbers
	FROM PatientRecords pr
	JOIN (SELECT TOP 1 * FROM Demographics WHERE PatientRecordId = @patientId AND active = 0) d ON pr.Id = d.PatientRecordId
	WHERE pr.Id = @patientId
	AND   pr.PracticeId = @PracticeID
END
	

