﻿
CREATE PROCEDURE [dbo].[GetPatientTestHistory] 
	@practiceId INT = 0,
	@patientId INT = 0,
	@appointmentTestId INT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	
	SET NOCOUNT ON;

	DECLARE @vp BIT = 1;
	DECLARE @notvp BIT = 0;
	DECLARE @lastLogOffices TABLE  -- offices that want to use the last log for the doctor save root categories
	( 
		officeId int 
	);		

	IF @appointmentTestId > 0 AND @practiceId = 0 AND @patientId = 0
	BEGIN
		SELECT TOP 1 
		@practiceId = o.PracticeId,		
		@patientId = app.PatientRecordId		
		FROM AppointmentTests appTest
		JOIN Appointments app ON appTest.AppointmentId = app.Id	
		JOIN Office o ON app.OfficeId = o.Id
		WHERE appTest.Id = @appointmentTestId
	END

	INSERT INTO @lastLogOffices 
	SELECT Id FROM office WHERE PracticeId = 1 -- pace clinic offices

	--speciality clinic for practice 16
	DECLARE @specialityOfficeId INT = ISNULL((SELECT TOP 1 Id FROM Office where PracticeId = 16 AND [name] = 'Specialty Clinics'),0);
	IF @specialityOfficeId > 0
	BEGIN
		INSERT INTO @lastLogOffices 
		SELECT @specialityOfficeId;
	END
	
	SELECT
	apt.PatientId
	,apt.PracticeId
	,apt.OfficeId
	,apt.AppointmentTestId
	,apt.AppointmentId
	,apt.AppointmentTypeId
	,apt.AppointmentStatus
	,apt.AppointmentTime
	,apt.PracticeDoctorId
	,apt.PracticeDoctorSpecialtyId
	,apt.PracticeDoctorUserId
	,apt.TestId
	,apt.TestGroupId
	,apt.TestName
	,apt.TestNameFull
	,apt.TestStatusId
	,apt.TestStatus
	,apt.TestStatusColor
	,apt.TestStatusCSS
	,apt.TestDate
	,apt.IsVP		
	,apt.PracticeDoctor
	,apt.ExternalDoctorId
	,apt.ReportTemplate
	,apt.BillStatusId	
	,apt.BillStatus
	,apt.BillStatusColor
	,apt.AppointmentTestLogId
	,apt.AppointmentTestLogDate
	,apt.PracticeTemplateId
	,apt.PrevAppointmentTestLogId 
	,(CASE WHEN EXISTS(SELECT * FROM @lastLogOffices WHERE officeId = apt.OfficeId) THEN apt.PrevAppointmentTestLogId ELSE apt.PrevAppTestLogIdDoctor END) AS PrevAppTestLogIdDoctor
	,isnull(bill.ConsultCodeId,0) AS ConsultCodeId
	,isnull(bill.ConsultCodeId2,0) AS ConsultCodeId2
	,isnull(bill.ConsultCodeId3,0) AS ConsultCodeId3
	,isnull(bill.DiagnosticCodeId,0) AS DiagnosticCodeId
	,isnull(bill.DiagnosticCodeId2,0) AS DiagnosticCodeId2
	,isnull(bill.DiagnosticCodeId3,0) AS DiagnosticCodeId3
	,isnull(bill.ImmunizationCodeId,0) AS ImmunizationCodeId
    ,isnull(bill.ConsultCode, '') AS ConsultCode
	,isnull(bill.ConsultCode2, '') AS ConsultCode2
	,isnull(bill.ConsultCode3, '') AS ConsultCode3
	,isnull(bill.DiagnosticCode, '') AS DiagnosticCode
	,isnull(bill.DiagnosticCode2, '') AS DiagnosticCode2
	,isnull(bill.DiagnosticCode3, '') AS DiagnosticCode3
	,isnull(bill.ImmunizationCode, '') AS ImmunizationCode        
  FROM fn_GetPatientTests(@patientId,@practiceId,@appointmentTestId) apt     
  LEFT JOIN ( 
	SELECT AppointmentID
		,bill1.ConsultCode as ConsultCodeID
		,bill1.ConsultCode2 as ConsultCodeID2
		,bill1.ConsultCode3 as ConsultCodeID3
		,bill1.DiagnosticCode AS DiagnosticCodeID
	    ,bill1.DiagnosticCode2 AS DiagnosticCodeID2
	    ,bill1.DiagnosticCode3 AS DiagnosticCodeID3
		,bill1.ImmunizationCode AS ImmunizationCodeID
  		,CONVERT(NVARCHAR(50),
			(CASE  
  				WHEN isnull(bill1.ConsultCode,0) > 0
				THEN (SELECT TOP 1 Code FROM ConsultCodes WHERE Id = bill1.ConsultCode)
				ELSE ''
	  		END)) as ConsultCode
		,CONVERT(NVARCHAR(50),
			(CASE  
  				WHEN isnull(bill1.ConsultCode2,0) > 0
				THEN (SELECT TOP 1 Code FROM ConsultCodes WHERE Id = bill1.ConsultCode2)
				ELSE ''
	  		END)) as ConsultCode2
		,CONVERT(NVARCHAR(50),
			(CASE  
  				WHEN isnull(bill1.ConsultCode3,0) > 0
				THEN (SELECT TOP 1 Code FROM ConsultCodes WHERE Id = bill1.ConsultCode3)
				ELSE ''
	  		END)) as ConsultCode3
		,CONVERT(NVARCHAR(50),
			(CASE  
				WHEN isnull(bill1.DiagnosticCode,0) > 0
				THEN (SELECT TOP 1 [Code] FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode)
				ELSE ''
			END)) as DiagnosticCode
		,CONVERT(NVARCHAR(50),
			(CASE  
				WHEN isnull(bill1.DiagnosticCode2,0) > 0
				THEN (SELECT TOP 1 [Code] FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode2)
				ELSE ''
			END)) as DiagnosticCode2
		,CONVERT(NVARCHAR(50),
			(CASE  
				WHEN isnull(bill1.DiagnosticCode3,0) > 0
				THEN (SELECT TOP 1 [Code] FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode3)
				ELSE ''
			END)) as DiagnosticCode3
		,CONVERT(NVARCHAR(50),
			(CASE  
				WHEN isnull(bill1.ImmunizationCode,0) > 0
				THEN (SELECT TOP 1 [Code] FROM PreventiveCareBonusCodes WHERE Id = bill1.ImmunizationCode)
				ELSE ''
			END)) as ImmunizationCode
	   FROM AppointmentBills as bill1 ) as bill ON apt.AppointmentId = bill.AppointmentID	   	
	ORDER BY apt.AppointmentTime DESC
END

