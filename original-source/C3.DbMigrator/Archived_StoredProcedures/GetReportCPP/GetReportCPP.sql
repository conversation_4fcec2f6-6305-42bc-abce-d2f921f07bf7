﻿CREATE PROCEDURE [dbo].[GetReportCPP]
	@appointmentTestId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	
	DECLARE @categoryIDFamilyHistory INT = 1;
	DECLARE @categoryIDPastHealth INT = 2;
	DECLARE @categoryIDProblemList INT = 3;
	DECLARE @categoryIDRiskFactors INT = 4;
	DECLARE @categoryIDImmunizationAndPreventiveCare INT = 5;
	DECLARE @categoryIDAlertAndSpecialNeeds INT = 6;

	DECLARE @practiceId INT;
	DECLARE @officeId INT;	
	DECLARE @appointmentId INT;
	DECLARE @patientId INT;	
	DECLARE @appointmentTime DATETIME2;	
	DECLARE @practiceDoctorId INT;
	DECLARE @externalDoctorId INT; -- ExternalDoctorId for main appointment doctor
	DECLARE @categorynName nvarchar(512);
	--DECLARE @isVisiblCategory BIT = 0;

	DECLARE @col1Visible BIT = 1;
	DECLARE @col2Visible BIT = 1;
	DECLARE @col3Visible BIT = 1;
	DECLAR<PERSON> @col4Visible BIT = 1;
	DECLARE @col5Visible BIT = 1;
	DECLARE @col6Visible BIT = 1;
	DECLARE @col7Visible BIT = 1;
	DECLARE @col8Visible BIT = 1;
	DECLARE @col9Visible BIT = 1;
	DECLARE @col10Visible BIT = 1;
	DECLARE @col11Visible BIT = 1;
	DECLARE @col12Visible BIT = 1;

	DECLARE @tempCPP TABLE
	(
		AppointmentId int NOT NULL,
		AppointmentTestId INT NOT NULL,
		AppointmentTime datetime2 NOT NULL,	
		CategoryId int NOT NULL,
		CategorynName nvarchar(512) NULL,
		SavedValue nvarchar(4000) NULL,
		DateSaved datetime2 NOT NULL		
	)
	
	SELECT TOP 1 
	@practiceId = o.PracticeId,
	@officeId = o.Id,	
	@appointmentId = appTest.appointmentId,	
	@patientId = app.PatientRecordId,
	@appointmentTime = app.appointmentTime,
	@practiceDoctorId = app.PracticeDoctorId,
	@externalDoctorId = pd.ExternalDoctorId	
	FROM AppointmentTests appTest
	JOIN Appointments app ON appTest.AppointmentId = app.Id	
	JOIN PracticeDoctors pd ON app.PracticeDoctorId = pd.Id
	JOIN Office o ON app.OfficeId = o.Id
	WHERE appTest.Id = @appointmentTestId

	--Family History
	SELECT TOP 1 @categorynName = [Text] FROM VP_CPP_Category WHERE Id = @categoryIDFamilyHistory;
	--SELECT TOP 1 @isVisiblCategory = ISNULL([Visible], 0), @categorynName = [Text] FROM VP_CPP_Setting WHERE DoctorID = @externalDoctorId AND VP_CPP_Category_Id = @categoryIDFamilyHistory;
	--IF @isVisiblCategory = 1
	--BEGIN
		SET @col1Visible = 1;
		SET @col2Visible = 1;
		SET @col3Visible = 1;
		SET @col4Visible = 1;
		SET @col5Visible = 1;
		SET @col6Visible = 1;
		SET @col7Visible = 1;
		SET @col8Visible = 1;
		SET @col9Visible = 1;
		SET @col10Visible = 1;
		SET @col11Visible = 1;
		SET @col12Visible = 1;

		SELECT TOP 1 @col1Visible=Col1Visible, @col2Visible=Col2Visible, @col3Visible=Col3Visible, @col4Visible=Col4Visible, @col5Visible=Col5Visible, @col6Visible=Col6Visible,
					 @col7Visible=Col7Visible, @col8Visible=Col8Visible, @col9Visible=Col9Visible, @col10Visible=Col10Visible, @col11Visible=Col11Visible, @col12Visible=Col12Visible
		FROM VP_CPP_Visible_Field WHERE PracticeDoctorId = @practiceDoctorId AND VP_CPP_Category_Id = @categoryIDFamilyHistory;

		INSERT INTO @tempCPP (AppointmentId,AppointmentTestId,AppointmentTime,CategoryId,CategorynName,SavedValue,DateSaved)
		SELECT @appointmentId,@appointmentTestId,@appointmentTime,@categoryIDFamilyHistory,@categorynName,
		dbo.[fn_GetCPPData](@col10Visible, a.DiagnosticCode, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
		dbo.[fn_GetCPPData](@col11Visible, IIF(a.DiagnosticCode = '' or a.DiagnosticCode is null, a.DiagnosticDescription, '(' + a.DiagnosticDescription + ')'), DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) +	
		dbo.[fn_GetCPPData](@col1Visible, a.ProblemDescription, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
		dbo.[fn_GetCPPData](@col3Visible, a.RelationShip, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
		dbo.[fn_GetCPPData](@col4Visible, DEFAULT, ',', DEFAULT, DEFAULT, a.AgeOnset, a.Units, a.LifeStage) +
		dbo.[fn_GetCPPData](@col2Visible, a.Treatment, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
		dbo.[fn_GetCPPData](@col6Visible, DEFAULT, ',', a.StartDateDay, a.StartDateMonth, a.StartDateYear, DEFAULT, DEFAULT) +
		dbo.[fn_GetCPPData](@col9Visible, a.Notes, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)
		,AddDate		
		FROM VP_CPP_FamilyHistory a
		INNER JOIN (SELECT MAX(b.Id) Id FROM VP_CPP_FamilyHistory b WHERE b.Visible = 1 AND b.PatientRecordId = @patientId AND DATEDIFF(DAY, b.AddDate, @appointmentTime) >= 0 GROUP BY ParentId) b on a.Id=b.Id
	--END	
	
	--Past Health
	SELECT TOP 1 @categorynName = [Text] FROM VP_CPP_Category WHERE Id = @categoryIDPastHealth;
	SET @col1Visible = 1;
	SET @col2Visible = 1;
	SET @col3Visible = 1;
	SET @col4Visible = 1;
	SET @col5Visible = 1;
	SET @col6Visible = 1;
	SET @col7Visible = 1;
	SET @col8Visible = 1;
	SET @col9Visible = 1;
	SET @col10Visible = 1;
	SET @col11Visible = 1;
	SET @col12Visible = 1;

	SELECT TOP 1 @col1Visible=Col1Visible, @col2Visible=Col2Visible, @col3Visible=Col3Visible, @col4Visible=Col4Visible, @col5Visible=Col5Visible, @col6Visible=Col6Visible,
					@col7Visible=Col7Visible, @col8Visible=Col8Visible, @col9Visible=Col9Visible, @col10Visible=Col10Visible, @col11Visible=Col11Visible, @col12Visible=Col12Visible
	FROM VP_CPP_Visible_Field WHERE PracticeDoctorId = @practiceDoctorId AND VP_CPP_Category_Id = @categoryIDPastHealth;

	INSERT INTO @tempCPP (AppointmentId,AppointmentTestId,AppointmentTime,CategoryId,CategorynName,SavedValue,DateSaved)
	SELECT @appointmentId,@appointmentTestId,@appointmentTime,@categoryIDPastHealth,@categorynName,
	dbo.[fn_GetCPPData](@col10Visible, a.DiagnosticCode, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col1Visible, IIF(a.DiagnosticCode = '' or a.DiagnosticCode is null, a.DiagnosticDescription, '(' + a.DiagnosticDescription + ')'), DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) +	
	dbo.[fn_GetCPPData](@col3Visible, a.Problem_Description, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col2Visible, a.Proc_Interv, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col4Visible, ISNULL(c.Text, ''), ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col5Visible, DEFAULT, ',', a.DateOfOnset_Day, a.DateOfOnset_Month, a.DateOfOnset_Year, DEFAULT, DEFAULT) +
	dbo.[fn_GetCPPData](@col6Visible, DEFAULT, ',', a.ResolutionDate_Day, a.ResolutionDate_Month, a.ResolutionDate_Year, DEFAULT, DEFAULT) +
	dbo.[fn_GetCPPData](@col7Visible, DEFAULT, ',', a.ProcDate_Day, a.ProcDate_Month, a.ProcDate_Year, DEFAULT, DEFAULT) +
	dbo.[fn_GetCPPData](@col8Visible, DEFAULT, ',', DEFAULT, DEFAULT, a.Years, a.Units, a.Life_Stage) +
	dbo.[fn_GetCPPData](@col9Visible, a.Notes, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)
	,AddDate		
	FROM VP_CPP_Problem_List a
	INNER JOIN (SELECT MAX(b.Id) Id FROM VP_CPP_Problem_List b WHERE b.IsProblemList = 0 AND b.PatientRecordId = @patientId AND DATEDIFF(DAY, b.AddDate, @appointmentTime) >= 0 GROUP BY ParentId) b on a.Id=b.Id
	LEFT JOIN VP_CPP_Problem_Status c on a.Problem_Status = c.Id

	--Problem List
	SELECT TOP 1 @categorynName = [Text] FROM VP_CPP_Category WHERE Id = @categoryIDProblemList;
	SET @col1Visible = 1;
	SET @col2Visible = 1;
	SET @col3Visible = 1;
	SET @col4Visible = 1;
	SET @col5Visible = 1;
	SET @col6Visible = 1;
	SET @col7Visible = 1;
	SET @col8Visible = 1;
	SET @col9Visible = 1;
	SET @col10Visible = 1;
	SET @col11Visible = 1;
	SET @col12Visible = 1;

	SELECT TOP 1 @col1Visible=Col1Visible, @col2Visible=Col2Visible, @col3Visible=Col3Visible, @col4Visible=Col4Visible, @col5Visible=Col5Visible, @col6Visible=Col6Visible,
					@col7Visible=Col7Visible, @col8Visible=Col8Visible, @col9Visible=Col9Visible, @col10Visible=Col10Visible, @col11Visible=Col11Visible, @col12Visible=Col12Visible
	FROM VP_CPP_Visible_Field WHERE PracticeDoctorId = @practiceDoctorId AND VP_CPP_Category_Id = @categoryIDProblemList;

	INSERT INTO @tempCPP (AppointmentId,AppointmentTestId,AppointmentTime,CategoryId,CategorynName,SavedValue,DateSaved)
	SELECT @appointmentId,@appointmentTestId,@appointmentTime,@categoryIDProblemList,@categorynName,
	dbo.[fn_GetCPPData](@col10Visible, a.DiagnosticCode, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col1Visible, IIF(a.DiagnosticCode = '' or a.DiagnosticCode is null, a.DiagnosticDescription, '(' + a.DiagnosticDescription + ')'), DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) +	
	dbo.[fn_GetCPPData](@col3Visible, a.Problem_Description, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col2Visible, a.Proc_Interv, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col4Visible, ISNULL(c.Text, ''), ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col5Visible, DEFAULT, ',', a.DateOfOnset_Day, a.DateOfOnset_Month, a.DateOfOnset_Year, DEFAULT, DEFAULT) +
	dbo.[fn_GetCPPData](@col6Visible, DEFAULT, ',', a.ResolutionDate_Day, a.ResolutionDate_Month, a.ResolutionDate_Year, DEFAULT, DEFAULT) +
	dbo.[fn_GetCPPData](@col7Visible, DEFAULT, ',', a.ProcDate_Day, a.ProcDate_Month, a.ProcDate_Year, DEFAULT, DEFAULT) +
	dbo.[fn_GetCPPData](@col8Visible, DEFAULT, ',', DEFAULT, DEFAULT, a.Years, a.Units, a.Life_Stage) +
	dbo.[fn_GetCPPData](@col9Visible, a.Notes, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)
	,AddDate		
	FROM VP_CPP_Problem_List a
	INNER JOIN (SELECT MAX(b.Id) Id FROM VP_CPP_Problem_List b WHERE b.IsProblemList = 1 AND b.PatientRecordId = @patientId AND DATEDIFF(DAY, b.AddDate, @appointmentTime) >= 0 GROUP BY ParentId) b on a.Id=b.Id
	LEFT JOIN VP_CPP_Problem_Status c on a.Problem_Status = c.Id

	--Risk Factors
	SELECT TOP 1 @categorynName = [Text] FROM VP_CPP_Category WHERE Id = @categoryIDRiskFactors;
	SET @col1Visible = 1;
	SET @col2Visible = 1;
	SET @col3Visible = 1;
	SET @col4Visible = 1;
	SET @col5Visible = 1;
	SET @col6Visible = 1;
	SET @col7Visible = 1;
	SET @col8Visible = 1;
	SET @col9Visible = 1;
	SET @col10Visible = 1;
	SET @col11Visible = 1;
	SET @col12Visible = 1;

	SELECT TOP 1 @col1Visible=Col1Visible, @col2Visible=Col2Visible, @col3Visible=Col3Visible, @col4Visible=Col4Visible, @col5Visible=Col5Visible, @col6Visible=Col6Visible,
					@col7Visible=Col7Visible, @col8Visible=Col8Visible, @col9Visible=Col9Visible, @col10Visible=Col10Visible, @col11Visible=Col11Visible, @col12Visible=Col12Visible
	FROM VP_CPP_Visible_Field WHERE PracticeDoctorId = @practiceDoctorId AND VP_CPP_Category_Id = @categoryIDRiskFactors;

	INSERT INTO @tempCPP (AppointmentId,AppointmentTestId,AppointmentTime,CategoryId,CategorynName,SavedValue,DateSaved)
	SELECT @appointmentId,@appointmentTestId,@appointmentTime,@categoryIDRiskFactors,@categorynName,
	dbo.[fn_GetCPPData](@col2Visible, a.RiskFactor, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col1Visible, a.ExposureDetails, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col4Visible, DEFAULT, ',', DEFAULT, DEFAULT, a.OnsetAge, a.Unit, a.LifeStage) +
	dbo.[fn_GetCPPData](@col5Visible, DEFAULT, ',', a.StartDateDay, a.StartDateMonth, a.StartDateYear, DEFAULT, DEFAULT) +
	dbo.[fn_GetCPPData](@col6Visible, DEFAULT, ',', a.EndDateDay, a.EndDateMonth, a.EndDateYear, DEFAULT, DEFAULT) +
	dbo.[fn_GetCPPData](@col9Visible, IIF(a.Status = 0, 'InActive', 'Active'), ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT) + 
	dbo.[fn_GetCPPData](@col3Visible, a.Notes, ',', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT)
	,AddDate		
	FROM VP_CPP_RiskFactor a
	INNER JOIN (SELECT MAX(b.Id) Id FROM VP_CPP_RiskFactor b WHERE b.PatientRecordId = @patientId AND DATEDIFF(DAY, b.AddDate, @appointmentTime) >= 0 GROUP BY ParentId) b on a.Id=b.Id

	SELECT * FROM @tempCPP
END
