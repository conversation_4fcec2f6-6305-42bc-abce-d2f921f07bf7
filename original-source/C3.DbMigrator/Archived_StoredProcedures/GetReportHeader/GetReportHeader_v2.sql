﻿-- Explicitly not using a different name for the SP to avoid complex manual SSRS Datasource configuration updates
-- The existing SP is replaced in this case.
CREATE PROCEDURE [dbo].[GetReportHeader] 
	@AppointmentTestsId INT,
	@appointmentTestLogId INT = 0, -- when test is vp we use AppointmentTestSaveLogs and when its worksheet we use AppointmentTestLogs
	@isPreviewReport BIT = 0
AS
BEGIN
	
	DECLARE @patientId INT;	
	DECLARE @appointmentId INT;	
	DECLARE @testId INT;
	DECLARE @bullsEyeId INT;
	DECLARE @bullsEyePath VARCHAR(500);
	DECLARE @vpTestId INT = 29; 
	DECLARE @completedTestStatusId INT = 11;
	DECLARE @isAmendedVP BIT = 0;
	DECLARE @isAmendedWS BIT = 0;
	DECLARE @clinicSendType INT = 6;
	DECLARE @saveType INT = 3 -- send letter for vp
	DECLARE @completedlogId INT = 0 -- send letter for vp
	DECLARE @firstReportQueueAppointmentTestLogId INT = 0 -- for the first queued appointment test id for this test.
	DECLARE @reportQueueChangeStatus BIT = 0; -- for report queue preliminary report
	DECLARE @reportQueueId INT = 0; -- for the current queue item for this test
	DECLARE @appointmentDate DATETIME;
	
	DECLARE @hcNumber nvarchar(20);  
	DECLARE @hcProvinceCode nvarchar(2); 
	DECLARE @hcVersionCode nvarchar(20);  
	DECLARE @hcLabel nvarchar(50) = ''

	SELECT TOP 1 @patientId = a.PatientRecordId, @appointmentId = a.Id, @testId = t.TestId, @appointmentDate = a.appointmentTime 
	FROM AppointmentTests t 
	JOIN Appointments a ON t.AppointmentId = a.Id 
	WHERE t.Id = @AppointmentTestsId;
	
	
	IF @testId = @vpTestId
	BEGIN
	-- VP - we need to check if its amended
		SELECT TOP 1 @completedlogId = ISNULL(sl.Id,0) FROM AppointmentTestSaveLogs sl WHERE sl.AppointmentTestId = @AppointmentTestsId AND sl.SaveType = @saveType ORDER BY sl.LogDate ASC;
		IF @completedlogId > 0 
		BEGIN
			IF @appointmentTestLogId > 0 
			BEGIN
				IF @completedlogId != @appointmentTestLogId
				BEGIN
					SET @isAmendedVP = 1;
				END
			END
			ELSE
			BEGIN
				SET @isAmendedVP = 1;
			END		
		END		
	END
	ELSE -- when its WS
	BEGIN

		-- check if any was in the report queue
		SET @firstReportQueueAppointmentTestLogId = ISNULL((SELECT TOP 1 rq.AppointmentTestLogId FROM ReportQueues rq WHERE rq.AppointmentTestId = @AppointmentTestsId AND rq.ChangeStatus = 1 ORDER BY rq.DateCreated ASC),0);

		IF @appointmentTestLogId <=0
		BEGIN
			SET @appointmentTestLogId = (SELECT TOP 1 l.Id FROM AppointmentTestLogs l JOIN AppointmentTests t ON l.AppointmentId = t.AppointmentId WHERE t.Id = @AppointmentTestsId AND l.[Status] = 0)
		END  

		SELECT TOP 1 @reportQueueId = ISNULL(rq.Id,0), @bullsEyeId = ISNULL(rq.BullsEyeId,0),@reportQueueChangeStatus = ISNULL(rq.ChangeStatus,0)  FROM ReportQueues rq WHERE rq.AppointmentTestId = @AppointmentTestsId AND rq.AppointmentTestLogId = @appointmentTestLogId;
		
		-- set the bullseye
		IF @reportQueueId > 0
		BEGIN
			IF @bullsEyeId > 0
			BEGIN
				SET @bullsEyePath = (SELECT TOP 1 be.svgImageFileName FROM BullEyes be WHERE be.Id = @bullsEyeId ORDER By be.Id DESC);
			END
		END
		ELSE
		BEGIN
			SET @bullsEyePath = (SELECT TOP 1 be.svgImageFileName FROM BullEyes be WHERE be.appointmentId = @appointmentId AND be.testId = @testId ORDER By be.Id DESC);
		END		

		-- check amended
		IF @firstReportQueueAppointmentTestLogId > 0 AND @firstReportQueueAppointmentTestLogId != @appointmentTestLogId
		BEGIN
			SET @isAmendedWS = 1;
		END
	   
	END

	SELECT TOP 1 @hcNumber = isnull(hc.[number],''),       
     @hcProvinceCode = case when hc.provinceCode = 0 then 'AB'  
	   when hc.provinceCode = 1 then 'BC'  
	   when hc.provinceCode = 2 then 'MB' 
	   when hc.provinceCode = 3 then 'NB' 
	   when hc.provinceCode = 4 then 'NL' 
	   when hc.provinceCode = 5 then 'NS'  
	   when hc.provinceCode = 6 then 'NT'  
	   when hc.provinceCode = 7 then 'NU'  
	   when hc.provinceCode = 8 then 'ON'  
       when hc.provinceCode = 9 then 'PE'         
	   when hc.provinceCode = 10 then 'QC'  
	   when hc.provinceCode = 11 then 'SK'  
	   when hc.provinceCode = 12 then 'YT'  
       else '' end,  
	@hcVersionCode = ISNULL(hc.[version],'')  
	FROM DemographicsHealthCards hc JOIN Demographics d ON (hc.DemographicId = d.id)  
    WHERE d.patientrecordid = @patientId   
    ORDER BY hc.id DESC;   

	IF((@hcNumber) <> '')
	BEGIN
		IF(@hcProvinceCode = 'ON')
		BEGIN
			SET @hcLabel = 'HIN: '+@hcNumber+' '+@hcVersionCode
		END
		ELSE
		BEGIN
			SET @hcLabel = 'HIN: '+@hcNumber+' ('+@hcProvinceCode+')'
		END
	END	
		
	
	SELECT
		apps.Id AS AppointmentId, 
		apps.appointmentTime, 
		apps.appointmentStatus,
		appTests.Id AS AppointmentTestsId,	
		appTests.AppointmentTestStatusId,	
		appTests.TestId, 
		appTests.startTime AS TestStartTime,	
		tests.testFullName, 
		tests.testShortName,	
		tests.HrmModality,	
		CAST(CASE WHEN appTests.TestId = @vpTestId THEN 1 ELSE 0 END AS BIT ) AS IsVP,
		CAST(
			CASE 
			WHEN appTests.TestId = @vpTestId THEN @isAmendedVP			
			ELSE @isAmendedWS END AS BIT) AS IsAmended,
		office.name AS Office_Name, 
		office.businessName AS Office_Businessname, 
		office.address1 AS Office_Address1, 
		office.address2 AS Office_Address2, 
        office.city AS Office_City, 
		office.phone AS Office_Phone, 
		office.fax AS Office_Fax, 
		office.country AS Office_Country, 
		office.zip AS Office_Zip, 
		office.state AS Office_State, 
		office.url AS Office_Url, 
		office.province AS Office_Province,
		case when office.province = 0 then 'AB'
			when office.province = 9 then 'PE'
			when office.province = 4 then 'NF'
			when office.province = 1 then 'BC'
			when office.province = 8 then 'ON'
			when office.province = 2 then 'MB'
			when office.province = 5 then 'NS'
			else 'ON' end AS Office_Province_Str,
        office.postalCode AS Office_PostalCode, 
		office.status AS Office_Status, 
		office.HRM_id AS Office_HRMId,
		outlook.leftLogo, 
		outlook.rightLogo, 
		outlook.middleLogo, 
		outlook.headerType,
		demo.FirstName AS Demographics_FirstName, 
		demo.LastName AS Demographics_LastName, 
		demo.MiddleName AS Demographics_MiddleName, 
        demo.DateOfBirth AS Demographics_DOB, 
		demo.AgeAccurate AS Demographics_AgeAccurate,
		demo.Gender AS Demographics_Gender, 		 
		demo.HealthCard AS Demographics_OHIP, 
        demo.HealthCardCode AS Demographics_Version,
		@hcLabel AS Demographics_HCLabel,		
		demo.AddressLine1 As Demographics_AddressLine1,	
		demo.AddressLine2 As Demographics_AddressLine2,
		demo.City As Demographics_City,
		demo.PostalCode As Demographics_PostalCode,
		demo.Province As Demographics_Province,
		demo.Country As Demographics_Country,
		BullsEye = 
		CASE WHEN @bullsEyePath IS NOT NULL THEN 'file:'+(REPLACE(@bullsEyePath, '\', '/'))
		ELSE NULL
		END	,
		dbo.fn_ShowSignatureSubReport(office.name,	tests.testShortName) as ShowSignatureSubReport,
		@reportQueueChangeStatus AS ReportQueueChangeStatus,
		@isPreviewReport AS IsPreviewReport
		FROM AppointmentTests appTests
		JOIN Tests tests ON appTests.TestId = tests.Id
		JOIN Appointments apps ON appTests.AppointmentId = apps.Id
		JOIN dbo.fn_GetPatientInfo(@patientId,@appointmentDate) demo ON apps.PatientRecordId = demo.PatientId
		JOIN Office office ON apps.OfficeId = office.Id
		JOIN OfficeOutlooks outlook ON outlook.OfficeId = office.Id		
		WHERE appTests.Id = @AppointmentTestsId
END