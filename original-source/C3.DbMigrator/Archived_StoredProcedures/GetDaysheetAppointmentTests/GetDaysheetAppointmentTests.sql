CREATE OR ALTER PROCEDURE [dbo].[GetDaysheetAppointmentTests_v3] 
	@officeId INT = NULL,
	@selectedDate DATETIME = NULL, 	
	@showExpected BIT = 0,
	@excludeTestOnly BIT = 0,
	@excludeCancelled BIT = 0,
	@onlyActionOnAbnormal BIT = 0,
	@appointmentStatus INT = NULL,	
	@appointmentId INT = NULL,
	@testGroupId dbo.IntegerList READONLY,
	@filterPatient NVARCHAR(50) = NULL,
	@userId INT = 0,
    @showNoBillingOnly bit = NULL,
    @PageNum int,
    @PageSize int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	--DECLARE @appointmentStatus int = 1;
	DECLARE @isActive int = 1;
	--DECLARE @practiceId int;-- = (SELECT PracticeId FROM Office WHERE Id = @officeId)
	DECLARE @cancelFlag int = 1;
	DECLARE @excludeTestFlag int = 1;
	DECLARE @triageAppStatus int = 16; -- Triage status
	DECLARE @excludeCancelID int = 7; -- App Status Canceled 
	DECLARE @noListStatus int = 1;  -- Skips Cancellation List (Id = 0) and Wait List (Id = 1)

	DECLARE @lookupDate datetime2;

	DECLARE @initdt datetime;
	DECLARE @enddt datetime;

    DECLARE @query nvarchar(max);
    DECLARE @params nvarchar(1000);

	DECLARE @officeIdNew int;

	SET @officeIdNew = @officeId;

	IF(@appointmentId IS NULL OR @appointmentId = 0)
	BEGIN
		set @initdt = cast(convert(varchar, @selectedDate, 112)+' 00:00:00' as datetime)
		set @enddt = cast(convert(varchar, @selectedDate, 112)+' 23:59:59' as datetime)
		SET @lookupDate = CONVERT(char(8), @selectedDate, 112);
	END
	ELSE
	BEGIN
	    SELECT @initdt = cast(concat(CONVERT(char(8), AppointmentTime, 112),' 00:00:00') as datetime),
		       @enddt = cast(concat(CONVERT(char(8), AppointmentTime, 112),' 23:59:59') as datetime),
			   @lookupDate = CONVERT(char(8), AppointmentTime, 112),
		       @officeIdNew = OfficeId
	      FROM Appointments WHERE Id = @appointmentId;
	END;
	DECLARE @practiceId int; 
	SELECT @practiceId = PracticeId FROM Office WHERE Id = @officeIdNew
	DECLARE @tblActiveUserFilters dbo.IntegerList;	-- for filter practice doctor ids
    DECLARE @tblActiveUserFiltersTechs dbo.IntegerList;	-- for filter practice doctor ids

	IF @userId > 0
	BEGIN
		-- get only the practice doctors ids from the user filters
		INSERT INTO @tblActiveUserFilters(IntegerValue)
		SELECT DISTINCT pd.Id FROM UserScheduleViewFilters uf 
		JOIN AspNetUsers u ON uf.UserFilterId = u.UserID
		JOIN PracticeDoctors pd ON u.Id = pd.ApplicationUserId
		WHERE uf.UserId = @userId 
		AND uf.IsActive = 1
		AND uf.OfficeId = @officeIdNew
		AND u.CerebrumUserType = 5 -- means a doctor
		AND pd.ApplicationUserId IS NOT NULL;

		-- get only the practice techs user ids from the user filters
		INSERT INTO @tblActiveUserFiltersTechs(IntegerValue)
		SELECT DISTINCT u.UserID FROM UserScheduleViewFilters uf 
		JOIN AspNetUsers u ON uf.UserFilterId = u.UserID
		JOIN dbo.fn_GetTechnicianTypes() tp ON u.CerebrumUserType = tp.Id		
		WHERE uf.UserId = @userId 
		AND uf.IsActive = 1
		AND uf.OfficeId = @officeIdNew					
	END
	
	-- Ticket #12272 - Commented as table is empty, should not be populated with all practice's doctors but skipped completely in the join below
	-- check if the user has active filters, if none then just get the defaults
	--IF NOT EXISTS (SELECT * FROM @tblActiveUserFilters) 
	--BEGIN
	--	 get only the practice doctors ids who are associated with  this office
	--	INSERT INTO @tblActiveUserFilters(IntegerValue)
	--	SELECT pd.Id FROM PracticeDoctors pd WHERE pd.PracticeId = @practiceId
	--END;

	DECLARE @appTestResources TABLE
	   ( id                        int identity(1,1) not null,
	     appId                     int not null,
		 testId                    int not null,
		 appointmentTestId         int not null,		     
		 appointmentTestTime       datetime2 null,
		 assignedUserId            int not null,		 
		 practiceDoctorId          int not null,
		 qtyVPbooked               int not null
		 ,primary key (id)
	   );

	DECLARE @appointmentsCore appointmentsCoreType;
	   
	DECLARE @appointments TABLE
	   ( id                        int identity(1,1) not null,
	     app_id                    int not null,
		 practiceid                int null,
	     officeid                  int null,
		 appointmentTime           datetime2 null,
		 arrivedTime               nvarchar(10) null,
		 leftTime                  nvarchar(10) null,
         appointmentPurpose        nvarchar(500) null,
         appointmentStatus         int null,
         appointmentNotes          nvarchar(1000) null,
         appointmentRegistrar      int null,
         MWLUrl                    nvarchar(2000) null,
         MWLSentFlag               bit null,
         actionOnAbnormal          bit null,
         bookingConfirmation       bit null,
         roomNumber                nvarchar(10) null,
         PracticeDoctorId          int null,
         billStatusId              int null,
         OpeningStatement          nvarchar(500) null,
         referralDoctorId          int null,
		 ReferralDoctorAddressId   int null,
		 ReferralDoctorPhoneId     int null,
		 AppointmentTypeId         int null,
		 AppointmentType           nvarchar(100) null,
		 AppointmentTypeParentId   int null,
	     appointmentConfirmation   int null,
         appointmentPaymentMethod  int null,
         TriageUrgencyId           int null,
         TriageStatusId            int null,
         IsActive                  bit null,
         PatientRecordId           int null,
		 RecordType				   smallint null,
         DateCreated               datetime2 null,
         LastModified              datetime2 null,
		 PracticeDoctor            nvarchar(100) null,
		 IsImported                bit null,
		 qtyAppByTime              int null,
         TotalRecords              int null,
		 AppointmentPriorityId     int null,
		 FhirId					   uniqueidentifier null,
		 primary key (id)
	   );

	SET @query = '';

	SET @query = 'select [Id]
						,[OfficeId]
						,[appointmentTime]
						,[ArrivedTime]
						,[LeftTime]
						,[appointmentPurpose]
						,[appointmentStatus]
						,[appointmentNotes]
						,[appointmentRegistrar]
						,[MWLUrl]
						,[MWLSentFlag]
						,[actionOnAbnormal]
						,[bookingConfirmation]
						,[roomNumber]
						,[PracticeDoctorId]
						,[billStatusId]
						,[OpeningStatement]
						,[referralDoctorId]
						,[ReferralDoctorAddressId] 
						,[ReferralDoctorPhoneNumberId]
						,[AppointmentTypeId]
						,[appointmentConfirmation]
						,[appointmentPaymentMethod]
						,[TriageUrgencyId]
						,[TriageStatusId]
						,[IsActive]
						,[PatientRecordId]
						,[RecordType]
						,[DateCreated]
						,[LastModified]
						,IsImported
						,count(*) over (partition by officeid, appointmenttime, PracticeDoctorId) as qtyAppByTime
						,AppointmentPriorityId
						,FhirId
					FROM [dbo].[Appointments] apps '+
					CASE WHEN ( ( @appointmentId IS NULL OR @appointmentId = 0) AND EXISTS (SELECT * FROM @tblActiveUserFilters) ) THEN -- Ticket 12272
					'JOIN @tblActiveUserFilters uf ON apps.PracticeDoctorId = uf.IntegerValue ' ELSE '' END+'
				   WHERE apps.appointmentTime BETWEEN @initdt and @enddt 
		           AND   apps.OfficeId = @officeIdNew 
                ORDER BY OfficeID ASC, apps.appointmenttime ASC ';

    SET @params = N'@initdt datetime, @enddt datetime, @officeIdNew int, @tblActiveUserFilters IntegerList READONLY';

    INSERT INTO @appointmentsCore(app_id       
								,officeid                 
								,appointmentTime           
								,arrivedTime               
								,leftTime                  
								,appointmentPurpose        
								,appointmentStatus         
								,appointmentNotes          
								,appointmentRegistrar      
								,MWLUrl                    
								,MWLSentFlag               
								,actionOnAbnormal          
								,bookingConfirmation       
								,roomNumber                
								,PracticeDoctorId          
								,billStatusId              
								,OpeningStatement          
								,referralDoctorId
								,ReferralDoctorAddressId 
								,ReferralDoctorPhoneId       
								,AppointmentTypeId
								,appointmentConfirmation   
								,appointmentPaymentMethod  
								,TriageUrgencyId           
								,TriageStatusId            
								,IsActive                  
								,PatientRecordId  
								,RecordType
								,DateCreated               
								,LastModified
								,IsImported
								,qtyAppByTime
								,AppointmentPriorityId
								,FhirId)
								EXEC sp_executesql @query, @params, @initdt = @initdt, @enddt = @enddt, @officeIdNew = @officeIdNew, @tblActiveUserFilters = @tblActiveUserFilters; 

--	select * from @appointmentsCore;

    SET @query = '';
	SET @params = '';

	IF @showExpected = 1 
	begin
		SET @query = N'DECLARE @excludeAppStatuses TABLE
						( 
							appointmentStatus int 
						);
						INSERT INTO @excludeAppStatuses VALUES (0); /* cancellationlist */
						INSERT INTO @excludeAppStatuses VALUES (1); /* waitlist */
						INSERT INTO @excludeAppStatuses VALUES (5); /* left */
						INSERT INTO @excludeAppStatuses VALUES (6); /* missed */
						INSERT INTO @excludeAppStatuses VALUES (7); /* cancelled */					
						INSERT INTO @excludeAppStatuses VALUES (10); /* Orders to transcribe */
						INSERT INTO @excludeAppStatuses VALUES (11); /* Orders done */						
						INSERT INTO @excludeAppStatuses VALUES (12); /* patientLeftAndOrdersDone */ 
						INSERT INTO @excludeAppStatuses VALUES (13); /* Orders to be done */
						INSERT INTO @excludeAppStatuses VALUES (14); /* Flowsheet Completed */
						INSERT INTO @excludeAppStatuses VALUES (15); /* Ready to leave */
						INSERT INTO @excludeAppStatuses VALUES (16); /* triage */'+Char(10)+Char(13);
	end;

   	IF EXISTS(SELECT * FROM @testGroupId)
	begin
        SET @query = @query + N'DECLARE @testtab TABLE ( TestID int not null, primary key (TestID) );

		                        INSERT INTO @testtab SELECT DISTINCT TestId FROM TestGroups tg, @testGroupId tv WHERE GroupId = tv.IntegerValue;  '+Char(10)+Char(13);
    end;

	SET @query = @query + N' ;WITH AppsFull AS (
                                SELECT apps.id
                                      ,apps.[app_Id]
								      ,O.[PracticeId]
								      ,apps.[OfficeId]
								      ,apps.[appointmentTime]
								      ,apps.[ArrivedTime]
								      ,apps.[LeftTime]
								      ,apps.[appointmentPurpose]
								      ,apps.[appointmentStatus]
								      ,apps.[appointmentNotes]
								      ,apps.[appointmentRegistrar]
								      ,apps.[MWLUrl]
								      ,apps.[MWLSentFlag]
								      ,apps.[actionOnAbnormal]
								      ,apps.[bookingConfirmation]
								      ,apps.[roomNumber]
								      ,apps.[PracticeDoctorId]
								      ,apps.[billStatusId]
								      ,apps.[OpeningStatement]
								      ,apps.[referralDoctorId]
								      ,apps.ReferralDoctorAddressId 
								      ,apps.ReferralDoctorPhoneId    
								      ,apps.[AppointmentTypeId]
								      ,apptype.[name] AS AppointmentType
								      ,ISNULL(apptype.AppointmentTypeId,0) AS AppointmentTypeParentId
								      ,apps.[appointmentConfirmation]
								      ,apps.[appointmentPaymentMethod]
								      ,apps.[TriageUrgencyId]
								      ,apps.[TriageStatusId]
								      ,apps.[IsActive]
								      ,apps.[PatientRecordId]
									  ,apps.[RecordType]
								      ,apps.[DateCreated]
								      ,apps.[LastModified]
								      ,isnull(extDocs.firstName,'''') +'' ''+isnull(extDocs.lastName,'''') AS PracticeDoctor
								      ,apps.IsImported 
								      ,apps.qtyAppByTime
									  ,apps.AppointmentPriorityId
									  ,apps.FhirId
								    FROM @appointmentsCore as apps '+
			    --		             CASE WHEN @appointmentId IS NOT NULL AND @appointmentId > 0 THEN ' JOIN PatientRecords precs ON apps.PatientRecordId =precs.Id ' ELSE '' END +
	                                   ' JOIN Office O ON apps.OfficeId=O.Id
									     JOIN AppointmentTypes apptype ON apps.AppointmentTypeId = apptype.Id
									     JOIN PracticeDoctors pracDocs ON apps.PracticeDoctorId = pracDocs.Id
									     JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id '+
                                     CASE WHEN @filterPatient IS NOT NULL OR (@appointmentId IS NULL OR @appointmentId = 0) THEN ' JOIN Demographics demo ON (apps.PatientRecordId = demo.PatientRecordId AND demo.active = 0) ' ELSE '' END+
							    '  WHERE O.PracticeId = pracDocs.PracticeId AND apps.IsActive = @isActive ';

	if @appointmentId is null or @appointmentId = 0
	begin
	    IF @showExpected = 1 
		begin
		   SET @query = @query + N' AND apps.appointmentStatus NOT IN (SELECT appointmentStatus FROM @excludeAppStatuses) ';
		end;
		ELSE IF (@appointmentStatus IS NULL AND @showExpected = 0)
		begin
		    SET @query = @query + N' AND apps.appointmentStatus > @noListStatus ';

			IF @excludeCancelled = 1
			begin
				SET @query = @query + N' AND apps.appointmentStatus <> @excludeCancelID ';
			end
		end;
		ELSE IF @appointmentStatus IS NOT NULL
		begin
			SET @query = @query + N' AND apps.appointmentStatus = @appointmentStatus ';
		end;

		IF @onlyActionOnAbnormal = 1
		BEGIN
				SET @query = @query + N' AND apps.actionOnAbnormal = 1 ';
		END;

		IF @excludeTestOnly = 1
		begin
		    
			SET @query = @query + N' AND apptype.AppointmentTypeId <> @excludeTestFlag '
		end;

/*		SET @query = @query + N' AND apps.cancellationList <> @cancelFlag    (REMOVED BY DEV) */
		SET @query = @query + N' AND apps.appointmentStatus <> @triageAppStatus '
--                               + CASE WHEN @appointmentId IS NOT NULL AND @appointmentId > 0 THEN ' AND O.PracticeId = precs.PracticeId ' ELSE '' END;   
	end;
	else
	begin
		SET @query = @query + N' AND apps.app_Id = @appointmentId '
	end;

    IF @filterPatient IS NOT NULL
        SET @query = @query + N' AND ( lower(demo.lastName) LIKE '''+@filterPatient+'%'''+' OR lower(demo.firstName) LIKE '''+@filterPatient+'%'' ) ';

    -- Filter appointments with Tests from the TestGroup list and/or filter out the tests for the technicians
	IF EXISTS(SELECT * FROM @testGroupId) OR EXISTS (SELECT * FROM @tblActiveUserFiltersTechs)
	begin
		SET @query = @query + N'  AND EXISTS ( SELECT 1 FROM AppointmentTests apptest, AppointmentTestResources res '+
                                 case when EXISTS(SELECT * FROM @testGroupId) 
                                      then ', @testtab tt '+char(13)+' WHERE apptest.TestId = tt.TestID AND ' else ' WHERE  ' end+
                                              ' apptest.AppointmentID = apps.app_Id
                                                AND   apptest.Id = res.AppointmentTestId 
                                                AND   apptest.IsActive = 1 AND res.isActive = 1 '+
                                 case when EXISTS(SELECT * FROM @tblActiveUserFiltersTechs) 
                                      then ' AND res.assignedToUserId IN (SELECT IntegerValue FROM @tblActiveUserFiltersTechs) ' else '' end +' ) ';
	end;

    SET @query = @query + N' )
                            SELECT apps.[app_Id]
								    ,apps.[PracticeId]
								    ,apps.[OfficeId]
								    ,apps.[appointmentTime]
								    ,apps.[ArrivedTime]
								    ,apps.[LeftTime]
								    ,apps.[appointmentPurpose]
								    ,apps.[appointmentStatus]
								    ,apps.[appointmentNotes]
								    ,apps.[appointmentRegistrar]
								    ,apps.[MWLUrl]
								    ,apps.[MWLSentFlag]
								    ,apps.[actionOnAbnormal]
								    ,apps.[bookingConfirmation]
								    ,apps.[roomNumber]
								    ,apps.[PracticeDoctorId]
								    ,apps.[billStatusId]
								    ,apps.[OpeningStatement]
								    ,apps.[referralDoctorId]
								    ,apps.ReferralDoctorAddressId 
								    ,apps.ReferralDoctorPhoneId    
								    ,apps.[AppointmentTypeId]
								    ,apps.AppointmentType
								    ,AppointmentTypeParentId
								    ,apps.[appointmentConfirmation]
								    ,apps.[appointmentPaymentMethod]
								    ,apps.[TriageUrgencyId]
								    ,apps.[TriageStatusId]
								    ,apps.[IsActive]
								    ,apps.[PatientRecordId]
									,apps.[RecordType]
								    ,apps.[DateCreated]
								    ,apps.[LastModified]
								    ,apps.PracticeDoctor
								    ,apps.IsImported 
								    ,apps.qtyAppByTime
                                    ,CountTotalRecs.TotalRecords
									,apps.AppointmentPriorityId
									,apps.FhirId
                                 FROM AppsFull apps
                          CROSS APPLY ( SELECT COUNT(*) as TotalRecords FROM AppsFull ) CountTotalRecs
                             ORDER BY apps.ID 
                               OFFSET (@PageNum-1) * @PageSize ROWS
                           FETCH NEXT @PageSize ROWS ONLY OPTION (RECOMPILE)';  --Original Order by per AppointmentTime is applied to the IDENTITY INSERT on @appoitmentsCore table

--	SET @params = N'@isActive int, @initdt datetime, @enddt datetime, @officeIdNew int, @appointmentStatus int, @appointmentId int, @practiceDoctorId int, @excludeTestFlag int, @cancelFlag int, @triageAppStatus int';
	SET @params = N'@appointmentsCore appointmentsCoreType READONLY, @tblActiveUserFiltersTechs IntegerList READONLY, @testGroupId IntegerList READONLY, @isActive int, @noListStatus int, @excludeCancelID int, @appointmentStatus int, @appointmentId int, @excludeTestFlag int, @cancelFlag int, @triageAppStatus int, @PageNum bigint, @PageSize bigint';

--	select @query;
--	print @params;

    INSERT INTO @appointments(app_id       
							,practiceid             
							,officeid                 
							,appointmentTime           
							,arrivedTime               
							,leftTime                  
							,appointmentPurpose        
							,appointmentStatus         
							,appointmentNotes          
							,appointmentRegistrar      
							,MWLUrl                    
							,MWLSentFlag               
							,actionOnAbnormal          
							,bookingConfirmation       
							,roomNumber                
							,PracticeDoctorId          
							,billStatusId              
							,OpeningStatement          
							,referralDoctorId   
							,ReferralDoctorAddressId 
							,ReferralDoctorPhoneId       
							,AppointmentTypeId
							,AppointmentType
							,AppointmentTypeParentId
							,appointmentConfirmation   
							,appointmentPaymentMethod  
							,TriageUrgencyId           
							,TriageStatusId            
							,IsActive                  
							,PatientRecordId     
							,RecordType
							,DateCreated               
							,LastModified
							,PracticeDoctor
							,IsImported
							,qtyAppByTime
                            ,TotalRecords
							,AppointmentPriorityId
							,FhirId)
							EXEC sp_executesql @query, @params, @appointmentsCore = @appointmentsCore, @isActive = @isActive, @noListStatus = @noListStatus, 
							                   @excludeCancelID = @excludeCancelID, @appointmentStatus = @appointmentStatus, @appointmentId = @appointmentId, 
											   @excludeTestFlag = @excludeTestFlag, @cancelFlag = @cancelFlag, @triageAppStatus = @triageAppStatus, 
                                               @PageNum = @PageNum, @PageSize = @PageSize, @testGroupId = @testGroupId, @tblActiveUserFiltersTechs = @tblActiveUserFiltersTechs;
											   
--	select * from @appointments;

INSERT INTO @appTestResources(appId,appointmentTestId,testId,appointmentTestTime,assignedUserId,practiceDoctorId,qtyVPbooked)
	SELECT att.AppointmentId, att.Id, att.TestId,att.startTime, atr.assignedToUserId, apps.PracticeDoctorId, 
	       count(*) over (partition by apps.PracticeDoctorId, att.startTime) as qtyVPbooked
  	  FROM AppointmentTestResources atr 
	  JOIN AppointmentTests att ON atr.AppointmentTestId = att.Id
	  JOIN @appointmentsCore apps ON att.AppointmentId = apps.app_Id
	 WHERE apps.IsActive = 1 AND att.TestId = 29 AND att.IsActive = 1 
     AND   atr.isActive = 1 AND atr.assignedToUserId IS NOT NULL
 	 AND   CONVERT(char(8), att.startTime, 112) = @lookupDate;

    SELECT apps.[app_id] as [Id]	    
		    ,apps.[practiceid] AS PracticeId
		    ,apps.[OfficeId]
		    ,apps.[appointmentTime]
		    ,apps.[ArrivedTime]
		    ,apps.[LeftTime]
		    ,apps.[appointmentPurpose]
		    ,apps.[appointmentStatus]
		    ,apps.[appointmentNotes]
		    ,apps.[appointmentRegistrar]
		    ,apps.[MWLUrl]
		    ,apps.[MWLSentFlag]
		    ,apps.[actionOnAbnormal]
		    ,apps.[bookingConfirmation]
		    ,apps.[roomNumber]
		    ,apps.[PracticeDoctorId]
		    ,isnull((SELECT top 1 2 FROM BillDetails WHERE appointmentId = apps.app_Id),0) AS billStatusId
	        ,billstat.[name] AS BillStatus
	        ,billstat.color AS BillStatusColor
		    ,apps.[OpeningStatement]
		    ,apps.[referralDoctorId]
		    ,extDocs.ReferralDoctor -- referral doctor
		    ,extDocs.RefDocOHIPPhysicianId -- referral doctor
		    ,extDocs.CPSO AS RefDocCPSO -- referral doctor
		    ,apps.ReferralDoctorAddressId
		    ,isnull(extDocs.addressLine1,'') AS ReferralDoctorAddress 
		    ,apps.ReferralDoctorPhoneId
		    ,ISNULL((SELECT top 1 faxNumber FROM ExternalDoctorPhoneNumbers WHERE Id = apps.ReferralDoctorPhoneId) ,'') AS ReferralDoctorFaxNumber		
		    ,ISNULL((SELECT TOP 1 isnull(CAST(ed.Id as nvarchar(50)),'')
			+'|'+ isnull(ed.OHIPPhysicianId,'')
			+'|'+ isnull(ed.CPSO,'')
			+'|'+ isnull(ad.faxNumber,'')
			+'|'+ isnull(ad.addressLine1,'')
			+'|'+ isnull(ed.firstName,'')+' '+isnull(ed.lastName,'')
			            FROM ExternalDoctors as ed 
				    	    JOIN DemographicsFamilyDoctors fd ON ed.Id = fd.ExternalDoctorId AND fd.IsActive = 1 AND fd.IsRemoved = 0 
                            LEFT JOIN (
                                SELECT ad.Id, ad.ExternalDoctorId, ad.addressLine1, pn.faxNumber 
                                    FROM ExternalDoctorAddresses as ad 
                                    JOIN ExternalDoctorLocations edl ON edl.ExternalDoctorAddressId = ad.Id AND edl.IsActive = 1
                                    JOIN ExternalDoctorPhoneNumbers pn ON edl.ExternalDoctorPhoneNumberId = pn.Id AND pn.IsActive = 1
                                    JOIN PatientLocations pl ON (pl.ExternalDoctorLocationId = edl.Id AND pl.IsActive = 1)
                                    WHERE pn.faxNumber IS NOT NULL
                                    AND pl.PatientRecordId = demo.PatientRecordId
                                    AND ad.IsActive = 1
                            ) AS ad ON ad.ExternalDoctorId = ed.Id
				        WHERE fd.DemographicId = demo.Id
						),'') AS FamilyDoctor
            ,(SELECT d.phoneNumber
				   ,(CASE d.typeOfPhoneNumber
								    WHEN 0 THEN 'H'
								    WHEN 1 THEN 'C'
								    WHEN 2 THEN 'W'
								    ELSE '' END) as Type
                    ,d.IsActive AS isPrimary
				    FROM DemographicsPhoneNumbers d
			        WHERE phoneNumber IS NOT NULL AND ltrim(rtrim(phoneNumber)) <> '' 
			        AND   d.DemographicId = demo.Id AND d.IsRemoved = 0
					ORDER BY  DemographicId, typeOfPhoneNumber, isActive DESC, Id DESC
					FOR JSON PATH) as PatientPhoneNumbers
		    ,apps.[AppointmentTypeId]
		    ,apps.AppointmentType
		    ,apps.AppointmentTypeParentId
		    ,ISNULL((SELECT [name] FROM AppointmentTypes WHERE id=apps.AppointmentTypeParentId),'') AS AppointmentTypeParent 
		    ,apps.[appointmentConfirmation]
		    ,apps.[appointmentPaymentMethod]
		    ,apps.[TriageUrgencyId]
		    ,apps.[TriageStatusId]
		    ,apps.[IsActive]
		    ,apps.[PatientRecordId]
		    ,apps.[DateCreated]
		    ,apps.[LastModified]
		    ,apps.[PracticeDoctor]
		    ,demo.firstName+ ' '+ISNULL(demo.middleName,'') AS PatientFirstName
		    ,demo.lastName AS PatientLastName
			,ISNULL(demo.PreferredName,'') AS PatientPreferredName
		    ,demo.dateOfBirth AS DateOfBirth
		    ,isnull(bill.ConsultCodeId,0) AS ConsultCodeId
		    ,isnull(bill.DiagnosticCodeId,0) AS DiagnosticCodeId
		    ,isnull(bill.DiagnosticCodeId2,0) AS DiagnosticCodeId2
		    ,isnull(bill.DiagnosticCodeId3,0) AS DiagnosticCodeId3
            ,isnull(bill.ConsultCode, '') AS ConsultCode
		    ,isnull(bill.DiagnosticCode, '') AS DiagnosticCode
		    ,isnull(bill.DiagnosticCode2, '') AS DiagnosticCode2
		    ,isnull(bill.DiagnosticCode3, '') AS DiagnosticCode3
		    ,isnull((SELECT top 1 1 FROM DemographicsHealthCards WHERE DemographicId = demo.Id AND RTRIM(LTRIM(number))<>''),0) AS TotalHealthCards
		    ,isnull((SELECT top 1 1 FROM ReportReceivedAppointments WHERE appointmentId = apps.app_Id),0) AS TotalReferralDocuments
		    ,isnull((SELECT top 1 1 FROM DoctorComments WHERE PatientRecordId = apps.PatientRecordId),0) AS TotalDoctorComments
            ,isnull(extDocs.TotalReferralDocFax,0) as TotalReferralDocFax
		    ,isnull(extDocs.TotalReferralDocNumbers,0) as TotalReferralDocNumbers
            ,cast((case when qtyAppByTime > 1 then 1 else 0 end) AS bit) AS IsDoctorDoubleBook	
		    ,(select count(atr.appointmentTestId)
		        from @appTestResources atr 
		        where atr.practiceDoctorId = apps.PracticeDoctorId 
		        AND   atr.appId            = apps.app_id
		        and   atr.qtyVPbooked > 1) as TotalVPBooked
		    ,cast(0 as bit) as IsImported--apps.IsImported commented out as per Galina's request August 31, 2018
            ,IsNull(apptest.[Id],0) as AppointmentTestId
			,IsNull(apptest.[TestId],0) as TestId
			,IsNull(apptest.[startTime], CAST('1900-01-01' as datetime)) as startTime
			,IsNull(apptest.[AppointmentTestStatusId], 0) as AppointmentTestStatusId
			,IsNull(apptest.[testDuration],0) as testDuration
			,IsNull(apptest.[billStatusId],0) as AppTestbillStatusId
			,IsNull(apptest.[referralDoctorId],0) as AppTestReferralDoctorID
			,apptest.[AccessionNumber]
			,apptest.[PhysicianComments]
			,apptest.[TechnicianComments]
			,IsNull(apptest.[IsActive],0) as AppTestIsActive
			,IsNull(apptest.[DateCreated], CAST('1900-01-01' as datetime)) as AppTestDateCreated
			,IsNull(apptest.[DateUpdated], CAST('1900-01-01' as datetime)) as DateUpdated
			,IsNull(apptest.[SetForReview], 0) as SetForReview
			,IsNull(apptest.[ReassignDocID],0) as ReassignDocID
			,IsNull(apptest.[ReassignDate], CAST('1900-01-01' as datetime)) as ReassignDate
			,ISNULL(apptest.assignedToUserId,0) AS assignedToUserId
			,ISNULL(apptest.performedByUserId,0) AS performedByUserId
			,ISNULL(apptest.permissionId,0) as permissionId
			,ISNULL(apptest.isDoctorRequiredInOffice,0) as isDoctorRequiredInOffice
			,ISNULL(apptest.FirstName,'') + ' '+ ISNULL(apptest.LastName,'') AS UserFullName
			,apptest.testFullName
			,apptest.testShortName
			,ISNULL(apptest.RequireDevice,0) as RequireDevice
			,apptest.Color AS TestStatusColor
			,apptest.Status AS TestStatus
			,apps.RecordType
            ,apps.TotalRecords
			-- Email & ConsentEmail are for redmine ticket #12769 note 4
			,demo.Email
			,demo.ConsentEmail
			,apps.AppointmentPriorityId
			,appriority.PriorityName
			,apps.FhirId
	    FROM @appointments apps
	    JOIN Demographics demo ON apps.PatientRecordId = demo.PatientRecordId AND demo.active = 0
		LEFT JOIN ( SELECT apptest.[Id], apptest.AppointmentID, apptest.testid, apptest.[startTime], apptest.[AppointmentTestStatusId],
                           apptest.[testDuration], apptest.[billStatusId], apptest.[referralDoctorId], apptest.[AccessionNumber],
			               apptest.[PhysicianComments],apptest.[TechnicianComments], apptest.[IsActive], apptest.[DateCreated],
                           apptest.[DateUpdated], apptest.[SetForReview], apptest.[ReassignDocID], apptest.[ReassignDate], 
                           test.testFullName, test.testShortName, test.RequireDevice, res.AppointmentTestId, res.assignedToUserId, 
                           res.performedByUserId, res.permissionId, res.isDoctorRequiredInOffice, u.FirstName, u.LastName, 
                           teststatus.color,teststatus.[status]
                      FROM AppointmentTests apptest
                      JOIN Tests test ON ( apptest.TestId = test.Id )
		              JOIN AppointmentTestResources res ON ( apptest.Id = res.AppointmentTestId )
		         LEFT JOIN AspNetUsers u ON res.assignedToUserId = u.UserID
                      JOIN AppointmentTestStatus teststatus ON apptest.AppointmentTestStatusId = teststatus.Id
                     WHERE res.isActive = 1
                     AND   apptest.IsActive = 1 ) apptest ON (apps.app_id = apptest.AppointmentID)
		LEFT JOIN ( SELECT ed.id, isnull(ed.firstName,'')+' '+isnull(ed.lastName,'') AS ReferralDoctor, 
			            ed.OHIPPhysicianId as RefDocOHIPPhysicianId, ed.CPSO, pn.TotalReferralDocFax, pn.TotalReferralDocNumbers,
						ad.addressLine1, ad.addressLine2, ad.city, ad.province
			        FROM ExternalDoctors as ed 
					LEFT JOIN ExternalDoctorAddresses ad ON (ed.Id = ad.ExternalDoctorId AND ad.IsActive = 1)
						CROSS APPLY (select MAX(CASE WHEN phoneNumber IS NOT NULL THEN 1 ELSE 0 END) as TotalReferralDocNumbers, 
							                MAX(CASE WHEN faxnumber IS NOT NULL THEN 1 ELSE 0 END) as TotalReferralDocFax 
								        from ExternalDoctorPhoneNumbers edpn 
								        where edpn.ExternalDoctorId = ed.id ) as pn
				) as extDocs ON apps.referralDoctorId = extDocs.Id 
		LEFT JOIN ( SELECT AppointmentID
			            ,bill1.ConsultCode as ConsultCodeID
			            ,bill1.DiagnosticCode AS DiagnosticCodeID
	                    ,bill1.DiagnosticCode2 AS DiagnosticCodeID2
	                    ,bill1.DiagnosticCode3 AS DiagnosticCodeID3
  						,CONVERT(NVARCHAR(50),
							CONCAT(
								CASE WHEN ISNULL(ConsultCode,0) > 0 THEN CONVERT(NVARCHAR(10),(SELECT TOP 1 CC.Code FROM ConsultCodes CC WHERE CC.Id=bill1.ConsultCode))+ ',' ELSE '' END
							   ,CASE WHEN ISNULL(ConsultCode2,0) > 0 THEN	CONVERT(NVARCHAR(10),(SELECT TOP 1 CC.Code FROM ConsultCodes CC WHERE CC.Id=bill1.ConsultCode2))+ ',' ELSE '' END
							   ,CASE WHEN ISNULL(ConsultCode3,0) > 0 THEN	CONVERT(NVARCHAR(10),(SELECT TOP 1 CC.Code FROM ConsultCodes CC WHERE CC.Id=bill1.ConsultCode3))+ ',' ELSE '' END
								) ) as ConsultCode
						,CONVERT(NVARCHAR(50),
							(CASE  
								WHEN isnull(bill1.DiagnosticCode,0) > 0
								THEN (SELECT TOP 1 Diagnosis FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode)
								ELSE ''
							END)) as DiagnosticCode
						,CONVERT(NVARCHAR(50),
							(CASE  
								WHEN isnull(bill1.DiagnosticCode,0) > 0
								THEN (SELECT TOP 1 Diagnosis FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode2)
								ELSE ''
							END)) as DiagnosticCode2
						,CONVERT(NVARCHAR(50),
							(CASE  
								WHEN isnull(bill1.DiagnosticCode,0) > 0
								THEN (SELECT TOP 1 Diagnosis FROM DiagnoseCodes WHERE Id = bill1.DiagnosticCode3)
								ELSE ''
							END)) as DiagnosticCode3
	                FROM AppointmentBills as bill1 ) as bill ON apps.app_Id = bill.AppointmentID
		LEFT JOIN BillStatus billstat ON apps.billStatusId = billstat.id
		LEFT JOIN AppointmentPriority appriority ON apps.AppointmentPriorityId = appriority.id
     ORDER BY apps.id ASC, apptest.startTime ASC
	 
END;