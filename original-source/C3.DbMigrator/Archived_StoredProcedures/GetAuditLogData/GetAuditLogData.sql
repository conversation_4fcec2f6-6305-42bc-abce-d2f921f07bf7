﻿

CREATE PROCEDURE [dbo].[GetAuditLogData]
    @p_page int,
	@p_pagesize int,
	@p_startdate datetime,
	@p_enddate datetime,
	@p_UserId int=Null,
	@p_PatientRecordId int=Null,
	@p_IPAddress nvarchar(50)=null,
	@p_text_search nvarchar(1000)=Null
AS
BEGIN

	SET NOCOUNT ON;

	DECLARE @inirec int;
	DECLARE @lastrec int;
    DECLARE @query nvarchar(4000);
	DECLARE @log_params nvarchar(4000) = '';

	DECLARE @logs TABLE
	   ( UserID int,
	     IpAddress nvarchar(50),
		 EventType nvarchar(1),
	     EventDateTime datetime,
		 TableName nvarchar(100),
		 PatientRecordID int,
		 Changes nvarchar(4000)
	   );

	IF @p_page > 1
	   SET @inirec = ((@p_page-1)*@p_pagesize)+1;
	ELSE
	   SET @inirec = 1;

	SET @lastrec = @p_page * @p_pagesize;

	IF (@p_PatientRecordId is not null AND @p_PatientRecordId > 0)
	begin
	   SET @log_params = N' and a.PatientRecordId = '+CAST(@p_PatientRecordId as nvarchar);
	end;

	IF (@p_UserId is not null AND @p_UserId > 0)
	begin
	   SET @log_params = @log_params + N' and a.UserId = '+CAST(@p_UserId as nvarchar);
	end;

	IF (@p_IPAddress is not null AND ltrim(rtrim(@p_IPAddress)) <> '')
	begin
	   SET @log_params = @log_params + N' and a.IpAddress like ''%'+@p_IPAddress+'%''';
	end;

	IF (@p_text_search is not null and ltrim(rtrim(@p_text_search)) <> '')
	begin
	    SET @log_params = @log_params + N' and CONTAINS(ixChanges, '''+@p_text_search+''') and nv like ''%'+@p_text_search+'%''';

		SET @query = N'SELECT top '+cast(@lastrec as nvarchar)+' userid, IpAddress, EventType, EventDateTime, tablename, PatientRecordID, changes
						 FROM dbo.Audits a CROSS APPLY OPENJSON(ixChanges, ''$'') WITH (ov nvarchar(1000) ''$.OV'', nv nvarchar(1000) ''$.NV'')
						WHERE EventDateTime >= @startdate
						and   EventDateTime <= @enddate' + @log_params + ' ORDER BY a.EventDateTime desc';
	end
	else
	begin
		SET @query = N'SELECT top '+cast(@lastrec as nvarchar)+' userid, IpAddress, EventType, EventDateTime, tablename, PatientRecordID, changes
						 FROM dbo.Audits a
						WHERE EventDateTime >= @startdate
						and   EventDateTime <= @enddate' + @log_params + ' ORDER BY a.EventDateTime desc';
	end;

	print @query;

	INSERT INTO @logs (UserID, IpAddress, EventType, EventDateTime, TableName, PatientRecordID, Changes) EXEC sp_executesql @query, N'@startdate datetime, @enddate datetime', @startdate = @p_startdate, @enddate = @p_enddate;

	print cast(@inirec as varchar);
	print cast(@lastrec as varchar);

	SELECT l.UserID, u.LastName+', '+u.FirstName as UserName,  l.IpAddress, l.EventType, l.EventDateTime, l.TableName, l.PatientRecordID,d.lastName+', '+ d.firstName  as PatientFullName, changes
	  FROM (select userid, IpAddress, EventType, EventDateTime, tablename, PatientRecordID, changes, 
	               ROW_NUMBER() OVER (ORDER BY EventDateTime desc) as rn from @logs ) l left join
	       AspNetUsers u on (l.UserID = u.UserID) left join 
		   Demographics d on (l.PatientRecordID = d.PatientRecordId)
	 WHERE l.rn between @inirec and @lastrec
    ORDER BY l.rn asc
    OPTION (RECOMPILE);
/*

	DECLARE @p_UserId int
	DECLARE @p_PatientRecordId int


	-- PARECE QUERY IDEAL
	SELECT id, userid, EventDateTime, tablename, changes
	 FROM dbo.Audits a CROSS APPLY OPENJSON(ixChanges, '$') WITH (ov nvarchar(1000) '$.OV', nv nvarchar(1000) '$.NV')
	WHERE EventDateTime >= convert(datetime2, '2017-06-22 08:00:00', 120) 
	and   EventDateTime <= convert(datetime2, '2017-06-28 08:00:00', 120)
	and   CONTAINS(ixChanges, 'langer')
	and   nv like '%langer%'
	and   a.PatientRecordId = @p_PatientRecordId
	and   a.UserId = @p_UserId
 ORDER BY a.EventDateTime desc
 */

END;



