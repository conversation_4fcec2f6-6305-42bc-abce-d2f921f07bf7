﻿CREATE PROCEDURE [dbo].[GetDoctorRootCategoryPhraseSubItems] 
	@phraseId INT,
	@rootCategoryId INT,	
	@externalDoctorId INT,
	@practiceId INT,
	@practiceTemplateId INT
	
	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @isRootCategory BIT = 0; -- means the phrase id is a rootcategory id for the parent dropdowns
	IF @phraseId = @rootCategoryId
	BEGIN
		SET @isRootCategory = 1;
	END

    -- Insert statements for procedure here
	SELECT 
	 prp.Id AS PracRootCatPhraseId
	,prp.RootCategoryPhraseId
	,prp.PracRootCategoryTempId
	,rcp.RootCategoryId
	,@practiceId AS PracticeId
	,@externalDoctorId AS ExternalDoctorId
	,ISNULL(dcp.PhraseName,rcp.PhraseName) AS PhraseName
	,ISNULL(dcp.PhraseValue,rcp.PhraseValue) AS PhraseValue
	,ISNULL(rcp.ParentId,-1) AS ParentId
	,ISNULL(dcp.Id,0) AS DoctorRootCategoryPhraseId	
	,ISNULL(dcp.DisplayOrder,1000000) AS DisplayOrder
	,CAST(ISNULL(dcp.IsVisible,1) AS BIT) AS IsVisible -- make visible if there is not set by the doctor
	,rcp.IsSubCategory AS IsCategory
	,CAST(1 AS BIT) AS IsVisibleToolbar
	FROM PracticeRootCategoryPhrases prp
	JOIN RootCategoryPhrases rcp ON prp.RootCategoryPhraseId = rcp.Id
	JOIN DoctorRootCategoryPhrases dcp ON prp.RootCategoryPhraseId = dcp.RootCategoryPhraseId AND dcp.PracRootCategoryTempId = @practiceTemplateId
	AND dcp.ExternalDoctorId = @externalDoctorId
	WHERE prp.PracRootCategoryTempId = @practiceTemplateId
	AND ISNULL(rcp.ParentId,0) = CASE WHEN @isRootCategory = 1 THEN 0 ELSE @phraseId END
	AND rcp.RootCategoryId = @rootCategoryId
	AND rcp.IsActive = 1
	AND prp.IsActive = 1
	AND 1 = (CASE WHEN dcp.IsActive IS NULL THEN 1 ELSE dcp.IsActive END)
	ORDER By DisplayOrder, PhraseName
	
END