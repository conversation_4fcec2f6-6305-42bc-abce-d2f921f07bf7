﻿CREATE PROCEDURE GetKioskCheckins
	@practiceId int,
	@officeId int = 0,
	@appDate datetime2 = null
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	DECLARE @checkins TABLE
	(
		CheckinId INT,
		PracticeId INT,
		AppointmentId INT NULL,
		AppointmentTime DateTime2 NULL,
		Heathcard nvarchar (100),
		CheckinTime DateTime2 NULL,
		OfficeId int null,
		OfficeName nvarchar (200) NULL,
		KioskMessageCode int,
		InternalMessage nvarchar (1000),
		ExternalMessage nvarchar (1000)		
	)

	IF @appDate IS NULL
	BEGIN
		SET @appDate = GETDATE();
	END
    
	INSERT INTO @checkins
	SELECT
	ck.Id
	,@practiceId
	,apps.Id AS AppointmentId
	,apps.appointmentTime AS AppointmentTime
	,ck.HealthCard
	,ck.DateCheckedIn
	,o.Id
	,o.[Name]	
	,km.Code
	,km.InternalMessage
	,km.ExternalMessage	
	FROM 
	KioskCheckins ck 
	JOIN <PERSON>kIpAddresses kip ON ck.IpAddress = kip.IpAddress
	JOIN KioskMessages km ON ck.KioskMessageId = km.Id
	JOIN Office o ON kip.OfficeId = o.Id
	LEFT JOIN Appointments apps	ON ck.AppointmentId = apps.Id	
	WHERE CONVERT(char(8), ck.DateCheckedIn, 112) = CONVERT(char(8), @appDate, 112)
	AND ck.IpAddress IS NOT NULL
	AND ck.HealthCard IS NOT NULL
	AND o.Id = CASE WHEN @officeId > 0 THEN @officeId ELSE o.Id END
	and o.PracticeId = @practiceId
	

	SELECT DISTINCT(ck.CheckinId)
	,ck.PracticeId
	,ck.AppointmentId
	,ck.AppointmentTime
	,ck.CheckinTime
	,ck.Heathcard
	,ck.KioskMessageCode
	,ck.InternalMessage
	,ck.ExternalMessage
	,ck.OfficeId
	,ck.OfficeName
	,ck.PracticeId
	,demo.PatientRecordId AS PatientId
	,demo.Id AS DemographicId
	,demo.firstName AS FirstName
	,demo.lastName AS LastName
	,demo.middleName AS MiddleName	
	FROM @checkins ck
	LEFT JOIN 
	(
		SELECT d.PatientRecordId, d.Id, d.firstName, d.lastName, d.middleName, d.aliasFirstName, d.aliasLastName, d.aliasMiddleName, demoHCs.number,demoHCs.expirydate,demoHCs.RowNum
		FROM
		Demographics d
		JOIN 
		(
			SELECT ROW_NUMBER() OVER  (PARTITION BY hc.number ORDER BY hc.Id DESC) AS RowNum, *
			FROM DemographicsHealthCards hc 
			WHERE hc.number IN (SELECT DISTINCT Heathcard FROM @checkins)
		)demoHCs ON d.Id = demoHCs.DemographicId		
		JOIN PatientRecords pr ON d.PatientRecordId = pr.Id
		WHERE demoHCs.number IN (SELECT DISTINCT Heathcard FROM @checkins)
		AND pr.PracticeId = @practiceId		
	) demo ON ck.Heathcard = demo.number
	AND (demo.RowNum IS NULL OR demo.RowNum = 1)
	ORDER BY ck.CheckinTime DESC

	
END