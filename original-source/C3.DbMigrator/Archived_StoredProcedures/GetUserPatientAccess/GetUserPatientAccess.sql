﻿
CREATE PROCEDURE [dbo].[GetUserPatientAccess]
	@userId INT = NULL,
	@patientIds AS dbo.IntegerList READONLY	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;	

	DECLARE @distinctPatientIds TABLE
	   ( Id                        int identity(1,1) not null,
	     PatientId                 int not null		 
		 ,primary key (id)
	   );

	DECLARE @userPatientAccess TABLE
	   ( Id                        int identity(1,1) not null,
	     PatientId                 int not null,
		 UserId                    int not null,
		 PatientAccessStatus        int not null
		 ,primary key (id)
	   );

	INSERT INTO @distinctPatientIds(PatientId)
	SELECT DISTINCT(IntegerValue) FROM @patientIds

	INSERT INTO @userPatientAccess(PatientId,UserId,PatientAccessStatus)
	SELECT PatientId,@userId,dbo.fn_GetUserPatientAccess(@userId,PatientId) FROM @distinctPatientIds	   
	
	
	SELECT 
		upa.UserId,
		upa.PatientId,
		upa.PatientAccessStatus
	FROM @userPatientAccess upa
	
END

