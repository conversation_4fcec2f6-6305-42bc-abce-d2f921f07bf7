﻿CREATE PROCEDURE [dbo].[GetBillingEdtFileSearch] 
	@isBillingAdmin int,
	@practiceId int,
	@practiceDoctorIds varchar(256),
	@rowStart int,
	@rowCount int,
	@totalRowRequest int,
	@sortByColumn varchar(32),
	@sortByOrder varchar(8),
	@filterNotSent int,
	@filterNotConfirmed int,
	@filterError int,
	--@filterServiceStart varchar(16),
	--@filterServiceEnd varchar(16),
	@filterSentDateStart varchar(16),
	@filterSentDateEnd varchar(16),
	@filterConfirmedStart varchar(16),
	@filterConfirmedEnd varchar(16),
	@filterConfirmdlStart varchar(16),
	@filterConfirmdlEnd varchar(16),
	@filterPracticeDoctorIds varchar(256),
	@filterOfficeIds varchar(256)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @totalRow int
	DECLARE @sql varchar(8000)
	DECLARE @sqlCounter nvarchar(max)
	DECLARE @orderBySql varchar(64)
	DECLARE @offsetFetchSql varchar(64)
	DECLARE @orderByColumn varchar(64)
		
	SET @sql = 'select a.id,a.practiceDoctorId,a.officeId,(case when a.edtGroupId=''0000'' then '''' else a.edtGroupId end) edtGroup,
				(case when CHARINDEX(''\'', REVERSE(ISNULL(claimFileName, '''')))<=0 then ISNULL(claimFileName, '''') else RIGHT(ISNULL(claimFileName, ''''), CHARINDEX(''\'', REVERSE(ISNULL(claimFileName, ''''))) -1) end) claimFileName, ISNULL(claimFileName, '''') claimFileFullName,
				(case when CHARINDEX(''\'', REVERSE(ISNULL(confirmedFileName, '''')))<=0 then ISNULL(confirmedFileName, '''') else RIGHT(ISNULL(confirmedFileName, ''''), CHARINDEX(''\'', REVERSE(ISNULL(confirmedFileName, ''''))) -1) end) confirmationFileName, ISNULL(confirmedFileName, '''') confirmationFileFullName,
				(case when c.lastName is null then '''' else upper(c.lastName) end) + '', ''+ISNULL(c.firstName, '''') doctorName,
				ISNULL(e.name, '''') officeName,(case when f.name=''paid'' and a.error=1 then ''Fixed'' else '''' end) status,
				d.serviceDate,a.DateSent,a.DateReceived,a.DateDownloaded,(case when a.Error=1 then ''Y'' else '''' end) Error 
				from Billing_EDTFile a inner join PracticeDoctors b on a.practiceDoctorId=b.Id
				inner join ExternalDoctors c on b.ExternalDoctorId=c.Id
				OUTER APPLY (select top 1 * from BillDetails dd where dd.edtFileId=a.id order by dd.id) d
				left join Office e on a.officeId=e.id
				left join BillStatus f on a.billStatusId=f.id
				where ' + FORMATMESSAGE('b.PracticeId=%d',@practiceId)

	IF @isBillingAdmin=0
		SET @sql = @sql + FORMATMESSAGE(' and a.practiceDoctorId in (SELECT value FROM STRING_SPLIT(''%s'', '','')) ', @practiceDoctorIds)

	--IF (@filterServiceStart <> '')
	--	SET @sql = @sql + FORMATMESSAGE(' and d.serviceDate>=CONVERT(datetime, ''%s'', 101)', @filterServiceStart)
	--IF (@filterServiceEnd <> '')
	--	SET @sql = @sql + FORMATMESSAGE(' and d.serviceDate<=CONVERT(datetime, ''%s'', 101)', @filterServiceEnd + ' 23:59:59')

	IF (@filterSentDateStart <> '')
		SET @sql = @sql + FORMATMESSAGE(' and a.DateSent>=CONVERT(datetime, ''%s'', 101)', @filterSentDateStart)
	IF (@filterSentDateEnd <> '')
		SET @sql = @sql + FORMATMESSAGE(' and a.DateSent<=CONVERT(datetime, ''%s'', 101)', @filterSentDateEnd + ' 23:59:59')

	IF (@filterConfirmedStart <> '')
		SET @sql = @sql + FORMATMESSAGE(' and a.DateReceived>=CONVERT(datetime, ''%s'', 101)', @filterConfirmedStart)
	IF (@filterConfirmedEnd <> '')
		SET @sql = @sql + FORMATMESSAGE(' and a.DateReceived<=CONVERT(datetime, ''%s'', 101)', @filterConfirmedEnd + ' 23:59:59')

	IF (@filterConfirmdlStart <> '')
		SET @sql = @sql + FORMATMESSAGE(' and a.DateDownloaded>=CONVERT(datetime, ''%s'', 101)', @filterConfirmdlStart)
	IF (@filterConfirmdlEnd <> '')
		SET @sql = @sql + FORMATMESSAGE(' and a.DateDownloaded<=CONVERT(datetime, ''%s'', 101)', @filterConfirmdlEnd + ' 23:59:59')

	IF (@filterPracticeDoctorIds is not null and @filterPracticeDoctorIds <> '')
		SET @sql = @sql + FORMATMESSAGE(' and a.practiceDoctorId in (SELECT value FROM STRING_SPLIT(''%s'', '','')) ', @filterPracticeDoctorIds)

	IF (@filterOfficeIds is not null and @filterOfficeIds <> '')
		SET @sql = @sql + FORMATMESSAGE(' and a.officeId in (SELECT value FROM STRING_SPLIT(''%s'', '','')) ', @filterOfficeIds)		

	IF (@filterNotSent = 1)
		SET @sql = @sql + ' and a.DateSent is null'

	IF (@filterNotConfirmed = 1)
		SET @sql = @sql + ' and a.DateReceived is null'

	IF (@filterError = 1)
		SET @sql = @sql + ' and a.Error<>0'

	SET @totalRow = -1
	IF (@totalRowRequest = 1)
	BEGIN
		SET @sqlCounter = 'select @total=count(*) from (' + @sql + ') aa'
		EXEC sp_executesql @sqlCounter, N'@total int OUTPUT', @total = @totalRow OUTPUT
	END

	IF (@totalRow <> 0)
	BEGIN
		SET @orderByColumn = (CASE WHEN @sortByColumn='doctor' THEN 'doctorName'
									WHEN @sortByColumn='office' THEN 'officeName'
									WHEN @sortByColumn='edtgroup' THEN 'edtgroup'
									WHEN @sortByColumn='filename' THEN 'claimFileName'
									WHEN @sortByColumn='confirmationfile' THEN 'confirmationFileName'
									WHEN @sortByColumn='servicedate' THEN 'serviceDate'
									WHEN @sortByColumn='senddate' THEN 'DateSent'
									WHEN @sortByColumn='confirmationdate' THEN 'DateReceived'
									WHEN @sortByColumn='error' THEN 'error'
									WHEN @sortByColumn='status' THEN 'status'
									ELSE 'DateDownloaded' END)

		SET @orderBySql = ' order by ' + @orderByColumn + ' ' + (CASE WHEN @sortByOrder='asc' THEN ' asc' ELSE ' desc' END)
		SET @offsetFetchSql = FORMATMESSAGE(' offset %d rows fetch next %d rows only', @rowStart, @rowCount)

		EXEC ('select id,doctorName doctor,officeName office,edtGroup,practiceDoctorId,officeId,edtGroup,
					claimFileName, claimFileFullName, confirmationFileName, confirmationFileFullName,
					(case when serviceDate is null then '''' else convert(varchar, serviceDate, 101) end) dateService,
					(case when DateSent is null then '''' else convert(varchar, DateSent, 101) end) dateSent,
					(case when DateReceived is null then '''' else convert(varchar, DateReceived, 101) end) dateConfirmed,
					(case when DateDownloaded is null then '''' else convert(varchar, DateDownloaded, 101) end) dateDownloaded,
					error, status
				from (' + @sql + @orderBySql + @offsetFetchSql + ') EdtFile')
	END

	return @totalRow
END