﻿CREATE PROCEDURE [dbo].[GetReportMedications]
	@appointmentTestId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	
	DECLARE @practiceId INT;
	DECLARE @officeId INT;	
	DECLAR<PERSON> @appointmentId INT;
	DECLARE @patientId INT;	
	DECLARE @appointmentTime DATETIME2;	
	DECLAR<PERSON> @practiceDoctorId INT;
	DECLARE @externalDoctorId INT; -- ExternalDoctorId for main appointment doctor	
	DECLARE @ADDEDMED nvarchar(100) = 'Added';
	DECLARE @PRIORMED nvarchar(100) = 'Prior';
	DECLARE @DISCONTINUEDMED nvarchar(100) = 'Discontinued';
	DECLARE @DOSECHANGEMED nvarchar(100) = 'Dose Changed';
	DECLARE @ACTIVEMED nvarchar(100) = 'Active';
	DECLARE @isVP BIT = 0;	
	
	DECLARE @medications TABLE
	(	
	PatientMedicationId int NOT NULL,
	PatientId int NOT NULL,
	MedicationSetId int NOT NULL,
	MedicationName nvarchar(500) NULL,
	DIN nvarchar(50) NULL,
	Dose nvarchar(50) NULL,
	Strength nvarchar(50) NULL,
	Form nvarchar(100) NULL,
	Ingredients nvarchar(1500) NULL,
	SIG nvarchar(50) NULL,
	[Route] nvarchar(100) NULL,	
	MedicationNoDinId int NULL,	
	DateStarted datetime2 NOT NULL,
	DateDiscontinued datetime2 NULL,
	AppointmentId int NOT NULL,
	AppointmentTestId INT NOT NULL,
	AppointmentTime datetime2 NOT NULL,
	ActionType nvarchar(100) NULL,
	RowNum INT NOT NULL	
	);

		
	SELECT TOP 1 
	@practiceId = o.PracticeId,
	@officeId = o.Id,	
	@appointmentId = appTest.appointmentId,	
	@patientId = app.PatientRecordId,
	@appointmentTime = app.appointmentTime,
	@practiceDoctorId = app.PracticeDoctorId,
	@externalDoctorId = pd.ExternalDoctorId,	
	@isVP = CAST(ISNULL(CASE WHEN appTest.TestId = 29 THEN 1 ELSE 0 END,0) as bit)
	FROM AppointmentTests appTest
	JOIN Appointments app ON appTest.AppointmentId = app.Id	
	JOIN PracticeDoctors pd ON app.PracticeDoctorId = pd.Id
	JOIN Office o ON app.OfficeId = o.Id
	WHERE appTest.Id = @appointmentTestId

	
	INSERT INTO @medications
	SELECT 
	 pm.PatientMedicationId 
	,pm.PatientId
	,pm.MedicationSetId
	,pm.MedicationName
	,pm.DIN
	,pm.Dose
	,pm.Strength
	,pm.Form
	,pm.Ingredients	
	,pm.SIG
	,pm.[Route]	
	,pm.MedicationNoDinId
	,pm.DateStarted
	,pm.DateDiscontinued
	,@appointmentTestId
	,@appointmentId
	,@appointmentTime		
	,pm.ActionType
	,pm.RowNum
	FROM fn_GetReportMedications(@practiceId,@patientId,@externalDoctorId,@appointmentTime, @isVP,0) pm	
	
	DECLARE @ActiveMedication NVARCHAR(4000)      
	SELECT @ActiveMedication = COALESCE(@ActiveMedication + ',  ', '') + MedicationName + ' ' + isnull(Dose, '') + ' ' + isNull(SIG,'')  
	FROM @medications  where ActionType = @ACTIVEMED or ActionType = @PRIORMED
	

	SELECT
	m.PatientId
	,m.PatientMedicationId  
	,m.MedicationSetId
	,m.MedicationName
	,@ActiveMedication  as MedicationNameConCat
	,m.DIN
	,m.MedicationNoDinId
	,m.Dose
	,m.Strength
	,m.Ingredients
	,m.Form
	,m.[Route]
	,m.SIG
	,m.DateStarted
	,m.DateDiscontinued	
	,m.AppointmentId
	,m.AppointmentTestId
	,m.AppointmentTime
	,m.ActionType	
	,m.RowNum
	FROM @medications m	
	
END