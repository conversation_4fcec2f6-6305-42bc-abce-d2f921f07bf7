﻿CREATE PROCEDURE [dbo].[GetBillingPerformeter] 
	@xmlParameter XML
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @totalRow int
	DECLARE @isBillingAdmin int
	DECLARE @practiceId int
	DECLARE @practiceDoctorIds varchar(256)
	DECLARE @billingType int
	DECLARE @columnDoctor int
	DECLARE @columnOffice int
	DECLARE @columnEdtGroup int
	DECLARE @columnHospital int
	DECLARE @columnPatient int
	DECLARE @columnAppointment int
	DECLARE @columnAdmission int
	DECLARE @columnServiceCode int
	DECLARE @columnReferralDoctor int
	DECLARE @columnFamilyDoctor int
	DECLARE @columnAppointmentBillStatus int
	DECLARE @columnAdmissionBillStatus int
	DECLARE @columnClaimStatus int
	DECLARE @columnTest int
	DECLARE @columnCohort int
	DECLARE @rowStart int
	DECLARE @rowCount int
	DECLARE @totalRowRequest int
	DECLARE @sortByColumn varchar(32)
	DECLARE @sortByOrder varchar(8)
	DECLARE @filterPracticeDoctorIds varchar(256)
	DECLARE @filterOfficeIds varchar(256)
	DECLARE @filterEdtGroupIds varchar(256)
	DECLARE @filterHospitalIds varchar(256)
	DECLARE @filterTestIds varchar(256)
	DECLARE @filterPatientName varchar(32)
	DECLARE @filterFamilyDoctorName varchar(32)
	DECLARE @filterReferralDoctorName varchar(32)
	DECLARE @filterServiceStart varchar(16)
	DECLARE @filterServiceEnd varchar(16)
	DECLARE @filterReconcileStart varchar(16)
	DECLARE @filterReconcileEnd varchar(16)
	DECLARE @filterSendDateStart varchar(16)
	DECLARE @filterSendDateEnd varchar(16)
	DECLARE @filterAppointmentTypeIds varchar(256)
	DECLARE @filterPaymentIds varchar(256)
	DECLARE @filterAppointmentBillStatusIds varchar(256)
	DECLARE @filterAdmissionBillStatusIds varchar(256)
	DECLARE @filterClaimStatusIds varchar(256)
	DECLARE @filterTestBillCodeIds varchar(256)
	DECLARE @filterConsultCodeIds varchar(256)
	DECLARE @filterCohortIds varchar(256)

	DECLARE @selectSql varchar(4000)
	DECLARE @selectSqlCounter nvarchar(4000)
	DECLARE @tableSql varchar(2048)
	DECLARE @whereSql varchar(2048)
	DECLARE @groupBySql varchar(2048)
	DECLARE @orderBySql varchar(64)
	DECLARE @offsetFetchSql varchar(64)
	DECLARE @orderByColumn varchar(64)

	SELECT TOP 1 @isBillingAdmin = Tbl.Col.value('isBillingAdmin[1]', 'INT'),
					@practiceId = Tbl.Col.value('practiceId[1]', 'INT'),
					@practiceDoctorIds = Tbl.Col.value('practiceDoctorIds[1]', 'varchar(256)'),
					@billingType = Tbl.Col.value('billingType[1]', 'INT'),
					@columnDoctor = Tbl.Col.value('columnDoctor[1]', 'INT'),
					@columnOffice = Tbl.Col.value('columnOffice[1]', 'INT'),
					@columnEdtGroup = Tbl.Col.value('columnEdtGroup[1]', 'INT'),
					@columnHospital = Tbl.Col.value('columnHospital[1]', 'INT'),
					@columnPatient = Tbl.Col.value('columnPatient[1]', 'INT'),
					@columnAppointment = Tbl.Col.value('columnAppointment[1]', 'INT'),
					@columnAdmission = Tbl.Col.value('columnAdmission[1]', 'INT'),
					@columnServiceCode = Tbl.Col.value('columnServiceCode[1]', 'INT'),
					@columnReferralDoctor = Tbl.Col.value('columnReferralDoctor[1]', 'INT'),
					@columnFamilyDoctor = Tbl.Col.value('columnFamilyDoctor[1]', 'INT'),
					@columnAppointmentBillStatus = Tbl.Col.value('columnAppointmentBillStatus[1]', 'INT'),
					@columnAdmissionBillStatus = Tbl.Col.value('columnAdmissionBillStatus[1]', 'INT'),
					@columnClaimStatus = Tbl.Col.value('columnClaimStatus[1]', 'INT'),
					@columnTest = Tbl.Col.value('columnTest[1]', 'INT'),
					@columnCohort = Tbl.Col.value('columnCohort[1]', 'INT'),
					@rowStart = Tbl.Col.value('rowStart[1]', 'INT'),
					@rowCount = Tbl.Col.value('rowCount[1]', 'INT'),
					@totalRowRequest = Tbl.Col.value('totalRowRequest[1]', 'INT'),
					@sortByColumn = Tbl.Col.value('sortByColumn[1]', 'varchar(32)'),
					@sortByOrder = Tbl.Col.value('sortByOrder[1]', 'varchar(8)'),
					@filterPracticeDoctorIds = Tbl.Col.value('filterPracticeDoctorIds[1]', 'varchar(256)'),
					@filterOfficeIds = Tbl.Col.value('filterOfficeIds[1]', 'varchar(256)'),
					@filterEdtGroupIds = Tbl.Col.value('filterEdtGroupIds[1]', 'varchar(256)'),
					@filterHospitalIds = Tbl.Col.value('filterHospitalIds[1]', 'varchar(256)'),
					@filterTestIds = Tbl.Col.value('filterTestIds[1]', 'varchar(256)'),
					@filterPatientName = Tbl.Col.value('filterPatientName[1]', 'varchar(32)'),
					@filterFamilyDoctorName = Tbl.Col.value('filterFamilyDoctorName[1]', 'varchar(32)'),
					@filterReferralDoctorName = Tbl.Col.value('filterReferralDoctorName[1]', 'varchar(32)'),
					@filterServiceStart = Tbl.Col.value('filterServiceStart[1]', 'varchar(16)'),
					@filterServiceEnd = Tbl.Col.value('filterServiceEnd[1]', 'varchar(16)'),
					@filterReconcileStart = Tbl.Col.value('filterReconcileStart[1]', 'varchar(16)'),
					@filterReconcileEnd = Tbl.Col.value('filterReconcileEnd[1]', 'varchar(16)'),
					@filterSendDateStart = Tbl.Col.value('filterSendDateStart[1]', 'varchar(16)'),
					@filterSendDateEnd = Tbl.Col.value('filterSendDateEnd[1]', 'varchar(16)'),
					@filterAppointmentTypeIds = Tbl.Col.value('filterAppointmentTypeIds[1]', 'varchar(256)'),
					@filterPaymentIds = Tbl.Col.value('filterPaymentIds[1]', 'varchar(256)'),
					@filterAppointmentBillStatusIds = Tbl.Col.value('filterAppointmentBillStatusIds[1]', 'varchar(256)'),
					@filterAdmissionBillStatusIds = Tbl.Col.value('filterAdmissionBillStatusIds[1]', 'varchar(256)'),
					@filterClaimStatusIds = Tbl.Col.value('filterClaimStatusIds[1]', 'varchar(256)'),
					@filterTestBillCodeIds = Tbl.Col.value('filterTestBillCodeIds[1]', 'varchar(256)'),
					@filterConsultCodeIds = Tbl.Col.value('filterConsultCodeIds[1]', 'varchar(256)'),
					@filterCohortIds = Tbl.Col.value('filterCohortIds[1]', 'varchar(256)')
	FROM @xmlParameter.nodes('BillingPerformeter') AS Tbl(Col)
	
	SET @selectSql = 'select sum(coalesce(fee,0)) amount,sum(case when billingTypeId=1 then coalesce(fee,0) else 0 end) amountProfessional,
						sum(case when billingTypeId=2 then coalesce(fee,0) else 0 end) amountTechnical,sum(coalesce(ohipAmountPaid,0)) amountPaid,sum(case when billingTypeId=1 then coalesce(ohipAmountPaid,0) else 0 end) amountPaidProfessional,
						sum(case when billingTypeId=2 then coalesce(ohipAmountPaid,0) else 0 end) amountPaidTechnical,sum(coalesce(fee,0)-coalesce(ohipAmountPaid,0)) amountDelta,count(distinct convert(varchar(15),b.appointmentId)+''|~|''+convert(varchar(15),b.hdAdmissionActionId)) quantity'
	SET @selectSqlCounter = ''
	SET @tableSql = ' from billdetails b inner join practicedoctors c on b.practicedoctorid=c.id'
	SET @whereSql = FORMATMESSAGE(' where c.practiceid=%d',@practiceId)
	SET @groupBySql = ''
	SET @orderBySql = ''
	SET @offsetFetchSql = FORMATMESSAGE(' offset %d rows fetch next %d rows only',@rowStart,@rowCount)

	--office billing / hospital billing
	IF (@billingType = 0)
		SET @whereSql = @whereSql + ' and b.hdAdmissionId>0'
	ELSE
		SET @whereSql = @whereSql + ' and b.appointmentId>0'

	--user (not admin) can view his/related doctors data
	IF (@isBillingAdmin = 0)
		SET @whereSql = @whereSql + FORMATMESSAGE(' and c.id in (%s)',@practiceDoctorIds)

	--for specific doctor
	IF (@filterPracticeDoctorIds is not null and @filterPracticeDoctorIds <> '')
		SET @whereSql = @whereSql + FORMATMESSAGE(' and c.id in (%s)',@filterPracticeDoctorIds)

	--for specific payment method
	IF @filterPaymentIds is not null and @filterPaymentIds <> ''
		SET @whereSql = @whereSql + FORMATMESSAGE(' and b.payment in (%s)',@filterPaymentIds)

	--for specific bill status
	IF @filterClaimStatusIds is not null and @filterClaimStatusIds <> ''
		SET @whereSql = @whereSql + FORMATMESSAGE(' and b.billStatusId in (%s)',@filterClaimStatusIds)

	--for specific test billing code
	IF @filterTestBillCodeIds is not null and @filterTestBillCodeIds <> ''
		SET @whereSql = @whereSql + FORMATMESSAGE(' and b.serviceCode in (%s)',''''+REPLACE(REPLACE(@filterTestBillCodeIds,'''',''),',',''',''')+'''')  --Put quotes around each word in comma separated sql string

	--for specific consult code
	IF @filterConsultCodeIds is not null and @filterConsultCodeIds <> ''
		SET @whereSql = @whereSql + FORMATMESSAGE(' and b.serviceCode in (%s)',''''+REPLACE(REPLACE(@filterConsultCodeIds,'''',''),',',''',''')+'''')  --Put quotes around each word in comma separated sql string
		
	--edt service code needed
	IF @columnServiceCode > 0
	BEGIN
		SET @selectSql = @selectSql + ',b.serviceCode serviceCode'
		SET @groupBySql = @groupBySql + ',b.serviceCode'
	END

	--edt group needed
	IF @billingType = 1 and (@columnEdtGroup > 0 or (@filterEdtGroupIds is not null and @filterEdtGroupIds <> ''))
	BEGIN
		IF @columnEdtGroup > 0
		BEGIN
			SET @selectSql = @selectSql + ',(case when b.edtGroupId=''0000'' then '''' else b.edtGroupId end) edtGroup'
			SET @groupBySql = @groupBySql + ',b.edtGroupId'
		END
		IF (@filterEdtGroupIds is not null and @filterEdtGroupIds <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and b.edtGroupId in (%s)',''''+REPLACE(REPLACE(@filterEdtGroupIds,'''',''),',',''',''')+'''')  --Put quotes around each word in comma separated sql string
	END

	--hospital needed
	IF @billingType = 0 and (@columnHospital > 0 or (@filterHospitalIds is not null and @filterHospitalIds <> ''))
	BEGIN
		SET @tableSql = @tableSql + ' inner join Hospitals ss on b.hospitalCode=ss.code'
		IF @columnHospital > 0
		BEGIN
			SET @selectSql = @selectSql + ',ss.name hospital'
			SET @groupBySql = @groupBySql + ',ss.name'
		END
		IF (@filterHospitalIds is not null and @filterHospitalIds <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and ss.Id in (%s)',@filterHospitalIds)
	END

	--test needed
	IF @billingType = 1 and (@columnTest > 0 or (@filterTestIds is not null and @filterTestIds <> ''))
	BEGIN
		DECLARE @testIdVP int
		DECLARE @testSql varchar(512)

		SELECT @testIdVP = id FROM tests WHERE testShortName='VP'
		SET @testSql = ' select xxx.appointmentId,xxx.Id appointmentTestId,0 appointmentBillId,xxx.testId,xxx2.testShortName from AppointmentTests xxx inner join Tests xxx2 on xxx.TestId=xxx2.Id'
		SET @testSql = @testSql +  FORMATMESSAGE(' union select xxx6.appointmentId,0 appointmentTestId,xxx6.Id appointmentBillId,%d testId,''VP'' testShortName from AppointmentBills xxx6',@testIdVP)

		SET @tableSql = @tableSql + ' inner join (' + @testSql + ') xx on b.appointmentId=xx.appointmentId and b.appointmentTestId=xx.appointmentTestId and b.appointmentBillId=xx.appointmentBillId'
		IF @columnTest > 0
		BEGIN
			SET @selectSql = @selectSql + ',xx.testShortName test'
			SET @groupBySql = @groupBySql + ',xx.testShortName'
		END
		IF (@filterTestIds is not null and @filterTestIds <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and xx.testId in (%s)',@filterTestIds)
	END

	--patient name needed / filter by patient name or family doctor name needed / filter by family doctor name
	IF (@columnPatient > 0 or (@filterPatientName is not null and @filterPatientName <> '')) or (@columnFamilyDoctor > 0 or (@columnFamilyDoctor is not null and @filterFamilyDoctorName <> ''))
	BEGIN
		SET @tableSql = @tableSql + ' inner join demographics d on b.PatientRecordId=d.PatientRecordId'
		IF @columnPatient > 0
		BEGIN
			SET @selectSql = @selectSql + ',d.PatientRecordId patientRecordId,(case when d.lastName is null or d.lastName='''' then '''' else d.lastName+'','' end)+(case when d.firstName is null or d.firstName='''' then '''' else d.firstName end) patientName'
			SET @groupBySql = @groupBySql + ',d.PatientRecordId,d.firstName,d.lastName'
		END
		IF (@filterPatientName is not null and @filterPatientName <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and (d.lastName like ''%%%s%%'' or d.firstName like ''%%%s%%'')',@filterPatientName,@filterPatientName)

		IF @columnFamilyDoctor > 0 or (@columnFamilyDoctor is not null and @filterFamilyDoctorName <> '')
		BEGIN
			SET @tableSql = @tableSql + ' left join (select e.demographicId,f.lastName,f.firstName from DemographicsFamilyDoctors e inner join ExternalDoctors f on e.externalDoctorId=f.id where e.IsActive=1 and e.IsRemoved=0) g on d.Id=g.DemographicId'
			IF @columnFamilyDoctor > 0
			BEGIN
				SET @selectSql = @selectSql + ',(case when g.lastName is null or g.lastName='''' then '''' else g.lastName+'','' end)+(case when g.firstName is null or g.firstName='''' then '''' else g.firstName end) familyDoctorName'
				SET @groupBySql = @groupBySql + ',g.firstName,g.lastName'
			END
			IF (@filterFamilyDoctorName is not null and @filterFamilyDoctorName <> '')
				SET @whereSql = @whereSql + FORMATMESSAGE(' and (g.lastName like ''%%%s%%'' or g.firstName like ''%%%s%%'')',@filterFamilyDoctorName,@filterFamilyDoctorName)
		END
	END

	--cohort needed / filter by cohort
	IF (@columnCohort > 0 or (@filterCohortIds is not null and @filterCohortIds <> ''))
	BEGIN
		SET @tableSql = @tableSql + ' inner join cohorts co on b.cohortId=co.Id'
		IF @columnCohort > 0
		BEGIN
			SET @selectSql = @selectSql + ',co.description cohort'
			SET @groupBySql = @groupBySql + ',co.description'
		END
		IF (@filterCohortIds is not null and @filterCohortIds <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and co.Id in (%s)',@filterCohortIds)
	END

	--doctor name needed
	IF @columnDoctor > 0
	BEGIN
		SET @tableSql = @tableSql + ' inner join ExternalDoctors h on c.ExternalDoctorId=h.id'
		SET @selectSql = @selectSql + ',(case when h.lastName is null or h.lastName='''' then '''' else h.lastName+'','' end)+(case when h.firstName is null or h.firstName='''' then '''' else h.firstName end) doctorName'
		SET @groupBySql = @groupBySql + ',h.firstName,h.lastName'
	END

	--referral doctor name needed / filter by referral doctor name
	IF @columnReferralDoctor > 0 or (@filterReferralDoctorName is not null and @filterReferralDoctorName <> '')
	BEGIN
		SET @tableSql = @tableSql + ' left join ExternalDoctors i on b.referralDoctorId=i.id'
		IF @columnReferralDoctor > 0
		BEGIN
			SET @selectSql = @selectSql + ',(case when i.lastName is null or i.lastName='''' then '''' else i.lastName+'','' end)+(case when i.firstName is null or i.firstName='''' then '''' else i.firstName end) referralDoctorName'
			SET @groupBySql = @groupBySql + ',i.firstName,i.lastName'
		END
		IF (@filterReferralDoctorName is not null and @filterReferralDoctorName <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and (i.lastName like ''%%%s%%'' or i.firstName like ''%%%s%%'')',@filterReferralDoctorName,@filterReferralDoctorName)
	END

	--office needed / filter by office
	IF @billingType = 1 and (@columnOffice > 0 or (@filterOfficeIds is not null and @filterOfficeIds <> ''))
	BEGIN
		SET @tableSql = @tableSql + ' left join Office j on b.officeId=j.id'
		IF @columnOffice > 0
		BEGIN
			SET @selectSql = @selectSql + ',j.name office'
			SET @groupBySql = @groupBySql + ',j.name'
		END
		IF @filterOfficeIds is not null and @filterOfficeIds <> ''
			SET @whereSql = @whereSql + FORMATMESSAGE(' and j.id in (%s)',@filterOfficeIds)
	END

	--appointment needed / filter by appointment bill status
	IF @billingType = 1 and (@columnAppointment > 0 or @columnAppointmentBillStatus > 0 or (@filterAppointmentTypeIds is not null and @filterAppointmentTypeIds <> '') or (@filterAppointmentBillStatusIds is not null and @filterAppointmentBillStatusIds <> ''))
	BEGIN
		IF @columnAppointmentBillStatus = 0
			SET @tableSql = @tableSql + ' left join appointments k on b.appointmentId=k.id'
		ELSE
			SET @tableSql = @tableSql + ' left join (select l.id,l.appointmentTime,l.officeId,l.appointmentTypeId,l.billStatusId,m.name appointmentBillStatus from appointments l inner join billstatus m on l.billStatusId=m.id) k on b.appointmentId=k.id'

		IF @columnAppointment > 0
		BEGIN
			SET @selectSql = @selectSql + ',CAST(k.appointmentTime AS DATE) appointmentTime,k.officeId officeId'
			SET @groupBySql = @groupBySql + ',k.id,CAST(k.appointmentTime AS DATE),k.officeId'
		END
		IF @columnAppointmentBillStatus > 0
		BEGIN
			SET @selectSql = @selectSql + ',k.appointmentBillStatus'
			SET @groupBySql = @groupBySql + ',k.appointmentBillStatus'
		END

		IF @filterAppointmentBillStatusIds is not null and @filterAppointmentBillStatusIds <> ''
			SET @whereSql = @whereSql + FORMATMESSAGE(' and k.billStatusId in (%s)',@filterAppointmentBillStatusIds)
		IF @filterAppointmentTypeIds is not null and @filterAppointmentTypeIds <> ''
			SET @whereSql = @whereSql + FORMATMESSAGE(' and k.appointmentTypeId in (%s)',@filterAppointmentTypeIds)
	END
	
	--admission needed / filter by admission bill status
	IF @billingType = 0 and (@columnAdmission > 0 or @columnAdmissionBillStatus > 0 or (@filterAdmissionBillStatusIds is not null and @filterAdmissionBillStatusIds <> ''))
	BEGIN
		IF @columnAdmissionBillStatus = 0
			SET @tableSql = @tableSql + ' left join HDAdmissions n on b.hdAdmissionId=n.id'
		ELSE
			SET @tableSql = @tableSql + ' left join (select o.id,o.DateAdmitted,o.PracticeDoctorId,o.billStatusId,p.name admissionBillStatus from HDAdmissions o inner join billstatus p on o.billStatusId=p.id) n on b.hdAdmissionId=n.id'

		IF @columnAdmission > 0
		BEGIN
			SET @selectSql = @selectSql + ',CAST(n.DateAdmitted AS DATE) admissionDate,n.PracticeDoctorId practiceDoctorId'
			SET @groupBySql = @groupBySql + ',CAST(n.DateAdmitted AS DATE),n.PracticeDoctorId'
		END
		IF @columnAdmissionBillStatus > 0
		BEGIN
			SET @selectSql = @selectSql + ',n.admissionBillStatus'
			SET @groupBySql = @groupBySql + ',n.admissionBillStatus'
		END

		IF @filterAdmissionBillStatusIds is not null and @filterAdmissionBillStatusIds <> ''
			SET @whereSql = @whereSql + FORMATMESSAGE(' and n.billStatusId in (%s)',@filterAdmissionBillStatusIds)
	END

	--claim status needed
	IF @columnClaimStatus > 0
	BEGIN
		SET @tableSql = @tableSql + ' inner join billstatus q on b.billStatusId=q.id'
		SET @selectSql = @selectSql + ',q.name claimBillStatus'
		SET @groupBySql = @groupBySql + ',q.name'
	END

	--filter by service date
	IF (@filterServiceStart is not null and @filterServiceStart <> '') or (@filterServiceEnd is not null and @filterServiceEnd <> '')
	BEGIN
		IF (@filterServiceStart is not null and @filterServiceStart <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and convert(varchar(8),b.serviceDate,112)>=''%s''',@filterServiceStart)
		IF (@filterServiceEnd is not null and @filterServiceEnd <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and convert(varchar(8),b.serviceDate,112)<=''%s''',@filterServiceEnd)
	END
	
	--filter by reconciled date
	IF (@filterReconcileStart is not null and @filterReconcileStart <> '') or (@filterReconcileEnd is not null and @filterReconcileEnd <> '')
	BEGIN
		IF (@filterReconcileStart is not null and @filterReconcileStart <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and convert(varchar(8),b.reconciledDate,112)>=''%s''',@filterReconcileStart)
		IF (@filterReconcileEnd is not null and @filterReconcileEnd <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and convert(varchar(8),b.reconciledDate,112)<=''%s''',@filterReconcileEnd)
	END

	--filter by sent date
	IF (@filterSendDateStart is not null and @filterSendDateStart <> '') or (@filterSendDateEnd is not null and @filterSendDateEnd <> '')
	BEGIN
		SET @tableSql = @tableSql + ' inner join Billing_EDTFile s on b.edtFileId=s.id'
		IF (@filterSendDateStart is not null and @filterSendDateStart <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and convert(varchar(8),s.DateSent,112)>=''%s''',@filterSendDateStart)
		IF (@filterSendDateEnd is not null and @filterSendDateEnd <> '')
			SET @whereSql = @whereSql + FORMATMESSAGE(' and convert(varchar(8),s.DateSent,112)<=''%s''',@filterSendDateEnd)
	END
	
	IF (@groupBySql <> '')
		SET @groupBySql = ' group by ' + RIGHT(@groupBySql, LEN(@groupBySql)-1)


	SET @orderByColumn = (CASE WHEN @sortByColumn='quantity' THEN 'quantity'
								WHEN @sortByColumn='amountpaidprofessional' THEN 'amountPaidProfessional'
								WHEN @sortByColumn='amountpaidtechnical' THEN 'amountPaidProfessional'
								WHEN @sortByColumn='amount' THEN 'amount'
								WHEN @sortByColumn='amountProfessional' THEN 'amountProfessional'
								WHEN @sortByColumn='amountTechnical' THEN 'amountTechnical'
								WHEN @sortByColumn='amountDelta' THEN 'amountDelta'
								WHEN @sortByColumn='doctorname' THEN 'doctorName'
								WHEN @sortByColumn='office' THEN 'office'
								WHEN @sortByColumn='edtgroup' THEN 'edtGroup'
								WHEN @sortByColumn='patientname' THEN 'patientName'
								WHEN @sortByColumn='appointmenttime' THEN 'appointmentTime'
								WHEN @sortByColumn='admissiondate' THEN 'admissionDate'
								WHEN @sortByColumn='servicecode' THEN 'serviceCode'
								WHEN @sortByColumn='referraldoctorname' THEN 'referralDoctorName'
								WHEN @sortByColumn='familydoctorname' THEN 'familyDoctorName'
								WHEN @sortByColumn='appointmentbillstatus' THEN 'appointmentbillstatus'
								WHEN @sortByColumn='admissionbillstatus' THEN 'admissionbillstatus'
								WHEN @sortByColumn='claimbillstatus' THEN 'claimbillstatus'
								WHEN @sortByColumn='test' THEN 'test'
								WHEN @sortByColumn='cohort' THEN 'cohort'
								WHEN @sortByColumn='hospital' THEN 'hospital'
								ELSE 'amountPaid' END)
	IF (CHARINDEX(@orderByColumn, @selectSql) <= 0)
		SET @orderByColumn = 'amountPaid'

	SET @orderBySql = ' order by ' + @orderByColumn + ' ' + (CASE WHEN @sortByOrder='asc' THEN ' asc' ELSE ' desc' END)
	SET @totalRow = -1
	IF (@totalRowRequest = 1)
	BEGIN
		SET @selectSqlCounter = FORMATMESSAGE('select @total=count(*) from (select sum(coalesce(fee,0)) amountPaid %s) billingCounter where amountPaid is not null',@tableSql + @whereSql + @groupBySql)
		EXEC sp_executesql @selectSqlCounter, N'@total int OUTPUT', @total = @totalRow OUTPUT
	END

	IF (@totalRow <> 0)
		EXEC (@selectSql + @tableSql + @whereSql + @groupBySql + @orderBySql + @offsetFetchSql)

	return @totalRow
END