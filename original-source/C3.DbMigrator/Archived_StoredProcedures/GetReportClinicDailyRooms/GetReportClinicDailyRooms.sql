CREATE PROCEDURE [dbo].[GetReportClinicDailyRooms]
	@PracticeId INT,
	@OfficeIds dbo.IntegerList READONLY	
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @OfficeIdsTable AS TABLE (OfficeId INT);

	INSERT INTO @OfficeIdsTable SELECT IntegerValue FROM @OfficeIds;

	IF NOT EXISTS (SELECT TOP 1 1 FROM @OfficeIdsTable)
	BEGIN
		INSERT INTO @OfficeIdsTable
		SELECT Id
		FROM Office O
		WHERE O.PracticeId = @PracticeId;		
	END

	DECLARE @ResultSetTable AS TABLE (
		[UserId] INT NOT NULL,
		[OfficeId] INT NOT NULL,
		[Name] NVARCHAR(300)
	);
	
	INSERT INTO @ResultSetTable ([UserId],[OfficeId],[Name])
	SELECT DISTINCT AU.UserID,UO.OfficeId,ISNULL(AU.LastName+' ','') + ISNULL(AU.FirstName,'') AS [Name]
	FROM ScheduleUsers SU
	JOIN AspNetUsers AU ON AU.UserID = SU.UserId
	JOIN UserOffices UO ON AU.Id=UO.ApplicationUserId	
	WHERE SU.PracticeId = @PracticeId
	AND AU.PracticeID = @PracticeId
	AND AU.[Status] = 0
	AND UO.OfficeId IN (SELECT OfficeId FROM @OfficeIdsTable)
	AND  EXISTS (
		SELECT [value] FROM string_split(SU.Permission,',') WHERE [value] IN (
			SELECT DISTINCT T.permissionId
			FROM PracticeTests PT
			JOIN TestResources T ON PT.TestId = T.TestId
			WHERE PracticeId = @PracticeId
			AND PT.isActive = 1
			AND T.IsActive = 1
			--AND T.TestId = @TestId;
		)
	)

	SELECT [UserId],[OfficeId],[Name]
	FROM @ResultSetTable;
END