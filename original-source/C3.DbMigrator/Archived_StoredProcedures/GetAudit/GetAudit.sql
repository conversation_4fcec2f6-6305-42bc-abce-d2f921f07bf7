﻿-- =============================================
-- Author:		<Divyesh>
-- Create date: <Aug 9,2017>
-- Description:	<Audit Search>
-- =============================================
CREATE PROCEDURE [dbo].[GetAudit](
	@fromDate DATETIME,
	@toDate DATETIME,
	@IPAddress  nvarchar(200)= Null,
	@userId  int= Null,
	@PatientRecordId  int=Null,
	@content  nvarchar(200)= Null
	)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	IF @IPAddress IS NOT NULL AND RTRIM(ltrim(@IPAddress)) <>'' AND @content IS NOT NULL AND RTRIM(ltrim(@content)) <>'' AND @PatientRecordId >0 AND @userId>0 
		SELECT l.[Id]
		  ,l.[UserId]
		  ,u.Em<PERSON> as UserName
		  ,[IpAddress]
		  ,[EventType]
		  ,[EventDateTime]
		  ,[TableName]
		  ,[PatientRecordId]
		  ,[Changes]
	  FROM [Cer30AuditNew].[dbo].[Audits] as l
	  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
	   CROSS APPLY OPENJSON([Changes],'$.changes') 
		WITH(colnumnName varchar(200) '$.CN',OLDValue varchar(100) '$.OV',NewValue varchar(100) '$.NV') p

	  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
	  AND u.UserID=@userId
	  AND l.IpAddress like '%'+@IPAddress+'%' AND l.PatientRecordId=@PatientRecordId
	  AND (ISJSON([Changes])>0 AND (p.OLDValue LIKE '%'+@content+'%' OR p.NewValue LIKE '%'+@content+'%' ))
	  ELSE IF @IPAddress IS NOT NULL AND RTRIM(ltrim(@IPAddress)) <>''  AND @content IS NOT NULL AND RTRIM(ltrim(@content)) <>'' AND @PatientRecordId >0 
		SELECT l.[Id]
		  ,l.[UserId]
		  ,u.Email as UserName
		  ,[IpAddress]
		  ,[EventType]
		  ,[EventDateTime]
		  ,[TableName]
		  ,[PatientRecordId]
		  ,[Changes]
	  FROM [Cer30AuditNew].[dbo].[Audits] as l
	  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
	   CROSS APPLY OPENJSON([Changes],'$.changes') 
		WITH(colnumnName varchar(200) '$.CN',OLDValue varchar(100) '$.OV',NewValue varchar(100) '$.NV') p
	  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
	  AND l.IpAddress like '%'+@IPAddress+'%' AND l.PatientRecordId=@PatientRecordId
	  AND (ISJSON([Changes])>0 AND (p.OLDValue LIKE '%'+@content+'%' OR p.NewValue LIKE '%'+@content+'%' ))
	ELSE IF @IPAddress IS NOT NULL AND RTRIM(ltrim(@IPAddress)) <>'' AND @PatientRecordId >0 AND @userId>0 
		SELECT l.[Id]
		  ,l.[UserId]
		  ,u.Email as UserName
		  ,[IpAddress]
		  ,[EventType]
		  ,[EventDateTime]
		  ,[TableName]
		  ,[PatientRecordId]
		  ,[Changes]
	  FROM [Cer30AuditNew].[dbo].[Audits] as l
	  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
	  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
	  AND u.UserID=@userId
	  AND l.IpAddress like '%'+@IPAddress+'%' AND l.PatientRecordId=@PatientRecordId
   ELSE IF @IPAddress IS NOT NULL AND RTRIM(ltrim(@IPAddress)) <>'' AND @PatientRecordId >0
			SELECT l.[Id]
			  ,l.[UserId]
			  ,u.Email as UserName
			  ,[IpAddress]
			  ,[EventType]
			  ,[EventDateTime]
			  ,[TableName]
			  ,[PatientRecordId]
			  ,[Changes]
		  FROM [Cer30AuditNew].[dbo].[Audits] as l
		  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
		  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
		  AND l.IpAddress like '%'+@IPAddress+'%' 
		  AND l.PatientRecordId=@PatientRecordId
	ELSE IF @content IS NOT NULL AND RTRIM(ltrim(@content)) <>'' AND @PatientRecordId >0
			SELECT l.[Id]
			  ,l.[UserId]
			  ,u.Email as UserName
			  ,[IpAddress]
			  ,[EventType]
			  ,[EventDateTime]
			  ,[TableName]
			  ,[PatientRecordId]
			  ,[Changes]
		  FROM [Cer30AuditNew].[dbo].[Audits] as l
		  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
		  CROSS APPLY OPENJSON([Changes],'$.changes') 
		WITH(colnumnName varchar(200) '$.CN',OLDValue varchar(100) '$.OV',NewValue varchar(100) '$.NV') p

		  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
		  AND l.PatientRecordId=@PatientRecordId
		    AND (ISJSON([Changes])>0 AND (p.OLDValue LIKE '%'+@content+'%' OR p.NewValue LIKE '%'+@content+'%' ))
   ELSE IF @PatientRecordId >0 AND @userId>0
			SELECT l.[Id]
			  ,l.[UserId]
			  ,u.Email as UserName
			  ,[IpAddress]
			  ,[EventType]
			  ,[EventDateTime]
			  ,[TableName]
			  ,[PatientRecordId]
			  ,[Changes]
		  FROM [Cer30AuditNew].[dbo].[Audits] as l
		  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
		  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
		   AND l.IpAddress like '%'+@IPAddress+'%' 
		  AND l.PatientRecordId=@PatientRecordId
	  --Other
	ELSE IF  @IPAddress IS NOT NULL AND RTRIM(ltrim(@IPAddress)) <>'' AND  @PatientRecordId >0 
				SELECT l.[Id]
				  ,l.[UserId]
				  ,u.Email as UserName
				  ,[IpAddress]
				  ,[EventType]
				  ,[EventDateTime]
				  ,[TableName]
				  ,[PatientRecordId]
				  ,[Changes]
			  FROM [Cer30AuditNew].[dbo].[Audits] as l
			  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
			  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
			  AND u.UserID=@userId
			  AND l.PatientRecordId=@PatientRecordId
			  --Other
	ELSE IF @IPAddress IS NOT NULL AND RTRIM(ltrim(@IPAddress)) <>''
				SELECT l.[Id]
				  ,l.[UserId]
				  ,u.Email as UserName
				  ,[IpAddress]
				  ,[EventType]
				  ,[EventDateTime]
				  ,[TableName]
				  ,[PatientRecordId]
				  ,[Changes]
			  FROM [Cer30AuditNew].[dbo].[Audits] as l
			  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
			  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
			  AND l.IpAddress like '%'+@IPAddress+'%' 
   ELSE IF  @PatientRecordId >0
			SELECT l.[Id]
			  ,l.[UserId]
			  ,u.Email as UserName
			  ,[IpAddress]
			  ,[EventType]
			  ,[EventDateTime]
			  ,[TableName]
			  ,[PatientRecordId]
			  ,[Changes]
		  FROM [Cer30AuditNew].[dbo].[Audits] as l
		  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
		  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
		  AND l.PatientRecordId=@PatientRecordId
	  ELSE IF @content IS NOT NULL AND RTRIM(ltrim(@content)) <>''
	  SELECT l.[Id]
		  ,l.[UserId]
		  ,u.Email as UserName
		  ,[IpAddress]
		  ,[EventType]
		  ,[EventDateTime]
		  ,[TableName]
		  ,[PatientRecordId]
		  ,[Changes]
	  FROM [Cer30AuditNew].[dbo].[Audits] as l
	  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
	   CROSS APPLY OPENJSON([Changes],'$.changes') 
		WITH(colnumnName varchar(200) '$.CN',OLDValue varchar(100) '$.OV',NewValue varchar(100) '$.NV') p

	  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)	 
	  AND (ISJSON([Changes])>0 AND (p.OLDValue LIKE '%'+@content+'%' OR p.NewValue LIKE '%'+@content+'%' ))

	  ELSE 	SELECT l.[Id]
		  ,l.[UserId]
		  ,u.Email as UserName
		  ,[IpAddress]
		  ,[EventType]
		  ,[EventDateTime]
		  ,[TableName]
		  ,[PatientRecordId]
		  ,[Changes]
	  FROM [Cer30AuditNew].[dbo].[Audits] as l
	  join [Cer30NEW].[dbo].[AspNetUsers] as u ON u.UserID=l.UserId
	  WHERE (l.EventDateTime> @fromDate AND l.EventDateTime < @toDate)
	  

END
