﻿
CREATE PROCEDURE [dbo].[GetPracticeScheduledUsers_bkp_ticket_5589] 
	@practiceId INT,
	@officeId INT,
	@userId INT = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	
	DECLARE @filterCount INT = 0;
	

    DECLARE @tblActiveUserFilters TABLE -- for the user schedule view filters
	( 
        UserFilterId INT -- the user id from the users that the current user would like to see
	);
	INSERT INTO @tblActiveUserFilters(UserFilterId)
	SELECT uf.UserFilterId FROM UserScheduleViewFilters uf 
	WHERE uf.UserId = @userId 
	AND uf.IsActive = 1
	AND uf.OfficeId = @officeId
		
	SET @filterCount = ISNULL((SELECT COUNT(UserFilterId) FROM @tblActiveUserFilters),0); 
	
	DECLARE @UserPermissions TABLE 
	(
	    ApplicationUserId NVARCHAR(128),
		PermissionIds VARCHAR(8000)
	);

	INSERT INTO @UserPermissions(ApplicationUserId, PermissionIds)
	SELECT U.Id AS ApplicationUserId,
	PermissionIds= STUFF(' '+(SELECT DISTINCT CAST(P.PermissionId AS varchar(10))+ ','   
								FROM RolePermissions P 
								JOIN AspNetUserRoles UR ON P.ApplicationRoleId=UR.RoleId
								JOIN PermissionTypes AS pt ON P.PermissionTypeId =pt.Id
								WHERE UR.UserId=U.Id 
								--AND pt.[Name] IN (SELECT Permission FROM @tblIncludedPermissions)
								AND  pt.[Name] IN ('CanDoTests','VP')
								FOR XMl PATH('') 
								),1,1,'') 
	 FROM AspNetUsers U
	WHERE U.PracticeID=@practiceId AND U.Status=0 

	IF @userId > 0 AND @filterCount > 0
	BEGIN
		
		DECLARE @tblUserDoctorFilters dbo.IntegerList;	-- for filter practice doctor user ids
		DECLARE @tblUserTechFilters dbo.IntegerList;	-- for filter techs user ids

		INSERT INTO @tblUserTechFilters(IntegerValue)
		SELECT DISTINCT u.UserID FROM @tblActiveUserFilters uf 
		JOIN AspNetUsers u ON uf.UserFilterId = u.UserID
		JOIN dbo.fn_GetTechnicianTypes() tp ON u.CerebrumUserType = tp.Id				

		INSERT INTO @tblUserDoctorFilters(IntegerValue)
		SELECT DISTINCT u.UserID FROM @tblActiveUserFilters uf 
		JOIN AspNetUsers u ON uf.UserFilterId = u.UserID
		JOIN PracticeDoctors pd ON u.Id = pd.ApplicationUserId
		WHERE u.CerebrumUserType = 5 -- means a doctor
		AND pd.ApplicationUserId IS NOT NULL
				
		SELECT 
		su.PracticeId
		,ISNULL(pd.Id,0) AS PracticeDoctorId
		,su.UserId
		,u.Id AS ApplicationUserId
		,u.UserName
		,u.FirstName
		,u.LastName
		,u.CerebrumUserType
		,u.[Status]
		,su.startDate AS StartDate
		,su.endDate AS EndDate
		,su.updatedDate
		,UPA.PermissionIds  
		FROM ScheduleUsers su
		JOIN AspNetUsers u ON su.UserId = u.UserId
		JOIN @UserPermissions UPA ON u.Id=UPA.ApplicationUserId
		--JOIN @tblActiveUserFilters uf ON  u.UserID = uf.UserFilterId
		LEFT JOIN PracticeDoctors pd ON u.Id = pd.ApplicationUserId
		WHERE su.PracticeId = @practiceId 
		AND u.[Status] = 0
		--AND (su.endDate IS NULL OR su.endDate>GETDATE())
		AND exists ( select 1 from ScheduleWeekDays sw 
					   where su.Id = sw.ScheduleUserId
					   AND sw.officeId = @officeId )
		AND   1 = 
	   CASE 		
	    WHEN u.UserId IN (SELECT Integervalue FROM @tblUserDoctorFilters) THEN 1
		WHEN u.UserId IN (SELECT Integervalue FROM @tblUserTechFilters) THEN 1 		
		WHEN u.UserId NOT IN (SELECT Integervalue FROM @tblUserDoctorFilters) AND NOT EXISTS(SELECT * FROM @tblUserTechFilters) AND u.CerebrumUserType IN (SELECT Id FROM fn_GetTechnicianTypes()) THEN 1			   
		ELSE 0
		END 	  	
	END
	ELSE
	BEGIN
		SELECT 
		su.PracticeId
		,ISNULL(pd.Id,0) AS PracticeDoctorId
		,su.UserId
		,u.Id AS ApplicationUserId
		,u.UserName
		,u.FirstName
		,u.LastName
		,u.CerebrumUserType
		,u.[Status]
		,su.startDate AS StartDate
		,su.endDate AS EndDate
		,su.updatedDate
		,UPA.PermissionIds  
		FROM ScheduleUsers su
		JOIN AspNetUsers u ON su.UserId = u.UserId
		JOIN @UserPermissions UPA ON u.Id=UPA.ApplicationUserId
		LEFT JOIN PracticeDoctors pd ON u.Id = pd.ApplicationUserId
		WHERE su.PracticeId = @practiceId 
		AND u.[Status] = 0
		--AND (su.endDate IS NULL OR su.endDate>GETDATE())
		AND exists ( select 1 from ScheduleWeekDays sw 
					   where su.Id = sw.ScheduleUserId
					   AND sw.officeId = @officeId )
	END
	
END
















