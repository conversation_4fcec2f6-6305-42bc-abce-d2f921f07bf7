﻿CREATE PROCEDURE [dbo].[GetReportAllergies]
	@appointmentTestId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	
	DECLARE @practiceId INT;
	DECLARE @officeId INT;	
	DECLAR<PERSON> @appointmentId INT;
	DECLARE @patientId INT;	
	DECLAR<PERSON> @appointmentTime DATETIME2;	
	DECLAR<PERSON> @practiceDoctorId INT;
	DECLAR<PERSON> @externalDoctorId INT; -- ExternalDoctorId for main appointment doctor
	DE<PERSON><PERSON><PERSON> @lookupDate datetime2;
	DECLARE @isVisibleAllegies BIT = 0;
	DECLARE @cppCategoryId INT = 8;
	DECLARE @acticeAllergies NVARCHAR(4000);   
	DECLARE @tempAllergy TABLE
	(		
	PatientAllergyId int null,
	PatientId int NOT NULL,	
	MedicationName nvarchar(500) NULL,
	Ingredient nvarchar(500) NULL,
	DisplayName nvarchar(500) NULL,
	Severity nvarchar(50) NULL,	
	DateStarted datetime2 NOT NULL	
	)
	DECLARE @allergies TABLE
	(	
	PatientAllergyId int null,
	PatientId int NOT NULL,	
	MedicationName nvarchar(500) NULL,
	Severity nvarchar(50) NULL,	
	DateStarted datetime2 NOT NULL
	);
		
	SELECT TOP 1 
	@practiceId = o.PracticeId,
	@officeId = o.Id,	
	@appointmentId = appTest.appointmentId,	
	@patientId = app.PatientRecordId,
	@appointmentTime = app.appointmentTime,
	@practiceDoctorId = app.PracticeDoctorId,
	@externalDoctorId = pd.ExternalDoctorId	
	FROM AppointmentTests appTest
	JOIN Appointments app ON appTest.AppointmentId = app.Id	
	JOIN PracticeDoctors pd ON app.PracticeDoctorId = pd.Id
	JOIN Office o ON app.OfficeId = o.Id
	WHERE appTest.Id = @appointmentTestId

	SET @isVisibleAllegies = ISNULL((SELECT TOP 1 cs.[Visible] FROM VP_CPP_Setting cs WHERE cs.DoctorID = @externalDoctorId AND cs.VP_CPP_Category_Id = @cppCategoryId),0);


	IF @isVisibleAllegies = 1
	BEGIN
		INSERT INTO @tempAllergy
		SELECT 	
		 pa.Id	 
		,pa.PatientRecordId		
		,pa.MedicationName
		,pal.Ingredient
		,CASE WHEN pal.Ingredient IS NULL THEN pa.MedicationName ELSE pal.Ingredient END
		,s.[Description]		
		,pa.DateStarted				
		FROM PatientAllergies pa
		JOIN Severity s ON pa.SeverityId = s.Id
		LEFT JOIN PatientAllergyIngredients pal ON pa.Id = pal.PatientAllergyId
		WHERE pa.PatientRecordId = @patientId 
		AND CONVERT(char(8), pa.DateStarted, 112) <= CONVERT(char(8), @appointmentTime, 112)
		AND pa.IsActive = 1
		AND pa.AllergyStatusId = 1
		ORDER BY pa.DateStarted DESC
	END
	
	   
	SELECT @acticeAllergies = COALESCE(@acticeAllergies + ',  ', '') + DisplayName + ' ' + isnull(Severity, '') FROM @tempAllergy
	

	SELECT
	 al.PatientId
	,al.PatientAllergyId  
	,al.MedicationName
	,al.Ingredient
	,al.DisplayName
	,al.Severity
	,al.DateStarted
	,@acticeAllergies  as AllergiesNameConCat	
	FROM @tempAllergy al	
	
END
