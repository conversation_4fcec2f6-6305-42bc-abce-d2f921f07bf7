﻿CREATE PROCEDURE [dbo].[GetDashboardCDMDIAB001] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT, PRIMARY KEY (PatientRecordId))
	DECLARE @DiabetesCoded AS TABLE (PatientRecordId INT)
	DECLARE @DiabetesTextDocumented AS TABLE (PatientRecordId INT)
	DECLARE @Diabetestwobillings AS TABLE (PatientRecordId INT)	
	DECLARE @HbA1cLast2Years AS TABLE (PatientRecordId INT)
	DECLARE @MedicationPresent AS TABLE (PatientRecordId INT)
	DECLARE @OtherCondintions AS TABLE (PatientRecordId INT)
	DECLARE @PreDiabetes AS TABLE (PatientRecordId INT)

	DECLARE @PracticeId INT
	SELECT @PracticeId = PracticeID FROM PracticeDoctors WHERE Id = @PracticeDoctorId

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE D.active = 0
	AND (MRP.PracticeDoctorId = @PracticeDoctorId) --OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	-- Diabetes Coded 
	INSERT INTO @DiabetesCoded
	SELECT PatientRecordId	
	FROM VP_CPP_Problem_List CPP
	JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
	WHERE 			   
	(CPP.DiagnosticCode IN ('250'
							,'DB-610'
							,'46635009', '44054006', '73211009')
	OR CPP.DiagnosticCode LIKE 'E10%'
	OR CPP.DiagnosticCode LIKE 'E11%')
	AND Deleted = 0 AND CPP.UpdateDate IS NULL
	AND PR.PracticeId = @PracticeId

	--- PreDiabetes
	INSERT INTO @PreDiabetes
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
	WHERE 			   
	(CPP.DiagnosticCode IN ('790.2'
							,'DB-61200'
							,'9414007')
		OR CPP.DiagnosticCode LIKE 'E10%'
		OR CPP.DiagnosticCode LIKE 'E11%'	)
	AND Deleted = 0 AND CPP.UpdateDate IS NULL
	AND PR.PracticeId = @PracticeId
	UNION ALL
		SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
	WHERE 			   				
		(CPP.Problem_Description LIKE '%Pre-diabet%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%Prediabet%'
		OR CPP.Problem_Description LIKE '%Pre diabet%'
		OR CPP.Problem_Description LIKE '%Borderline diabet%'
		OR CPP.Problem_Description LIKE '%Impaired Glucose%'
		OR CPP.Problem_Description LIKE '%Impaired Fasting%'
		OR CPP.Problem_Description LIKE '%IGT%'
		OR CPP.Problem_Description LIKE '%IFG%'	)	 
		AND Deleted = 0 AND CPP.UpdateDate IS NULL
		AND PR.PracticeId = @PracticeId
		UNION ALL
		SELECT PatientRecordId 
		FROM VP_CPP_RiskFactor CPP
		JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
		WHERE 			   								 
		(CPP.RiskFactor LIKE '%Pre-diabet%'				-- Excluded Text Snippets
		OR CPP.RiskFactor LIKE '%Prediabet%'
		OR CPP.RiskFactor LIKE '%Pre diabet%'
		OR CPP.RiskFactor LIKE '%Borderline diabet%'
		OR CPP.RiskFactor LIKE '%Impaired Glucose%'
		OR CPP.RiskFactor LIKE '%Impaired Fasting%'
		OR CPP.RiskFactor LIKE '%IGT%'
		OR CPP.RiskFactor LIKE '%IFG%')
		AND Deleted = 0
		AND PR.PracticeId = @PracticeId

	--- Diabetes Exclusion: Other Conditions
	INSERT INTO @OtherCondintions
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
	WHERE 			   
	(CPP.DiagnosticCode IN ('249','256.4','648.8','775.1', '790.29')	)		
	AND Deleted = 0 AND CPP.UpdateDate IS NULL
	AND PR.PracticeId = @PracticeId
	UNION ALL
	SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
	WHERE 			   				
		(CPP.Problem_Description LIKE '%gest%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%PCOS%'
		OR CPP.Problem_Description LIKE '%polycystic ovar%') 
		AND Deleted = 0 AND CPP.UpdateDate IS NULL
		AND PR.PracticeId = @PracticeId
		UNION ALL
		SELECT PatientRecordId 
		FROM VP_CPP_RiskFactor CPP
		JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
		WHERE 			   								 
		(CPP.RiskFactor LIKE '%gest%'						-- Excluded Text Snippets
		OR CPP.RiskFactor LIKE '%PCOS%'
		OR CPP.RiskFactor LIKE '%polycystic ovar%' )
		AND Deleted = 0
		AND PR.PracticeId = @PracticeId

	--- Diabetes documented as text diagnosis or problem
	INSERT INTO @DiabetesTextDocumented
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
	WHERE 			   				
		(CPP.Problem_Description LIKE '%diab%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%DM%'
		OR CPP.Problem_Description LIKE '%t2d%' )
		AND Deleted = 0 AND CPP.UpdateDate IS NULL
		AND PR.PracticeId = @PracticeId
		UNION ALL
		SELECT PatientRecordId 
		FROM VP_CPP_RiskFactor CPP
		JOIN PatientRecords PR ON CPP.PatientRecordId = PR.Id
		WHERE 			   								 
		(CPP.RiskFactor LIKE '%diab%'						-- Excluded Text Snippets
		OR CPP.RiskFactor LIKE '%DM%'
		OR CPP.RiskFactor LIKE '%t2d%') 
		AND Deleted = 0
		AND PR.PracticeId = @PracticeId

	--- HbA1c value > 6.5% in the last 2 years 	
	INSERT INTO @HbA1cLast2Years
	SELECT P.PatientRecordId	
	FROM HL7Result R
	JOIN HL7ReportVersion V ON R.HL7ReportVersionId = V.Id
	JOIN HL7Report RPT ON RPT.Id = V.HL7ReportId
	JOIN HL7Patient P ON RPT.HL7PatientId = P.Id
	JOIN PatientRecords PR ON P.PatientRecordId = PR.Id
	WHERE 
	CONTAINS(testCodeIdentifier,'"*hba1c*" OR "*gluc*"')
	--(testCodeIdentifier LIKE '%hba1c%' OR testCodeIdentifier LIKE '%gluc%')
	AND testResultFloat > 6.5
	AND R.collectionDate > DATEADD(YEAR,-2,GETDATE())	
	AND r.resultStatus IN ('F', 'A', 'C')	
	AND PR.PracticeId = @PracticeId

	--- Medication Present
	INSERT INTO @MedicationPresent
	SELECT P.PatientRecordId	
	FROM PatientMedications P
	JOIN PatientRecords PR ON P.PatientRecordId = PR.Id
	WHERE 
	CONTAINS(MedicationName,'"Acarbose" OR "Glibenclamide" OR "Gliclazide" OR "Glimepiride" OR "insulin" OR "Metformin" OR "Sitagliptin" OR "Tolbutamide"')	
	--(MedicationName LIKE '%Acarbose%' 
	--OR MedicationName LIKE '%Glibenclamide%'
	--OR MedicationName LIKE '%Gliclazide%'
	--OR MedicationName LIKE '%Glimepiride%'
	--OR MedicationName LIKE '%insulin%'
	--OR MedicationName LIKE '%Metformin%'
	--OR MedicationName LIKE '%Sitagliptin%'
	--OR MedicationName LIKE '%Tolbutamide%')
	AND IsActive = 1 and DateDiscontinued is null and DateExpired is null
	AND PR.PracticeId = @PracticeId


	INSERT INTO @Diabetestwobillings
	SELECT patientrecordid from BillDetails B 
	JOIN PatientRecords PR ON B.PatientRecordId = PR.Id 
	WHERE PR.PracticeId = @PracticeId
	AND diagnoseCode = '250' 
	GROUP BY  B.PatientRecordId 	
	HAVING COUNT(B.id) >=2
	
	--- Segments	
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)
	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesCoded)
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId			
		FROM @BasePopulation
		WHERE PatientRecordId not IN (SELECT PatientRecordId FROM @DiabetesCoded)
		and PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesTextDocumented)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @PreDiabetes)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @OtherCondintions)


	INSERT INTO @Segments
	SELECT 3,PatientRecordId	
		FROM @BasePopulation
		WHERE PatientRecordId not IN (SELECT PatientRecordId FROM @DiabetesCoded)
		and PatientRecordId IN (SELECT PatientRecordId FROM @Diabetestwobillings)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @PreDiabetes)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @OtherCondintions)
		
	INSERT INTO @Segments
	SELECT 4,PatientRecordId	
		PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId not IN (SELECT PatientRecordId FROM @DiabetesCoded)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @PreDiabetes)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @OtherCondintions)
		and PatientRecordId IN (SELECT PatientRecordId FROM @HbA1cLast2Years)	
	
	INSERT INTO @Segments
	SELECT 5,PatientRecordId		
		PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId not IN (SELECT PatientRecordId FROM @DiabetesCoded)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @PreDiabetes)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @OtherCondintions)
		and PatientRecordId IN (SELECT PatientRecordId FROM @MedicationPresent)	

	INSERT INTO @Segments
	SELECT 6,PatientRecordId			
		FROM @BasePopulation
		WHERE PatientRecordId not IN (SELECT PatientRecordId FROM @DiabetesCoded)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @PreDiabetes)
		and PatientRecordId not IN (SELECT PatientRecordId FROM @OtherCondintions)
		and ( PatientRecordId IN (SELECT PatientRecordId FROM @MedicationPresent)	
		or PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesTextDocumented)
		or PatientRecordId IN (SELECT PatientRecordId FROM @Diabetestwobillings)
		or PatientRecordId IN (SELECT PatientRecordId FROM @HbA1cLast2Years)
		)


	INSERT INTO @Segments
	SELECT 7,PatientRecordId	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @DiabetesCoded)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @OtherCondintions)
	
	INSERT INTO @Segments
	SELECT 8,PatientRecordId	
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @DiabetesCoded)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @PreDiabetes)
	
	--INSERT INTO @Segments
	--SELECT 9,PatientRecordId			
	--	FROM @BasePopulation
	--	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @DiabetesCoded)
	
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments	

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log
END
