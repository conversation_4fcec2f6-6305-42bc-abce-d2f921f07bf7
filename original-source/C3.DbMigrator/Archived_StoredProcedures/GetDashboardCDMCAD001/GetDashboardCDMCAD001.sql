﻿CREATE PROCEDURE [dbo].[GetDashboardCDMCAD001] (@PracticeDoctorId INT = NULL)
AS
BEGIN	
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs
	
	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @CadCoded AS TABLE (PatientRecordId INT)
	DECLARE @CADDocumented AS TABLE (PatientRecordId INT)
	DECLARE @CADBilled AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)
	
	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE 
	D.active = 0														-- Pastient is active		
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) >= 18	-- Patient is 18 and over	
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	--- CAD Coded	
	INSERT INTO @CadCoded
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('D3-13040', 'D3-10030', 'D3-14016', 'D3-14017', '414545008', '413439005', '413838009', '194828000')
              OR CPP.DiagnosticCode LIKE '410%'
              OR CPP.DiagnosticCode LIKE '411%'
              OR CPP.DiagnosticCode LIKE '412%'
              OR CPP.DiagnosticCode LIKE '413%'
              OR CPP.DiagnosticCode LIKE '414%'
              OR CPP.DiagnosticCode LIKE '429%'
              OR CPP.DiagnosticCode LIKE 'I20%'
              OR CPP.DiagnosticCode LIKE 'I21%'
              OR CPP.DiagnosticCode LIKE 'I22%'
              OR CPP.DiagnosticCode LIKE 'I23%'
              OR CPP.DiagnosticCode LIKE 'I24%'
              OR CPP.DiagnosticCode LIKE 'I25%'
	)
    AND Deleted = 0 AND UpdateDate IS NULL


	--- CAD documented as text diagnosis or problem 
	INSERT INTO @CADDocumented
	SELECT PatientRecordId 	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   				
		CPP.Problem_Description LIKE '%CAD%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%C.A.D.%'
		OR CPP.Problem_Description LIKE '%coronary artery dis%'
		OR CPP.Problem_Description LIKE '%CVD%'
		OR CPP.Problem_Description LIKE '%C.V.D.%'
		OR CPP.Problem_Description LIKE '%cardiovascular disease%'
		OR CPP.Problem_Description LIKE '%IHD%'
		OR CPP.Problem_Description LIKE '%I.H.D.%'
		OR CPP.Problem_Description LIKE '%ischemic hrt dis%'
		OR CPP.Problem_Description LIKE '%ischemic heart dis%'
		OR CPP.Problem_Description LIKE '%M.I.%'
		OR CPP.Problem_Description LIKE '%myocardial%'		
		AND Deleted = 0 AND UpdateDate IS NULL
		UNION ALL
		SELECT PatientRecordId 
		FROM VP_CPP_RiskFactor CPP
		WHERE 			   				
		CPP.RiskFactor LIKE '%CAD%'				-- Excluded Text Snippets
		OR CPP.RiskFactor LIKE '%C.A.D.%'
		OR CPP.RiskFactor LIKE '%coronary artery dis%'
		OR CPP.RiskFactor LIKE '%CVD%'
		OR CPP.RiskFactor LIKE '%C.V.D.%'
		OR CPP.RiskFactor LIKE '%cardiovascular disease%'
		OR CPP.RiskFactor LIKE '%IHD%'
		OR CPP.RiskFactor LIKE '%I.H.D.%'
		OR CPP.RiskFactor LIKE '%ischemic hrt dis%'
		OR CPP.RiskFactor LIKE '%ischemic heart dis%'
		OR CPP.RiskFactor LIKE '%M.I.%'
		OR CPP.RiskFactor LIKE '%myocardial%'
		AND Deleted = 0

	--- Two or more bills containing a CAD diagnosis
	INSERT INTO @CADBilled
	SELECT PatientRecordId	
	FROM BillDetails B
	WHERE B.diagnoseCode IN ('410','411','412','413','414','429')
	GROUP BY PatientRecordId
	HAVING COUNT(B.Id) >= 2
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @CADCoded)	

	INSERT INTO @Segments
	SELECT 2,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @CADCoded)	
	AND PatientRecordId IN (SELECT PatientRecordId FROM @CADDocumented)

	
	INSERT INTO @Segments
	SELECT 3,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @CADCoded)	
	AND PatientRecordId IN (SELECT PatientRecordId FROM @CADBilled)
	
	INSERT INTO @Segments
	SELECT 4,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @CADCoded)	
	AND PatientRecordId IN (SELECT PatientRecordId FROM @CADDocumented
								UNION ALL
								SELECT PatientRecordId FROM @CADBilled)		

	INSERT INTO @Segments
	SELECT 5,PatientRecordId	
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @CADCoded)
	
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments	
	
	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END

