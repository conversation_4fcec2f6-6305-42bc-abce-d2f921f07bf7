﻿
CREATE   PROCEDURE [dbo].[GetReportQueueById] 
	@ReportQueueId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	IF OBJECT_ID('#tempReportQueues') IS NOT NULL 
		DROP Table #tempReportQueues

	SELECT [Id]
			,[AppointmentTestId]
			,[AppointmentTestLogId]
			,[IsDoc<PERSON>]
			,[ReportType]
			,[LetterType]
			,[UserId]
			,[UserFullName]
			,[IpAddress]
			,[IsAmended]
			,[ChangeStatus]
			,[DateCreated]
			,[DateLastModified]
			,[SendStatusId]
			,[IsVP]
			,[NumberOfAttempts]
			,[IsResend]
	INTO #tempReportQueues
	FROM [dbo].[ReportQueues]
	WHERE id = @ReportQueueId
	ORDER BY [DateCreated] ASC

	SELECT * FROM #tempReportQueues

	SELECT [Id]
			,[ReportQueueId]
			,[Doctor]
	FROM [dbo].[ReportQueueResends]
	WHERE ReportQueueId in (SELECT ID FROM #tempReportQueues)

	DROP Table #tempReportQueues
END
