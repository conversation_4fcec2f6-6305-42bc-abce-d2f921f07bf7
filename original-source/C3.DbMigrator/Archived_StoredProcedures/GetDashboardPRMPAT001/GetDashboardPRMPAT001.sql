﻿CREATE PROCEDURE [dbo].[GetDashboardPRMPAT001] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT,Status INT)
	DECLARE @Rostered AS TABLE (PatientRecordId INT)
	DECLARE @Appointments AS TABLE (PatientRecordId INT, LastAppDate DATETIME2)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	--- 0 = Active, 1 = NotActive, 2 = Other, 3 = Deceased  
	INSERT INTO @BasePopulation	
	SELECT		
	D.PatientRecordId,D.active AS [Status]	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId,MRP.id,D.active

	INSERT INTO @Rostered
	SELECT
	D.PatientRecordId	
	FROM Demographics D
	JOIN @BasePopulation BP ON BP.PatientRecordId = D.PatientRecordId
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId	
	JOIN DemographicsEnrollments ROSTER ON MRP.id = ROSTER.DemographicsMRPId -- this checks for roster status, active if no termination date
	WHERE MRP.IsActive = 1												-- Rostered ( Source: sp_GetRecallList)
	AND ROSTER.enrollmentTerminationDate IS NULL -- Roster not terminated

	--- Appointments
	INSERT INTO @Appointments
	SELECT ap.PatientRecordId,MAX(appointmentTime) AS LastAppDate	
	FROM
	Appointments ap
	JOIN @BasePopulation bp on ap.PatientRecordId = bp.PatientRecordId
	WHERE ap.appointmentStatus NOT IN (3,7) -- Not arrived/Cancelled
	and ap.appointmentTime < GETDATE()
	GROUP BY ap.PatientRecordId
	
	--- Segments	
		INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] = 0 AND PatientRecordId IN (SELECT PatientRecordId FROM @Rostered)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @Appointments 
							WHERE LastAppDate >= DATEADD(MONTH,-12,GETDATE()))
	
		INSERT INTO @Segments
	SELECT 2,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] = 0 AND PatientRecordId IN (SELECT PatientRecordId FROM @Rostered)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @Appointments 
							WHERE LastAppDate > DATEADD(MONTH,-24,GETDATE()) AND LastAppDate < DATEADD(MONTH,-12,GETDATE()))

	
		INSERT INTO @Segments
	SELECT 3,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] = 0 AND PatientRecordId IN (SELECT PatientRecordId FROM @Rostered)
	AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @Appointments 
								WHERE LastAppDate >= DATEADD(MONTH,-24,GETDATE()))

		INSERT INTO @Segments
	SELECT 4,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] = 0 AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @Rostered)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @Appointments 
								WHERE LastAppDate >= DATEADD(MONTH,-12,GETDATE()))

		INSERT INTO @Segments
	SELECT 5,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] = 0 AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @Rostered)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @Appointments 
							WHERE LastAppDate > DATEADD(MONTH,-36,GETDATE()) AND LastAppDate < DATEADD(MONTH,-12,GETDATE()))
		INSERT INTO @Segments
	SELECT 6,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] = 0 AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @Rostered)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @Appointments 
								WHERE LastAppDate <= DATEADD(MONTH,-36,GETDATE()))

		INSERT INTO @Segments
	SELECT 7,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] = 1

		INSERT INTO @Segments
	SELECT 8,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] = 3

		INSERT INTO @Segments
	SELECT 9,PatientRecordId
	FROM @BasePopulation
	WHERE [Status] NOT IN (0,1,3)

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

END
