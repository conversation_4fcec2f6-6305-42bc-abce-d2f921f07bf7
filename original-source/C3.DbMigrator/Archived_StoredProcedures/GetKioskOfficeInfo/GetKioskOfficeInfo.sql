﻿CREATE PROCEDURE [dbo].[GetKioskOfficeInfo] 
	@ipAddress nvarchar(100)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT TOP 1
	o.name AS OfficeName, 
	o.businessName AS OfficeBusinessname, 
	o.address1 AS OfficeAddress1, 
	o.address2 AS OfficeAddress2, 
    o.city AS OfficeCity, 
	o.phone AS OfficePhone, 
	o.fax AS Office_Fax, 
	o.country AS OfficeCountry, 
	o.zip AS OfficeZip, 
	o.state AS OfficeState, 
	o.url AS OfficeUrl, 
	o.province AS OfficeProvince,
    o.postalCode AS OfficePostalCode,	
	ou.leftLogo AS LeftLogo, 
	ou.rightLogo AS RightLogo, 
	ou.middleLogo AS MiddleLogo,
	CAST( 1 AS BIT ) AS ShowLogo,
	k.WelcomeMessage AS OfficeMessage
	FROM Office o	
	JOIN KioskIpAddresses k ON o.Id = k.OfficeId
	JOIN OfficeOutlooks ou ON ou.OfficeId = o.Id		
	WHERE k.IpAddress = @ipAddress
END
