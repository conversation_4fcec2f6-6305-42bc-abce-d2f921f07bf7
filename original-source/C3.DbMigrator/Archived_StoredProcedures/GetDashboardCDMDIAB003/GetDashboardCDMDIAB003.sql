﻿CREATE PROCEDURE [dbo].[GetDashboardCDMDIAB003] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @DiabetesCoded AS TABLE (PatientRecordId INT)
	DECLARE @HbA1cDocumented AS TABLE (PatientRecordId INT,testResultFloat FLOAT, collectionDate DATETIME2)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE D.active = 0	
	AND (MRP.PracticeDoctorId = @PracticeDoctorId)-- OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	--- Diabetes Coded 
	INSERT INTO @DiabetesCoded
	SELECT PatientRecordId	
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('250'
							,'DB-610'
							,'46635009', '44054006', '73211009')
	OR CPP.DiagnosticCode LIKE 'E10%'
	OR CPP.DiagnosticCode LIKE 'E11%')	
	AND Deleted = 0 AND CPP.UpdateDate IS NULL

	--- >= 1 documented HbA1c tests in the last 12 months 
	INSERT INTO @HbA1cDocumented
	SELECT P.PatientRecordId, r.testResultFloat, r.collectionDate	
	FROM HL7Result R
	JOIN HL7ReportVersion V ON R.HL7ReportVersionId = V.Id
	JOIN HL7Report RPT ON RPT.Id = V.HL7ReportId
	JOIN HL7Patient P ON RPT.HL7PatientId = P.Id
	WHERE 
	--CONTAINS(testCodeIdentifier,'"hba1c" OR "gluc"')	
	(testCodeIdentifier LIKE '%hba1c%' OR testCodeIdentifier LIKE '%gluc%')
	AND R.collectionDate > DATEADD(MONTH,-12,GETDATE())
	AND r.resultStatus in ('F', 'A', 'C')
	--GROUP BY P.PatientRecordId
	--HAVING COUNT(R.Id)>=1
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
        FROM @HbA1cDocumented V2
        WHERE V2.PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesCoded)
        AND V2.PatientRecordId IN (SELECT PatientRecordId FROM @BasePopulation)
        AND V2.collectionDate = (SELECT MAX(V1.collectionDate) FROM @HbA1cDocumented V1 WHERE V1.PatientRecordId = V2.PatientRecordId GROUP BY V1.patientrecordID)
        AND V2.testResultFloat <= 7
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
       
        FROM @HbA1cDocumented V2
        WHERE V2.PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesCoded)
        AND V2.PatientRecordId IN (SELECT PatientRecordId FROM @BasePopulation)
        AND V2.collectionDate = (SELECT MAX(V1.collectionDate) FROM @HbA1cDocumented V1 WHERE V1.PatientRecordId = V2.PatientRecordId GROUP BY V1.patientrecordID)
        AND V2.testResultFloat > 7 and V2.testResultFloat <= 8.5

	INSERT INTO @Segments
	SELECT 3,V2.PatientRecordId
        FROM @HbA1cDocumented V2
        WHERE V2.PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesCoded)
        AND V2.PatientRecordId IN (SELECT PatientRecordId FROM @BasePopulation)
        AND V2.collectionDate = (SELECT MAX(V1.collectionDate) FROM @HbA1cDocumented V1 WHERE V1.PatientRecordId = V2.PatientRecordId GROUP BY V1.patientrecordID)
        AND V2.testResultFloat > 8.5
	
	INSERT INTO @Segments
	SELECT 4,PatientRecordId
		FROM @BasePopulation
        WHERE PatientRecordId IN (SELECT PatientRecordId FROM @DiabetesCoded)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @HbA1cDocumented)   
	
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments


	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log
END
