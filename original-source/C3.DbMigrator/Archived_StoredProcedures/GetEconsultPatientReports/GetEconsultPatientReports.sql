﻿
-- =============================================
-- Author:		<Author,<PERSON><PERSON><PERSON>>
-- Create date: <2021-08-10>
-- Description:	<Description,GetEconsultPatientReports>
-- exec GetEconsultPatientReports 918392
-- =============================================
CREATE PROCEDURE [dbo].[GetEconsultPatientReports] 
	@patientRecordId INT
AS
BEGIN

   select Id, url, filename, filesize, officeId, fileExtensionAndVersion, receivedDateTime, description
     from (
	       --External documents
		   select Id, url, fileName, IsNull(fileSize,0) as fileSize, officeId, fileExtensionAndVersion, receivedDateTime, description
			 from reportreceiveds where PatientRecordId = @patientRecordId
			union all
		   --Sent letters
		   select sr.Id, sr.URL, reverse(substring(reverse(url), 1, charindex('\', reverse(url))-1 )) as filename,
				  0 as filezise, a.officeId, 
				  reverse(substring(reverse(url), 1, charindex('.', reverse(url)))) as fileExtensionAndVersion, DateEntered as receivedDateTime, 'Letter' as description
			 from vp_sendreport sr,
				  appointments a
			where sr.appointmentID = a.id
			and   sr.SendTypeId = 6
			and   sr.[Sent] = 1
			and   sr.PatientRecordId = @patientRecordId
			union all
		   --Raw documents and Sent reports
		   select sr.Id, sr.URL, reverse(substring(reverse(url), 1, charindex('\', reverse(url))-1 )) as filename,
				  0 as filezise, a.officeId, 
				  reverse(substring(reverse(url), 1, charindex('.', reverse(url)))) as fileExtensionAndVersion, DateEntered as receivedDateTime,
				  case when sr.SendTypeId = 5 then 'Raw Document' else 'Report' end as description
			 from ws_sendreport sr,
				  appointments a
			where sr.appointmentID = a.id
			and   ( sr.SendTypeId = 5 or ( sr.SendTypeId = 6 and sr.Sent = 1 ) )
			and   sr.Sent = 1
			and   a.PatientRecordId = @patientRecordId
		  ) z
	ORDER BY receivedDateTime desc
END
