﻿
-- =============================================
-- Author:		DIVYESH
-- Create date: 2020-09-18
-- Description:	Health card validation credential
-- =============================================
CREATE PROCEDURE [dbo].[HCV_Get_Practice_Doctor_Credential]
	@practiceId INT,
	@practiceDoctorId INT=0
AS
BEGIN
	SET NOCOUNT ON;

    
	SELECT TOP 1 PD.Id AS PracticeDoctorId
			   , PD.ExternalDoctorId
			   , ED.OHIPPhysicianId AS BillingNumber
			   , PD.mc_un AS UserName
			   , PD.mc_pwd AS MC_EDT_PWD  
	FROM PracticeDoctors PD
	JOIN ExternalDoctors ED ON PD.ExternalDoctorId=ED.Id
	WHERE PD.PracticeId=@practiceId AND
	1 = (CASE WHEN PD.canUseCredentialForHCV IS NOT NULL AND PD.canUseCredentialForHCV=1 THEN 1 ELSE 1 END) AND
	(PD.mc_un IS NOT NULL AND PD.mc_pwd IS NOT NULL ) AND
	(PD.mc_un <>'' AND PD.mc_pwd <> '') AND
	ED.OHIPPhysicianId IS NOT NULL AND
	1 = (CASE WHEN @practiceDoctorId>0 AND PD.Id=@practiceDoctorId THEN 1 ELSE 1 END)

END
