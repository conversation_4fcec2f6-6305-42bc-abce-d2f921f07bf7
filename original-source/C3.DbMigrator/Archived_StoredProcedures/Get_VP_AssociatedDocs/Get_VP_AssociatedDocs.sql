﻿CREATE PROCEDURE [dbo].[Get_VP_AssociatedDocs]

 
@patientID  int 

AS


SELECT  DISTINCT
		e.Id AS ExternalDocID,
        e.lastName + ' ' + e.firstName AS [Name],
        @patientID AS PatientID,
		CAST(IIF(e.fax = 0 AND e.HRM = 0 AND e.email = 0, 0, 1) AS BIT) AS Contact,
		CAST(IIF(e.fax = 1 AND (select [dbo].[fn_HasFaxNumber](e.Id)) = 0, 0, 1) AS BIT) as HasFax
FROM 
        DemographicsAssociatedDoctors doc
        join Demographics d on doc.DemographicId = d.Id
        join ExternalDoctors e on doc.ExternalDoctorId = e.Id
WHERE
      d.PatientRecordId = @patientID AND 
      doc.IsCC = 1 AND doc.IsRemoved = 0 AND doc.IsActive = 1
