﻿
-- =============================================
-- Author:		<Author,Divyesh>
-- Create date: <Create Date,2019-03-14,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetAllPracticeDoctorsForOLIS]
	(@practiceId  INT)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
   
	SELECT 
	pd.Id,
	pd.PracticeId,
	pd.specialtyCodes,
	pd.OLIS_AssiningJuridictionId AS OLIS_AssiningJuridictionId,
	pd.OLIS_AssiningJuridictionIdCode AS OLIS_AssiningJuridictionIdCode,
	pd.OLIS_IdType AS OLIS_IdType,
	pd.OLISActive,
	ex.Id AS ExternalDoctorId,
	ex.lastName AS LastName,
	ex.firstName AS FirstName,
	ex.middleName AS MiddleName,
	ex.HRMId AS HRMId,
	ex.OHIPPhysicianId AS OHIPPhysicianId,
	ex.CPSO AS OLIS_RequestingHIC,
	ex.emailAddress AS emailAddress,
	pd.OLISPreloadState AS PreloadState,
    pd.OLISPreloadStartDate AS PreloadStartDate,
    pd.OLISPreloadEndDate AS PreloadEndDate
	,(SELECT TOP 1 L.createdDateTime FROM OLISCommunicationLogs L 
	WHERE (L.RequestingHIC=ex.CPSO AND L.EMRQueryType=0)  ORDER By L.createdDateTime DESC) AS OLISLastSuccessRunDate
	FROM PracticeDoctors pd
	JOIN ExternalDoctors ex ON pd.ExternalDoctorId=ex.Id
	
	WHERE pd.PracticeId=@practiceId 
	AND (pd.OLISActive=1 AND ex.active=1 AND pd.IsActive=1)

END
