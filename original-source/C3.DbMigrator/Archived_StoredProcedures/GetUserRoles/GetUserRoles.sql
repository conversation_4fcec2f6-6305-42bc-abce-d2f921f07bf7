﻿
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetUserRoles]
	(@userId  INT)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT r.PracticeId, r.Id, r.[Name] 
	FROM AspNetUsers u
	JOIN AspNetUserRoles AS ur ON u.Id = ur.UserId
	JOIN AspNetRoles AS r ON ur.RoleId=r.Id	
	WHERE u.UserID=@userId
END
