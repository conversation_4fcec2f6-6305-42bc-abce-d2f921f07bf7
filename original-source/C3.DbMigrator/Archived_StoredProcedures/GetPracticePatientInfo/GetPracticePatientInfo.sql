﻿-- =============================================
-- Author:		<Author,,Divy<PERSON>>
-- Create date: <Create 2019-08-26,>
-- Description:	<Description,Get practice patients,>
-- =============================================
CREATE PROCEDURE [dbo].[GetPracticePatientInfo]
	@practiceId INT,
	@patientRecordId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 
	d.PracticeId
	,d.Demo<PERSON>d
	,d.<PERSON>
	,d.PatientId AS PatientRecordId
	,d.Ali<PERSON>ast<PERSON>ame
	,d.AliasFirstName
	,d.<PERSON> 
	,d.<PERSON> 
	,d.PreferredName
	,d.<PERSON>Name 
	,d.<PERSON>
	,d.<PERSON>
	,d.<PERSON>
	,d.<PERSON>
	,d.<PERSON> 
	,d.<PERSON> 
	,d.HealthCardCode AS OHIPVersionCode
	,d.PhoneNumbers AS PatientPhoneNumbers
	,d.DefaultPaymentMethod
	
	FROM 
	fn_GetPatientInfo(@patientRecordId,null) d
	JOIN PatientRecords p ON d.PatientId = p.Id
	WHERE p.PracticeId=@practiceId
		
	
END


