﻿
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetPracticeScheduleUsers] 
	@practiceId INT,
	@officeId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    DECLARE @tblIncludedPermissions TABLE
	   ( 
         Permission NVARCHAR(100) 
	   );
	INSERT INTO @tblIncludedPermissions VALUES ('VP'); 
	INSERT INTO @tblIncludedPermissions VALUES ('CanDoTests');


	SELECT 
	su.Id AS ScheduleUserId
	,su.UserId
	,su.PracticeId
	,su.permission
	,su.startDate
	,su.endDate
	,su.updatedDate
	,u.Id AS ApplicationUserId
	,u.FirstName
	,u.LastName
	,u.UserName
	,u.CerebrumUserType
	,u.[Status]
	,sw.Id AS ScheduleWeekDayId
	,sw.officeId
	,sw.absentTime
	,sw.startTime
	,sw.finishTime
	,sw.reservedTime
	,sw.[Date]
	,sw.[dayOfWeek]
	,rp.PermissionId 
	,rp.PermissionName
	,rp.PermissionTypeId
	,pt.[Name] AS PermissionType
	,r.Id AS RoleId
	,r.[Name] AS RoleName	
	--,ISNULL((SELECT TOP 1 Id FROM PracticeDoctors WHERE ApplicationUserId = u.Id AND ApplicationUserId IS NOT NULL AND PracticeId = @practiceId),0) AS PracticeDoctorId
	,ISNULL(pd.Id,0) AS PracticeDoctorId
	FROM ScheduleUsers su
	JOIN AspNetUsers u ON su.UserId = u.UserId
	JOIN ScheduleWeekDays sw ON su.Id = sw.ScheduleUserId
	JOIN AspNetUserRoles AS ur ON u.Id =ur.UserId
	JOIN AspNetRoles AS r ON ur.RoleId=r.Id
	JOIN RolePermissions AS rp ON ur.RoleId=rp.ApplicationRoleId
	JOIN PermissionTypes AS pt ON rp.PermissionTypeId =pt.Id
	LEFT JOIN PracticeDoctors pd ON u.Id = pd.ApplicationUserId
	WHERE su.PracticeId = @practiceId 
	AND u.[Status] = 0 AND sw.officeId = @officeId 
		AND 1 = CASE 
					WHEN u.CerebrumUserType = 5 AND pd.ApplicationUserId IS NOT NULL AND pd.PracticeId = @practiceId AND pt.[Name] IN (SELECT Permission FROM @tblIncludedPermissions) THEN 1 -- 5 is a doctor
					WHEN u.CerebrumUserType != 5 AND pt.[Name] = 'CanDoTests' THEN 1
					ELSE 0
				END
	--AND u.[Status] = 0 AND sw.officeId = @officeId AND 1 = CASE WHEN u.CerebrumUserType = 5 AND pt.[Name] IN (SELECT Permission FROM @tblIncludedPermissions) THEN 1
													 
	
	
END
