﻿CREATE PROCEDURE [dbo].[GetVPReportPhraseSaveTextByLogIds]
	@vpAppointmentTestLogIds AS dbo.IntegerList READONLY
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT 
	   v.[Id]
      ,v.[Value]
      ,v.[AppointmentId]
      ,v.[PatientRecordId]
      ,v.[VP_AppointmentTestLogId]
      ,v.[TopLevelVPReportPhraseID] AS TopLevelVPReportPhraseId
	FROM 
	VP_ReportPhrasesSavedText v
	JOIN @vpAppointmentTestLogIds l ON v.VP_AppointmentTestLogId = l.IntegerValue
	
END

