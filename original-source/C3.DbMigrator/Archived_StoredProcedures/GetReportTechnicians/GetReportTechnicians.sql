﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetReportTechnicians]
	@appointmentTestId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    SELECT 
	u.FirstName
	,u.LastName
	,u.CerebrumUserType
	FROM AppointmentTestResources t
	JOIN AspNetUsers u ON t.performedByUserId = u.UserID
	WHERE t.AppointmentTestId = @appointmentTestId
	AND t.performedByUserId IS NOT NULL
	AND t.isActive = 1
END
