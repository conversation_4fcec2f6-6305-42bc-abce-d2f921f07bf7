﻿CREATE PROCEDURE [dbo].[GetDashboardCAREBNS002] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @CurrentDate AS DATE,@FYDate AS DATE
	SELECT @CurrentDate = GETDATE()
	SELECT @FYDate = STR(IIF(MONTH(GETDATE())<4,YEAR(GETDATE()),YEAR(GETDATE()+1))) + '-03-31' -- FiscalYear CutOffDate

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)
	DECLARE @PapExclusion AS TABLE (PatientRecordId INT)
	DECLARE @PapTest AS TABLE (PatientRecordId INT)
	
	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT		
	D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	JOIN DemographicsEnrollments ROSTER ON MRP.id = ROSTER.DemographicsMRPId -- this checks for roster status, active if no termination date
	WHERE 
	D.active = 0	
	AND MRP.IsActive = 1 -- Rostered ( Source: sp_GetRecallList)
	AND (DATEDIFF(DD,D.dateOfBirth,@FYDate) / 365.5) BETWEEN 21 AND 69
	AND D.gender = 1
	AND (MRP.PracticeDoctorId = @PracticeDoctorId)-- OR @PracticeDoctorId IS NULL)
	AND ROSTER.enrollmentTerminationDate IS NULL -- Roster not terminated
	GROUP BY D.PatientRecordId

	--- PAP Exclusion 
	INSERT INTO @PapExclusion
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q140A'
	AND DATEDIFF(MONTH,b.serviceDate,@FYDate) <= 42		-- date of service within 42 months
	UNION ALL 
	SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('V45.77','68.3','68.4','68.5','68.6','68.7','68.8','68.9')						-- Excluded Diagnosis codes
		OR CPP.Problem_Description LIKE '%hysterectomy%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%hysterosal%'
		OR CPP.Problem_Description LIKE '%Cervical Ca%'
		OR CPP.Problem_Description LIKE '%TVHt%'
		OR CPP.Problem_Description LIKE '%TABH%'
		OR CPP.Problem_Description LIKE '%TAH%'
		)
		AND Deleted = 0 AND UpdateDate IS NULL

	--- PAP Test
	INSERT INTO @PapTest
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q011A'
	AND DATEDIFF(MONTH,b.serviceDate,@FYDate) <= 42		-- date of service within 42 months
	UNION ALL
	SELECT IM.PatientRecordId
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId=2							-- Type = PAP
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),@FYDate) <= 42		-- date of service within 24 months		

	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PapExclusion)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @PapTest)
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @PapExclusion)
	AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @PapTest)
		
	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log
END
