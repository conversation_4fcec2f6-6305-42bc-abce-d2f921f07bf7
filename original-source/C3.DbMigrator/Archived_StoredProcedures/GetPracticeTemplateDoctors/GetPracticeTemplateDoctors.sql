﻿CREATE PROCEDURE [dbo].[GetPracticeTemplateDoctors] 
	@practiceId int,
	@groupId int,
	@practiceTemplateId INT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @tbdDoctorId INT = 17428
    -- Insert statements for procedure here
	SELECT 
	ISNULL(drt.Id,0) AS DoctorPracticeTemplateId
	,prt.Id AS PracticeTemplateId
	,prt.PracticeId	
	,prt.DateCreated
	,prt.DateLastModified
	,prt.LastModifiedByUserId
	,prt.TemplateId
	,rt.TemplateName
	,rt.IsSystem
	,prt.IsActive
	,ISNULL(drt.IsActive,0) AS IsActiveDoctorTemplate
	,ISNULL(drt.IsDefault,0) AS IsDefault	
	,pd.ExternalDoctorId
	,ex.Firstname AS FirstName
	,ex.lastName AS LastName
	FROM PracticeRootCategoryTemplates prt
	JOIN PracticeDoctors pd ON prt.PracticeId = pd.PracticeId
	JOIN ExternalDoctors ex ON pd.ExternalDoctorId = ex.Id
	JOIN RootTemplates rt ON prt.TemplateId = rt.Id
	LEFT JOIN DoctorRootCategoryTemplates drt ON drt.PracRootCategoryTempId = prt.Id AND drt.ExternalDoctorId = pd.ExternalDoctorId
	WHERE prt.PracticeId = @practiceId
	AND rt.IsActive = 1		
	AND rt.GroupId = @groupId
	AND prt.PracticeId = @practiceId
	AND pd.PracticeId = @practiceId
	AND prt.Id = @practiceTemplateId
	AND pd.ExternalDoctorId != @tbdDoctorId
	ORDER BY ex.lastName
END