﻿CREATE   PROCEDURE [dbo].[GetBillingRemittanceSearch] 
	@isRASoloBillingAdmin int,
	@isRAGroupBillingAdmin int,
	@practiceId int,
	@practiceDoctorIds varchar(256),
	@billingType int,
	@billingFileType int,
	@rowStart int,
	@rowCount int,
	@totalRowRequest int,
	@sortByColumn varchar(32),
	@sortByOrder varchar(8),
	@filterDownloadStart varchar(16),
	@filterDownloadEnd varchar(16)
AS
BEGIN
	SET NOCOUNT ON;
	
	DECLARE @totalRow int
	DECLARE @sql varchar(8000)
	DECLARE @sqlCounter nvarchar(max)
	DECLARE @orderBySql varchar(64)
	DECLARE @offsetFetchSql varchar(64)
	DECLARE @orderByColumn varchar(64)

	SET @sql = FORMATMESSAGE('select id,fileName fileFullName,DateEntered dateEntered from Billing_File where fileType=%d', @billingFileType)

	IF @billingType = 1
	BEGIN
		IF @isRAGroupBillingAdmin = 1
			SET @sql = @sql + FORMATMESSAGE(' and edtGroupId in (select a.edtGroupId from Billing_Group a, Office b where a.officeId=b.Id and b.PracticeId=%d)',@practiceId)
		ELSE
			SET @sql = @sql + ' and 1 = 0'
	END
	ELSE
	BEGIN
		IF @isRASoloBillingAdmin = 1
			SET @sql = @sql + FORMATMESSAGE(' and PracticeDoctorId in (select id from PracticeDoctors where PracticeId=%d)',@practiceId)
		ELSE
			SET @sql = @sql + FORMATMESSAGE(' and PracticeDoctorId in (SELECT value FROM STRING_SPLIT(''%s'', '','')) ', @practiceDoctorIds)
	END

	IF (@filterDownloadStart is not null and @filterDownloadStart <> '')
		SET @sql = @sql + FORMATMESSAGE(' and DateEntered>=convert(datetime,''%s'', 102)',@filterDownloadStart)

	IF (@filterDownloadEnd is not null and @filterDownloadEnd <> '')
		SET @sql = @sql + FORMATMESSAGE(' and DateEntered<=DATEADD (day , 1 , convert(datetime,''%s'', 102))',@filterDownloadEnd)

	SET @totalRow = -1
	IF (@totalRowRequest = 1)
	BEGIN
		SET @sqlCounter = 'select @total=count(*) from (' + @sql + ') a'
		EXEC sp_executesql @sqlCounter, N'@total int OUTPUT', @total = @totalRow OUTPUT
	END

	IF (@totalRow <> 0)
	BEGIN
		SET @orderByColumn = (CASE WHEN @sortByColumn='filename' THEN 'fileName'
									ELSE 'DateEntered' END)

		SET @orderBySql = ' order by ' + @orderByColumn + ' ' + (CASE WHEN @sortByOrder='asc' THEN ' asc' ELSE ' desc' END)
		SET @offsetFetchSql = FORMATMESSAGE(' offset %d rows fetch next %d rows only', @rowStart, @rowCount)

		EXEC (@sql + @orderBySql + @offsetFetchSql)
	END

	return @totalRow
END