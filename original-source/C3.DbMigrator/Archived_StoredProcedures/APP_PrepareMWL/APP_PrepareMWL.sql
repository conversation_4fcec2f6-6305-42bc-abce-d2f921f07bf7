﻿
-- =============================================
-- Author:		<Author,,Divyesh>
-- Create date: <2021-07-09,>
-- Update date: <2021-07-19>
-- Description:	<Description,GetMWL,>
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[APP_PrepareMWL] 
	@practiceId INT,
	@appointmentId INT
AS
BEGIN
SELECT a.Id as AppointmentId
      ,a.MW<PERSON>ent<PERSON>lag
	  ,a.MWLUrl
	  ,a.OfficeId
	  ,a.PatientRecordId AS PatientID
	  ,demo.SIN
	  ,(SELECT TOP 1 H.number FROM DemographicsHealthCards H WHERE H.DemographicId=demo.Id AND H.IsActive=1 ORDER BY H.Id DESC) AS OHIP
	  ,demo.lastName AS PatientLastName
	  ,demo.firstName AS PatientFirstName
	  ,CONVERT(varchar,demo.dateOfBirth,112) AS PatientDateOfBirth
	  ,demo.gender AS Gender
	  ,ed.LastName AS DoctorLastName
	  ,ed.firstName AS DoctorFirstName
	  ,ed.CPSO
	  ,ed.OHIPPhysicianId
	  ,ourl.url AS OfficeURL
	  ,ISNULL((SELECT APT.Id AS AppointmentTestId
					 ,APT.AccessionNumber
	                 ,T.Id AS TestId
					 ,T.testShortName AS TestName
					 ,M.Id AS ModalityId
					 ,M.modalityName AS ModalityName
					 ,M.Description AS ModalityDescription
	    FROM AppointmentTests APT
		JOIN Tests T ON APT.TestId =T.Id
		LEFT JOIN TestModalities TM ON APT.TestId=TM.TestId
		LEFT JOIN Modalities M ON TM.ModalityId=M.Id
		WHERE (APT.AppointmentId=A.Id AND APT.IsActive=1) --AND TM.IsActive=1
		FOR JSON PATH
	  ),NULL) AS Tests
FROM Appointments a
JOIN PatientRecords pr on a.PatientRecordId=pr.Id
JOIN Demographics demo on pr.Id =demo.PatientRecordId
JOIN PracticeDoctors pd on a.PracticeDoctorId=pd.Id
JOIN ExternalDoctors ed on pd.ExternalDoctorId=ed.Id
JOIN OfficeUrls ourl ON a.OfficeId=ourl.officeId
JOIN OfficeUrlTypes ty ON ourl.urlTypeId=ty.Id
WHERE pr.PracticeId=@practiceId 
AND a.Id=@appointmentId AND ty.urlType='MWL' 
AND demo.dateOfBirth IS NOT NULL
order by a.appointmentTime desc

END