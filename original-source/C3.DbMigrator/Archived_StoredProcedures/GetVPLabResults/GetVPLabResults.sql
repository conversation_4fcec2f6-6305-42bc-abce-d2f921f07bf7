﻿CREATE PROCEDURE [dbo].[GetVPLabResults] 
	@practiceId INT,	
	@appointmentTestLogId INT	 

AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	SELECT 
	l.Id AS VPLabResultId
	,l.LabR<PERSON>ultDate
	,ISNULL(l.HL7ReportId,0) AS HL7ReportId
	,r.collectionDateTime AS HL7CollectionDate
	,l.AppointmentTestSaveLogId
	FROM VPLabResults l
	JOIN AppointmentTestSaveLogs sl ON l.AppointmentTestSaveLogId = sl.Id
	LEFT JOIN HL7Report r ON r.Id = l.HL7ReportId
	WHERE l.AppointmentTestSaveLogId = @appointmentTestLogId


END