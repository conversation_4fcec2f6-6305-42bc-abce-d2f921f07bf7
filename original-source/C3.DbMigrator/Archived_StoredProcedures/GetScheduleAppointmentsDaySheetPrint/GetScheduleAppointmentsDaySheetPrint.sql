﻿CREATE PROCEDURE [dbo].[GetScheduleAppointmentsDaySheetPrint] 
	-- Add the parameters for the stored procedure here
	@officeId INT,
	@practiceDoctorId INT,
	@practiceid INT,
	@fromDate DATETIME,
	@toDate DATETIME
AS

BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	DECLARE @appointmentStatus AS INT
	DECLARE @cancelledStatus AS INT
	DECLARE @isActive AS BIT

	SET NOCOUNT ON;
	SET @appointmentStatus =1
	set @isActive=1
	SET @cancelledStatus=7
    -- Insert statements for procedure here
IF @practiceDoctorId > 0  and  @officeId > 0

Begin            
	SELECT
	ap.[Id] as AppointmentId
	,ap.[AppointmentTypeId]
	,ap.OfficeId
    ,prac.Id as PracticeId
	,ISNULL(typ.[AppointmentTypeId],0) as TypeId
	,typ.[name] as AppointmentTypeName
	--,aptyp.name as <PERSON>Name
	,typ.name as <PERSON><PERSON><PERSON>
	,ap.[appointmentStatus] as [AppointmentStatus]
	,ap.[PracticeDoctorId]
	,'Dr.'+extDocs.firstName +' '+extDocs.lastName AS PracticeDoctor	
	,pracDocs.[ApplicationUserId]
	,ap.[PatientRecordId]
	,demo.firstName AS PatientFirstName
	,demo.lastName AS PatientLastName
	,res.[AppointmentTestId]
	,tst.Id as TestId
	,tst.[testShortName] as TestShortName
	,tst.[isActive] as TestActive
    ,res.[Id] as TestResourceId
    ,res.[assignedToUserId]    as AssignedToUserId
    ,res.[isDoctorRequiredInOffice]
    ,ap.[appointmentTime] as AppointmentTime
	,apt.[startTime] as TestStartTime
	,apt.[testDuration] as TestDuration
	,res.[permissionId] as PermissionId
	,tst.[color] as TestColor
  FROM [AppointmentTestResources] as res
  join [AppointmentTests] as apt on res.AppointmentTestId=apt.id  
  join [Appointments] as ap on apt.AppointmentId=ap.Id
  join [Office] as office on ap.OfficeId=office.Id
  join [Practices] as prac on  office.PracticeId = prac.Id
  right join [AppointmentTypes] as typ on ap.[AppointmentTypeId]=typ.Id
  --join [AppointmentTypes] as aptyp on typ.[AppointmentTypeId]=aptyp.Id
  JOIN [PracticeDoctors] pracDocs ON ap.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  JOIN Demographics demo ON ap.PatientRecordId = demo.PatientRecordId
  right join [Tests] as tst on apt.TestId=tst.Id
  
  where(prac.Id=@practiceid)   
  and ap.PracticeDoctorId = @practiceDoctorId 
  and ap.OfficeId = @officeId
  and (ap.[appointmentStatus] >@appointmentStatus AND ap.[appointmentStatus]<> @cancelledStatus) 
  and (ap.appointmentTime>@fromDate and ap.appointmentTime<@toDate) 
  and ap.[IsActive]=@isActive 
  and apt.IsActive=@isActive
 -- and LOWER( tst.testShortName )  =  'vp'
 -- and res.assignedToUserId is not null 
  order by ap.[appointmentTime] 
END
 ELSE IF  @practiceDoctorId > 0 and  @officeId < 1

 Begin 
 SELECT
		ap.[Id] as AppointmentId
	,ap.[AppointmentTypeId]
	,ap.OfficeId
    ,prac.Id as PracticeId
	,ISNULL(typ.[AppointmentTypeId],0) as TypeId
	,typ.[name] as AppointmentTypeName
	--,aptyp.name as TypeName
	,typ.name as TypeName
	,ap.[appointmentStatus] as [AppointmentStatus]
	,ap.[PracticeDoctorId]
	,'Dr.'+extDocs.firstName +' '+extDocs.lastName AS PracticeDoctor	
	,pracDocs.[ApplicationUserId]
	,ap.[PatientRecordId]
	,demo.firstName AS PatientFirstName
	,demo.lastName AS PatientLastName
	,res.[AppointmentTestId]
	,tst.Id as TestId
	,tst.[testShortName] as TestShortName
	,tst.[isActive] as TestActive
    ,res.[Id] as TestResourceId
    ,res.[assignedToUserId]    as AssignedToUserId
    ,res.[isDoctorRequiredInOffice]
    ,ap.[appointmentTime] as AppointmentTime
	,apt.[startTime] as TestStartTime
	,apt.[testDuration] as TestDuration
	,res.[permissionId] as PermissionId
	,tst.[color] as TestColor
  FROM [AppointmentTestResources] as res
  join [AppointmentTests] as apt on res.AppointmentTestId=apt.id  
  join [Appointments] as ap on apt.AppointmentId=ap.Id
  join [Office] as office on ap.OfficeId=office.Id
  join [Practices] as prac on  office.PracticeId = prac.Id
  right join [AppointmentTypes] as typ on ap.[AppointmentTypeId]=typ.Id
  --join [AppointmentTypes] as aptyp on typ.[AppointmentTypeId]=aptyp.Id
  JOIN [PracticeDoctors] pracDocs ON ap.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  JOIN Demographics demo ON ap.PatientRecordId = demo.PatientRecordId
  right join [Tests] as tst on apt.TestId=tst.Id
  
  where(prac.Id=@practiceid)  and 
  ap.PracticeDoctorId = @practiceDoctorId 
  and (ap.[appointmentStatus] >@appointmentStatus AND ap.[appointmentStatus]<> @cancelledStatus) 
  and (ap.appointmentTime>@fromDate and ap.appointmentTime<@toDate) 
  and ap.[IsActive]=@isActive 
  and apt.IsActive=@isActive
  --and LOWER( tst.testShortName )  =  'vp'
  --and res.assignedToUserId is not null 
  order by ap.[PatientRecordId]
END            
ELSE IF @practiceDoctorId < 1  and  @officeId > 0

begin               
	SELECT
		ap.[Id] as AppointmentId
	,ap.[AppointmentTypeId]
	,ap.OfficeId
    ,prac.Id as PracticeId
	,ISNULL(typ.[AppointmentTypeId],0) as TypeId
	,typ.[name] as AppointmentTypeName
	--,aptyp.name as TypeName
	,typ.name as TypeName
	,ap.[appointmentStatus] as [AppointmentStatus]
	,ap.[PracticeDoctorId]
	,'Dr.'+extDocs.firstName +' '+extDocs.lastName AS PracticeDoctor	
	,pracDocs.[ApplicationUserId]
	,ap.[PatientRecordId]
	,demo.firstName AS PatientFirstName
	,demo.lastName AS PatientLastName
	,res.[AppointmentTestId]
	,tst.Id as TestId
	,tst.[testShortName] as TestShortName
	,tst.[isActive] as TestActive
    ,res.[Id] as TestResourceId
    ,res.[assignedToUserId]    as AssignedToUserId
    ,res.[isDoctorRequiredInOffice]
    ,ap.[appointmentTime] as AppointmentTime
	,apt.[startTime] as TestStartTime
	,apt.[testDuration] as TestDuration
	,res.[permissionId] as PermissionId
	,tst.[color] as TestColor
  FROM [AppointmentTestResources] as res
  join [AppointmentTests] as apt on res.AppointmentTestId=apt.id  
  join [Appointments] as ap on apt.AppointmentId=ap.Id
  join [Office] as office on ap.OfficeId=office.Id
  join [Practices] as prac on  office.PracticeId = prac.Id
  right join [AppointmentTypes] as typ on ap.[AppointmentTypeId]=typ.Id
  --join [AppointmentTypes] as aptyp on typ.[AppointmentTypeId]=aptyp.Id
  JOIN [PracticeDoctors] pracDocs ON ap.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  JOIN Demographics demo ON ap.PatientRecordId = demo.PatientRecordId
  right join [Tests] as tst on apt.TestId=tst.Id
  
  where(prac.Id=@practiceid)  and 
  ap.OfficeId = @officeId
  and (ap.[appointmentStatus] >@appointmentStatus AND ap.[appointmentStatus]<> @cancelledStatus) 
  and (ap.appointmentTime>@fromDate and ap.appointmentTime<@toDate) 
  and ap.[IsActive]=@isActive 
  and apt.IsActive=@isActive
 -- and LOWER( tst.testShortName )  =  'vp'
  --and res.assignedToUserId is not null 
  order by ap.[PatientRecordId]
end 
ELSE

begin              
SELECT
		ap.[Id] as AppointmentId
	,ap.[AppointmentTypeId]
	,ap.OfficeId
    ,prac.Id as PracticeId
	,ISNULL(typ.[AppointmentTypeId],0) as TypeId
	,typ.[name] as AppointmentTypeName
	--,aptyp.name as TypeName
	,typ.name as TypeName
	,ap.[appointmentStatus] as [AppointmentStatus]
	,ap.[PracticeDoctorId]
	,'Dr.'+extDocs.firstName +' '+extDocs.lastName AS PracticeDoctor	
	,pracDocs.[ApplicationUserId]
	,ap.[PatientRecordId]
	,demo.firstName AS PatientFirstName
	,demo.lastName AS PatientLastName
	,res.[AppointmentTestId]
	,tst.Id as TestId
	,tst.[testShortName] as TestShortName
	,tst.[isActive] as TestActive
    ,res.[Id] as TestResourceId
    ,res.[assignedToUserId]    as AssignedToUserId
    ,res.[isDoctorRequiredInOffice]
    ,ap.[appointmentTime] as AppointmentTime
	,apt.[startTime] as TestStartTime
	,apt.[testDuration] as TestDuration
	,res.[permissionId] as PermissionId
	,tst.[color] as TestColor
  FROM [AppointmentTestResources] as res
  join [AppointmentTests] as apt on res.AppointmentTestId=apt.id  
  join [Appointments] as ap on apt.AppointmentId=ap.Id
  join [Office] as office on ap.OfficeId=office.Id
  join [Practices] as prac on  office.PracticeId = prac.Id
  right join [AppointmentTypes] as typ on ap.[AppointmentTypeId]=typ.Id
  --join [AppointmentTypes] as aptyp on typ.[AppointmentTypeId]=aptyp.Id
  JOIN [PracticeDoctors] pracDocs ON ap.PracticeDoctorId = pracDocs.Id
  JOIN ExternalDoctors extDocs ON pracDocs.ExternalDoctorId = extDocs.Id 
  JOIN Demographics demo ON ap.PatientRecordId = demo.PatientRecordId
  right join [Tests] as tst on apt.TestId=tst.Id  
  where(prac.Id=@practiceid) 
  and (ap.[appointmentStatus] >@appointmentStatus AND ap.[appointmentStatus]<> @cancelledStatus) 
  and (ap.appointmentTime>@fromDate and ap.appointmentTime<@toDate) 
  and ap.[IsActive]=@isActive 
  and apt.IsActive=@isActive
 -- and LOWER( tst.testShortName )  =  'vp'
 -- and res.assignedToUserId is not null 
  order by ap.[PatientRecordId]		  
end 
END