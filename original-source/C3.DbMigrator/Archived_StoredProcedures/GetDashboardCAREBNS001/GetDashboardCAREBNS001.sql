﻿CREATE PROCEDURE [dbo].[GetDashboardCAREBNS001] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON
	
	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @CurrentDate AS DATE,@FYDate AS DATE
	SELECT @CurrentDate = GETDATE()
	SELECT @FYDate = STR(IIF(MONTH(GETDATE())<4,YEAR(GETDATE()),YEAR(GETDATE()+1))) + '-03-31' -- FiscalYear CutOffDate	

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @MammogramExclusion AS TABLE (PatientRecordId INT)
	DECLARE @MammogramScreening AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	JOIN DemographicsEnrollments ROSTER ON MRP.id = ROSTER.DemographicsMRPId -- this checks for roster status, active if no termination date
	WHERE 	
	D.active = 0														-- Pastient is active
	AND MRP.IsActive = 1												-- Rostered ( Source: sp_GetRecallList)
	AND (DATEDIFF(DD,D.dateOfBirth,@FYDate) / 365.5) BETWEEN 50 AND 74	-- Patient is between 50 and 74
	AND D.gender = 1													-- Patient is Female
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	AND ROSTER.enrollmentTerminationDate IS NULL -- Roster not terminated
	GROUP BY D.PatientRecordId

	-- Mammogram Exclusion 
	INSERT INTO @MammogramExclusion
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q141A'
	AND DATEDIFF(MONTH,b.serviceDate,@FYDate) <= 30		-- date of service within 30 months		
	UNION ALL 
	SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('V45.71','174')						-- Excluded Diagnosis codes
		OR CPP.Problem_Description LIKE '%mastectomy%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%cancer breast%'
		OR CPP.Problem_Description LIKE '%breast ca%'
		OR CPP.Problem_Description LIKE '%ca breast%')
		AND Deleted = 0 AND UpdateDate IS NULL

	--- Mammogram Screening in the months inclusive prior to March 31 of the current fiscal 
	INSERT INTO @MammogramScreening
	SELECT IM.PatientRecordId	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId=3							-- Type = Mamography
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),@FYDate) <= 30		-- date of service within 30 months		
	UNION ALL
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q131A'
	AND DATEDIFF(MONTH,b.serviceDate,@FYDate) <= 30		-- date of service within 30 months	

	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @MammogramScreening)
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)
	AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreening)

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

	END
