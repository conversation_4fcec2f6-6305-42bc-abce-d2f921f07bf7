﻿CREATE PROCEDURE [dbo].[GetDashboardPHCCAN002] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @MammogramExclusion AS TABLE (PatientRecordId INT)
	DECLARE @MammogramScreening AS TABLE (PatientRecordId INT)
	DECLARE @MammogramScreeningCurrent AS TABLE (PatientRecordId INT,AgeMonths INT)
	DECLARE @MammogramScreeningDeclined AS TABLE (PatientRecordId INT,AgeMonths INT)
	DECLARE @MammogramScreeningPending AS TABLE (PatientRecordId INT,AgeMonths INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE 
	D.active = 0														-- Pastient is active	
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) BETWEEN 50 AND 74	-- Patient is between 50 and 74
	AND D.gender = 1		
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)-- Patient is Female
	GROUP BY D.PatientRecordId

	-- Mammogram Exclusion 
	INSERT INTO @MammogramExclusion
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q141A'
	UNION ALL 
	SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('V45.71','174')						-- Excluded Diagnosis codes
		OR CPP.Problem_Description LIKE '%mastectomy%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%cancer breast%'
		OR CPP.Problem_Description LIKE '%breast ca%'
		OR CPP.Problem_Description LIKE '%ca breast%')
		AND cpp.Deleted = 0 and cpp.UpdateDate is null

	-- select * from @MammogramExclusion where PatientRecordId in (select PatientRecordId from @BasePopulation)

	--- Mammogram Screening - current
	INSERT INTO @MammogramScreeningCurrent
	SELECT IM.PatientRecordId,DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) AS AgeMonths	
	FROM @BasePopulation BP
	join [VP_CPP_Immunization] IM on bp.PatientRecordId = im.PatientRecordId
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId=3							-- Type = Mamography
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) < 28  -- documented mammogram within 27 months
																												      
	 
	
	--- Mammogram Screening Declined
	INSERT INTO @MammogramScreeningDeclined
	SELECT IM.PatientRecordId,DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) AS AgeMonths	
	FROM @BasePopulation BP
	join [VP_CPP_Immunization] IM on bp.PatientRecordId = im.PatientRecordId
	WHERE IM.VP_CPP_ImmunizationStatusId=2						-- Status = Refused
	AND IM.VP_CPP_ImmunizationTypeId=3							-- Type = Mamography
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) <= 24
	
  -- select * from @MammogramScreeningDeclined where PatientRecordId in (select PatientRecordId from @BasePopulation)

	--- Mammogram Screening Pending
	INSERT INTO @MammogramScreeningPending
	SELECT bp.PatientRecordId,DATEDIFF(MONTH,req.testTime,GETDATE()) AS AgeMonths	
	FROM @BasePopulation bp
	join RequisitionPatient rp on bp.PatientRecordId = rp.PatientRecordId
	join Requisition req on rp.id = req.requisitionPatientId
	where req.isActive = 1
	and req.requisitionStatus = 2
	and req.requisitionItems like '%"test":"44"%'
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)											-- not excluded
		AND PatientRecordId not IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths <= 24)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths BETWEEN 25 AND 27)	    -- over due by 1- 3 months (month 25 - month 27)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningPending WHERE AgeMonths <= 24)				-- not pending for two years
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningDeclined WHERE AgeMonths <= 24)				-- not declined in two years

		INSERT INTO @Segments
	SELECT 2,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths <= 27)				-- not documented in last 27 months
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningDeclined WHERE AgeMonths <= 24)				-- not declined in two years
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningPending WHERE AgeMonths <= 24)				-- not pending in two years

		INSERT INTO @Segments
	SELECT 3,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths <= 24)						-- not documented in two years
		AND PatientRecordId IN (SELECT PatientRecordId FROM @MammogramScreeningPending WHERE AgeMonths < 3)						-- pending for 1 or 2 months

		INSERT INTO @Segments
	SELECT 4,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths <= 24)
		AND PatientRecordId not IN (SELECT PatientRecordId FROM @MammogramScreeningPending WHERE AgeMonths < 3)		-- not documented in two years
		AND PatientRecordId IN (SELECT PatientRecordId FROM @MammogramScreeningPending WHERE AgeMonths between 3 and 24)		-- pending for 3-24 months

		INSERT INTO @Segments
	SELECT 5,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths <= 24)
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningPending WHERE AgeMonths <= 24)
		AND PatientRecordId IN (SELECT PatientRecordId FROM @MammogramScreeningDeclined WHERE AgeMonths <= 24)

		INSERT INTO @Segments
	SELECT 6,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths <= 20)				-- not current
		AND PatientRecordId IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths BETWEEN 21 AND 24)		-- will be over due in 1-3 months

		INSERT INTO @Segments
	SELECT 7,PatientRecordId
		FROM @BasePopulation
		WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)											-- not excluded
		AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths <= 16)				-- not current in 16 months
		AND PatientRecordId IN (SELECT PatientRecordId FROM @MammogramScreeningCurrent WHERE AgeMonths BETWEEN 17 AND 20)		-- will be over due in 4-6 months

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

	END
