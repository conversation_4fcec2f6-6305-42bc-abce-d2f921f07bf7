﻿-- =============================================
-- Author:		<Author,Divyesh>
-- Create date: <Create Date,2017-07-11>
-- Description:	<Description,Get Appointment not in waitlist or cancelled>
-- =============================================
CREATE PROCEDURE [dbo].[GetUserPermissions]
	(@userId  INT)
AS
BEGIN
SELECT rp.PermissionId AS Id,rp.PermissionName,r.Name AS Role, rp.PermissionTypeId, pt.Name AS PermissionType, r.PracticeId FROM AspNetUsers u
	JOIN AspNetUserRoles AS ur ON u.Id =ur.UserId
	JOIN AspNetRoles AS r ON ur.RoleId=r.Id
	JOIN RolePermissions AS rp ON ur.RoleId=rp.ApplicationRoleId
	JOIN PermissionTypes AS pt ON rp.PermissionTypeId =pt.Id
	WHERE u.UserID=@userId
END




