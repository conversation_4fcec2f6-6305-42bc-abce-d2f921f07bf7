﻿CREATE PROCEDURE [dbo].[GetContactManagerTaskList]
	@taskSource varchar(12),
	@userId varchar(128),
	@practiceId int,
	@patientRecordId int,
	@rowStart int,
	@rowCount int,
	@readRequest int,
	@sortByColumn varchar(32),
	@sortByOrder varchar(8),
	@filterStatus varchar(8),
	@filterUrgency varchar(8),
	@filterDue varchar(16),
	@filterSubject varchar(64),
	@filterMessage varchar(64),
	@filterCreator varchar(64),
	@filterPatient varchar(64),
	@filterSeenat varchar(16),
	@filterMessageDate varchar(16)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @taskStatus_Open int = 0

	DECLARE @totalRow int
	DECLARE @sql nvarchar(4000)
	DECLARE @orderBySql varchar(64)
	DECLARE @offsetFetchSql varchar(64)
	DECLARE @orderByColumn varchar(64)

	IF (@taskSource = 'creator')
	BEGIN
		SET @sql = 'select a.id,a.taskStatus,a.dueDate,a.taskUrgency,a.subject,b.id messageId,b.message,b.messageCreatedate,e.firstName userFirstName,e.lastName userLastName,
		a.patientRecordId,null markSeenDate,ROW_NUMBER() over (partition by a.id order by b.id desc) rn  
		from CM_TaskDefinition a 
		inner join CM_TaskMessage b on a.id=b.CM_TaskDefinitionId 
		inner join AspNetUsers e on a.userId=e.id
        where a.userid = @userID
        and   b.messageCreateDate >= (cast(convert(varchar, getdate(), 112) as datetime)-30)'
	END
	ELSE IF (@taskSource = 'recipient')
	BEGIN
		SET @sql = 'select a.id,a.taskStatus,a.dueDate,a.taskUrgency,a.subject,b.id messageId,b.message,b.messageCreatedate,e.firstName userFirstName,e.lastName userLastName,
		a.patientRecordId,c.seenAt markSeenDate,ROW_NUMBER() over (partition by a.id order by b.id desc) rn  
		from CM_TaskDefinition a 
		inner join CM_TaskMessage b on a.id=b.CM_TaskDefinitionId 
		inner join CM_TaskMessageRecipient c on b.id=c.CM_TaskMessageId
		inner join AspNetUsers e on a.userId=e.id
		where c.userid = @userId 
        and   b.messageCreateDate >= (cast(convert(varchar, getdate(), 112) as datetime)-7)'

		IF (@filterSeenat <> '')
			SET @sql = @sql + FORMATMESSAGE(N' and convert(varchar(8),c.seenAt,112)=''%s''',@filterSeenat)
	END
	ELSE IF (@taskSource = 'commonfolder')
	BEGIN
		SET @sql = 'select a.id,a.taskStatus,a.dueDate,a.taskUrgency,a.subject,b.id messageId,b.message,b.messageCreatedate,e.firstName userFirstName,e.lastName userLastName,
		a.patientRecordId,null markSeenDate,ROW_NUMBER() over (partition by a.id order by b.id desc) rn  
		from CM_TaskDefinition a 
		inner join CM_TaskMessage b on a.id=b.CM_TaskDefinitionId 
		inner join AspNetUsers e on a.userId=e.id
		where a.practiceId = @practiceId  
        and   b.messageCreateDate >= (cast(convert(varchar, getdate(), 112) as datetime)-30)'
	END
	ELSE IF (@taskSource = 'patient')
	BEGIN
		SET @sql = 'select a.id,a.taskStatus,a.dueDate,a.taskUrgency,a.subject,b.id messageId,b.message,b.messageCreatedate,e.firstName userFirstName,e.lastName userLastName,
		a.patientRecordId,null markSeenDate,ROW_NUMBER() over (partition by a.id order by b.id desc) rn  
		from CM_TaskDefinition a 
		inner join CM_TaskMessage b on a.id=b.CM_TaskDefinitionId 
		inner join AspNetUsers e on a.userId=e.id
		where a.practiceId = @practiceId and a.PatientRecordId = @patientRecordId '
	END
	ELSE	--inbox
	BEGIN
		SET @sql = 'select a.id,a.taskStatus,a.dueDate,a.taskUrgency,a.subject,b.id messageId,b.message,b.messageCreatedate,e.firstName userFirstName,e.lastName userLastName,
		a.patientRecordId,null markSeenDate,ROW_NUMBER() over (partition by a.id order by b.id desc) rn  
		from CM_TaskDefinition a 
		inner join CM_TaskMessage b on a.id=b.CM_TaskDefinitionId 
		inner join CM_TaskMessageRecipient c on b.id=c.CM_TaskMessageId
		inner join AspNetUsers e on a.userId=e.id
        where a.taskStatus = @taskStatus and c.userid = @userID and c.seen=0 '
	END

	IF (@filterStatus <> '')
		SET @sql = @sql + N' and a.taskStatus=' + @filterStatus
	IF (@filterUrgency <> '')
		SET @sql = @sql + N' and a.taskUrgency=' + @filterUrgency
	IF (@filterDue <> '')
		SET @sql = @sql + FORMATMESSAGE(N' and convert(varchar(8),a.dueDate,112)<=''%s''',@filterDue)
	IF (@filterSubject <> '')
		SET @sql = @sql + FORMATMESSAGE(N' and a.subject like ''%s''','%' + @filterSubject + '%')
	IF (@filterMessage <> '')
		SET @sql = @sql + FORMATMESSAGE(N' and b.message like ''%s''','%' + @filterMessage + '%')
	IF (@filterMessageDate <> '')
		SET @sql = @sql + FORMATMESSAGE(N' and convert(varchar(8),b.messageCreatedate,112)=''%s''',@filterMessageDate)
	IF (@filterCreator <> '')
		SET @sql = @sql + FORMATMESSAGE(N' and (e.firstName like ''%s'' or e.lastName like ''%s'')','%' + @filterCreator + '%','%' + @filterCreator + '%')
/*	IF (@filterPatient <> '') -- Disable as per Galina's request on Feb 13, 2019
		SET @sql = @sql + FORMATMESSAGE(N' and (d.firstName like ''%s'' or d.lastName like ''%s'')','%' + @filterPatient + '%','%' + @filterPatient + '%')*/

    SET @totalRow = -1;

	IF OBJECT_ID('tempdb..##tempTaskList') IS NOT NULL
	DROP TABLE #tempTaskList

    Create table #tempTaskList
	(
		id int,
		taskStatus varchar(10),
		due varchar(20),
		taskUrgency varchar(20),
		subject nvarchar(1024),
		messageId varchar(16),
		message nvarchar(4000),
		userFirstName nvarchar(128),
        userLastName nvarchar(128),
        patientRecordId int,
		markSeen varchar(20),
		messageCreatedate varchar(20),
        PRIMARY KEY (ID)
	);


	SET @sql = N'INSERT INTO #tempTaskList(id,
                                          taskStatus,
                                          due,
                                          taskUrgency,
                                          subject,
                                          messageId,
                                          message,
                                          userFirstName,
                                          userLastName,
                                          patientRecordId,
                                          markSeen,
										  messageCreatedate)
                                   select id,
                                          taskStatus,
                                          dueDate due,
                                          taskUrgency,
                                          subject,
                                          messageId,
                                          message,
                                          ltrim(rtrim(userFirstName)) userFirstName,
                                          ltrim(rtrim(userLastName)) userLastName,
				                          patientRecordId,
                                          markSeenDate markSeen,
										  messageCreatedate
                                    from (' + @sql + ') s where s.rn=1;
                                 SELECT @total = @@rowcount'

    EXEC sp_executesql @sql, N'@taskStatus int, @userID nvarchar(128), @practiceId int, @patientRecordId int, @total int OUTPUT', 
                       @taskStatus = @taskStatus_Open, @userID = @userID, @practiceId = @practiceId, @patientRecordId = @patientRecordId, @total = @totalRow OUTPUT;

/*
	IF (@readRequest = 1 or @readRequest = 2)
	BEGIN
		SET @sqlCounter = 'select @total=count(*) from (' + @sql + ') t'
		EXEC sp_executesql @sqlCounter, N'@total int OUTPUT', @total = @totalRow OUTPUT
	END
*/
	IF (@totalRow <> 0)
	BEGIN

		IF OBJECT_ID('tempdb..##ttempDocumentList') IS NOT NULL
		DROP TABLE #tempDocumentList

		Create table #tempDocumentList
	   (
			pkid int identity(1,1) not null,
            id int,
			status varchar(10),
			dueDate varchar(20),
			urgency varchar(20),
			subject nvarchar(1024),
			lastMessageId varchar(16),
			lastMessage nvarchar(4000),
			taskCreator varchar(128),
			patientName varchar(128),
			patientRecordId int,
			isOpen bit,
			isLastReviewer bit,
			markSeenDate varchar(20),
			messageTime varchar(20), 
            PRIMARY KEY (pkid)
		);
        
		set @sql = N'select x.id,
                            (case when x.taskStatus=0 then ''Open'' else ''Closed'' end) status,
					        (case when x.due is null then '''' else convert(varchar(10),x.due, 101) end) dueDate,
					        (case when x.taskUrgency=0 then ''Elective'' when x.taskUrgency=1 then ''Semi Urgent'' else ''Urgent'' end) urgency,
					        x.subject,
                            convert(varchar(16),x.messageId) lastMessageId,
                            x.message lastMessage,
                            x.userLastName+(case when x.userFirstName is null then '''' else '', '' + x.userFirstName end) taskCreator,
                            x.patientrecordid, 
					        cast((case when x.taskStatus=0 then 1 else 0 end) as bit) isOpen,
                            cast(1 as bit) isLastReviewer, 
					        (case when x.markSeen is null then '''' else convert(varchar(10),x.markSeen, 101) end) markSeenDate,
							(case when x.messageCreatedate is null then '''' else convert(varchar(19),x.messageCreatedate, 120) end) messageTime
                        from #tempTaskList x ';
 
		SET @offsetFetchSql = FORMATMESSAGE(' offset %d rows fetch next %d rows only',@rowStart,@rowCount)
		SET @orderByColumn = (CASE WHEN @sortByColumn='status' THEN 'taskStatus'
									WHEN @sortByColumn='due' THEN 'due'
									WHEN @sortByColumn='urgency' THEN 'taskUrgency'
									WHEN @sortByColumn='subject' THEN 'subject'
									WHEN @sortByColumn='message' THEN 'message'
									WHEN @sortByColumn='creator' THEN 'taskCreator'
									WHEN @sortByColumn='seenat' THEN 'markSeen'
									WHEN @sortByColumn='messagetime' THEN 'messageCreatedate'
									ELSE 'messageCreatedate' END)
		SET @orderBySql = ' order by ' + @orderByColumn + (CASE WHEN @sortByOrder='asc' THEN ' asc' ELSE ' desc' END)
		SET @sql = @sql + @orderBySql + @offsetFetchSql

--		EXEC ('insert into #tempDocumentList (id,status,dueDate,urgency,subject,lastMessageId,lastMessage,taskCreator,patientRecordId,patientName,isOpen,isLastReviewer,markSeenDate) ' + @sql)
--		EXEC ('update #tempDocumentList set isLastReviewer=(case when b.id is null then 0 else 1 end) from #tempDocumentList a left join CM_TaskMessageRecipient b on a.lastMessageId=b.CM_TaskMessageId and b.userid=''' + @userId + '''')

        SET @sql = N'insert into #tempDocumentList (id,status,dueDate,urgency,subject,lastMessageId,lastMessage,taskCreator,patientRecordId,isOpen,isLastReviewer,markSeenDate,messageTime) ' + @sql;

--        print @sql

        EXEC sp_executesql @sql, N'@taskSource int, @userID nvarchar(128), @practiceId int, @patientRecordId int', @taskSource = @taskStatus_Open, @userID = @userID, @practiceId = @practiceId, @patientRecordId = @patientRecordId;

        SELECT dl.id,
               dl.status,
               dl.dueDate,
               dl.urgency,
               dl.subject,
               dl.lastMessageId,
               dl.lastMessage,
               dl.taskCreator,
               IsNull(upper(d.lastName)+(case when d.firstName is null then '' else ', ' + d.firstName end),'') as patientName, 
               Isnull(dl.patientRecordId, 0) as patientRecordId,
               dl.isOpen,
               cast(IsNull(( select top 1 1 from CM_TaskMessageRecipient b where dl.lastMessageId=b.CM_TaskMessageId and b.userid=@userId ),0) as bit) as isLastReviewer,
               dl.markSeenDate,
			   dl.messageTime
          FROM #tempDocumentList dl 
     LEFT JOIN Demographics d on (dl.patientrecordid = d.patientrecordid)
      ORDER BY pkid;

		DROP TABLE #tempDocumentList
	END

--    print cast(@totalRow as varchar)

	return @totalRow
END
