﻿CREATE PROCEDURE [dbo].[Get_VP_Summary]

@PracticeID int ,
@AppointmentID int ,
@Username	   nvarchar(512)

AS


declare @MainDoctorID    int , 
		@PracticeDoctorID  int , -- appointment practice doctor id
		@UserID		     int ,
		@PatientID       int ,
		@OfficeID	     int ,
		@SpecialtyCodes  int ,
		@ReportTemplate  int ,
		@TestID			 int ,
		@IsImported		 int ,
		@BillStatusId    int ,

		@ConsultCode     int , 
		@ConsultCode2    int , 
		@ConsultCode3    int ,

		@ConsultCodeCode   nvarchar(200) , 
		@ConsultCodeCode2  nvarchar(200) , 
		@ConsultCodeCode3  nvarchar(200) , 

		@DiagnosisCode    int,
		@DiagnosisCode2   int,
		@DiagnosisCode3   int,

		@DCCode			nvarchar(1024) ,
		@DCDiagnosis	nvarchar(2048) , 

		@DCCode2		nvarchar(1024) ,
		@DCDiagnosis2	nvarchar(2048) , 

		@DCCode3		nvarchar(1024) ,
		@DCDiagnosis3	nvarchar(2048) , 
		

		@AppointmentDate  datetime;


SELECT 
	@PatientID		 = a.PatientRecordId,
	@OfficeID		 = a.OfficeId ,
	@AppointmentDate = a.appointmentTime,
	@IsImported      = 0 --a.IsImported
FROM 
	Appointments a
	JOIN PatientRecords pr ON a.PatientRecordId=pr.Id
where pr.PracticeId=@PracticeID AND a.Id = @AppointmentID;


SELECT 
		@UserID =  u.UserID
FROM 
		aspnetusers u 
WHERE
		u.UserName = @Username;

 
 SELECT 
	@MainDoctorID	= e.Id,
	@ReportTemplate = e.ReportTemplate,
	@PracticeDoctorID = P.Id
 FROM 
	ExternalDoctors e join PracticeDoctors P on e.Id = p.ExternalDoctorId
 WHERE
	p.Id = ( SELECT PracticeDoctorId FROM Appointments where Id= @AppointmentID );


	--SELECT 
	--@MainDoctorID	= e.Id 
	----@ReportTemplate = e.ReportTemplate
 --FROM 
	--ExternalDoctors e join DemographicsMainResponsiblePhysicians P on e.Id = p.ExternalDoctorId
 --WHERE
	--p.DemographicId = ( SELECT Id FROM Demographics where PatientRecordId= @PatientID );

	

SELECT 
	@SpecialtyCodes = p.specialtyCodes 
FROM
    Appointments a join PracticeDoctors p on a.PracticeDoctorId = p.Id
  WHERE p.PracticeId=@PracticeID AND a.Id = @AppointmentID;


SELECT 
	@TestID = T.Id 
FROM
	Tests t 
WHERE	 
	T.testShortName = 'VP';


SELECT 
	@ConsultCode   = b.ConsultCode		,
	@ConsultCode2  = b.ConsultCode2		,
	@ConsultCode3  = b.ConsultCode3		,
	@DiagnosisCode = b.DiagnosticCode	,
	@DiagnosisCode2 = b.DiagnosticCode2	,
	@DiagnosisCode3 = b.DiagnosticCode3	,
	@BillStatusId  = b.billStatusId
FROM 
	AppointmentBills b 
WHERE
	b.AppointmentID = @AppointmentID;


SELECT 
	@ConsultCodeCode = C.Code
FROM 
	ConsultCodes C
WHERE
	C.Id = @ConsultCode;

SELECT 
	@ConsultCodeCode2 = C.Code
FROM 
	ConsultCodes C
WHERE
	C.Id = @ConsultCode2;

SELECT 
	@ConsultCodeCode3 = C.Code
FROM 
	ConsultCodes C
WHERE
	C.Id = @ConsultCode3;


SELECT 
		@DCCode	     = D.Code,
		@DCDiagnosis = D.Diagnosis
FROM 
	 DiagnoseCodes d
where
	D.Id = @DiagnosisCode;


SELECT 
		@DCCode2	  = D.Code,
		@DCDiagnosis2 = D.Diagnosis
FROM 
	 DiagnoseCodes d
where
	D.Id = @DiagnosisCode2;
	
SELECT 
		@DCCode3	  = D.Code,
		@DCDiagnosis3 = D.Diagnosis
FROM 
	 DiagnoseCodes d
where
	D.Id = @DiagnosisCode3;



SELECT 
	isnull(@MainDoctorID ,0)   as MainDoctorID , 
	isnull(@practiceDoctorId ,0)   as PracticeDoctorID , 
	isnull(@UserID,0)		     as UserID,
	@PatientID       as PatientID,
	@OfficeID        as OfficeID,
	@SpecialtyCodes  as SpecialtyCodes,
	@ReportTemplate  as ReportTemplate,
	@AppointmentDate as AppointmentDate,
	@TestID          as TestID,
	@IsImported      as IsImported ,
	
	isnull(@ConsultCode ,0)  as ConsultCode , 
	isnull(@ConsultCode2,0)  as ConsultCode2, 
	isnull(@ConsultCode3,0)  as ConsultCode3,

	 
	@ConsultCodeCode  as ConsultCodeCode, 
	@ConsultCodeCode2 as ConsultCodeCode2, 
	@ConsultCodeCode3 as ConsultCodeCode3,

	isnull(@DiagnosisCode  ,0)  as DignosisCode,
	isnull(@DiagnosisCode2 ,0)  as DignosisCode2,
	isnull(@DiagnosisCode3 ,0)  as DignosisCode3,

	@DCCode			 as DCCode,
	@DCDiagnosis	 as DCDiagnosis,
	@DCCode2		 as DCCode2,
	@DCDiagnosis2	 as DCDiagnosis2,
	@DCCode3		 as DCCode3,
	@DCDiagnosis3	 as DCDiagnosis3 ;



	
	 



	
	 



	
	 



	
	 



	
	 



	
	 
