CREATE PROCEDURE [dbo].[GetReportClinicDailyRegister]
	@PracticeId INT,
	@OfficeIds dbo.IntegerList READONLY,
	@AssignedToUserIds dbo.IntegerList READONLY,
	@StartDate DATETIME,
	@EndDate DATETIME
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @fullStatementSql NVARCHAR(MAX);  
	DECLARE @querySql NVARCHAR(4000);
	DECLARE @whereSql NVARCHAR(1000);
    DECLARE @paramsSql NVARCHAR(1000);  	

	SET @StartDate = CAST(CONVERT(VARCHAR, @StartDate, 112)+' 00:00:00' AS DATETIME);
	SET @EndDate = CAST(CONVERT(VARCHAR, @EndDate, 112)+' 23:59:59' AS DATETIME);	

	DECLARE @ResultSetTable AS TABLE (
		[AppointmentId] INT NOT NULL,
		[TestId] INT NOT NULL,
		[OfficeId] INT NOT NULL,
		[OfficeName] NVARCHAR(100),
		[PatientRecordId] INT NOT NULL,
		[PatientName] NVARCHAR(500),
		[PatientPhoneNumbers] NVARCHAR(300),
		[PatientEmail] VARCHAR(100),
		[ReferralDoctorId] INT NOT NULL,
		[ReferralDoctorName] NVARCHAR(500),
		[Service] NVARCHAR(100),
		[Modality] NVARCHAR(100),
		[AppointmentTime] DATETIME
	);

	DECLARE @Appointments AS TABLE (
		[AppointmentId] INT NOT NULL,
		[OfficeId] INT NOT NULL,
		[PatientRecordId] INT NOT NULL,
		[ReferralDoctorId] INT NOT NULL,
		[TestId] INT NOT NULL,
		[TestShortName] NVARCHAR(300),
		[AppointmentTime] DATETIME
	)	

	SET @querySql = 
	'SELECT AP.Id AS AppointmentId,AP.OfficeId,AP.PatientRecordId,AP.ReferralDoctorId,T.Id AS TestId,T.TestShortName,AP.appointmentTime
	FROM Appointments AP	
	JOIN AppointmentTests APT ON APT.AppointmentId = AP.Id
	JOIN Office O ON O.Id = AP.OfficeId
	JOIN Tests T ON T.Id = APT.TestId '

	SET @whereSql = N' WHERE O.PracticeId = @PracticeId AND AP.appointmentTime BETWEEN @StartDate AND @EndDate ';
	SET @whereSql = @whereSql + N' AND AP.appointmentStatus <> 7 AND AP.IsActive = 1 AND APT.IsActive = 1 '	

	IF EXISTS(SELECT TOP 1 1 FROM @OfficeIds)
	BEGIN
		SET @whereSql = @whereSql + N' AND AP.OfficeId IN (SELECT IntegerValue FROM @OfficeIds) ';
	END

	IF EXISTS (SELECT TOP 1 1 FROM @AssignedToUserIds)
	BEGIN		
		SET @whereSql = @whereSql + + N' AND EXISTS (SELECT TOP 1 1 FROM AppointmentTestResources RES, AspNetUsers USR WHERE RES.AppointmentTestId = APT.Id AND RES.assignedToUserId = USR.UserId AND RES.IsActive = 1 AND RES.assignedToUserId IN (SELECT IntegerValue FROM @AssignedToUserIds) ) ';
	END
	ELSE
	BEGIN
		SET @whereSql = @whereSql + + N' AND EXISTS (SELECT TOP 1 1 FROM AppointmentTestResources RES, AspNetUsers USR WHERE RES.AppointmentTestId = APT.Id AND RES.assignedToUserId = USR.UserId AND RES.IsActive = 1) ';
	END	

	SET @fullStatementSql = @querySql + @whereSql;	

	--PRINT @fullStatementSql;

	SET @paramsSql = N'@PracticeId INT, @OfficeIds IntegerList READONLY, @StartDate DATETIME, @EndDate DATETIME, @AssignedToUserIds IntegerList READONLY';

	INSERT INTO @Appointments ([AppointmentId],[OfficeId],[PatientRecordId],[ReferralDoctorId],[TestId],[TestShortName],[AppointmentTime])
	EXEC sp_executesql @fullStatementSql, @paramsSql, @PracticeId = @PracticeId, @OfficeIds = @OfficeIds, @StartDate = @StartDate, @EndDate = @EndDate, @AssignedToUserIds = @AssignedToUserIds

	--> Pull demographic data
	INSERT INTO @ResultSetTable ([AppointmentId],[TestId],[OfficeId],[OfficeName],[PatientRecordId],[PatientName],[PatientPhoneNumbers],[PatientEmail],[ReferralDoctorId],[ReferralDoctorName],[Service],[Modality],[AppointmentTime])
	SELECT A.AppointmentId,A.TestId,A.OfficeId,O.[name],A.PatientRecordId,
	ISNULL(D.LastName + ', ','') + ISNULL(D.firstName,'') AS [PatientName],
	(SELECT STUFF(    
      (  
	   SELECT ', '+ CAST(ph.phoneNumber AS VARCHAR(500)) + ' '+   
		  (CASE ph.typeOfPhoneNumber  
		   WHEN 0 THEN 'H'  
		   WHEN 1 THEN 'C'  
		   WHEN 2 THEN 'W'  
		   ELSE '' END)  
		FROM DemographicsPhoneNumbers ph  
		WHERE phoneNumber IS NOT NULL AND LTRIM(RTRIM(phoneNumber)) <> '' and isRemoved = 0  
		AND   ph.DemographicId = d.Id  
		ORDER BY  DemographicId, typeOfPhoneNumber, isActive DESC, Id DESC  
		FOR XMl PATH('')),1,1,''  )) AS [PatientPhoneNumbers],
	D.[Email] AS [PatientEmail],
	A.ReferralDoctorId,
	ISNULL(ED.LastName + ', ','') + ISNULL(ED.firstName,'') AS [ReferralDoctorName],
	A.TestShortName AS [Service],
	(SELECT TOP 1 M.modalityName FROM TestModalities T JOIN Modalities M ON T.ModalityId = M.Id WHERE T.TestId = A.TestId AND T.IsActive = 1) AS [Modality],
	A.AppointmentTime AS [AppointmentTime]
	FROM @Appointments A
	JOIN Demographics D ON A.PatientRecordId = D.PatientRecordId
	JOIN Office O ON O.Id = A.OfficeId
	JOIN ExternalDoctors ED ON ED.Id = A.ReferralDoctorId;	

	SELECT [AppointmentId],[OfficeId],[OfficeName],[PatientRecordId],[PatientName],[PatientPhoneNumbers],[PatientEmail],[ReferralDoctorId],[ReferralDoctorName],[Service],[Modality],[AppointmentTime]
	FROM @ResultSetTable
	ORDER BY [OfficeId] ASC, [AppointmentTime] ASC, [AppointmentId] ASC;

END