﻿


CREATE PROCEDURE [dbo].[GetExternalDocumentList_v2]
	@patientRecordId int,
	@practiceDoctorId int,
	@practiceId int,
	@rowStart int,
	@rowCount int,
	@totalRowRequest int,
	@sortByColumn varchar(32),
	@sortByOrder varchar(8),
	@filterIncludeHL7 bit,
	@filterIncludeFax bit,
	@filterIncludeHRM bit,
	@filterIncludeScan bit,
	@filterClassId int,
	@filterCategoryId int,
	@filterIncludeMarkseen bit,
	@filterMarkseenStart varchar(16),
	@filterMarkseenEnd varchar(16),
	@filterReceivedDate varchar(16),
	@filterTestDate varchar(16),
	@filterPatient varchar(256),
	@filterDescription varchar(256),
	@filterComment varchar(256)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @totalRow int
	DECLARE @sql nvarchar(max)
	DECLARE @sqlCounter nvarchar(max)
	DECLARE @selectSql nvarchar(4000)
	DECLARE @tableSql nvarchar(2048)
	DECLARE @whereSql nvarchar(2048)
	DECLARE @orderBySql nvarchar(200)
	DECLARE @orderByCount nvarchar(200)
	DECLARE @offsetFetchSql nvarchar(200)
	DECLARE @orderByColumn nvarchar(200)
	DECLARE @params nvarchar(1000)
	DECLARE @paramsCT nvarchar(1000)
	DECLARE @filterMarkseenStartDt datetime;
	DECLARE @filterMarkseenEndDt datetime;
	DECLARE @filterReceivedDateStart datetime;
	DECLARE @filterReceivedDateEnd datetime;
	DECLARE @filterTestDateStart datetime;
	DECLARE @filterTestDateEnd datetime;
	DECLARE @sfilterPatient nvarchar(256);
	DECLARE @reporttype nvarchar(10);
	DECLARE @filterTestDateStartUTC datetime;
	DECLARE @filterTestDateEndUTC datetime;
	DECLARE @filterReceivedDateStartUTC datetime;
	DECLARE @filterReceivedDateEndUTC datetime;
	DECLARE @filterMarkseenStartUTC datetime;
	DECLARE @filterMarkseenEndUTC datetime;

	IF OBJECT_ID('tempdb..##tempDocumentList') IS NOT NULL
		DROP TABLE #tempDocumentList

	IF OBJECT_ID('tempdb..##SearchedPatient') IS NOT NULL
			DROP TABLE #SearchedPatient

	Create table #tempDocumentList(
		id int,
		arrivedAfterLastAppointment bit,
		abnormal bit,
		markSeen bit,
		lastAppointmentDate datetime2,
		receivedDateTime datetime2,
		testDateTime datetime2,
		patientFirstName varchar(100),
		patientLastName varchar(100),
		demographicId int,
		patientRecordId int,
		description nvarchar(1024),
		comment nvarchar(1024),
		documentType nvarchar(10),
		hrmFacilityId nvarchar(128),
		hrmReportNumber nvarchar(512),
		isActive bit,
		totalRow int,
		rn int
	)

	IF (@filterPatient <> '') -- When there is a filter by patient's name, join table variable to filter out records. Otherwise, join Demographic table in the pagination query
	begin
		SET @sfilterPatient = '"'+@filterPatient+'*"'
		
		CREATE TABLE #SearchedPatient
		(
			patientRecordId int not null,
			Id int not null,
			FirstName varchar(100),
			LastName varchar(100),
			CONSTRAINT TSearchedPatient_PK PRIMARY KEY (patientRecordId)
		)

		-- Prefetch patients to perform 1 search instead of 3.
		insert into #SearchedPatient(patientRecordId, Id, FirstName, LastName)
			select d.patientRecordId, d.id, d.firstName, d.lastName
				from Demographics d,
					PatientRecords pr
				where d.patientRecordId = pr.id
				and   pr.PracticeID = @practiceId
				and   contains ((d.lastname, d.firstname), @sfilterPatient);
				--and   (d.lastName like @filterPatient or d.firstName like @filterPatient); --Index full scan / not use

	end

	IF (@filterMarkseenStart <> N'')
		SELECT @filterMarkseenStartDt = convert(varchar, convert(datetime, @filterMarkseenStart, 120), 112)+' 00:00:00';
	IF (@filterMarkseenEnd <> N'')
		SELECT @filterMarkseenEndDt = convert(varchar, convert(datetime, @filterMarkseenEnd, 120), 112)+' 23:59:59';

	SELECT @filterReceivedDateStart = convert(varchar, convert(datetime, @filterReceivedDate, 120), 112)+' 00:00:00';
	SELECT @filterReceivedDateEnd = convert(varchar, convert(datetime, @filterReceivedDate, 120), 112)+' 23:59:59';

	SELECT @filterTestDateStart = convert(varchar, convert(datetime, @filterTestDate, 120), 112)+' 00:00:00';
	SELECT @filterTestDateEnd = convert(varchar, convert(datetime, @filterTestDate, 120), 112)+' 23:59:59';

	SET @sql = ''
	IF (@filterIncludeFax = 1) or (@filterIncludeHRM = 1)
	BEGIN
		SET @reporttype = ''
		IF @filterIncludeFax = 1
			SET @reporttype = '0'
		IF @filterIncludeHRM = 1
			SET @reporttype = @reporttype + case when len(@reporttype) > 0 then ',' else '' end + '2'

		SET @selectSql = N' select convert(nvarchar(512), ROW_NUMBER() over (order by aa.id)) as rnpart'
		SET @selectSql = @selectSql + N',aa.id,(cast(case when aa.abnormal is null then 0 else aa.abnormal end as bit)) abnormal,aa.receivedDateTime,(case when aa.testDate is null then aa.ObservationDateTime else aa.testDate end) testDateTime'

		IF (@filterPatient <> '') -- When there is a filter by patient's name, join table variable to filter out records. Otherwise, join Demographic table in the pagination query
			SET @selectSql = @selectSql + N',ac.lastName patientLastName,ac.firstName patientFirstName, ac.id as demographicId'

		SET @selectSql = @selectSql + N',aa.patientRecordId,aa.description,case when aa.reporttype = 0 then ''fax'' else ''hrm'' end documentType,aa.looseReportCategoryId categoryId,aa.reportClassId classId,aa.sendingFacilityId hrmFacilityId'
		SET @selectSql = @selectSql + N',aa.sendingFacilityReport hrmReportNumber,(case when aa.status is null then 1 else aa.status end) isActive,aa.reportType,aa.hrmVersion'

		SET @tableSql = N' from ReportReceiveds aa inner join PatientRecords ab on aa.PatientRecordId=ab.id inner join @reporttypetab rt on aa.reportType = rt.reportType '
        
		SET @whereSql = N' where (aa.status is null or aa.status=1) and aa.assignmentStatus <> 2 and ab.practiceid = @practiceId'

		IF (@filterPatient <> '') -- When there is a filter by patient's name, join table variable to filter out records. Otherwise, join Demographic table in the pagination query
		begin 
--patient filter test
			--SET @tableSql = @tableSql + ' join Demographics ac on ab.id=ac.PatientRecordId'
			--SET @whereSql = @whereSql + FORMATMESSAGE(' and (ac.lastName like ''%s'' or ac.firstName like ''%s'')','%' + @filterPatient + '%','%' + @filterPatient + '%')

			SET @tableSql = @tableSql+N' inner join #SearchedPatient ac on ab.id=ac.PatientRecordId'
		end

		IF (@patientRecordId = 0)
		BEGIN
			SET @selectSql = @selectSql + N',(case when ad.dateTimeReportReviewed is null then 0 else 1 end) markSeen,ad.dateTimeReportReviewed markSeenDateTime,ad.ReportReviewedNotes comment'
			SET @tableSql = @tableSql + N' inner join DoctorsReportRevieweds ad on ad.ReportReceivedId=aa.id'
			SET @whereSql =  @whereSql + N' and ad.practiceDoctorId = @practiceDoctorId'

			--filter by mark seen
			IF (@filterIncludeMarkseen = 0)
				SET @whereSql = @whereSql +N' and ad.dateTimeReportReviewed is null' -- Exclude Marked Seen
			ELSE
			BEGIN
				IF (@filterMarkseenStartDt is not null)
					SET @whereSql = @whereSql + N' and (ad.dateTimeReportReviewed is null or ad.dateTimeReportReviewed >= @filterMarkseenStartDt)'
				IF (@filterMarkseenEndDt is not null)
					SET @whereSql = @whereSql + N' and (ad.dateTimeReportReviewed is null or ad.dateTimeReportReviewed <= @filterMarkseenEndDt)'
			END

			IF (@filterComment <> '')
				SET @whereSql = @whereSql + N' and ad.ReportReviewedNotes like ''%'+@filterComment+'%'''
		END
		ELSE
		BEGIN
			SET @selectSql = @selectSql + ',(case when ad.dateTimeReportReviewed is null then 0 else 1 end) markSeen,ad.dateTimeReportReviewed markSeenDateTime,ad3.ReportReviewedNotes comment'
			SET @tableSql = @tableSql + ' left join DoctorsReportRevieweds ad on aa.id=ad.ReportReceivedId and ad.dateTimeReportReviewed is not null and ad.id=(select top 1 id from DoctorsReportRevieweds ad2 where aa.id=ad2.ReportReceivedId and ad2.dateTimeReportReviewed is not null order by ad2.dateTimeReportReviewed desc)'
			SET @tableSql = @tableSql + + case when @filterComment <> '' then N'' else N' left' end+N' join DoctorsReportRevieweds ad3 on aa.id=ad3.ReportReceivedId and ad3.ReportReviewedNotes is not null and ad3.id=(select top 1 id from DoctorsReportRevieweds ad4 where aa.id=ad4.ReportReceivedId and ad4.ReportReviewedNotes is not null order by ad4.dateTimeReportReviewed desc)'
			SET @whereSql = @whereSql + N' and aa.PatientRecordId = @patientRecordId'

			IF (@filterComment <> '')
				SET @tableSql = @tableSql + N' and ad3.ReportReviewedNotes like ''%'+@filterComment+'%'''
		END

		IF (@filterReceivedDate <> '')
			SET @whereSql = @whereSql + N' and aa.receivedDateTime between @filterReceivedDateStart and @filterReceivedDateEnd'
		IF (@filterTestDate <> '')
			SET @whereSql = @whereSql + N' and IsNull(aa.testDate,aa.ObservationDateTime) between @filterTestDateStart and @filterTestDateEnd'
		IF (@filterClassId <> 0)
			SET @whereSql = @whereSql + N' and aa.reportClassId = @filterClassId'
		IF (@filterCategoryId <> 0)
			SET @whereSql = @whereSql + N' and aa.looseReportCategoryId = @filterCategoryId'
		IF (@filterDescription <> '')
			SET @whereSql = @whereSql + N' and aa.description like ''%'+@filterDescription+'%'''

		SET @sql = @selectSql + @tableSql + @whereSql
		SET @sql = N'select ROW_NUMBER() over (partition by hrmFacilityId, case when reporttype = 0 then rnpart else hrmReportNumber end,patientRecordId,reporttype order by hrmVersion desc) as rn,* from (' + @sql + ') hrm '

		SET @sql = N'select id,abnormal,receivedDateTime,testDateTime'+case when @filterPatient <> '' then ',patientLastName,patientFirstName,demographicId' else '' end+',patientRecordId,description,documentType,categoryId,classId,hrmFacilityId,hrmReportNumber,isActive,markSeen,markSeenDateTime,comment from (' + @sql + ') hrm2 where rn=1'
	END

	-- HL7 is selected and either the description filter is not used or the description string matches with parto of Lab HL7
	IF (@filterIncludeHL7 = 1) 	
	BEGIN
		IF ( (@filterDescription = '') or (charindex(@filterDescription, 'Lab HL7') > 0) ) 
		begin
			SET @selectSql = N'select ba.id, cast(null as bit) abnormal,ba.createdDate receivedDateTime,ba.collectionDateTime testDateTime'
			IF (@filterPatient <> '') -- When there is a filter by patient's name, join table variable to filter out records. Otherwise, join Demographic table in the pagination query
				SET @selectSql = @selectSql + N',bd.lastName patientLastName,bd.firstName patientFirstName,bd.id as demographicId'

			SET @selectSql = @selectSql + N',bb.patientRecordId,''Lab HL7'' description,''hl7'' documentType,0 categoryId,0 classId,'''' hrmFacilityId'
			SET @selectSql = @selectSql + N','''' hrmReportNumber,1 isActive'

			SET @tableSql = N' from HL7Report ba inner join HL7Patient bb on ba.HL7PatientId=bb.id inner join PatientRecords bc on bb.PatientRecordId=bc.id'

			SET @whereSql = N' where bc.practiceid=@practiceId'

			IF (@patientRecordId = 0)
			BEGIN
				SET @tableSql = @tableSql + N' inner join HL7ReportDoctor be on ba.Id=be.HL7ReportId'
				SET @whereSql = @whereSql + N' and be.practiceDoctorId = @practiceDoctorId'
			END
			ELSE
				SET @whereSql = @whereSql + N' and bc.Id = @patientRecordId'

			IF (@filterPatient <> '') -- When there is a filter by patient's name, join table variable to filter out records. Otherwise, join Demographic table in the pagination query
			begin
--patient filter test
				--SET @tableSql = @tableSql+N' inner join Demographics bd on bc.Id=bd.PatientRecordId'
				--SET @whereSql = @whereSql + FORMATMESSAGE(' and (bd.lastName like ''%s'' or bd.firstName like ''%s'')','%' + @filterPatient + '%','%' + @filterPatient + '%')

				SET @tableSql = @tableSql+N' inner join #SearchedPatient bd on bc.Id=bd.PatientRecordId'
			end;

	--		SET @tableSql = @tableSql + N' left join (select HL7ReportId,abnormalFlag, bh.id HL7ResultId from HL7ReportVersion bg inner join HL7Result bh on bg.Id=bh.HL7ReportVersionId where bh.abnormalFlag<>''N'' and bh.abnormalFlag<>''No'' and bh.abnormalFlag<>''Normal'') bi on bi.HL7ReportId=ba.Id and bi.HL7ResultId=(select top 1 bhh.id HL7ResultId from HL7ReportVersion bgg inner join HL7Result bhh on bgg.Id=bhh.HL7ReportVersionId where bgg.HL7ReportId=ba.id and bhh.abnormalFlag<>''N'' and bhh.abnormalFlag<>''No'' and bhh.abnormalFlag<>''Normal'')'
	--		SET @tableSql = @tableSql + N' left join (select HL7ReportId,(select top 1 abnormalFlag from HL7Result bh where bg.Id = bh.HL7ReportVersionId AND ltrim(rtrim(bh.abnormalFlag)) NOT IN (''N'', ''No'', ''Normal'')) abnormalFlag from HL7ReportVersion bg ) bi on bi.HL7ReportId=ba.Id '

			IF (@patientRecordId = 0)
			BEGIN
				SET @selectSql = @selectSql + N',(case when bj.seenAt is null then 0 else 1 end) markSeen,bj.seenAt markSeenDateTime,bj.comment'

--Rownum test
--				SET @tableSql = @tableSql + case when @filterComment <> '' then N'' else N' left' end+N' join HL7MarkedSeen bj on bj.HL7ReportId=ba.Id and bj.practiceDoctorId=@practiceDoctorId ' --If filtered by comment, then it's a regular join. Otherwise it's a left join
				SET @tableSql = @tableSql + case when @filterComment <> '' then N'' else N' left' end+N' join (select  HL7ReportId, seenAt, comment from ( select HL7ReportId, seenAt, comment, row_number() over (partition by HL7ReportId order by seenAt DESC) as rn from HL7MarkedSeen where practiceDoctorId=@practiceDoctorId '

				IF (@filterComment <> '')
					SET @tableSql = @tableSql + N' and comment like ''%'+@filterComment+'%''' --Filter by comments it's only possible if marked seen HL7 documents are included

--Rownum test
--				SET @tableSql = @tableSql + N' and bj.id=(select top 1 id from HL7MarkedSeen bk where bk.HL7ReportId=ba.Id and bk.practiceDoctorId = @practiceDoctorId order by bk.seenAt desc)'
				SET @tableSql = @tableSql + ' ) z where rn = 1 ) bj ON bj.HL7ReportId = ba.Id'
			END	
			ELSE
			BEGIN
				SET @selectSql = @selectSql + N',(case when bj.seenAt is null then 0 else 1 end) markSeen,bj.seenAt markSeenDateTime'
				SET @tableSql = @tableSql + N' left join HL7MarkedSeen bj on bj.HL7ReportId=ba.Id '

				SET @tableSql = @tableSql + N' and bj.id=(select top 1 id from HL7MarkedSeen bk where bk.HL7ReportId=ba.Id order by bk.seenAt desc)'

				SET @selectSql = @selectSql + N',bl.comment'
				--If filtered by comment, then it's a regular join. Otherwise it's a left join
				SET @tableSql = @tableSql + case when @filterComment <> '' then N'' else N' left' end +N' join HL7MarkedSeen bl on bl.HL7ReportId=ba.Id and bl.comment is not null and bl.comment<>'''''

				IF (@filterComment <> '')
					SET @tableSql = @tableSql + N' and bl.comment like ''%'+@filterComment+'%'''

				SET @tableSql = @tableSql + ' and bl.id=(select top 1 id from HL7MarkedSeen bm where bm.HL7ReportId=ba.Id order by bm.seenAt desc)'
			END	

			IF (@filterReceivedDate <> '')
				SET @whereSql = @whereSql + N' and ba.createdDate between @filterReceivedDateStart and @filterReceivedDateEnd'
			IF (@filterTestDate <> '')
				SET @whereSql = @whereSql + N' and ba.collectionDateTime between @filterTestDateStart and @filterTestDateEnd'
		end
		else
		begin
			-- Empty query returning 0 rows used to not break dynamic join when filter by description does not include Labs HL7
			SET @selectSql = N'select cast(null as int) as id, cast(null as bit) as abnormal, cast(null as datetime) as receivedDateTime, cast(null as datetime) as testDateTime'
            IF (@filterPatient <> '') -- When there is a filter by patient's name, join table variable to filter out records. Otherwise, join Demographic table in the pagination query 
			   SET @selectSql = @selectSql + N', cast(null as nvarchar(100)) as patientLastName, cast(null as nvarchar(100)) as patientFirstName, cast(null as int) as demographicId'
			SET @selectSql = @selectSql + N', cast(null as int) as patientRecordId, ''Lab HL7'' description,''hl7'' documentType, 0 categoryId, 0 classId,'''' hrmFacilityId, '''' hrmReportNumber, 1 isActive, 0 AS markSeen, cast(NULL AS DATETIME) AS markSeenDateTime, cast(null as nvarchar(100)) as comment'
			SET @tableSql = ''
			SET @whereSql = N' where 1=2 '
		end

		IF (@sql <> '')
			SET @sql = @sql + N' UNION ALL '
		SET @sql = @sql + @selectSql + @tableSql + @whereSql
	END

	-- ==============================================================
	-- FHIR Documents (run while you still can!)
	-- ==============================================================
	IF (@filterIncludeScan = 1 AND @filterClassId = 0 AND @filterCategoryId = 0 AND @filterComment = '')
	BEGIN 
		SET @selectSql =  N' select fdoc.Id, 0 abnormal, CONVERT(datetime, fdoc.CreatedAt AT TIME ZONE ''Eastern Standard Time'') receivedDateTime, CONVERT(datetime, fdoc.CreatedAt AT TIME ZONE ''Eastern Standard Time'') testDateTime '
		IF (@filterPatient <> '') 
			SET @selectSql = @selectSql + N',patient.lastName patientLastName,patient.firstName patientFirstName, patient.id as demographicId'
		SET @selectSql = @selectSql + N', patient.patientRecordId '
		SET @selectSql = @selectSql + N', SUBSTRING(fdoc.Description, 1, 1024)  description, ''fhir'' documentType, 0 categoryId, 0 classId, '''' hrmFacilityId, '''' hrmReportNumber, 1 isActive, 0 markSeen, cast(NULL AS DATETIME) AS markSeenDateTime, '''' comment '
		SET @tableSql = N' from Documents fdoc'
		SET @tableSql = @tableSql + N' join Demographics patient on fdoc.PatientRecordId=patient.PatientRecordId'
		SET @tableSql = @tableSql + N' join PatientRecords pRecord on patient.PatientRecordId=pRecord.id'
		SET @whereSql = N' where pRecord.PracticeId=@practiceId'
		SET @whereSql = N' and fdoc.DeletedAt IS NULL'

		IF (@patientRecordId <> 0)
		BEGIN
			SET @whereSql = @whereSql + N' AND patient.PatientRecordId = @patientRecordId'
		END

		IF (@filterTestDateStart <> '')
			SET @filterTestDateStartUTC = CONVERT(DATETIME, @filterTestDateStart, 120) AT TIME ZONE 'Eastern Standard Time' AT TIME ZONE 'UTC';
		IF (@filterTestDateEnd <> '')
			SET @filterTestDateEndUTC = CONVERT(DATETIME, @filterTestDateEnd, 120) AT TIME ZONE 'Eastern Standard Time' AT TIME ZONE 'UTC';
		IF (@filterReceivedDateStart <> '')
			SET @filterReceivedDateStartUTC = CONVERT(DATETIME, @filterReceivedDateStart, 120) AT TIME ZONE 'Eastern Standard Time' AT TIME ZONE 'UTC';
		IF (@filterReceivedDateEnd <> '')
			SET @filterReceivedDateEndUTC = CONVERT(DATETIME, @filterReceivedDateEnd, 120) AT TIME ZONE 'Eastern Standard Time' AT TIME ZONE 'UTC';
		IF (@filterMarkseenStartDt <> '')
			SET @filterMarkseenStartUTC = CONVERT(DATETIME, @filterMarkseenStartDt, 120) AT TIME ZONE 'Eastern Standard Time' AT TIME ZONE 'UTC';
		IF (@filterMarkseenEndDt <> '')
			SET @filterMarkseenEndUTC = CONVERT(DATETIME, @filterMarkseenEndDt, 120) AT TIME ZONE 'Eastern Standard Time' AT TIME ZONE 'UTC';

		IF (@filterTestDate <> '')
		BEGIN
			SET @whereSql = @whereSql + N' AND (fdoc.CreatedAt BETWEEN @filterTestDateStartUTC AND @filterTestDateEndUTC)'
		END
		IF (@filterReceivedDate <> '')
		BEGIN 
			SET @whereSql = @whereSql + N' AND (fdoc.CreatedAt BETWEEN @filterReceivedDateStartUTC AND @filterReceivedDateEndUTC)'
		END 
		IF (@filterMarkseenStart <> '') 
		BEGIN 
			SET @whereSql = @whereSql + N' AND (fdoc.CreatedAt > @filterMarkseenStartUTC)'
		END 
		IF (@filterMarkseenEnd <> '') 
		BEGIN 
			SET @whereSql = @whereSql + N' AND (fdoc.CreatedAt < @filterMarkseenEndUTC)'
		END 
		IF (@filterDescription <> '')
		BEGIN 
			SET @whereSql = @whereSql + N' and SUBSTRING(fdoc.Description, 1, 255) like CONCAT(''%'', @filterDescription, ''%'')'
		END

		IF (@sql <> '')
			SET @sql = @sql + N' UNION ALL '
		SET @sql = @sql + @selectSql + @tableSql + @whereSql
	END
	-- ==============================================================
	-- FHIR Documents END
	-- ==============================================================

	SET @sql = N'(' + @sql + N') z'

	IF (@filterPatient = '' or @filterPatient is null) -- When there is NO filter by patient's name, join Demographic table in the pagination query
		SET @sql = @sql + ' inner join Demographics d on (d.PatientRecordId = z.PatientRecordId) '

	SET @whereSql = ''
    
	--filter by mark seen
	IF (@filterIncludeMarkseen = 0)
		SET @whereSql = @whereSql +' and (markSeen is null or markSeen=0)'
	ELSE
	BEGIN
		IF (@filterMarkseenStart <> '')
			SET @whereSql = @whereSql + ' and ((markSeen is null or markSeen=0) or markSeenDateTime >= @filterMarkseenStartDt)'
		IF (@filterMarkseenEnd <> '')
			SET @whereSql = @whereSql + ' and ((markSeen is null or markSeen=0) or markSeenDateTime <= @filterMarkseenEndDt)'
	END

	IF ( @whereSql <> '')
	BEGIN
		SET @whereSql = STUFF(@whereSql, CHARINDEX(' and', @whereSql), LEN(' and'), ' where')
		SET @sql = @sql + @whereSql
	END

	SET @params = N'@PracticeID int, @practiceDoctorId int, @patientRecordId int, @filterClassId int, @filterCategoryId int, @filterMarkseenStartDt datetime, @filterMarkseenEndDt datetime, @filterReceivedDateStart datetime, @filterReceivedDateEnd datetime, @filterTestDateStart datetime, @filterTestDateEnd datetime, @filterPatient varchar(256), @rowStart int, @rowCount int, @reporttype nvarchar(10), @filterDescription nvarchar(256), @filterTestDateStartUTC datetime, @filterTestDateEndUTC datetime, @filterReceivedDateStartUTC datetime, @filterReceivedDateEndUTC datetime, @filterMarkseenStartUTC datetime, @filterMarkseenEndUTC datetime'
	
	SET @orderByColumn = (CASE WHEN @sortByColumn='receiveddate' THEN N'receivedDateTime'
								WHEN @sortByColumn='abnormal' THEN N'abnormal'
								WHEN @sortByColumn='patient' THEN N'patientLastName'+(CASE WHEN @sortByOrder='asc' THEN N' asc' ELSE N' desc' END)+N', patientFirstName'
								WHEN @sortByColumn='description' THEN N'description'
								WHEN @sortByColumn='comment' THEN N'comment'
								ELSE N'testDateTime' END)

	SET @orderByCount = N' order by ' + 
	      CASE WHEN @sortByColumn='patient' THEN 
		       CASE WHEN (@filterPatient <> '') THEN N'patientLastName' ELSE N'd.lastName' END+(CASE WHEN @sortByOrder='asc' THEN N' desc' ELSE N' asc' END)+', '+
			   CASE WHEN (@filterPatient <> '') THEN N'patientFirstName' ELSE N'd.firstName' END ELSE @orderByColumn 
		  END + (CASE WHEN @sortByOrder='asc' THEN N' desc' ELSE N' asc' END)+', z.id ' + (CASE WHEN @sortByOrder='asc' THEN N' desc' ELSE N' asc' END)

	IF (@filterPatient <> '') -- When there is a filter by patient's name, join table variable to filter out records. Otherwise, join Demographic table in the pagination query
		SET @sql = N'declare @reporttypetab table ( reporttype int not null ) insert into @reporttypetab(reporttype) select value from STRING_SPLIT(@reporttype, '','') rt; ;with rs as (select z.id,abnormal,markSeen,receivedDateTime,testDateTime,patientFirstName,patientLastName,demographicId,patientRecordId,description,comment,documentType,hrmFacilityId,hrmReportNumber,isActive, row_number() over ( ' + @orderByCount + ') as SeqRecs from ' + @sql + ')'
	ELSE
		SET @sql = N'declare @reporttypetab table ( reporttype int not null ) insert into @reporttypetab(reporttype) select value from STRING_SPLIT(@reporttype, '','') rt; ;with rs as (select z.id,abnormal,markSeen,receivedDateTime,testDateTime,d.firstName as patientFirstName,d.lastName as patientLastName,d.id as demographicId,d.patientRecordId,description,comment,documentType,hrmFacilityId,hrmReportNumber,isActive, row_number() over ( ' + @orderByCount + ') as SeqRecs from ' + @sql + ')'

    SET @orderBySql = N' order by ' + @orderByColumn + (CASE WHEN @sortByOrder='asc' THEN N' asc' ELSE N' desc' END) + ', rs.id ' + (CASE WHEN @sortByOrder='asc' THEN N' asc' ELSE N' desc' END)
	SET @sql = @sql + N'select rs.id, case when documentType = ''hl7'' then cast((case when ( select top 1 abnormalFlag from HL7ReportVersion bg, HL7Result bh where bg.Id = bh.HL7ReportVersionId and   ltrim(rtrim(bh.abnormalFlag)) NOT IN (''N'',''No'',''Normal'') and   HL7ReportId = rs.id ) is null then 0 else 1 end ) as bit) else abnormal end abnormal,markSeen,receivedDateTime,testDateTime,patientFirstName,patientLastName,demographicId,patientRecordId,description,comment,documentType,hrmFacilityId,hrmReportNumber,isActive, SeqRecs as rowTotal, row_number() over( '+ @orderBySql +') as rn from rs '

	SET @offsetFetchSql = N' offset @rowStart rows fetch next @rowCount rows only option (optimize for unknown)'

	SET @sql = @sql + @orderBySql + @offsetFetchSql

--	select @sql

	insert into #tempDocumentList(id,abnormal,markSeen,receivedDateTime,testDateTime,patientFirstName,patientLastName,demographicId,patientRecordId,description,comment,documentType,hrmFacilityId,hrmReportNumber,isActive,totalRow, rn)
		EXEC sp_executesql @sql, @params, @PracticeID = @PracticeID, @practiceDoctorId = @practiceDoctorId, @patientRecordId = @patientRecordId, @filterClassId = @filterClassId, 
		                    @filterCategoryId = @filterCategoryId, @filterMarkseenStartDt = @filterMarkseenStartDt, @filterMarkseenEndDt = @filterMarkseenEndDt, 
							@filterReceivedDateStart = @filterReceivedDateStart, @filterReceivedDateEnd = @filterReceivedDateEnd, @filterTestDateStart = @filterTestDateStart,
							@filterTestDateEnd = @filterTestDateEnd, @filterPatient = @filterPatient, @rowStart = @rowStart, @rowCount = @rowCount, @reporttype = @reporttype,
							@filterDescription = @filterDescription, @filterTestDateStartUTC = @filterTestDateStartUTC, @filterTestDateEndUTC = @filterTestDateEndUTC, 
							@filterReceivedDateStartUTC = @filterReceivedDateStartUTC, @filterReceivedDateEndUTC = @filterReceivedDateEndUTC, 
							@filterMarkseenStartUTC = @filterMarkseenStartUTC, @filterMarkseenEndUTC = @filterMarkseenEndUTC;

	update #tempDocumentList 
		set lastAppointmentDate=a.appointmentTime 
		from (select max(appointmentTime) appointmentTime, PatientRecordId 
		        from (select appointmentTime,PatientRecordId 
				        from appointments 
		                where appointmentTime<getdate() 
				        and   PatientRecordId in (select patientRecordId from #tempDocumentList)) f 
		    group by PatientRecordId) a 
			inner join #tempDocumentList b on a.PatientRecordId=b.patientRecordId

	update #tempDocumentList set arrivedAfterLastAppointment=(case when DATEDIFF(day,lastAppointmentDate,receivedDateTime)>0 then 1 else 0 end)

	SET @totalRow = -1
	IF (@totalRowRequest = 1)
	BEGIN
	    -- Must run only for page 1
		SELECT TOP 1 @totalRow = totalRow FROM #tempDocumentList WHERE rn = 1;
	END;

	select id,arrivedAfterLastAppointment,abnormal,markSeen,receivedDateTime,testDateTime,patientFirstName,patientLastName,demographicId,patientRecordId,description,comment,documentType,hrmFacilityId,hrmReportNumber,isActive from #tempDocumentList order by rn;

	IF OBJECT_ID('tempdb..##SearchedPatient') IS NOT NULL
			DROP TABLE #SearchedPatient

	DROP TABLE #tempDocumentList

	return @totalRow
END
