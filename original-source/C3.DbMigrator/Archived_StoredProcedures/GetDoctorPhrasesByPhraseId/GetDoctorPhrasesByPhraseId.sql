﻿CREATE PROCEDURE [dbo].[GetD<PERSON><PERSON><PERSON><PERSON>sesByPhraseId]
	@groupId INT,
	@externalDoctorId INT,
	@practiceId INT,
	@practiceTemplateId INT,
	@phraseId INT -- 	RootCategoryPhraseId
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @tblPhrases table
	(		
		DoctorPhraseId int not null,
		PracRootCategoryTempId int NOT NULL,
		RootCategoryPhraseId int NOT NULL,
		PhraseName nvarchar(500) null,
		PhraseValue nvarchar(max),
		IsVisible bit not null,
		DisplayOrder int not null,
		ParentId int NULL,	
		RootCategoryId int not null,
		GroupId int not null,
		IsSubCategory bit not null,		
		IsActive bit not null		
	);
	

	WITH CustomPhraseCTE
	(
		DoctorPhraseId,
		PracRootCategoryTempId,
		RootCategoryPhraseId,
		PhraseName,
		PhraseVal<PERSON>,
		<PERSON>V<PERSON><PERSON>,
		<PERSON><PERSON>lay<PERSON><PERSON><PERSON>,
		<PERSON>rentId,	
		<PERSON><PERSON>ategoryId,
		GroupId,
		IsSubCategory,		
		IsActive	
		,[Level]					
	)
	AS(
		SELECT 
		 drp.Id
		,drp.PracRootCategoryTempId
		,rcp.Id  
		,CAST((CASE WHEN drp.PhraseName IS NULL THEN rcp.PhraseName ELSE drp.PhraseName END) AS nvarchar(500)) AS PhraseName
		,CAST((CASE WHEN drp.PhraseValue IS NULL THEN rcp.PhraseValue ELSE drp.PhraseValue END) AS nvarchar(Max)) AS PhraseValue
		,CAST((CASE WHEN drp.IsVisible IS NULL THEN 1 ELSE drp.IsVisible END) AS BIT) AS IsVisible	
		,ISNULL(drp.DisplayOrder,1000000)
		,rcp.ParentId	
		,rc.Id
		,rc.GroupId
		,rcp.IsSubCategory
		,CAST((CASE WHEN drp.IsActive IS NULL THEN 1 ELSE drp.IsActive END) AS BIT)
		,0
		FROM RootCategoryPhrases rcp 
		JOIN RootCategories rc ON rcp.RootCategoryId = rc.Id
		JOIN DoctorRootCategoryPhrases drp ON drp.RootCategoryPhraseId = rcp.Id AND drp.PracRootCategoryTempId = @practiceTemplateId 
		AND drp.ExternalDoctorId = @externalDoctorId
		WHERE rc.GroupId = @groupId AND rcp.Id = @phraseId
		UNION ALL
		--recursive select
		SELECT 
		 drp.Id		
		,cte.PracRootCategoryTempId
		,drp.RootCategoryPhraseId				
		,CAST((CASE WHEN drp.PhraseName IS NULL THEN rcp.PhraseName ELSE drp.PhraseName END) AS nvarchar(500)) AS PhraseName
		,CAST((CASE WHEN drp.PhraseValue IS NULL THEN rcp.PhraseValue ELSE drp.PhraseValue END) AS nvarchar(Max)) AS PhraseValue
		,CAST((CASE WHEN drp.IsVisible IS NULL THEN 1 ELSE drp.IsVisible END) AS BIT) AS IsVisible	
		,ISNULL(drp.DisplayOrder,1000000)
		,rcp.ParentId	
		,rc.Id
		,rc.GroupId
		,rcp.IsSubCategory
		,CAST((CASE WHEN drp.IsActive IS NULL THEN 1 ELSE drp.IsActive END) AS BIT)		
		,cte.[level] + 1						
		FROM RootCategoryPhrases rcp 
		JOIN CustomPhraseCTE cte ON rcp.ParentId = cte.RootCategoryPhraseId		
		JOIN RootCategories rc ON rcp.RootCategoryId = rc.Id
		JOIN DoctorRootCategoryPhrases drp ON drp.RootCategoryPhraseId = rcp.Id AND drp.PracRootCategoryTempId = @practiceTemplateId 
		AND drp.ExternalDoctorId = @externalDoctorId
		WHERE rc.GroupId = @groupId 
	)
	SELECT 
	 cte.DoctorPhraseId
	,cte.RootCategoryPhraseId
	,cte.PracRootCategoryTempId	
	,cte.RootCategoryId		
	,cte.GroupId	
	,cte.IsVisible
	,cte.PhraseName
	,cte.PhraseValue
	,ISNULL(cte.ParentId,0) AS ParentId		
	,cte.IsSubCategory
	,cte.[Level]					
	FROM CustomPhraseCTE cte 	
		
END