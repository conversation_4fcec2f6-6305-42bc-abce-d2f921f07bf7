﻿CREATE PROCEDURE [dbo].[GetDashboardPHCCAN001] (@PracticeDoctorId INT = NULL)
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @TimeCheck DATETIME = GETDATE(),@ObjectName VARCHAR(30) = OBJECT_NAME(@@PROCID) -- Logs

	DECLARE @BasePopulation AS TABLE (PatientRecordId INT)
	DECLARE @MammogramExclusion AS TABLE (PatientRecordId INT)
	DECLARE @MammogramScreening AS TABLE (PatientRecordId INT)
	DECLARE @Segments AS TABLE (SegmentId INT,PatientRecordId INT)

	-- Loading Tables
	--- Base Population
	INSERT INTO @BasePopulation
	SELECT D.PatientRecordId	
	FROM Demographics D
	JOIN DemographicsMainResponsiblePhysicians MRP on D.Id = MRP.DemographicId and MRP.IsActive = 1
	WHERE 
	D.active = 0														-- Pastient is active	
	AND (DATEDIFF(DD,D.dateOfBirth,GETDATE()) / 365.5) BETWEEN 50 AND 74	-- Patient is between 50 and 74
	AND D.gender = 1													-- Patient is Female
	AND (MRP.PracticeDoctorId = @PracticeDoctorId OR @PracticeDoctorId IS NULL)
	GROUP BY D.PatientRecordId

	-- Mammogram Exclusion 
	INSERT INTO @MammogramExclusion
	SELECT PatientRecordId 	
	FROM BillDetails B					-- Exclusion code, any date
	WHERE B.serviceCode = 'Q141A'
	UNION ALL 
	SELECT PatientRecordId 
	FROM VP_CPP_Problem_List CPP
	WHERE 			   
	(CPP.DiagnosticCode IN ('V45.71','174')						-- Excluded Diagnosis codes
		OR CPP.Problem_Description LIKE '%mastectomy%'				-- Excluded Text Snippets
		OR CPP.Problem_Description LIKE '%cancer breast%'
		OR CPP.Problem_Description LIKE '%breast ca%'
		OR CPP.Problem_Description LIKE '%ca breast%')
		AND Deleted = 0 and cpp.UpdateDate is null

	--- Mammogram Screening in the last 24 months inclusive  
	INSERT INTO @MammogramScreening
	SELECT IM.PatientRecordId	
	FROM [VP_CPP_Immunization] IM
	WHERE IM.VP_CPP_ImmunizationStatusId=3						-- Status = Complete
	AND IM.VP_CPP_ImmunizationTypeId=3							-- Type = Mamography
	AND DATEDIFF(MONTH,DATEFROMPARTS(IM.ImmunizationYear, IM.ImmunizationMonth, IM.ImmunizationDay),GETDATE()) <= 24
	
	--- Segments	
	INSERT INTO @Segments
	SELECT 1,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)
	AND PatientRecordId IN (SELECT PatientRecordId FROM @MammogramScreening)
	
	INSERT INTO @Segments
	SELECT 2,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramExclusion)
	AND PatientRecordId NOT IN (SELECT PatientRecordId FROM @MammogramScreening)

	INSERT INTO @Segments
	SELECT 3,PatientRecordId
	FROM @BasePopulation
	WHERE PatientRecordId IN (SELECT PatientRecordId FROM @MammogramExclusion)	

	--- Final Select
	SELECT SegmentId,PatientRecordId FROM @Segments

	PRINT (@ObjectName+' PracticeDoctorId='+ISNULL(LTRIM(STR(@PracticeDoctorId)),'NULL')+' Completed in '+CONVERT(VARCHAR(100),DATEDIFF(s, @TimeCheck, GETDATE())) + ' seconds' ) -- Output Log

	END
