﻿using AwareMD.DHDR.Seedwork;
using Hl7.Fhir.Model;
using Hl7.Fhir.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Caching;

namespace AwareMD.DHDR.FHIR
{
    public static class Parser
    {
        private const string CacheKey = "ListOfOhCmsIdentifiers";

        /// <summary>
        /// For requirement DHDR04.01.
        /// Deserialize a list of <see cref="Hl7.Fhir.Model.MedicationDispense"/> from DHDR search results.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="json"></param>
        /// <returns></returns>
        public static S ParseDrugDispenseSummary<S, D, P>(string json)
            where P : IPatient, new()
            where D : IDrugDispense<P>, new()
            where S : IDrugDispenseSummary<D, P>, new()
        {
            S result = new S { DrugDispenseList = new List<D>() };

            FhirJsonParser parser = new FhirJsonParser(new ParserSettings());
            string resourceTypeBundle = AddQuotes("resourceType") + ": " + AddQuotes("Bundle");
            string resourceTypeOperationOutcome = AddQuotes("resourceType") + ": " + AddQuotes("OperationOutcome");

            if (json.Contains(resourceTypeBundle))
            {
                Bundle bundle = parser.Parse<Bundle>(json);
                result.DrugDispenseTotal = Convert.ToInt32(bundle.Total);

                foreach (Bundle.EntryComponent entry in bundle?.Entry)
                {
                    Resource resource = entry?.Resource;
                    switch (resource.TypeName)
                    {
                        case "MedicationDispense":
                            result.DrugDispenseList.Add(ParseMedicationDispense<D, P>((MedicationDispense)resource));
                            break;
                        case "OperationOutcome":
                            OperationOutcome operationOutcome = (OperationOutcome)(resource);
                            List<OperationOutcome.IssueComponent> issues = operationOutcome.Issue;
                            if (issues?.Count > 0)
                            {
                                foreach (var item in issues)
                                {
                                    EmrIssue emrIssue = new EmrIssue();

                                    emrIssue.severity = item.Severity.ToString();
                                    emrIssue.code = item.Code.Value.ToString();
                                    emrIssue.details.text = item.Details.Text;

                                    result.OperationOutcomeIssues.Add(emrIssue);
                                }
                            }

                            break;
                        default:
                            // @TODO: other resources, not in used for now in Summary View
                            break;
                    }
                    if (result.Patient == null && resource.TypeName == "MedicationDispense")
                    {
                        result.Patient = ParsePatient<P>((MedicationDispense)resource);
                    }
                }
            }
            else if (json.Contains(resourceTypeOperationOutcome))
            {
                result.ErrorOperationOutcome = Newtonsoft.Json.JsonConvert.DeserializeObject<ErrorOperationOutcome>(json);
            }
            return result;
        }

        static P ParsePatient<P>(MedicationDispense md)
            where P : IPatient, new()
        {
            Patient pat = (Patient)md.Contained.Where(x => x.TypeName == "Patient").FirstOrDefault();

            P result = new P
            {
                Gender = (int?)((AdministrativeGender?)pat?.Gender),
                BirthDate = pat?.BirthDate,
                HealthCardNumber = pat?.Identifier[0]?.ValueElement?.ToString(),
                Name = pat?.Name.Count > 0 ? pat.Name[0]?.ToString() : null
            };

            return result;
        }

        private static List<OhCmsIdentifier> GetOhCmsIdentifiers()
        {
            ObjectCache cache = MemoryCache.Default;

            if (cache.Contains(CacheKey))
            {
                return (List<OhCmsIdentifier>)cache.Get(CacheKey);
            }
            else
            {
                List<OhCmsIdentifier> list = AwareMD.DHDR.Seedwork.Common.SetOhCmsIdentifiers();
                CacheItemPolicy policy = new CacheItemPolicy()
                {
                    AbsoluteExpiration = DateTime.Now.AddHours(8)
                };
                cache.Add(CacheKey, list, policy);

                return list;
            }
        }


        static D ParseMedicationDispense<D, P>(MedicationDispense md)
        where P : IPatient, new()
        where D : IDrugDispense<P>, new()
        {
            if (md == null) throw new ArgumentNullException("AwareMD.DHDR.FHIR.Parser.ParseMedicationDispense parameter cannot be null.");

            string refillsRemaining = string.Empty;
            string quantityRemaining = string.Empty;
            string amoutOfMedicationPerDose = string.Empty;
            string frequency = string.Empty;
            string din = string.Empty;
            string therapeuticClass = string.Empty;
            string therapeuticSubClass = string.Empty;
            string rxNumber = string.Empty;
            string pharmacistLicense = string.Empty;
            string pharmacistSystem = string.Empty;
            string pharmacistDescription = string.Empty;
            string pharmacyId = string.Empty;
            string pharmacistName = string.Empty;
            string medicationCondition = string.Empty;
            string prescriberLicense = string.Empty;
            string prescriberSystem = string.Empty;
            string prescriberDescription = string.Empty;

            List<CategoryCoding> categoryCoding = new List<CategoryCoding>();
            if (md?.Category?.Coding != null)
            {
                foreach (var item in md?.Category?.Coding)
                {
                    CategoryCoding c = new CategoryCoding()
                    {
                        Code = item.Code,
                        Display = item.Display,
                        System = item.System
                    };
                    categoryCoding.Add(c);
                }
            }

            MedicationRequest mr = (MedicationRequest)md.Contained.Where(x => x.TypeName == "MedicationRequest").FirstOrDefault();
            CodeableConcept cc = mr?.ReasonCode?.Count > 0 ? mr.ReasonCode[0] : null;
            medicationCondition = cc?.Coding?.Count > 0 ? cc.Coding[0].Display : "";//cc?.Text; // check when have real data

            if (md.Extension?.Count > 0)
            {
                // @TODO: this is for Refills remaining and Quantity remaining, but still couldn't find any PST data, will do later

                try
                {
                    if (md.Extension.Count > 1 && md.Extension[1] != null)
                    {
                        dynamic jsonDosageInstruction1 = md.Extension[0];
                        dynamic jsonDosageInstruction2 = md.Extension[1];
                        if (jsonDosageInstruction1.Value != null && jsonDosageInstruction2.Value != null)
                        {
                            refillsRemaining = Convert.ToString(jsonDosageInstruction1.Value);
                            quantityRemaining = Convert.ToString(jsonDosageInstruction2.Value.Value) + " " + Convert.ToString(jsonDosageInstruction2.Value.Unit);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var err = ex.Message;
                }
                try
                {
                    if (md.Extension.Count > 2 && md.Extension[2] != null)
                    {
                        quantityRemaining = md.Extension[2].Extension[0].Value.ToString();
                    }
                }
                catch (Exception)
                {

                }
            }

            Medication med = (Medication)md.Contained.Where(x => x.TypeName == "Medication").FirstOrDefault();
            if (md.DosageInstruction.Count > 0)
            {
                //var Frequency = md.DosageInstruction[0].DoseAndRate[0].Dose.TypeName == "Dose" ; // @TODO
                try
                {
                    dynamic jsonDosageInstruction = md.DosageInstruction.FirstOrDefault()?.DoseAndRate.FirstOrDefault()?.Dose;
                    if (jsonDosageInstruction?.TypeName == "Quantity")
                    {
                        amoutOfMedicationPerDose = Convert.ToString(jsonDosageInstruction.Value) + " " + Convert.ToString(jsonDosageInstruction.Unit);
                    }
                    if (string.IsNullOrEmpty(amoutOfMedicationPerDose))
                    {
                        amoutOfMedicationPerDose = md.DosageInstruction[0].Text;
                    }
                }
                catch (Exception ex)
                {
                    var err = ex.Message;
                }
                try
                {
                    Timing Frequency = md.DosageInstruction?.Count > 0 ? md.DosageInstruction[0].Timing : null;
                    StringBuilder sb = new StringBuilder();
                    if (Frequency.Repeat.Frequency.HasValue)
                    {
                        string time = Frequency.Repeat.Frequency > 1 ? "times" : "time";

                        sb.Append(Frequency.Repeat.Frequency + "\u00a0" + time + "\u00a0/\u00a0");
                        sb.Append(Frequency.Repeat.Period);
                        if (Frequency.Repeat.PeriodMax != null)
                        {
                            sb.Append("-" + Frequency.Repeat.PeriodMax);
                        }
                        sb.Append("\u00a0");
                        string periodUnit = Convert.ToString(Frequency.Repeat.PeriodUnit).ToLower();
                        sb.Append(GetPeriodUnit(periodUnit));
                    }
                    frequency = sb.ToString();
                }
                catch (Exception ex)
                {
                    var err = ex.Message;
                }
            }

            if (med.Code?.Coding?.Count > 0)
            {
                Coding c = med.Code.Coding.Where(x => x.System == "http://hl7.org/fhir/NamingSystem/ca-hc-din").FirstOrDefault();
                if (c != null)
                {
                    din = c.Code;
                }
                if (med.Code.Coding.Count > 3)
                {
                    therapeuticClass = med.Code.Coding[2].Display;
                    therapeuticSubClass = med.Code.Coding[3].Display;
                }
            }
            if (md.Identifier?.Count > 0)
            {
                Identifier identifier = md.Identifier.Where(i => i.System.Contains("ca-on-pharmacy-") && i.System.Contains("-rx-number")).FirstOrDefault();
                rxNumber = identifier?.Value;
            }
            Extension MedicationStrength = med?.GetExtension("http://ehealthontario.ca/fhir/StructureDefinition/ca-on-medications-ext-medication-strength");

            #region prescriber
            Practitioner practitPhysician = (Practitioner)md.Contained.Where(x => x.TypeName == "Practitioner" && x.Id.Contains("PractPrescr")).FirstOrDefault();
            HumanName PrescriberName = practitPhysician?.Name?.Count > 0 ? practitPhysician.Name[0] : null;

            Identifier pri = practitPhysician?.Identifier?.Count > 0 ? practitPhysician?.Identifier[0] : null;

            if (pri != null)
            {
                prescriberLicense = pri.Value;
                prescriberSystem = pri.System;
                prescriberDescription = Common.GetOhCmsIdentifierByUri(pri.System, GetOhCmsIdentifiers())?.Description;
            }
            #endregion

            #region pharmacist
            Practitioner practitPharmacist = (Practitioner)md.Contained.Where(x => x.TypeName == "Practitioner" && x.Id.Contains("PractDisp")).FirstOrDefault();
            HumanName PharmacistName = practitPharmacist?.Name?.Count > 0 ? practitPharmacist.Name[0] : null;

            Identifier pi = practitPharmacist?.Identifier?.Count > 0 ? practitPharmacist?.Identifier[0] : null;
            if (pi != null)
            {
                pharmacistLicense = pi.Value;
                pharmacistSystem = pi.System;
                pharmacistDescription = Common.GetOhCmsIdentifierByUri(pi.System, GetOhCmsIdentifiers())?.Description;
            }

            if (practitPharmacist?.Name.Count > 0)
            {
                string pharmacistFirstName = PharmacistName?.GivenElement?.Count > 0 ? PharmacistName?.GivenElement[0].Value : null;
                string pharmacistLastName = PharmacistName?.Family;
                if (!string.IsNullOrEmpty(pharmacistFirstName))
                {
                    pharmacistName = pharmacistFirstName + ", " + pharmacistLastName;
                }
                else
                {
                    pharmacistName = pharmacistLastName;
                }
            }
            #endregion
            Organization org = (Organization)md.Contained.Where(x => x.TypeName == "Organization").FirstOrDefault();
            if (org?.Identifier?.Count > 0)
            {
                Identifier orgIdentifier = org.Identifier.Where(o => o.System == "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-pharmacy-id-ocp").FirstOrDefault();
                pharmacyId = orgIdentifier?.Value;
            }


            D result = new D
            {
                DispensedDate = md.WhenPrepared,
                PickupDate = md.WhenHandedOver,
                GenericNameOfTheDispensedDrug = med?.Code.Coding?.Count > 1 ? med.Code.Coding[1].Display : null,
                BrandNameOfTheDispensedDrug = med?.Code.Coding?.Count > 0 ? med.Code.Coding[0].Display : null,
                AmountOfMedicationperDose = amoutOfMedicationPerDose,
                Frequency = frequency,
                DispensedDrugStrength = MedicationStrength?.Value?.ToString(),
                DrugDosageForm = med.Form?.Text,
                DispensedQuantityValue = md.Quantity?.Value,
                DispensedQuantityUnit = md.Quantity?.Unit,
                EstimatedDaysSupply = md.DaysSupply?.Value,
                RefillsRemaining = refillsRemaining,
                QuantityRemaining = quantityRemaining,
                PrescriberFirstName = PrescriberName?.GivenElement?.Count > 0 ? PrescriberName?.GivenElement[0].Value : null,
                PrescriberLastName = PrescriberName?.Family,
                PrescriberPhoneNumber = practitPhysician?.Telecom?.Count > 0 ? practitPhysician.Telecom[0].Value : null,
                DispensingPharmacy = org?.Name,
                DispensingPharmacyFaxNumber = org != null ? (org.Telecom?.Count > 1 ? org.Telecom[1].Value : null) : null,
                DispensingPharmacyPhoneNumber = org != null ? (org.Telecom?.Count > 0 ? org.Telecom[0].Value : null) : null,
                CategoryCoding = categoryCoding,
                DIN = din,
                TherapeuticClass = therapeuticClass,
                TherapeuticSubClass = therapeuticSubClass,
                RxNumber = rxNumber,
                PharmacistLicense = pharmacistLicense,
                PharmacistSystem = pharmacistSystem,
                PharmacistDescription = pharmacistDescription,
                PharmacyId = pharmacyId,
                PharmacistName = pharmacistName,
                MedicationCondition = medicationCondition,
                PrescriberLicense = prescriberLicense,
                PrescriberSystem = prescriberSystem,
                PrescriberDescription = prescriberDescription,
                Patient = ParsePatient<P>(md)
            };

            return result;
        }

        static string AddQuotes(string data)
        {
            return (char)34 + data + (char)34;
        }
        static string GetPeriodUnit(string periodUnit)
        {
            switch (periodUnit)
            {
                case "s":
                    return "second";
                case "min":
                    return "minute";
                case "h":
                    return "hour";
                case "d":
                    return "day";
                case "wk":
                    return "week";
                case "mo":
                    return "month";
                case "a":
                    return "year";
                default:
                    return "";
            }
        }
    }
}
