﻿using System.Configuration;

namespace Cerebrum.DHDR.Services.Models
{
    public class DHDRDefaultDateLowerLimitConfig : ConfigurationElement
    {
        [ConfigurationProperty("Enabled", IsRequired = false, DefaultValue = false)]
        public bool Enabled
        {
            get
            {
                return System.Convert.ToBoolean(this["Enabled"] ?? false);
            }
        }

        [ConfigurationProperty("Value", IsRequired = false, DefaultValue = null)]
        public int Value
        {
            get
            {
                return System.Convert.ToInt32(this["Value"] ?? 120);
            }
        }
    }
}
