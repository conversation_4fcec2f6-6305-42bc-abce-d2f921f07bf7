﻿using AwareMD.DHDR.Seedwork;
using System.Collections.Generic;

namespace Cerebrum.DHDR.Services.Models
{
    public class DrugDispenseSummary: IDrugDispenseSummary<DrugDispense<Patient>, Patient>
    {
        public DrugDispenseSummary()
        {
            OtherResponse = new DHDRResponse();
            OperationOutcomeIssues = new List<EmrIssue>();
        }
        public ICollection<DrugDispense<Patient>> DrugDispenseList { get; set; }
        public ICollection<DrugDispense<Patient>> GroupedDrugDispenseList { get; set; }
        public ICollection<DrugDispense<Patient>> GroupedPharmacyServicesList { get; set; }
        public DHDRResponse OtherResponse { get; set; }
        public Patient Patient { get; set; }
        public int DrugDispenseTotal { get; set; }
        public List<EmrIssue> OperationOutcomeIssues { get; set; }
        public ErrorOperationOutcome ErrorOperationOutcome { get; set; }
    }
}
