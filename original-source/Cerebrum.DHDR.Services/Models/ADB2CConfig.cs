﻿using System.Configuration;

namespace Cerebrum.DHDR.Services.Models
{
    public class ADB2CConfig : ConfigurationElement
    {
        [ConfigurationProperty("ClientId", IsRequired = true)]
        public string ClientId
        {
            get
            {
                return (string)this["ClientId"];
            }
        }

        [ConfigurationProperty("ClientSecret", IsRequired = true)]
        public string ClientSecret
        {
            get
            {
                return (string)this["ClientSecret"];
            }
        }

        [ConfigurationProperty("Authority", IsRequired = true)]
        public string Authority
        {
            get
            {
                return (string)this["Authority"];
            }
        }

        [ConfigurationProperty("Scope", IsRequired = true)]
        public string Scope
        {
            get
            {
                return (string)this["Scope"];
            }
        }
    }
}
