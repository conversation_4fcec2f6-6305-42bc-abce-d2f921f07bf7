﻿using AwareMD.DHDR.Seedwork;
using Cerebrum.ViewModels.Patient;
using System;
using System.Collections.Generic;

namespace Cerebrum.DHDR.Services.Models
{
    [Serializable]
    public class DrugDispense<P> : IDrugDispense<P>
        where P : IPatient, new()
    {
        public string DispensedDate { get; set; }
        public string PickupDate { get; set; }
        public string GenericNameOfTheDispensedDrug { get; set; }
        public string BrandNameOfTheDispensedDrug { get; set; }
        public string AmountOfMedicationperDose { get; set; }
        public string Frequency { get; set; }
        public string DispensedDrugStrength { get; set; }
        public string DrugDosageForm { get; set; }
        public decimal? DispensedQuantityValue { get; set; }
        public string DispensedQuantityUnit { get; set; }
        public decimal? EstimatedDaysSupply { get; set; }
        public string RefillsRemaining { get; set; }
        public string QuantityRemaining { get; set; }
        public string PrescriberFirstName { get; set; }
        public string PrescriberLastName { get; set; }
        public string PrescriberPhoneNumber { get; set; }
        public string DispensingPharmacy { get; set; }
        public string DispensingPharmacyFaxNumber { get; set; }
        public string DispensingPharmacyPhoneNumber { get; set; }
        public string Gender { get; set; }
        public string BirthDate { get; set; }
        public string HealthCardNumber { get; set; }
        public string Name { get; set; }
        public int RxCount { get; set; } = 1;
        public bool Show { get; set; } = true;
        public int Group { get; set; }
        public string DIN { get; set; }
        public string TherapeuticClass { get; set; }
        public string TherapeuticSubClass { get; set; }
        public string RxNumber { get; set; }
        public string PharmacistLicense { get; set; }
        public string PharmacistSystem { get; set; }
        public string PharmacistDescription { get; set; }
        public string PrescriberLicense { get; set; }
        public string PrescriberSystem { get; set; }
        public string PrescriberDescription { get; set; }
        public string PharmacyId { get; set; }
        public string PharmacistName { get; set; }
        public string MedicationCondition { get; set; }
        public ICollection<CategoryCoding> CategoryCoding { get; set; }
        public P Patient { get; set; }
        public VMHealthCardDbCompare Compare { get; set; }
    }
}
