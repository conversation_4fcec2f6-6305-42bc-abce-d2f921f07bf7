﻿using System.Configuration;

namespace Cerebrum.DHDR.Services.Models
{
    public class CerebrumConfig : ConfigurationElement
    {
        [ConfigurationProperty("Enabled", IsRequired = false, DefaultValue = false)]
        public bool Enabled
        {
            get
            {
                return System.Convert.ToBoolean(this["Enabled"] ?? false);
            }
        }

        [ConfigurationProperty("ApiBaseUrl", IsRequired = false, DefaultValue = null)]
        public string ApiBaseUrl
        {
            get
            {
                return System.Convert.ToString(this["ApiBaseUrl"] ?? null);
            }
        }
    }
}
