﻿using AwareMD.DHDR.Dto;
using AwareMD.DHDR.FHIR;
using Cerebrum.DHDR.Services.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using Cerebrum.DHDR.Services.Utility;
using AwareMD.DHDR.Seedwork;
using Cerebrum.ViewModels.Patient;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.DHDR.Services
{
    public interface IDHDRService
    {
        Task<DrugDispenseSummary> GetPatientAllMed(string dhdrendpoint, int practiceId, int officeId, int userId, int patientRecordId, string hcn, string token);
        Task<Models.DrugDispenseSummary> GetPatientMedOnDate(string dhdrendpoint, int practiceId, int officeId, int userId, int patientRecordId, string hcn, DateTime onDate, string token);
        Task<Models.DrugDispenseSummary> GetPatientMedByDateRange(string dhdrendpoint, int practiceId, int officeId, int userId, int patientRecordId, string hcn, DateTime fromDate, DateTime toDate, string token);
        ICollection<DrugDispense<Patient>> GroupDrugDispenseList(ICollection<DrugDispense<Patient>> dragList, VMPatientInfo emrPatient);
        ICollection<DrugDispense<Patient>> GroupPharmacyServices(ICollection<DrugDispense<Patient>> dragList, VMPatientInfo emrPatient);
    }

    public class DHDRService : IDHDRService
    {
        /// <summary>
        /// Warning: be cautious when increasing this pagination, there is a limitation on how much we can store into the audit logs, for example, as on today 2023 Jan, <see cref="Cerebrum.Data.DHDRCommunicationLog.MedicationDispenseId"/> length is 4000, which is long enough to store up-to (50 rows per page x 80 characters id) - for example, Ids of covax can be 70 characters.
        /// </summary>
        private int DHDR_SEARCH_COUNT = 2000;
        readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private readonly IHttpClientFactory _httpClientFactory;
        private string baseURL = string.Empty; // URL of the DHDR microservices (not the DHDR EHR Services)
        private readonly string defaultErrorMessage = "There was a problem. Please Contact Administrator!";
        public DHDRService(IHttpClientFactory httpClientFactory)
        {
            baseURL = Config.Cerebrum?.ApiBaseUrl;
            _httpClientFactory = httpClientFactory;
        }
        public async Task<DrugDispenseSummary> GetPatientAllMed(string dhdrendpoint, int practiceId, int officeId, int userId, int patientRecordId, string hcn, string token)
        {
            DrugDispenseSummary result = new DrugDispenseSummary();

            try
            {
                if (!string.IsNullOrWhiteSpace(dhdrendpoint))
                {
                    PatientMedAllDTO patient = new PatientMedAllDTO
                    {
                        PatientRecordId = patientRecordId,
                        PracticeId = practiceId,
                        hcn = hcn,
                        OfficeId = officeId,
                        UserId = userId,
                        ClientId = Config.ADB2C?.ClientId,
                        Token = token,
                        DHDREndPoint = dhdrendpoint
                    };
                    HttpClient client = _httpClientFactory.CreateClient("DHDR");
                    client.Timeout = TimeSpan.FromMinutes(10);
                    //client.Timeout = TimeSpan.FromSeconds(int.Parse(REQUEST_TIMEOUT));
                    client.DefaultRequestHeaders.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    // for autorization API
                    //var dhdrApiToken = GetAwareMdDhdrApiToken();
                    //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", dhdrApiToken);

                    string message = JsonConvert.SerializeObject(patient);
                    HttpContent content = new StringContent(message, Encoding.UTF8, "application/json");
                    string adb2ctoken = GetAwareMdDhdrApiToken();
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", adb2ctoken);
                    string URL = $"{baseURL}/PatientMedAll";
                    HttpResponseMessage response = await client.PostAsync(URL, content);

                    if (response != null && response.IsSuccessStatusCode)
                    {
                        HttpContent httpContent = response.Content;
                        string responseContent = await httpContent.ReadAsStringAsync();
                        responseContent = Newtonsoft.Json.JsonConvert.DeserializeObject(responseContent).ToString(); // @TODO: a workaround to fix issue when json string was being escaped too much.

                        try
                        {
                            if (responseContent.Contains("httpCode"))
                            {
                                DHDRResponse resp = JsonConvert.DeserializeObject<DHDRResponse>(responseContent);
                                result.OtherResponse = resp;
                            }
                            else
                            {
                                result = Parser.ParseDrugDispenseSummary<DrugDispenseSummary, DrugDispense<Patient>, Patient>(responseContent);
                            }
                        }
                        catch (Exception ex)
                        {
                            _log.Error("Parsing error: " + ex.ToString());
                        }

                        return result;
                    }
                    else
                    {
                        string err = $"StatusCode: {response?.StatusCode}, URL: {URL}, message sent: {message}";
                        _log.Error(err);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex);
                result.OtherResponse.moreInformation = defaultErrorMessage;
            }
            return result;
        }
        public async Task<DrugDispenseSummary> GetPatientMedOnDate(string dhdrendpoint, int practiceId, int officeId, int userId, int patientRecordId, string hcn, DateTime onDate, string token)
        {
            DrugDispenseSummary result = new DrugDispenseSummary();
            try
            {
                if (!string.IsNullOrWhiteSpace(dhdrendpoint))
                {
                    PatientMedOnDateDTO patient = new PatientMedOnDateDTO
                    {
                        PatientRecordId = patientRecordId,
                        PracticeId = practiceId,
                        hcn = hcn,
                        OfficeId = officeId,
                        UserId = userId,
                        ClientId = Config.ADB2C?.ClientId,
                        OnDate = onDate,
                        Token = token,
                        DHDREndPoint = dhdrendpoint,
                        count = DHDR_SEARCH_COUNT
                    };
                    HttpClient client = _httpClientFactory.CreateClient("DHDR");
                    client.Timeout = TimeSpan.FromMinutes(10);
                    client.DefaultRequestHeaders.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                    string message = JsonConvert.SerializeObject(patient);
                    HttpContent content = new StringContent(message, Encoding.UTF8, "application/json");
                    string adb2ctoken = GetAwareMdDhdrApiToken();
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", adb2ctoken);
                    string URL = $"{baseURL}/PatientMedOnDate";
                    HttpResponseMessage response = await client.PostAsync(URL, content);

                    if (response != null && response.IsSuccessStatusCode)
                    {
                        HttpContent httpContent = response.Content;
                        string responseContent = await httpContent.ReadAsStringAsync();
                        responseContent = Newtonsoft.Json.JsonConvert.DeserializeObject(responseContent).ToString(); // @TODO: a workaround to fix issue when json string was being escaped too much.
                        try
                        {
                            if (responseContent.Contains("httpCode"))
                            {
                                DHDRResponse resp = JsonConvert.DeserializeObject<DHDRResponse>(responseContent);
                                result.OtherResponse = resp;
                            }
                            else
                            {
                                result = Parser.ParseDrugDispenseSummary<DrugDispenseSummary, DrugDispense<Patient>, Patient>(responseContent);
                            }
                        }
                        catch (Exception ex)
                        {
                            _log.Error("Parsing error: " + ex.ToString());
                        }
                        return result;
                    }
                    else
                    {
                        string err = $"StatusCode: {response?.StatusCode}, URL: {URL}, message sent: {message}";
                        _log.Error(err);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex);
                result.OtherResponse.moreInformation = defaultErrorMessage;
            }

            return result;
        }

        public async Task<DrugDispenseSummary> GetPatientMedByDateRange(string dhdrendpoint, int practiceId, int officeId, int userId, int patientRecordId, string hcn, DateTime fromDate, DateTime toDate, string token)
        {
            DrugDispenseSummary result = new DrugDispenseSummary();
            try
            {
                if (!string.IsNullOrWhiteSpace(dhdrendpoint))
                {
                    PatientMedDateRangeDTO patient = new PatientMedDateRangeDTO
                    {
                        PatientRecordId = patientRecordId,
                        PracticeId = practiceId,
                        hcn = hcn,
                        OfficeId = officeId,
                        UserId = userId,
                        ClientId = Config.ADB2C?.ClientId,
                        FromDate = fromDate,
                        ToDate = toDate,
                        Token = token,
                        DHDREndPoint = dhdrendpoint,
                        count = DHDR_SEARCH_COUNT
                    };
                    HttpClient client = _httpClientFactory.CreateClient("DHDR");
                    client.Timeout = TimeSpan.FromMinutes(10);
                    client.DefaultRequestHeaders.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                    string message = JsonConvert.SerializeObject(patient);
                    HttpContent content = new StringContent(message, Encoding.UTF8, "application/json");
                    string adb2ctoken = GetAwareMdDhdrApiToken();
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", adb2ctoken);
                    string URL = $"{baseURL}/PatientMedDateRange";
                    HttpResponseMessage response = await client.PostAsync(URL, content);

                    if (response != null && response.IsSuccessStatusCode)
                    {
                        HttpContent httpContent = response.Content;
                        string responseContent = await httpContent.ReadAsStringAsync();
                        responseContent = Newtonsoft.Json.JsonConvert.DeserializeObject(responseContent).ToString(); // @TODO: a workaround to fix issue when json string was being escaped too much.

                        try
                        {
                            if (responseContent.Contains("httpCode"))
                            {
                                DHDRResponse resp = JsonConvert.DeserializeObject<DHDRResponse>(responseContent);
                                result.OtherResponse = resp;
                            }
                            else
                            {
                                result = Parser.ParseDrugDispenseSummary<DrugDispenseSummary, DrugDispense<Patient>, Patient>(responseContent);
                            }
                        }
                        catch (Exception ex)
                        {
                            _log.Error("Parsing error: " + ex.ToString());
                        }
                        return result;
                    }
                    else
                    {
                        result.OtherResponse.moreInformation = defaultErrorMessage;
                        string err = $"StatusCode: {response?.StatusCode}, URL: {URL}, message sent: {message}";
                        _log.Error(err);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex);
                result.OtherResponse.moreInformation = defaultErrorMessage;
            }
            return result;
        }
        public ICollection<DrugDispense<Patient>> GroupPharmacyServices(ICollection<DrugDispense<Patient>> dragList, VMPatientInfo emrPatient)
        {
            int groupNumber = 1;
            ICollection<DrugDispense<Patient>> list = new List<DrugDispense<Patient>>();
            string code = Common.GetEnumDescription(DhdrCategory.Service);
            List<DrugDispense<Patient>> filtered = dragList.Where(item => item.CategoryCoding.Where(x => x.Code == code).ToList().Count > 0).OrderByDescending(x => x.DispensedDate).ToList();

            foreach (var item in filtered)
            {
                DrugDispense<Patient> drag = list.FirstOrDefault(d => d.BrandNameOfTheDispensedDrug == item.BrandNameOfTheDispensedDrug);

                DrugDispense<Patient> dragItem = item.DeepClone();
                IPatient patient = item.Patient;
                dragItem.Compare = GetMatchDispensAndEMRPatientInfo(emrPatient, patient);
                if (drag == null)
                {
                    dragItem.Group = groupNumber;
                    groupNumber++;
                }
                else
                {
                    dragItem.RxCount++;
                    dragItem.Show = false;
                    dragItem.Group = drag.Group;
                }

                list.Add(dragItem);
            }
            return list;
        }
        public ICollection<DrugDispense<Patient>> GroupDrugDispenseList(ICollection<DrugDispense<Patient>> dragList, VMPatientInfo emrPatient)
        {
            int groupNumber = 1;
            ICollection<DrugDispense<Patient>> list = new List<DrugDispense<Patient>>();
            string code = Common.GetEnumDescription(DhdrCategory.Product);
            List<DrugDispense<Patient>> filtered = dragList.Where(item => item.CategoryCoding.Where(x => x.Code == code || string.IsNullOrEmpty(x.Code)).ToList().Count > 0
                                                 || item.CategoryCoding.Count == 0).OrderByDescending(x => x.DispensedDate).ThenByDescending(x => x.PrescriberLastName).ToList();

            foreach (var item in filtered)
            {
                DrugDispense<Patient> drag = list.FirstOrDefault(d => d.GenericNameOfTheDispensedDrug == item.GenericNameOfTheDispensedDrug
                                               && d.DispensedDrugStrength == item.DispensedDrugStrength
                                               && d.DrugDosageForm == item.DrugDosageForm);

                DrugDispense<Patient> dragItem = item.DeepClone();
                IPatient patient = item.Patient;
                dragItem.Compare = GetMatchDispensAndEMRPatientInfo(emrPatient, patient);
                if (drag == null)
                {
                    dragItem.Group = groupNumber;
                    groupNumber++;
                }
                else
                {
                    dragItem.RxCount++;
                    dragItem.Show = false;
                    dragItem.Group = drag.Group;
                }

                list.Add(dragItem);
            }

            return list;
        }
        private VMHealthCardDbCompare GetMatchDispensAndEMRPatientInfo(VMPatientInfo emrPatient, IPatient patient)
        {
            VMHealthCardDbCompare cmp = new VMHealthCardDbCompare();
            if (emrPatient != null && patient != null)
            {
                IPatient dispensPatient = patient;
                string healthCardEMR = emrPatient.OHIP;
                string dispHC = dispensPatient.HealthCardNumber;

                var hccmpr = ((!(string.IsNullOrWhiteSpace(healthCardEMR) && string.IsNullOrWhiteSpace(dispHC))) && healthCardEMR.Trim().Equals(dispHC.Trim()));
                cmp.HealthNumber = new Tuple<string, string, bool>(healthCardEMR, dispHC, hccmpr);

                string[] dispName = dispensPatient.Name.Split(' ');

                string dispFirstName = string.Empty;
                string dispLastName = string.Empty;

                dispFirstName = dispName[0];
                dispLastName = dispName.Length > 1 ? dispName[1] : string.Empty;

                var LNCmp = ((!(string.IsNullOrWhiteSpace(emrPatient.LastName) && string.IsNullOrWhiteSpace(dispLastName))) && emrPatient.LastName.Trim().ToLower().Equals(dispLastName.Trim().ToLower()));
                cmp.LastName = new Tuple<string, string, bool>(emrPatient.LastName, dispLastName, LNCmp);

                var FNcmp = ((!(string.IsNullOrWhiteSpace(emrPatient.FirstName) && string.IsNullOrWhiteSpace(dispFirstName))) && emrPatient.FirstName.Trim().ToLower().Equals(dispFirstName.Trim().ToLower()));

                cmp.FirstName = new Tuple<string, string, bool>(emrPatient.FirstName, dispFirstName, FNcmp);

                var BDcmp = ((emrPatient.DateOfBirth != null && dispensPatient.BirthDate != null) && DateTime.Compare(DateTime.Parse(emrPatient.DateOfBirth.ToString()), DateTime.Parse(dispensPatient.BirthDate.ToString())) == 0);

                DateTime dbd = DateTime.Parse(dispensPatient.BirthDate);
                cmp.BirthDate = new Tuple<DateTime?, DateTime?, bool>(emrPatient.DateOfBirth, dbd, BDcmp);

                bool sameGenderInfo = false;

                if (dispensPatient.Gender != null)
                {
                    Gender gender = (Gender)dispensPatient.Gender;

                    if (emrPatient.Gender == gender)
                    {
                        sameGenderInfo = true;
                    }

                    cmp.Gender = new Tuple<string, string, bool>(emrPatient.Gender.ToString(), gender.ToString(), sameGenderInfo);
                }
            }
            return cmp;
        }
        private string GetAwareMdDhdrApiToken()
        {
            string token = AwareMD.Utility.Services.ADB2CToken.TokenGetAsync(Config.ADB2C?.ClientId, Config.ADB2C?.ClientSecret, Config.ADB2C?.Authority, Config.ADB2C?.Scope);

            return token;
        }
        private void LogError(Exception ex)
        {
            string logMessage = ex.ToString();
            if (ex.InnerException != null)
            {
                logMessage += Environment.NewLine + ex.InnerException.Message;
            }

            _log.Error(logMessage);
        }
    }
}
