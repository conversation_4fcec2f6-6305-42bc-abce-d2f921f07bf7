﻿using System.Configuration;
using Cerebrum.DHDR.Services.Models;

namespace Cerebrum.DHDR.Services
{
    public class Config : ConfigurationSection
    {
        public const string CEREBRUM_DHDR_API_ENDPOINT = "Cerebrum.DHDR_API.Endpoint";
        public const string DHDREndPointKey = "dhdrendpoint";
        public static Config GetConfiguration { get; } = ReadConfig();
        public static CerebrumConfig Cerebrum { get { return GetConfiguration?.CerebrumConfig; } }
        public static ADB2CConfig ADB2C { get { return GetConfiguration?.ADB2CConfig; } }
        public static DHDRDefaultDateLowerLimitConfig defaultDateLowerLimit { get { return GetConfiguration?.DHDRDefaultDateLowerLimit; } }
    private static Config ReadConfig()
    {
        Config result = new Config { };

        try
        {
            object attempt = ConfigurationManager.GetSection("DHDRSettings");

            if (attempt != null)
            {
                result = attempt as Config;
            }
        }
        catch { }

        return result;
    }

    [ConfigurationProperty("CerebrumConfig")]
    public CerebrumConfig CerebrumConfig
    {
        get
        {
            return (CerebrumConfig)this["CerebrumConfig"];
        }
        private set
        {
            value = (CerebrumConfig)this["CerebrumConfig"];
        }
    }

    [ConfigurationProperty("DHDRDefaultDateLowerLimit")]
    public DHDRDefaultDateLowerLimitConfig DHDRDefaultDateLowerLimit
    {
        get
        {
            return (DHDRDefaultDateLowerLimitConfig)this["DHDRDefaultDateLowerLimit"];
        }
        private set
        {
            value = (DHDRDefaultDateLowerLimitConfig)this["DHDRDefaultDateLowerLimit"];
        }
    }

    [ConfigurationProperty("ADB2CConfig")]
    public ADB2CConfig ADB2CConfig
    {
        get
        {
            return (ADB2CConfig)this["ADB2CConfig"];
        }
        private set
        {
            value = (ADB2CConfig)this["ADB2CConfig"];
        }
    }
}
}
