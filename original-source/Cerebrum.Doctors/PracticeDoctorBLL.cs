﻿using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data;
using Cerebrum.DTO.Doctor;
using Cerebrum.ViewModels.Common;
using Cerebrum.ViewModels.Doctor;
using Cerebrum.ViewModels.Medications;
using Cerebrum.ViewModels.OLIS;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace Cerebrum.Doctors
{
    public interface IPracticeDoctorBLL
    {
        List<VMPracticeDoctorLookup> GetAllPracticeDoctorsForOLIS(int practiceId);
        List<VMPracticeDoctorLookup> GetAllPracticeDoctors(int practiceId);
        List<VMOLISPreloadStatus> GetDoctorsPreloadInfo(int practiceId, string requestingHIC);
        VMPracticeDoctorLookup Get_Practice_TBD_Doctor(int practiceId);
        List<VMLookupItem> SearchMainDocs(string term_, int practiceId);
        List<VMLookupItem> SearchDemographicsDocs(string term_);
        VMDoctor GetPrescriptionDoctor(int practiceId, int userId);

        IPracticeDoctorDTO GetPracticeDoctor(int practiceId, int practiceDoctorId);
    }
    public class PracticeDoctorBLL : IPracticeDoctorBLL
    {
        readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private CerebrumContext _context;

        public PracticeDoctorBLL()
        {
            _context = new CerebrumContext();
        }
        public PracticeDoctorBLL(CerebrumContext dbCtx)
        {
            _context = dbCtx;
        }
        public List<VMPracticeDoctorLookup> GetAllPracticeDoctorsForOLIS(int practiceId)
        {
            List<VMPracticeDoctorLookup> dbdocs = new List<VMPracticeDoctorLookup>();
            try
            {
                var parameters = new List<SqlParameter> { new SqlParameter("practiceId", practiceId) };
                dbdocs = _context.GetData<VMPracticeDoctorLookup>("[dbo].[GetAllPracticeDoctorsForOLIS]", parameters).ToList();
            }
            catch (Exception ex)
            {
                _log.Error(ex);
            }


            return (dbdocs != null && dbdocs.Count() > 0) ? dbdocs.ToList() : null;
        }
        public List<VMPracticeDoctorLookup> GetAllPracticeDoctors(int practiceId)
        {
            List<VMPracticeDoctorLookup> dbdocs = new List<VMPracticeDoctorLookup>();
            var bdrs = (from pd in _context.PracticeDoctors
                        join ex in _context.ExternalDoctors on pd.ExternalDoctorId equals ex.Id
                        where pd.PracticeId == practiceId && (pd.IsActive && ex.active)
                        select new VMPracticeDoctorLookup
                        {
                            Id = pd.Id,
                            ExternalDoctorId = ex.Id,
                            PracticeId = pd.PracticeId,
                            LastName = ex.lastName,
                            FirstName = ex.firstName,
                            MiddleName = ex.middleName,
                            HRMId = ex.HRMId,
                            OHIPPhysicianId = ex.OHIPPhysicianId,
                            OLIS_AssiningJuridictionIdCode = pd.OLIS_AssiningJuridictionIdCode,
                            OLIS_AssiningJuridictionId = pd.OLIS_AssiningJuridictionId,
                            OLIS_IdType = pd.OLIS_IdType,
                            OLIS_RequestingHIC = ex.CPSO,
                            emailAddress = ex.emailAddress,
                            OLISActive = pd.OLISActive,
                            PreloadState = pd.OLISPreloadState,
                            PreloadStartDate = pd.OLISPreloadStartDate,
                            PreloadEndDate = pd.OLISPreloadEndDate
                        });
            foreach (var d in bdrs)
            {
                try
                {
                    if (!string.IsNullOrWhiteSpace(d.OLIS_RequestingHIC))
                    {
                        var olisqury = _context.OLISCommunicationLogs.Where(l => l.RequestingHIC.Equals(d.OLIS_RequestingHIC) && l.EMRQueryType == EMRToOLISQueryType.Preload).OrderBy(o => o.Id).Take(1);
                        if (olisqury != null && olisqury.Any())
                        {
                            var q = olisqury.FirstOrDefault();
                            d.OLISLastSuccessRunDate = q.createdDateTime;
                        }
                        dbdocs.Add(d);
                    }

                }
                catch (Exception ex)
                {
                    _log.Error(ex);
                }
            }

            return (dbdocs != null && dbdocs.Count() > 0) ? dbdocs.ToList() : null;
        }
        public List<VMOLISPreloadStatus> GetDoctorsPreloadInfo(int practiceId, string requestingHIC)
        {
            if (!string.IsNullOrWhiteSpace(requestingHIC))
            {
                string[] hics = requestingHIC.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                var dbdocs = from pd in _context.PracticeDoctors
                             join ex in _context.ExternalDoctors on pd.ExternalDoctorId equals ex.Id
                             join comlog in _context.OLISCommunicationLogs on ex.CPSO equals comlog.RequestingHIC
                             where pd.PracticeId == practiceId && hics.Contains(ex.CPSO)
                                   && pd.OLISPreloadState == OLISPreloadState.On
                                   && comlog.EMRQueryType == EMRToOLISQueryType.Preload
                             select new VMOLISPreloadStatus
                             {
                                 LastOLISQueryDate = comlog.createdDateTime,
                                 DoctorLastName = ex.lastName,
                                 DoctorFirstName = ex.firstName,
                                 RequestingHIC = ex.CPSO,
                             };


                return (dbdocs != null && dbdocs.Count() > 0) ? dbdocs.ToList() : null;
            }
            return null;
        }
        public VMPracticeDoctorLookup Get_Practice_TBD_Doctor(int practiceId)
        {
            int tbd_doctor = (int)Practice_DoctorType.TBD;
            var pd = _context.PracticeDoctors.FirstOrDefault(d => d.PracticeId == practiceId && d.PracticeDoctorTypeId == tbd_doctor && d.IsActive == true);
            if (pd != null)
            {
                var exDoc = _context.ExternalDoctors.Find(pd.ExternalDoctorId);

                var tbd = new VMPracticeDoctorLookup();
                tbd.Id = pd.Id;
                tbd.ExternalDoctorId = pd.ExternalDoctorId;
                tbd.IsActive = pd.IsActive;
                if (exDoc != null)
                {
                    tbd.LastName = exDoc.lastName;
                    tbd.FirstName = exDoc.firstName;
                    tbd.CPSO = exDoc.CPSO;
                    tbd.OHIPPhysicianId = exDoc.OHIPPhysicianId;
                }

                return tbd;
            }
            return null;
        }
        public List<VMLookupItem> SearchMainDocs(string term_, int practiceId)
        {
            List<ExternalDoctor> dbDoctors = new List<ExternalDoctor>();
            List<VMLookupItem> doctors = new List<VMLookupItem>();
            int n;
            bool isNumeric = int.TryParse(term_, out n);
            if (isNumeric)
            {
                doctors = (from ed in _context.ExternalDoctors
                           join pd in _context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                           where (ed.OHIPPhysicianId.StartsWith(term_.Trim().ToLower()) && ed.active == true && pd.PracticeId == practiceId)
                           select new VMLookupItem
                           {
                               Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                               Value = pd.Id.ToString()
                           }).ToList();
            }
            else if (term_.Contains(","))
            {
                string[] names = term_.Split(',');
                string lastname = names[0].Trim();
                string firstname = names[1].Trim();
                if ((!string.IsNullOrWhiteSpace(lastname)) && (string.IsNullOrWhiteSpace(firstname)))
                {
                    doctors = (from ed in _context.ExternalDoctors
                               join pd in _context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                               where (ed.lastName.StartsWith(lastname) && ed.active == true && pd.PracticeId == practiceId)
                               select new VMLookupItem
                               {
                                   Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                                   Value = pd.Id.ToString()
                               }).ToList();
                }
                else if ((string.IsNullOrWhiteSpace(lastname)) && (!string.IsNullOrWhiteSpace(firstname)))
                {
                    doctors = (from ed in _context.ExternalDoctors
                               join pd in _context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                               where (ed.firstName.StartsWith(firstname) && ed.active == true && pd.PracticeId == practiceId)
                               select new VMLookupItem
                               {
                                   Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                                   Value = pd.Id.ToString()
                               }).ToList();
                }
                else
                {
                    doctors = (from ed in _context.ExternalDoctors
                               join pd in _context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                               where (ed.firstName.StartsWith(firstname) && ed.lastName.StartsWith(lastname)
                               && ed.active == true && pd.PracticeId == practiceId)
                               select new VMLookupItem
                               {
                                   Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                                   Value = pd.Id.ToString()
                               }).ToList();
                }
            }
            else
            {
                doctors = (from ed in _context.ExternalDoctors
                           join pd in _context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                           where (ed.lastName.StartsWith(term_.Trim().ToLower()) && ed.active == true && pd.PracticeId == practiceId)
                           select new VMLookupItem
                           {
                               Text = (ed.lastName == null ? "" : ed.lastName.ToLower()) + ", " + (ed.firstName == null ? "" : ed.firstName.ToLower()),
                               Value = pd.Id.ToString()
                           }).ToList();
            }

            return doctors;
        }
        public List<VMLookupItem> SearchDemographicsDocs(string term_)
        {
            int topRecords = 15;
            List<ExternalDoctor> dbDoctors = new List<ExternalDoctor>();
            List<VMLookupItem> doctors = new List<VMLookupItem>();
            int n;
            bool isNumeric = int.TryParse(term_, out n);
            if (isNumeric)
            {
                dbDoctors = _context.ExternalDoctors.
                Where(d => d.OHIPPhysicianId.StartsWith(term_) && d.active).Take(topRecords).ToList();

            }
            else if (term_.Contains(","))
            {
                string[] names = term_.Split(',');
                string lastname = names[0].Trim();
                string firstname = names[1].Trim();
                if ((!string.IsNullOrWhiteSpace(lastname)) && (string.IsNullOrWhiteSpace(firstname)))
                {
                    dbDoctors = _context.ExternalDoctors.Where(d => d.lastName.StartsWith(lastname) && d.active).Take(topRecords).ToList();
                }
                else if ((string.IsNullOrWhiteSpace(lastname)) && (!string.IsNullOrWhiteSpace(firstname)))
                {
                    dbDoctors = _context.ExternalDoctors.Where(d => d.firstName.StartsWith(firstname) && d.active).Take(topRecords).ToList();
                }
                else
                {
                    dbDoctors = _context.ExternalDoctors.Where(d =>
                    d.lastName.StartsWith(lastname) &&
                    d.firstName.StartsWith(firstname)
                    && d.active
                    ).Take(topRecords).ToList();
                }
            }
            else
            {
                dbDoctors = _context.ExternalDoctors.Where(d => d.lastName.StartsWith(term_.Trim()) && d.active).Take(topRecords).ToList();
            }


            foreach (var dbRefDoctor in dbDoctors)
            {
                var doctor = new VMLookupItem();
                doctor.Value = dbRefDoctor.Id.ToString();
                doctor.Text = (dbRefDoctor.lastName == null ? "" : dbRefDoctor.lastName) + ", " +
                    (dbRefDoctor.firstName == null ? "" : dbRefDoctor.firstName) + " - " + (dbRefDoctor.OHIPPhysicianId == null ? "" : dbRefDoctor.OHIPPhysicianId);
                doctors.Add(doctor);
            }

            return doctors;
        }

        public IPracticeDoctorDTO GetPracticeDoctor(int practiceId, int practiceDoctorId)
        {
            IPracticeDoctorDTO doctor = new PracticeDoctorDTO();

            try
            {
                doctor = (from ed in _context.ExternalDoctors
                          join pd in _context.PracticeDoctors on ed.Id equals pd.ExternalDoctorId
                          where pd.PracticeId == practiceId && pd.Id == practiceDoctorId && pd.IsActive == true
                          select new PracticeDoctorDTO
                          {
                              PracticeDoctorId = pd.Id,
                              ExternalDoctorId = ed.Id,
                              LastName = ed.lastName,
                              FirstName = ed.firstName,
                              MiddleName = ed.middleName,
                              HRMId = ed.HRMId,
                              OHIPPhysicianId = ed.OHIPPhysicianId,
                              CPSO = ed.CPSO,
                              emailAddress = ed.emailAddress,
                              Degrees = ed.Degrees,
                              specialization = pd.specialization,
                              specialtyCodes = pd.specialtyCodes,
                              PhysicianType = ed.PhysicianType,
                              IsActive = pd.IsActive
                          }).FirstOrDefault();
            }
            catch (Exception ex)
            {
                _log.Error(ex);
            }
            return doctor;
        }

        public VMDoctor GetPrescriptionDoctor(int practiceId, int userId)
        {
            var doc = new VMDoctor();

            var extDoc = (from e in _context.ExternalDoctors
                          join p in _context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                          join u in _context.Users on p.ApplicationUserId equals u.Id
                          where u.UserID == userId && u.PracticeID == practiceId && p.PracticeId == practiceId
                          && p.ApplicationUserId != null
                          select new { e, p, u }).FirstOrDefault();

            if (extDoc != null)
            {
                doc.DoctorId = extDoc.e.Id;
                doc.FullName = extDoc.e.FullName;
                doc.FirstName = extDoc.e.firstName;
                doc.LastName = extDoc.e.lastName;
                doc.PracticeDoctorId = extDoc.p.Id;
                doc.PracticeId = extDoc.p.PracticeId;
                doc.UserId = extDoc.u.UserID;
                doc.CPSO = extDoc.e.CPSO;
                doc.OhipId = extDoc.e.OHIPPhysicianId;
                doc.LetterHead = extDoc.e.LetterHead;
                doc.Degress = extDoc.e.Degrees;

                var addressStr = "";
                var phoneStr = "";
                var fax = "";

                if (extDoc.p.mainOfficeId > 0)
                {
                    var office = _context.Offices.Find(extDoc.p.mainOfficeId);
                    doc.MainOfficeId = office.Id;

                    addressStr += office.address1 + ", ";
                    addressStr += !String.IsNullOrWhiteSpace(office.address2) ? office.address2 + ", " : "";
                    addressStr += !String.IsNullOrWhiteSpace(office.city) ? office.city + ", " : "";
                    addressStr += !String.IsNullOrWhiteSpace(office.postalCode) ? office.postalCode + " " : "";
                    addressStr += !String.IsNullOrWhiteSpace(office.country) ? office.country + " " : "";

                    fax = office.fax;
                    phoneStr += office.phone;
                }
                else
                {

                    var address = extDoc.e.externalDoctorAddresses.LastOrDefault();
                    var phone = extDoc.e.phoneNumbers.LastOrDefault();


                    if (address != null)
                    {
                        addressStr += address.addressLine1 + ", ";
                        addressStr += !String.IsNullOrWhiteSpace(address.addressLine2) ? address.addressLine2 + ", " : "";
                        addressStr += !String.IsNullOrWhiteSpace(address.city) ? address.city + ", " : "";
                        addressStr += !String.IsNullOrWhiteSpace(address.postalCode) ? address.postalCode + " " : "";
                        addressStr += !String.IsNullOrWhiteSpace(address.country) ? address.country + " " : "";

                        fax = address.faxNumber;
                    }

                    if (phone != null)
                    {
                        phoneStr += phone.phoneNumber;
                    }

                }

                doc.Address = addressStr;
                doc.Phone = phoneStr;
                doc.Fax = fax;

                var dbSignature = _context.PracticeDoctorReportSingature
                   .Where(x => x.PracticeDoctorId == doc.PracticeDoctorId)
                   .FirstOrDefault();

                if (dbSignature != null)
                {
                    doc.Signature = dbSignature.Image;
                }
            }
            else
            {
                doc = null;
            }


            return doc;
        }
    }
}
