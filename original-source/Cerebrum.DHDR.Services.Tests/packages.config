﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Castle.Core" version="5.1.0" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Http" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="6.0.0" targetFramework="net48" />
  <package id="Moq" version="4.18.2" targetFramework="net461" requireReinstallation="true" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.0" targetFramework="net48" />
  <package id="System.Diagnostics.EventLog" version="4.7.0" targetFramework="net461" requireReinstallation="true" />
  <package id="System.Memory" version="4.5.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Reflection.Emit" version="4.7.0" targetFramework="net461" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net461" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>