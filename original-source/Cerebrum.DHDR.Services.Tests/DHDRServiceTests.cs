﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Cerebrum.DHDR.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Moq;
using Cerebrum.Data;
using Cerebrum.BLL.Patient;
using System.Net.Http;

namespace Cerebrum.DHDR.Services.Tests
{
    [TestClass()]
    public class DHDRServiceTests
    {
        [TestMethod()]
        public void GetPatientAllMedTest_ML()
        {
            //CerebrumContext _dbcontext = new CerebrumContext();
            //PatientBLL _patientBll = new PatientBLL(_dbcontext);
            //var service = new DHDRService(_dbcontext, _patientBll);
            //var v= service.GetPatientAllMed(1, 1, 1, "asdadad", 1, "");

            Assert.IsTrue(true);
        }
    }
}