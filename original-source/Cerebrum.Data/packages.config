﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="6.1.1" targetFramework="net452" />
  <package id="AwareMD.Cerebrum.Shared" version="1.1.7" targetFramework="net461" />
  <package id="AwareMD.DHDR.Dto" version="1.0.25" targetFramework="net461" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net452" />
  <package id="log4net" version="2.0.12" targetFramework="net461" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.Owin" version="2.1.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Security" version="2.1.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Cookies" version="2.1.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.OAuth" version="2.1.0" targetFramework="net452" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net461" />
  <package id="Owin" version="1.0" targetFramework="net452" />
</packages>