// <auto-generated />
namespace Cerebrum.Data.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class NewCreate : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(NewCreate));
        
        string IMigrationMetadata.Id
        {
            get { return "201706232007589_NewCreate"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
