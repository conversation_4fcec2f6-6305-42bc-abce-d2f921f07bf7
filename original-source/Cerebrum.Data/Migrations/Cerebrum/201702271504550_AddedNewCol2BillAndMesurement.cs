namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddedNewCol2BillAndMesurement : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ReportPhrases", "Index", c => c.Int(nullable: false));
            AddColumn("dbo.ReportPhrases", "OldId", c => c.Int(nullable: false));
            AddColumn("dbo.Billing_FileSequence", "specialty", c => c.Int(nullable: false));
            AddColumn("dbo.Billing_Heb", "file", c => c.String(maxLength: 1024));
            DropColumn("dbo.Bills", "file");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Bills", "file", c => c.String(maxLength: 1024));
            DropColumn("dbo.Billing_Heb", "file");
            DropColumn("dbo.Billing_FileSequence", "specialty");
            DropColumn("dbo.ReportPhrases", "OldId");
            DropColumn("dbo.ReportPhrases", "Index");
        }
    }
}
