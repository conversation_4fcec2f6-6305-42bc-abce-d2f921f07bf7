namespace Cerebrum.Data.Migrations.Cerebrum
{
    using Shared;
    using System;
    using System.Collections.Generic;
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.Globalization;
    using System.Linq;

    internal sealed class CerebrumConfiguration : DbMigrationsConfiguration<CerebrumContext>
    {
        public CerebrumConfiguration()
        {
            AutomaticMigrationsEnabled = true;
            AutomaticMigrationDataLossAllowed = true;
            MigrationsDirectory = @"Migrations\Cerebrum";
        }

        //protected override void Seed(CerebrumContext context)
        //{
        //    var master = new Master { MasterName = "Cerebrum 3.0 Database", GenerationDate = DateTime.Now, Id = 1 };
        //    context.Master.AddOrUpdate(m => m.MasterName, master);
        //    context.SaveChanges();
        //    master = master ?? context.Master.FirstOrDefault();
        //    //AddPractices(master, context);
        //    AddInsuranceCompanies(master, context);
        //    AddSpecialties(master, context);

        //    AddApointmentTypes(context);
        //    AddHospitals(context);
        //    AddStorInventoryType(context);
        //    AddReasons(context);
        //    AddMobileNetworks(context);
        //    AddDocServices(context);
        //    AddOfficeUrlTypes(context);
        //    AddLooseReportCategories(context);
        //    AddTestResourceTypes(context);
        //    AddTreatments(context);
        //    AppointmentStatus(context);
        //    RequisitionInitializer(context);
        //    BillingInitializer(context);
        //    PermissionsInitializer(context);
        //    TriageUrgencyInitializer(context);
        //    AddReportClasses(context);
        //}
       
        //private void AddInsuranceCompanies(Master master, CerebrumContext c)
        //{
        //    var ins = new List<InsuranceCompany> {
        //        new InsuranceCompany {MasterId=master.Id, name = "Manulife Financial", address = "500 King St. Waterloo,ON N2J 4Z6" },
        //        new InsuranceCompany {MasterId=master.Id, name = "SunLife (UHIP)", address = "22 Frederick St. Kitchener,ON N2H 6M6" }
        //    };
        //    ins.ForEach(f => c.InsuranceCompanys.AddOrUpdate(i => i.name, f));
        //}
        //private void AddSpecialties(Master master, CerebrumContext c)
        //{
        //    var specialties = new List<Specialty>
        //    {
        //        new Specialty {MasterId=master.Id,title = "Cardiology", code = 60},
        //        new Specialty {MasterId=master.Id,title = "Family Doctor", code = 00},
        //        new Specialty {MasterId=master.Id,title = "Nuclear Medicine", code = 63},
        //        new Specialty {MasterId=master.Id,title = "Respiratory Diseases", code = 47},
        //        new Specialty {MasterId=master.Id,title = "Paediatrics", code = 26},
        //        new Specialty {MasterId=master.Id,title = "Internal Medicine", code = 13},
        //        new Specialty {MasterId=master.Id,title = "Vascular Surgery", code = 17},
        //        new Specialty {MasterId=master.Id,title = "TBD", code = 0},
        //        new Specialty {MasterId=master.Id,title = "Endocrinology", code = 15},
        //        new Specialty {MasterId=master.Id,title = "Internal Medicine", code = 13},
        //        new Specialty {MasterId=master.Id,title = "Radiology", code = 33}
        //    };
        //    c.Specialties.AddOrUpdate(f => f.title, specialties.ToArray());
        //    c.SaveChanges();
        //}
        //private void AddApointmentTypes(CerebrumContext context)
        //{
        //    var appointmentTypes = new List<AppointmentType>();
        //    appointmentTypes.Add(new AppointmentType
        //    {
        //        name = "Test Only",
        //        children = new List<AppointmentType> {
        //                                                        new AppointmentType {name = "Test Only ER", VPRequired = false},
        //                                                        new AppointmentType {name = "Test Only Ext", VPRequired = false},
        //                                                        new AppointmentType {name = "Test Only Int", VPRequired = false}
        //                                                        }
        //    });
        //    appointmentTypes.Add(new AppointmentType
        //    {
        //        name = "Consult",
        //        children = new List<AppointmentType> {
        //                                                        new AppointmentType {name = "ER consult"},
        //                                                        new AppointmentType {name = "ER consult 15",duration=TestDuration.T15},
        //                                                        new AppointmentType {name = "ER Urgent consult"},
        //                                                        new AppointmentType {name = "ER Urgent 15",duration=TestDuration.T15},
        //                                                        new AppointmentType {name = "New Patient 15",duration=TestDuration.T15},
        //                                                        new AppointmentType {name = "New Patient 45",duration=TestDuration.T45},
        //                                                        new AppointmentType {name = "New Patient 60",duration=TestDuration.T60},
        //                                                        new AppointmentType {name = "New Patient 75",duration=TestDuration.T75},
        //                                                        new AppointmentType {name = "New Patient Urgent"},
        //                                                        new AppointmentType {name = "New Patient Urgent 15",duration=TestDuration.T15},
        //                                                        new AppointmentType {name = "New Triage Patient"},
        //                                                        new AppointmentType {name = "New Ref"},
        //                                                        new AppointmentType {name = "New Ref 15",duration=TestDuration.T15},
        //                                                        new AppointmentType {name = "New Ref Urgent"},
        //                                                        new AppointmentType {name = "New Ref Urgent 15",duration=TestDuration.T15},
        //                                                     }
        //    });
        //    appointmentTypes.Add(new AppointmentType
        //    {
        //        name = "Follow Up",
        //        children = new List<AppointmentType> {
        //                                                        new AppointmentType {name = "2 week"},
        //                                                        new AppointmentType {name = "6 week "},
        //                                                        new AppointmentType {name = "6 week - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "1 month"},
        //                                                        new AppointmentType {name = "1 month - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "2 month"},
        //                                                        new AppointmentType {name = "2 month - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "2.5 month"},
        //                                                        new AppointmentType {name = "3 month"},
        //                                                        new AppointmentType {name = "3 month - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "4 month"},
        //                                                        new AppointmentType {name = "4 month - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "6 month"},
        //                                                        new AppointmentType {name = "6 month - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "9 month"},
        //                                                        new AppointmentType {name = "9 month - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "1 year"},
        //                                                        new AppointmentType {name = "1 year - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "18 month"},
        //                                                        new AppointmentType {name = "18 month - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "2 year"},
        //                                                        new AppointmentType {name = "2 year - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "Recent FU"},
        //                                                        new AppointmentType {name = "Recent FU - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "Urgent FU"},
        //                                                        new AppointmentType {name = "Urgent FU - 30 min",duration=TestDuration.T30},
        //                                                        new AppointmentType {name = "Quick Visit"},
        //                                                        new AppointmentType {name = "Research FU"},
        //                                                        new AppointmentType {name = "Telephone Call"},
        //                                                        new AppointmentType {name = "Tilt Test"},
        //                                                        new AppointmentType {name = "Patient Request"},
        //                                                        new AppointmentType {name = "CT/MRI FU"},
        //                                                         new AppointmentType {name = "Data Transfer",duration=TestDuration.T30}
        //                                                                                       }
        //    });

        //    appointmentTypes.Add(new AppointmentType
        //    {
        //        name = "No Visit",
        //        children = new List<AppointmentType> {
        //                                                        new AppointmentType {name = "E-Consult", VPRequired = false},
        //                                                        new AppointmentType {name = "Phone", VPRequired = false},
        //                                                        new AppointmentType {name = "Letter", VPRequired = false}
        //                                                     }
        //    });

        //    appointmentTypes.ForEach(f => context.AppointmentTypes.AddOrUpdate(a => a.name, f));
        //    context.SaveChanges();
        //}
        //private void AddHospitals(CerebrumContext context)
        //{
        //    var hospitals = new List<Hospital> { new Hospital { name = "SRHC", code = "2038" },
        //        new Hospital { name = "RVH", code = "1825" },
        //        new Hospital { name = "RVHS", code = "3943" },
        //        new Hospital { name = "SMGH", code = "1921" }
        //    };
        //    hospitals.ForEach(f => context.Hospitals.AddOrUpdate(h => h.code, f));
        //    context.SaveChanges();
        //}
        //private void AddStorInventoryType(CerebrumContext context)
        //{
        //    var inventoryTypes = new List<StoreInventoryType>
        //    {
        //        new StoreInventoryType {name="Holter Monitor"},
        //        new StoreInventoryType {name="BP Monitor"},
        //        new StoreInventoryType {name="ELR Monitor" }
        //    };
        //    inventoryTypes.ForEach(rs => context.StoreInventoryTypes.AddOrUpdate(s => s.name, rs));
        //    context.SaveChanges();
        //}
        //private void AddReasons(CerebrumContext context)
        //{
        //    var reasons = new List<Reason>
        //    {
        //        new Reason {Name="Please assess regarding ...",mark=1 },
        //        new Reason {Name="Please assess provide follow up regarding ...",mark=1 }
        //    };
        //    reasons.ForEach(rs => context.Reasons.AddOrUpdate(s => s.Name, rs));
        //    context.SaveChanges();
        //}
        //private void AddMobileNetworks(CerebrumContext context)
        //{
        //    var networks = new List<MobileNetwork>
        //    {
        //        new MobileNetwork {Name="Bell" },
        //        new MobileNetwork {Name="Rogers" }
        //    };
        //    networks.ForEach(rs => context.MobileNetworks.AddOrUpdate(s => s.Name, rs));
        //    context.SaveChanges();
        //}
        //private void AddDocServices(CerebrumContext context)
        //{
        //    var services = new List<DocService>
        //    {
        //        new DocService {Name="Cardiology",mark=1 },
        //        new DocService {Name="Rheumatology",mark=1 },
        //        new DocService {Name="Haematology",mark=1 },
        //        new DocService {Name="Oncology",mark=1 },
        //        new DocService {Name="Respirology",mark=1 },
        //        new DocService {Name="Neurology",mark=1 },
        //        new DocService {Name="Gastroenterology",mark=1 },
        //        new DocService {Name="Nephrology",mark=1 },
        //        new DocService {Name="Dermatology",mark=1 },
        //        new DocService {Name="Geriatrics",mark=1 },
        //        new DocService {Name="endocrinology",mark=1 },
        //        new DocService {Name="Allergy and Immunology",mark=1 },
        //        new DocService {Name="Ophthalmology",mark=1 },
        //        new DocService {Name="ENT",mark=1 },
        //        new DocService {Name="Neurosurgery",mark=1 },
        //        new DocService {Name="General Surgery",mark=1 },
        //        new DocService {Name="Orthopedic Surgery",mark=1 },
        //        new DocService {Name="Psychiatry",mark=1 },
        //        new DocService {Name="Other",mark=1 }
        //    };
        //    services.ForEach(rs => context.DocServices.AddOrUpdate(s => s.Name, rs));
        //    context.SaveChanges();
        //}
        //private void AddOfficeUrlTypes(CerebrumContext context)
        //{
        //    var officeUrlTypes = new List<OfficeUrlType>();
        //    officeUrlTypes.Add(new OfficeUrlType { urlType = "Website", description = "Website" });
        //    officeUrlTypes.Add(new OfficeUrlType { urlType = "Fax", description = "Fax" });
        //    officeUrlTypes.Add(new OfficeUrlType { urlType = "Image", description = "Image" });
        //    officeUrlTypes.Add(new OfficeUrlType { urlType = "MWL", description = "Modalities Work List" });
        //    officeUrlTypes.ForEach(f => context.OfficeUrlTypes.AddOrUpdate(s => s.urlType, f));
        //    context.SaveChanges();
        //}
        //private void AddLooseReportCategories(CerebrumContext context)
        //{
        //    var categories = new List<LooseReportCategory>();
        //    categories.Add(new LooseReportCategory { category = "angiograms" });
        //    categories.Add(new LooseReportCategory { category = "PCI notes" });
        //    categories.Add(new LooseReportCategory { category = "OR notes" });
        //    categories.Add(new LooseReportCategory { category = "Nuclear Cardiology" });
        //    categories.Add(new LooseReportCategory { category = "Radiology" });
        //    categories.Add(new LooseReportCategory { category = "EP proceedure notes" });
        //    categories.Add(new LooseReportCategory { category = "CT" });
        //    categories.Add(new LooseReportCategory { category = "MRI" });
        //    categories.Add(new LooseReportCategory { category = "general" });
        //    categories.Add(new LooseReportCategory { category = "hospital info" });
        //    categories.Add(new LooseReportCategory { category = "bloodwork" });
        //    categories.Add(new LooseReportCategory { category = "consultants letters" });
        //    categories.Add(new LooseReportCategory { category = "discharge summary" });
        //    categories.Add(new LooseReportCategory { category = "triage notes/referrals" });
        //    categories.Add(new LooseReportCategory { category = "consult request" });
        //    categories.Add(new LooseReportCategory { category = "referrals/requisitions" });
        //    categories.Add(new LooseReportCategory { category = "patient's consent" });
        //    categories.Add(new LooseReportCategory { category = "external testing" });
        //    categories.Add(new LooseReportCategory { category = "Cardiac Rehab" });
        //    categories.Add(new LooseReportCategory { category = "Pharmacy" });
        //    categories.Add(new LooseReportCategory { category = "Sleep Study" });
        //    categories.Add(new LooseReportCategory { category = "ABP Report" });
        //    categories.Add(new LooseReportCategory { category = "HRM" });
        //    categories.Add(new LooseReportCategory { category = "Hospital Med. Records" });
        //    categories.Add(new LooseReportCategory { category = "Vascular Cardiology" });
        //    categories.ForEach(c => context.LooseReportCategories.AddOrUpdate(s => s.category, c));
        //    context.SaveChanges();
        //}
        //private void AddTestResourceTypes(CerebrumContext context)
        //{
        //    var testresourcetypes = new List<TestResourceType> {
        //        new TestResourceType { TestResourceName = "Doctor" },
        //        new TestResourceType { TestResourceName = "Technician" },
        //        new TestResourceType { TestResourceName = "Room" },
        //    };
        //    testresourcetypes.ForEach(f => context.TestResourceTypes.AddOrUpdate(s => s.TestResourceName, f));
        //    context.SaveChanges();
        //}
        //private void AddTreatments(CerebrumContext context)
        //{
        //    var treatments = new List<TreatmentType>
        //    {
        //        new TreatmentType() { Description="CHRON-Continuous/Chronic", IsActive = true },
        //        new TreatmentType() { Description="ACU-Acute", IsActive = true },
        //        new TreatmentType() { Description="ONET-One Time", IsActive = true },
        //        new TreatmentType() { Description="PRN Long-term - As needed", IsActive = true },
        //        new TreatmentType() { Description="PRN Short-term - As needed", IsActive = true }
        //    };
        //    treatments.ForEach(f => context.TreatmentTypes.AddOrUpdate(s => s.Description, f));
        //    context.SaveChanges();
        //}
        //private void AddPrescriptionStatuses(CerebrumContext context)
        //{
        //    var statuses = new List<PrescriptionStatus>
        //    {
        //        new PrescriptionStatus() { Description="New", IsActive = true },
        //        new PrescriptionStatus() { Description="Active", IsActive = true },
        //        new PrescriptionStatus() { Description="Suspended", IsActive = true },
        //        new PrescriptionStatus() { Description="Aborted", IsActive = true },
        //        new PrescriptionStatus() { Description="Completed", IsActive = true },
        //        new PrescriptionStatus() { Description="Obsolete", IsActive = true },
        //        new PrescriptionStatus() { Description="Nullified", IsActive = true }

        //    };
        //    statuses.ForEach(f => context.PrescriptionStatuses.AddOrUpdate(s => s.Description, f));
        //    context.SaveChanges();
        //}
        //private void AppointmentStatus(CerebrumContext context)
        //{
        //    var aptestStatus = new List<AppointmentTestStatus> {
        //        new AppointmentTestStatus { Status="requested", Color="DarkTurquoise"},
        //        new AppointmentTestStatus { Status="appointed", Color="Chocolate"},
        //        new AppointmentTestStatus { Status="not arrived", Color="RosyBrown"},
        //        new AppointmentTestStatus { Status="arrived", Color="Olive"},
        //        new AppointmentTestStatus { Status="test started", Color="pink"},
        //        new AppointmentTestStatus { Status="test completed", Color="violet"},
        //        new AppointmentTestStatus { Status="images / data transferred", Color="magenta"},
        //        new AppointmentTestStatus { Status="ready for doctor", Color="orange"},
        //        new AppointmentTestStatus { Status="trainee report ready", Color="burlywood"}
        //        //new AppointmentTestStatus { Status="ready for sending", Color="cyan"},
        //        //new AppointmentTestStatus { Status="sent by fax", Color="lawngreen"},
        //        //new AppointmentTestStatus { Status="sent by HRM", Color="blue"},
        //        //new AppointmentTestStatus { Status="sent by both", Color="red"},
        //        //new AppointmentTestStatus { Status="sent by email", Color="green"},
        //        //new AppointmentTestStatus { Status="amended", Color="gray"},
        //        //new AppointmentTestStatus { Status="amended sent", Color="teal"}
        //    };

        //    aptestStatus.ForEach(f => context.AppointmentTestStatus.AddOrUpdate(s => s.Status, f));
        //    context.SaveChanges();
        //}

        //private void RequisitionInitializer(CerebrumContext c)
        //{
        //    RequisitionType requisitionTypeBloodUrine = new RequisitionType { name = "Blood&Urine", orderNumber = 0 };
        //    RequisitionType requisitionTypeInternal = new RequisitionType { name = "Internal", orderNumber = 1 };
        //    //RequisitionType requisitionTypeRadiology = new RequisitionType { name = "Radiology", orderNumber = 2 };
        //    RequisitionType requisitionTypeExternal = new RequisitionType { name = "External", orderNumber = 3 };
        //    RequisitionType requisitionTypeCath = new RequisitionType { name = "Cath", orderNumber = 4 };
        //    RequisitionType requisitionTypeConsultationRequest = new RequisitionType { name = "Consultation Request", orderNumber = 5 };
        //    c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeBloodUrine);
        //    c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeInternal);
        //    //c.RequisitionType.Add(requisitionTypeRadiology);
        //    c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeExternal);
        //    c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeCath);
        //    c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeConsultationRequest);

        //    c.RequisitionStatus.AddOrUpdate(r => r.text, new RequisitionStatus { color = "red", text = "Ordered" });
        //    c.RequisitionStatus.AddOrUpdate(r => r.text, new RequisitionStatus { color = "blue", text = "Arranged" });
        //    c.RequisitionStatus.AddOrUpdate(r => r.text, new RequisitionStatus { color = "green", text = "Fulfilled" });

        //    List<string> times = @"ASAP,After the test,1 Month,2 Months,3 Months,6 Months,1 year,Prior Next Appointment,At Next Appointment,No FU Needed".Split(',').Distinct().ToList();
        //    for (int i = 0; i < times.Count; i++)
        //    {
        //        c.RequisitionTime.Add(new RequisitionTime { name = times[i].Trim(), orderNumber = (i + 1) * 10 });
        //    }

        //    List<string> requisitionItems = @"0,Diabetes;1,IV contrast allergies;2,Nephrectomy;3,Pregnancy;4,Breastfeeding;5,Weight over 300 lbs.;
        //                                6,CT Head;7,CT Neck;8,CT Abdomen;9, CT Pelvis;10,CT Spine Cervical;11,CT Spine Lumbar;12,CT Extremety;
        //                                13,CT Head;14,CT Carotid;15,CT Pulmonary;16,CT Aorta;17,Ultrasound Abdomen;18,Ultrasound Pelvis;
        //                                19,Ultrasound Pelvis TV;20,Ultrasound Transrectal Prostate;21,Ultrasound Obstetrical Thyroid;
        //                                22,Ultrasound Scrotum;23,Ultrasound Arterial: Carotid;24,Ultrasound Leg;25,Ultrasound Venous Leg;
        //                                26,Ultrasound msk: Knee;27,Ultrasound msk: Shoulder;28,Ultrasound Hysterosonogram;29,Nuclear Bone;
        //                                30,Nuclear Brain;31,Nuclear Gallium;32,Nuclear Liver:rbc;33,Nuclear Liver/Spleen;34,Nuclear Lung V/Q;
        //                                35,Nuclear Renal:Baseline;36,Nuclear Renal:Captopril Thyroid;37,Cardiac Imaging  Nuclear Stress Exercise;
        //                                38,Cardiac Imaging  Nuclear Stress Persantine;39,Cardiac Imaging  Nuclear Stress Rest Thallium;
        //                                40,Cardiac Imaging  Nuclear Stress Rest MUGA;41,Echocardiography Regular;42,Echocardiography Stress;
        //                                43,Echocardiography Transesophageal;44,Mammography Screening;45,Mammography Work Up;46,Brest Ultrasound Right;
        //                                47,Brest Ultrasound Left;48,Fluoroscopy:Esophagus only;49,Fluoroscopy:Upper GI Series;50,Fluoroscopy:Small Bowel;
        //                                51,Fluoroscopy:Barium Enema;52,Fluoroscopy:Hysterosalpingogram;53,X-ray:Chest;54,X-ray:Abdomen;55,X-ray:Pelvis;
        //                                56,X-ray:Shoulder;57,X-ray:Hand;58,X:ray:Wrist;59,X-ray:Hip;60,X-ray:Knee;61,Xray:Ankle;62,Xray:Foot;
        //                                63,Xray:Spine Servical;64,Xray:SpineThoracic;65,X-ray:Spine Lumbar;66,CT Colonography;
        //                                67,CT Angiogram;68,Bone Mineral Densitometry;69,Sleep Study;
        //                                70,PFT;71,EP Study;72,ICD;73,Pace maker;74,Ablation;75,Cardiac MRI;76,Coronary Angiography;77,EEG;79,Cardioversion;
        //                                80,EPS;81,Renal Artery Doppler".Split(';').Distinct().ToList();
        //    for (int i = 0; i < requisitionItems.Count; i++)
        //    {
        //        string[] requisitionItem = requisitionItems[i].Split(',');
        //        c.RequisitionItem.AddOrUpdate(r => r.name, new RequisitionItem { requisitionItemId = int.Parse(requisitionItem[0].Trim()), name = requisitionItem[1].Trim(), requisitionType = requisitionTypeExternal });
        //    }
        //    c.SaveChanges();
        //}


        //private void BillingInitializer(CerebrumContext c)
        //{
        //    string DATEFORMAT = "yyyy/MM/dd";
        //    #region BilingTestRares
        //    List<string> billigTestRares = @"E1,G570,G570A,1 and 2 dim tech,1,11260,,1,1970/01/01,3000/01/01;
        //                                    E1,G571,G571A,Professional,,9620,,1,1970/01/01,3000/01/01;
        //                                    SE,G315,G315A,Technical,1,4350,,1,1970/01/01,3000/01/01;
        //                                    SE,G319,G319A,Interpretation,,6265,,1,1970/01/01,3000/01/01;
        //                                    SE,G582,G582A,Technical,1,12785,,1,1970/01/01,3000/01/01;
        //                                    SE,G583,G583A,Professional,,11015,,1,1970/01/01,3000/01/01;
        //                                    GXT,G315,G315A,Technical,1,4350,,1,1970/01/01,3000/01/01;
        //                                    GXT,G319,G319A,Interpretation,,6265,,1,1970/01/01,3000/01/01;
        //                                    ECG,G310,G310A,Technical,1,660,,1,1970/01/01,3000/01/01;
        //                                    ECG,G313,G313A,Interpretation,,445,,1,1970/01/01,3000/01/01;
        //                                    ELR,G690,G690A,Professional,,12225,,1,1970/01/01,3000/01/01;
        //                                    ELR,G692,G692A,Technical Recorder,1,16845,,1,1970/01/01,3000/01/01;
        //                                    H1,G650,G650A,Professional 12-35 hours,,4790,,1,1970/01/01,3000/01/01;
        //                                    H1,G651,G651A,Hook Up,1,2390,,1,1970/01/01,3000/01/01;
        //                                    H1,G652,G652A,Scanning,1,3270,,1,1970/01/01,3000/01/01;
        //                                    E2,G570,G570A,Technical,1,15000,for research studies only,1,1970/01/01,3000/01/01;
        //                                    H2,G658,G658A,Professional 36 - 59 hours,,7545,,1,1970/01/01,3000/01/01;
        //                                    H2,G682,G682A,Hook Up,1,4780,,1,1970/01/01,3000/01/01;
        //                                    H2,G683,G683A,Scanning,1,6540,,1,1970/01/01,3000/01/01;
        //                                    H3,G659,G659A,Professional - over 60 hours,,9585,,1,1970/01/01,3000/01/01;
        //                                    H3,G684,G684A,Hook Up,1,7165,,1,1970/01/01,3000/01/01;
        //                                    H3,G685,G685A,Scanning,1,9810,,1,1970/01/01,3000/01/01;
        //                                    E1B,G574A,G574AA,focussed,1,1605,,1,1970/01/01,3000/01/01;
        //                                    E1B,G575A,G575AA,focussed,,1395,,1,1970/01/01,3000/01/01;
        //                                    E1B,G579A,G579AA,Bubble Study,1,1135,,1,1970/01/01,3000/01/01;
        //                                    E3,G574,G574A,Technical,1,1605,,1,1970/01/01,3000/01/01;
        //                                    E3,G575,G575A,Professional,,1395,,1,1970/01/01,3000/01/01;
        //                                    BP,BP00,BP00A,AMBULATORY BLOOD PRESSURE MONITOR,1,6000,,1,1970/01/01,3000/01/01;
        //                                    H14,G647,G647A,recording,1,11265,,1,1970/01/01,3000/01/01;
        //                                    H14,G648,G648A,scanning technical,1,16400,,1,1970/01/01,3000/01/01;
        //                                    H14,G649,G649A,reporting,,12225,,1,1970/01/01,3000/01/01;
        //                                    H7,G659,G659A,professional,,9585,,1,1970/01/01,3000/01/01;
        //                                    H7,G684,G684A,hook up,1,7165,,1,1970/01/01,3000/01/01;
        //                                    H7,G685,G685A,scanning,1,9810,,1,1970/01/01,3000/01/01;
        //                                    SE30,G315,G315A,Technical ,1,4350,,1,1970/01/01,3000/01/01;
        //                                    SE30,G319,G319A,Interpretation ,,6265,,1,1970/01/01,3000/01/01;
        //                                    SE30,G582,G582A,Stress Echo 30 ,1,12785,,1,1970/01/01,3000/01/01;
        //                                    SE30,G583,G583A,Stress Echo 30,,11015,,1,1970/01/01,3000/01/01;
        //                                    EPED,G570,G570A,Techinical,1,11260,,1,1970/01/01,3000/01/01;
        //                                    EPED,G571,G571A,Professional,,9620,,1,1970/01/01,3000/01/01;
        //                                    ABG,J319,J319B,,2,1125,,1,1970/01/01,3000/01/01;
        //                                    ABG,Z459,Z459A,Arterial puncture,,1020,,1,1970/01/01,3000/01/01;
        //                                    PnP,J304,J304B,,1,1855,,1,1970/01/01,3000/01/01;
        //                                    PnP,J304,J304C,,,1075,,1,1970/01/01,3000/01/01;
        //                                    PnP,J323,J323B,,2,420,,1,1970/01/01,3000/01/01;
        //                                    PnP,J327,J327B,,1,281,,1,1970/01/01,3000/01/01;
        //                                    PnP,J327,J327C,,,645,,1,1970/01/01,3000/01/01;
        //                                    PFT,J304,J304B,,1,1855,,1,1970/01/01,3000/01/01;
        //                                    PFT,J304,J304C,,,1075,,1,1970/01/01,3000/01/01;
        //                                    PFT,J306,J306C,Functional residual capacity,,1605,,1,1970/01/01,3000/01/01;
        //                                    PFT,J306,J306B,Volume versus Time Study,2,1620,,1,1970/01/01,3000/01/01;
        //                                    PFT,J307,J307B,Volume versus Time Study,2,1750,,1,1970/01/01,3000/01/01;
        //                                    PFT,J307,J307C,Volume versus Time Study,,1785,,1,1970/01/01,3000/01/01;
        //                                    PFT,J310,J310C,Volume versus Time Study,,1800,,1,1970/01/01,3000/01/01;
        //                                    PFT,J310,J310B,Functional residual capacity,2,2140,,1,1970/01/01,3000/01/01;
        //                                    PFT,J323,J323B,,2,420,,1,1970/01/01,3000/01/01;
        //                                    PFT,J327,J327B,,1,281,,1,1970/01/01,3000/01/01;
        //                                    PFT,J327,J327C,,,645,,1,1970/01/01,3000/01/01;
        //                                    SPR,J304,J304B,Set to technical,1,1855,,1,1970/01/01,3000/01/01;
        //                                    SPR,J304,J304C,,,1075,,1,1970/01/01,3000/01/01;
        //                                    SPR,J323,J323B,,2,420,,1,1970/01/01,3000/01/01;
        //                                    OXM,J323B,J323BA,Oximetry,2,420,,1,1970/01/01,3000/01/01;
        //                                    E45,G570,G570A,Technical,1,11260,,1,1970/01/01,3000/01/01;
        //                                    E45,G571,G571A,Professional,,9620,,1,1970/01/01,3000/01/01;
        //                                    contrast,G585,G585A,Technical contrast,1,12675,,1,1970/01/01,3000/01/01;
        //                                    ELR7,G656,G656A,Professional,,5115,,1,1970/01/01,3000/01/01;
        //                                    ELR7,G686,G686A,Technical component for recording/hook up ,1,4560,,1,1970/01/01,3000/01/01;
        //                                    ELR7,G687,G687A,Technical component for scanning ,1,3120,,1,1970/01/01,3000/01/01;
        //                                    SC,G315,G315A,Exercise Stress Technical ,1,4350,,1,1970/01/01,3000/01/01;
        //                                    SC,G319,G319A,Exercise Stress Professional,,6265,,1,1970/01/01,3000/01/01;
        //                                    SC,G489A,G489AA,iv starting,1,354,,1,1970/01/01,3000/01/01;
        //                                    SC,J807,J807B,Exercise Perfusion,1,21755,,1,1970/01/01,3000/01/01;
        //                                    SC,J807,J807C,Exercise Perfusion,,3810,,1,1970/01/01,3000/01/01;
        //                                    SC,J808,J808B,Exercise Perfusion Delayed,1,8010,,1,1970/01/01,3000/01/01;
        //                                    SC,J808,J808C,Exercise Perfusion Delayed,,2090,,1,1970/01/01,3000/01/01;
        //                                    SC,J809,J809B,SPECT ,1,4350,$43.50 per unit x 2 ),2,1970/01/01,3000/01/01;
        //                                    SC,J809,J809C,SPECT,,2365,$23.65per unit x 2,2,1970/01/01,3000/01/01;
        //                                    SC,J813,J813B,Ejection Fraction,1,13515,,1,1970/01/01,3000/01/01;
        //                                    SC,J813,J813C,Ejection Fraction,,6250,,1,1970/01/01,3000/01/01;
        //                                    SC,J814,J814B,Myocardial Wall Motion,1,4815,,1,1970/01/01,3000/01/01;
        //                                    SC,J814,J814C,Myocardial Wall Motion,,3300,,1,1970/01/01,3000/01/01;
        //                                    PC,G111,G111A,Persantine Stress Technical,1,5075,,1,1970/01/01,3000/01/01;
        //                                    PC,G112,G112A,Persantine Stress Professional,,7500,,1,1970/01/01,3000/01/01;
        //                                    PC,G489A,G489AA,iv,1,354,,1,1970/01/01,3000/01/01;
        //                                    PC,J807,J807B,Exercise Perfusion,1,21755,,1,1970/01/01,3000/01/01;
        //                                    PC,J807,J807C,Exercise Perfusion,,3810,,1,1970/01/01,3000/01/01;
        //                                    PC,J808,J808B,Exercise Perfusion Delayed,1,8010,,1,1970/01/01,3000/01/01;
        //                                    PC,J808,J808C,Exercise Perfusion Delayed,,2090,,1,1970/01/01,3000/01/01;
        //                                    PC,J809,J809B,SPECT,1,4350,$43.50 per unit x 2,2,1970/01/01,3000/01/01;
        //                                    PC,J809,J809C,SPECT,,2365,$23.65per unit x 2,2,1970/01/01,3000/01/01;
        //                                    PC,J813,J813B,Ejection Fraction,1,13515,,1,1970/01/01,3000/01/01;
        //                                    PC,J813,J813C,Ejection Fraction,,6250,,1,1970/01/01,3000/01/01;
        //                                    PC,J814,J814B,Myocardial Wall Motion,1,4815,,1,1970/01/01,3000/01/01;
        //                                    PC,J814,J814C,Myocardial Wall Motion,,3300,,1,1970/01/01,3000/01/01;
        //                                    BST,G579A,G579AA,Bubble add on,1,1135,,1,1970/01/01,3000/01/01;
        //                                    SLECG,G311,G311A,Interpretation of transmitted ECG rhythm strip,1,192,,1,1970/01/01,3000/01/01;
        //                                    SLECG,G320,G320A,Interpretation of transmitted ECG rhythm strip,,430,,1,1970/01/01,3000/01/01;
        //                                    SLECG,G660,G660A,Event Recorder,,865,,1,1970/01/01,3000/01/01;
        //                                    SLECG,G661,G661A,Event Recorder,1,400,,1,1970/01/01,3000/01/01;
        //                                    DE,G174A,G174AA,Technical ,1,4675,,1,1970/01/01,3000/01/01;
        //                                    DE,G315A,G315AA,Technical,1,4350,,1,1970/01/01,3000/01/01;
        //                                    DE,G319A,G319AA,Interpretation,,6265,,1,1970/01/01,3000/01/01;
        //                                    DE,G582A,G582AA,Technical,1,12785,,1,1970/01/01,3000/01/01;
        //                                    DE,G583A,G583AA,Professional,,11015,,1,1970/01/01,3000/01/01".Replace("\n", "").Replace("\r", "").Split(';').Distinct().ToList();
        //    #endregion
        //    for (int i = 0; i < billigTestRares.Count; i++)
        //    {
        //        string[] billigTestRare = billigTestRares[i].Split(',');
        //        string testName = billigTestRare[0].Trim().ToLower();
        //        var test = c.Tests.Where(t => t.testShortName.ToLower() == testName).FirstOrDefault();
        //        if (test == null)
        //        {
        //            continue;
        //        }
        //        var testRare = new Billing_TestRare();
        //        testRare.testId = test.Id;
        //        testRare.code = billigTestRare[1].Trim();
        //        testRare.claimCode = billigTestRare[2].Trim().Substring(0, 5);
        //        testRare.name = billigTestRare[3].Trim();
        //        if (!string.IsNullOrEmpty(billigTestRare[4].Trim()) && (billigTestRare[4].Trim() == "1" || billigTestRare[4].Trim() == "2"))
        //        {
        //            if (billigTestRare[4].Trim() == "1")
        //            {
        //                testRare.billingTypeId = 2; //technical
        //            }
        //            else
        //            {
        //                testRare.billingTypeId = 3; //IHF
        //            }
        //        }
        //        else
        //        {
        //            testRare.billingTypeId = 1; //professional
        //        }

        //        testRare.fee = int.Parse(billigTestRare[5].Trim());
        //        if (!string.IsNullOrEmpty(billigTestRare[6].Trim()))
        //        {
        //            testRare.note = billigTestRare[6].Trim();
        //        }
        //        testRare.numberOfServices = (short)int.Parse(billigTestRare[7].Trim());
        //        testRare.startDate = DateTime.ParseExact(billigTestRare[8].Trim(), DATEFORMAT, CultureInfo.InvariantCulture);
        //        testRare.endDate = DateTime.ParseExact(billigTestRare[9].Trim(), DATEFORMAT, CultureInfo.InvariantCulture);
        //        c.BillingTestRares.AddOrUpdate(r => r.name, testRare);
        //    }
        //    #region ConsultRares
        //    List<string> consultRares = @"A001,A001A,2060,0,Minor Assessment,  ;
        //                                    A003,A003A,7125,0,General Assessment,    1/yr unless different diag;
        //                                    A004,A004A,3540,0,General ReAssessment,   2/yr;
        //                                    A005,A005A,6750,0,Consultation,   written referral;
        //                                    A006,A006A,4235,0,Repeat Consultation,  written referral;
        //                                    A007,A007A,3310,0,Intermediate Assessment,       ;
        //                                    A130,A130A,30070,13,Comprehensive Internal Medicine Consultation,  Start and stop times must be recorded in chart;
        //                                    A131,A131A,7090,13,Complex Medical Spec. Ass.,   4 per 12 mth period;
        //                                    A133,A133A,7985,13,Medical Specific Assessment,   1 per 12 mth unless different diag;
        //                                    A134,A134A,6125,13,Medical Specific Re-Assessment,   2 per 12 mth period;
        //                                    A135,A135A,15700,13,Consultation ,      Written referral;
        //                                    A136,A136A,10525,13,Repeat Consultation,     written referral;
        //                                    A138,A138A,3805,13,Partial Assessment,      ;
        //                                    A145,A145A,11460,13,Consultation - Cardiology - deleted code,        written referral;
        //                                    A150,A150A,30070,15,Comprehensive Endocrinology Consultation,     The consultation must be seventy-five (75) minutes of direct contact with the patient.;
        //                                    A151,A151A,7090,15,Complex medical specific re-assessment, ;
        //                                    A153,A153A,7985,15,Medical Specific Assessement, ;
        //                                    A154,A154A,6125,15,Medical specific re-assessment, ;
        //                                    A155,A155A,15700,15,NEW PATIENT CONSULTATION, ;
        //                                    A156,A156A,10525,15,repeat consult, ;
        //                                    A158,A158A,3805,15,Partial assessment, ;
        //                                    A260,A260A,30070,26,Special Pediatric Consult (75 min), ;
        //                                    A263,A263A,7770,26,Medical Specific Pediatric Assessment,  ;
        //                                    A264,A264A,5945,26,Medical Specific Re-assessment, ;
        //                                    A265,A265A,16700,26,Pediatric Consultation ,   ;
        //                                    A266,A266A,9135,26,Repeat Pediatric Consultation, ;
        //                                    A435,A435A,10525,13,Ltd. Consultation,   ;
        //                                    A470,A470A,30070,47,Comprehensive Assessment (>75 min),  ;
        //                                    A471,A471A,7090,47,Complex Medical Spec. Ass.,  4 per 12 mth period;
        //                                    A473,A473A,7985,47,Medical Specific Assessment,  1 per12 mth unless different diag;
        //                                    A474,A474A,6125,47,Medical Specific Re-Assessment,  2 per 12 mth period;
        //                                    A475,A475A,15700,47,Consultation,       written referral;
        //                                    A476,A476A,10525,47,Repeat Consultation,    written referral;
        //                                    A478,A478A,3805,47,Partial Assessment,     ;
        //                                    A565,A565A,9135,26,Limited Pediatric Consultation, ;
        //                                    A600,A600A,30070,60,Comprehensive Assessment (>75 min),  ;
        //                                    A601,A601A,7090,60,Complex Medical Spec. Ass.,  4 per 12 mth period;
        //                                    A603,A603A,7985,60,Medical Specific Assessment,  1 per12 mth unless different diag;
        //                                    A604,A604A,6125,60,Medical Specific Re-Assessment,  2 per 12 mth period;
        //                                    A605,A605A,15700,60,Consultation,       written referral;
        //                                    A606,A606A,10525,60,Repeat Consultation,    written referral;
        //                                    A608,A608A,3805,60,Partial Assessment,     ;
        //                                    A661,A661A,6880,26,Complex Medical Specific Re-assessmt,  ;
        //                                    A662,A662A,39565,26,Extended Special Pediatric Consult (90 min), ;
        //                                    A665,A665A,9135,26,Prenatal Consultation, ;
        //                                    A675,A675A,10525,60,Ltd. Consultation,   ;
        //                                    A760,A760A,8985,15,Complex endocrine neoplastic disease assessment, ;
        //                                    A765,A765A,16550,15,Consult of person 16 or younger, ;
        //                                    A765,A765A,16550,60,Consultation <16 yrs or under,    written referral;
        //                                    A905,A905A,5480,0,Ltd. Consultation,   ;
        //                                    A960,A960A,3640,13,Travel Premium for Physician's Office, ;
        //                                    A990,A990A,2000,13,1st pt seen in Physician's office, ;
        //                                    B100A,B100A,3500,60,First Telemedicine Patient Encounter premium (formerly OTN1), ;
        //                                    B101A,B101A,3500,60,First Cancelled/Missed Telemedicine Patient Encounter premium, Services cannot be billed in addition to the premiums for a missed, cancelled, or abandoned session.;
        //                                    B102A,B102A,3500,60,First Technical Difficulties Abandoned Patient Encounter premium, Services cannot be billed in addition to the premiums for a missed, cancelled, or abandoned session.;
        //                                    B200A,B200A,1500,60,Subsequent Telemedicine Patient Encounter premium (formerly OTN2), ;
        //                                    B201A,B201A,1500,60,Subsequent Missed/Cancelled Telemedicine Patient Encounter premium, Services cannot be billed in addition to the premiums for a missed, cancelled, or abandoned session.;
        //                                    B202A,B202A,1500,60,Subsequent Technical Difficulties Abandoned Patient Encounter premium, Services cannot be billed in addition to the premiums for a missed, cancelled, or abandoned session.;
        //                                    C601,C601A,7090,60,Hospital complex medical spec. ass.,     ;
        //                                    C603,C603A,7985,60,Hospital Medical Spec. Ass. ,    ;
        //                                    C604,C604A,5585,60,Hospital medical spec. re-assessment,   ;
        //                                    C605,C605A,15700,60,Hospital Consultation,  Hospital consultation;
        //                                    C605,C605A,15700,60,Hospital consult,    ;
        //                                    C606,C606A,10525,60,Hospital repeat consultation,    ;
        //                                    DVD,DVDA,5000,60,DVD of study, For a patient picking up a DVD of their echo;
        //                                    E078_A151,E078A,3545,15,chronic - A151, Diabetes;
        //                                    E078_A153,E078A,3992,15,chronic - A153, Diabetes;
        //                                    E078_A154,E078A,3062,15,chronic - A154, Diabetes;
        //                                    E078_A158,E078A,1902,15,chronic - A158, Diabetes;
        //                                    E078_B,E078A,3790,13,Chronic - A133,   250 Diab. 402 HT 428 CHF;
        //                                    E078_C,E078A,2910,13,Chronic - A134,   250 Diab. 402 HT 428 CHF;
        //                                    E078_D,E078A,1810,13,Chronic - A138,   250 Diab. 402 HT 428 CHF;
        //                                    E078_E,E078A,3368,60,Chronic - A601,     402 HT 428 CHF 250 Diab;
        //                                    E078_F,E078A,3790,60,Chronic - A603,       402 HT 428 CHF 250 Diab;
        //                                    E078_G,E078A,2910,60,Chronic - A604,     402 HT 428 CHF 250 Diab;
        //                                    E078_H,E078A,1810,60,Chronic - A608,      402 HT 428 CHF 250 Diab;
        //                                    E078_I,E078A,3368,13,Chronic - A131,   250 Diab. 402 HT 428 CHF;
        //                                    E078_J,E078A,2127,47,chronic - A471,;
        //                                    E078_K,E078A,2395,47,chronic - A473,;
        //                                    G570,G570A,22182,60,G 570 (for Payors), G 570 (for Payors);
        //                                    G571,G571A,18951,60,G 571 (for Payors), G 571 for Payor;
        //                                    G002,G002A,201,13,Glucose Test, ;
        //                                    G002,G002A,218,15,Dr,Singer Request,  ;
        //                                    G112,G112A,7500,13,Thallium - professional,   ;
        //                                    G176,G176A,33425,60,pacing - atrial,  ;
        //                                    G177,G177A,41680,60,pacing - ventricular,  ;
        //                                    G178,G178A,35205,60,pacing - catheter ablation therapy,  ;
        //                                    G179,G179A,11120,60,Repeated electrophysiology pacing, Repeated electro. pacing;
        //                                    G180,G180A,1695,60,dual chamber rep,  ;
        //                                    G249,G249A,23165,60,electrophysiologic measurements,  ;
        //                                    G259,G259A,38330,60,induction of ventricular arrhythmias,  ;
        //                                    G261,G261A,33105,60,induction of atrial arrhythmias,  ;
        //                                    G268,G268A,3125,60,cannulation of artery,   ;
        //                                    G283,G283A,1130,60,single chamber reprogramming,   ;
        //                                    G297,G297A,11870,60,Angiogram,  Angiogram;
        //                                    G307,G307A,955,60,Pacemaker pulse wave analysis,  ;
        //                                    G310,G310A,660,0,ECG technical,  ECG technical;
        //                                    G310,G310A,660,60,ECG - Technical,  ;
        //                                    G310,G310A,675,13,ECG technical,   ;
        //                                    G311,G311A,192,60,Interpretation of transmitted ECG rhythm strip,;
        //                                    G313,G313A,445,13,ECG interpretation,     ;
        //                                    G313,G313A,445,60,ECG - Professional,  ;
        //                                    G313,G313A,445,0,ECG professional,  ;
        //                                    G314,G314A,11200,60,tilt table testing,  ;
        //                                    G319,G319A,6265,60,Stress test prof. ,  ;
        //                                    G319,G319A,6265,13,Stress - Professional,  ;
        //                                    G320,G320A,430,60,Interpretation of transmitted ECG rhythm strip,;
        //                                    G321,G321A,4765,60,ICD programming,  Programmable ICD - automatic implantable defibrillator;
        //                                    G366,G366A,14850,60,Acute administration of anti-arrhythmic drugs, ;
        //                                    G372,G372A,389,60,Injection - each/addtional injection, Intramuscular, Subcutaneous or Intradermal Injection;
        //                                    G372,G372A,389,15,Injection,  Intrasmuscular, subcutaneous, and intradermal;
        //                                    G373,G373A,675,60,Injection - Sole/1st Injection, Intramuscular, Subcutaneous or Intradermal Injection;
        //                                    G391,G391A,2110,60,Resuscitation - after first 1/4 hour, ;
        //                                    G395,G395A,4225,60,Resuscitation - first 1/4 hour,  ;
        //                                    G493,G493A,625,15,ACTH Test, Single or multiple, per injection;
        //                                    G500,G500A,3180,13,Month in which insulin injections, two or more daily;
        //                                    G500,G500A,1060,15,Insulin supervision - newly started on 3 or more,;
        //                                    G510,G510A,2100,15,Over the phone insulin adjustment,  ;
        //                                    G514,G514A,1060,13,Each additional month, one to three contacts;
        //                                    G514,G514A,1060,15,Diabetes Monthly Management, Each additional month, 1 to 3 contacts;
        //                                    G520,G520A,2120,13,Diabetes Monthly Management,Each additional month, 4 or more contacts ;
        //                                    G520,G520A,2120,15,Over the phone insulin adjustment,  ;
        //                                    G571,G571A,7410,13,Echo- prof complete study 1 & 2 dimensions, ;
        //                                    G575,G575A,1745,13,Echo - Limited Prof., ;
        //                                    G650,G650A,4790,13,Holter - 12-35 hours,  ;
        //                                    G650,G650A,4790,60,Holter 12-35 hours,  ;
        //                                    G650,G650A,4790,0,Holter 12-35 hours,  ;
        //                                    G651,G651A,2450,0,Holter Hook up 12-35 hours,  ;
        //                                    G651,G651A,2425,60,Holter Hook up 12-35 hours, ;
        //                                    G651,G651A,2450,13,Holter Hook up - 12 - 35 hours,  ;
        //                                    G652,G652A,3355,13,Holter Scanning 12-35 hours,  ;
        //                                    G652,G652A,3355,60,Holter Scanning 12-35 hours,  ;
        //                                    G652,G652A,3355,0,Holter Scanning 12-35 hours,  ;
        //                                    G660,G660A,865,60,Event Recorder,;
        //                                    G661,G661A,400,60,Event Recorder,;
        //                                    G700,G700A,510,60,Procedure ECG add on, Procedure ECG add on;
        //                                    G700,G700A,510,15,Procedure ECG add on,   Procedure ECG add on;
        //                                    G700,G700A,510,0,Procedure ECG add on,     Procedure ECG add on;
        //                                    J021,J021A,12140,60,angiography - insertion of catheter,   ;
        //                                    J332,J332C,1080,0,Oximetry,   Oximetry;
        //                                    J332C,J332A,1080,60,oximetry at rest and exercise or during sleep ,   oximetry at rest and exercise, or during sleep ;
        //                                    J332E,J332A,1080,13,oximetry at rest and exercise or during sleep,   oximetry at rest and exercise, or during sleep;
        //                                    K002,K002A,6275,26,Interview with Relatives (30 min/per unit),  ;
        //                                    K012,K012A,1580,15,Psychotherapy 4 people, Group-per member-first 12 units per day;
        //                                    K013,K013A,5835,15,Counselling-Individual,     3 units/12 mth (K013 & K040);
        //                                    K013,K013A,5835,60,Counselling-Individual, 3 units/12 mth (K013 & K040);
        //                                    K013,K013A,5835,13,Counselling-Individual,   3 units/12 mth (K013 & K040);
        //                                    K013,K013A,5835,0,Counselling-Individual,  ;
        //                                    K014,K014A,5835,0,Transplant Counselling,  ;
        //                                    K015,K015A,5835,0,Counselling Relatives,  ;
        //                                    K015,K015A,5505,60,Counselling Relatives,Terminally ill patient;
        //                                    K015,K015A,5835,13,Counselling Relatives, Terminally ill patient;
        //                                    K015,K015A,5835,15,Counselling Relatives,   ;
        //                                    K015,K015A,6275,15,Counseling of Relatives, Counseling of relatives-on behalf of catastrophically or terminally ill patient- 1 or more persons;
        //                                    K019,K019A,3140,15,Psychotherapy 2 people, Group-per member-first 12 units per day;
        //                                    K020,K020A,2090,15,Psychotherapy 3 people, group-permember-first 12 units per day;
        //                                    K024,K024A,1300,15,Psychotherapy 5 people, Group-per member-first 12 units per day;
        //                                    K025,K025A,1105,15,Psychotherapy 6 to 12 people group,   Group-per member-first 12 units per day;
        //                                    K029,K029A,6275,15,Insulin Therapy Support,     6 units/year;
        //                                    K029,K029A,5835,13,Insulin Therapy Support,   6 units/year;
        //                                    K030,K030A,3135,13,Diabetic Man. Assessment,       3 units/year;
        //                                    K033,K033A,3195,13,Additional individual counselling,  Additional units - individual care;
        //                                    K033,K033A,3195,60,Additional Individual Counselling,    Additional units-individual care;
        //                                    K035,K035A,3625,60,MOT form,  ;
        //                                    K036,K036A,1025,60,Northern travel grant,  ;
        //                                    K036,K036A,1025,15,Northern Health Travel Grant Application, Long Distance Travel Form;
        //                                    K036,K036A,1025,13,Northern Health Travel Grant Application, ;
        //                                    K040,K040A,5835,13,Group Counselling 2 or more,  3 units/12 mth (K013 & K040);
        //                                    K040,K040A,5835,60,Group Counselling 2 or more, 3 units/12 mth (K013 &K040);
        //                                    K040,K040A,5835,0,Group Counselling  2 or more,  ;
        //                                    K041,K041A,3195,60,Additional Group Counselling ,  Additional Group Counselling;
        //                                    K041,K041A,3195,13,Additional group counselling,  Additional group counselling;
        //                                    K045,K045A,7500,13,Diabetes management per 12 month period, ;
        //                                    K045,K045A,7500,15,Diabetes management per 12 month period, ;
        //                                    K046,K046A,11500,13,Team, ;
        //                                    K046,K046A,11500,15,Diabetes team management per 12 month period,  ;
        //                                    K050,K050A,10000,60,Health Status Report Completion, Ontario Disability Support Program Health Status Report and activities of Daily Living Index (completion of amalgamated forms;
        //                                    K055,K055A,2000,15,Application for Special Diet Allowance, ;
        //                                    K070,K070A,3175,15,CCAC home care application, ;
        //                                    K072,K072A,2140,15,CCAC chronic home care supervision,  ;
        //                                    K731,K731A,3550,60,Phone Consult Consulting Physician,  ;
        //                                    K731,K731A,3550,15,Phone Consult Consulting Physician, Must be 10 min in length. Referring and consultant doctors noted, also note and document recommendation.;
        //                                    K738,K738A,1600,60,Physician to Physician e-consultation-referring physician, ;
        //                                    K738,K738A,1600,15,Physician to Physician e-consultation-referring Physician, ;
        //                                    K739,K739A,2050,15,Physician to Physician e-consultation consultant physician, ;
        //                                    K739,K739A,2050,60,Physician to Physician e-consultation-Consultant Physician, ;
        //                                    K994,K994A,5455,13,On call , ;
        //                                    PFTCA,PFTCA,6000,60,CANN AMM PFT,  PFT for CANN AMM billing only;
        //                                    Z429,Z429A,29925,60,implantation of coronary sinus lead,  ;
        //                                    Z437,Z437A,9245,60,cardioversion,  ;
        //                                    Z439,Z439A,16690,60,Right heart - pressures only,  ;
        //                                    Z440,Z440A,21055,60,left - retrograde aortic,   ;
        //                                    Z441,Z441A,29715,60,left - transeptal,   ;
        //                                    Z459,Z459A,1020,60,Arterial puncture,  Arterial puncture;
        //                                    Z771,Z771A,2860,13,Aspiration Biopsy, ;
        //                                    Z771,Z771A,3800,15,Aspiration biopsy, thyroid gland or nodule fine needle method, ;
        //                                    z771_x_2,z771A,7600,15,Aspiration Biopsy,".Replace("\n", "").Replace("\r", "").Split(';').Distinct().ToList();
        //    #endregion
        //    for (int i = 0; i < consultRares.Count; i++)
        //    {
        //        string[] consultRare = consultRares[i].Split(',');
        //        var testRare = new Billing_ConsultRare();
        //        testRare.consultCode = consultRare[0].Trim();
        //        testRare.claimCode = consultRare[1].Trim();
        //        if (!string.IsNullOrEmpty(consultRare[2].Trim()))
        //        {
        //            testRare.fee = int.Parse(consultRare[2].Trim());
        //        }
        //        if (!string.IsNullOrEmpty(consultRare[3].Trim()))
        //        {
        //            testRare.specialty = int.Parse(consultRare[3].Trim());
        //        }
        //        testRare.name = consultRare[4].Trim();
        //        testRare.note = consultRare[5].Trim();
        //        c.BillingConsultRares.AddOrUpdate(r => r.name, testRare);
        //        c.SaveChanges();
        //    }

        //    List<string> billStatuses = @"Not Billed,saddlebrown,0;
        //                                Billed,blue,1;
        //                                Filed,lime,2;
        //                                Sent,magenta,3;
        //                                Confirmed,indigo,4;
        //                                Returned,cyan,5;
        //                                Paid,green,6;
        //                                Refused,red,7;
        //                                Mixed,gray,8;
        //                                Stall,darkslategray,9;
        //                                WriteOff,black,10;
        //                                Readjusted,darkcyan,11;
        //                                Retracted,navy,12".Replace("\n", "").Replace("\r", "").Split(';').Distinct().ToList();
        //    for (int i = 0; i < billStatuses.Count; i++)
        //    {
        //        string[] billStatus = billStatuses[i].Split(',');
        //        var status = new BillStatus();
        //        status.name = billStatus[0].Trim();
        //        status.color = billStatus[1].Trim();
        //        status.orderNumber = int.Parse(billStatus[2].Trim());
        //        c.BillStatuses.AddOrUpdate(r => r.name, status);
        //    }

        //    c.BillingTypes.AddOrUpdate(r => r.name, new Billing_Type { name = "professional" });
        //    c.BillingTypes.AddOrUpdate(r => r.name, new Billing_Type { name = "technical" });
        //    c.BillingTypes.AddOrUpdate(r => r.name, new Billing_Type { name = "ihf" });

        //    c.SaveChanges();
        //}
        //private void LISInitializer(CerebrumContext c)
        //{
        //    var requestcatslst = new List<OLISTestRequestCategory>
        //    {
        //        new OLISTestRequestCategory { categoryName = "Allergen", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestRequestCategory { categoryName = "Blood Bank", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestRequestCategory { categoryName = "Chem", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestRequestCategory { categoryName = "Clinical", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestRequestCategory { categoryName = "Hematology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestRequestCategory { categoryName = "Immuno", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestRequestCategory { categoryName = "Microbiology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestRequestCategory { categoryName = "Pathology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestRequestCategory { categoryName = "Serology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true }
        //    };
        //    requestcatslst.ForEach(f => c.OLISTestRequestCategories.AddOrUpdate(r => r.categoryName, f));
        //    c.SaveChanges();

        //    var reqtestsubcat = new List<OLISTestRequestSubCategory>()
        //    {
        //        new OLISTestRequestSubCategory {categoryName="Amino Acid",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Animal Epidermals & Proteins Regular Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Animal Epidermals & Proteins Special Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Autoantibody",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Challenge",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Coag",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Drug/Tox",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Drug",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Endocrine",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Enzyme",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Fetal Status",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="flow",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Foods Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Food Regular Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Food Special Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Glucose",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //        new OLISTestRequestSubCategory {categoryName="Protein",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
        //    };
        //    reqtestsubcat.ForEach(f => c.OLISTestRequestSubCategories.AddOrUpdate(r => r.categoryName, f));
        //    try
        //    {
        //        c.SaveChanges();
        //    }
        //    catch (System.Data.Entity.Validation.DbEntityValidationException dbEx)
        //    {
        //        Exception raise = dbEx;
        //        foreach (var validationErrors in dbEx.EntityValidationErrors)
        //        {
        //            foreach (var validationError in validationErrors.ValidationErrors)
        //            {
        //                string message = string.Format("{0}:{1}",
        //                    validationErrors.Entry.Entity.ToString(),
        //                    validationError.ErrorMessage);
        //                // raise a new exception nesting
        //                // the current instance as InnerException
        //                raise = new InvalidOperationException(message, raise);
        //            }
        //        }
        //        throw raise;
        //    }


        //    var rptcat = new List<OLISTestReportCategory>
        //    {
        //        new OLISTestReportCategory {categoryName="Allergens", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestReportCategory {categoryName="Blood Bank", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestReportCategory {categoryName="Chemistry", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestReportCategory {categoryName="Clinical", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestReportCategory {categoryName="Hematology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestReportCategory {categoryName="Immunology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestReportCategory {categoryName="Microbiology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestReportCategory {categoryName="Pathology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestReportCategory {categoryName="Serology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true }
        //    };
        //    rptcat.ForEach(f => c.OLISTestReportCategories.AddOrUpdate(r => r.categoryName, f));
        //    try
        //    {
        //        c.SaveChanges();
        //    }
        //    catch (System.Data.Entity.Validation.DbEntityValidationException dbEx)
        //    {
        //        Exception raise = dbEx;
        //        foreach (var validationErrors in dbEx.EntityValidationErrors)
        //        {
        //            foreach (var validationError in validationErrors.ValidationErrors)
        //            {
        //                string message = string.Format("{0}:{1}",
        //                    validationErrors.Entry.Entity.ToString(),
        //                    validationError.ErrorMessage);
        //                // raise a new exception nesting
        //                // the current instance as InnerException
        //                raise = new InvalidOperationException(message, raise);
        //            }
        //        }
        //        throw raise;
        //    }

        //    var rsltcat = new List<OLISTestResultCategory>
        //    {
        //        new OLISTestResultCategory {categoryName="ATTACHED.MEDS", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestResultCategory {categoryName="CALCULUS ANALYSIS", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestResultCategory {categoryName="CYTO", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestResultCategory {categoryName="MICRO", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestResultCategory {categoryName="MOLPATH", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestResultCategory {categoryName="MOLPATH.MUT", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestResultCategory {categoryName="PATH.PROTOCOLS", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
        //        new OLISTestResultCategory {categoryName="SERO", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true }
        //    };
        //    rsltcat.ForEach(f => c.OLISTestResultCategories.AddOrUpdate(r => r.categoryName, f));
        //    c.SaveChanges();
        //}
        //private void PermissionsInitializer(CerebrumContext context)
        //{
        //    var MasterName = "Cerebrum 3.0 Database";
        //    var master = context.Master.FirstOrDefault(d => d.MasterName == MasterName);

        //    var permissionbase = new PermissionsBase { MasterId = master.Id, MasterName = master.MasterName };


        //    var permissionTypes = new List<PermissionType> {
        //         new PermissionType{
        //                Name = "DaysheetLinks", Permissions = new List<Permission>{
        //                    new Permission { Name = "OpenTestWorksheet" },
        //                    new Permission { Name = "OpenTestReport" },
        //                    new Permission { Name = "OpenVP" },
        //                    new Permission { Name = "OpenLetter" },
        //                    new Permission { Name = "OpenPerformeter" },
        //                    new Permission { Name = "OpenSummary" },
        //                    new Permission { Name = "OpenLetterList" },
        //                    new Permission { Name = "OpenPatientGroups" },
        //                    new Permission { Name = "OpenReport" },
        //                    new Permission { Name = "OpenAssignNewHL7" },
        //                    new Permission { Name = "OpenWLSearch" },
        //                    new Permission { Name = "OpenInbox" },
        //                    new Permission { Name = "Inventory" },
        //                    new Permission { Name = "UploadReports" },
        //                    new Permission { Name = "ReminderReports" },
        //                    new Permission { Name = "FaxReport" },
        //                    new Permission { Name = "Inventory" },
        //                    new Permission { Name = "RD" }
        //                }
        //    },
        //        new PermissionType
        //        {
        //            Name = "Demographics",
        //            Permissions = new List<Permission>{
        //                    new Permission { Name = "VewPreviousTests" },
        //                    new Permission { Name = "SearchAppointments" },
        //                    new Permission { Name = "ViewMedicationHistory" },
        //                    new Permission { Name = "ViewTestHistory" },
        //                    new Permission { Name = "ViewExternalReportsList" },
        //                    new Permission { Name = "ViewClinicalSummary" },
        //                    new Permission { Name = "ViewPatientsCM" },
        //                    new Permission { Name = "ViewBillingHistory" },
        //                    new Permission { Name = "ViewLetterHistory" },
        //                    new Permission { Name = "ViewAlertManager" },
        //                    new Permission { Name = "ViewPatientStatement" },
        //                    new Permission { Name = "OpenProstheticValve" },
        //                    new Permission { Name = "OpenUpUpcomingAppointments" },
        //                    new Permission { Name = "OpenDoctorComments" },
        //                    new Permission { Name = "AddOrDeleteNewGroup" }
        //                }
        //        },
        //        new PermissionType
        //        {
        //            Name = "Worksheet",
        //            Permissions = new List<Permission>{
        //                    new Permission { Name = "SaveChanges" },
        //                    new Permission { Name = "SendReport" },
        //                    new Permission { Name = "SendAmendedReport" },
        //                    new Permission { Name = "ReAssign" },
        //                    new Permission { Name = "ViewImagesRawData" }
        //                }
        //        },
        //        new PermissionType
        //        {
        //            Name = "VP",
        //            Permissions = new List<Permission>{
        //                    new Permission { Name = "SaveChangesToPhrasesOrMeasurements" },
        //                    new Permission { Name = "SendLetter" },
        //                    new Permission { Name = "SendAmendedLetter" },
        //                    new Permission { Name = "AddBillingCodes" },
        //                    new Permission { Name = "CreateOrders" },
        //                    new Permission { Name = "Doctor" }
        //                }
        //        },
        //        new PermissionType
        //        {
        //            Name = "Medication",
        //            Permissions = new List<Permission>{
        //                    new Permission { Name = "Add_Or_DC_a_Medication" },
        //                    new Permission { Name = "ChangeDose" },
        //                    new Permission { Name = "AddAllergy" },
        //                    new Permission { Name = "CreateTemplates" },
        //                    new Permission { Name = "WritePrescription" },
        //                    new Permission { Name = "ReprintOrRefaxPrescription" },
        //                    new Permission { Name = "View Medications", crtype = CRType.Data  }
        //                }
        //        },
        //        new PermissionType
        //        {
        //            Name = "ExternalReports",
        //            Permissions = new List<Permission>{
        //                    new Permission { Name = "ViewReports" },
        //                    new Permission { Name = "AlertReports" },
        //                    new Permission { Name = "CommentOnReports" },
        //                    new Permission { Name = "AddRequisition" },
        //                    new Permission { Name = "AddBilling" },
        //                    new Permission { Name = "MarkSeen" },
        //                    new Permission { Name = "ReclassifyReports" }
        //                }
        //        },
        //        new PermissionType
        //        {
        //            Name = "OtherActions",
        //            Permissions = new List<Permission>{
        //                    new Permission { Name = "IssuePatientStatement" },
        //                    new Permission { Name = "PrintReceipt" },
        //                    new Permission { Name = "ChangePatientStatus" },
        //                    new Permission { Name = "ChangeClaims" },
        //                    new Permission { Name = "SaveDoctorComments" },
        //                    new Permission { Name = "ViewWaitList" },
        //                    new Permission { Name = "SaveChangesToWaitlist" },
        //                    new Permission { Name = "AddTestsToWaitlist" },
        //                    new Permission { Name = "AssignReportsInUploadReports" },
        //                    new Permission { Name = "SaveChangesInTheInventory" },
        //                    new Permission { Name = "SaveChangesInAssignHL7" }
        //                }
        //        },
        //        new PermissionType
        //        {
        //            Name = "Admin",
        //            Permissions = new List<Permission>{
        //                    new Permission { Name = "AddUserOrEditUser" },
        //                    new Permission { Name = "AddDoctorOrEditDoctor" },
        //                    new Permission { Name = "AddClinicOrEditClinic" },
        //                    new Permission { Name = "CreateNewRole" },
        //                    new Permission { Name = "LoginReport" },
        //                    new Permission { Name = "PageOpeningReport" },
        //                    new Permission { Name = "AuditLog" },
        //                    new Permission { Name = "SecurityMeasurement" },
        //                    new Permission { Name = "ExternalDoctors" },
        //                    new Permission { Name = "Offices" },
        //                    new Permission { Name = "Hospitals" },
        //                    new Permission { Name = "Reminder" },
        //                    new Permission { Name = "AppointmentTypes" },
        //                    new Permission { Name = "PatientGroups" },
        //                    new Permission { Name = "DuplicatedPatientProcessing" },
        //                    new Permission { Name = "MedicalReports" },
        //                    new Permission { Name = "TestsInternal" },
        //                    new Permission { Name = "ExternalTests" },
        //                    new Permission { Name = "DiagnosticCodesLookUp" },
        //                    new Permission { Name = "BillingCodesLookUp" },
        //                    new Permission { Name = "CerebrumReports" },
        //                    new Permission { Name = "Billing" },
        //                    new Permission { Name = "ScheduleAdmin" },
        //                    new Permission { Name = "WorkingHoursAdmin" },
        //                    new Permission { Name = "Postprocedure" },
        //                    new Permission { Name = "ReportsOnStats" },
        //                    new Permission { Name = "HRMReports" }
        //              }
        //        },
        //        new PermissionType
        //        {
        //            Name = "CanDoTests",
        //            Permissions = new List<Permission> {
        //                    new Permission { Name = "Perform ECHO" },
        //                    new Permission { Name = "Perform SE" },
        //                    new Permission { Name = "Perform SE ECHO" },
        //                    new Permission { Name = "Perform SE GXT" },
        //                    new Permission { Name = "Perform GXT" },
        //                    new Permission { Name = "Perform ECG" },
        //                    new Permission { Name = "Perform Holter" },
        //                    new Permission { Name = "Perform ELR" },
        //                    new Permission { Name = "Perform PFT" },
        //                    new Permission { Name = "Perform NUCLEAR" },
        //                    new Permission { Name = "Perform VASCULAR" },
        //                    new Permission { Name = "Perform GENERAL US" },
        //                    new Permission { Name = "Perform X RAY" },
        //                    new Permission { Name = "Perform BP" },
        //                    new Permission { Name = "Perform SLECG" },
        //                    new Permission { Name = "Perform CVT" }
        //                }
        //        },
        //        new PermissionType
        //        {
        //            Name = "WebBooking",
        //            Permissions = new List<Permission> {
        //                    new Permission { Name = "View Page" }
        //                }
        //        },
        //        new PermissionType
        //        {
        //            Name = "DoctorPermissionTypes",
        //            Permissions = new List<Permission> {
        //                    new Permission { Name = "VPConsulting" }
        //                }
        //        }

        //    };
        //    permissionbase.PermissionTypes.AddRange(permissionTypes);
        //    context.PermissionsBases.AddOrUpdate(p => p.MasterName, permissionbase);

        //    context.SaveChanges();
        //}
        //private void TriageUrgencyInitializer(CerebrumContext c)
        //{
        //    var tiageurgencies = new List<TriageUrgency> {
        //        new TriageUrgency {description="All" },
        //        new TriageUrgency {description="For Triage" },
        //        new TriageUrgency {description="Urgent" },
        //        new TriageUrgency {description="Semi-Urgent" },
        //        new TriageUrgency {description="Can wait" },
        //        new TriageUrgency {description="Do not need to see" },
        //    };
        //    tiageurgencies.ForEach(f => c.TriageUrgencies.AddOrUpdate(a => a.description, f));
        //    c.SaveChanges();
        //}
        //private void AddReportClasses(CerebrumContext context)
        //{
        //    var reportClasses = new List<ReportClass> {
        //        new ReportClass
        //        {
        //            name = "Diagnostic Imaging Report",
        //            ReportSubClass = new List<ReportSubClass>
        //            {
        //                            new ReportSubClass { name = "Misc.X - Ray" },
        //                            new ReportSubClass { name = "Mammogram" },
        //                            new ReportSubClass { name = "Chest X-Ray" },
        //                            new ReportSubClass { name = "Abdomen X-Ray" },
        //                            new ReportSubClass { name = "Lumbar Spine X - Ray" },
        //                            new ReportSubClass { name = "Cervical Spine X - Ray" },
        //                            new ReportSubClass { name = "Upper GI Series" },
        //                            new ReportSubClass { name = "ERCP X-Ray" },
        //                            new ReportSubClass { name = "UGI with Small Bowel" },
        //                            new ReportSubClass { name = "Barium Enema" },
        //                            new ReportSubClass { name = "Myelogram" },
        //                            new ReportSubClass { name = "IVP" },
        //                            new ReportSubClass { name = "Hysterosalpingogram" },
        //                            new ReportSubClass { name = "Coronary Angiography" },
        //                            new ReportSubClass { name = "Carotid Angiography" },
        //                            new ReportSubClass { name = "Other Angiography" },
        //                            new ReportSubClass { name = "Misc. CT Scan" },
        //                            new ReportSubClass { name = "CT Scan Head" },
        //                            new ReportSubClass { name = "CT Scan Body" },
        //                            new ReportSubClass { name = "Misc. MRI Scan" },
        //                            new ReportSubClass { name = "MRI Scan Head" },
        //                            new ReportSubClass { name = "MRI Scan Body" },
        //                            new ReportSubClass { name = "Misc. Ultrasound" },
        //                            new ReportSubClass { name = "Ultrasound Abdomen" },
        //                            new ReportSubClass { name = "Ultrasound Pelvis" },
        //                            new ReportSubClass { name = "Ultrasound Obstetrical" },
        //                            new ReportSubClass { name = "Ultrasound Breast," },
        //                            new ReportSubClass { name = "Ultrasound Thyroid" },
        //                            new ReportSubClass { name = "Venous Doppler Ultrasound" },
        //                            new ReportSubClass { name = "Carotid Doppler Ultrasound" },
        //                            new ReportSubClass { name = "Sonohistogram" },
        //                            new ReportSubClass { name = "Echocardiogram" },
        //                            new ReportSubClass { name = "Misc. Nuclear Scan" },
        //                            new ReportSubClass { name = "Bone Scan" },
        //                            new ReportSubClass { name = "Stress Heart Scan(Thallium" },
        //                            new ReportSubClass { name = "Sestamibi" },
        //                            new ReportSubClass { name = "Myoview)" },
        //                            new ReportSubClass { name = "Brain Scan" },
        //                            new ReportSubClass { name = "Lung Scan" },
        //                            new ReportSubClass { name = "Liver - Spleen Scan" },
        //                            new ReportSubClass { name = "Bone Densitometry" },
        //                            new ReportSubClass { name = "Retinal Tomograph" },
        //                            new ReportSubClass { name = "Retinal Angiography" }
        //                  }
        //        },
        //        new ReportClass {
        //            name = "Diagnostic Test Report",
        //            ReportSubClass=new List<Data.ReportSubClass>
        //            {
        //                 new ReportSubClass { name = "Misc. Diagnostic Test" },
        //                                        new ReportSubClass { name = "Pap Test Report" },
        //                                        new ReportSubClass { name = "Mantoux Test" },
        //                                        new ReportSubClass { name = "ECG" },
        //                                        new ReportSubClass { name = "Stress Test (Exercise" },
        //                                        new ReportSubClass { name = "Persantine" },
        //                                        new ReportSubClass { name = "Dobutamine)" },
        //                                        new ReportSubClass { name = "Holter Monitor" },
        //                                        new ReportSubClass { name = "Loop Recorder" },
        //                                        new ReportSubClass { name = "Ambulatory BP Monitoring" },
        //                                        new ReportSubClass { name = "Arterial Segmental Pressures (ABI)" },
        //                                        new ReportSubClass { name = "Pulmonary Function Testing" },
        //                                        new ReportSubClass { name = "Bronchoscopy" },
        //                                        new ReportSubClass { name = "EEG" },
        //                                        new ReportSubClass { name = "EMG" },
        //                                        new ReportSubClass { name = "Sleep Study" },
        //                                        new ReportSubClass { name = "EGD-oscopy" },
        //                                        new ReportSubClass { name = "Sigmoidoscopy" },
        //                                        new ReportSubClass { name = "Colonoscopy" },
        //                                        new ReportSubClass { name = "Cystoscopy" },
        //                                        new ReportSubClass { name = "Urodynamic Testing" },
        //                                        new ReportSubClass { name = "Colposcopy" },
        //                                        new ReportSubClass { name = "Audiogram" },
        //                                    }
        //        },
        //        new ReportClass
        //        {
        //            name = "Cardio Respiratory Report",
        //            ReportSubClass =new List<ReportSubClass> {
        //                        new ReportSubClass { name = "Echocardiography Bubble Study" },
        //                        new ReportSubClass { name = "Pericardiocentesis" },
        //                        new ReportSubClass { name = "Echocardiography Esophageal" }
        //                        }
        //         }
        //};



        //    var reportSubClass = new List<ReportSubClass>();
        //    Char delimiter = ',';
        //    var valueA = "On-Call Physician, On-Call Nurse, Emergency Physician, Urgent Care/Walk-In Clinic Physician, Hospitalis, Anaesthesiology, Allergy & Immunology, Audiology, Cardiology, Cardiovascular Surgery, Chiropody / Podiatry, Chiropractic, Clinical Biochemistry, Dentistry, Dermatology, Dietitian, Emergency Medicine, Endocrinology, Family Practice, Gastroenterology, General Surgery, Genetics, Geriatrics, Hematology, Infectious Disease, Internal Medicine, Kinesiology, Microbiology, Midwifery, Naturopathy, Neonatology, Nephrology, Neurology, Neurosurgery, Nuclear Medicine, Nursing, Nurse Practitioner, Obstetrics & Gynecology, Occupational Therapy, Oncology / Chemotherapy, Ophthalmology, Optometry, Oral Surgery, Orthopedic Surgery, Osteopathy, Other Therapy, Otolaryngology (ENT), Palliative Care, Pathology, Paediatrics, Pharmacology, Physical Medicine, Physiotherapy, Plastic Surgery, Psychiatry, Psychology, Diagnostic Radiology, Respiratory Technology, Respirology, Rheumatology, Social Work, Speech Therapy, Sports Medicine, Therapeutic Radiology, Thoracic Surgery, Urology, Uro-Gynecology, Vascular Surgery, Other Consultant";
        //    var valueB = "Consultation, Admission History, Operative Report, Discharge Summary, Progress Report, Encounter Report";
        //    foreach (var va in valueA.Split(delimiter))
        //    {
        //        foreach (var vb in valueB.Split(delimiter))
        //        {
        //            reportSubClass.Add(new ReportSubClass { name = va.Trim() + " " + vb.Trim() });
        //        }
        //    }
        //    reportClasses.Add(new ReportClass { name = "Consultant Report", ReportSubClass = reportSubClass });

        //    reportSubClass = new List<ReportSubClass>();
        //    reportClasses.Add(new ReportClass { name = "Lab Report", ReportSubClass = reportSubClass });

        //    reportSubClass = new List<ReportSubClass>();
        //    reportSubClass.Add(new ReportSubClass { name = "etter from Patient" });
        //    reportSubClass.Add(new ReportSubClass { name = "Living Will" });
        //    reportSubClass.Add(new ReportSubClass { name = "Power of Attorney for Health Care" });
        //    reportSubClass.Add(new ReportSubClass { name = "Consent from Patient" });
        //    reportSubClass.Add(new ReportSubClass { name = "Authorization from Patient" });
        //    reportSubClass.Add(new ReportSubClass { name = "Letter from Lawyer" });
        //    reportSubClass.Add(new ReportSubClass { name = "Letter from WSIB" });
        //    reportSubClass.Add(new ReportSubClass { name = "Letter from Insurance Company" });
        //    reportSubClass.Add(new ReportSubClass { name = "Disability Report" });
        //    reportSubClass.Add(new ReportSubClass { name = "Miscellaneous Letter" });
        //    reportClasses.Add(new ReportClass { name = "Other Letter", ReportSubClass = reportSubClass });

        //    reportClasses.ForEach(s => context.ReportClasses.AddOrUpdate(f=>f.name,s));
        //    context.SaveChanges();
        //}
    }
}
