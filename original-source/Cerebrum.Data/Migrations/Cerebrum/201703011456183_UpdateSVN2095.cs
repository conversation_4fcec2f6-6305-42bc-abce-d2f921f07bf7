namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class UpdateSVN2095 : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ReportReceiveds", "sourceAuthorPhysician", c => c.String());
            AddColumn("dbo.Measurements", "OLDID", c => c.Int(nullable: false));
            AddColumn("dbo.MeasurementByPractices", "Visible", c => c.<PERSON>(nullable: false));
            AddColumn("dbo.PatientPrescriptions", "PrescriberOHIP", c => c.String(maxLength: 50));
            DropColumn("dbo.ReportReceiveds", "sourceAuthorPhysicianId");
            DropColumn("dbo.MeasurementByPractices", "Text");
            DropColumn("dbo.MeasurementByPractices", "isCompulsory");
            DropColumn("dbo.MeasurementByPractices", "isVisibleOnWS");
            DropColumn("dbo.MeasurementByPractices", "isVisibleOnReports");
        }
        
        public override void Down()
        {
            AddColumn("dbo.MeasurementByPractices", "isVisibleOnReports", c => c.Boolean());
            AddColumn("dbo.MeasurementByPractices", "isVisibleOnWS", c => c.Boolean());
            AddColumn("dbo.MeasurementByPractices", "isCompulsory", c => c.Boolean());
            AddColumn("dbo.MeasurementByPractices", "Text", c => c.String());
            AddColumn("dbo.ReportReceiveds", "sourceAuthorPhysicianId", c => c.Int());
            DropColumn("dbo.PatientPrescriptions", "PrescriberOHIP");
            DropColumn("dbo.MeasurementByPractices", "Visible");
            DropColumn("dbo.Measurements", "OLDID");
            DropColumn("dbo.ReportReceiveds", "sourceAuthorPhysician");
        }
    }
}
