// <auto-generated />
namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class AppointmentTestResources : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AppointmentTestResources));
        
        string IMigrationMetadata.Id
        {
            get { return "201703011826483_AppointmentTestResources"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
