namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AppointmentTestResources : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.AppointmentTestResources",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        AppointmentTestId = c.Int(nullable: false),
                        UserId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AppointmentTest", t => t.AppointmentTestId, cascadeDelete: true)
                .Index(t => t.AppointmentTestId);
            
            DropColumn("dbo.AppointmentTest", "ApplicationUserId");
        }
        
        public override void Down()
        {
            AddColumn("dbo.AppointmentTest", "ApplicationUserId", c => c.Int(nullable: false));
            DropForeignKey("dbo.AppointmentTestResources", "AppointmentTestId", "dbo.AppointmentTest");
            DropIndex("dbo.AppointmentTestResources", new[] { "AppointmentTestId" });
            DropTable("dbo.AppointmentTestResources");
        }
    }
}
