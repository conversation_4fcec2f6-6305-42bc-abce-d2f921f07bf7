// <auto-generated />
namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.0-30225")]
    public sealed partial class AppointmentMWLStringLenght : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AppointmentMWLStringLenght));
        
        string IMigrationMetadata.Id
        {
            get { return "201702281426024_AppointmentMWLStringLenght"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return Resources.GetString("Source"); }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
