namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class PracticedocUnique : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.PracticeDoctors", "ApplicationUserId", "dbo.AspNetUsers");
            DropIndex("dbo.PracticeDoctors", new[] { "PracticeId" });
            DropIndex("dbo.PracticeDoctors", new[] { "ApplicationUserId" });
            AlterColumn("dbo.PracticeDoctors", "ApplicationUserId", c => c.String(nullable: false, maxLength: 128));
            CreateIndex("dbo.PracticeDoctors", new[] { "ExternalDoctorId", "PracticeId" }, unique: true, name: "IX_ExternalDoctor");
            CreateIndex("dbo.PracticeDoctors", "ApplicationUserId");
            AddForeignKey("dbo.PracticeDoctors", "ApplicationUserId", "dbo.AspNetUsers", "Id", cascadeDelete: true);
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.PracticeDoctors", "ApplicationUserId", "dbo.AspNetUsers");
            DropIndex("dbo.PracticeDoctors", new[] { "ApplicationUserId" });
            DropIndex("dbo.PracticeDoctors", "IX_ExternalDoctor");
            AlterColumn("dbo.PracticeDoctors", "ApplicationUserId", c => c.String(maxLength: 128));
            CreateIndex("dbo.PracticeDoctors", "ApplicationUserId");
            CreateIndex("dbo.PracticeDoctors", "PracticeId");
            AddForeignKey("dbo.PracticeDoctors", "ApplicationUserId", "dbo.AspNetUsers", "Id");
        }
    }
}
