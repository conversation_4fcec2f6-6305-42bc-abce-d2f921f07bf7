// <auto-generated />
namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.0-30225")]
    public sealed partial class AddedNewCol2BillAndMesurement : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddedNewCol2BillAndMesurement));
        
        string IMigrationMetadata.Id
        {
            get { return "201702271504550_AddedNewCol2BillAndMesurement"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
