namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AppointmentTypeColumnChange : DbMigration
    {
        public override void Up()
        {
            RenameColumn(table: "dbo.AppointmentTypes", name: "appointmentType_Id", newName: "AppointmentTypeId");
            RenameIndex(table: "dbo.AppointmentTypes", name: "IX_appointmentType_Id", newName: "IX_AppointmentTypeId");
            DropColumn("dbo.AppointmentTypes", "parentAppointmentTypeId");
        }
        
        public override void Down()
        {
            AddColumn("dbo.AppointmentTypes", "parentAppointmentTypeId", c => c.Int());
            RenameIndex(table: "dbo.AppointmentTypes", name: "IX_AppointmentTypeId", newName: "IX_appointmentType_Id");
            RenameColumn(table: "dbo.AppointmentTypes", name: "AppointmentTypeId", newName: "appointmentType_Id");
        }
    }
}
