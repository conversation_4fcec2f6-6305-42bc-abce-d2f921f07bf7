namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class Initial : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.AllergyStatuses",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c<PERSON>(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.AppointmentBills",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        AppointmentID = c.Int(nullable: false),
                        ConsultCode = c.Int(nullable: false),
                        DiagnosticCode = c.Int(nullable: false),
                        DiagnosticCode2 = c.Int(nullable: false),
                        DiagnosticCode3 = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.AppointmentPreconditons",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Type = c.String(),
                        Status = c.<PERSON>(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Appointments", t => t.AppointmentId, cascadeDelete: true)
                .Index(t => t.AppointmentId);
            
            CreateTable(
                "dbo.Appointments",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OfficeId = c.Int(nullable: false),
                        appointmentTime = c.DateTime(nullable: false),
                        ArrivedTime = c.String(),
                        LeftTime = c.String(),
                        appointmentPurpose = c.String(),
                        appointmentStatus = c.Int(nullable: false),
                        appointmentNotes = c.String(),
                        appointmentRegistrar = c.Int(nullable: false),
                        MWLUrl = c.String(),
                        MWLSentFlag = c.Boolean(nullable: false),
                        actionOnAbnormal = c.Boolean(nullable: false),
                        bookingConfirmation = c.Boolean(nullable: false),
                        roomNumber = c.String(),
                        PracticeDoctorId = c.Int(nullable: false),
                        billStatusId = c.Int(),
                        referralDoctorId = c.Int(nullable: false),
                        AppointmentTypeId = c.Int(nullable: false),
                        appointmentConfirmation = c.Int(nullable: false),
                        appointmentPaymentMethod = c.Int(nullable: false),
                        createdOn = c.DateTime(nullable: false),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.AppointmntModifiers",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        userName = c.String(),
                        reasonforChange = c.String(),
                        oldAppointmentDateTime = c.DateTime(),
                        newAppointmentDateTime = c.DateTime(),
                        officeId = c.Int(),
                        appointmentProviderId = c.Int(nullable: false),
                        appointmentReferralDoctorId = c.Int(nullable: false),
                        createDate = c.DateTime(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Appointments", t => t.AppointmentId, cascadeDelete: true)
                .Index(t => t.AppointmentId);
            
            CreateTable(
                "dbo.AppointmentProviders",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ExternalDoctorId = c.Int(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                        DateCreated = c.DateTime(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Appointments", t => t.AppointmentId, cascadeDelete: true)
                .Index(t => t.AppointmentId);
            
            CreateTable(
                "dbo.AppointmentTest",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        TestId = c.Int(nullable: false),
                        startTime = c.DateTime(nullable: false),
                        AppointmentTestStatusId = c.Int(nullable: false),
                        testDuration = c.Int(nullable: false),
                        isBilled = c.Boolean(nullable: false),
                        referralDoctorId = c.Int(nullable: false),
                        ApplicationUserId = c.Int(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                        AccessionNumber = c.String(maxLength: 50),
                        PhysicianComments = c.String(),
                        TechnicianComments = c.String(),
                        IsActive = c.Boolean(nullable: false),
                        DateCreated = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Appointments", t => t.AppointmentId, cascadeDelete: true)
                .ForeignKey("dbo.AppointmentTestStatus", t => t.AppointmentTestStatusId, cascadeDelete: true)
                .Index(t => t.AppointmentTestStatusId)
                .Index(t => t.AppointmentId);
            
            CreateTable(
                "dbo.AppointmentTestStatus",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Color = c.String(),
                        Status = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.TestReportingDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ExternalDoctorId = c.Int(nullable: false),
                        AppointmentTestId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AppointmentTest", t => t.AppointmentTestId, cascadeDelete: true)
                .Index(t => t.AppointmentTestId);
            
            CreateTable(
                "dbo.PatientRecords",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PracticeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Practices", t => t.PracticeId, cascadeDelete: true)
                .Index(t => t.PracticeId);
            
            CreateTable(
                "dbo.Demographics",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        namePrefix = c.Int(nullable: false),
                        firstName = c.String(),
                        middleName = c.String(),
                        lastName = c.String(),
                        useAliases = c.Boolean(nullable: false),
                        aliasFirstName = c.String(),
                        aliasMiddleName = c.String(),
                        aliasLastName = c.String(),
                        dateOfBirth = c.DateTime(storeType: "date"),
                        SIN = c.String(),
                        chartNumber = c.String(),
                        pharmacyFaxNumber = c.String(),
                        pictureURL = c.String(),
                        email = c.String(),
                        consentEmail = c.Boolean(nullable: false),
                        password = c.String(),
                        gender = c.Int(nullable: false),
                        officialSpokenLanguage = c.Int(nullable: false),
                        uniqueVendorIdSequence = c.String(),
                        preferredOfficialLanguageSpecified = c.Boolean(nullable: false),
                        preferredSpokenLanguage = c.String(),
                        notesAboutPatient = c.String(),
                        personStatusCode = c.Int(nullable: false),
                        personStatusDate = c.DateTime(),
                        personStatusDateSpecified = c.Boolean(nullable: false),
                        HospitalCode = c.String(),
                        InsuranceCompanyId = c.Int(nullable: false),
                        insuranceType = c.Int(nullable: false),
                        defaultPaymentMethod = c.Int(nullable: false),
                        active = c.Int(nullable: false),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.DemographicsAddresses",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        addressLine1 = c.String(),
                        addressLine2 = c.String(),
                        city = c.String(),
                        postalCode = c.String(),
                        postalCodeType = c.Int(nullable: false),
                        province = c.String(),
                        country = c.String(),
                        addressType = c.Int(nullable: false),
                        DemographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Demographics", t => t.DemographicId, cascadeDelete: true)
                .Index(t => t.DemographicId);
            
            CreateTable(
                "dbo.DemographicsAssociatedDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ExternalDoctorId = c.Int(nullable: false),
                        authorizedBy = c.String(),
                        authorizedDate = c.DateTime(),
                        removedBy = c.String(),
                        removedDate = c.DateTime(),
                        DemographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Demographics", t => t.DemographicId, cascadeDelete: true)
                .Index(t => t.DemographicId);
            
            CreateTable(
                "dbo.DemographicsDefaultReferralDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ExternalDoctorId = c.Int(nullable: false),
                        enrolled = c.Boolean(nullable: false),
                        enrollmentStatusSpecified = c.Boolean(nullable: false),
                        enrollmentDate = c.DateTime(),
                        enrollmentDateSpecified = c.Boolean(nullable: false),
                        enrollmentTerminationDate = c.DateTime(),
                        enrollmentTerminationDateSpecified = c.Boolean(nullable: false),
                        terminationReason = c.Int(nullable: false),
                        terminationReasonSpecified = c.Boolean(nullable: false),
                        DemographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Demographics", t => t.DemographicId, cascadeDelete: true)
                .Index(t => t.DemographicId);
            
            CreateTable(
                "dbo.DemographicsNextOfKins",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        contactPurpose = c.String(),
                        firstName = c.String(),
                        middleName = c.String(),
                        lastName = c.String(),
                        emailAddress = c.String(),
                        notes = c.String(),
                        DemographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Demographics", t => t.DemographicId, cascadeDelete: true)
                .Index(t => t.DemographicId);
            
            CreateTable(
                "dbo.DemographicsContactPhoneNumbers",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        contactPhoneNumber = c.String(),
                        DemographicsContactId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.DemographicsNextOfKins", t => t.DemographicsContactId, cascadeDelete: true)
                .Index(t => t.DemographicsContactId);
            
            CreateTable(
                "dbo.DemographicsFamilyDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ExternalDoctorId = c.Int(nullable: false),
                        enrolled = c.Boolean(nullable: false),
                        enrollmentStatusSpecified = c.Boolean(nullable: false),
                        enrollmentDate = c.DateTime(),
                        enrollmentDateSpecified = c.Boolean(nullable: false),
                        enrollmentTerminationDate = c.DateTime(),
                        enrollmentTerminationDateSpecified = c.Boolean(nullable: false),
                        terminationReason = c.Int(nullable: false),
                        terminationReasonSpecified = c.Boolean(nullable: false),
                        DemographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Demographics", t => t.DemographicId, cascadeDelete: true)
                .Index(t => t.DemographicId);
            
            CreateTable(
                "dbo.DemographicsHealthCards",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        number = c.String(),
                        version = c.String(),
                        expirydate = c.DateTime(),
                        dateIssued = c.DateTime(),
                        lastValidated = c.DateTime(),
                        provinceCode = c.Int(nullable: false),
                        DemographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Demographics", t => t.DemographicId, cascadeDelete: true)
                .Index(t => t.DemographicId);
            
            CreateTable(
                "dbo.DemographicsMainResponsiblePhysicians",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PracticeDoctorId = c.Int(nullable: false),
                        ExternalDoctorId = c.Int(nullable: false),
                        DemographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Demographics", t => t.DemographicId, cascadeDelete: true)
                .Index(t => t.DemographicId);
            
            CreateTable(
                "dbo.DemographicsEnrollments",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        enrolled = c.Boolean(nullable: false),
                        enrollmentStatus = c.Int(nullable: false),
                        enrollmentStatusSpecified = c.Boolean(nullable: false),
                        enrollmentDate = c.DateTime(),
                        enrollmentDateSpecified = c.Boolean(nullable: false),
                        enrollmentTerminationDate = c.DateTime(),
                        enrollmentTerminationDateSpecified = c.Boolean(nullable: false),
                        terminationReason = c.Int(nullable: false),
                        terminationReasonSpecified = c.Boolean(nullable: false),
                        DemographicsMRPId = c.Int(nullable: false),
                        DemographicsMainResponsiblePhysician_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.DemographicsMainResponsiblePhysicians", t => t.DemographicsMainResponsiblePhysician_Id)
                .Index(t => t.DemographicsMainResponsiblePhysician_Id);
            
            CreateTable(
                "dbo.DemographicsPhoneNumbers",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        phoneNumber = c.String(),
                        extention = c.String(),
                        faxNumber = c.String(),
                        typeOfPhoneNumber = c.Int(nullable: false),
                        DemographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Demographics", t => t.DemographicId, cascadeDelete: true)
                .Index(t => t.DemographicId);
            
            CreateTable(
                "dbo.FamilyHistories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categorySummaryLine = c.String(),
                        ageAtOnset = c.String(),
                        startDate = c.DateTime(),
                        lifeStage = c.Int(nullable: false),
                        lifeStageSpecified = c.Boolean(nullable: false),
                        problemDiagnosisProcedureDescription = c.String(),
                        treatment = c.String(),
                        relationship = c.String(),
                        note = c.String(),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.FamilyHistoryResidualInfoes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        dataType = c.String(),
                        content = c.String(),
                        FamilyHistoryId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.FamilyHistories", t => t.FamilyHistoryId, cascadeDelete: true)
                .Index(t => t.FamilyHistoryId);
            
            CreateTable(
                "dbo.HL7Patient",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        healthcCardNo = c.String(maxLength: 20),
                        versionNo = c.String(maxLength: 10, fixedLength: true, unicode: false),
                        provinceCode = c.String(maxLength: 2, fixedLength: true, unicode: false),
                        externalID = c.String(maxLength: 50),
                        internalID = c.String(maxLength: 50),
                        familyName = c.String(maxLength: 500),
                        firstName = c.String(maxLength: 500),
                        middleName = c.String(maxLength: 500),
                        DOB = c.DateTime(),
                        sex = c.String(maxLength: 1, fixedLength: true, unicode: false),
                        address1 = c.String(maxLength: 500),
                        address2 = c.String(maxLength: 500),
                        city = c.String(maxLength: 500),
                        province = c.String(),
                        postalCode = c.String(maxLength: 7),
                        phoneNo = c.String(maxLength: 20),
                        createdDate = c.DateTime(nullable: false),
                        HL7MessageId = c.Int(nullable: false),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.HL7Message", t => t.HL7MessageId, cascadeDelete: true)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .Index(t => t.HL7MessageId)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.HL7Message",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        sendingApp = c.String(maxLength: 300),
                        messageType = c.String(maxLength: 100),
                        messageDate = c.DateTime(),
                        MSGID = c.Guid(nullable: false),
                        rawMessage = c.Binary(),
                        createdDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.HL7ReportVersion",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        orderControl = c.String(maxLength: 10, fixedLength: true, unicode: false),
                        testStatus = c.String(maxLength: 4, fixedLength: true, unicode: false),
                        consentBlock = c.Int(nullable: false),
                        priority = c.String(maxLength: 2, fixedLength: true, unicode: false),
                        requestedDateTime = c.DateTime(),
                        physicianNo = c.String(maxLength: 50),
                        physicianName = c.String(maxLength: 500),
                        resultedDateTime = c.DateTime(),
                        status = c.String(),
                        xmlReport = c.String(),
                        createdDate = c.DateTime(nullable: false),
                        HL7MessageId = c.Int(),
                        HL7ReportId = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.HL7Message", t => t.HL7MessageId)
                .ForeignKey("dbo.HL7Report", t => t.HL7ReportId)
                .Index(t => t.HL7MessageId)
                .Index(t => t.HL7ReportId);
            
            CreateTable(
                "dbo.HL7Report",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        accessionNumber = c.String(maxLength: 50),
                        priority = c.String(maxLength: 2, fixedLength: true, unicode: false),
                        requestedDateTime = c.DateTime(),
                        collectionDateTime = c.DateTime(),
                        physicianNo = c.String(maxLength: 50),
                        physicianName = c.String(maxLength: 100),
                        resultedDateTime = c.DateTime(),
                        status = c.String(),
                        testLocationId = c.String(maxLength: 200),
                        testLocationDescription = c.String(maxLength: 500),
                        testLocationCoding = c.String(maxLength: 200),
                        createdDate = c.DateTime(nullable: false),
                        HL7PatientId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.HL7Patient", t => t.HL7PatientId, cascadeDelete: true)
                .Index(t => t.HL7PatientId);
            
            CreateTable(
                "dbo.HL7ReportNote",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        setId = c.Int(nullable: false),
                        comments = c.String(),
                        HL7ReportVersionId = c.Int(nullable: false),
                        createdDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.HL7ReportVersion", t => t.HL7ReportVersionId, cascadeDelete: true)
                .Index(t => t.HL7ReportVersionId);
            
            CreateTable(
                "dbo.HL7Result",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        setId = c.Int(nullable: false),
                        valueType = c.String(maxLength: 10, fixedLength: true, unicode: false),
                        observationSubId = c.String(maxLength: 200),
                        testCodeIdentifier = c.String(maxLength: 200),
                        codingSystem = c.String(maxLength: 200),
                        testDescription = c.String(),
                        testResult = c.String(),
                        testResultFloat = c.Decimal(nullable: false, precision: 18, scale: 2),
                        units = c.String(maxLength: 100),
                        range1 = c.Decimal(nullable: false, precision: 18, scale: 2),
                        range2 = c.Decimal(nullable: false, precision: 18, scale: 2),
                        structureRefRange = c.String(),
                        formattedRefRange = c.String(),
                        abnormalFlag = c.String(maxLength: 10, fixedLength: true, unicode: false),
                        resultStatus = c.String(maxLength: 2, fixedLength: true, unicode: false),
                        reportCode = c.String(maxLength: 200),
                        reportFormID = c.String(maxLength: 200),
                        reportGroupHead = c.String(maxLength: 300),
                        reportGroupDescription = c.String(maxLength: 200),
                        reportGroupID = c.Int(),
                        groupSortCode = c.Int(),
                        showResult = c.Boolean(nullable: false),
                        collectionDate = c.DateTime(),
                        createdDate = c.DateTime(nullable: false),
                        HL7ReportVersionId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.HL7ReportVersion", t => t.HL7ReportVersionId, cascadeDelete: true)
                .Index(t => t.HL7ReportVersionId);
            
            CreateTable(
                "dbo.HL7ResultNote",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        setId = c.Int(),
                        comments = c.String(),
                        HL7ResultId = c.Int(nullable: false),
                        createdDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.HL7Result", t => t.HL7ResultId, cascadeDelete: true)
                .Index(t => t.HL7ResultId);
            
            CreateTable(
                "dbo.PastHealths",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categorySummaryLine = c.String(),
                        pastHealthProblemDescriptionOrProcedures = c.String(),
                        startDate = c.DateTime(),
                        lifeStage = c.Int(nullable: false),
                        lifeStageSpecified = c.Boolean(nullable: false),
                        resolvedDate = c.DateTime(),
                        procedureDate = c.DateTime(),
                        note = c.String(),
                        problemStatus = c.String(),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.PastHealthResidualInfoes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        dataType = c.String(),
                        content = c.String(),
                        PastHealthId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PastHealths", t => t.PastHealthId, cascadeDelete: true)
                .Index(t => t.PastHealthId);
            
            CreateTable(
                "dbo.PatientMRNs",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        HospitalCode = c.Int(nullable: false),
                        MedicalRecordNumber = c.Int(nullable: false),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.PersonalHistories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categorySummaryLine = c.String(),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.PersonalHistoryResidualInfo",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        dataType = c.String(),
                        content = c.String(),
                        PersonalHistoryId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PersonalHistories", t => t.PersonalHistoryId, cascadeDelete: true)
                .Index(t => t.PersonalHistoryId);
            
            CreateTable(
                "dbo.Practices",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PracticeName = c.String(),
                        PracticeNumber = c.Short(nullable: false),
                        MasterId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Masters", t => t.MasterId, cascadeDelete: true)
                .Index(t => t.MasterId);
            
            CreateTable(
                "dbo.Masters",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        MasterName = c.String(),
                        GenerationDate = c.DateTime(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ExternalDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Initials = c.String(maxLength: 10),
                        Team = c.Int(nullable: false),
                        BlueCross = c.String(),
                        RegionalCode = c.String(),
                        LetterHead = c.String(),
                        Degrees = c.String(),
                        SpecialtyId = c.Int(nullable: false),
                        Cell = c.String(),
                        MCEDTMailbox = c.String(),
                        MCEDTPassword = c.String(),
                        MCEDTId = c.String(),
                        HospBilling = c.Int(nullable: false),
                        DiagnosticSet = c.Int(nullable: false),
                        LetterTemplate = c.Int(nullable: false),
                        ReportTemplate = c.Int(nullable: false),
                        OHIPPhysicianId = c.String(),
                        firstName = c.String(maxLength: 50),
                        middleName = c.String(maxLength: 50),
                        lastName = c.String(maxLength: 50),
                        CPSO = c.String(),
                        HRMId = c.String(),
                        description = c.String(),
                        comment = c.String(),
                        emailAddress = c.String(),
                        locked = c.Boolean(nullable: false),
                        active = c.Boolean(nullable: false),
                        fax = c.Boolean(nullable: false),
                        mail = c.Boolean(nullable: false),
                        email = c.Boolean(nullable: false),
                        byphone = c.Boolean(nullable: false),
                        HRM = c.Boolean(nullable: false),
                        password = c.String(),
                        MasterId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Masters", t => t.MasterId, cascadeDelete: true)
                .Index(t => t.MasterId);
            
            CreateTable(
                "dbo.ExternalDoctorAddresses",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        addressLine1 = c.String(),
                        addressLine2 = c.String(),
                        faxNumber = c.String(),
                        city = c.String(),
                        postalCode = c.String(),
                        province = c.String(),
                        country = c.String(),
                        addressType = c.Int(nullable: false),
                        ExternalDoctorId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ExternalDoctors", t => t.ExternalDoctorId, cascadeDelete: true)
                .Index(t => t.ExternalDoctorId);
            
            CreateTable(
                "dbo.ExternalDoctorPhoneNumbers",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        phoneNumber = c.String(),
                        extention = c.String(),
                        faxNumber = c.String(),
                        typeOfPhoneNumber = c.Int(nullable: false),
                        ExternalDoctorId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ExternalDoctors", t => t.ExternalDoctorId, cascadeDelete: true)
                .Index(t => t.ExternalDoctorId);
            
            CreateTable(
                "dbo.InsuranceCompanies",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        phoneNumber = c.String(),
                        extention = c.String(),
                        faxNumber = c.String(),
                        address = c.String(),
                        MasterId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Masters", t => t.MasterId, cascadeDelete: true)
                .Index(t => t.MasterId);
            
            CreateTable(
                "dbo.PermissionsBases",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        MasterName = c.String(),
                        MasterId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Masters", t => t.MasterId, cascadeDelete: true)
                .Index(t => t.MasterId);
            
            CreateTable(
                "dbo.PermissionTypes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        PermissionsBaseId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PermissionsBases", t => t.PermissionsBaseId, cascadeDelete: true)
                .Index(t => t.PermissionsBaseId);
            
            CreateTable(
                "dbo.Permissions",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        crtype = c.Int(nullable: false),
                        PermissionTypeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PermissionTypes", t => t.PermissionTypeId, cascadeDelete: true)
                .Index(t => t.PermissionTypeId);
            
            CreateTable(
                "dbo.Specialties",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        title = c.String(),
                        code = c.Short(nullable: false),
                        MasterId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Masters", t => t.MasterId, cascadeDelete: true)
                .Index(t => t.MasterId);
            
            CreateTable(
                "dbo.Office",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        businessName = c.String(),
                        address1 = c.String(),
                        address2 = c.String(),
                        city = c.String(),
                        phone = c.String(),
                        fax = c.String(),
                        faxUrl = c.String(),
                        state = c.String(),
                        zip = c.String(),
                        country = c.String(),
                        url = c.String(),
                        physicalPath = c.String(),
                        regionalCode = c.String(),
                        externalName = c.String(),
                        locCode = c.Int(nullable: false),
                        hospitalCode = c.String(),
                        HRM_id = c.String(),
                        province = c.Int(nullable: false),
                        postalCode = c.String(),
                        imgStorageUrl = c.String(),
                        docStorageUrl = c.String(),
                        watingList = c.Int(nullable: false),
                        status = c.Int(nullable: false),
                        BillGrNum_Consulting = c.String(),
                        BillGrNum_Tech = c.String(),
                        BillGrNum_Prof = c.String(),
                        BillGrNum_IHF = c.String(),
                        OfficeSpeciality = c.Int(nullable: false),
                        PracticeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Practices", t => t.PracticeId, cascadeDelete: true)
                .Index(t => t.PracticeId);
            
            CreateTable(
                "dbo.OfficeGroupBillingNumbers",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        serviceLocatorIndicator = c.Int(nullable: false),
                        OfficeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Office", t => t.OfficeId, cascadeDelete: true)
                .Index(t => t.OfficeId);
            
            CreateTable(
                "dbo.OfficeOutlooks",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        leftLogo = c.Binary(),
                        rightLogo = c.Binary(),
                        middleLogo = c.Binary(),
                        fontType = c.Int(nullable: false),
                        fontSize = c.Int(nullable: false),
                        fontHeadSize = c.Int(nullable: false),
                        fontWeight = c.Int(nullable: false),
                        logoLocation = c.Int(nullable: false),
                        showRefDoc = c.Boolean(nullable: false),
                        isAddressBold = c.Boolean(nullable: false),
                        impressionLocation = c.Int(nullable: false),
                        impressionBorder = c.Boolean(nullable: false),
                        isTable = c.Boolean(nullable: false),
                        isWebsite = c.Boolean(nullable: false),
                        siteUrl = c.String(),
                        isSex = c.Boolean(nullable: false),
                        isOfficeNameVisible = c.Boolean(nullable: false),
                        OfficeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Office", t => t.OfficeId, cascadeDelete: true)
                .Index(t => t.OfficeId);
            
            CreateTable(
                "dbo.StaticIPs",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        IPAddress = c.String(),
                        Note = c.String(),
                        OfficeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Office", t => t.OfficeId, cascadeDelete: true)
                .Index(t => t.OfficeId);
            
            CreateTable(
                "dbo.PracticeDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ExternalDoctorId = c.Int(nullable: false),
                        mainOfficeId = c.Int(nullable: false),
                        doubleBookingAllowed = c.Boolean(nullable: false),
                        mc_un = c.String(),
                        mc_pwd = c.String(),
                        diagnosisVPset = c.Int(nullable: false),
                        specialization = c.String(),
                        PracticeId = c.Int(nullable: false),
                        ApplicationUserId = c.String(maxLength: 128),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AspNetUsers", t => t.ApplicationUserId)
                .ForeignKey("dbo.Practices", t => t.PracticeId, cascadeDelete: true)
                .Index(t => t.PracticeId)
                .Index(t => t.ApplicationUserId);
            
            CreateTable(
                "dbo.AspNetUsers",
                c => new
                    {
                        Id = c.String(nullable: false, maxLength: 128),
                        PracticeID = c.Int(nullable: false),
                        UserID = c.Int(nullable: false, identity: true),
                        Salut = c.Int(nullable: false),
                        Status = c.Int(nullable: false),
                        FirstName = c.String(),
                        MiddleName = c.String(),
                        LastName = c.String(),
                        Address = c.String(),
                        City = c.String(),
                        Province = c.String(),
                        PostalCode = c.String(),
                        CellPhone = c.String(nullable: false),
                        UserCountry = c.Int(nullable: false),
                        CerebrumUserType = c.Int(nullable: false),
                        UserLoginPersistance = c.Int(nullable: false),
                        Email = c.String(maxLength: 256),
                        EmailConfirmed = c.Boolean(nullable: false),
                        PasswordHash = c.String(),
                        SecurityStamp = c.String(),
                        PhoneNumber = c.String(),
                        PhoneNumberConfirmed = c.Boolean(nullable: false),
                        TwoFactorEnabled = c.Boolean(nullable: false),
                        LockoutEndDateUtc = c.DateTime(),
                        LockoutEnabled = c.Boolean(nullable: false),
                        AccessFailedCount = c.Int(nullable: false),
                        UserName = c.String(nullable: false, maxLength: 256),
                    })
                .PrimaryKey(t => t.Id)
                .Index(t => t.UserName, unique: true, name: "UserNameIndex");
            
            CreateTable(
                "dbo.AspNetUserClaims",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        UserId = c.String(nullable: false, maxLength: 128),
                        ClaimType = c.String(),
                        ClaimValue = c.String(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AspNetUsers", t => t.UserId, cascadeDelete: true)
                .Index(t => t.UserId);
            
            CreateTable(
                "dbo.AspNetUserLogins",
                c => new
                    {
                        LoginProvider = c.String(nullable: false, maxLength: 128),
                        ProviderKey = c.String(nullable: false, maxLength: 128),
                        UserId = c.String(nullable: false, maxLength: 128),
                    })
                .PrimaryKey(t => new { t.LoginProvider, t.ProviderKey, t.UserId })
                .ForeignKey("dbo.AspNetUsers", t => t.UserId, cascadeDelete: true)
                .Index(t => t.UserId);
            
            CreateTable(
                "dbo.AspNetUserRoles",
                c => new
                    {
                        UserId = c.String(nullable: false, maxLength: 128),
                        RoleId = c.String(nullable: false, maxLength: 128),
                    })
                .PrimaryKey(t => new { t.UserId, t.RoleId })
                .ForeignKey("dbo.AspNetUsers", t => t.UserId, cascadeDelete: true)
                .ForeignKey("dbo.AspNetRoles", t => t.RoleId, cascadeDelete: true)
                .Index(t => t.UserId)
                .Index(t => t.RoleId);
            
            CreateTable(
                "dbo.PracticeDoctorAppointmentType",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        duration = c.Int(nullable: false),
                        PracticeDoctorId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PracticeDoctors", t => t.PracticeDoctorId, cascadeDelete: true)
                .Index(t => t.PracticeDoctorId);
            
            CreateTable(
                "dbo.ReportPhraseByDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DrID = c.Int(nullable: false),
                        Text = c.String(),
                        ReportPhraseID = c.Int(nullable: false),
                        PracticeDoctor_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PracticeDoctors", t => t.PracticeDoctor_Id)
                .ForeignKey("dbo.ReportPhrases", t => t.ReportPhraseID, cascadeDelete: true)
                .Index(t => t.ReportPhraseID)
                .Index(t => t.PracticeDoctor_Id);
            
            CreateTable(
                "dbo.PracticeSpecialty",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        SpecialtyId = c.Int(nullable: false),
                        PracticeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Practices", t => t.PracticeId, cascadeDelete: true)
                .Index(t => t.PracticeId);
            
            CreateTable(
                "dbo.VPRootCategories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        order = c.Short(nullable: false),
                        SpecialtyId = c.Int(nullable: false),
                        PracticeSpecialty_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Specialties", t => t.SpecialtyId, cascadeDelete: true)
                .ForeignKey("dbo.PracticeSpecialty", t => t.PracticeSpecialty_Id)
                .Index(t => t.SpecialtyId)
                .Index(t => t.PracticeSpecialty_Id);
            
            CreateTable(
                "dbo.VPItems",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        VPRootCategoryId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.VPRootCategories", t => t.VPRootCategoryId, cascadeDelete: true)
                .Index(t => t.VPRootCategoryId);
            
            CreateTable(
                "dbo.WSRootCategories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        order = c.Short(nullable: false),
                        SpecialtyId = c.Int(nullable: false),
                        PracticeSpecialty_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Specialties", t => t.SpecialtyId, cascadeDelete: true)
                .ForeignKey("dbo.PracticeSpecialty", t => t.PracticeSpecialty_Id)
                .Index(t => t.SpecialtyId)
                .Index(t => t.PracticeSpecialty_Id);
            
            CreateTable(
                "dbo.WSItems",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        WSRootCategoryId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.WSRootCategories", t => t.WSRootCategoryId, cascadeDelete: true)
                .Index(t => t.WSRootCategoryId);
            
            CreateTable(
                "dbo.PracticeTests",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        TestInstruction = c.String(),
                        testDuration = c.Int(nullable: false),
                        createdDate = c.DateTime(nullable: false),
                        updatedDate = c.DateTime(nullable: false),
                        isActive = c.Boolean(nullable: false),
                        TestId = c.Int(nullable: false),
                        PracticeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Practices", t => t.PracticeId, cascadeDelete: true)
                .ForeignKey("dbo.Tests", t => t.TestId, cascadeDelete: true)
                .Index(t => t.TestId)
                .Index(t => t.PracticeId);
            
            CreateTable(
                "dbo.Tests",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OLDID = c.Int(nullable: false),
                        color = c.String(maxLength: 40),
                        testFullName = c.String(),
                        testShortName = c.String(),
                        order = c.Int(nullable: false),
                        duration = c.Int(nullable: false),
                        IsRadiology = c.Boolean(nullable: false),
                        modality = c.Int(nullable: false),
                        startTime = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.TestResources",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        permissionId = c.Int(nullable: false),
                        nurseRequired = c.Boolean(nullable: false),
                        doctorRequiredInOffice = c.Boolean(nullable: false),
                        isPerformedInGroup = c.Boolean(nullable: false),
                        TestResourceTypeId = c.Int(nullable: false),
                        TestId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Permissions", t => t.permissionId, cascadeDelete: true)
                .ForeignKey("dbo.Tests", t => t.TestId, cascadeDelete: true)
                .ForeignKey("dbo.TestResourceTypes", t => t.TestResourceTypeId, cascadeDelete: true)
                .Index(t => t.permissionId)
                .Index(t => t.TestResourceTypeId)
                .Index(t => t.TestId);
            
            CreateTable(
                "dbo.TestResourceTypes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        TestResourceName = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ProblemLists",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categorySummaryLine = c.String(),
                        problemDiagnosisDescription = c.String(),
                        problemDescription = c.String(),
                        problemStatus = c.String(),
                        onsetDate = c.DateTime(),
                        lifeStage = c.Int(nullable: false),
                        lifeStageSpecified = c.Boolean(nullable: false),
                        resolutionDate = c.DateTime(),
                        notes = c.String(),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.ProblemListResidualInfoes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        dataType = c.String(),
                        content = c.String(),
                        ProblemListId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ProblemLists", t => t.ProblemListId, cascadeDelete: true)
                .Index(t => t.ProblemListId);
            
            CreateTable(
                "dbo.ReportReceiveds",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        testDate = c.DateTime(nullable: false),
                        fileSize = c.Int(),
                        fileType = c.String(maxLength: 50),
                        fileName = c.String(maxLength: 250),
                        userIp = c.String(maxLength: 20),
                        officeId = c.Int(nullable: false),
                        looseReportCategoryId = c.Int(nullable: false),
                        abnormal = c.Boolean(),
                        url = c.String(maxLength: 512),
                        markSeen = c.Boolean(nullable: false),
                        seenDateTime = c.DateTime(),
                        userId = c.String(maxLength: 128),
                        media = c.Int(),
                        mediaSpecified = c.Boolean(),
                        reportFormat = c.Int(),
                        fileExtensionAndVersion = c.String(),
                        eventDateTime = c.DateTime(),
                        receivedDateTime = c.DateTime(),
                        sourceAuthorPhysicianId = c.Int(),
                        sourceFacility = c.String(),
                        sendingFacilityId = c.String(),
                        sendingFacilityReport = c.String(),
                        hRMResultStatus = c.String(),
                        messageUniqueID = c.String(),
                        description = c.String(),
                        reportClassId = c.Int(nullable: false),
                        reportSubClassId = c.Int(),
                        appointmentId = c.Int(),
                        testId = c.Int(),
                        PatientRecordId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Appointments", t => t.appointmentId)
                .ForeignKey("dbo.PatientRecords", t => t.PatientRecordId, cascadeDelete: true)
                .ForeignKey("dbo.ReportClasses", t => t.reportClassId, cascadeDelete: true)
                .ForeignKey("dbo.ReportSubClasses", t => t.reportSubClassId)
                .Index(t => t.reportClassId)
                .Index(t => t.reportSubClassId)
                .Index(t => t.appointmentId)
                .Index(t => t.PatientRecordId);
            
            CreateTable(
                "dbo.DoctorsReportRevieweds",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        practiceDoctorId = c.Int(nullable: false),
                        dateTimeReportReviewed = c.DateTime(),
                        ReportReviewedNotes = c.String(),
                        ReportReceivedId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ReportReceiveds", t => t.ReportReceivedId, cascadeDelete: true)
                .Index(t => t.ReportReceivedId);
            
            CreateTable(
                "dbo.ReportClasses",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ReportSubClasses",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        ReportClassId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ReportClasses", t => t.ReportClassId, cascadeDelete: true)
                .Index(t => t.ReportClassId);
            
            CreateTable(
                "dbo.AppointmentStatusLogs",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        userName = c.String(),
                        changedDateTime = c.DateTime(nullable: false),
                        appointmentStatus = c.Int(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Appointments", t => t.AppointmentId, cascadeDelete: true)
                .Index(t => t.AppointmentId);
            
            CreateTable(
                "dbo.AppointmentTestLogs",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Date = c.DateTime(),
                        Status = c.Int(nullable: false),
                        AppointmentID = c.Int(nullable: false),
                        TestID = c.Int(nullable: false),
                        IP = c.String(),
                        UserID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.MeasurementSavedValues",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Value = c.String(),
                        isCalculated = c.Boolean(nullable: false),
                        AppointmentID = c.Int(nullable: false),
                        TestID = c.Int(nullable: false),
                        AppointmentTestLogID = c.Int(nullable: false),
                        MeasurementId = c.Int(nullable: false),
                        MeasurementOperatorId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Measurements", t => t.MeasurementId, cascadeDelete: true)
                .ForeignKey("dbo.MeasurementOperators", t => t.MeasurementOperatorId, cascadeDelete: true)
                .ForeignKey("dbo.AppointmentTestLogs", t => t.AppointmentTestLogID, cascadeDelete: true)
                .Index(t => t.AppointmentTestLogID)
                .Index(t => t.MeasurementId)
                .Index(t => t.MeasurementOperatorId);
            
            CreateTable(
                "dbo.Measurements",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        order = c.Int(nullable: false),
                        units = c.String(),
                        categoryCode = c.Int(nullable: false),
                        measurementCode = c.String(),
                        status = c.Int(nullable: false),
                        mask = c.String(),
                        dateAdded = c.DateTime(),
                        isCompulsory = c.Boolean(),
                        visibleOnWorkSheet = c.Boolean(),
                        calculateBSA = c.Boolean(),
                        MeasurementCategoryID = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.MeasurementCategories", t => t.MeasurementCategoryID)
                .Index(t => t.MeasurementCategoryID);
            
            CreateTable(
                "dbo.MeasurementCategories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OLDID = c.Int(nullable: false),
                        par = c.Int(nullable: false),
                        name = c.String(),
                        order = c.Int(nullable: false),
                        categoryCode = c.String(),
                        status = c.Int(nullable: false),
                        dateAdded = c.DateTime(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.MeasurementCategoryTests",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        MeasurementCategoryID = c.Int(nullable: false),
                        TestID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.MeasurementCategories", t => t.MeasurementCategoryID, cascadeDelete: true)
                .Index(t => t.MeasurementCategoryID);
            
            CreateTable(
                "dbo.MeasurementByPractices",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Text = c.String(),
                        isCompulsory = c.Boolean(),
                        isVisibleOnWS = c.Boolean(),
                        isVisibleOnReports = c.Boolean(),
                        PracticeID = c.Int(nullable: false),
                        MeasurementID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Measurements", t => t.MeasurementID, cascadeDelete: true)
                .Index(t => t.MeasurementID);
            
            CreateTable(
                "dbo.MeasurementMappings",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        measureName = c.String(),
                        measureCode = c.String(),
                        status = c.Int(nullable: false),
                        dateAdded = c.DateTime(),
                        categoryCode = c.String(),
                        MeasurementID = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Measurements", t => t.MeasurementID)
                .Index(t => t.MeasurementID);
            
            CreateTable(
                "dbo.MeasurementOperators",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ReportPhraseSavedTexts",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Value = c.String(),
                        AppointmentID = c.Int(nullable: false),
                        TestID = c.Int(nullable: false),
                        AppointmentTestLogID = c.Int(nullable: false),
                        TopLevelReportPhraseID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ReportPhrases", t => t.TopLevelReportPhraseID, cascadeDelete: true)
                .ForeignKey("dbo.AppointmentTestLogs", t => t.AppointmentTestLogID, cascadeDelete: true)
                .Index(t => t.AppointmentTestLogID)
                .Index(t => t.TopLevelReportPhraseID);
            
            CreateTable(
                "dbo.ReportPhrases",
                c => new
                    {
                        Id = c.Int(nullable: false),
                        name = c.String(),
                        value = c.String(),
                        parent = c.Int(),
                        root = c.Int(),
                        ordernumber = c.Int(),
                        test = c.Int(),
                        type = c.Int(),
                        field = c.Int(),
                        status = c.Int(nullable: false),
                        grp = c.String(),
                        dr = c.Int(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ReportPhraseByPractices",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Text = c.String(),
                        order = c.Int(nullable: false),
                        PracticeID = c.Int(nullable: false),
                        ReportPhraseID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ReportPhrases", t => t.ReportPhraseID, cascadeDelete: true)
                .Index(t => t.ReportPhraseID);
            
            CreateTable(
                "dbo.ReportPhraseSavedValues",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Value = c.Int(nullable: false),
                        AppointmentID = c.Int(nullable: false),
                        TestID = c.Int(nullable: false),
                        ReportPhraseID = c.Int(nullable: false),
                        TopLevelReportPhraseID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ReportPhrases", t => t.ReportPhraseID, cascadeDelete: true)
                .Index(t => t.ReportPhraseID);
            
            CreateTable(
                "dbo.AppointmentTypes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        parentAppointmentTypeId = c.Int(),
                        name = c.String(),
                        VPRequired = c.Boolean(nullable: false),
                        duration = c.Int(nullable: false),
                        appointmentType_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AppointmentTypes", t => t.appointmentType_Id)
                .Index(t => t.appointmentType_Id);
            
            CreateTable(
                "dbo.Billing_ConsultRare",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        consultCode = c.String(maxLength: 50),
                        fee = c.Int(nullable: false),
                        specialty = c.Int(nullable: false),
                        name = c.String(maxLength: 100),
                        note = c.String(maxLength: 128),
                        claimCode = c.String(maxLength: 10),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.Billing_DiagnoseRare",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        diagnoseCode = c.Int(nullable: false),
                        diagnose = c.String(maxLength: 255),
                        type = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.Billing_FileSequence",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        officeId = c.Int(nullable: false),
                        billingNumber = c.String(maxLength: 20),
                        sequenceNumber = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.Billing_Group",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        edtGroupId = c.String(maxLength: 20),
                        officeId = c.Int(nullable: false),
                        serviceLocationIndicator = c.String(maxLength: 6),
                        MOHCODE = c.String(maxLength: 1),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.Billing_Heb",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        billId = c.Int(nullable: false),
                        MOHCODE = c.String(maxLength: 1),
                        date = c.DateTime(nullable: false),
                        sequenceNumber = c.Int(nullable: false),
                        officeId = c.Int(nullable: false),
                        billingNumber = c.String(maxLength: 20),
                        specialty = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.Bills", t => t.billId, cascadeDelete: true)
                .Index(t => t.billId);
            
            CreateTable(
                "dbo.Bills",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        officeId = c.Int(nullable: false),
                        date = c.DateTime(nullable: false),
                        file = c.String(maxLength: 1024),
                        locked = c.Boolean(nullable: false),
                        billStatusId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.BillStatus", t => t.billStatusId, cascadeDelete: true)
                .Index(t => t.billStatusId);
            
            CreateTable(
                "dbo.BillStatus",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        name = c.String(maxLength: 50),
                        color = c.String(maxLength: 50),
                        orderNumber = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.Billing_Heh",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        billingHebId = c.Int(nullable: false),
                        claimNumber = c.String(maxLength: 20),
                        healthCardNumber = c.String(maxLength: 12),
                        healthCardVersion = c.String(maxLength: 2),
                        firstName = c.String(maxLength: 50),
                        lastName = c.String(maxLength: 50),
                        gender = c.Int(nullable: false),
                        province = c.String(maxLength: 20),
                        dateOfBirth = c.DateTime(nullable: false),
                        billingNumber = c.String(maxLength: 20),
                        serviceLocationIndicator = c.String(maxLength: 6),
                        payment = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.Billing_Heb", t => t.billingHebId, cascadeDelete: true)
                .Index(t => t.billingHebId);
            
            CreateTable(
                "dbo.Billing_Het",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        billingHehId = c.Int(nullable: false),
                        code = c.String(maxLength: 20),
                        serviceCode = c.String(maxLength: 20),
                        fee = c.Int(nullable: false),
                        numberOfServices = c.Short(nullable: false),
                        serviceDate = c.DateTime(nullable: false),
                        diagnoseCode = c.String(maxLength: 20),
                        billStatusId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.Billing_Heh", t => t.billingHehId, cascadeDelete: true)
                .Index(t => t.billingHehId);
            
            CreateTable(
                "dbo.Billing_TestRare",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        testId = c.Int(nullable: false),
                        code = c.String(maxLength: 8),
                        fee = c.Int(),
                        name = c.String(maxLength: 50),
                        note = c.String(maxLength: 255),
                        billingTypeId = c.Int(nullable: false),
                        claimCode = c.String(maxLength: 10),
                        numberOfServices = c.Short(nullable: false),
                        startDate = c.DateTime(nullable: false),
                        endDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.BillingType_Office_Test",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        TestId = c.Int(nullable: false),
                        OfficeId = c.Int(nullable: false),
                        ProfessionalBillingType = c.Int(nullable: false),
                        TechnicalBillingType = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.Billing_Type",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        name = c.String(maxLength: 50),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.CM_TaskDefinition",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        userId = c.String(maxLength: 128),
                        dateCreated = c.DateTime(nullable: false),
                        subject = c.String(),
                        dueDate = c.DateTime(),
                        closedBy = c.String(maxLength: 128),
                        dateClosed = c.DateTime(),
                        practiceId = c.Int(nullable: false),
                        officeId = c.Int(),
                        appointmentid = c.Int(),
                        taskStatus = c.Int(nullable: false),
                        taskUrgency = c.Int(nullable: false),
                        patientRecordid = c.Int(),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.PatientRecords", t => t.patientRecordid)
                .Index(t => t.patientRecordid);
            
            CreateTable(
                "dbo.CM_TaskMessage",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        message = c.String(),
                        messageCreatedate = c.DateTime(),
                        messageCreator = c.String(maxLength: 128),
                        isLast = c.Boolean(),
                        CM_TaskDefinitionId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.CM_TaskDefinition", t => t.CM_TaskDefinitionId, cascadeDelete: true)
                .Index(t => t.CM_TaskDefinitionId);
            
            CreateTable(
                "dbo.CM_TaskMessageRecipient",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        userid = c.String(maxLength: 128),
                        seen = c.Boolean(nullable: false),
                        seenAt = c.DateTime(),
                        CM_TaskMessageId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.CM_TaskMessage", t => t.CM_TaskMessageId, cascadeDelete: true)
                .Index(t => t.CM_TaskMessageId);
            
            CreateTable(
                "dbo.CM_TaskReport",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        reportid = c.Int(nullable: false),
                        reportType = c.Int(nullable: false),
                        reportTypeSpecified = c.Boolean(nullable: false),
                        labResultId = c.Int(nullable: false),
                        externalReportId = c.Int(nullable: false),
                        CM_TaskDefinitionId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.CM_TaskDefinition", t => t.CM_TaskDefinitionId, cascadeDelete: true)
                .Index(t => t.CM_TaskDefinitionId);
            
            CreateTable(
                "dbo.Cohorts",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(),
                        UserId = c.String(),
                        CreatedDate = c.DateTime(),
                        PracticeDoctorId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.PatientCohorts",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientId = c.Int(nullable: false),
                        Started = c.DateTime(),
                        Terminated = c.DateTime(),
                        DoctorId = c.Int(nullable: false),
                        OfficeId = c.Int(nullable: false),
                        Notes = c.String(),
                        CohortId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Cohorts", t => t.CohortId, cascadeDelete: true)
                .Index(t => t.CohortId);
            
            CreateTable(
                "dbo.Compliance",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ConsultCodes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OLDID = c.Int(nullable: false),
                        Code = c.String(),
                        Spec = c.Int(),
                        Name = c.String(),
                        Tech = c.Int(),
                        Pro = c.Int(),
                        Fee = c.Int(nullable: false),
                        Note = c.String(),
                        SortOrder = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.DiagnoseCodes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OLDID = c.Int(nullable: false),
                        Diagnosis = c.String(),
                        Type = c.Int(nullable: false),
                        SpecialityID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.DiscontinueReasons",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.DocServices",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        mark = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.DoseChangeReasons",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.EventLog_Transaction",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        fileName = c.String(maxLength: 700),
                        patientId = c.Int(nullable: false),
                        tableName = c.String(),
                        destPrimKey = c.Int(nullable: false),
                        transName = c.String(),
                        date = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.ExternalReports",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.FamilyHistoryStandardCodings",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        standardCodingSystem = c.String(),
                        standardCode = c.String(),
                        standardCodeDescription = c.String(),
                        FamilyHistoryId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.FamilyHistories", t => t.FamilyHistoryId, cascadeDelete: true)
                .Index(t => t.FamilyHistoryId);
            
            CreateTable(
                "dbo.Groups",
                c => new
                    {
                        Id = c.Int(nullable: false),
                        Name = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.HL7Coding",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        labName = c.String(maxLength: 20),
                        labCode = c.String(maxLength: 20),
                        LOINC = c.String(maxLength: 20),
                        description = c.String(maxLength: 100),
                        createdDate = c.DateTime(nullable: false),
                        updatedDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.HL7Lab",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(maxLength: 50),
                        webpage = c.String(maxLength: 256),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.HL7MarkedSeen",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        practiceDoctorId = c.Int(),
                        ApplicationUserId = c.String(),
                        seenAt = c.DateTime(nullable: false),
                        IPAddress = c.String(maxLength: 40),
                        machineName = c.String(maxLength: 100),
                        comment = c.String(maxLength: 500),
                        HL7ReportId = c.Int(nullable: false),
                        HL7ReportVersionId = c.Int(nullable: false),
                        createdDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.HL7Report", t => t.HL7ReportId, cascadeDelete: true)
                .ForeignKey("dbo.HL7ReportVersion", t => t.HL7ReportVersionId, cascadeDelete: true)
                .Index(t => t.HL7ReportId)
                .Index(t => t.HL7ReportVersionId);
            
            CreateTable(
                "dbo.HL7ReportDoctor",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        HL7ReportId = c.Int(nullable: false),
                        practiceDoctorId = c.Int(),
                        ExternalDoctorId = c.Int(nullable: false),
                        IsPrimary = c.Boolean(nullable: false),
                        createdDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.HL7TestDescription",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        testdescription = c.String(maxLength: 242),
                        ordernumber = c.Int(nullable: false),
                        createdDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.Hospitals",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        code = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ICD10",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        Code = c.String(),
                        Name = c.String(),
                        Description = c.String(),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.ImportEventLogs",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        patientId = c.Int(nullable: false),
                        FamHist = c.Int(nullable: false),
                        PastHealth = c.Int(nullable: false),
                        ProblemList = c.Int(nullable: false),
                        RiskFactor = c.Int(nullable: false),
                        Allergy = c.Int(nullable: false),
                        Medication = c.Int(nullable: false),
                        Immunization = c.Int(nullable: false),
                        Labs = c.Int(nullable: false),
                        App = c.Int(nullable: false),
                        MyClinicalNotes = c.Int(nullable: false),
                        ReportsTxt = c.Int(nullable: false),
                        ReportsImg = c.Int(nullable: false),
                        CareElements = c.Int(nullable: false),
                        Alerts = c.Int(nullable: false),
                        fileName = c.String(maxLength: 700),
                        success = c.Boolean(nullable: false),
                        result = c.String(),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.LabResults",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.LifeStages",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.LooseReportCategories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        category = c.String(maxLength: 50),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.MeasurementBSARanges",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categoryCode = c.String(),
                        measurementCode = c.String(),
                        gender = c.String(),
                        IndexedRange1 = c.Double(),
                        IndexedRange2 = c.Double(),
                        units = c.String(),
                        MeasurementRangeTypeId = c.Int(nullable: false),
                        ProstaticValveID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.MeasurementRangeTypes", t => t.MeasurementRangeTypeId, cascadeDelete: true)
                .Index(t => t.MeasurementRangeTypeId);
            
            CreateTable(
                "dbo.MeasurementRangeTypes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        name = c.String(),
                        color = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.MeasurementRanges",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categoryCode = c.String(),
                        measurementCode = c.String(),
                        gender = c.String(),
                        Range1 = c.Double(),
                        Range2 = c.Double(),
                        misc = c.String(),
                        age1 = c.Int(),
                        age2 = c.Int(),
                        units = c.String(),
                        MeasurementRangeTypeId = c.Int(nullable: false),
                        ProstaticValveID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.MeasurementRangeTypes", t => t.MeasurementRangeTypeId, cascadeDelete: true)
                .Index(t => t.MeasurementRangeTypeId);
            
            CreateTable(
                "dbo.MeasurementRangeTexts",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        Text = c.String(),
                        MeasurementId = c.Int(nullable: false),
                        MeasurementRangeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.QRYM_THERAPEUTIC_CLASS",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DRUG_CODE = c.Decimal(nullable: false, precision: 18, scale: 0, storeType: "numeric"),
                        TC_ATC_NUMBER = c.String(maxLength: 50),
                        TC_ATC = c.String(maxLength: 120),
                        TC_AHFS_NUMBER = c.String(maxLength: 50),
                        TC_AHFS = c.String(maxLength: 80),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.QRYM_DRUG", t => t.DRUG_CODE, cascadeDelete: true)
                .Index(t => t.DRUG_CODE);
            
            CreateTable(
                "dbo.QRYM_DRUG",
                c => new
                    {
                        DRUG_CODE = c.Decimal(nullable: false, precision: 18, scale: 0, storeType: "numeric"),
                        PRODUCT_CATEGORIZATION = c.String(maxLength: 80),
                        CLASS = c.String(maxLength: 50),
                        DRUG_IDENTIFICATION_NUMBER = c.String(maxLength: 50),
                        BRAND_NAME = c.String(maxLength: 200),
                        DESCRIPTOR = c.String(maxLength: 150),
                        PEDIATRIC_FLAG = c.String(maxLength: 50),
                        ACCESSION_NUMBER = c.String(maxLength: 50),
                        NUMBER_OF_AIS = c.String(maxLength: 50),
                        LAST_UPDATE_DATE = c.DateTime(),
                        AI_GROUP_NO = c.String(maxLength: 50),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.DRUG_CODE);
            
            CreateTable(
                "dbo.QRYM_FORM",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DRUG_CODE = c.Decimal(nullable: false, precision: 18, scale: 0, storeType: "numeric"),
                        PHARM_FORM_CODE = c.Decimal(nullable: false, precision: 18, scale: 0, storeType: "numeric"),
                        PHARMACEUTICAL_FORM = c.String(maxLength: 50),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.QRYM_DRUG", t => t.DRUG_CODE, cascadeDelete: true)
                .Index(t => t.DRUG_CODE);
            
            CreateTable(
                "dbo.QRYM_ingred",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DRUG_CODE = c.Decimal(nullable: false, precision: 18, scale: 0, storeType: "numeric"),
                        ACTIVE_INGREDIENT_CODE = c.Int(nullable: false),
                        INGREDIENT = c.String(maxLength: 240),
                        INGREDIENT_SUPPLIED_IND = c.String(maxLength: 50),
                        STRENGTH = c.String(maxLength: 50),
                        STRENGTH_UNIT = c.String(maxLength: 50),
                        STRENGTH_TYPE = c.String(maxLength: 50),
                        DOSAGE_VALUE = c.String(maxLength: 50),
                        BASE = c.String(maxLength: 50),
                        DOSAGE_UNIT = c.String(maxLength: 50),
                        NOTES = c.String(maxLength: 2000),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.QRYM_DRUG", t => t.DRUG_CODE, cascadeDelete: true)
                .Index(t => t.DRUG_CODE);
            
            CreateTable(
                "dbo.QRYM_ROUTE",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DRUG_CODE = c.Decimal(nullable: false, precision: 18, scale: 0, storeType: "numeric"),
                        ROUTE_OF_ADMINISTRATION_CODE = c.Int(nullable: false),
                        ROUTE_OF_ADMINISTRATION = c.String(maxLength: 50),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.QRYM_DRUG", t => t.DRUG_CODE, cascadeDelete: true)
                .Index(t => t.DRUG_CODE);
            
            CreateTable(
                "dbo.MedicationDefaults",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Strength = c.String(),
                        SIG = c.String(),
                        Route = c.String(),
                        Mitte = c.String(),
                        Repeats = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.MedicationNoDins",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        MedicationName = c.String(maxLength: 200),
                        IsActive = c.Boolean(nullable: false),
                        DateCreated = c.DateTime(nullable: false),
                        LastModified = c.DateTime(),
                        LastModifiedByUser = c.Int(nullable: false),
                        IpAddress = c.String(maxLength: 50),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.MedicationTemplateClass",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ClassName = c.String(maxLength: 100),
                        Group = c.Int(nullable: false),
                        DoctorSpecializationCode = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.MedicationTemplates",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        TemplateClassId = c.Int(nullable: false),
                        UserId = c.Int(nullable: false),
                        Name = c.String(maxLength: 50),
                        Dose = c.String(maxLength: 50),
                        SIG = c.String(maxLength: 50),
                        Route = c.String(maxLength: 50),
                        Repeats = c.Int(),
                        Mitte = c.String(maxLength: 50),
                        TreatmentTypeId = c.Int(),
                        ComplianceId = c.Int(),
                        Strength = c.String(maxLength: 50),
                        Instructions = c.String(),
                        LU = c.String(maxLength: 50),
                        CanShare = c.Boolean(nullable: false),
                        IsActive = c.Boolean(nullable: false),
                        IsDin = c.Boolean(nullable: false),
                        IpAddress = c.String(maxLength: 50),
                        DateCreated = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.MedicationTemplateClass", t => t.TemplateClassId, cascadeDelete: true)
                .Index(t => t.TemplateClassId);
            
            CreateTable(
                "dbo.MedicationTemplateDins",
                c => new
                    {
                        MedicationTemplateId = c.Int(nullable: false),
                        MedicationId = c.Decimal(nullable: false, precision: 18, scale: 0, storeType: "numeric"),
                    })
                .PrimaryKey(t => t.MedicationTemplateId)
                .ForeignKey("dbo.QRYM_DRUG", t => t.MedicationId, cascadeDelete: true)
                .ForeignKey("dbo.MedicationTemplates", t => t.MedicationTemplateId)
                .Index(t => t.MedicationTemplateId)
                .Index(t => t.MedicationId);
            
            CreateTable(
                "dbo.MedicationTemplateNoDins",
                c => new
                    {
                        MedicationTemplateId = c.Int(nullable: false),
                        MedicationNoDinId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.MedicationTemplateId)
                .ForeignKey("dbo.MedicationNoDins", t => t.MedicationNoDinId, cascadeDelete: true)
                .ForeignKey("dbo.MedicationTemplates", t => t.MedicationTemplateId)
                .Index(t => t.MedicationTemplateId)
                .Index(t => t.MedicationNoDinId);
            
            CreateTable(
                "dbo.MobileNetworks",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Notes = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.OfficeFaxFolder",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        officeId = c.Int(nullable: false),
                        folder = c.String(maxLength: 50),
                        description = c.String(maxLength: 250),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Office", t => t.officeId, cascadeDelete: true)
                .Index(t => t.officeId);
            
            CreateTable(
                "dbo.OfficeRooms",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        roomType = c.Int(nullable: false),
                        note = c.String(),
                        OfficeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Office", t => t.OfficeId, cascadeDelete: true)
                .Index(t => t.OfficeId);
            
            CreateTable(
                "dbo.OfficeUrls",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        officeId = c.Int(nullable: false),
                        url = c.String(maxLength: 250),
                        urlTypeId = c.Int(nullable: false),
                        folder = c.String(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Office", t => t.officeId, cascadeDelete: true)
                .ForeignKey("dbo.OfficeUrlTypes", t => t.urlTypeId, cascadeDelete: true)
                .Index(t => t.officeId)
                .Index(t => t.urlTypeId);
            
            CreateTable(
                "dbo.OfficeUrlTypes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        urlType = c.String(),
                        description = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.OHIPTimeLimitedFeeCodes",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        Code = c.String(),
                        Description = c.String(),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.OLISTestReportCategories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categoryName = c.String(),
                        createdOn = c.DateTime(nullable: false),
                        updatedOn = c.DateTime(nullable: false),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.OLISTestRequestCategories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categoryName = c.String(),
                        createdOn = c.DateTime(nullable: false),
                        updatedOn = c.DateTime(nullable: false),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.OLISTestRequestNomenclatures",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OLISTestRequestCode = c.String(maxLength: 15),
                        testRequestName = c.String(maxLength: 500),
                        requestAlternateName1 = c.String(maxLength: 500),
                        requestAlternateName2 = c.String(maxLength: 500),
                        requestAlternateName3 = c.String(maxLength: 500),
                        comment = c.String(),
                        sortKey = c.String(maxLength: 15),
                        OLISTestRequestCategoryId = c.Int(nullable: false),
                        OLISTestRequestSubCategoryId = c.Int(nullable: false),
                        OLISTestReportCategoryId = c.Int(nullable: false),
                        createdOn = c.DateTime(nullable: false),
                        updatedOn = c.DateTime(nullable: false),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.OLISTestReportCategories", t => t.OLISTestReportCategoryId, cascadeDelete: true)
                .ForeignKey("dbo.OLISTestRequestCategories", t => t.OLISTestRequestCategoryId, cascadeDelete: true)
                .ForeignKey("dbo.OLISTestRequestSubCategories", t => t.OLISTestRequestSubCategoryId, cascadeDelete: true)
                .Index(t => t.OLISTestRequestCategoryId)
                .Index(t => t.OLISTestRequestSubCategoryId)
                .Index(t => t.OLISTestReportCategoryId);
            
            CreateTable(
                "dbo.OLISTestRequestSubCategories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categoryName = c.String(),
                        createdOn = c.DateTime(nullable: false),
                        updatedOn = c.DateTime(nullable: false),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.OLISTestResultCategories",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        categoryName = c.String(),
                        createdOn = c.DateTime(nullable: false),
                        updatedOn = c.DateTime(nullable: false),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.OLISTestResultNomenclatures",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        LOINCCode = c.String(maxLength: 10),
                        LOINCComponent = c.String(),
                        LOINCProperty = c.String(),
                        Units = c.String(),
                        LOINCTime = c.String(),
                        LOINCSystem = c.String(),
                        LOINCScale = c.String(),
                        LOINCMethod = c.String(maxLength: 100),
                        LOINCShortName = c.String(maxLength: 150),
                        LOINCFullySpecifiedName = c.String(maxLength: 500),
                        resultAlternateName1 = c.String(maxLength: 500),
                        resultAlternateName2 = c.String(maxLength: 500),
                        resultAlternateName3 = c.String(maxLength: 500),
                        LOINCAnswerList = c.String(),
                        effectiveDate = c.DateTime(),
                        expiredDate = c.DateTime(),
                        sortKey = c.String(maxLength: 15),
                        OLISTestResultCategoryId = c.Int(nullable: false),
                        createdOn = c.DateTime(nullable: false),
                        updatedOn = c.DateTime(nullable: false),
                        isActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.OLISTestResultCategories", t => t.OLISTestResultCategoryId, cascadeDelete: true)
                .Index(t => t.OLISTestResultCategoryId);
            
            CreateTable(
                "dbo.PastHealthStandardCodings",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        standardCodingSystem = c.String(),
                        standardCode = c.String(),
                        standardCodeDescription = c.String(),
                        PastHealthId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PastHealths", t => t.PastHealthId, cascadeDelete: true)
                .Index(t => t.PastHealthId);
            
            CreateTable(
                "dbo.PatientAllergies",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientId = c.Int(nullable: false),
                        MedicationName = c.String(maxLength: 200),
                        AgeStarted = c.Int(),
                        LifeStageId = c.Int(nullable: false),
                        ReactionTypeId = c.Int(nullable: false),
                        SeverityId = c.Int(nullable: false),
                        AllergyStatusId = c.Int(),
                        Description = c.String(maxLength: 200),
                        Diagnosis = c.String(maxLength: 200),
                        Procedure = c.String(maxLength: 200),
                        Notes = c.String(),
                        IsDin = c.Boolean(nullable: false),
                        MedicationId = c.Decimal(precision: 18, scale: 0, storeType: "numeric"),
                        MedicationNoDinId = c.Int(),
                        DateStarted = c.DateTime(nullable: false),
                        DateResolved = c.DateTime(),
                        DateProcedure = c.DateTime(),
                        DateCreated = c.DateTime(nullable: false),
                        LastModfied = c.DateTime(nullable: false),
                        LastModifiedByUser = c.Int(nullable: false),
                        IsActive = c.Boolean(nullable: false),
                        IpAddress = c.String(maxLength: 50),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AllergyStatuses", t => t.AllergyStatusId)
                .ForeignKey("dbo.LifeStages", t => t.LifeStageId, cascadeDelete: true)
                .ForeignKey("dbo.QRYM_DRUG", t => t.MedicationId)
                .ForeignKey("dbo.MedicationNoDins", t => t.MedicationNoDinId)
                .ForeignKey("dbo.ReactionTypes", t => t.ReactionTypeId, cascadeDelete: true)
                .ForeignKey("dbo.Severity", t => t.SeverityId, cascadeDelete: true)
                .Index(t => t.LifeStageId)
                .Index(t => t.ReactionTypeId)
                .Index(t => t.SeverityId)
                .Index(t => t.AllergyStatusId)
                .Index(t => t.MedicationId)
                .Index(t => t.MedicationNoDinId);
            
            CreateTable(
                "dbo.PatientAllergyIngredients",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientId = c.Int(nullable: false),
                        IngredientCode = c.Int(nullable: false),
                        Ingredient = c.String(maxLength: 250),
                        IsActive = c.Boolean(nullable: false),
                        CreatedBy = c.Int(nullable: false),
                        DateCreated = c.DateTime(nullable: false),
                        IpAddress = c.String(maxLength: 50),
                        PatientAllergy_Id = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientAllergies", t => t.PatientAllergy_Id)
                .Index(t => new { t.PatientId, t.IngredientCode }, unique: true, name: "IX_AllergyIngredient")
                .Index(t => t.PatientAllergy_Id);
            
            CreateTable(
                "dbo.PatientAllergyIngredientDetails",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        AllergyIngredientId = c.Int(nullable: false),
                        MedicationId = c.Int(nullable: false),
                        MedicationName = c.String(maxLength: 200),
                        LastModifiedByUser = c.Int(nullable: false),
                        LastModified = c.DateTime(nullable: false),
                        IpAddress = c.String(maxLength: 50),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientAllergyIngredients", t => t.AllergyIngredientId, cascadeDelete: true)
                .Index(t => t.AllergyIngredientId);
            
            CreateTable(
                "dbo.ReactionTypes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.Severity",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.PatientMedications",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientId = c.Int(nullable: false),
                        MedicationSetId = c.Int(nullable: false),
                        MedicationName = c.String(maxLength: 200),
                        DIN = c.String(maxLength: 50),
                        Dose = c.String(maxLength: 50),
                        Strength = c.String(maxLength: 50),
                        SIG = c.String(maxLength: 50),
                        Route = c.String(maxLength: 50),
                        Mitte = c.String(maxLength: 50),
                        Repeats = c.Int(nullable: false),
                        DoseChangeReasonId = c.Int(),
                        DoseChangeComment = c.String(),
                        DiscontinueReasonId = c.Int(),
                        DiscontinueComment = c.String(),
                        LU = c.String(maxLength: 50),
                        Instructions = c.String(),
                        IsDin = c.Boolean(nullable: false),
                        MedicationId = c.Decimal(precision: 18, scale: 0, storeType: "numeric"),
                        MedicationNoDinId = c.Int(),
                        LastPrescribedByUser = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                        MedicationLastUpdated = c.DateTime(),
                        DateLastPrescribed = c.DateTime(),
                        DateStarted = c.DateTime(nullable: false),
                        DateDiscontinued = c.DateTime(),
                        DateCreated = c.DateTime(nullable: false),
                        LastModified = c.DateTime(),
                        LastModifiedByUser = c.Int(nullable: false),
                        IpAddress = c.String(maxLength: 50),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.DiscontinueReasons", t => t.DiscontinueReasonId)
                .ForeignKey("dbo.DoseChangeReasons", t => t.DoseChangeReasonId)
                .ForeignKey("dbo.QRYM_DRUG", t => t.MedicationId)
                .ForeignKey("dbo.MedicationNoDins", t => t.MedicationNoDinId)
                .ForeignKey("dbo.PatientMedicationSets", t => t.MedicationSetId, cascadeDelete: true)
                .Index(t => t.MedicationSetId)
                .Index(t => t.DoseChangeReasonId)
                .Index(t => t.DiscontinueReasonId)
                .Index(t => t.MedicationId)
                .Index(t => t.MedicationNoDinId);
            
            CreateTable(
                "dbo.PatientMedicationSets",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DateCreated = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.PatientPrescriptions",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PrescriptionSetId = c.Int(nullable: false),
                        PatientMedicationId = c.Int(nullable: false),
                        Description = c.String(maxLength: 200),
                        MedicationForm = c.String(maxLength: 50),
                        Strength = c.String(maxLength: 50),
                        Mitte = c.String(maxLength: 50),
                        Quantity = c.Int(),
                        Repeats = c.Int(),
                        Frequency = c.String(maxLength: 50),
                        Duration = c.Int(),
                        RefillDuration = c.Int(),
                        RefillQuantity = c.Int(),
                        DispenseInterval = c.String(maxLength: 50),
                        LU = c.String(maxLength: 50),
                        SubstitutionNotAllowed = c.Boolean(nullable: false),
                        LongTerm = c.Boolean(nullable: false),
                        NonAuthoritive = c.Boolean(nullable: false),
                        Protocol = c.String(maxLength: 50),
                        PrescribedByUser = c.Int(nullable: false),
                        PrescriberCPSO = c.String(maxLength: 50),
                        PrescriberFirstName = c.String(maxLength: 50),
                        PrescriberLastName = c.String(maxLength: 50),
                        PrescriptionStatusId = c.Int(nullable: false),
                        TreatmentTypeId = c.Int(),
                        ProblemCode = c.String(maxLength: 50),
                        PrescripedByOutsideProvider = c.Boolean(nullable: false),
                        OutsideProviderFirstName = c.String(maxLength: 50),
                        OutsideProviderLastName = c.String(maxLength: 50),
                        Compliance = c.Boolean(nullable: false),
                        DateWritten = c.DateTime(nullable: false),
                        DateStarted = c.DateTime(nullable: false),
                        DateLastRefill = c.DateTime(),
                        PastMedication = c.Boolean(nullable: false),
                        PriorPrescriptionId = c.Int(),
                        PharmacyIdentifier = c.String(maxLength: 50),
                        PharmacyName = c.String(maxLength: 50),
                        PharmacyAddress = c.String(maxLength: 50),
                        Instructions = c.String(),
                        Notes = c.String(),
                        DateToBePickup = c.DateTime(),
                        DateCreated = c.DateTime(nullable: false),
                        LastModified = c.DateTime(),
                        LastModifiedByUser = c.Int(nullable: false),
                        IpAddress = c.String(maxLength: 50),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientMedications", t => t.PatientMedicationId, cascadeDelete: true)
                .ForeignKey("dbo.PrescriptionStatus", t => t.PrescriptionStatusId, cascadeDelete: true)
                .ForeignKey("dbo.PatientPrescriptionSets", t => t.PrescriptionSetId, cascadeDelete: true)
                .ForeignKey("dbo.TreatmentTypes", t => t.TreatmentTypeId)
                .Index(t => t.PrescriptionSetId)
                .Index(t => t.PatientMedicationId)
                .Index(t => t.PrescriptionStatusId)
                .Index(t => t.TreatmentTypeId);
            
            CreateTable(
                "dbo.PatientPrescriptionsPrinted",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PrescriptionId = c.Int(nullable: false),
                        UserId = c.Int(nullable: false),
                        IPAddress = c.String(maxLength: 50),
                        DatePrinted = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.PatientPrescriptions", t => t.PrescriptionId, cascadeDelete: true)
                .Index(t => t.PrescriptionId);
            
            CreateTable(
                "dbo.PrescriptionStatus",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.PatientPrescriptionSets",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DateCreated = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.TreatmentTypes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.PatientProstaticValves",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Patientid = c.Int(),
                        ProstaticValveId = c.Int(nullable: false),
                        Size = c.Decimal(precision: 18, scale: 2),
                        Addedby = c.Int(),
                        StatusChangeBy = c.Int(),
                        AddedDate = c.DateTime(),
                        EndDate = c.DateTime(),
                        InActComment = c.String(),
                        InActreason = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.PrescriptionDefaults",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PrescriptionStatusId = c.Int(),
                        TreatmentTypeId = c.Int(),
                        ComplianceId = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Compliance", t => t.ComplianceId)
                .ForeignKey("dbo.PrescriptionStatus", t => t.PrescriptionStatusId)
                .ForeignKey("dbo.TreatmentTypes", t => t.TreatmentTypeId)
                .Index(t => t.PrescriptionStatusId)
                .Index(t => t.TreatmentTypeId)
                .Index(t => t.ComplianceId);
            
            CreateTable(
                "dbo.ProblemListStandardCodings",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        standardCodingSystem = c.String(),
                        standardCode = c.String(),
                        standardCodeDescription = c.String(),
                        ProblemListId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ProblemLists", t => t.ProblemListId, cascadeDelete: true)
                .Index(t => t.ProblemListId);
            
            CreateTable(
                "dbo.ProstaticValves",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Type = c.String(),
                        Typeid = c.Int(),
                        Gentype = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.Reasons",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        mark = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.ReportPhrase_Custom",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Text = c.String(),
                        Visible = c.Boolean(nullable: false),
                        Rank = c.Int(nullable: false),
                        TestID = c.Int(nullable: false),
                        PatientID = c.Int(nullable: false),
                        PracticeID = c.Int(nullable: false),
                        UserID = c.Int(nullable: false),
                        ReportPhraseId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ReportPhraseMeasurmentCategories",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        ReportPhraseID = c.Int(nullable: false),
                        MeasurementCategoryID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.ReportPhraseMeasurmentCategoryScrolls",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        ReportPhraseID = c.Int(nullable: false),
                        MeasurementCategoryID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.ReportPhraseNormals",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        ReportPhraseID = c.Int(nullable: false),
                        NormalReportPhraseID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.Requisition",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        requisitionPatientId = c.Int(nullable: false),
                        requisitionTypeId = c.Int(nullable: false),
                        testTime = c.DateTime(),
                        resultTime = c.DateTime(),
                        requisitionStatus = c.Int(nullable: false),
                        requisitionItems = c.String(),
                        fileName = c.String(maxLength: 256),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.RequisitionPatient", t => t.requisitionPatientId, cascadeDelete: true)
                .ForeignKey("dbo.RequisitionType", t => t.requisitionTypeId, cascadeDelete: true)
                .ForeignKey("dbo.RequisitionStatus", t => t.requisitionStatus, cascadeDelete: true)
                .Index(t => t.requisitionPatientId)
                .Index(t => t.requisitionTypeId)
                .Index(t => t.requisitionStatus);
            
            CreateTable(
                "dbo.RequisitionPatient",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        patientRecordId = c.Int(nullable: false),
                        practiceDoctorId = c.Int(nullable: false),
                        requisitionTime = c.DateTime(nullable: false),
                        requisitionTimeId = c.Int(nullable: false),
                        nextAppointmentDoctor = c.Int(nullable: false),
                        nextAppointmentOffice = c.Int(nullable: false),
                        comment = c.String(),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.PatientRecords", t => t.patientRecordId, cascadeDelete: true)
                .ForeignKey("dbo.RequisitionTime", t => t.requisitionTimeId, cascadeDelete: true)
                .Index(t => t.patientRecordId)
                .Index(t => t.requisitionTimeId);
            
            CreateTable(
                "dbo.RequisitionTime",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        name = c.String(maxLength: 50),
                        orderNumber = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.RequisitionType",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        name = c.String(maxLength: 50),
                        orderNumber = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.RequisitionStatus",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        color = c.String(maxLength: 32),
                        text = c.String(maxLength: 128),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.RequisitionItems",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        requisitionItemId = c.Int(nullable: false),
                        requisitionTypeId = c.Int(nullable: false),
                        name = c.String(maxLength: 50),
                        orderNumber = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.RequisitionType", t => t.requisitionTypeId, cascadeDelete: true)
                .Index(t => t.requisitionTypeId);
            
            CreateTable(
                "dbo.RequisitionResult",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        requisitionId = c.Int(nullable: false),
                        fileSize = c.Int(nullable: false),
                        fileType = c.String(maxLength: 128),
                        fileName = c.String(maxLength: 256),
                        ip = c.String(maxLength: 128),
                        description = c.String(maxLength: 128),
                        userId = c.String(maxLength: 128),
                        url = c.String(maxLength: 256),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.Requisition", t => t.requisitionId, cascadeDelete: true)
                .Index(t => t.requisitionId);
            
            CreateTable(
                "dbo.RequisitionTemplate",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        practiceId = c.Int(nullable: false),
                        name = c.String(maxLength: 50),
                        requisitionTypeId = c.Int(nullable: false),
                        requisitionItems = c.String(),
                        active = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.id)
                .ForeignKey("dbo.Practices", t => t.practiceId, cascadeDelete: true)
                .ForeignKey("dbo.RequisitionType", t => t.requisitionTypeId, cascadeDelete: true)
                .Index(t => t.practiceId)
                .Index(t => t.requisitionTypeId);
            
            CreateTable(
                "dbo.RolePermissions",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PermissionId = c.Int(nullable: false),
                        PermissionName = c.String(),
                        PermissionTypeId = c.Int(nullable: false),
                        filterType = c.Int(nullable: false),
                        criticalResType = c.Int(nullable: false),
                        OwnerID = c.String(),
                        PracticeID = c.Int(nullable: false),
                        RoleID = c.String(),
                        UserID = c.String(),
                        UserName = c.String(),
                        Date = c.DateTime(nullable: false),
                        IPaddress = c.String(),
                        ApplicationRoleId = c.String(maxLength: 128),
                        selected = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AspNetRoles", t => t.ApplicationRoleId)
                .Index(t => t.ApplicationRoleId);
            
            CreateTable(
                "dbo.AspNetRoles",
                c => new
                    {
                        Id = c.String(nullable: false, maxLength: 128),
                        Name = c.String(nullable: false, maxLength: 256),
                        Description = c.String(),
                        PracticeId = c.Int(),
                        Discriminator = c.String(nullable: false, maxLength: 128),
                    })
                .PrimaryKey(t => t.Id)
                .Index(t => t.Name, unique: true, name: "RoleNameIndex");
            
            CreateTable(
                "dbo.ScheduleColorSchemas",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        colorHex = c.String(),
                        forPermissions = c.String(),
                        forAppointmentTypes = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ScheduleDayResources",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        startTime = c.String(),
                        finishTime = c.String(),
                        absentTime = c.String(),
                        isBooked = c.Boolean(nullable: false),
                        TestResourceTypeId = c.Int(nullable: false),
                        UserId = c.Int(),
                        RoomId = c.Int(),
                        ScheduleDayId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ScheduleDays", t => t.ScheduleDayId, cascadeDelete: true)
                .Index(t => t.ScheduleDayId);
            
            CreateTable(
                "dbo.ScheduleDays",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Date = c.DateTime(nullable: false),
                        dayOfWeek = c.Int(nullable: false),
                        ScheduleId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Schedules", t => t.ScheduleId, cascadeDelete: true)
                .Index(t => t.ScheduleId);
            
            CreateTable(
                "dbo.Schedules",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OfficeId = c.Int(nullable: false),
                        maxNumOfDays = c.Int(nullable: false),
                        timeSlotDuration = c.Int(nullable: false),
                        startDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ScheduleDayResourceSlots",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        AppointmentId = c.Int(nullable: false),
                        TestId = c.Int(nullable: false),
                        Status = c.Int(nullable: false),
                        slotColor = c.Int(nullable: false),
                        isDoubleBooked = c.Boolean(nullable: false),
                        isMDScheduled = c.Boolean(nullable: false),
                        startTime = c.String(),
                        finishTime = c.String(),
                        comment = c.String(),
                        createdDate = c.DateTime(nullable: false),
                        updatedDate = c.DateTime(nullable: false),
                        ScheduleDayResourceId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ScheduleDayResources", t => t.ScheduleDayResourceId, cascadeDelete: true)
                .Index(t => t.ScheduleDayResourceId);
            
            CreateTable(
                "dbo.ScheduleUsers",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        UserId = c.Int(nullable: false),
                        PracticeId = c.Int(nullable: false),
                        permission = c.String(),
                        startDate = c.DateTime(nullable: false),
                        endDate = c.DateTime(nullable: false),
                        createdDate = c.DateTime(nullable: false),
                        updatedDate = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.ScheduleWeekDays",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        officeId = c.Int(nullable: false),
                        date = c.DateTime(),
                        dayOfWeek = c.Int(nullable: false),
                        startTime = c.String(),
                        finishTime = c.String(),
                        absentTime = c.String(),
                        reservedTime = c.String(),
                        comment = c.String(),
                        createdDate = c.DateTime(nullable: false),
                        ScheduleUserId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.ScheduleUsers", t => t.ScheduleUserId, cascadeDelete: true)
                .Index(t => t.ScheduleUserId);
            
            CreateTable(
                "dbo.SendTypes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Color = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.SigCodes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Description = c.String(maxLength: 50),
                        DisplayOrder = c.Int(),
                        IsActive = c.Boolean(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.StoreInventories",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        code = c.String(maxLength: 10),
                        officeId = c.Int(nullable: false),
                        inventoryType = c.Int(nullable: false),
                        status = c.Int(nullable: false),
                        date = c.DateTime(nullable: false),
                        practiceId = c.Int(nullable: false),
                        expectedRetDate = c.DateTime(nullable: false),
                        userId = c.String(maxLength: 50),
                        notes = c.String(maxLength: 1000),
                        demographicId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.StoreInventoryStatus",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        name = c.String(maxLength: 100),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.StoreInventoryTypes",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        name = c.String(maxLength: 1000),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.TestGroups",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        TestId = c.Int(nullable: false),
                        GroupId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.TestGroupDetails",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Visible = c.Boolean(nullable: false),
                        TestID = c.Int(nullable: false),
                        TestGroupId = c.Int(nullable: false),
                        ReportPhraseId = c.Int(),
                        MeasurementCategoryId = c.Int(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.TestSequences",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ECG = c.Int(nullable: false),
                        ECHO = c.Int(nullable: false),
                        PFT = c.Int(nullable: false),
                        STRESS = c.Int(nullable: false),
                        STRESS_NUCLEAR = c.Int(nullable: false),
                        SE = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.TriageUrgencies",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        description = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.UserBillingDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ApplicationUserId = c.String(),
                        ExternalDoctorId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.UserDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ApplicationUserId = c.String(),
                        ExternalDoctorId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.UserLocations",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        EveryWhere = c.Boolean(nullable: false),
                        ApplicationUserId = c.String(maxLength: 128),
                        OfficeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.UserMobileNetworks",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ApplicationUserId = c.String(),
                        MobileNetworkId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.UserOffices",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        ApplicationUserId = c.String(maxLength: 128),
                        OfficeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_AppointmentTestLog",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Date = c.DateTime(),
                        Status = c.Int(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                        PatientId = c.Int(nullable: false),
                        IP = c.String(),
                        UserId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_Alert",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientId = c.Int(nullable: false),
                        UserId = c.Int(nullable: false),
                        Description = c.String(),
                        Notes = c.String(),
                        EndDate = c.DateTime(),
                        Status = c.Boolean(nullable: false),
                        eSubmitDate = c.DateTime(nullable: false),
                        DateActive_Day = c.Int(nullable: false),
                        DateActive_Month = c.Int(nullable: false),
                        DateActive_Year = c.Int(nullable: false),
                        DateActive = c.DateTime(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_Category",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Text = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_FamilyHistory",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientID = c.Int(nullable: false),
                        StartDateDay = c.Int(nullable: false),
                        StartDateMonth = c.Int(nullable: false),
                        StartDateYear = c.Int(nullable: false),
                        AgeOnset = c.Int(nullable: false),
                        Units = c.String(),
                        Position = c.Int(),
                        Status = c.Int(),
                        ProblemDescription = c.String(),
                        Treatment = c.String(),
                        Notes = c.String(),
                        YearsDays = c.String(),
                        RelationShip = c.String(),
                        LifeStage = c.String(),
                        ReasonForDelete = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_Immunization",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientId = c.Int(nullable: false),
                        PhysicianId = c.Int(nullable: false),
                        AdministeredBy = c.String(),
                        Table_Of_Doctors_Names = c.String(),
                        ImmunizationDate = c.DateTime(),
                        ImmunizationRefusedDate = c.DateTime(),
                        Name = c.String(),
                        Notes = c.String(),
                        Manufacturer = c.String(),
                        LotNumber = c.String(),
                        Route = c.String(),
                        Site = c.String(),
                        Dose = c.String(),
                        Instructions = c.String(),
                        NextDate = c.DateTime(),
                        NotRemind = c.Boolean(nullable: false),
                        ReasonNextDate = c.String(),
                        CodingVocabulary = c.String(),
                        ImmunizationCode = c.String(),
                        ReasonForDel = c.String(),
                        SubmitDate = c.DateTime(),
                        isactive = c.Boolean(nullable: false),
                        Setid = c.Int(nullable: false),
                        Colonoscopy = c.Boolean(nullable: false),
                        ParentID = c.Int(),
                        VP_CPP_ImmunizationTypeId = c.Int(nullable: false),
                        VP_CPP_ImmunizationStatusId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_ImmunizationStatus",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Status = c.String(),
                        Color = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_ImmunizationType",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Agecategory = c.String(),
                        Period = c.Int(),
                        Submite_date = c.DateTime(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_Item",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_Problem_List",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Patient_id = c.Int(nullable: false),
                        Life_Stage = c.String(),
                        Units = c.String(),
                        Diagnosis = c.String(),
                        Problem_Description = c.String(),
                        Problem_Status = c.Boolean(nullable: false),
                        Notes = c.String(),
                        Position = c.Int(nullable: false),
                        Proc_Interv = c.String(),
                        ProcDate_Day = c.Int(nullable: false),
                        ProcDate_Month = c.Int(nullable: false),
                        ProcDate_Year = c.Int(nullable: false),
                        ProcDate = c.DateTime(),
                        Diagnosis_id = c.Int(nullable: false),
                        Record_Status = c.Boolean(nullable: false),
                        Reason_for_Del = c.String(),
                        Submit_Date = c.DateTime(nullable: false),
                        DateOfOnset_Day = c.Int(nullable: false),
                        DateOfOnset_Month = c.Int(nullable: false),
                        DateOfOnset_Year = c.Int(nullable: false),
                        DateOfOnset = c.DateTime(),
                        ResolutionDate_Day = c.Int(nullable: false),
                        ResolutionDate_Month = c.Int(nullable: false),
                        ResolutionDate_Year = c.Int(nullable: false),
                        ResolutionDate = c.DateTime(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_CPP_RiskFactor",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        PatientId = c.Int(nullable: false),
                        RiskFactor = c.String(),
                        ExposureDetails = c.String(),
                        OnsetAge = c.Int(nullable: false),
                        StartDate = c.DateTime(),
                        EndDate = c.DateTime(),
                        LifeStage = c.String(),
                        Notes = c.String(),
                        Position = c.Int(nullable: false),
                        Status = c.Boolean(nullable: false),
                        ReasonForDel = c.String(),
                        SubmitDate = c.DateTime(nullable: false),
                        StartDateDay = c.Int(nullable: false),
                        StartDateMonth = c.Int(nullable: false),
                        StartDateYear = c.Int(nullable: false),
                        Unit = c.String(),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.VP_CPP_Setting",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        PatientID = c.Int(nullable: false),
                        Text = c.String(),
                        Order = c.Int(nullable: false),
                        Visible = c.Boolean(nullable: false),
                        VP_CPP_Category_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.VP_MeasurementSavedValue",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Value = c.String(),
                        AppointmentId = c.Int(nullable: false),
                        PatientId = c.Int(nullable: false),
                        VP_AppointmentTestLogId = c.Int(nullable: false),
                        VPMeasurementId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.VPMeasurements", t => t.VPMeasurementId, cascadeDelete: true)
                .Index(t => t.VPMeasurementId);
            
            CreateTable(
                "dbo.VPMeasurements",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OLDID = c.Int(nullable: false),
                        Name = c.String(),
                        Order = c.Int(nullable: false),
                        Units = c.String(),
                        Normal = c.String(),
                        Range1 = c.Decimal(precision: 18, scale: 2),
                        Range2 = c.Decimal(precision: 18, scale: 2),
                        Spec = c.Int(nullable: false),
                        Type = c.Int(),
                        DrID = c.Int(),
                        Status = c.Int(nullable: false),
                        Options = c.String(),
                        Testcode = c.String(),
                        Cdid = c.Int(),
                        VPCategoryID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_Privacy_Notes",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Notes = c.String(),
                        PatientId = c.Int(nullable: false),
                        UserId = c.Int(nullable: false),
                        DateEntered = c.DateTime(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_ReportPhrase_Custom",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Text = c.String(),
                        Visible = c.Boolean(nullable: false),
                        Rank = c.Int(nullable: false),
                        Accumulative = c.Boolean(nullable: false),
                        PatientID = c.Int(nullable: false),
                        PracticeID = c.Int(nullable: false),
                        VP_ReportPhraseId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_ReportPhrases_Skipped",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        UserID = c.Int(nullable: false),
                        PatientID = c.Int(nullable: false),
                        Skip = c.Boolean(nullable: false),
                        VP_ReportPhraseID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.VP_ReportPhrasesSavedText",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Value = c.String(),
                        AppointmentId = c.Int(nullable: false),
                        PatientId = c.Int(nullable: false),
                        VP_AppointmentTestLogId = c.Int(nullable: false),
                        TopLevelVPReportPhraseID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_ReportPhrasesSavedValue",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Value = c.Int(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                        PatientId = c.Int(nullable: false),
                        VPReportPhraseId = c.Int(nullable: false),
                        TopLevelReportPhraseId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_SendReport",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Location = c.String(),
                        URL = c.String(),
                        DateEntered = c.DateTime(nullable: false),
                        Sent = c.Boolean(nullable: false),
                        Amended = c.Boolean(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                        PatientId = c.Int(nullable: false),
                        SendTypeId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_Template",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_Template_Detail",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Value = c.String(),
                        NH = c.Decimal(precision: 18, scale: 2),
                        NL = c.Decimal(precision: 18, scale: 2),
                        TH = c.Decimal(precision: 18, scale: 2),
                        TL = c.Decimal(precision: 18, scale: 2),
                        Frequency = c.Int(nullable: false),
                        VPTemplateField = c.Int(nullable: false),
                        VP_TemplateId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_Template_Patient_Data",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientID = c.Int(nullable: false),
                        LogId = c.Int(nullable: false),
                        LogDate = c.DateTime(),
                        Value = c.String(),
                        DateEntered = c.DateTime(),
                        VPTemplateFieldId = c.Int(nullable: false),
                        TemplateId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VP_Template_Patient_Detail",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        PatientID = c.Int(nullable: false),
                        Value = c.String(),
                        NH = c.Decimal(precision: 18, scale: 2),
                        NL = c.Decimal(precision: 18, scale: 2),
                        TH = c.Decimal(precision: 18, scale: 2),
                        TL = c.Decimal(precision: 18, scale: 2),
                        Frequency = c.Int(nullable: false),
                        VPTemplateField = c.Int(nullable: false),
                        VP_TemplateId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VPCategories",
                c => new
                    {
                        id = c.Int(nullable: false, identity: true),
                        OldId = c.Int(nullable: false),
                        Name = c.String(),
                        Order = c.Int(nullable: false),
                        Dt = c.Int(nullable: false),
                        Options = c.String(),
                        DrID = c.Int(),
                        Status = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.id);
            
            CreateTable(
                "dbo.VPCategoryOptions",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        VPCategoryId = c.Int(nullable: false),
                        VPOptionId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VPMeasurementOptions",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        VPMeasurementId = c.Int(nullable: false),
                        VPOptionId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VPOptions",
                c => new
                    {
                        Id = c.Int(nullable: false),
                        Spec = c.Int(nullable: false),
                        Name = c.String(),
                        DrID = c.Int(nullable: false),
                        Status = c.Int(nullable: false),
                        Grp = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VPReportPhrases",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Index = c.Int(nullable: false),
                        OldId = c.Int(nullable: false),
                        Name = c.String(),
                        Value = c.String(),
                        Parent = c.Int(nullable: false),
                        Root = c.Int(nullable: false),
                        Order = c.Int(nullable: false),
                        Type = c.Int(),
                        Options = c.String(),
                        Spec = c.Int(nullable: false),
                        DrID = c.Int(),
                        Status = c.Int(nullable: false),
                        Grp = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VPReportPhraseByDoctors",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        DrID = c.Int(nullable: false),
                        Text = c.String(),
                        VPReportPhraseID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VPReportPhraseOptions",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        VPVPReportPhraseId = c.Int(nullable: false),
                        VPOptionId = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VPTemplateFields",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        OLDId = c.Int(nullable: false),
                        Name = c.String(),
                        Order = c.Int(nullable: false),
                        Grp = c.Int(nullable: false),
                        Type = c.Int(nullable: false),
                        TestCode = c.String(),
                        NH = c.Decimal(precision: 18, scale: 2),
                        NL = c.Decimal(precision: 18, scale: 2),
                        TH = c.Decimal(precision: 18, scale: 2),
                        TL = c.Decimal(precision: 18, scale: 2),
                        Frequency = c.Int(nullable: false),
                        OLDVPMID = c.Int(),
                        VPMeasurementId = c.Int(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.WebBookingDoctors",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        UserID = c.Int(nullable: false),
                        DoctorID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ID);
            
            CreateTable(
                "dbo.WS_SendReport",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Location = c.String(),
                        DateEntered = c.DateTime(nullable: false),
                        Sent = c.Boolean(nullable: false),
                        Amended = c.Boolean(nullable: false),
                        AppointmentId = c.Int(nullable: false),
                        TestId = c.Int(nullable: false),
                        SendTypeId = c.Int(nullable: false),
                        URL = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.VP_MeasurementSavedValue", "VPMeasurementId", "dbo.VPMeasurements");
            DropForeignKey("dbo.ScheduleWeekDays", "ScheduleUserId", "dbo.ScheduleUsers");
            DropForeignKey("dbo.ScheduleDayResourceSlots", "ScheduleDayResourceId", "dbo.ScheduleDayResources");
            DropForeignKey("dbo.ScheduleDayResources", "ScheduleDayId", "dbo.ScheduleDays");
            DropForeignKey("dbo.ScheduleDays", "ScheduleId", "dbo.Schedules");
            DropForeignKey("dbo.RolePermissions", "ApplicationRoleId", "dbo.AspNetRoles");
            DropForeignKey("dbo.AspNetUserRoles", "RoleId", "dbo.AspNetRoles");
            DropForeignKey("dbo.RequisitionTemplate", "requisitionTypeId", "dbo.RequisitionType");
            DropForeignKey("dbo.RequisitionTemplate", "practiceId", "dbo.Practices");
            DropForeignKey("dbo.RequisitionResult", "requisitionId", "dbo.Requisition");
            DropForeignKey("dbo.RequisitionItems", "requisitionTypeId", "dbo.RequisitionType");
            DropForeignKey("dbo.Requisition", "requisitionStatus", "dbo.RequisitionStatus");
            DropForeignKey("dbo.Requisition", "requisitionTypeId", "dbo.RequisitionType");
            DropForeignKey("dbo.Requisition", "requisitionPatientId", "dbo.RequisitionPatient");
            DropForeignKey("dbo.RequisitionPatient", "requisitionTimeId", "dbo.RequisitionTime");
            DropForeignKey("dbo.RequisitionPatient", "patientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.ProblemListStandardCodings", "ProblemListId", "dbo.ProblemLists");
            DropForeignKey("dbo.PrescriptionDefaults", "TreatmentTypeId", "dbo.TreatmentTypes");
            DropForeignKey("dbo.PrescriptionDefaults", "PrescriptionStatusId", "dbo.PrescriptionStatus");
            DropForeignKey("dbo.PrescriptionDefaults", "ComplianceId", "dbo.Compliance");
            DropForeignKey("dbo.PatientPrescriptions", "TreatmentTypeId", "dbo.TreatmentTypes");
            DropForeignKey("dbo.PatientPrescriptions", "PrescriptionSetId", "dbo.PatientPrescriptionSets");
            DropForeignKey("dbo.PatientPrescriptions", "PrescriptionStatusId", "dbo.PrescriptionStatus");
            DropForeignKey("dbo.PatientPrescriptionsPrinted", "PrescriptionId", "dbo.PatientPrescriptions");
            DropForeignKey("dbo.PatientPrescriptions", "PatientMedicationId", "dbo.PatientMedications");
            DropForeignKey("dbo.PatientMedications", "MedicationSetId", "dbo.PatientMedicationSets");
            DropForeignKey("dbo.PatientMedications", "MedicationNoDinId", "dbo.MedicationNoDins");
            DropForeignKey("dbo.PatientMedications", "MedicationId", "dbo.QRYM_DRUG");
            DropForeignKey("dbo.PatientMedications", "DoseChangeReasonId", "dbo.DoseChangeReasons");
            DropForeignKey("dbo.PatientMedications", "DiscontinueReasonId", "dbo.DiscontinueReasons");
            DropForeignKey("dbo.PatientAllergies", "SeverityId", "dbo.Severity");
            DropForeignKey("dbo.PatientAllergies", "ReactionTypeId", "dbo.ReactionTypes");
            DropForeignKey("dbo.PatientAllergyIngredients", "PatientAllergy_Id", "dbo.PatientAllergies");
            DropForeignKey("dbo.PatientAllergyIngredientDetails", "AllergyIngredientId", "dbo.PatientAllergyIngredients");
            DropForeignKey("dbo.PatientAllergies", "MedicationNoDinId", "dbo.MedicationNoDins");
            DropForeignKey("dbo.PatientAllergies", "MedicationId", "dbo.QRYM_DRUG");
            DropForeignKey("dbo.PatientAllergies", "LifeStageId", "dbo.LifeStages");
            DropForeignKey("dbo.PatientAllergies", "AllergyStatusId", "dbo.AllergyStatuses");
            DropForeignKey("dbo.PastHealthStandardCodings", "PastHealthId", "dbo.PastHealths");
            DropForeignKey("dbo.OLISTestResultNomenclatures", "OLISTestResultCategoryId", "dbo.OLISTestResultCategories");
            DropForeignKey("dbo.OLISTestRequestNomenclatures", "OLISTestRequestSubCategoryId", "dbo.OLISTestRequestSubCategories");
            DropForeignKey("dbo.OLISTestRequestNomenclatures", "OLISTestRequestCategoryId", "dbo.OLISTestRequestCategories");
            DropForeignKey("dbo.OLISTestRequestNomenclatures", "OLISTestReportCategoryId", "dbo.OLISTestReportCategories");
            DropForeignKey("dbo.OfficeUrls", "urlTypeId", "dbo.OfficeUrlTypes");
            DropForeignKey("dbo.OfficeUrls", "officeId", "dbo.Office");
            DropForeignKey("dbo.OfficeRooms", "OfficeId", "dbo.Office");
            DropForeignKey("dbo.OfficeFaxFolder", "officeId", "dbo.Office");
            DropForeignKey("dbo.MedicationTemplates", "TemplateClassId", "dbo.MedicationTemplateClass");
            DropForeignKey("dbo.MedicationTemplateNoDins", "MedicationTemplateId", "dbo.MedicationTemplates");
            DropForeignKey("dbo.MedicationTemplateNoDins", "MedicationNoDinId", "dbo.MedicationNoDins");
            DropForeignKey("dbo.MedicationTemplateDins", "MedicationTemplateId", "dbo.MedicationTemplates");
            DropForeignKey("dbo.MedicationTemplateDins", "MedicationId", "dbo.QRYM_DRUG");
            DropForeignKey("dbo.QRYM_ROUTE", "DRUG_CODE", "dbo.QRYM_DRUG");
            DropForeignKey("dbo.QRYM_ingred", "DRUG_CODE", "dbo.QRYM_DRUG");
            DropForeignKey("dbo.QRYM_FORM", "DRUG_CODE", "dbo.QRYM_DRUG");
            DropForeignKey("dbo.QRYM_THERAPEUTIC_CLASS", "DRUG_CODE", "dbo.QRYM_DRUG");
            DropForeignKey("dbo.MeasurementBSARanges", "MeasurementRangeTypeId", "dbo.MeasurementRangeTypes");
            DropForeignKey("dbo.MeasurementRanges", "MeasurementRangeTypeId", "dbo.MeasurementRangeTypes");
            DropForeignKey("dbo.HL7MarkedSeen", "HL7ReportVersionId", "dbo.HL7ReportVersion");
            DropForeignKey("dbo.HL7MarkedSeen", "HL7ReportId", "dbo.HL7Report");
            DropForeignKey("dbo.FamilyHistoryStandardCodings", "FamilyHistoryId", "dbo.FamilyHistories");
            DropForeignKey("dbo.PatientCohorts", "CohortId", "dbo.Cohorts");
            DropForeignKey("dbo.CM_TaskReport", "CM_TaskDefinitionId", "dbo.CM_TaskDefinition");
            DropForeignKey("dbo.CM_TaskMessageRecipient", "CM_TaskMessageId", "dbo.CM_TaskMessage");
            DropForeignKey("dbo.CM_TaskMessage", "CM_TaskDefinitionId", "dbo.CM_TaskDefinition");
            DropForeignKey("dbo.CM_TaskDefinition", "patientRecordid", "dbo.PatientRecords");
            DropForeignKey("dbo.Billing_Het", "billingHehId", "dbo.Billing_Heh");
            DropForeignKey("dbo.Billing_Heh", "billingHebId", "dbo.Billing_Heb");
            DropForeignKey("dbo.Billing_Heb", "billId", "dbo.Bills");
            DropForeignKey("dbo.Bills", "billStatusId", "dbo.BillStatus");
            DropForeignKey("dbo.AppointmentTypes", "appointmentType_Id", "dbo.AppointmentTypes");
            DropForeignKey("dbo.ReportPhraseSavedTexts", "AppointmentTestLogID", "dbo.AppointmentTestLogs");
            DropForeignKey("dbo.ReportPhraseSavedTexts", "TopLevelReportPhraseID", "dbo.ReportPhrases");
            DropForeignKey("dbo.ReportPhraseSavedValues", "ReportPhraseID", "dbo.ReportPhrases");
            DropForeignKey("dbo.ReportPhraseByPractices", "ReportPhraseID", "dbo.ReportPhrases");
            DropForeignKey("dbo.ReportPhraseByDoctors", "ReportPhraseID", "dbo.ReportPhrases");
            DropForeignKey("dbo.MeasurementSavedValues", "AppointmentTestLogID", "dbo.AppointmentTestLogs");
            DropForeignKey("dbo.MeasurementSavedValues", "MeasurementOperatorId", "dbo.MeasurementOperators");
            DropForeignKey("dbo.MeasurementSavedValues", "MeasurementId", "dbo.Measurements");
            DropForeignKey("dbo.MeasurementMappings", "MeasurementID", "dbo.Measurements");
            DropForeignKey("dbo.MeasurementByPractices", "MeasurementID", "dbo.Measurements");
            DropForeignKey("dbo.Measurements", "MeasurementCategoryID", "dbo.MeasurementCategories");
            DropForeignKey("dbo.MeasurementCategoryTests", "MeasurementCategoryID", "dbo.MeasurementCategories");
            DropForeignKey("dbo.AppointmentStatusLogs", "AppointmentId", "dbo.Appointments");
            DropForeignKey("dbo.ReportReceiveds", "reportSubClassId", "dbo.ReportSubClasses");
            DropForeignKey("dbo.ReportReceiveds", "reportClassId", "dbo.ReportClasses");
            DropForeignKey("dbo.ReportSubClasses", "ReportClassId", "dbo.ReportClasses");
            DropForeignKey("dbo.ReportReceiveds", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.DoctorsReportRevieweds", "ReportReceivedId", "dbo.ReportReceiveds");
            DropForeignKey("dbo.ReportReceiveds", "appointmentId", "dbo.Appointments");
            DropForeignKey("dbo.ProblemListResidualInfoes", "ProblemListId", "dbo.ProblemLists");
            DropForeignKey("dbo.ProblemLists", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.PracticeTests", "TestId", "dbo.Tests");
            DropForeignKey("dbo.TestResources", "TestResourceTypeId", "dbo.TestResourceTypes");
            DropForeignKey("dbo.TestResources", "TestId", "dbo.Tests");
            DropForeignKey("dbo.TestResources", "permissionId", "dbo.Permissions");
            DropForeignKey("dbo.PracticeTests", "PracticeId", "dbo.Practices");
            DropForeignKey("dbo.WSRootCategories", "PracticeSpecialty_Id", "dbo.PracticeSpecialty");
            DropForeignKey("dbo.WSItems", "WSRootCategoryId", "dbo.WSRootCategories");
            DropForeignKey("dbo.WSRootCategories", "SpecialtyId", "dbo.Specialties");
            DropForeignKey("dbo.VPRootCategories", "PracticeSpecialty_Id", "dbo.PracticeSpecialty");
            DropForeignKey("dbo.VPItems", "VPRootCategoryId", "dbo.VPRootCategories");
            DropForeignKey("dbo.VPRootCategories", "SpecialtyId", "dbo.Specialties");
            DropForeignKey("dbo.PracticeSpecialty", "PracticeId", "dbo.Practices");
            DropForeignKey("dbo.ReportPhraseByDoctors", "PracticeDoctor_Id", "dbo.PracticeDoctors");
            DropForeignKey("dbo.PracticeDoctors", "PracticeId", "dbo.Practices");
            DropForeignKey("dbo.PracticeDoctorAppointmentType", "PracticeDoctorId", "dbo.PracticeDoctors");
            DropForeignKey("dbo.PracticeDoctors", "ApplicationUserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserRoles", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserLogins", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserClaims", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.PatientRecords", "PracticeId", "dbo.Practices");
            DropForeignKey("dbo.StaticIPs", "OfficeId", "dbo.Office");
            DropForeignKey("dbo.Office", "PracticeId", "dbo.Practices");
            DropForeignKey("dbo.OfficeOutlooks", "OfficeId", "dbo.Office");
            DropForeignKey("dbo.OfficeGroupBillingNumbers", "OfficeId", "dbo.Office");
            DropForeignKey("dbo.Specialties", "MasterId", "dbo.Masters");
            DropForeignKey("dbo.Practices", "MasterId", "dbo.Masters");
            DropForeignKey("dbo.PermissionTypes", "PermissionsBaseId", "dbo.PermissionsBases");
            DropForeignKey("dbo.Permissions", "PermissionTypeId", "dbo.PermissionTypes");
            DropForeignKey("dbo.PermissionsBases", "MasterId", "dbo.Masters");
            DropForeignKey("dbo.InsuranceCompanies", "MasterId", "dbo.Masters");
            DropForeignKey("dbo.ExternalDoctorPhoneNumbers", "ExternalDoctorId", "dbo.ExternalDoctors");
            DropForeignKey("dbo.ExternalDoctors", "MasterId", "dbo.Masters");
            DropForeignKey("dbo.ExternalDoctorAddresses", "ExternalDoctorId", "dbo.ExternalDoctors");
            DropForeignKey("dbo.PersonalHistoryResidualInfo", "PersonalHistoryId", "dbo.PersonalHistories");
            DropForeignKey("dbo.PersonalHistories", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.PatientMRNs", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.PastHealthResidualInfoes", "PastHealthId", "dbo.PastHealths");
            DropForeignKey("dbo.PastHealths", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.HL7Patient", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.HL7ResultNote", "HL7ResultId", "dbo.HL7Result");
            DropForeignKey("dbo.HL7Result", "HL7ReportVersionId", "dbo.HL7ReportVersion");
            DropForeignKey("dbo.HL7ReportNote", "HL7ReportVersionId", "dbo.HL7ReportVersion");
            DropForeignKey("dbo.HL7ReportVersion", "HL7ReportId", "dbo.HL7Report");
            DropForeignKey("dbo.HL7Report", "HL7PatientId", "dbo.HL7Patient");
            DropForeignKey("dbo.HL7ReportVersion", "HL7MessageId", "dbo.HL7Message");
            DropForeignKey("dbo.HL7Patient", "HL7MessageId", "dbo.HL7Message");
            DropForeignKey("dbo.FamilyHistoryResidualInfoes", "FamilyHistoryId", "dbo.FamilyHistories");
            DropForeignKey("dbo.FamilyHistories", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.DemographicsPhoneNumbers", "DemographicId", "dbo.Demographics");
            DropForeignKey("dbo.Demographics", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.DemographicsEnrollments", "DemographicsMainResponsiblePhysician_Id", "dbo.DemographicsMainResponsiblePhysicians");
            DropForeignKey("dbo.DemographicsMainResponsiblePhysicians", "DemographicId", "dbo.Demographics");
            DropForeignKey("dbo.DemographicsHealthCards", "DemographicId", "dbo.Demographics");
            DropForeignKey("dbo.DemographicsFamilyDoctors", "DemographicId", "dbo.Demographics");
            DropForeignKey("dbo.DemographicsNextOfKins", "DemographicId", "dbo.Demographics");
            DropForeignKey("dbo.DemographicsContactPhoneNumbers", "DemographicsContactId", "dbo.DemographicsNextOfKins");
            DropForeignKey("dbo.DemographicsDefaultReferralDoctors", "DemographicId", "dbo.Demographics");
            DropForeignKey("dbo.DemographicsAssociatedDoctors", "DemographicId", "dbo.Demographics");
            DropForeignKey("dbo.DemographicsAddresses", "DemographicId", "dbo.Demographics");
            DropForeignKey("dbo.Appointments", "PatientRecordId", "dbo.PatientRecords");
            DropForeignKey("dbo.TestReportingDoctors", "AppointmentTestId", "dbo.AppointmentTest");
            DropForeignKey("dbo.AppointmentTest", "AppointmentTestStatusId", "dbo.AppointmentTestStatus");
            DropForeignKey("dbo.AppointmentTest", "AppointmentId", "dbo.Appointments");
            DropForeignKey("dbo.AppointmentProviders", "AppointmentId", "dbo.Appointments");
            DropForeignKey("dbo.AppointmentPreconditons", "AppointmentId", "dbo.Appointments");
            DropForeignKey("dbo.AppointmntModifiers", "AppointmentId", "dbo.Appointments");
            DropIndex("dbo.VP_MeasurementSavedValue", new[] { "VPMeasurementId" });
            DropIndex("dbo.ScheduleWeekDays", new[] { "ScheduleUserId" });
            DropIndex("dbo.ScheduleDayResourceSlots", new[] { "ScheduleDayResourceId" });
            DropIndex("dbo.ScheduleDays", new[] { "ScheduleId" });
            DropIndex("dbo.ScheduleDayResources", new[] { "ScheduleDayId" });
            DropIndex("dbo.AspNetRoles", "RoleNameIndex");
            DropIndex("dbo.RolePermissions", new[] { "ApplicationRoleId" });
            DropIndex("dbo.RequisitionTemplate", new[] { "requisitionTypeId" });
            DropIndex("dbo.RequisitionTemplate", new[] { "practiceId" });
            DropIndex("dbo.RequisitionResult", new[] { "requisitionId" });
            DropIndex("dbo.RequisitionItems", new[] { "requisitionTypeId" });
            DropIndex("dbo.RequisitionPatient", new[] { "requisitionTimeId" });
            DropIndex("dbo.RequisitionPatient", new[] { "patientRecordId" });
            DropIndex("dbo.Requisition", new[] { "requisitionStatus" });
            DropIndex("dbo.Requisition", new[] { "requisitionTypeId" });
            DropIndex("dbo.Requisition", new[] { "requisitionPatientId" });
            DropIndex("dbo.ProblemListStandardCodings", new[] { "ProblemListId" });
            DropIndex("dbo.PrescriptionDefaults", new[] { "ComplianceId" });
            DropIndex("dbo.PrescriptionDefaults", new[] { "TreatmentTypeId" });
            DropIndex("dbo.PrescriptionDefaults", new[] { "PrescriptionStatusId" });
            DropIndex("dbo.PatientPrescriptionsPrinted", new[] { "PrescriptionId" });
            DropIndex("dbo.PatientPrescriptions", new[] { "TreatmentTypeId" });
            DropIndex("dbo.PatientPrescriptions", new[] { "PrescriptionStatusId" });
            DropIndex("dbo.PatientPrescriptions", new[] { "PatientMedicationId" });
            DropIndex("dbo.PatientPrescriptions", new[] { "PrescriptionSetId" });
            DropIndex("dbo.PatientMedications", new[] { "MedicationNoDinId" });
            DropIndex("dbo.PatientMedications", new[] { "MedicationId" });
            DropIndex("dbo.PatientMedications", new[] { "DiscontinueReasonId" });
            DropIndex("dbo.PatientMedications", new[] { "DoseChangeReasonId" });
            DropIndex("dbo.PatientMedications", new[] { "MedicationSetId" });
            DropIndex("dbo.PatientAllergyIngredientDetails", new[] { "AllergyIngredientId" });
            DropIndex("dbo.PatientAllergyIngredients", new[] { "PatientAllergy_Id" });
            DropIndex("dbo.PatientAllergyIngredients", "IX_AllergyIngredient");
            DropIndex("dbo.PatientAllergies", new[] { "MedicationNoDinId" });
            DropIndex("dbo.PatientAllergies", new[] { "MedicationId" });
            DropIndex("dbo.PatientAllergies", new[] { "AllergyStatusId" });
            DropIndex("dbo.PatientAllergies", new[] { "SeverityId" });
            DropIndex("dbo.PatientAllergies", new[] { "ReactionTypeId" });
            DropIndex("dbo.PatientAllergies", new[] { "LifeStageId" });
            DropIndex("dbo.PastHealthStandardCodings", new[] { "PastHealthId" });
            DropIndex("dbo.OLISTestResultNomenclatures", new[] { "OLISTestResultCategoryId" });
            DropIndex("dbo.OLISTestRequestNomenclatures", new[] { "OLISTestReportCategoryId" });
            DropIndex("dbo.OLISTestRequestNomenclatures", new[] { "OLISTestRequestSubCategoryId" });
            DropIndex("dbo.OLISTestRequestNomenclatures", new[] { "OLISTestRequestCategoryId" });
            DropIndex("dbo.OfficeUrls", new[] { "urlTypeId" });
            DropIndex("dbo.OfficeUrls", new[] { "officeId" });
            DropIndex("dbo.OfficeRooms", new[] { "OfficeId" });
            DropIndex("dbo.OfficeFaxFolder", new[] { "officeId" });
            DropIndex("dbo.MedicationTemplateNoDins", new[] { "MedicationNoDinId" });
            DropIndex("dbo.MedicationTemplateNoDins", new[] { "MedicationTemplateId" });
            DropIndex("dbo.MedicationTemplateDins", new[] { "MedicationId" });
            DropIndex("dbo.MedicationTemplateDins", new[] { "MedicationTemplateId" });
            DropIndex("dbo.MedicationTemplates", new[] { "TemplateClassId" });
            DropIndex("dbo.QRYM_ROUTE", new[] { "DRUG_CODE" });
            DropIndex("dbo.QRYM_ingred", new[] { "DRUG_CODE" });
            DropIndex("dbo.QRYM_FORM", new[] { "DRUG_CODE" });
            DropIndex("dbo.QRYM_THERAPEUTIC_CLASS", new[] { "DRUG_CODE" });
            DropIndex("dbo.MeasurementRanges", new[] { "MeasurementRangeTypeId" });
            DropIndex("dbo.MeasurementBSARanges", new[] { "MeasurementRangeTypeId" });
            DropIndex("dbo.HL7MarkedSeen", new[] { "HL7ReportVersionId" });
            DropIndex("dbo.HL7MarkedSeen", new[] { "HL7ReportId" });
            DropIndex("dbo.FamilyHistoryStandardCodings", new[] { "FamilyHistoryId" });
            DropIndex("dbo.PatientCohorts", new[] { "CohortId" });
            DropIndex("dbo.CM_TaskReport", new[] { "CM_TaskDefinitionId" });
            DropIndex("dbo.CM_TaskMessageRecipient", new[] { "CM_TaskMessageId" });
            DropIndex("dbo.CM_TaskMessage", new[] { "CM_TaskDefinitionId" });
            DropIndex("dbo.CM_TaskDefinition", new[] { "patientRecordid" });
            DropIndex("dbo.Billing_Het", new[] { "billingHehId" });
            DropIndex("dbo.Billing_Heh", new[] { "billingHebId" });
            DropIndex("dbo.Bills", new[] { "billStatusId" });
            DropIndex("dbo.Billing_Heb", new[] { "billId" });
            DropIndex("dbo.AppointmentTypes", new[] { "appointmentType_Id" });
            DropIndex("dbo.ReportPhraseSavedValues", new[] { "ReportPhraseID" });
            DropIndex("dbo.ReportPhraseByPractices", new[] { "ReportPhraseID" });
            DropIndex("dbo.ReportPhraseSavedTexts", new[] { "TopLevelReportPhraseID" });
            DropIndex("dbo.ReportPhraseSavedTexts", new[] { "AppointmentTestLogID" });
            DropIndex("dbo.MeasurementMappings", new[] { "MeasurementID" });
            DropIndex("dbo.MeasurementByPractices", new[] { "MeasurementID" });
            DropIndex("dbo.MeasurementCategoryTests", new[] { "MeasurementCategoryID" });
            DropIndex("dbo.Measurements", new[] { "MeasurementCategoryID" });
            DropIndex("dbo.MeasurementSavedValues", new[] { "MeasurementOperatorId" });
            DropIndex("dbo.MeasurementSavedValues", new[] { "MeasurementId" });
            DropIndex("dbo.MeasurementSavedValues", new[] { "AppointmentTestLogID" });
            DropIndex("dbo.AppointmentStatusLogs", new[] { "AppointmentId" });
            DropIndex("dbo.ReportSubClasses", new[] { "ReportClassId" });
            DropIndex("dbo.DoctorsReportRevieweds", new[] { "ReportReceivedId" });
            DropIndex("dbo.ReportReceiveds", new[] { "PatientRecordId" });
            DropIndex("dbo.ReportReceiveds", new[] { "appointmentId" });
            DropIndex("dbo.ReportReceiveds", new[] { "reportSubClassId" });
            DropIndex("dbo.ReportReceiveds", new[] { "reportClassId" });
            DropIndex("dbo.ProblemListResidualInfoes", new[] { "ProblemListId" });
            DropIndex("dbo.ProblemLists", new[] { "PatientRecordId" });
            DropIndex("dbo.TestResources", new[] { "TestId" });
            DropIndex("dbo.TestResources", new[] { "TestResourceTypeId" });
            DropIndex("dbo.TestResources", new[] { "permissionId" });
            DropIndex("dbo.PracticeTests", new[] { "PracticeId" });
            DropIndex("dbo.PracticeTests", new[] { "TestId" });
            DropIndex("dbo.WSItems", new[] { "WSRootCategoryId" });
            DropIndex("dbo.WSRootCategories", new[] { "PracticeSpecialty_Id" });
            DropIndex("dbo.WSRootCategories", new[] { "SpecialtyId" });
            DropIndex("dbo.VPItems", new[] { "VPRootCategoryId" });
            DropIndex("dbo.VPRootCategories", new[] { "PracticeSpecialty_Id" });
            DropIndex("dbo.VPRootCategories", new[] { "SpecialtyId" });
            DropIndex("dbo.PracticeSpecialty", new[] { "PracticeId" });
            DropIndex("dbo.ReportPhraseByDoctors", new[] { "PracticeDoctor_Id" });
            DropIndex("dbo.ReportPhraseByDoctors", new[] { "ReportPhraseID" });
            DropIndex("dbo.PracticeDoctorAppointmentType", new[] { "PracticeDoctorId" });
            DropIndex("dbo.AspNetUserRoles", new[] { "RoleId" });
            DropIndex("dbo.AspNetUserRoles", new[] { "UserId" });
            DropIndex("dbo.AspNetUserLogins", new[] { "UserId" });
            DropIndex("dbo.AspNetUserClaims", new[] { "UserId" });
            DropIndex("dbo.AspNetUsers", "UserNameIndex");
            DropIndex("dbo.PracticeDoctors", new[] { "ApplicationUserId" });
            DropIndex("dbo.PracticeDoctors", new[] { "PracticeId" });
            DropIndex("dbo.StaticIPs", new[] { "OfficeId" });
            DropIndex("dbo.OfficeOutlooks", new[] { "OfficeId" });
            DropIndex("dbo.OfficeGroupBillingNumbers", new[] { "OfficeId" });
            DropIndex("dbo.Office", new[] { "PracticeId" });
            DropIndex("dbo.Specialties", new[] { "MasterId" });
            DropIndex("dbo.Permissions", new[] { "PermissionTypeId" });
            DropIndex("dbo.PermissionTypes", new[] { "PermissionsBaseId" });
            DropIndex("dbo.PermissionsBases", new[] { "MasterId" });
            DropIndex("dbo.InsuranceCompanies", new[] { "MasterId" });
            DropIndex("dbo.ExternalDoctorPhoneNumbers", new[] { "ExternalDoctorId" });
            DropIndex("dbo.ExternalDoctorAddresses", new[] { "ExternalDoctorId" });
            DropIndex("dbo.ExternalDoctors", new[] { "MasterId" });
            DropIndex("dbo.Practices", new[] { "MasterId" });
            DropIndex("dbo.PersonalHistoryResidualInfo", new[] { "PersonalHistoryId" });
            DropIndex("dbo.PersonalHistories", new[] { "PatientRecordId" });
            DropIndex("dbo.PatientMRNs", new[] { "PatientRecordId" });
            DropIndex("dbo.PastHealthResidualInfoes", new[] { "PastHealthId" });
            DropIndex("dbo.PastHealths", new[] { "PatientRecordId" });
            DropIndex("dbo.HL7ResultNote", new[] { "HL7ResultId" });
            DropIndex("dbo.HL7Result", new[] { "HL7ReportVersionId" });
            DropIndex("dbo.HL7ReportNote", new[] { "HL7ReportVersionId" });
            DropIndex("dbo.HL7Report", new[] { "HL7PatientId" });
            DropIndex("dbo.HL7ReportVersion", new[] { "HL7ReportId" });
            DropIndex("dbo.HL7ReportVersion", new[] { "HL7MessageId" });
            DropIndex("dbo.HL7Patient", new[] { "PatientRecordId" });
            DropIndex("dbo.HL7Patient", new[] { "HL7MessageId" });
            DropIndex("dbo.FamilyHistoryResidualInfoes", new[] { "FamilyHistoryId" });
            DropIndex("dbo.FamilyHistories", new[] { "PatientRecordId" });
            DropIndex("dbo.DemographicsPhoneNumbers", new[] { "DemographicId" });
            DropIndex("dbo.DemographicsEnrollments", new[] { "DemographicsMainResponsiblePhysician_Id" });
            DropIndex("dbo.DemographicsMainResponsiblePhysicians", new[] { "DemographicId" });
            DropIndex("dbo.DemographicsHealthCards", new[] { "DemographicId" });
            DropIndex("dbo.DemographicsFamilyDoctors", new[] { "DemographicId" });
            DropIndex("dbo.DemographicsContactPhoneNumbers", new[] { "DemographicsContactId" });
            DropIndex("dbo.DemographicsNextOfKins", new[] { "DemographicId" });
            DropIndex("dbo.DemographicsDefaultReferralDoctors", new[] { "DemographicId" });
            DropIndex("dbo.DemographicsAssociatedDoctors", new[] { "DemographicId" });
            DropIndex("dbo.DemographicsAddresses", new[] { "DemographicId" });
            DropIndex("dbo.Demographics", new[] { "PatientRecordId" });
            DropIndex("dbo.PatientRecords", new[] { "PracticeId" });
            DropIndex("dbo.TestReportingDoctors", new[] { "AppointmentTestId" });
            DropIndex("dbo.AppointmentTest", new[] { "AppointmentId" });
            DropIndex("dbo.AppointmentTest", new[] { "AppointmentTestStatusId" });
            DropIndex("dbo.AppointmentProviders", new[] { "AppointmentId" });
            DropIndex("dbo.AppointmntModifiers", new[] { "AppointmentId" });
            DropIndex("dbo.Appointments", new[] { "PatientRecordId" });
            DropIndex("dbo.AppointmentPreconditons", new[] { "AppointmentId" });
            DropTable("dbo.WS_SendReport");
            DropTable("dbo.WebBookingDoctors");
            DropTable("dbo.VPTemplateFields");
            DropTable("dbo.VPReportPhraseOptions");
            DropTable("dbo.VPReportPhraseByDoctors");
            DropTable("dbo.VPReportPhrases");
            DropTable("dbo.VPOptions");
            DropTable("dbo.VPMeasurementOptions");
            DropTable("dbo.VPCategoryOptions");
            DropTable("dbo.VPCategories");
            DropTable("dbo.VP_Template_Patient_Detail");
            DropTable("dbo.VP_Template_Patient_Data");
            DropTable("dbo.VP_Template_Detail");
            DropTable("dbo.VP_Template");
            DropTable("dbo.VP_SendReport");
            DropTable("dbo.VP_ReportPhrasesSavedValue");
            DropTable("dbo.VP_ReportPhrasesSavedText");
            DropTable("dbo.VP_ReportPhrases_Skipped");
            DropTable("dbo.VP_ReportPhrase_Custom");
            DropTable("dbo.VP_Privacy_Notes");
            DropTable("dbo.VPMeasurements");
            DropTable("dbo.VP_MeasurementSavedValue");
            DropTable("dbo.VP_CPP_Setting");
            DropTable("dbo.VP_CPP_RiskFactor");
            DropTable("dbo.VP_CPP_Problem_List");
            DropTable("dbo.VP_CPP_Item");
            DropTable("dbo.VP_CPP_ImmunizationType");
            DropTable("dbo.VP_CPP_ImmunizationStatus");
            DropTable("dbo.VP_CPP_Immunization");
            DropTable("dbo.VP_CPP_FamilyHistory");
            DropTable("dbo.VP_CPP_Category");
            DropTable("dbo.VP_CPP_Alert");
            DropTable("dbo.VP_AppointmentTestLog");
            DropTable("dbo.UserOffices");
            DropTable("dbo.UserMobileNetworks");
            DropTable("dbo.UserLocations");
            DropTable("dbo.UserDoctors");
            DropTable("dbo.UserBillingDoctors");
            DropTable("dbo.TriageUrgencies");
            DropTable("dbo.TestSequences");
            DropTable("dbo.TestGroupDetails");
            DropTable("dbo.TestGroups");
            DropTable("dbo.StoreInventoryTypes");
            DropTable("dbo.StoreInventoryStatus");
            DropTable("dbo.StoreInventories");
            DropTable("dbo.SigCodes");
            DropTable("dbo.SendTypes");
            DropTable("dbo.ScheduleWeekDays");
            DropTable("dbo.ScheduleUsers");
            DropTable("dbo.ScheduleDayResourceSlots");
            DropTable("dbo.Schedules");
            DropTable("dbo.ScheduleDays");
            DropTable("dbo.ScheduleDayResources");
            DropTable("dbo.ScheduleColorSchemas");
            DropTable("dbo.AspNetRoles");
            DropTable("dbo.RolePermissions");
            DropTable("dbo.RequisitionTemplate");
            DropTable("dbo.RequisitionResult");
            DropTable("dbo.RequisitionItems");
            DropTable("dbo.RequisitionStatus");
            DropTable("dbo.RequisitionType");
            DropTable("dbo.RequisitionTime");
            DropTable("dbo.RequisitionPatient");
            DropTable("dbo.Requisition");
            DropTable("dbo.ReportPhraseNormals");
            DropTable("dbo.ReportPhraseMeasurmentCategoryScrolls");
            DropTable("dbo.ReportPhraseMeasurmentCategories");
            DropTable("dbo.ReportPhrase_Custom");
            DropTable("dbo.Reasons");
            DropTable("dbo.ProstaticValves");
            DropTable("dbo.ProblemListStandardCodings");
            DropTable("dbo.PrescriptionDefaults");
            DropTable("dbo.PatientProstaticValves");
            DropTable("dbo.TreatmentTypes");
            DropTable("dbo.PatientPrescriptionSets");
            DropTable("dbo.PrescriptionStatus");
            DropTable("dbo.PatientPrescriptionsPrinted");
            DropTable("dbo.PatientPrescriptions");
            DropTable("dbo.PatientMedicationSets");
            DropTable("dbo.PatientMedications");
            DropTable("dbo.Severity");
            DropTable("dbo.ReactionTypes");
            DropTable("dbo.PatientAllergyIngredientDetails");
            DropTable("dbo.PatientAllergyIngredients");
            DropTable("dbo.PatientAllergies");
            DropTable("dbo.PastHealthStandardCodings");
            DropTable("dbo.OLISTestResultNomenclatures");
            DropTable("dbo.OLISTestResultCategories");
            DropTable("dbo.OLISTestRequestSubCategories");
            DropTable("dbo.OLISTestRequestNomenclatures");
            DropTable("dbo.OLISTestRequestCategories");
            DropTable("dbo.OLISTestReportCategories");
            DropTable("dbo.OHIPTimeLimitedFeeCodes");
            DropTable("dbo.OfficeUrlTypes");
            DropTable("dbo.OfficeUrls");
            DropTable("dbo.OfficeRooms");
            DropTable("dbo.OfficeFaxFolder");
            DropTable("dbo.MobileNetworks");
            DropTable("dbo.MedicationTemplateNoDins");
            DropTable("dbo.MedicationTemplateDins");
            DropTable("dbo.MedicationTemplates");
            DropTable("dbo.MedicationTemplateClass");
            DropTable("dbo.MedicationNoDins");
            DropTable("dbo.MedicationDefaults");
            DropTable("dbo.QRYM_ROUTE");
            DropTable("dbo.QRYM_ingred");
            DropTable("dbo.QRYM_FORM");
            DropTable("dbo.QRYM_DRUG");
            DropTable("dbo.QRYM_THERAPEUTIC_CLASS");
            DropTable("dbo.MeasurementRangeTexts");
            DropTable("dbo.MeasurementRanges");
            DropTable("dbo.MeasurementRangeTypes");
            DropTable("dbo.MeasurementBSARanges");
            DropTable("dbo.LooseReportCategories");
            DropTable("dbo.LifeStages");
            DropTable("dbo.LabResults");
            DropTable("dbo.ImportEventLogs");
            DropTable("dbo.ICD10");
            DropTable("dbo.Hospitals");
            DropTable("dbo.HL7TestDescription");
            DropTable("dbo.HL7ReportDoctor");
            DropTable("dbo.HL7MarkedSeen");
            DropTable("dbo.HL7Lab");
            DropTable("dbo.HL7Coding");
            DropTable("dbo.Groups");
            DropTable("dbo.FamilyHistoryStandardCodings");
            DropTable("dbo.ExternalReports");
            DropTable("dbo.EventLog_Transaction");
            DropTable("dbo.DoseChangeReasons");
            DropTable("dbo.DocServices");
            DropTable("dbo.DiscontinueReasons");
            DropTable("dbo.DiagnoseCodes");
            DropTable("dbo.ConsultCodes");
            DropTable("dbo.Compliance");
            DropTable("dbo.PatientCohorts");
            DropTable("dbo.Cohorts");
            DropTable("dbo.CM_TaskReport");
            DropTable("dbo.CM_TaskMessageRecipient");
            DropTable("dbo.CM_TaskMessage");
            DropTable("dbo.CM_TaskDefinition");
            DropTable("dbo.Billing_Type");
            DropTable("dbo.BillingType_Office_Test");
            DropTable("dbo.Billing_TestRare");
            DropTable("dbo.Billing_Het");
            DropTable("dbo.Billing_Heh");
            DropTable("dbo.BillStatus");
            DropTable("dbo.Bills");
            DropTable("dbo.Billing_Heb");
            DropTable("dbo.Billing_Group");
            DropTable("dbo.Billing_FileSequence");
            DropTable("dbo.Billing_DiagnoseRare");
            DropTable("dbo.Billing_ConsultRare");
            DropTable("dbo.AppointmentTypes");
            DropTable("dbo.ReportPhraseSavedValues");
            DropTable("dbo.ReportPhraseByPractices");
            DropTable("dbo.ReportPhrases");
            DropTable("dbo.ReportPhraseSavedTexts");
            DropTable("dbo.MeasurementOperators");
            DropTable("dbo.MeasurementMappings");
            DropTable("dbo.MeasurementByPractices");
            DropTable("dbo.MeasurementCategoryTests");
            DropTable("dbo.MeasurementCategories");
            DropTable("dbo.Measurements");
            DropTable("dbo.MeasurementSavedValues");
            DropTable("dbo.AppointmentTestLogs");
            DropTable("dbo.AppointmentStatusLogs");
            DropTable("dbo.ReportSubClasses");
            DropTable("dbo.ReportClasses");
            DropTable("dbo.DoctorsReportRevieweds");
            DropTable("dbo.ReportReceiveds");
            DropTable("dbo.ProblemListResidualInfoes");
            DropTable("dbo.ProblemLists");
            DropTable("dbo.TestResourceTypes");
            DropTable("dbo.TestResources");
            DropTable("dbo.Tests");
            DropTable("dbo.PracticeTests");
            DropTable("dbo.WSItems");
            DropTable("dbo.WSRootCategories");
            DropTable("dbo.VPItems");
            DropTable("dbo.VPRootCategories");
            DropTable("dbo.PracticeSpecialty");
            DropTable("dbo.ReportPhraseByDoctors");
            DropTable("dbo.PracticeDoctorAppointmentType");
            DropTable("dbo.AspNetUserRoles");
            DropTable("dbo.AspNetUserLogins");
            DropTable("dbo.AspNetUserClaims");
            DropTable("dbo.AspNetUsers");
            DropTable("dbo.PracticeDoctors");
            DropTable("dbo.StaticIPs");
            DropTable("dbo.OfficeOutlooks");
            DropTable("dbo.OfficeGroupBillingNumbers");
            DropTable("dbo.Office");
            DropTable("dbo.Specialties");
            DropTable("dbo.Permissions");
            DropTable("dbo.PermissionTypes");
            DropTable("dbo.PermissionsBases");
            DropTable("dbo.InsuranceCompanies");
            DropTable("dbo.ExternalDoctorPhoneNumbers");
            DropTable("dbo.ExternalDoctorAddresses");
            DropTable("dbo.ExternalDoctors");
            DropTable("dbo.Masters");
            DropTable("dbo.Practices");
            DropTable("dbo.PersonalHistoryResidualInfo");
            DropTable("dbo.PersonalHistories");
            DropTable("dbo.PatientMRNs");
            DropTable("dbo.PastHealthResidualInfoes");
            DropTable("dbo.PastHealths");
            DropTable("dbo.HL7ResultNote");
            DropTable("dbo.HL7Result");
            DropTable("dbo.HL7ReportNote");
            DropTable("dbo.HL7Report");
            DropTable("dbo.HL7ReportVersion");
            DropTable("dbo.HL7Message");
            DropTable("dbo.HL7Patient");
            DropTable("dbo.FamilyHistoryResidualInfoes");
            DropTable("dbo.FamilyHistories");
            DropTable("dbo.DemographicsPhoneNumbers");
            DropTable("dbo.DemographicsEnrollments");
            DropTable("dbo.DemographicsMainResponsiblePhysicians");
            DropTable("dbo.DemographicsHealthCards");
            DropTable("dbo.DemographicsFamilyDoctors");
            DropTable("dbo.DemographicsContactPhoneNumbers");
            DropTable("dbo.DemographicsNextOfKins");
            DropTable("dbo.DemographicsDefaultReferralDoctors");
            DropTable("dbo.DemographicsAssociatedDoctors");
            DropTable("dbo.DemographicsAddresses");
            DropTable("dbo.Demographics");
            DropTable("dbo.PatientRecords");
            DropTable("dbo.TestReportingDoctors");
            DropTable("dbo.AppointmentTestStatus");
            DropTable("dbo.AppointmentTest");
            DropTable("dbo.AppointmentProviders");
            DropTable("dbo.AppointmntModifiers");
            DropTable("dbo.Appointments");
            DropTable("dbo.AppointmentPreconditons");
            DropTable("dbo.AppointmentBills");
            DropTable("dbo.AllergyStatuses");
        }
    }
}
