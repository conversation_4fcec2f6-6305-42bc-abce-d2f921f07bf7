// <auto-generated />
namespace Cerebrum.Data.Migrations.Cerebrum
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.0-30225")]
    public sealed partial class AddedNewColEnum : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddedNewColEnum));
        
        string IMigrationMetadata.Id
        {
            get { return "201702272123537_AddedNewColEnum"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
