namespace Cerebrum.Data
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.Linq;

    internal sealed class ConfigurationInterfaceLog : DbMigrationsConfiguration<Cerebrum.Data.InterfaceLog.InterfaceLogContext>
    {
        public ConfigurationInterfaceLog()
        {
            AutomaticMigrationsEnabled = false;
        }

        protected override void Seed(Cerebrum.Data.InterfaceLog.InterfaceLogContext context)
        {
            //  This method will be called after migrating to the latest version.
        }
    }
}
