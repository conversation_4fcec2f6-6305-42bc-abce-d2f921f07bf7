namespace Cerebrum.Data
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class koo : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.VP_CPP_ImmunizationType", "Age", c => c.Int(nullable: false));
            AddColumn("dbo.VP_CPP_ImmunizationType", "Operator", c => c.Int(nullable: false));
            AddColumn("dbo.VP_CPP_ImmunizationType", "Gender", c => c.Int(nullable: false));
        }
        
        public override void Down()
        {
            DropColumn("dbo.VP_CPP_ImmunizationType", "Gender");
            DropColumn("dbo.VP_CPP_ImmunizationType", "Operator");
            DropColumn("dbo.VP_CPP_ImmunizationType", "Age");
        }
    }
}
