namespace Cerebrum.Data
{
    using AwareMD.Cerebrum.Shared.Enums;
    using System;
    using System.Collections.Generic;
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.Linq;

    internal sealed class Configuration : DbMigrationsConfiguration<Cerebrum.Data.CerebrumContext>
    {
        readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public Configuration()
        {
            AutomaticMigrationsEnabled = false;
            ContextKey = "Cerebrum.Data.CerebrumContext";
            //ContextType = typeof(CerebrumContext);
        }

        protected override void Seed(Cerebrum.Data.CerebrumContext context)
        {
            if (!context.Master.Any())
            {
                var master = new Master { MasterName = "Cerebrum 3.0 Database", GenerationDate = DateTime.Now, Id = 1 };
                try
                {

                    context.Master.AddOrUpdate(m => m.MasterName, master);
                    context.SaveChanges();
                }
                catch (Exception ex)
                {
                    _log.Error($"Master Creation error.{ex.Message}");
                }
                master = master ?? context.Master.FirstOrDefault();
                AddLanguages(context);
                AddModalities(context);
                AddInsuranceCompanies(master, context);
                AddSpecialties(master, context);

                AddPrescriptionStatuses(context);
                AddApointmentTypes(context);
                AddHospitals(context);
                AddStorInventoryType(context);
                AddStorInventoryStatus(context);
                AddReasons(context);
                AddMobileNetworks(context);
                AddDocServices(context);
                AddOfficeUrlTypes(context);
                AddLooseReportCategories(context);
                AddTestResourceTypes(context);
                AddTreatments(context);
                AppointmentStatus(context);
                RequisitionInitializer(context);
                PermissionsInitializer(context);
                AddScheduleSchemaColor(context);
                TriageUrgencyInitializer(context);
                TriageStatusInitializer(context);
                AddReportClasses(context);
                LISInitializer(context);
                //AddHDAdmissionTypes(context);
            }
        }
        private void AddLanguages(CerebrumContext c)
        {
            var lan = new List<Cerebrum.Data.Entities.SpokenLanguage>
            {
                new Entities.SpokenLanguage {LanguageCode= "ENG",LanguageName="English",CountryCode="CA",Country="Canada",IsActive=true},
                new Entities.SpokenLanguage {LanguageCode= "FRC",LanguageName="French",CountryCode="CA",Country="Canada",IsActive=true},
                new Entities.SpokenLanguage {LanguageCode= "RUS",LanguageName="Russian",CountryCode="RU",Country="Russia",IsActive=true},
                new Entities.SpokenLanguage {LanguageCode= "",LanguageName="Punjabi",CountryCode="IN",Country="India",IsActive=true},
                new Entities.SpokenLanguage {LanguageCode= "PTB",LanguageName="Portuguese",CountryCode="BR",Country="Brazil",IsActive=true},
                new Entities.SpokenLanguage {LanguageCode= "ESM",LanguageName="Spanish",CountryCode="MX",Country="Mexico",IsActive=true},
            };
        }
        private void AddCountryProvince(CerebrumContext c)
        {
            var cp = new List<CountryProvince>
            {
                new CountryProvince { name="Canada" ,children=new List<CountryProvince>
                {
                    new CountryProvince { name="ON"},
                    new CountryProvince { name="QC"},
                    new CountryProvince { name="BC"},
                }
                }
            };
            //cp.ForEach(p=>c.CountryProvinces.AddOrUpdate)
        }
        private void AddModalities(CerebrumContext c)
        {
            var modalities = new List<Modality>() {
                new Modality {modalityName="ECG" },
                new Modality {modalityName="US",description="E1" },
                new Modality {modalityName="OT",description="GXT" },
                new Modality {modalityName="NM",description="NUC" },
            };
            modalities.ForEach(f => c.Modalities.AddOrUpdate(m => m.modalityName, f));
            c.SaveChanges();
        }
        private void AddHDAdmissionTypes(CerebrumContext c)
        {
            var hdAdmissionTypes = new List<HDAdmissionType>() {
                new HDAdmissionType { Description = "In", IsActive = true, DisplayOrder = 1 },
                new HDAdmissionType { Description = "Out", IsActive = true, DisplayOrder = 2 }
            };
            hdAdmissionTypes.ForEach(f => c.HDAdmissionTypes.AddOrUpdate(m => m.Description, f));
            c.SaveChanges();
        }
        private void AddInsuranceCompanies(Master master, CerebrumContext c)
        {
            var ins = new List<InsuranceCompany> {
                new InsuranceCompany {MasterId=master.Id, name = "Manulife Financial", address = "500 King St. Waterloo,ON N2J 4Z6" },
                new InsuranceCompany {MasterId=master.Id, name = "SunLife (UHIP)", address = "22 Frederick St. Kitchener,ON N2H 6M6" }
            };
            ins.ForEach(f => c.InsuranceCompanys.AddOrUpdate(i => i.name, f));
        }
        private void AddSpecialties(Master master, CerebrumContext c)
        {
            var specialties = new List<Specialty>
            {
                new Specialty {MasterId=master.Id,title = "Cardiology", code = 60},
                new Specialty {MasterId=master.Id,title = "Family Doctor", code = 00},
                new Specialty {MasterId=master.Id,title = "Nuclear Medicine", code = 63},
                new Specialty {MasterId=master.Id,title = "Respiratory Diseases", code = 47},
                new Specialty {MasterId=master.Id,title = "Paediatrics", code = 26},
                new Specialty {MasterId=master.Id,title = "Internal Medicine", code = 13},
                new Specialty {MasterId=master.Id,title = "Vascular Surgery", code = 17},
                new Specialty {MasterId=master.Id,title = "TBD", code = 0},
                new Specialty {MasterId=master.Id,title = "Endocrinology", code = 15},
                new Specialty {MasterId=master.Id,title = "Internal Medicine", code = 13},
                new Specialty {MasterId=master.Id,title = "Radiology", code = 33}
            };
            specialties.ForEach(f => c.Specialties.AddOrUpdate(a => a.title, f));
            //c.Specialties.AddOrUpdate(f => f.title, specialties);
            c.SaveChanges();
        }
        private void AddApointmentTypes(CerebrumContext context)
        {
            var appointmentTypes = new List<AppointmentType>();
            appointmentTypes.Add(new AppointmentType
            {
                name = "Test Only",
                children = new List<AppointmentType> {
                                                                new AppointmentType {name = "Test Only ER", VPRequired = false},
                                                                new AppointmentType {name = "Test Only Ext", VPRequired = false},
                                                                new AppointmentType {name = "Test Only Int", VPRequired = false}
                                                                }
            });
            appointmentTypes.Add(new AppointmentType
            {
                name = "Consult",
                children = new List<AppointmentType> {
                                                                new AppointmentType {name = "ER consult"},
                                                                new AppointmentType {name = "ER consult 15",duration=TestDuration.T15},
                                                                new AppointmentType {name = "ER Urgent consult"},
                                                                new AppointmentType {name = "ER Urgent 15",duration=TestDuration.T15},
                                                                new AppointmentType {name = "New Patient 15",duration=TestDuration.T15},
                                                                new AppointmentType {name = "New Patient 45",duration=TestDuration.T45},
                                                                new AppointmentType {name = "New Patient 60",duration=TestDuration.T60},
                                                                new AppointmentType {name = "New Patient 75",duration=TestDuration.T75},
                                                                new AppointmentType {name = "New Patient Urgent"},
                                                                new AppointmentType {name = "New Patient Urgent 15",duration=TestDuration.T15},
                                                                new AppointmentType {name = "New Triage Patient"},
                                                                new AppointmentType {name = "New Ref"},
                                                                new AppointmentType {name = "New Ref 15",duration=TestDuration.T15},
                                                                new AppointmentType {name = "New Ref Urgent"},
                                                                new AppointmentType {name = "New Ref Urgent 15",duration=TestDuration.T15},
                                                             }
            });
            appointmentTypes.Add(new AppointmentType
            {
                name = "Follow Up",
                children = new List<AppointmentType> {
                                                                new AppointmentType {name = "2 week"},
                                                                new AppointmentType {name = "6 week "},
                                                                new AppointmentType {name = "6 week - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "1 month"},
                                                                new AppointmentType {name = "1 month - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "2 month"},
                                                                new AppointmentType {name = "2 month - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "2.5 month"},
                                                                new AppointmentType {name = "3 month"},
                                                                new AppointmentType {name = "3 month - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "4 month"},
                                                                new AppointmentType {name = "4 month - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "6 month"},
                                                                new AppointmentType {name = "6 month - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "9 month"},
                                                                new AppointmentType {name = "9 month - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "1 year"},
                                                                new AppointmentType {name = "1 year - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "18 month"},
                                                                new AppointmentType {name = "18 month - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "2 year"},
                                                                new AppointmentType {name = "2 year - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "Recent FU"},
                                                                new AppointmentType {name = "Recent FU - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "Urgent FU"},
                                                                new AppointmentType {name = "Urgent FU - 30 min",duration=TestDuration.T30},
                                                                new AppointmentType {name = "Quick Visit"},
                                                                new AppointmentType {name = "Research FU"},
                                                                new AppointmentType {name = "Telephone Call"},
                                                                new AppointmentType {name = "Tilt Test"},
                                                                new AppointmentType {name = "Patient Request"},
                                                                new AppointmentType {name = "CT/MRI FU"},
                                                                 new AppointmentType {name = "Data Transfer",duration=TestDuration.T30}
                                                                                               }
            });

            appointmentTypes.Add(new AppointmentType
            {
                name = "No Visit",
                children = new List<AppointmentType> {
                                                                new AppointmentType {name = "E-Consult", VPRequired = false},
                                                                new AppointmentType {name = "Phone", VPRequired = false},
                                                                new AppointmentType {name = "Letter", VPRequired = false}
                                                             }
            });

            appointmentTypes.ForEach(f => context.AppointmentTypes.AddOrUpdate(a => a.name, f));
            context.SaveChanges();
        }
        private void AddHospitals(CerebrumContext context)
        {
            var hospitals = new List<Hospital> { new Hospital { name = "SRHC", code = "2038" },
                new Hospital { name = "RVH", code = "1825" },
                new Hospital { name = "RVHS", code = "3943" },
                new Hospital { name = "SMGH", code = "1921" }
            };
            hospitals.ForEach(f => context.Hospitals.AddOrUpdate(h => h.code, f));
            context.SaveChanges();
        }
        private void AddStorInventoryType(CerebrumContext context)
        {
            var inventoryTypes = new List<StoreInventoryType>
            {
                new StoreInventoryType {name="Holter Monitor"},
                new StoreInventoryType {name="BP Monitor"},
                new StoreInventoryType {name="ELR Monitor" }
            };
            inventoryTypes.ForEach(rs => context.StoreInventoryTypes.AddOrUpdate(s => s.name, rs));
            context.SaveChanges();
        }
        private void AddStorInventoryStatus(CerebrumContext c)
        {
            var reasons = new List<StoreInventoryStatus>
            {
                new StoreInventoryStatus {name="Not Used"},
                new StoreInventoryStatus {name="In Use"},
                new StoreInventoryStatus {name="Returned" }
            };
            reasons.ForEach(rs => c.StoreInventoryStatuses.Add(rs));
            c.SaveChanges();
        }

        private void AddReasons(CerebrumContext context)
        {
            var reasons = new List<Reason>
            {
                new Reason {Name="Please assess regarding ...",mark=1 },
                new Reason {Name="Please assess provide follow up regarding ...",mark=1 }
            };
            reasons.ForEach(rs => context.Reasons.AddOrUpdate(s => s.Name, rs));
            context.SaveChanges();
        }
        private void AddMobileNetworks(CerebrumContext context)
        {
            var networks = new List<MobileNetwork>
            {
                new MobileNetwork {Name="Bell" },
                new MobileNetwork {Name="Rogers" },
                new MobileNetwork {Name="Fido" },
                new MobileNetwork {Name="Telus" },
                new MobileNetwork {Name="Virgin Mobile" },
                new MobileNetwork {Name="Wind/Freedom Mobile" },
            };
            networks.ForEach(rs => context.MobileNetworks.AddOrUpdate(s => s.Name, rs));
            context.SaveChanges();
        }
        private void AddDocServices(CerebrumContext context)
        {
            var services = new List<DocService>
            {
                new DocService {Name="Cardiology",mark=1 },
                new DocService {Name="Rheumatology",mark=1 },
                new DocService {Name="Haematology",mark=1 },
                new DocService {Name="Oncology",mark=1 },
                new DocService {Name="Respirology",mark=1 },
                new DocService {Name="Neurology",mark=1 },
                new DocService {Name="Gastroenterology",mark=1 },
                new DocService {Name="Nephrology",mark=1 },
                new DocService {Name="Dermatology",mark=1 },
                new DocService {Name="Geriatrics",mark=1 },
                new DocService {Name="endocrinology",mark=1 },
                new DocService {Name="Allergy and Immunology",mark=1 },
                new DocService {Name="Ophthalmology",mark=1 },
                new DocService {Name="ENT",mark=1 },
                new DocService {Name="Neurosurgery",mark=1 },
                new DocService {Name="General Surgery",mark=1 },
                new DocService {Name="Orthopedic Surgery",mark=1 },
                new DocService {Name="Psychiatry",mark=1 },
                new DocService {Name="Other",mark=1 }
            };
            services.ForEach(rs => context.DocServices.AddOrUpdate(s => s.Name, rs));
            context.SaveChanges();
        }
        private void AddOfficeUrlTypes(CerebrumContext context)
        {
            var officeUrlTypes = new List<OfficeUrlType>();
            officeUrlTypes.Add(new OfficeUrlType { urlType = "Website", description = "Website" });
            officeUrlTypes.Add(new OfficeUrlType { urlType = "Fax", description = "Fax" });
            officeUrlTypes.Add(new OfficeUrlType { urlType = "Image", description = "Image" });
            officeUrlTypes.Add(new OfficeUrlType { urlType = "MWL", description = "Modalities Work List" });
            officeUrlTypes.ForEach(f => context.OfficeUrlTypes.AddOrUpdate(s => s.urlType, f));
            context.SaveChanges();
        }
        private void AddLooseReportCategories(CerebrumContext context)
        {
            var categories = new List<LooseReportCategory>();
            categories.Add(new LooseReportCategory { category = "angiograms" });
            categories.Add(new LooseReportCategory { category = "PCI notes" });
            categories.Add(new LooseReportCategory { category = "OR notes" });
            categories.Add(new LooseReportCategory { category = "Nuclear Cardiology" });
            categories.Add(new LooseReportCategory { category = "Radiology" });
            categories.Add(new LooseReportCategory { category = "EP proceedure notes" });
            categories.Add(new LooseReportCategory { category = "CT" });
            categories.Add(new LooseReportCategory { category = "MRI" });
            categories.Add(new LooseReportCategory { category = "general" });
            categories.Add(new LooseReportCategory { category = "hospital info" });
            categories.Add(new LooseReportCategory { category = "bloodwork" });
            categories.Add(new LooseReportCategory { category = "consultants letters" });
            categories.Add(new LooseReportCategory { category = "discharge summary" });
            categories.Add(new LooseReportCategory { category = "triage notes/referrals" });
            categories.Add(new LooseReportCategory { category = "consult request" });
            categories.Add(new LooseReportCategory { category = "referrals/requisitions" });
            categories.Add(new LooseReportCategory { category = "patient's consent" });
            categories.Add(new LooseReportCategory { category = "external testing" });
            categories.Add(new LooseReportCategory { category = "Cardiac Rehab" });
            categories.Add(new LooseReportCategory { category = "Pharmacy" });
            categories.Add(new LooseReportCategory { category = "Sleep Study" });
            categories.Add(new LooseReportCategory { category = "ABP Report" });
            categories.Add(new LooseReportCategory { category = "HRM" });
            categories.Add(new LooseReportCategory { category = "Hospital Med. Records" });
            categories.Add(new LooseReportCategory { category = "Vascular Cardiology" });
            categories.ForEach(c => context.LooseReportCategories.AddOrUpdate(s => s.category, c));
            context.SaveChanges();
        }
        private void AddTestResourceTypes(CerebrumContext context)
        {
            var testresourcetypes = new List<TestResourceType> {
                new TestResourceType { TestResourceName = "Doctor" },
                new TestResourceType { TestResourceName = "Tech" },
                new TestResourceType { TestResourceName = "Room" },
            };
            testresourcetypes.ForEach(f => context.TestResourceTypes.AddOrUpdate(s => s.TestResourceName, f));
            context.SaveChanges();
        }
        private void AddTreatments(CerebrumContext context)
        {
            var treatments = new List<TreatmentType>
            {
                new TreatmentType() { Description="CHRON-Continuous/Chronic", IsActive = true },
                new TreatmentType() { Description="ACU-Acute", IsActive = true },
                new TreatmentType() { Description="ONET-One Time", IsActive = true },
                new TreatmentType() { Description="PRN Long-term - As needed", IsActive = true },
                new TreatmentType() { Description="PRN Short-term - As needed", IsActive = true }
            };
            treatments.ForEach(f => context.TreatmentTypes.AddOrUpdate(s => s.Description, f));
            context.SaveChanges();
        }
        private void AddPrescriptionStatuses(CerebrumContext context)
        {
            var statuses = new List<PrescriptionStatus>
            {
                new PrescriptionStatus() { Description="New", IsActive = true },
                new PrescriptionStatus() { Description="Active", IsActive = true },
                new PrescriptionStatus() { Description="Suspended", IsActive = true },
                new PrescriptionStatus() { Description="Aborted", IsActive = true },
                new PrescriptionStatus() { Description="Completed", IsActive = true },
                new PrescriptionStatus() { Description="Obsolete", IsActive = true },
                new PrescriptionStatus() { Description="Nullified", IsActive = true }

            };
            statuses.ForEach(f => context.PrescriptionStatuses.AddOrUpdate(s => s.Description, f));
            context.SaveChanges();
        }
        private void AppointmentStatus(CerebrumContext context)
        {
            var aptestStatus = new List<AppointmentTestStatus> {
                new AppointmentTestStatus { Status="Requested", Color="Red", CSSClass = "Requested"},
                new AppointmentTestStatus { Status="Appointed", Color="Chocolate", CSSClass = "Appointed"},
                new AppointmentTestStatus { Status="Not arrived", Color="#808080", CSSClass="Not_Arrived"},
                new AppointmentTestStatus { Status="Arrived", Color="#ebc944", CSSClass ="Arrived"},
                new AppointmentTestStatus { Status="Test started", Color="pink", CSSClass="Test_Started"},
                new AppointmentTestStatus { Status="Test completed", Color="violet", CSSClass="Test_Completed"},
                new AppointmentTestStatus { Status="Test ready to start", Color="teal", CSSClass="ReadyToStart"},
                new AppointmentTestStatus { Status="Images / data transferred", Color="blueviolet", CSSClass = "Images_Transferred"},
                new AppointmentTestStatus { Status="Ready for doctor", Color="orange", CSSClass="ReadyForDoctor"},
                new AppointmentTestStatus { Status="Trainee report ready", Color="burlywood", CSSClass="TraineeReportReady"},
                new AppointmentTestStatus { Status="Report Completed", Color="blue", CSSClass="ReportCompleted"}
            };

            aptestStatus.ForEach(f => context.AppointmentTestStatus.AddOrUpdate(s => s.Status, f));
            context.SaveChanges();
        }
        private void RequisitionTimeInitializer(CerebrumContext c)
        {
            List<RequisitionTime> times = new List<RequisitionTime>() {
                new RequisitionTime {name="ASAP",days=0,orderNumber=10 },
                new RequisitionTime {name="After the test",days=0 ,orderNumber=20},
                new RequisitionTime {name="1 Month",days=30 ,orderNumber=30},
                new RequisitionTime {name="2 Months",days=60 ,orderNumber=40},
                new RequisitionTime {name="3 Months",days=90 ,orderNumber=50},
                new RequisitionTime {name="6 Months",days=180 ,orderNumber=60},
                new RequisitionTime {name="1 year",days=365 ,orderNumber=70},
                new RequisitionTime {name= "Prior Next Appointment",orderNumber=80},
                new RequisitionTime {name= "At Next Appointment",orderNumber=90},
                new RequisitionTime {name= "No FU Needed",orderNumber=100},
            };
            times.ForEach(f => c.RequisitionTime.AddOrUpdate(a => a.name, f));
            c.SaveChanges();

        }

        private void RequisitionInitializer(CerebrumContext c)
        {
            RequisitionType requisitionTypeBloodUrine = new RequisitionType { name = "Blood&Urine", orderNumber = 0 };
            RequisitionType requisitionTypeInternal = new RequisitionType { name = "Internal", orderNumber = 1 };
            //RequisitionType requisitionTypeRadiology = new RequisitionType { name = "Radiology", orderNumber = 2 };
            RequisitionType requisitionTypeExternal = new RequisitionType { name = "External", orderNumber = 3 };
            RequisitionType requisitionTypeCath = new RequisitionType { name = "Cath", orderNumber = 4 };
            RequisitionType requisitionTypeConsultationRequest = new RequisitionType { name = "Consultation Request", orderNumber = 5 };
            c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeBloodUrine);
            c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeInternal);
            //c.RequisitionType.Add(requisitionTypeRadiology);
            c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeExternal);
            c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeCath);
            c.RequisitionType.AddOrUpdate(r => r.name, requisitionTypeConsultationRequest);

            c.RequisitionStatus.AddOrUpdate(r => r.text, new RequisitionStatus { color = "red", text = "Ordered" });
            c.RequisitionStatus.AddOrUpdate(r => r.text, new RequisitionStatus { color = "blue", text = "Arranged" });
            c.RequisitionStatus.AddOrUpdate(r => r.text, new RequisitionStatus { color = "green", text = "Fulfilled" });

            RequisitionTimeInitializer(c);

            List<string> requisitionItems = @"0,Diabetes,DM;1,IV contrast allergies,;2,Nephrectomy,;3,Pregnancy,;4,Breastfeeding,;5,Weight over 300 lbs.,;
                                        6,CT Head,;7,CT Neck,;8,CT Abdomen,CT abdo;9, CT Pelvis,;10,CT Spine Cervical,CT neck;11,CT Spine Lumbar,CT lumbar;12,CT Extremety,;
                                        13,CT Head,;14,CT Carotid,;15,CT Pulmonary,CT chest;16,CT Aorta,;17,Ultrasound Abdomen,US abdo;18,Ultrasound Pelvis,US Pelv;
                                        19,Ultrasound Pelvis TV,US Pelv TV;20,Ultrasound Transrectal Prostate,US Prost TR;21,Ultrasound Obstetrical Thyroid,;
                                        22,Ultrasound Scrotum,US scrot;23,Ultrasound Arterial: Carotid,US Carot;24,Ultrasound Leg,PAD leg;25,Ultrasound Venous Leg,PVD leg;
                                        26,Ultrasound msk: Knee,US knee;27,Ultrasound msk: Shoulder,US should;28,Ultrasound Hysterosonogram,US Ueterus;29,Nuclear Bone,Bone scan;
                                        30,Nuclear Brain,brain scan;31,Nuclear Gallium,Ga scan;32,Nuclear Liver:rbc,Liver rbc;33,Nuclear Liver/Spleen,L/S scan;34,Nuclear Lung V/Q,Lung V/Q;
                                        35,Nuclear Renal:Baseline,Renal:Baseline;36,Nuclear Renal:Captopril Thyroid,Renal:Captopril;37,Cardiac Imaging Nuclear Stress Exercise,MIBI-S;
                                        38,Cardiac Imaging  Nuclear Stress Persantine,MIBI-P;39,Cardiac Imaging  Nuclear Stress Rest Thallium,STh;
                                        40,Cardiac Imaging  Nuclear Stress Rest MUGA,SMUGA;41,Echocardiography Regular,E1;42,Echocardiography Stress,SE;
                                        43,Echocardiography Transesophageal,TEE;44,Mammography Screening,Mammo;45,Mammography Work Up,Mammo Work up;46,Brest Ultrasound Right,US breast R;
                                        47,Brest Ultrasound Left,US breast L;48,Fluoroscopy:Esophagus only,;49,Fluoroscopy:Upper GI Series,;50,Fluoroscopy:Small Bowel,;
                                        51,Fluoroscopy:Barium Enema,;52,Fluoroscopy:Hysterosalpingogram,;53,X-ray:Chest,CXR;54,X-ray:Abdomen,;55,X-ray:Pelvis,;
                                        56,X-ray:Shoulder,;57,X-ray:Hand,;58,X:ray:Wrist,;59,X-ray:Hip,;60,X-ray:Knee,;61,Xray:Ankle,;62,Xray:Foot,;
                                        63,Xray:Spine Servical,;64,Xray:SpineThoracic,;65,X-ray:Spine Lumbar,;66,CT Colonography,;
                                        67,CT Angiogram,CT angio;68,Bone Mineral Densitometry,BMD;69,Sleep Study,;
                                        70,PFT,;71,EP Study,;72,ICD,;73,Pace maker,;74,Ablation,;75,Cardiac MRI,;76,Coronary Angiography,Coronary angio;77,EEG,;79,Cardioversion,;
                                        80,EPS,;81,Renal Artery Doppler,".Split(';').Distinct().ToList();
            for (int i = 0; i < requisitionItems.Count; i++)
            {
                string[] requisitionItem = requisitionItems[i].Split(',');
                c.RequisitionItem.AddOrUpdate(r => r.name, new RequisitionItem { requisitionItemId = int.Parse(requisitionItem[0].Trim()), name = requisitionItem[1].Trim(), shortName = string.IsNullOrEmpty(requisitionItem[2].Trim()) ? requisitionItem[1].Trim() : requisitionItem[2].Trim(), requisitionType = requisitionTypeExternal });
            }
            c.SaveChanges();
        }

        private void LISInitializer(CerebrumContext c)
        {
            var requestcatslst = new List<OLISTestRequestCategory>
            {
                new OLISTestRequestCategory { categoryName = "Allergen", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestRequestCategory { categoryName = "Blood Bank", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestRequestCategory { categoryName = "Chem", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestRequestCategory { categoryName = "Clinical", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestRequestCategory { categoryName = "Hematology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestRequestCategory { categoryName = "Immuno", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestRequestCategory { categoryName = "Microbiology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestRequestCategory { categoryName = "Pathology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestRequestCategory { categoryName = "Serology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true }
            };
            requestcatslst.ForEach(f => c.OLISTestRequestCategories.AddOrUpdate(r => r.categoryName, f));
            c.SaveChanges();

            var reqtestsubcat = new List<OLISTestRequestSubCategory>()
            {
                new OLISTestRequestSubCategory {categoryName="Amino Acid",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Animal Epidermals & Proteins Regular Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Animal Epidermals & Proteins Special Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Autoantibody",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Challenge",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Coag",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Drug/Tox",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Drug",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Endocrine",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Enzyme",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Fetal Status",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="flow",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Foods Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Food Regular Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Food Special Allergens",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Glucose",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
                new OLISTestRequestSubCategory {categoryName="Protein",createdOn=DateTime.Now,updatedOn=DateTime.Now,isActive=true },
            };
            reqtestsubcat.ForEach(f => c.OLISTestRequestSubCategories.AddOrUpdate(r => r.categoryName, f));
            try
            {
                c.SaveChanges();
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException dbEx)
            {
                Exception raise = dbEx;
                foreach (var validationErrors in dbEx.EntityValidationErrors)
                {
                    foreach (var validationError in validationErrors.ValidationErrors)
                    {
                        string message = string.Format("{0}:{1}",
                            validationErrors.Entry.Entity.ToString(),
                            validationError.ErrorMessage);
                        // raise a new exception nesting
                        // the current instance as InnerException
                        raise = new InvalidOperationException(message, raise);
                    }
                }
                throw raise;
            }


            var rptcat = new List<OLISTestReportCategory>
            {
                new OLISTestReportCategory {categoryName="Allergens", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestReportCategory {categoryName="Blood Bank", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestReportCategory {categoryName="Chemistry", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestReportCategory {categoryName="Clinical", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestReportCategory {categoryName="Hematology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestReportCategory {categoryName="Immunology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestReportCategory {categoryName="Microbiology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestReportCategory {categoryName="Pathology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestReportCategory {categoryName="Serology", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true }
            };
            rptcat.ForEach(f => c.OLISTestReportCategories.AddOrUpdate(r => r.categoryName, f));
            try
            {
                c.SaveChanges();
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException dbEx)
            {
                Exception raise = dbEx;
                foreach (var validationErrors in dbEx.EntityValidationErrors)
                {
                    foreach (var validationError in validationErrors.ValidationErrors)
                    {
                        string message = string.Format("{0}:{1}",
                            validationErrors.Entry.Entity.ToString(),
                            validationError.ErrorMessage);
                        // raise a new exception nesting
                        // the current instance as InnerException
                        raise = new InvalidOperationException(message, raise);
                    }
                }
                throw raise;
            }

            var rsltcat = new List<OLISTestResultCategory>
            {
                new OLISTestResultCategory {categoryName="ATTACHED.MEDS", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestResultCategory {categoryName="CALCULUS ANALYSIS", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestResultCategory {categoryName="CYTO", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestResultCategory {categoryName="MICRO", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestResultCategory {categoryName="MOLPATH", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestResultCategory {categoryName="MOLPATH.MUT", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestResultCategory {categoryName="PATH.PROTOCOLS", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true },
                new OLISTestResultCategory {categoryName="SERO", createdOn = DateTime.Now, updatedOn = DateTime.Now, isActive = true }
            };
            rsltcat.ForEach(f => c.OLISTestResultCategories.AddOrUpdate(r => r.categoryName, f));


            var query = new List<OLISQueryMessage> {
                new OLISQueryMessage {QueryId="Z01",QueryTrigger="SPQ^Z01^SPQ_Q08",StoreProcedureName="Z_QryLabInfoForPatientID", QueryName="Retrieve Laboratory Information for Patient" },
                new OLISQueryMessage {QueryId="Z02",QueryTrigger="SPQ^Z02^SPQ_Q08",StoreProcedureName="Z_QryLabInfoForOrderID", QueryName="Retrieve Laboratory Information for Order ID" },
                new OLISQueryMessage {QueryId="Z04",QueryTrigger="SPQ^Z04^SPQ_Q08", StoreProcedureName="Z_QryLabInfoUpdatesForPractitionerID", QueryName="Retrieve Laboratory Information Updates for Practitioner" },
                new OLISQueryMessage {QueryId="Z05",QueryTrigger="SPQ^Z05^SPQ_Q08", StoreProcedureName="Z_QryLabInfoUpdatesForLaboratoryID", QueryName="Retrieve Laboratory Information Updates for Laboratory" },
                new OLISQueryMessage {QueryId="Z06",QueryTrigger="SPQ^Z06^SPQ_Q08", StoreProcedureName="Z_QryLabInfoUpdatesForHCFID", QueryName="Retrieve Laboratory Information Updates for Ordering Facility" },
                new OLISQueryMessage {QueryId="Z07",QueryTrigger="SPQ^Z07^SPQ_Q08",StoreProcedureName="Z_QryLabInfoByPHBReportFlag", QueryName="Retrieve Laboratory Information Reportable to Public Health" },
                new OLISQueryMessage {QueryId="Z08",QueryTrigger="SPQ^Z08^SPQ_Q08", StoreProcedureName="Z_QryLabInfoByCCOReportFlag", QueryName="Retrieve Laboratory Information to Cancer Care Ontario" },
                new OLISQueryMessage {QueryId="Z50",QueryTrigger="SPQ^Z50^SPQ_Q08", StoreProcedureName="Z_IDPatientByNameSexDoB", QueryName="Identify Patient by Name, Sex, and Date of Birth" },
            };
            query.ForEach(q => c.OLISQueryMessages.AddOrUpdate(r => r.QueryId, q));
            c.SaveChanges();
        }
        private void PermissionsInitializer(CerebrumContext context)
        {
            var MasterName = "Cerebrum 3.0 Database";
            var master = context.Master.FirstOrDefault(d => d.MasterName == MasterName);

            var permissionbase = new PermissionsBase { MasterId = master.Id, MasterName = master.MasterName };


            var permissionTypes = new List<PermissionType> {
                 new PermissionType{
                        Name = "DaysheetLinks", Permissions = new List<Permission>{
                            new Permission { Name = "OpenTestWorksheet" },
                            new Permission { Name = "OpenTestReport" },
                            new Permission { Name = "OpenVP" },
                            new Permission { Name = "OpenLetter" },
                            new Permission { Name = "OpenPerformeter" },
                            new Permission { Name = "OpenSummary" },
                            new Permission { Name = "OpenLetterList" },
                            new Permission { Name = "OpenPatientGroups" },
                            new Permission { Name = "OpenReport" },
                            new Permission { Name = "OpenAssignNewHL7" },
                            new Permission { Name = "OpenWLSearch" },
                            new Permission { Name = "OpenInbox" },
                            new Permission { Name = "Inventory" },
                            new Permission { Name = "UploadReports" },
                            new Permission { Name = "ReminderReports" },
                            new Permission { Name = "FaxReport" },
                            new Permission { Name = "RD" }
                        }
            },
                new PermissionType
                {
                    Name = "Demographics",
                    Permissions = new List<Permission>{
                            new Permission { Name = "VewPreviousTests" },
                            new Permission { Name = "SearchAppointments" },
                            new Permission { Name = "ViewMedicationHistory" },
                            new Permission { Name = "ViewTestHistory" },
                            new Permission { Name = "ViewExternalReportsList" },
                            new Permission { Name = "ViewClinicalSummary" },
                            new Permission { Name = "ViewPatientsCM" },
                            new Permission { Name = "ViewBillingHistory" },
                            new Permission { Name = "ViewLetterHistory" },
                            new Permission { Name = "ViewAlertManager" },
                            new Permission { Name = "ViewPatientStatement" },
                            new Permission { Name = "OpenProstheticValve" },
                            new Permission { Name = "OpenUpUpcomingAppointments" },
                            new Permission { Name = "OpenDoctorComments" },
                            new Permission { Name = "AddOrDeleteNewGroup" },
                        }
                },
                new PermissionType
                {
                    Name = "Worksheet",
                    Permissions = new List<Permission>{
                            new Permission { Name = "SaveChanges" },
                            new Permission { Name = "SendReport" },
                            new Permission { Name = "SendAmendedReport" },
                            new Permission { Name = "ReAssign" },
                            new Permission { Name = "ViewImagesRawData" }
                        }
                },
                new PermissionType
                {
                    Name = "VP",
                    Permissions = new List<Permission>{
                            new Permission { Name = "SaveChangesToPhrasesOrMeasurements" },
                            new Permission { Name = "SendLetter" },
                            new Permission { Name = "SendAmendedLetter" },
                            new Permission { Name = "AddBillingCodes" },
                            new Permission { Name = "CreateOrders" },
                            new Permission { Name = "Doctor" }
                        }
                },
                new PermissionType
                {
                    Name = "Medication",
                    Permissions = new List<Permission>{
                            new Permission { Name = "Add_Or_DC_a_Medication" },
                            new Permission { Name = "ChangeDose" },
                            new Permission { Name = "AddAllergy" },
                            new Permission { Name = "CreateTemplates" },
                            new Permission { Name = "WritePrescription" },
                            new Permission { Name = "ReprintOrRefaxPrescription" },
                            new Permission { Name = "View Medications", crtype = CRType.Data  }
                        }
                },
                new PermissionType
                {
                    Name = "ExternalReports",
                    Permissions = new List<Permission>{
                            new Permission { Name = "ViewReports" },
                            new Permission { Name = "AlertReports" },
                            new Permission { Name = "CommentOnReports" },
                            new Permission { Name = "AddRequisition" },
                            new Permission { Name = "AddBilling" },
                            new Permission { Name = "MarkSeen" },
                            new Permission { Name = "ReclassifyReports" }
                        }
                },
                new PermissionType
                {
                    Name = "OtherActions",
                    Permissions = new List<Permission>{
                            new Permission { Name = "IssuePatientStatement" },
                            new Permission { Name = "PrintReceipt" },
                            new Permission { Name = "ChangePatientStatus" },
                            new Permission { Name = "ChangeClaims" },
                            new Permission { Name = "SaveDoctorComments" },
                            new Permission { Name = "ViewWaitList" },
                            new Permission { Name = "SaveChangesToWaitlist" },
                            new Permission { Name = "AddTestsToWaitlist" },
                            new Permission { Name = "AssignReportsInUploadReports" },
                            new Permission { Name = "SaveChangesInTheInventory" },
                            new Permission { Name = "SaveChangesInAssignHL7" }
                        }
                },
                new PermissionType
                {
                    Name = "Admin",
                    Permissions = new List<Permission>{
                            new Permission { Name = "AddUserOrEditUser" },
                            new Permission { Name = "AddDoctorOrEditDoctor" },
                            new Permission { Name = "AddClinicOrEditClinic" },
                            new Permission { Name = "CreateNewRole" },
                            new Permission { Name = "LoginReport" },
                            new Permission { Name = "PageOpeningReport" },
                            new Permission { Name = "AuditLog" },
                            new Permission { Name = "SecurityMeasurement" },
                            new Permission { Name = "ExternalDoctors" },
                            new Permission { Name = "Offices" },
                            new Permission { Name = "Hospitals" },
                            new Permission { Name = "Reminder" },
                            new Permission { Name = "AppointmentTypes" },
                            new Permission { Name = "PatientGroups" },
                            new Permission { Name = "DuplicatedPatientProcessing" },
                            new Permission { Name = "MedicalReports" },
                            new Permission { Name = "TestsInternal" },
                            new Permission { Name = "ExternalTests" },
                            new Permission { Name = "DiagnosticCodesLookUp" },
                            new Permission { Name = "CerebrumReports" },
                            new Permission { Name = "Billing" },
                            new Permission { Name = "BillingAdmin" },
                            new Permission { Name = "BillingCodesLookUp" },
                            new Permission { Name = "ScheduleAdmin" },
                            new Permission { Name = "WorkingHoursAdmin" },
                            new Permission { Name = "Postprocedure" },
                            new Permission { Name = "ReportsOnStats" },
                            new Permission { Name = "HRMReports" }
                      }
                },
                new PermissionType
                {
                    Name = "CanDoTests",
                    Permissions = new List<Permission> {
                        new Permission { Name = "Perform ECHO" },
                        new Permission { Name = "Perform SE ECHO" },
                        new Permission { Name = "Perform PFT" },
                        new Permission { Name = "Perform SE" },
                        new Permission { Name = "Perform SE GXT" },
                        new Permission { Name = "Perform GXT" },
                        new Permission { Name=  "Perform DE" },
                        new Permission { Name = "Perform ECG" },
                        new Permission { Name = "Perform Holter" },
                        new Permission { Name = "Perform ELR" },
                        new Permission { Name = "Perform BP" },
                        new Permission { Name = "Perform NUCLEAR" },
                        new Permission { Name = "Perform VASCULAR" },
                        new Permission { Name = "Perform GENERAL US" },
                        new Permission { Name = "Perform X RAY" },
                        new Permission { Name = "Perform SLECG" },
                        new Permission { Name = "Perform CVT" }
                        }
                },
                new PermissionType
                {
                    Name = "WebBooking",
                    Permissions = new List<Permission> {
                            new Permission { Name = "View Page" }
                        }
                },
                new PermissionType
                {
                    Name = "DoctorPermissionTypes",
                    Permissions = new List<Permission> {
                            new Permission { Name = "VPConsulting" }
                        }
                },
                new PermissionType
                {
                    Name = "ContactManager",
                    Permissions = new List<Permission> {
                            new Permission { Name = "ViewCommonMessages" }
                        }
                }
            };
            permissionbase.PermissionTypes.AddRange(permissionTypes);
            context.PermissionsBases.AddOrUpdate(p => p.MasterName, permissionbase);

            context.SaveChanges();
        }
        private void AddScheduleSchemaColor(CerebrumContext c)
        {
            var echoper = c.Permissions.Single(prmsn => prmsn.Name == "Perform ECHO").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#f9f1e8", forPermissions = echoper.ToString() });

            var seper = c.Permissions.Single(prmsn => prmsn.Name == "Perform SE").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#f9f1e8", forPermissions = seper.ToString() });

            var gxtper = c.Permissions.Single(prmsn => prmsn.Name == "Perform GXT").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#f9f1e8", forPermissions = gxtper.ToString() });

            var ecgper = c.Permissions.Single(prmsn => prmsn.Name == "Perform ECG").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#f4f1d0", forPermissions = ecgper.ToString() });

            var holtper = c.Permissions.Single(prmsn => prmsn.Name == "Perform Holter").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#f2e6d7", forPermissions = holtper.ToString() });

            var nuper = c.Permissions.Single(prmsn => prmsn.Name == "Perform NUCLEAR").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#e9f2de", forPermissions = nuper.ToString() });

            var xper = c.Permissions.Single(prmsn => prmsn.Name == "Perform X RAY").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#f7ebea", forPermissions = xper.ToString() });

            var vper = c.Permissions.Single(prmsn => prmsn.Name == "Perform VASCULAR").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#eaf7f5", forPermissions = vper.ToString() });

            var uper = c.Permissions.Single(prmsn => prmsn.Name == "Perform GENERAL US").Id;
            c.ScheduleColorSchemas.Add(new ScheduleColorSchema { colorHex = "#f1eaf7", forPermissions = uper.ToString() });
          
            c.SaveChanges();
        }
        private void TriageUrgencyInitializer(CerebrumContext c)
        {
            var tiageurgencies = new List<TriageUrgency> {
                new TriageUrgency {description="All" },
                //new TriageUrgency {description="For Triage" },
                new TriageUrgency {description="Urgent" },
                new TriageUrgency {description="Semi-Urgent" },
                new TriageUrgency {description="Can wait" },
                // new TriageUrgency {description="Triaged" },
                new TriageUrgency {description="Do not need to see" },
            };
            tiageurgencies.ForEach(f => c.TriageUrgencies.AddOrUpdate(a => a.description, f));
            c.SaveChanges();
        }

        private void TriageStatusInitializer(CerebrumContext c)
        {
            var statuses = new List<TriageStatus> {
                new TriageStatus {Description="For Triage" },
                new TriageStatus {Description="Triaged" }
            };
            statuses.ForEach(f => c.TriageStatuses.AddOrUpdate(a => a.Description, f));
            c.SaveChanges();
        }

        private void AddReportClasses(CerebrumContext context)
        {
            var reportClasses = new List<ReportClass> {
                new ReportClass
                {
                    name = "Diagnostic Imaging Report",
                    ReportSubClass = new List<ReportSubClass>
                    {
                                    new ReportSubClass { name = "Misc.X - Ray" },
                                    new ReportSubClass { name = "Mammogram" },
                                    new ReportSubClass { name = "Chest X-Ray" },
                                    new ReportSubClass { name = "Abdomen X-Ray" },
                                    new ReportSubClass { name = "Lumbar Spine X - Ray" },
                                    new ReportSubClass { name = "Cervical Spine X - Ray" },
                                    new ReportSubClass { name = "Upper GI Series" },
                                    new ReportSubClass { name = "ERCP X-Ray" },
                                    new ReportSubClass { name = "UGI with Small Bowel" },
                                    new ReportSubClass { name = "Barium Enema" },
                                    new ReportSubClass { name = "Myelogram" },
                                    new ReportSubClass { name = "IVP" },
                                    new ReportSubClass { name = "Hysterosalpingogram" },
                                    new ReportSubClass { name = "Coronary Angiography" },
                                    new ReportSubClass { name = "Carotid Angiography" },
                                    new ReportSubClass { name = "Other Angiography" },
                                    new ReportSubClass { name = "Misc. CT Scan" },
                                    new ReportSubClass { name = "CT Scan Head" },
                                    new ReportSubClass { name = "CT Scan Body" },
                                    new ReportSubClass { name = "Misc. MRI Scan" },
                                    new ReportSubClass { name = "MRI Scan Head" },
                                    new ReportSubClass { name = "MRI Scan Body" },
                                    new ReportSubClass { name = "Misc. Ultrasound" },
                                    new ReportSubClass { name = "Ultrasound Abdomen" },
                                    new ReportSubClass { name = "Ultrasound Pelvis" },
                                    new ReportSubClass { name = "Ultrasound Obstetrical" },
                                    new ReportSubClass { name = "Ultrasound Breast," },
                                    new ReportSubClass { name = "Ultrasound Thyroid" },
                                    new ReportSubClass { name = "Venous Doppler Ultrasound" },
                                    new ReportSubClass { name = "Carotid Doppler Ultrasound" },
                                    new ReportSubClass { name = "Sonohistogram" },
                                    new ReportSubClass { name = "Echocardiogram" },
                                    new ReportSubClass { name = "Misc. Nuclear Scan" },
                                    new ReportSubClass { name = "Bone Scan" },
                                    new ReportSubClass { name = "Stress Heart Scan(Thallium" },
                                    new ReportSubClass { name = "Sestamibi" },
                                    new ReportSubClass { name = "Myoview)" },
                                    new ReportSubClass { name = "Brain Scan" },
                                    new ReportSubClass { name = "Lung Scan" },
                                    new ReportSubClass { name = "Liver - Spleen Scan" },
                                    new ReportSubClass { name = "Bone Densitometry" },
                                    new ReportSubClass { name = "Retinal Tomograph" },
                                    new ReportSubClass { name = "Retinal Angiography" }
                          }
                },
                new ReportClass {
                    name = "Diagnostic Test Report",
                    ReportSubClass=new List<Cerebrum.Data.ReportSubClass>
                    {
                         new ReportSubClass { name = "Misc. Diagnostic Test" },
                                                new ReportSubClass { name = "Pap Test Report" },
                                                new ReportSubClass { name = "Mantoux Test" },
                                                new ReportSubClass { name = "ECG" },
                                                new ReportSubClass { name = "Stress Test (Exercise" },
                                                new ReportSubClass { name = "Persantine" },
                                                new ReportSubClass { name = "Dobutamine)" },
                                                new ReportSubClass { name = "Holter Monitor" },
                                                new ReportSubClass { name = "Loop Recorder" },
                                                new ReportSubClass { name = "Ambulatory BP Monitoring" },
                                                new ReportSubClass { name = "Arterial Segmental Pressures (ABI)" },
                                                new ReportSubClass { name = "Pulmonary Function Testing" },
                                                new ReportSubClass { name = "Bronchoscopy" },
                                                new ReportSubClass { name = "EEG" },
                                                new ReportSubClass { name = "EMG" },
                                                new ReportSubClass { name = "Sleep Study" },
                                                new ReportSubClass { name = "EGD-oscopy" },
                                                new ReportSubClass { name = "Sigmoidoscopy" },
                                                new ReportSubClass { name = "Colonoscopy" },
                                                new ReportSubClass { name = "Cystoscopy" },
                                                new ReportSubClass { name = "Urodynamic Testing" },
                                                new ReportSubClass { name = "Colposcopy" },
                                                new ReportSubClass { name = "Audiogram" },
                                            }
                },
                new ReportClass
                {
                    name = "Cardio Respiratory Report",
                    ReportSubClass =new List<ReportSubClass> {
                                new ReportSubClass { name = "Echocardiography Bubble Study" },
                                new ReportSubClass { name = "Pericardiocentesis" },
                                new ReportSubClass { name = "Echocardiography Esophageal" }
                                }
                 }
        };



            var reportSubClass = new List<ReportSubClass>();
            Char delimiter = ',';
            var valueA = "On-Call Physician, On-Call Nurse, Emergency Physician, Urgent Care/Walk-In Clinic Physician, Hospitalis, Anaesthesiology, Allergy & Immunology, Audiology, Cardiology, Cardiovascular Surgery, Chiropody / Podiatry, Chiropractic, Clinical Biochemistry, Dentistry, Dermatology, Dietitian, Emergency Medicine, Endocrinology, Family Practice, Gastroenterology, General Surgery, Genetics, Geriatrics, Hematology, Infectious Disease, Internal Medicine, Kinesiology, Microbiology, Midwifery, Naturopathy, Neonatology, Nephrology, Neurology, Neurosurgery, Nuclear Medicine, Nursing, Nurse Practitioner, Obstetrics & Gynecology, Occupational Therapy, Oncology / Chemotherapy, Ophthalmology, Optometry, Oral Surgery, Orthopedic Surgery, Osteopathy, Other Therapy, Otolaryngology (ENT), Palliative Care, Pathology, Paediatrics, Pharmacology, Physical Medicine, Physiotherapy, Plastic Surgery, Psychiatry, Psychology, Diagnostic Radiology, Respiratory Technology, Respirology, Rheumatology, Social Work, Speech Therapy, Sports Medicine, Therapeutic Radiology, Thoracic Surgery, Urology, Uro-Gynecology, Vascular Surgery, Other Consultant";
            var valueB = "Consultation, Admission History, Operative Report, Discharge Summary, Progress Report, Encounter Report";
            foreach (var va in valueA.Split(delimiter))
            {
                foreach (var vb in valueB.Split(delimiter))
                {
                    reportSubClass.Add(new ReportSubClass { name = va.Trim() + " " + vb.Trim() });
                }
            }
            reportClasses.Add(new ReportClass { name = "Consultant Report", ReportSubClass = reportSubClass });

            reportSubClass = new List<ReportSubClass>();
            reportClasses.Add(new ReportClass { name = "Lab Report", ReportSubClass = reportSubClass });

            reportSubClass = new List<ReportSubClass>();
            reportSubClass.Add(new ReportSubClass { name = "etter from Patient" });
            reportSubClass.Add(new ReportSubClass { name = "Living Will" });
            reportSubClass.Add(new ReportSubClass { name = "Power of Attorney for Health Care" });
            reportSubClass.Add(new ReportSubClass { name = "Consent from Patient" });
            reportSubClass.Add(new ReportSubClass { name = "Authorization from Patient" });
            reportSubClass.Add(new ReportSubClass { name = "Letter from Lawyer" });
            reportSubClass.Add(new ReportSubClass { name = "Letter from WSIB" });
            reportSubClass.Add(new ReportSubClass { name = "Letter from Insurance Company" });
            reportSubClass.Add(new ReportSubClass { name = "Disability Report" });
            reportSubClass.Add(new ReportSubClass { name = "Miscellaneous Letter" });
            reportClasses.Add(new ReportClass { name = "Other Letter", ReportSubClass = reportSubClass });

            reportClasses.ForEach(s => context.ReportClasses.AddOrUpdate(f => f.name, s));
            context.SaveChanges();
        }

    }
}
