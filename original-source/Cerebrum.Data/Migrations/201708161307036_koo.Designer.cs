// <auto-generated />
namespace Cerebrum.Data
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class koo : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(koo));
        
        string IMigrationMetadata.Id
        {
            get { return "201708161307036_koo"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
