namespace Cerebrum.Data.Migrations.Audit
{
    using Data.Audit;
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.Linq;

    internal sealed class AuditConfiguration : DbMigrationsConfiguration<CerebrumAuditContext>
    {
        public AuditConfiguration()
        {
            AutomaticMigrationsEnabled = false;
            MigrationsDirectory = @"Migrations\Audit";
        }

        protected override void Seed(CerebrumAuditContext context)
        {
            //  This method will be called after migrating to the latest version.

            //  You can use the DbSet<T>.AddOrUpdate() helper extension method 
            //  to avoid creating duplicate seed data. E.g.
            //
            //    context.People.AddOrUpdate(
            //      p => p.FullName,
            //      new Person { FullName = "<PERSON>" },
            //      new Person { FullName = "<PERSON><PERSON> Lambson" },
            //      new Person { FullName = "<PERSON> Miller" }
            //    );
            //
        }
    }
}
