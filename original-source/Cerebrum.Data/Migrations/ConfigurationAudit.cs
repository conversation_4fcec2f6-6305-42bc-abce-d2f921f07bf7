namespace Cerebrum.Data
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.Linq;

    internal sealed class ConfigurationAudit : DbMigrationsConfiguration<Cerebrum.Data.Audit.CerebrumAuditContext>
    {
        public ConfigurationAudit()
        {
            AutomaticMigrationsEnabled = false;
            //ContextKey = "Cerebrum.Data.Audit.CerebrumAuditContext";
        }

        protected override void Seed(Cerebrum.Data.Audit.CerebrumAuditContext context)
        {
            //  This method will be called after migrating to the latest version.
        }
    }
}
