﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class EntityErrorJson
    {
        public string EntityName { get; set; }
        public string State { get; set; }
        public List<EntityProperty> EntityProperties { get; set; } = new List<EntityProperty>();
    }
    public class EntityProperty
    {
        public string PropertyName { get; set; }
        public string Value { get; set; }
        public string ErrorMessage { get; set; }
    }
}
