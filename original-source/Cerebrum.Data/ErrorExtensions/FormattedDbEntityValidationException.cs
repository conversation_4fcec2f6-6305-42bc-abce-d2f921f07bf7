﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class FormattedDbEntityValidationException : Exception
    {
        public FormattedDbEntityValidationException(DbEntityValidationException innerException) :
        base(null, innerException)
        {
        }
        public override string Message
        {
            get
            {
                var innerException = InnerException as DbEntityValidationException;
                if (innerException != null)
                {
                    List<EntityErrorJson> errors = new List<EntityErrorJson>();

                    foreach (var eve in innerException.EntityValidationErrors)
                    {
                        var error = new EntityErrorJson { EntityName= eve.Entry.Entity.GetType().Name ,State= eve.Entry.State.ToString() };
                        foreach (var ve in eve.ValidationErrors)
                        {
                            var propertyValue = eve.Entry.CurrentValues.GetValue<object>(ve.PropertyName);

                            EntityProperty ep = new EntityProperty {PropertyName= ve.PropertyName,Value= propertyValue!=null?propertyValue.ToString():"", ErrorMessage=ve.ErrorMessage};
                            error.EntityProperties.Add(ep);
                        }
                        errors.Add(error);
                    }

                    return JsonConvert.SerializeObject(errors);
                }
                return base.Message;
            }
        }

    }
}
