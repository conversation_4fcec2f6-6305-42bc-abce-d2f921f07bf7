﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    public class PerformanceLog
    {
       [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string Area { get; set; }

        [StringLength(50)]
        public string Controller { get; set; }

        [StringLength(50)]
        public string Action { get; set; }
        [StringLength(2000)]
        public string Url { get; set; }
        [StringLength(2000)]
        public string ReferrerUrl { get; set; }

        [StringLength(50)]
        public string Method { get; set; }

        [StringLength(50)]
        public string StatusCode { get; set; }
        public bool IsAjax { get; set; }
        [StringLength(200)]
        public string Browser { get; set; }
        [StringLength(200)]
        public string BrowserVersion { get; set; }

        public long DurationInMilliseconds { get; set; }

        public DateTime DateCreated { get; set; }

    }
}
