﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    /// <summary>
    /// Cerebrum3AuditContext class
    /// </summary>
    public class CerebrumAuditContext : DbContext
    {
        /// <summary>
        /// Cerebrum3AuditContext constructor
        /// </summary>
        public CerebrumAuditContext() : base("name=C3AuditContext")
        {
            Database.SetInitializer<CerebrumAuditContext>(null);
            base.Configuration.ProxyCreationEnabled = false;            
        }
        /// <summary>
        /// Create Cerebrum3AuditContext overload
        /// </summary>
        /// <returns></returns>
        public static CerebrumAuditContext Create()
        {
            return new CerebrumAuditContext();
        }
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Properties<System.DateTime>().Configure(c => c.HasColumnType("datetime2"));
            //modelBuilder.Entity<Audit>().Property(p => p.ixChanges).hasd("AS JSON_QUERY(Changes, '$.changes')");
        }
        /// <summary>
        /// Activity tables in Cerebrum3AuditContext
        /// </summary>
        //public DbSet<PatientRecordActivity> PatientRecordActivities { get; set; }
        public DbSet<Log> Logs { get; set; }
        public DbSet<PerformanceLog> PerformanceLogs { get; set; }
        public DbSet<AuditPage> AuditPages { get; set; }
        public DbSet<DemographicSync> DemographicSync { get; set; }
        public DbSet<UserSync> UserSync { get; set; }
        public DbSet<Audit> Audits { get; set; }
        public DbSet<VirtualVisitLog> VirtualVisitLogs { get; set; }

        public IEnumerable<T> GetData<T>(string storedProcedure, List<SqlParameter> parameters = null) where T : class
        {
            List<T> list = new List<T>();
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                //cmd.CommandTimeout = sqlCommandTimeOut;
                try
                {
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    cmd.Connection.Open();
                    list = ((IObjectContextAdapter)this).ObjectContext.Translate<T>(cmd.ExecuteReader()).ToList();
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            return list;
        }
    }
    /// <summary>
    /// Init C3 Audit DB 
    /// </summary>
    public class C3AuditInitializer : DropCreateDatabaseIfModelChanges<CerebrumAuditContext> //CreateDatabaseIfNotExists<Cerebrum3AuditContext>
    {
        /// <summary>
        /// Populate C3 Audit as needed 
        /// </summary>
        /// <param name="context"></param>
        protected override void Seed(CerebrumAuditContext context)
        {
            base.Seed(context);
        }
    }
}
