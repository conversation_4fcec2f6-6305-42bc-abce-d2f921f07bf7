﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    public class AuditPage
    {        
        [Key]
        public int Id { get; set; }
        public int UserId { get; set; }
        [StringLength(50)]
        public string PatientId { get; set; }
        [Required]
        [StringLength(500)]
        public string UserName { get; set; }
        [Required]
        [StringLength(3000)]
        public string URLAccessed { get; set; }
        [StringLength(100)]
        public string HTTPMethod { get; set; }
        [StringLength(100)]
        public string Area { get; set; }
        [StringLength(100)]
        public string Controller { get; set; }
        [StringLength(100)]
        public string Action { get; set; }
        [StringLength(1500)]
        public string UserAgent { get; set; }
        [StringLength(100)]
        public string Browser { get; set; }
        [StringLength(100)]
        public string BrowserVersion { get; set; }
        [Required]
        [StringLength(50)]
        public string IpAddress { get; set; }        
        [StringLength(30)]
        public string ServerPort { get; set; }
        [Required]
        public DateTime TimeAccessed { get; set; }
       
    }
}
