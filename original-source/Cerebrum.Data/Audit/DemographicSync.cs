﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    public class DemographicSync
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int SyncId { get; set; }
        [StringLength(100)]
        public string FederatedId { get; set; }
        public int namePrefix { get; set; }
        [StringLength(100)]
        public string firstName { get; set; }
        [StringLength(100)]
        public string middleName { get; set; }
        [StringLength(100)]
        public string lastName { get; set; }
        [StringLength(100)]
        public string preferredName { get; set; }
        [StringLength(100)]
        public string useAliases { get; set; }
        [StringLength(100)]
        public string aliasFirstName { get; set; }
        [StringLength(100)]
        public string aliasMiddleName { get; set; }
        [StringLength(100)]
        public string aliasLastName { get; set; }
        public DateTime? dateOfBirth { get; set; }
        [StringLength(20)]
        public string SIN { get; set; }
        [StringLength(100)]
        public string chartNumber { get; set; }
        [StringLength(20)]
        public string pharmacyFaxNumber { get; set; }
        [StringLength(20)]
        public string pharmacyPhoneNumber { get; set; }
        [StringLength(500)]
        public string pictureURL { get; set; }
        [StringLength(50)]
        public string email { get; set; }
        public bool consentEmail { get; set; }
        [StringLength(50)]
        public string password { get; set; }
        public int gender { get; set; }
        public int officialSpokenLanguage { get; set; }
        [StringLength(50)]
        public string uniqueVendorIdSequence { get; set; }
        public bool preferredOfficialLanguageSpecified { get; set; }
        [StringLength(100)]
        public string preferredSpokenLanguage { get; set; }
        [StringLength(4000)]
        public string notesAboutPatient { get; set; }
        public int personStatusCode { get; set; }
        public DateTime? personStatusDate { get; set; }
        public bool personStatusDateSpecified { get; set; }
        [StringLength(100)]
        public string HospitalCode { get; set; }
        public int InsuranceCompanyId { get; set; }
        public int insuranceType { get; set; }
        public int defaultPaymentMethod { get; set; }
        public int active { get; set; }
        public int PatientRecordId { get; set; }
        public int? search_pid_1 { get; set; }
    }
}
