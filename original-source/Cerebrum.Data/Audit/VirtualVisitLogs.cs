﻿using Cerebrum.VirtualVisit.Seedwork.DAL;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data.Audit
{
    public class VirtualVisitLog: IVirtualVisitLogDAL
    {
        [Key]
        public int Id { get; set; }
        [StringLength(256)]
        public string VirtualVisitRoomId { get; set; }
        public DateTime? EventStartDate { get; set; } = DateTime.Now;

        /// <summary>
        /// The Event Type only needs to identify if the visit was Clinical – Direct (IE. Patient to Provider) or Clinical - Indirect (Provider to Provider). 
        /// 
        /// Default is Clinical - Direct, you can set as 1.. and Clinical - Indirect (2)
        /// </summary>
        public int? EventTypeId { get; set; } = 1;
        [StringLength(256)]
        public string EventActivity { get; set; }
        [StringLength(50)]
        public string EventModality { get; set; }
        public int? PatientId { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }
        [StringLength(256)]
        public string EventDetails { get; set; }
    }
}
