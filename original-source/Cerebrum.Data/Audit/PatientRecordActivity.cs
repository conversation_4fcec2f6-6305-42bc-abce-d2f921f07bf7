﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    public enum PatientRecordActivityType { ViewDemographics, EditDemographics, ViewReport, EditReport }
    public enum LoginStatus { Successed, Failed }
    /// <summary>
    /// Build PatientRecordActivity for C3Audit
    /// </summary>
    public class PatientRecordActivity
    {
        /// <summary>
        /// PatientRecordActivity ID
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// Patient Record Id Accessed 
        /// </summary>
        public int PatientRecordId { get; set; }
        /// <summary>
        /// Patient Record Access Time Started
        /// </summary>
        public DateTime AccessTimeStart { get; set; }
        /// <summary>
        /// Who Accessed the Patient Record
        /// </summary>
        public string AccessedBy { get; set; }
        /// <summary>
        /// Accessed from IP address specified
        /// </summary>
        public string AccessedFromIP { get; set; }
        /// <summary>
        /// Is True if patient document was accessed
        /// </summary>
        public bool IfAccessedDocuments { get; set; } = true;
        /// <summary>
        /// The url of the document accessed
        /// </summary>
        public string AccessedDocumentUrl { get; set; }
        /// <summary>
        /// Type of activity on Patient Record
        /// </summary>
        public PatientRecordActivityType TypeOfActivity { get; set; } = PatientRecordActivityType.ViewDemographics;
    }
    public class LoginLog
    {
        public int Id { get; set; }
        public string UserId { get; set; }
        public string UserName { get; set; }
        public DateTime Date { get; set; } = DateTime.Now;
        public string IPaddress { get; set; }
        public LoginStatus Status { get; set; }
    }
    /// <summary>
    /// Audit class contains Audit info in 2 levels
    /// 1. To record what page, by whom and when was accessed
    /// 2. Details what specific data was accessed
    /// </summary>
    
    public class AuditData
    {
        [Key]
        public int id { get; set; }

        [Required]
        [MaxLength(50)]
        public string UserID { get; set; }

        [MaxLength(50)]
        [Required]
        public string IPAddress { get; set; } = "::1";

        [Required]
        public DateTime EventDateTime { get; set; }

        [Required]
        [MaxLength(1)]
        public string EventType { get; set; }

        [Required]
        [MaxLength(100)]
        public string TableName { get; set; }

        [Required]
        [MaxLength(100)]
        public string RecordID { get; set; }

        [Required]
        [MaxLength(100)]
        public string ColumnName { get; set; }

        public string OriginalValue { get; set; }

        public string NewValue { get; set; }
    }
    public interface IDescribableEntity
    {
        // Override this method to provide a description of the entity for audit purposes
        string Describe();
    }
    public class Item : IDescribableEntity
    {
        [Key]
        public System.Guid ItemID { get; set; }

        [Required]
        [MaxLength(100)]
        public string ItemDescription { get; set; }

        public virtual ICollection ItemAdditionalAttributes { get; set; }

        public string Describe()
        {
            return "{ ItemID : \"" + ItemID + "\", ItemDescription : \"" + ItemDescription + "\" }";
        }
    }
}
