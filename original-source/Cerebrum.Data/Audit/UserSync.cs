﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    public class UserSync
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.None)]
        [StringLength(128)]
        public string SyncId { get; set; }
        public int PracticeID { get; set; }
        public int UserID { get; set; }
        public int? Salut { get; set; }
        public int? Status { get; set; }
        [StringLength(50)]
        public string FirstName { get; set; }
        [StringLength(50)]
        public string MiddleName { get; set; }
        [StringLength(50)]
        public string LastName { get; set; }
        [StringLength(150)]
        public string Address { get; set; }
        [StringLength(100)]
        public string City { get; set; }
        [StringLength(100)]
        public string Province { get; set; }
        [StringLength(10)]
        public string PostalCode { get; set; }
        [StringLength(25)]
        public string CellPhone { get; set; }
        public int? UserCountry { get; set; }
        public int? CerebrumUserType { get; set; }
        public int? UserLoginPersistance { get; set; }
        public bool? EveryWhere { get; set; }
        public bool? PasswordChangeRequired { get; set; }
        [StringLength(15)]
        public string PhoneNumber { get; set; }
        [StringLength(2000)]
        public string PasswordHash { get; set; }
        [StringLength(4000)]
        public string SecurityStamp { get; set; }
        [StringLength(256)]
        public string Email { get; set; }
        public bool? EmailConfirmed { get; set; }
        public bool? PhoneNumberConfirmed { get; set; }
        public bool? TwoFactorEnabled { get; set; }
        public DateTime? LockoutEndDateUtc { get; set; }
        public bool? LockoutEnabled { get; set; }
        public int? AccessFailedCount { get; set; }
        [StringLength(256)]
        public string UserName { get; set; }

    }
}
