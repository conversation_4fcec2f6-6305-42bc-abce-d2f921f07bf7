﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    public class VMAudit
    {
        public string tabelName { get; set; }
        public string primaryKeyName { get; set; }
        public string primaryKeyValue { get; set; }
        public DbEntityEntry entry { get; set; }
        public Dictionary<string, object> Added { get; set; } = new Dictionary<string, object>();
        public List<AuditVM> Modified { get; set; } = new List<AuditVM>();
        public List<AuditVM> Deleted { get; set; } = new List<AuditVM>();

    }
}
