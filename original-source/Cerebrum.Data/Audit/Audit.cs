﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    public class Audit
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }
        [StringLength(1)]
        public string EventType { get; set; }
        public DateTime EventDateTime { get; set; }
        [StringLength(100)]
        public string TableName { get; set; }
        public int PatientRecordId { get; set; }
        [Column(TypeName = "varchar")]
        public string Changes { get; set; }
        [NotMapped]
        [Column(TypeName = "varchar")]
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public string ixChanges { get; set; }
    }
    public class AuditVM
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }
        public string URL { get; set; }
        [StringLength(1)]
        public string EventType { get; set; }
        public DateTime EventDateTime { get; set; }
        [StringLength(100)]
        public string TableName { get; set; }
        public int PatientRecordId { get; set; }
        public string primaryKey { get; set; }
        public string primaryValue { get; set; }
        [StringLength(8000)]
        public string Changes { get; set; }
        public List<AuditValue> AuditValues { get; set; } = new List<AuditValue>();
    }
    public class AuditValue
    {
        public int Id { get; set; }
        //public int AuditId { get; set; }
        //public virtual Audit Audit { get; set; }
        [StringLength(200)]
        public string ColumnName { get; set; }
        [StringLength(4000)]
        public string OriginalValue { get; set; }
        [StringLength(4000)]
        public string NewValue { get; set; }
    }
    public class TableModel
    {
        public string TableName { get; set; }
        public string primaryKey { get; set; }
        public string primaryValue { get; set; }

        public List<ValueModel> changes { get; set; } = new List<ValueModel>();
    }
    public class ValueModel
    {
        public ValueModel()
        {

        }
        public ValueModel(string cn,string ov,string nv)
        {
            this.CN = cn;
            this.OV = ov;
            this.NV = nv;
        }
        //public string TableName { get; set; }
        public string CN { get; set; }
        public string OV { get; set; }
        public string NV { get; set; }
    }
}
