﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Audit
{
    public class Log
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Key, Column(Order = 1)]
        public int Id { get; set; }

        [Key, Column(Order = 0)]
        public DateTime Date { get; set; }
        [StringLength(150)]
        public string userName { get; set; }
        [StringLength(15)]
        public string ipaddress { get; set; }
        [StringLength(1000)]
        public string page { get; set; }

        [StringLength(5)]
        public string Thread { get; set; }
        [StringLength(50)]
        public string Level { get; set; }
        [StringLength(255)]
        public string Logger { get; set; }

        [StringLength(8000)]
        public string Message { get; set; }

        [StringLength(1)]
        public string LoggedIn { get; set; }

        public int? PracticeID { get; set; }

    }
}
