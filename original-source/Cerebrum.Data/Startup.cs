﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public static class Startup
    {
        static bool IsInitialized { get; set; }

        public static void RegisterMapper(IMapperConfigurationExpression cfg)
        {
            if (IsInitialized) throw new Exception("RegisterMapper should be called only once globally.");
            IsInitialized = true;
            AppointmentJSON.RegisterMapper(cfg);
        }
    }
}
