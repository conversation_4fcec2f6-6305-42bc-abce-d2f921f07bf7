﻿using log4net;
using log4net.Repository.Hierarchy;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure.Interception;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class QueryLog : IDbCommandInterceptor
    {
        private static readonly log4net.ILog Logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public void NonQueryExecuting(
            DbCommand command, DbCommandInterceptionContext<int> interceptionContext)
        {
            LogQuery(command, interceptionContext);
        }

        public void NonQueryExecuted(
            DbCommand command, DbCommandInterceptionContext<int> interceptionContext)
        {
            LogQuery(command, interceptionContext);
        }

        public void ReaderExecuting(
            DbCommand command, DbCommandInterceptionContext<DbDataReader> interceptionContext)
        {
            LogQuery(command, interceptionContext);
        }

        public void ReaderExecuted(
            DbCommand command, DbCommandInterceptionContext<DbDataReader> interceptionContext)
        {
            LogIfError(command, interceptionContext);
        }

        public void ScalarExecuting(
            DbCommand command, DbCommandInterceptionContext<object> interceptionContext)
        {
            LogQuery(command, interceptionContext);
        }

        public void ScalarExecuted(
            DbCommand command, DbCommandInterceptionContext<object> interceptionContext)
        {
            LogIfError(command, interceptionContext);
        }

        private void LogIfNonAsync<TResult>(
            DbCommand command, DbCommandInterceptionContext<TResult> interceptionContext)
        {
            //if (!interceptionContext.IsAsync)
            // {
            //Logger.Warn($"Non-async command used: {command.CommandText}");
            // }
        }
        private void LogQuery<TResult>(
           DbCommand command, DbCommandInterceptionContext<TResult> interceptionContext)
        {
            string query = command.CommandText;
            //if (query.Contains("PatientRecordId"))
            //{
            var parameters = LogEfQueryParameters(command);
            //Logger.Audit($"{query} {parameters}");
#if DEBUG
            
         Debug.WriteLine($"{query} {parameters}");
#endif
            //}
        }
        private string LogEfQueryParameters(DbCommand command)
        {
            var sb = new StringBuilder();
            for (int i = 0; i < command.Parameters.Count; i++)
            {
                if (command.Parameters[i].Value != null)
                    sb.AppendLine($"{command.Parameters[i].ParameterName}: {command.Parameters[i].Value}");
            }
            return sb.ToString();
        }

        private void LogIfError<TResult>(
            DbCommand command, DbCommandInterceptionContext<TResult> interceptionContext)
        {
            if (interceptionContext.Exception != null)
            {
                var parameters = LogEfQueryParameters(command);
                //Logger.Error($"Command {command.CommandText} {parameters} failed with exception {interceptionContext.Exception}");
                System.Diagnostics.Trace.WriteLine($"Command {command.CommandText} {parameters} failed with exception {interceptionContext.Exception}");
            }
        }
    }
}
