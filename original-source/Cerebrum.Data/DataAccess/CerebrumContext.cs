﻿using Cerebrum.Data.Audit;
using Microsoft.AspNet.Identity.EntityFramework;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.ComponentModel.DataAnnotations;
using System.Transactions;
using Cerebrum.Data.Entities.VisitPage;
using Cerebrum.Data.Entities.OLIS;
using Newtonsoft.Json;
using Cerebrum.Data.Attributes;
using System.Threading.Tasks;
using System.Data.Entity.Core.Objects;
using System.Data.SqlClient;
using System.Data.Entity.ModelConfiguration.Conventions;
using Cerebrum.Data.Entities.Measurement;
using Cerebrum.Data.Entities.Common;
using Cerebrum.Data.Entities;
using System.Data.Entity.Validation;
using System.Data;
using System.Collections;
using Cerebrum.Data.Entities.Econsults;
using Cerebrum.Data.Entities.Document;

namespace Cerebrum.Data
{
    public class CerebrumContext : IdentityDbContext<ApplicationUser>
    {
        private string IPAddress = string.Empty;
        private int userId = 0; //"Anonymous";
        private string RequestURL = string.Empty;
        private readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public static bool IsSaveChangesUseAsync { get; set; } = System.Configuration.ConfigurationManager.AppSettings["IsSaveChangesUseAsync"] != null ? Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["IsSaveChangesUseAsync"]) : false; // for ticket 12468
        public CerebrumContext() : base("C3Context", false)
        {
            //Database.SetInitializer<CerebrumContext>(new MigrateDatabaseToLatestVersion<CerebrumContext, Configuration>());
            Database.SetInitializer<CerebrumContext>(null);
            IPAddress = " ";
            userId = 0;
        }
        public CerebrumContext(int userid, string ipaddress) : base("C3Context", false)
        {
            IPAddress = ipaddress;
            userId = userid;
        }

        public CerebrumContext(int userid) : base("C3Context", false)  //called from billing service
        {
            Database.SetInitializer<CerebrumContext>(null);
            IPAddress = "BillingService";
            userId = userid;
        }

        public static CerebrumContext Create()
        {
            return new CerebrumContext();
        }
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            // base.OnModelCreating(modelBuilder);

            base.OnModelCreating(modelBuilder);
            modelBuilder.Properties<System.DateTime>().Configure(c => c.HasColumnType("datetime2"));
            modelBuilder.Conventions.Remove<OneToManyCascadeDeleteConvention>();
            modelBuilder.Conventions.Remove<ManyToManyCascadeDeleteConvention>();
        }
        public DbSet<Master> Master { get; set; }
        public DbSet<SpokenLanguage> Languages { get; set; }
        public DbSet<EnrolmentTerminationReason> TerminationReasons { get; set; }
        public DbSet<CUser> CUsers { get; set; }

        public DbSet<AspNetUserPasswordHistorty> PasswordHistorty { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<LoginHistory> LoginHistory { get; set; }
        public DbSet<ExternalDoctor> ExternalDoctors { get; set; }
        public DbSet<ExternalDoctorPhoneNumber> ExternalDoctorPhoneNumbers { get; set; }
        public DbSet<ExternalDoctorAddress> ExternalDoctorAddresses { get; set; }

        public DbSet<ExternalDoctorLocation> ExternalDoctorLocations { get; set; }
        public DbSet<Specialty> Specialties { get; set; }
        public DbSet<Test> Tests { get; set; }
        //public DbSet<TestResource> TestResources { get; set; }
        public DbSet<TestResourceType> TestResourceTypes { get; set; }
        public DbSet<Modality> Modalities { get; set; }
        public DbSet<TestModality> TestModalities { get; set; }
        public DbSet<TestReportingDoctor> TestReportingDoctor { get; set; }

        public DbSet<InternalFileName> InternalFileNames { get; set; }
        //public DbSet<TestStatus> TestStatuses { get; set; }
        public DbSet<InsuranceCompany> InsuranceCompanys { get; set; }
        public DbSet<OHIPTimeLimitedFeeCode> OHIPTimeLimitedFeeCodes { get; set; }
        //Practice
        public DbSet<Practice> Practices { get; set; }
        //PracticeDoctorType
        public DbSet<PracticeDoctorType> PracticeDoctorTypes { get; set; }
        public DbSet<PracticeDoctorReportFooter> PracticeDoctorReportFooter { get; set; }
        public DbSet<PracticeDoctorReportSignature> PracticeDoctorReportSingature { get; set; }

        public DbSet<PracticeDoctor> PracticeDoctors { get; set; }
        public DbSet<PracticeExternalDoctor> PracticeExternalDoctors { get; set; }
        public DbSet<PracticeTest> PracticeTests { get; set; }
        public DbSet<PracticeTestGroup> PracticeTestGroups { get; set; }
        public DbSet<PracticeHospital> PracticeHospitals { get; set; }
        public DbSet<BookingConfirmationMessage> BookingConfirmationMessages { get; set; }
        //Schedule
        public DbSet<ScheduleUser> ScheduleUsers { get; set; }
        public DbSet<ScheduleWeekDay> ScheduleWeekDays { get; set; }
        public DbSet<TestResource> Resources { get; set; }
        public DbSet<PracticeTestResource> PracticeTestResources { get; set; }

        public DbSet<Schedule> Schedules { get; set; }
        public DbSet<ScheduleDay> ScheduleDays { get; set; }
        public DbSet<ScheduleOfficeNote> ScheduleOfficeNotes { get; set; }

        public DbSet<UserScheduleViewFilter> UserScheduleViewFilters { get; set; }

        public DbSet<ScheduleStaffNote> ScheduleStaffNotes { get; set; }

        public DbSet<DoctorComment> DoctorComments { get; set; }
        public DbSet<Hospital> Hospitals { get; set; }


        public DbSet<AppointmentTestSaveLog> AppointmentTestSaveLogs { get; set; }

        public DbSet<RootTemplate> RootTemplates { get; set; }
        public DbSet<RootCategory> RootCategories { get; set; }
        public DbSet<RootCategoryPhrase> RootCategoryPhrases { get; set; }
        public DbSet<RootCategoryPhraseUsed> RootCategoryPhrasesUsed { get; set; }
        public DbSet<RootCategoryTemplate> RootCategoryTemplates { get; set; }
        public DbSet<PracticeRootCategoryTemplate> PracticeRootCategoryTemplates { get; set; }
        public DbSet<PracticeRootCategory> PracticeRootCategories { get; set; }
        public DbSet<PracticeRootCategoryPhrase> PracticeRootCategoryPhrases { get; set; }
        public DbSet<DoctorRootCategoryTemplate> DoctorRootCategoryTemplates { get; set; }
        public DbSet<DoctorRootCategory> DoctorRootCategories { get; set; }
        public DbSet<DoctorRootCategoryPhrase> DoctorRootCategoryPhrases { get; set; }
        public DbSet<RootCategorySavedValue> RootCategorySavedValues { get; set; }
        public DbSet<VPMeasurementSavedValue> VPMeasurementSavedValues { get; set; }
        public DbSet<VPLabResult> VPLabResults { get; set; }
        public DbSet<PatientBP> PatientBP { get; set; }

        public DbSet<tempVPRootCategory> tempVPRootCategories { get; set; }
        public DbSet<tempVPPhrase> tempVPPhrases { get; set; }
        public DbSet<tempVPLog> tempVPLogs { get; set; }

        public DbSet<WSRootCategory> WSRootCategories { get; set; }

        public DbSet<WSItem> WSItems { get; set; }

        public DbSet<LandingPage> LandingPages { get; set; }

        public DbSet<UserLandingPage> UserLandingPages { get; set; }

        //Office
        public DbSet<Office> Offices { get; set; }
        public DbSet<OfficeRoom> OfficeRooms { get; set; }
        public DbSet<OfficeRoomType> OfficeRoomTypes { get; set; }
        public DbSet<OfficeRoomPermission> OfficeRoomPermissions { get; set; }
        public DbSet<OfficeGroupBillingNumber> OfficeGroupBillingNumbers { get; set; }
        public DbSet<OfficeUrl> OfficeUrls { get; set; }
        public DbSet<OfficeUrlType> OfficeUrlTypes { get; set; }
        public DbSet<OfficeFaxFolder> OfficeFaxFolders { get; set; }
        public DbSet<OfficeSetting> OfficeSettings { get; set; }
        public DbSet<BillingType_Office_Test> BillingType_Office_Tests { get; set; }
        public DbSet<OfficeOutlook> OfficeOutlooks { get; set; }
        public DbSet<OfficeEmail> OfficeEmails { get; set; }
        public DbSet<TriageUrgency> TriageUrgencies { get; set; }
        public DbSet<TriageDisposition> TriageDispositions { get; set; }

        public DbSet<TriageStatus> TriageStatuses { get; set; }

        //Permissions
        public DbSet<PermissionsBase> PermissionsBases { get; set; }
        public DbSet<PermissionType> PermissionTypes { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        //Patients
        public DbSet<PatientRecord> PatientRecords { get; set; }
        public DbSet<Pharmacy> Pharmacies { get; set; }
        public DbSet<PatientPharmacy> PatientPharmacies { get; set; }

        public DbSet<PatientLocation> PatientLocations { get; set; }

        public DbSet<PatientAlert> PatientAlerts { get; set; }

        public DbSet<PatientRecordMessage> PatientRecordMessages { get; set; }
        public DbSet<PatientMRN> PatientMRNs { get; set; }
        public DbSet<PersonalHistory> PatientHistories { get; set; }
        public DbSet<PastHealth> PastHealth { get; set; }
        public DbSet<PastHealthStandardCoding> PastHealthStandardCoding { get; set; }
        public DbSet<PastHealthResidualInfo> PastHealthResidualInfo { get; set; }
        public DbSet<FamilyHistory> FamilyHistories { get; set; }
        public DbSet<FamilyHistoryStandardCoding> FamilyHistoryStandardCoding { get; set; }
        public DbSet<FamilyHistoryResidualInfo> FamilyHistoryResidualInfo { get; set; }

        public DbSet<ProblemList> ProblemLists { get; set; }
        public DbSet<ProblemListStandardCoding> ProblemListStandardCoding { get; set; }
        public DbSet<ProblemListResidualInfo> ProblemListResidualInfo { get; set; }
        public DbSet<PatientCohort> PatientCohorts { get; set; }
        public DbSet<PatientAuthorization> PatientAuthorizations { get; set; }
        public DbSet<Cohort> Cohorts { get; set; }
        public DbSet<CohortClass> CohortClasses { get; set; }
        public DbSet<ActiveHRM_User> ActiveHRM_Users { get; set; }

        public DbSet<PatientNoteType> PatientNoteTypes { get; set; }

        public DbSet<PatientAdditionalNote> PatientAdditionalNotes { get; set; }


        //Demographics
        public DbSet<Demographic> Demographics { get; set; }
        public DbSet<DemographicsAddress> DemographicsAddress { get; set; }
        public DbSet<DemographicsNextOfKin> DemographicsContacts { get; set; }
        public DbSet<DemographicsContactPhoneNumber> DemographicsContactPhoneNumbers { get; set; }
        public DbSet<DemographicsHealthCard> HealthCards { get; set; }
        public DbSet<DemographicsPhoneNumber> PhoneNumber { get; set; }
        public DbSet<DemographicsFamilyDoctor> DemographicsFamilyDoctors { get; set; }
        public DbSet<DemographicsMainResponsiblePhysician> DemographicsMainResponsiblePhysicians { get; set; }
        public DbSet<DemographicsDefaultReferralDoctor> DemographicsDefaultReferralDoctors { get; set; }
        public DbSet<DemographicsAssociatedDoctor> DemographicsAssociatedDoctors { get; set; }
        public DbSet<DemographicsEnrollment> DemographicsEnrollments { get; set; }
        //public DbSet<CountryProvince> CountryProvinces { get; set; }
        //Appointments 
        public DbSet<AppointmentC2ToC3> AppointmentsC2ToC3 { get; set; }
        public DbSet<AppC2ToC3Url> AppC2ToC3Urls { get; set; }
        public DbSet<Appointment> Appointments { get; set; }
        public DbSet<AppointmentsAssociation> AppointmentsAssociations { get; set; }
        public DbSet<AppointmentPreconditon> AppointmentPreconditions { get; set; }
        public DbSet<AppointmentProvider> AppointmentProviders { get; set; }
        public DbSet<AppointmentType> AppointmentTypes { get; set; }
        public DbSet<AppointmentTypeC2toC3> AppointmentTypeC2toC3 { get; set; }
        public DbSet<AppointmentHealthCardValidation> AppointmentHealthCardValidation { get; set; }

        public DbSet<PracticeAppointmentType> PracticeAppointmentTypes { get; set; }

        public DbSet<AppointmentModifier> AppointmentModifiers { get; set; }
        public DbSet<AppointmentTest> AppointmentTests { get; set; }
        public DbSet<AppointmentTestLegacyDoc> AppointmentTestLegacyDocs { get; set; }
        public DbSet<AppointmentTestResource> AppointmentTestResources { get; set; }
        public DbSet<AppointmentStatusLog> AppointmentStatusLogs { get; set; }
        public DbSet<AppointmentTestStatus> AppointmentTestStatus { get; set; }
        public DbSet<AppointmentTriageDisposition> AppointmentTriageDispositions { get; set; }
        public DbSet<ScheduleColorSchema> ScheduleColorSchemas { get; set; }
        public DbSet<CancellationReason> CancellationReasons { get; set; }
        //HL7
        public DbSet<HL7SensitiveResultAccessLog> HL7SensitiveResultAccessLogs { get; set; }
        public DbSet<HL7Coding> HL7Codings { get; set; }
        public DbSet<HL7Lab> HL7Labs { get; set; }
        public DbSet<HL7MarkedSeen> HL7MarkedSeens { get; set; }
        public DbSet<HL7Message> HL7Messages { get; set; }
        public DbSet<HL7Patient> HL7Patients { get; set; }
        public DbSet<HL7Report> HL7Reports { get; set; }
        public DbSet<HL7ReportDoctor> HL7ReportDoctors { get; set; }
        public DbSet<HL7ReportNote> HL7ReportNotes { get; set; }
        public DbSet<HL7ReportVersion> HL7ReportVersions { get; set; }
        public DbSet<HL7Result> HL7Results { get; set; }
        public DbSet<HL7ResultNote> HL7ResultNotes { get; set; }
        public DbSet<LabUser> LabUsers { get; set; }

        public DbSet<HL7TestDescription> HL7TestDescriptions { get; set; }
        public DbSet<HL7LostReport> HL7LostReports { get; set; }
        public DbSet<HL7LostReportDoctor> HL7LostReportDoctors { get; set; }
        //OLIS

        public DbSet<OLISTestRequestCategory> OLISTestRequestCategories { get; set; }
        public DbSet<OLISTestRequestSubCategory> OLISTestRequestSubCategories { get; set; }
        public DbSet<OLISTestReportCategory> OLISTestReportCategories { get; set; }
        public DbSet<OLISTestRequestNomenclature> OLISTestRequestNomenclatures { get; set; }
        public DbSet<OLISTestResultCategory> OLISTestResultCategories { get; set; }
        public DbSet<OLISTestResultNomenclature> OLISTestResultNomenclature { get; set; }
        public DbSet<OLISQueryMessage> OLISQueryMessages { get; set; }
        public DbSet<OLISMicrorganism> OLISMicrorganisms { get; set; }

        public DbSet<OLISBusinessLogicErrorCode> OLISBusinessLogicErrorCodes { get; set; }
        public DbSet<OLISQueryParameterError> OLISQueryParameterErrors { get; set; }
        public DbSet<OLISCommunicationLog> OLISCommunicationLogs { get; set; }
        public DbSet<OLISReceivedReport> OLISReceivedReports { get; set; }
        public DbSet<OLISReceivedResponseFile> OLISReceivedResponseFiles { get; set; }

        public DbSet<OLISReceivedReportPatient> OLISReceivedReportPatients { get; set; }
        public DbSet<OLISReceivedReportDetail> OLISReceivedReportDetails { get; set; }
        public DbSet<OLISReportDoctor> OLISReportDoctors { get; set; }

        public DbSet<OLISReportResult> OLISReportResults { get; set; }

        //public DbSet<OLISCommunicationError> OLISCommunicationErrors { get; set; }
        public DbSet<Laboratory> Laboratories { get; set; }


        //Contact Manager 
        public DbSet<CM_TaskDefinition> CM_TaskDefinitions { get; set; }
        public DbSet<CM_TaskMessage> CM_TaskMessages { get; set; }
        public DbSet<CM_TaskReport> CM_TaskReports { get; set; }
        public DbSet<CM_TaskMessageRecipient> CM_TaskMessageRecipients { get; set; }

        //Reports
        public DbSet<LabResult> LabResults { get; set; }
        public DbSet<ExternalReport> ExternalReports { get; set; }

        public DbSet<SSRSReport> SSRSReports { get; set; }

        //impressions/phrases
        public DbSet<ReportPhrase> ReportPhrase { get; set; }

        public DbSet<ReportPhraseByDoctor> ReportPhraseByDoctor { get; set; }

        public DbSet<ReportPhraseByPractice> ReportPhraseByPractice { get; set; }

        public DbSet<ReportPhraseSavedText> ReportPhraseSavedText { get; set; }

        public DbSet<ReportPhraseSavedValue> ReportPhraseSavedValue { get; set; }


        //Measurements
        public DbSet<MeasurementCategory> MeasurementCategory { get; set; }

        public DbSet<Measurement> Measurement { get; set; }

        public DbSet<MeasurementByPractice> MeasurementByPractice { get; set; }

        public DbSet<MeasurementMapping> MeasurementMapping { get; set; }

        public DbSet<MeasurementRange> MeasurementRange { get; set; }

        public DbSet<MeasurementBSARange> MeasurementBSARange { get; set; }

        public DbSet<MeasurementRangeType> MeasurementRangeType { get; set; }

        public DbSet<MeasurementOperator> MeasurementOperator { get; set; }

        public DbSet<MeasurementSavedValue> MeasurementSavedValue { get; set; }

        public DbSet<ReportPhraseNormal> ReportPhraseNormal { get; set; }

        public DbSet<AppointmentTestLog> AppointmentTestLog { get; set; }

        public DbSet<MeasurementRangeText> MeasurementRangeText { get; set; }

        public DbSet<ReportPhraseMeasurmentCategory> ReportPhraseMeasurmentCategory { get; set; }


        public DbSet<ReportPhraseMeasurmentCategoryScroll> ReportPhraseMeasurmentCategoryScroll { get; set; }

        public DbSet<ReportPhrase_Custom> ReportPhrase_Custom { get; set; }

        public DbSet<ProstaticValve> ProstaticValve { get; set; }

        public DbSet<PatientProstaticValve> PatientProstaticValve { get; set; }
        public DbSet<SendType> SendType { get; set; }
        public DbSet<WS_SendReport> WS_SendReport { get; set; }
        public DbSet<WS_MicroservicesConfig> WS_MicroservicesConfig { get; set; }
        public DbSet<VP_SendReport> VP_SendReport { get; set; }
        public DbSet<VP_Privacy_Notes> VP_Privacy_Notes { get; set; }
        public DbSet<TestGroup> TestGroup { get; set; }
        public DbSet<TestGroupDetail> TestGroupDetail { get; set; }
        public DbSet<Group> Group { get; set; }
        public DbSet<BullEye> BullEye { get; set; }
        public DbSet<CategoryByGroup> CategoryByGroup { get; set; }


        //VP
        public DbSet<PatientBloodPressure> PatientBloodPressure { get; set; }
        public DbSet<PreventiveCareBonusCategory> PreventiveCareBonusCategory { get; set; }
        public DbSet<PreventiveCareBonusCode> PreventiveCareBonusCode { get; set; }
        public DbSet<VPOption> VPOption { get; set; }
        public DbSet<VPCategory> VPCategory { get; set; }
        public DbSet<VPCategoryOption> VPCategoryOption { get; set; }
        public DbSet<VPMeasurement> VPMeasurement { get; set; }
        public DbSet<VPMeasurementOption> VPMeasurementOption { get; set; }
        public DbSet<VPReportPhrase> VPReportPhrase { get; set; }
        public DbSet<VPReportPhraseOption> VPReportPhraseOption { get; set; }
        public DbSet<VPReportPhraseByDoctor> VPReportPhraseByDoctor { get; set; }
        public DbSet<VPTemplateField> VPTemplateField { get; set; }
        public DbSet<VP_CPP_Category> VP_CPP_Category { get; set; }
        public DbSet<VP_CPP_Alert> VP_CPP_Alert { get; set; }
        public DbSet<VP_CPP_RiskFactor> VP_CPP_RiskFactor { get; set; }
        public DbSet<VP_CPP_ImmunizationStatus> VP_CPP_ImmunizationStatus { get; set; }
        public DbSet<VP_CPP_ImmunizationType> VP_CPP_ImmunizationType { get; set; }
        public DbSet<VP_CPP_Immunization> VP_CPP_Immunization { get; set; }
        public DbSet<ImmunizationRecall> ImmunizationRecall { get; set; }

        public DbSet<VP_CPP_Problem_List> VP_CPP_Problem_List { get; set; }
        public DbSet<VP_ReportPhrase_Custom> VP_ReportPhrase_Custom { get; set; }
        public DbSet<VP_AppointmentTestLog> VP_AppointmentTestLog { get; set; }
        public DbSet<VP_MeasurementSavedValue> VP_MeasurementSavedValue { get; set; }
        public DbSet<VP_ReportPhrasesSavedText> VP_ReportPhrasesSavedText { get; set; }
        public DbSet<VP_ReportPhrasesSavedValue> VP_ReportPhrasesSavedValue { get; set; }
        public DbSet<VP_CPP_FamilyHistory> VP_CPP_FamilyHistory { get; set; }
        public DbSet<ICD10> ICD10 { get; set; }
        public DbSet<VP_CPP_Item> VP_CPP_Item { get; set; }
        public DbSet<VP_CPP_Setting> VP_CPP_Setting { get; set; }
        public DbSet<VP_CPP_Skipped> VP_CPP_Skipped { get; set; }
        public DbSet<VP_CPP_Visible_Field> VP_CPP_Visible_Field { get; set; }
        public DbSet<VP_CPP_Problem_Status> VP_CPP_Problem_Status { get; set; }
        public DbSet<CareElementGroup> CareElementGroups { get; set; }
        public DbSet<VPUniqueMeasurement> VPUniqueMeasurement { get; set; }

        public DbSet<VPOptionByPatient> VPOptionByPatient { get; set; }

        public DbSet<VP_Template> VP_Template { get; set; }
        public DbSet<VP_Template_Detail> VP_Template_Detail { get; set; }
        public DbSet<VP_Template_Patient_Detail> VP_Template_Patient_Detail { get; set; }
        public DbSet<VP_Template_Patient_Data> VP_Template_Patient_Data { get; set; }
        public DbSet<VP_ReportPhrases_Skipped> VP_ReportPhrases_Skipped { get; set; }
        public DbSet<DiagnoseCode> DiagnoseCode { get; set; }
        public DbSet<DiagnoseCodeLookup> DiagnoseCodeLookup { get; set; }
        public DbSet<ConsultCode> ConsultCode { get; set; }
        public DbSet<AppointmentBill> AppointmentBill { get; set; }
        public DbSet<VP_Measurements_Patient> VP_Measurements_Patient { get; set; }

        public DbSet<RecallLog> RecallLog { get; set; }


        public DbSet<MobileNetwork> MobileNetworks { get; set; }
        public DbSet<UserMobileNetwork> UserMobileNetworks { get; set; }
        public DbSet<UserOffice> UserOffices { get; set; }
        public DbSet<UserLocation> UserLocations { get; set; }
        public DbSet<UserDoctor> UserDoctors { get; set; }
        public DbSet<UserBillingDoctor> UserBillingDoctors { get; set; }

        //Dashboard
        public DbSet<ExpiredUserSession> ExpiredUserSessions { get; set; }

        //WebBooking
        public DbSet<WebBookingDoctor> WebBookingDoctor { get; set; }


        //External Documents
        public DbSet<DocumentEntity> Documents { get; set; }
        public DbSet<LooseReportCategory> LooseReportCategories { get; set; }
        //
        public DbSet<ReportReceived> ReportsReceived { get; set; }
        public DbSet<ReportReceivedAppointment> ReportReceivedAppointments { get; set; }
        public DbSet<ReportClass> ReportClasses { get; set; }
        public DbSet<ReportSubClass> ReportSubClasses { get; set; }
        public DbSet<ReportAccompanyingSubclass> ReportAccompanyingSubclasses { get; set; }
        public DbSet<ReportAccompanyingMnemonic> ReportAccompanyingMnemonics { get; set; }
        public DbSet<ReportSendingFacility> ReportSendingFacilities { get; set; }
        public DbSet<DoctorsReportReviewed> DoctorsReportsReviewed { get; set; }

        //Requisition
        public DbSet<RequisitionType> RequisitionType { get; set; }
        public DbSet<RequisitionItem> RequisitionItem { get; set; }
        public DbSet<RequisitionPatient> RequisitionPatient { get; set; }
        public DbSet<Requisition> Requisition { get; set; }
        public DbSet<RequisitionResult> RequisitionResult { get; set; }
        public DbSet<RequisitionStatus> RequisitionStatus { get; set; }
        public DbSet<RequisitionTemplate> RequisitionTemplate { get; set; }
        public DbSet<RequisitionTime> RequisitionTime { get; set; }
        public DbSet<Reason> Reasons { get; set; }
        public DbSet<DocService> DocServices { get; set; }


        //Medications
        public DbSet<MedicationDBUpdate> MedicationDBUpdates { get; set; }
        public DbSet<Medication> Medications { get; set; }
        public DbSet<MedicationClass> MedicationClasses { get; set; }
        public DbSet<MedicationForm> MedicationForms { get; set; }
        public DbSet<MedicationIngredient> MedicationIngredients { get; set; }
        public DbSet<MedicationRoute> MedicationRoutes { get; set; }
        public DbSet<MedicationTemplate> MedicationTemplates { get; set; }
        public DbSet<MedicationTemplateClass> MedicationTemplateClasses { get; set; }
        //public DbSet<MedicationTemplateDin> MedicationTemplateDins { get; set; }
        //public DbSet<MedicationTemplateNoDin> MedicationTemplateNoDins { get; set; }
        public DbSet<MedicationDefault> MedicationDefaults { get; set; }
        public DbSet<MedicationFrequencyUnit> MedicationFrequencyUnits { get; set; }
        public DbSet<MedicationStrengthUnit> MedicationStrengthUnits { get; set; }
        public DbSet<AllergyStatus> AllergyStatuses { get; set; }
        public DbSet<PatientAllergy> PatientAllergies { get; set; }
        public DbSet<PatientAllergyIngredient> PatientAllergyIngredients { get; set; }
        //public DbSet<PatientAllergyIngredientDetail> PatientAllergyIngredientDetails { get; set; }
        //public DbSet<PatientAllergyDin> PatientAllergyDins { get; set; }
        //public DbSet<PatientAllergyNoDin> PatientAllergyNoDins { get; set; }
        public DbSet<PatientMedication> PatientMedications { get; set; }
        //public DbSet<PatientMedicationDin> PatientMedicationDins { get; set; }
        //public DbSet<PatientMedicationNoDin> PatientMedicationNoDins { get; set; }
        public DbSet<PatientMedicationSet> PatientMedicationSets { get; set; }
        public DbSet<PatientPrescriptionSet> PatientPrescriptionSets { get; set; }
        public DbSet<PatientPrescription> PatientPrescriptions { get; set; }
        public DbSet<PatientPrescriptionPrint> PatientPrescripionsPrinted { get; set; }
        public DbSet<PrescriptionDefault> PrescriptionDefaults { get; set; }
        public DbSet<PrescriptionStatus> PrescriptionStatuses { get; set; }
        public DbSet<MedicationNoDin> MedicationNoDins { get; set; }
        public DbSet<ReactionType> ReactionTypes { get; set; }
        public DbSet<Severity> Severities { get; set; }
        public DbSet<TreatmentType> TreatmentTypes { get; set; }
        public DbSet<Compliance> Compliances { get; set; }
        public DbSet<DoseChangeReason> DoseChangeReasons { get; set; }
        public DbSet<DiscontinueReason> DiscontinueReasons { get; set; }
        public DbSet<LifeStage> LifeStages { get; set; }
        public DbSet<SigCode> SigCodes { get; set; }

        public DbSet<UserInteractionAlert> UserInteractionAlerts { get; set; }
        public DbSet<UserInteractionSetting> UserInteractionSettings { get; set; }

        public DbSet<UserInteractionSettingType> UserInteractionSettingTypes { get; set; }

        public DbSet<UserInteractionOption> UserInteractionOptions { get; set; }

        public DbSet<PracticeInteractionSetting> PracticeInteractionSettings { get; set; }

        public DbSet<UserPatMedSort> UserPatientMedicationSortSettings { get; set; }

        public DbSet<UserPatMedCPPVisibility> UserPatientMedicationCPPSettings { get; set; }

        //Medications End
        //Inventary begin  StorInventoryStatus
        public DbSet<StoreInventory> StoreInventorys { get; set; }
        public DbSet<StoreInventoryType> StoreInventoryTypes { get; set; }
        public DbSet<StoreInventoryStatus> StoreInventoryStatuses { get; set; }

        public DbSet<PatientEquipment> PatientEquipments { get; set; }
        //Inventory end

        //Billing
        public DbSet<Bill> Bills { get; set; }
        public DbSet<BillDetail> BillDetails { get; set; }
        public DbSet<Billing_EDTFile> BillingEDTFiles { get; set; }
        public DbSet<Billing_EDTId> BillingEDTIds { get; set; }
        public DbSet<Billing_EDTError> BillingEDTErrors { get; set; }
        public DbSet<Billing_EDTErrorCode> BillingEDTErrorCodes { get; set; }
        public DbSet<BillStatus> BillStatuses { get; set; }
        public DbSet<Billing_ConsultRate> BillingConsultRates { get; set; }
        public DbSet<Billing_DiagnoseRate> BillingDiagnoseRates { get; set; }
        public DbSet<Billing_PreventiveCareBonusRate> BillingPreventiveCareBonusRates { get; set; }
        public DbSet<Billing_Group> BillingGroups { get; set; }
        public DbSet<Billing_FileSequence> BillingFileSequences { get; set; }
        public DbSet<Billing_TestRate> BillingTestRates { get; set; }
        public DbSet<Billing_Type> BillingTypes { get; set; }
        public DbSet<Billing_Payor> BillingPayors { get; set; }
        public DbSet<Billing_OHIPFeeScheduleMaster> BillingOHIPFeeScheduleMaster { get; set; }

        public DbSet<Billing_BonusCode> BillingBonusCodes { get; set; }
        public DbSet<Billing_TelephoneConsultCode> TelephoneConsultCode { get; set; }
        public DbSet<Billing_TelephoneConsultCodeBonus> TelephoneConsultCodeBonus { get; set; }
        public DbSet<Billing_File> Billing_Files { get; set; }
        public DbSet<Billing_ExternalDoctorClaimSetting> Billing_ExternalDoctorClaimSettings { get; set; }
        public DbSet<Billing_OMARate> Billing_OMARates { get; set; }

        //Sds Import Export
        public DbSet<ImportEventLog> ImportEventLogs { get; set; }
        public DbSet<ImportEventRecord> ImportEventRecords { get; set; }

        public DbSet<ExportEventLog> ExportEventLogs { get; set; }
        public DbSet<EventLog_Transaction> EventLog_Transactions { get; set; }
        public DbSet<C_Query> C_Queries { get; set; }

        //Hospital Daysheet
        public DbSet<HDAdmission> HDAdmissions { get; set; }
        public DbSet<HDAdmissionAction> HDAdmissionActions { get; set; }

        public DbSet<HDAdmissionType> HDAdmissionTypes { get; set; }

        public DbSet<HDService> HDServices { get; set; }

        public DbSet<HDServiceCode> HDServiceCodes { get; set; }

        public DbSet<HospitalBundleType> HospitalBundleTypes { get; set; }

        public DbSet<HospitalBundle> HospitalBundles { get; set; }

        public DbSet<HospitalServiceCode> HospitalServiceCodes { get; set; }
        public DbSet<PrintLog> PrintLogs { get; set; }

        public DbSet<HRMLog> HRMLogs { get; set; }
        public DbSet<HRMReportClass> HRMReportClasses { get; set; }
        public DbSet<HRMReportSubClass> HRMReportSubClasses { get; set; }
        public DbSet<ReportOBRContent> ReportOBRContent { get; set; }
        public DbSet<ReportSubclassMap> ReportSubclassMap { get; set; }
        public DbSet<ApplicationSetting> ApplicationSettings { get; set; }


        public DbSet<ExternalCommType> ExternalCommTypes { get; set; }

        public DbSet<PracticeCommType> PracticeCommTypes { get; set; }

        public DbSet<PracticeCommRecipient> PracticeCommRecipients { get; set; }

        public DbSet<ReminderType> ReminderTypes { get; set; }
        public DbSet<ReminderRule> ReminderRules { get; set; }
        public DbSet<ReminderSentHistory> ReminderSentHistories { get; set; }

        public DbSet<ZsCoreMethod> ZsCoreMethod { get; set; }
        public DbSet<ZsCoreConst> ZsCoreConst { get; set; }
        public DbSet<ZScoreParameterName> ZScoreParameterName { get; set; }
        public DbSet<ZSCoreParameters> ZSCoreParameters { get; set; }

        //Kiosk
        public DbSet<KioskSet> KioskSets { get; set; }
        public DbSet<KioskMessage> KioskMessages { get; set; }
        public DbSet<KioskCheckin> KioskCheckins { get; set; }
        public DbSet<KioskIpAddress> KioskIpAddresses { get; set; }

        private Lazy<CerebrumAuditContext> auditContext = new Lazy<CerebrumAuditContext>();

        // UserTimesheet
        public DbSet<UserTimesheet> UserTimesheets { get; set; }

        // CDS
        public DbSet<CDSqueue> CDSqueues { get; set; }

        // ReportQueue
        public DbSet<ReportQueue> ReportQueues { get; set; }
        public DbSet<ReportQueueSendStatus> ReportQueueSendStatuses { get; set; }
        public DbSet<ReportQueueSendAttempt> ReportQueueSendAttempts { get; set; }
        public DbSet<ReportQueueResend> ReportQueueResends { get; set; }

        //Study
        public DbSet<Study> Studies { get; set; }
        public DbSet<DoctorStudy> DoctorStudies { get; set; }
        public DbSet<HFiDOCPatient> HFiDOCPatients { get; set; }
        public DbSet<HFiDOCPatientMedication> HFiDOCPatientMedications { get; set; }
        public DbSet<TAPPPatient> TAPPPatients { get; set; }
        public DbSet<TAPPPatientMedication> TAPPPatientMedications { get; set; }

        // Virtual Visit

        public DbSet<VirtualVisitRoom> VirtualVisitRooms { get; set; }
        public DbSet<VirtualVisitInviteGuest> VirtualVisitInviteGuests { get; set; }



        #region eConsult

        public DbSet<Econsult> Econsults { get; set; }
        public DbSet<EconsultAttachedFile> EconsultAttachedFiles { get; set; }
        public DbSet<EconsultOneIdSession> EconsultOneIdSessions { get; set; }
        public DbSet<EconsultSupportedFileType> EconsultSupportedFileTypes { get; set; }
        public DbSet<EconsultPatientAssociation> EconsultPatientAssociations { get; set; }
        public DbSet<EconsultPatientAssociationLog> EconsultPatientAssociationLogs { get; set; }
        public DbSet<EconsultPatientDisassociationReason> EconsultPatientDisassociationReasons { get; set; }

        public DbSet<EconsultMetadata> EconsultMetadata { get; set; }
        public DbSet<EconsultMetadataNote> EconsultMetadataNotes { get; set; }
        public DbSet<EconsultMetadataNoteFile> EconsultMetadataNoteFiles { get; set; }
        public DbSet<EconsultUAO> EconsultUAOs { get; set; }
        public DbSet<EconsultUserUAO> EconsultUserUAOs { get; set; }
        public DbSet<EconsultUAOType> EconsultUAOTypes { get; set; }
        public DbSet<OntarioHealthService> OntarioHealthServices { get; set; }
        public DbSet<UaoService> UaoServices { get; set; }
        public DbSet<UaoServiceScope> UaoServiceScopes { get; set; }


        #endregion

        #region DHDR
        public DbSet<DHDRCommunicationLog> DHDRCommunicationLogs { get; set; }
        #endregion

        #region Appointment Priority
        public DbSet<AppointmentPriority> AppointmentPriorities { get; set; }

        public DbSet<PracticeDefaultAppointmentPriority> PracticeDefaultAppointmentPriorities { get; set; }
        #endregion

        /*     public override int SaveChanges()
             {
                 int saved = 0;
                 bool exception = false;
                 var vmaudits = new List<VMAudit>();
                 try
                 {
                     if (userId > 0)//|| (!string.IsNullOrWhiteSpace(IPAddress)))
                     {
                         vmaudits = GetAudit(this.ChangeTracker);
                     }
                     saved = base.SaveChanges();
                 }
                 catch (Exception ex)
                 {
                     exception = true;

                     throw;
                     //log.Error(string.Format("Audit SaveChanges(userId) failed. Item = {0}", changes.ToString()), ex);
                 }
                 finally
                 {

                     if (!exception)
                     {
                         //SaveAuditJson(vmaudits);
                         Task.Run(() => SaveAuditJson(vmaudits));
                     }
                     //TODO Add audit
                 }

                 return saved;
             } */
        public int SaveChanges(int? patientRecordId, int userId, string ipaddress)
        {
            this.userId = userId;
            this.IPAddress = ipaddress;
            int saved = 0;
            bool exception = false;

            var vmaudits = new List<VMAudit>();
            try
            {
                //var savedentries = this.ChangeTracker.Entries();
                if (this.userId > 0 && (!string.IsNullOrWhiteSpace(IPAddress)))
                {
                    vmaudits = GetAudit(this.ChangeTracker);
                }

                saved = base.SaveChanges();
            }
            catch (Exception ex)
            {
                exception = true;
                _log.Error($"SaveChanges:{ex.ToString()}");
            }
            finally
            {
                if (!exception)
                {
                    //SaveAuditJson(patientRecordId, vmaudits);
                    Task.Run(() => SaveAuditJson(patientRecordId, vmaudits));
                }
            }
            return saved;
        }


        /**
            * For Future user async method */
        public async Task<int> SaveChangesAsync(int userId, string ipaddress)
        {
            this.userId = userId;
            this.IPAddress = ipaddress;
            int saved = 0;
            bool exception = false;

            var vmaudits = new List<VMAudit>();
            try
            {
                //var savedentries = this.ChangeTracker.Entries();
                // for kiosk no user logged in, negative value of user id
                if (this.userId != 0 && (!string.IsNullOrWhiteSpace(IPAddress)))
                {
                    vmaudits = GetAudit(this.ChangeTracker);
                }

                saved = await base.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                exception = true;
                _log.Error($"SaveChangesAsync:{ex.ToString()}");
            }
            finally
            {
                if (!exception)
                {
                    await Task.Run(() => SaveAuditJsonAsync(vmaudits));

                }
            }
            return saved;
        }

        public int SaveChanges(int userId, string ipaddress)
        {
            this.userId = userId;
            this.IPAddress = ipaddress;
            int saved = 0;
            bool exception = false;

            var vmaudits = new List<VMAudit>();
            try
            {
                //var savedentries = this.ChangeTracker.Entries();
                // for kiosk no user logged in, negative value of user id
                if (this.userId != 0 && (!string.IsNullOrWhiteSpace(IPAddress)))
                {
                    vmaudits = GetAudit(this.ChangeTracker);
                }

                saved = base.SaveChanges();
            }
            catch (DbEntityValidationException ev)
            {
                var newException = new FormattedDbEntityValidationException(ev);
                throw newException;
            }
            catch (Exception ex)
            {
                exception = true;
                throw new Exception("SaveChanges Exception.", ex);
            }
            finally
            {
                if (!exception)
                {
                    // trying to enable async, see ticket 12468
                    if (CerebrumContext.IsSaveChangesUseAsync)
                    {
                        Task.Run(() => SaveAuditJsonStatic(vmaudits, userId, IPAddress));
                    }
                    else
                    {
                        SaveAuditJson(vmaudits);
                    }
                }
            }
            return saved;
        }

        // This static method is a "static" version copied from SaveAuditJson(List<VMAudit> vmaudits)
        private static void SaveAuditJsonStatic(List<VMAudit> vmaudits, int userId, string IPAddress)
        {
            using (CerebrumAuditContext auditContext = new CerebrumAuditContext())
            {
                try
                {
                    #region Save Audit
                    if (vmaudits != null && vmaudits.Count() > 0)
                    {
                        foreach (var vmaudit in vmaudits)
                        {
                            if (vmaudit.entry != null)
                            {
                                try
                                {
                                    var adt = new Audit.Audit();
                                    adt.TableName = ObjectContext.GetObjectType(vmaudit.entry.Entity.GetType()).Name;
                                    adt.TableName = adt.TableName.Replace("Cerebrum.Data.", "");
                                    var st = vmaudit.entry.State;
                                    adt.EventType = "A";

                                    adt.EventDateTime = DateTime.Now;
                                    adt.UserId = userId;
                                    adt.IpAddress = IPAddress;
                                    var keys = vmaudit.entry.Entity.GetType().GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);
                                    var tb = new TableModel();
                                    if (keys != null && keys.Count() > 0)
                                    {
                                        tb.primaryKey = keys.FirstOrDefault().Name;
                                    }

                                    var vals = new List<ValueModel>();
                                    foreach (var ad in vmaudit.entry.CurrentValues.PropertyNames)
                                    {
                                        var val = Convert.ToString(vmaudit.entry.CurrentValues[ad]);
                                        if (!string.IsNullOrWhiteSpace(val))
                                        {
                                            if (tb.primaryKey == ad)
                                            {
                                                tb.primaryValue = val;
                                            }
                                            if (ad.Trim().ToLower().Equals("patientrecordid"))
                                            {
                                                adt.PatientRecordId = int.Parse(val);
                                            }
                                            var av = new ValueModel
                                            {
                                                CN = ad,
                                                NV = Convert.ToString(val),
                                            };
                                            vals.Add(av);
                                        }
                                    }

                                    tb.TableName = ObjectContext.GetObjectType(vmaudit.entry.Entity.GetType()).Name;// vmaudit.entry.Entity.ToString();
                                    tb.changes = vals;
                                    var json = JsonConvert.SerializeObject(tb, Formatting.None);
                                    adt.Changes = json;

                                    // experiment to resolve deadlocking issue in ticket 12471 by lowing the isolation level from Serializable to ReadCommitted
                                    //using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                                    using (TransactionScope scope = TransactionUtils.CreateTransactionScopeSupress(new TimeSpan(0, 10, 0)))
                                    {
                                        auditContext.Audits.Add(adt);
                                        auditContext.SaveChanges();
                                        scope.Complete();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var exc = $"{ex}";
                                    //throw new Exception($"Audit Exception: {ex.Message}", ex.InnerException);
                                }
                            }
                            else if (vmaudit.Modified != null && vmaudit.Modified.Count() > 0)
                            {
                                //var savedentries = this.ChangeTracker.Entries();

                                // experiment to resolve deadlocking issue in ticket 12471 by lowing the isolation level from Serializable to ReadCommitted
                                //using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                                using (TransactionScope scope = TransactionUtils.CreateTransactionScopeSupress(new TimeSpan(0, 10, 0)))
                                {

                                    foreach (var m in vmaudit.Modified)
                                    {
                                        var vals = new List<ValueModel>();
                                        foreach (var v in m.AuditValues)
                                        {
                                            var av = new ValueModel
                                            {
                                                // TableName=m.TableName,
                                                CN = v.ColumnName,
                                                OV = v.OriginalValue,
                                                NV = v.NewValue
                                            };
                                            vals.Add(av);
                                        }
                                        if (vals.Count() > 0)
                                        {
                                            var tb = new TableModel();
                                            tb.TableName = m.TableName;
                                            tb.primaryKey = m.primaryKey;
                                            tb.primaryValue = m.primaryValue;
                                            tb.changes = vals;
                                            var json = JsonConvert.SerializeObject(tb, Formatting.None);
                                            m.Changes = json;
                                            var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };

                                            auditContext.Audits.Add(au);
                                        }
                                    }
                                    //auditContext.SaveChanges();
                                    auditContext.SaveChanges();
                                    scope.Complete();
                                }

                                //using (var dbt = auditContext.Database.UseTransaction(this.Database.CurrentTransaction))
                                {
                                    //    Task.Run(() => auditContext.SaveChanges());
                                }
                            }
                            else if (vmaudit.Deleted != null && vmaudit.Deleted.Count() > 0)
                            {
                                // experiment to resolve deadlocking issue in ticket 12471 by lowing the isolation level from Serializable to ReadCommitted
                                //using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                                using (TransactionScope scope = TransactionUtils.CreateTransactionScopeSupress(new TimeSpan(0, 10, 0)))
                                {
                                    foreach (var m in vmaudit.Deleted)
                                    {
                                        var vals = new List<ValueModel>();
                                        foreach (var v in m.AuditValues)
                                        {
                                            var av = new ValueModel
                                            {
                                                //TableName=m.TableName,
                                                CN = v.ColumnName,
                                                OV = v.OriginalValue,
                                                NV = v.NewValue
                                            };
                                            vals.Add(av);
                                        }
                                        if (vals.Count() > 0)
                                        {
                                            var tb = new TableModel();
                                            tb.TableName = m.TableName;
                                            tb.primaryKey = m.primaryKey;
                                            tb.primaryValue = m.primaryValue;
                                            tb.TableName = tb.TableName;//.Replace("Cerebrum.Data.", "");
                                            tb.changes = vals;

                                            var json = JsonConvert.SerializeObject(tb, Formatting.None);

                                            m.Changes = json;

                                            var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };
                                            auditContext.Audits.Add(au);
                                        }

                                    }
                                    //auditContext.SaveChanges();

                                    auditContext.SaveChanges();
                                    scope.Complete();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    try
                    {
                        var log = new Audit.Log { Level = "ERROR", userName = userId.ToString(), ipaddress = IPAddress, page = "CerebrumContext.SaveAuditJsonStatic", Date = DateTime.Now, Logger = "CerebrumContext.SaveAuditJsonStatic", Message = ex.Message };
                        auditContext.Logs.Add(log);
                        if (ex.InnerException != null)
                        {
                            var innerlog = new Audit.Log { Level = "ERROR", userName = userId.ToString(), ipaddress = IPAddress, page = "CerebrumContext.SaveAuditJsonStatic", Date = DateTime.Now, Logger = "CerebrumContext.SaveAuditJsonStatic (inner)", Message = ex.InnerException.Message };
                            auditContext.Logs.Add(innerlog);
                        }
                        auditContext.SaveChanges();
                    }
                    catch { }
                }
                #endregion
            }
        }

        public int SaveChanges(string userName)
        {
            int saved = 0;
            bool exception = false;
            var vmaudits = new List<VMAudit>();
            try
            {
                if (this.userId > 0)
                    vmaudits = GetAudit(this.ChangeTracker);

                saved = base.SaveChanges();
            }
            catch (DbEntityValidationException ev)
            {
                exception = true;
                var validationException = new FormattedDbEntityValidationException(ev);
                _log.Error($"SaveChanges(userName) ValidationException:{validationException.Message}");
            }
            catch (Exception ex)
            {
                exception = true;
                _log.Error($"SaveChanges(userName) Exception:{ex}");
            }
            finally
            {
                if (!exception)
                {
                    Task.Run(() => SaveAuditJson(vmaudits));
                }
            }
            return saved;
        }
        private void SaveAuditJson(int? patientRecordId, List<VMAudit> vmaudits)
        {
            #region Save Audit
            if (vmaudits != null && vmaudits.Count() > 0)
            {
                foreach (var vmaudit in vmaudits)
                {
                    if (vmaudit.entry != null)
                    {
                        try
                        {
                            var adt = new Audit.Audit();
                            adt.TableName = vmaudit.entry.Entity.ToString();
                            adt.TableName = adt.TableName.Replace("Cerebrum.Data.", "");
                            var st = vmaudit.entry.State;
                            adt.EventType = "A";

                            adt.EventDateTime = DateTime.Now;
                            adt.UserId = userId;
                            adt.IpAddress = IPAddress;
                            var keys = vmaudit.entry.Entity.GetType().GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);
                            var tb = new TableModel();
                            if (keys != null && keys.Count() > 0)
                            {
                                tb.primaryKey = keys.FirstOrDefault().Name;
                            }

                            var vals = new List<ValueModel>();
                            foreach (var ad in vmaudit.entry.CurrentValues.PropertyNames)
                            {
                                var val = Convert.ToString(vmaudit.entry.CurrentValues[ad]);
                                if (!string.IsNullOrWhiteSpace(val))
                                {
                                    if (tb.primaryKey == ad)
                                    {
                                        tb.primaryValue = val;
                                    }
                                    if (ad.Trim().ToLower().Equals("patientrecordid"))
                                    {
                                        adt.PatientRecordId = int.Parse(val);
                                    }
                                    var av = new ValueModel
                                    {
                                        CN = ad,
                                        NV = Convert.ToString(val),
                                    };
                                    vals.Add(av);
                                }
                            }

                            tb.TableName = adt.TableName;// vmaudit.entry.Entity.ToString();
                            tb.changes = vals;
                            var json = JsonConvert.SerializeObject(tb, Formatting.None);
                            adt.Changes = json;
                            if (patientRecordId > 0 && adt.PatientRecordId < 1)
                                adt.PatientRecordId = (int)patientRecordId;
                            auditContext.Value.Audits.Add(adt);
                            auditContext.Value.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            var exc = $"{ex}";
                        }
                    }
                    else if (vmaudit.Modified != null && vmaudit.Modified.Count() > 0)
                    {
                        //var savedentries = this.ChangeTracker.Entries();

                        foreach (var m in vmaudit.Modified)
                        {
                            var vals = new List<ValueModel>();
                            foreach (var v in m.AuditValues)
                            {
                                var av = new ValueModel
                                {
                                    // TableName=m.TableName,
                                    CN = v.ColumnName,
                                    OV = v.OriginalValue,
                                    NV = v.NewValue
                                };
                                vals.Add(av);
                            }
                            if (vals.Count() > 0)
                            {
                                var tb = new TableModel();
                                tb.TableName = m.TableName;
                                tb.primaryKey = m.primaryKey;
                                tb.primaryValue = m.primaryValue;
                                tb.changes = vals;
                                var json = JsonConvert.SerializeObject(tb, Formatting.None);
                                m.Changes = json;
                                var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };
                                if (patientRecordId > 0 && au.PatientRecordId < 1)
                                    au.PatientRecordId = (int)patientRecordId;
                                auditContext.Value.Audits.Add(au);
                            }
                        }
                        auditContext.Value.SaveChanges();
                        //using (var dbt = auditContext.Database.UseTransaction(this.Database.CurrentTransaction))
                        {
                            //    Task.Run(() => auditContext.SaveChanges());
                        }
                    }
                    else if (vmaudit.Deleted != null && vmaudit.Deleted.Count() > 0)
                    {
                        foreach (var m in vmaudit.Deleted)
                        {
                            var vals = new List<ValueModel>();
                            foreach (var v in m.AuditValues)
                            {
                                var av = new ValueModel
                                {
                                    //TableName=m.TableName,
                                    CN = v.ColumnName,
                                    OV = v.OriginalValue,
                                    NV = v.NewValue
                                };
                                vals.Add(av);
                            }
                            if (vals.Count() > 0)
                            {
                                var tb = new TableModel();
                                tb.TableName = m.TableName;
                                tb.primaryKey = m.primaryKey;
                                tb.primaryValue = m.primaryValue;
                                tb.TableName = tb.TableName.Replace("Cerebrum.Data.", "");
                                tb.changes = vals;

                                var json = JsonConvert.SerializeObject(tb, Formatting.None);

                                m.Changes = json;

                                var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };
                                if (patientRecordId > 0 && au.PatientRecordId < 1)
                                    au.PatientRecordId = (int)patientRecordId;
                                auditContext.Value.Audits.Add(au);
                            }

                        }
                        auditContext.Value.SaveChanges();
                        //Task.Run(() => auditContext.SaveChanges());
                    }
                }
            }
            #endregion
        }
        private void SaveAuditJson(List<VMAudit> vmaudits)
        {
            #region Save Audit
            if (vmaudits != null && vmaudits.Count() > 0)
            {
                foreach (var vmaudit in vmaudits)
                {
                    if (vmaudit.entry != null)
                    {
                        try
                        {
                            var adt = new Audit.Audit();
                            adt.TableName = ObjectContext.GetObjectType(vmaudit.entry.Entity.GetType()).Name;
                            adt.TableName = adt.TableName.Replace("Cerebrum.Data.", "");
                            var st = vmaudit.entry.State;
                            adt.EventType = "A";

                            adt.EventDateTime = DateTime.Now;
                            adt.UserId = userId;
                            adt.IpAddress = IPAddress;
                            var keys = vmaudit.entry.Entity.GetType().GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);
                            var tb = new TableModel();
                            if (keys != null && keys.Count() > 0)
                            {
                                tb.primaryKey = keys.FirstOrDefault().Name;
                            }

                            var vals = new List<ValueModel>();
                            foreach (var ad in vmaudit.entry.CurrentValues.PropertyNames)
                            {
                                var val = Convert.ToString(vmaudit.entry.CurrentValues[ad]);
                                if (!string.IsNullOrWhiteSpace(val))
                                {
                                    if (tb.primaryKey == ad)
                                    {
                                        tb.primaryValue = val;
                                    }
                                    if (ad.Trim().ToLower().Equals("patientrecordid"))
                                    {
                                        adt.PatientRecordId = int.Parse(val);
                                    }
                                    var av = new ValueModel
                                    {
                                        CN = ad,
                                        NV = Convert.ToString(val),
                                    };
                                    vals.Add(av);
                                }
                            }

                            tb.TableName = ObjectContext.GetObjectType(vmaudit.entry.Entity.GetType()).Name;// vmaudit.entry.Entity.ToString();
                            tb.changes = vals;
                            var json = JsonConvert.SerializeObject(tb, Formatting.None);
                            adt.Changes = json;

                            // experiment to resolve deadlocking issue in ticket 12471 by lowing the isolation level from Serializable to ReadCommitted
                            //using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                            using (TransactionScope scope = TransactionUtils.CreateTransactionScopeSupress(new TimeSpan(0, 10, 0)))
                            {
                                auditContext.Value.Audits.Add(adt);
                                auditContext.Value.SaveChanges();
                                scope.Complete();
                            }
                        }
                        catch (Exception ex)
                        {
                            var exc = $"{ex}";
                            //throw new Exception($"Audit Exception: {ex.Message}", ex.InnerException);
                        }
                    }
                    else if (vmaudit.Modified != null && vmaudit.Modified.Count() > 0)
                    {
                        //var savedentries = this.ChangeTracker.Entries();

                        // experiment to resolve deadlocking issue in ticket 12471 by lowing the isolation level from Serializable to ReadCommitted
                        //using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                        using (TransactionScope scope = TransactionUtils.CreateTransactionScopeSupress(new TimeSpan(0, 10, 0)))
                        {

                            foreach (var m in vmaudit.Modified)
                            {
                                var vals = new List<ValueModel>();
                                foreach (var v in m.AuditValues)
                                {
                                    var av = new ValueModel
                                    {
                                        // TableName=m.TableName,
                                        CN = v.ColumnName,
                                        OV = v.OriginalValue,
                                        NV = v.NewValue
                                    };
                                    vals.Add(av);
                                }
                                if (vals.Count() > 0)
                                {
                                    var tb = new TableModel();
                                    tb.TableName = m.TableName;
                                    tb.primaryKey = m.primaryKey;
                                    tb.primaryValue = m.primaryValue;
                                    tb.changes = vals;
                                    var json = JsonConvert.SerializeObject(tb, Formatting.None);
                                    m.Changes = json;
                                    var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };

                                    auditContext.Value.Audits.Add(au);
                                }
                            }
                            //auditContext.SaveChanges();
                            auditContext.Value.SaveChanges();
                            scope.Complete();
                        }

                        //using (var dbt = auditContext.Database.UseTransaction(this.Database.CurrentTransaction))
                        {
                            //    Task.Run(() => auditContext.SaveChanges());
                        }
                    }
                    else if (vmaudit.Deleted != null && vmaudit.Deleted.Count() > 0)
                    {
                        // experiment to resolve deadlocking issue in ticket 12471 by lowing the isolation level from Serializable to ReadCommitted
                        //using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                        using (TransactionScope scope = TransactionUtils.CreateTransactionScopeSupress(new TimeSpan(0, 10, 0)))
                        {
                            foreach (var m in vmaudit.Deleted)
                            {
                                var vals = new List<ValueModel>();
                                foreach (var v in m.AuditValues)
                                {
                                    var av = new ValueModel
                                    {
                                        //TableName=m.TableName,
                                        CN = v.ColumnName,
                                        OV = v.OriginalValue,
                                        NV = v.NewValue
                                    };
                                    vals.Add(av);
                                }
                                if (vals.Count() > 0)
                                {
                                    var tb = new TableModel();
                                    tb.TableName = m.TableName;
                                    tb.primaryKey = m.primaryKey;
                                    tb.primaryValue = m.primaryValue;
                                    tb.TableName = tb.TableName;//.Replace("Cerebrum.Data.", "");
                                    tb.changes = vals;

                                    var json = JsonConvert.SerializeObject(tb, Formatting.None);

                                    m.Changes = json;

                                    var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };
                                    auditContext.Value.Audits.Add(au);
                                }

                            }
                            //auditContext.SaveChanges();

                            auditContext.Value.SaveChanges();
                            scope.Complete();
                        }
                    }
                }
            }
            #endregion
        }

        private async void SaveAuditJsonAsync(List<VMAudit> vmaudits)
        {
            #region Save Audit
            try
            {
                if (vmaudits != null && vmaudits.Count() > 0)
                {
                    // experiment to resolve deadlocking issue in ticket 12471 by lowing the isolation level from Serializable to ReadCommitted
                    //using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress, TransactionScopeAsyncFlowOption.Enabled))
                    using (TransactionScope scope = TransactionUtils.CreateTransactionScopeSupressAsync())
                    {
                        foreach (var vmaudit in vmaudits)
                        {

                            if (vmaudit.entry != null)
                            {
                                try
                                {
                                    var adt = new Audit.Audit();

                                    var tablename = vmaudit.entry.Entity.ToString().Split('.').LastOrDefault();
                                    adt.TableName = tablename;
                                    var st = vmaudit.entry.State;
                                    adt.EventType = "A";

                                    adt.EventDateTime = DateTime.Now;
                                    adt.UserId = userId;
                                    adt.IpAddress = IPAddress;
                                    var keys = vmaudit.entry.Entity.GetType().GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);
                                    var tb = new TableModel();
                                    if (keys != null && keys.Count() > 0)
                                    {
                                        tb.primaryKey = keys.FirstOrDefault().Name;
                                    }

                                    var vals = new List<ValueModel>();
                                    foreach (var ad in vmaudit.entry.CurrentValues.PropertyNames)
                                    {
                                        var val = Convert.ToString(vmaudit.entry.CurrentValues[ad]);
                                        if (!string.IsNullOrWhiteSpace(val))
                                        {
                                            if (tb.primaryKey == ad)
                                            {
                                                tb.primaryValue = val;
                                            }
                                            if (ad.Trim().ToLower().Equals("patientrecordid"))
                                            {
                                                adt.PatientRecordId = int.Parse(val);
                                            }
                                            var av = new ValueModel
                                            {
                                                CN = ad,
                                                NV = Convert.ToString(val),
                                            };
                                            vals.Add(av);
                                        }
                                    }

                                    tb.TableName = adt.TableName;
                                    tb.changes = vals;
                                    var json = JsonConvert.SerializeObject(tb, Formatting.None);
                                    adt.Changes = json;
                                    auditContext.Value.Audits.Add(adt);
                                    // await auditContext.SaveChangesAsync();
                                }
                                catch (Exception ex)
                                {
                                    _log.Error($"SaveAuditJsonAsync:{ex.ToString()}");
                                }
                            }
                            else if (vmaudit.Modified != null && vmaudit.Modified.Count() > 0)
                            {

                                foreach (var m in vmaudit.Modified)
                                {
                                    var vals = new List<ValueModel>();
                                    foreach (var v in m.AuditValues)
                                    {
                                        var av = new ValueModel
                                        {
                                            CN = v.ColumnName,
                                            OV = v.OriginalValue,
                                            NV = v.NewValue
                                        };
                                        vals.Add(av);
                                    }
                                    if (vals.Count() > 0)
                                    {
                                        var tb = new TableModel();
                                        var tablename = m.TableName.Split('.').LastOrDefault();
                                        tb.TableName = tablename;
                                        tb.primaryKey = m.primaryKey;
                                        tb.primaryValue = m.primaryValue;
                                        tb.changes = vals;
                                        var json = JsonConvert.SerializeObject(tb, Formatting.None);
                                        m.Changes = json;
                                        var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };

                                        auditContext.Value.Audits.Add(au);
                                    }
                                }
                                //await auditContext.SaveChangesAsync();

                            }
                            else if (vmaudit.Deleted != null && vmaudit.Deleted.Count() > 0)
                            {
                                foreach (var m in vmaudit.Deleted)
                                {
                                    var vals = new List<ValueModel>();
                                    foreach (var v in m.AuditValues)
                                    {
                                        var av = new ValueModel
                                        {
                                            CN = v.ColumnName,
                                            OV = v.OriginalValue,
                                            NV = v.NewValue
                                        };
                                        vals.Add(av);
                                    }
                                    if (vals.Count() > 0)
                                    {
                                        var tb = new TableModel();

                                        var tablename = m.TableName.Split('.').LastOrDefault();
                                        tb.TableName = tablename;

                                        tb.primaryKey = m.primaryKey;
                                        tb.primaryValue = m.primaryValue;
                                        tb.TableName = tb.TableName;
                                        tb.changes = vals;

                                        var json = JsonConvert.SerializeObject(tb, Formatting.None);

                                        m.Changes = json;

                                        var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };
                                        auditContext.Value.Audits.Add(au);
                                    }

                                }

                            }

                        }
                        await auditContext.Value.SaveChangesAsync();
                        scope.Complete();
                        scope.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error($"Json Audit Async:{ex.ToString()}");
            }
            #endregion
        }

        private List<VMAudit> GetAudit(DbChangeTracker t)
        {
            var audits = new List<VMAudit>();
            if (t.HasChanges())
            {
                //var count = t.Entries().Count(a => a.State == EntityState.Added);
                var nechngs = t.Entries().Where(e => e.State == EntityState.Added || e.State == EntityState.Modified || e.State == EntityState.Deleted);
                foreach (var ent in nechngs)
                {
                    var entityType = ent.Entity.GetType();
                    TrackChangesAttribute trackChangesAttribute = entityType.GetCustomAttributes(true).OfType<TrackChangesAttribute>().FirstOrDefault();
                    if (trackChangesAttribute != null)
                    {
                        try
                        {
                            var audit = new VMAudit();
                            var tableName = ObjectContext.GetObjectType(ent.Entity.GetType()).Name;
                            var keys = ent.Entity.GetType().GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);

                            var lst = new List<AuditValue>();
                            if (ent.State == EntityState.Added)
                            {
                                var changes = ent.CurrentValues.PropertyNames.ToDictionary(pn => pn, pn => ent.CurrentValues[pn]);
                                audit.entry = ent;
                            }
                            else if (ent.State == EntityState.Modified)
                            {
                                var adt = new Audit.AuditVM();
                                adt.EventType = ent.State.ToString().Substring(0, 1);
                                adt.EventDateTime = DateTime.Now;

                                var tablename = ObjectContext.GetObjectType(ent.Entity.GetType()).ToString().Split('.').LastOrDefault();
                                adt.TableName = tablename;

                                adt.UserId = this.userId;
                                adt.IpAddress = this.IPAddress;
                                adt.URL = this.RequestURL;


                                if (keys != null && keys.Count() > 0)
                                {
                                    adt.primaryKey = keys.FirstOrDefault().Name;
                                }

                                var databaseValues = ent.GetDatabaseValues();
                                var changes = ent.CurrentValues.PropertyNames.Select(pn => 
                                    new AuditValue 
                                    { 
                                        ColumnName = pn, 
                                        OriginalValue = databaseValues[pn] != null ? Convert.ToString(databaseValues[pn]) : null,
                                        NewValue = Convert.ToString(ent.CurrentValues[pn]) 
                                    });

                                foreach (var item in changes)
                                {
                                    if (adt.primaryKey == item.ColumnName)
                                    {
                                        adt.primaryValue = item.NewValue;
                                    }
                                    if (adt.PatientRecordId < 1 && item.ColumnName.Trim().ToLower().Equals("patientrecordid"))
                                    {
                                        int patRecid = 0;
                                        bool newv = int.TryParse(item.NewValue, out patRecid);

                                        if (!newv)
                                        {
                                            int.TryParse(item.OriginalValue, out patRecid);
                                        }

                                        adt.PatientRecordId = patRecid;
                                    }
                                    var originalHasValue = !string.IsNullOrWhiteSpace(item.OriginalValue);
                                    var newHasValue = !string.IsNullOrWhiteSpace(item.NewValue);
                                    if (originalHasValue || newHasValue)
                                    {
                                        if (originalHasValue && newHasValue)
                                        {
                                            if (!item.OriginalValue.Equals(item.NewValue))
                                                adt.AuditValues.Add(item);
                                        }
                                        else
                                            adt.AuditValues.Add(item);
                                    }
                                }
                                audit.Modified.Add(adt);
                            }
                            else if (ent.State == EntityState.Deleted)
                            {
                                var adt = new Audit.AuditVM();
                                adt.EventType = ent.State.ToString().Substring(0, 1);
                                adt.EventDateTime = DateTime.Now;
                                adt.TableName = ObjectContext.GetObjectType(ent.Entity.GetType()).ToString();

                                var tablename = ObjectContext.GetObjectType(ent.Entity.GetType()).ToString().Split('.').LastOrDefault();
                                adt.TableName = tablename;

                                keys = (keys != null && keys.Count() > 0) ? keys : Type.GetType(adt.TableName).GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);

                                adt.UserId = this.userId;
                                adt.IpAddress = this.IPAddress;
                                adt.URL = this.RequestURL;

                                if (keys != null && keys.Count() > 0)
                                {
                                    adt.primaryKey = keys.FirstOrDefault().Name;
                                }

                                var changes = ent.OriginalValues.PropertyNames.Select(pn => new AuditValue { ColumnName = pn, OriginalValue = ent.OriginalValues[pn] != null ? Convert.ToString(ent.OriginalValues[pn]) : null, NewValue = null });

                                foreach (var item in changes)
                                {
                                    if (adt.primaryKey == item.ColumnName)
                                    {
                                        adt.primaryValue = item.NewValue;
                                    }
                                    if (item.NewValue != null && item.ColumnName.Trim().ToLower().Equals("patientrecordid"))
                                    {
                                        adt.PatientRecordId = int.Parse(item.NewValue);
                                    }
                                    adt.AuditValues.Add(item);
                                }
                                audit.Deleted.Add(adt);
                            }
                            audits.Add(audit);
                        }
                        catch (Exception ex)
                        {
                            _log.Error($"Get Audit Error:{ex.ToString()}");
                        }
                    }
                }
                return audits;
            }
            return null;
        }
        public T GetSingle<T>(string storedProcedure, List<SqlParameter> parameters = null) where T : class
        {
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                //cmd.CommandTimeout = sqlCommandTimeOut;
                try
                {
                    if (parameters != null)
                    {
                        cmd.Parameters.Clear();
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    if (cmd.Connection.State != System.Data.ConnectionState.Open)
                        cmd.Connection.Open();
                    return ((IObjectContextAdapter)this).ObjectContext.Translate<T>(cmd.ExecuteReader()).FirstOrDefault();
                }
                catch (Exception x)
                {
                    _log.Error($"Get Single Store Procedure:{x.ToString()}");
                    throw x;
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            //return null;
        }

        /// <summary>
        /// For ticket https://redmine.chrc.net:444/redmine/issues/12517
        /// <br/>
        /// <br/>This method is a copy of <see cref="CerebrumContext.GetData{T}(string, List{SqlParameter}, int)"/>, but the SqlConnection is created differently.
        /// <br/>The purpose is to resolve an production exception where it was throwing "... connection already open ... " when server is very busy.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="parameters"></param>
        /// <param name="sqlCommandTimeOut"></param>
        /// <returns></returns>
        public IEnumerable<T> GetData2<T>(string storedProcedure, List<SqlParameter> parameters = null, int sqlCommandTimeOut = 0) where T : class
        {
            List<T> list = new List<T>();
            using (SqlConnection connection = new SqlConnection(Database.Connection.ConnectionString))
            {
                using (System.Data.Common.DbCommand cmd = connection.CreateCommand())
                {
                    cmd.CommandText = storedProcedure;
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    //cmd.CommandTimeout = sqlCommandTimeOut;
                    if (sqlCommandTimeOut > 0)
                        cmd.CommandTimeout = sqlCommandTimeOut;

                    try
                    {
                        if (parameters != null)
                        {
                            cmd.Parameters.Clear();
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.Add(param);
                            }
                        }
                        if (cmd.Connection.State == System.Data.ConnectionState.Closed || cmd.Connection.State == System.Data.ConnectionState.Broken)
                            cmd.Connection.Open();
                        list = ((IObjectContextAdapter)this).ObjectContext.Translate<T>(cmd.ExecuteReader()).ToList();
                    }
                    catch (Exception x)
                    {
                        string parameter = string.Join(",", parameters.Select(s => Convert.ToString(s.Value)));
                        string excStr = $"storedProcedure:{storedProcedure} Parameters:{parameter} {x.ToString()}";

                        throw new Exception(excStr);
                    }
                    finally
                    {
                        if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                            cmd.Connection.Close();
                    }

                }
            }
            return list;
        }


        public IEnumerable<T> GetData<T>(string storedProcedure, List<SqlParameter> parameters = null, int sqlCommandTimeOut = 0) where T : class
        {
            List<T> list = new List<T>();
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                //cmd.CommandTimeout = sqlCommandTimeOut;
                if (sqlCommandTimeOut > 0)
                    cmd.CommandTimeout = sqlCommandTimeOut;

                try
                {
                    if (parameters != null)
                    {
                        cmd.Parameters.Clear();
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    if (cmd.Connection.State != System.Data.ConnectionState.Open)
                        cmd.Connection.Open();
                    var reader = cmd.ExecuteReader();
                    list = ((IObjectContextAdapter)this).ObjectContext.Translate<T>(reader).ToList();
                }
                catch (Exception x)
                {
                    string parameter = string.Join(",", parameters.Select(s => Convert.ToString(s.Value)));
                    string excStr = $"storedProcedure:{storedProcedure} Parameters:{parameter} {x.ToString()}";

                    throw new Exception(excStr);
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            return list;
        }

        public async Task<IEnumerable<T>> GetDataAsync<T>(string storedProcedure, List<SqlParameter> parameters = null, int sqlCommandTimeOut = 0) where T : class
        {
            List<T> list = new List<T>();
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                //cmd.CommandTimeout = sqlCommandTimeOut;
                if (sqlCommandTimeOut > 0)
                    cmd.CommandTimeout = sqlCommandTimeOut;

                try
                {
                    if (parameters != null)
                    {
                        cmd.Parameters.Clear();
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    if (cmd.Connection.State != System.Data.ConnectionState.Open)
                        cmd.Connection.Open();
                    var reader = await cmd.ExecuteReaderAsync();
                    list = ((IObjectContextAdapter)this).ObjectContext.Translate<T>(reader).ToList();
                }
                catch (Exception x)
                {
                    string parameter = string.Join(",", parameters.Select(s => Convert.ToString(s.Value)));
                    string excStr = $"async storedProcedure:{storedProcedure} Parameters:{parameter} {x.ToString()}";

                    throw new Exception(excStr);
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            return list;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="resultSets"></param>
        /// <param name="storedProcedure"></param>
        /// <param name="parameters"></param>
        /// <param name="sqlCommandTimeOut"></param>
        /// <remarks>Keep in mind that this is not necessary faster if you only have 1 to 3 SELECT statements to return.</remarks>
        /// <returns></returns>
        public List<IEnumerable> GetDataMultipleResultSets(List<Func<IObjectContextAdapter, DbDataReader, IEnumerable>> resultSets, string storedProcedure, List<SqlParameter> parameters = null, int sqlCommandTimeOut = 0)
        {
            var results = new List<IEnumerable>();
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                if (sqlCommandTimeOut > 0)
                    cmd.CommandTimeout = sqlCommandTimeOut;

                try
                {
                    if (parameters != null)
                    {
                        cmd.Parameters.Clear();
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    if (cmd.Connection.State != System.Data.ConnectionState.Open)
                        cmd.Connection.Open();

                    using (DbDataReader reader = cmd.ExecuteReader())
                    {
                        foreach (var resultSet in resultSets)
                        {
                            results.Add(resultSet(this, reader));
                            reader.NextResult();
                        }
                        reader.Close();
                    }
                }
                catch (Exception x)
                {
                    string parameter = string.Join(",", parameters.Select(s => Convert.ToString(s.Value)));
                    string excStr = $"storedProcedure:{storedProcedure} Parameters:{parameter} {x.ToString()}";

                    throw new Exception(excStr);
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            return results;
        }
        public static T GetFromResultSetOrDefault<T>(IEnumerable resultset)
        {
            if (resultset != null)
            {
                List<T> input = resultset as List<T>;
                if (input.Count > 0)
                {
                    return input[0];
                }
            }

            return default(T);
        }

        public bool ExecuteNonQueryFindExists(string storeProcedure, List<SqlParameter> parameters = null)
        {
            bool anyExists = false;
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = storeProcedure;

                if (parameters != null)
                {
                    cmd.Parameters.Clear();
                    foreach (var param in parameters)
                    {
                        cmd.Parameters.Add(param);
                    }
                }

                // open connection and execute stored procedure
                if (cmd.Connection.State != System.Data.ConnectionState.Open)
                    cmd.Connection.Open();
                cmd.ExecuteNonQuery();

                // read output value from @anyExists
                anyExists = Convert.ToBoolean(cmd.Parameters["@anyExists"].Value);
                cmd.Connection.Close();
            }
            return anyExists;
        }
        public void ExecuteSqlStoredProcedure(string storedProcedure, List<SqlParameter> parameters = null, int sqlCommandTimeOut = 0)
        {
            bool isNewConnection = false;
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;

                if (sqlCommandTimeOut > 0)
                    cmd.CommandTimeout = sqlCommandTimeOut;

                try
                {
                    if (parameters != null)
                    {
                        cmd.Parameters.Clear();
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Open)
                    {
                        cmd.Connection.Open();
                        isNewConnection = true;
                    }
                    else
                    {
                        if (Database.CurrentTransaction != null && Database.CurrentTransaction.UnderlyingTransaction != null)
                            cmd.Transaction = (SqlTransaction)Database.CurrentTransaction.UnderlyingTransaction;
                    }

                    cmd.ExecuteNonQuery();
                }
                catch (Exception x)
                {
                    string parameter = string.Join(",", parameters.Select(s => Convert.ToString(s.Value)));
                    string excStr = $"storedProcedure:{storedProcedure} Parameters:{parameter} {x.ToString()}";

                    throw new Exception(excStr);
                }
                finally
                {
                    if (isNewConnection && cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
        }

        public DbDataReader GetDataReader(string storedProcedure, List<SqlParameter> parameters = null, int sqlCommandTimeOut = 0)
        {
            DbDataReader dataReader = null;
            using (DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                if (sqlCommandTimeOut > 0)
                    cmd.CommandTimeout = sqlCommandTimeOut;

                try
                {
                    if (parameters != null)
                    {
                        cmd.Parameters.Clear();
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }

                    if (cmd.Connection.State != System.Data.ConnectionState.Open)
                        cmd.Connection.Open();

                    dataReader = cmd.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                }
                catch (Exception x)
                {
                    dataReader = null;

                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();

                    throw new Exception(x.ToString());
                }
            }

            return dataReader;
        }

        public void MyExe()
        {
            var r = ((IObjectContextAdapter)this).ObjectContext.ExecuteStoreQueryAsync<bool>("", new ObjectParameter("", 1));
        }
    }

}
