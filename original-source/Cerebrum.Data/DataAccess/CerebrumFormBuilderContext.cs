﻿using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;

using Cerebrum.Data.Radiology;

namespace Cerebrum.Data
{
    public class CerebrumFormBuilderContext : DbContext
    {
        private readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public CerebrumFormBuilderContext() : base("name=C3FormBuilderContext")
        {
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();
            base.OnModelCreating(modelBuilder);
        }

        public DbSet<PdfValueIndex> pdfValueIndexes { get; set; }
    }
}
