﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Infrastructure.Interception;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    /// <summary>
    /// If need in future for DbIntercept 
    /// 
    /// </summary>

    public class MyDbConfiguration : DbConfiguration
    {
        public MyDbConfiguration()
        {
            //DbInterception.Add(new QueryLog());
          //  SetDatabaseLogFormatter((context, writeAction) => new OneLineFormatter(context, writeAction));
        }
    }
}
