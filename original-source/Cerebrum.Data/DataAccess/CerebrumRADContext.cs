﻿using Cerebrum.Data.Radiology;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.Entity.Migrations;
using System.Data.SqlClient;
using System.Data.Entity.Infrastructure;
using Cerebrum.Data.Audit;
using Cerebrum.Data.Attributes;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Validation;
using Newtonsoft.Json;
using System.Transactions;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    public class CerebrumRADContext : DbContext
    {
        private readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public CerebrumRADContext() : base("name=C3RadContext")
        {
            //Database.SetInitializer<CerebrumRADContext>(null);
            Database.SetInitializer<CerebrumRADContext>(null);
            auditContext = auditContext ?? new CerebrumAuditContext();
            base.Configuration.ProxyCreationEnabled = true;
        }

        public static CerebrumRADContext Create()
        {
            return new CerebrumRADContext();
        }
        public DbSet<RAD_Image> RAD_Images { get; set; }
        public DbSet<RAD_Patient> RAD_Patients { get; set; }
        public DbSet<RAD_Institution> RAD_Institutions { get; set; }
        public DbSet<RAD_Series> RAD_Series { get; set; }
        public DbSet<RAD_Study> RAD_Studies { get; set; }
        public DbSet<RAD_RefPhysician> RAD_RefPhysicians { get; set; }

        private CerebrumAuditContext auditContext;
        public int SaveChanges(int userId, string ipaddress)
        {
            int saved = 0;
            bool exception = false;

            var vmaudits = new List<VMAudit>();
            try
            {
                if (userId != 0 && (!string.IsNullOrWhiteSpace(ipaddress)))
                {
                    vmaudits = GetAudit(this.ChangeTracker,userId,ipaddress);
                }

                saved = base.SaveChanges();
            }
            catch (DbEntityValidationException ev)
            {
                var newException = new FormattedDbEntityValidationException(ev);
                throw newException;
            }
            catch (Exception ex)
            {
                exception = true;
                throw new Exception("SaveChanges Exception.", ex);
            }
            finally
            {
                if (!exception)
                {
                    SaveAuditJson(vmaudits,userId,ipaddress);
                }
            }
            return saved;
        }


        public T GetSingle<T>(string storedProcedure, List<SqlParameter> parameters = null) where T : class
        {
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                //cmd.CommandTimeout = sqlCommandTimeOut;
                try
                {
                    if (parameters != null)
                    {
                        cmd.Parameters.Clear();
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    cmd.Connection.Open();
                    return ((IObjectContextAdapter)this).ObjectContext.Translate<T>(cmd.ExecuteReader()).FirstOrDefault();
                }
                catch (Exception x)
                {
                    string parameter = string.Join(",", parameters.Select(s => Convert.ToString(s.Value)));
                    string excStr = $"storedProcedure:{storedProcedure} Parameters:{parameter} {x.ToString()}";

                    throw new Exception(excStr);
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            
        }

        public IEnumerable<T> GetData<T>(string storedProcedure, List<SqlParameter> parameters = null) where T : class
        {
            List<T> list = new List<T>();
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                //cmd.CommandTimeout = sqlCommandTimeOut;
                try
                {
                    if (parameters != null)
                    {
                        cmd.Parameters.Clear();
                        foreach (var param in parameters)
                        {
                            if(param.Value==null)
                            {
                                param.Value = DBNull.Value;
                            }
                            cmd.Parameters.Add(param);
                        }
                    }
                    cmd.Connection.Open();
                    list = ((IObjectContextAdapter)this).ObjectContext.Translate<T>(cmd.ExecuteReader()).ToList();
                }catch(Exception x)
                {
                    string parameter = string.Join(",", parameters.Select(s => Convert.ToString(s.Value)));
                    string excStr = $"storedProcedure:{storedProcedure} Parameters:{parameter} {x.ToString()}";

                    throw new Exception(excStr);
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            return list;
        }

        private List<VMAudit> GetAudit(DbChangeTracker t,int userId,string ipAddress)
        {
            var audits = new List<VMAudit>();
            if (t.HasChanges())
            {
                //var count = t.Entries().Count(a => a.State == EntityState.Added);
                var nechngs = t.Entries().Where(e => e.State == EntityState.Added || e.State == EntityState.Modified || e.State == EntityState.Deleted);
                foreach (var ent in nechngs)
                {
                    var entityType = ent.Entity.GetType();
                    TrackChangesAttribute trackChangesAttribute = entityType.GetCustomAttributes(true).OfType<TrackChangesAttribute>().FirstOrDefault();
                    if (trackChangesAttribute != null)
                    {
                        try
                        {
                            var audit = new VMAudit();
                            var tableName = ObjectContext.GetObjectType(ent.Entity.GetType()).Name;
                            var keys = ent.Entity.GetType().GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);

                            var lst = new List<AuditValue>();
                            if (ent.State == EntityState.Added)
                            {
                                var changes = ent.CurrentValues.PropertyNames.ToDictionary(pn => pn, pn => ent.CurrentValues[pn]);
                                audit.entry = ent;
                            }
                            else if (ent.State == EntityState.Modified)
                            {
                                var adt = new Audit.AuditVM();
                                adt.EventType = ent.State.ToString().Substring(0, 1);
                                adt.EventDateTime = DateTime.Now;

                                var tablename = ObjectContext.GetObjectType(ent.Entity.GetType()).ToString().Split('.').LastOrDefault();
                                adt.TableName = tablename;

                                adt.UserId = userId;
                                adt.IpAddress = ipAddress;


                                if (keys != null && keys.Count() > 0)
                                {
                                    adt.primaryKey = keys.FirstOrDefault().Name;
                                }

                                var databaseValues = ent.GetDatabaseValues();
                                var changes = ent.CurrentValues.PropertyNames.Select(pn => 
                                    new AuditValue 
                                    { 
                                        ColumnName = pn, 
                                        OriginalValue = databaseValues[pn] != null ? Convert.ToString(databaseValues[pn]) : null,
                                        NewValue = Convert.ToString(ent.CurrentValues[pn]) 
                                    });

                                foreach (var item in changes)
                                {
                                    if (adt.primaryKey == item.ColumnName)
                                    {
                                        adt.primaryValue = item.NewValue;
                                    }
                                    if (adt.PatientRecordId < 1 && item.ColumnName.Trim().ToLower().Equals("patientrecordid"))
                                    {
                                        int patRecid = 0;
                                        bool newv = int.TryParse(item.NewValue, out patRecid);

                                        //adt.PatientRecordId = int.Parse(item.NewValue);
                                        if (!newv)
                                        {
                                            int.TryParse(item.OriginalValue, out patRecid);
                                        }

                                        adt.PatientRecordId = patRecid;
                                        //adt.AuditValues.Add(item);
                                    }
                                    if ((!string.IsNullOrWhiteSpace(item.OriginalValue)) && (!string.IsNullOrWhiteSpace(item.NewValue)))
                                        if (!item.OriginalValue.Equals(item.NewValue))
                                            adt.AuditValues.Add(item);
                                }
                                audit.Modified.Add(adt);
                            }
                            else if (ent.State == EntityState.Deleted)
                            {
                                var adt = new Audit.AuditVM();
                                adt.EventType = ent.State.ToString().Substring(0, 1);
                                adt.EventDateTime = DateTime.Now;
                                adt.TableName = ObjectContext.GetObjectType(ent.Entity.GetType()).ToString();

                                var tablename = ObjectContext.GetObjectType(ent.Entity.GetType()).ToString().Split('.').LastOrDefault();
                                adt.TableName = tablename;

                                keys = (keys != null && keys.Count() > 0) ? keys : Type.GetType(adt.TableName).GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);

                                adt.UserId = userId;
                                adt.IpAddress = ipAddress;

                                if (keys != null && keys.Count() > 0)
                                {
                                    adt.primaryKey = keys.FirstOrDefault().Name;
                                }

                                var changes = ent.OriginalValues.PropertyNames.Select(pn => new AuditValue { ColumnName = pn, OriginalValue = ent.OriginalValues[pn] != null ? Convert.ToString(ent.OriginalValues[pn]) : null, NewValue = null });

                                foreach (var item in changes)
                                {
                                    if (adt.primaryKey == item.ColumnName)
                                    {
                                        adt.primaryValue = item.NewValue;
                                    }
                                    if (item.NewValue != null && item.ColumnName.Trim().ToLower().Equals("patientrecordid"))
                                    {
                                        adt.PatientRecordId = int.Parse(item.NewValue);
                                    }
                                    adt.AuditValues.Add(item);
                                }
                                audit.Deleted.Add(adt);
                            }
                            audits.Add(audit);
                        }
                        catch (Exception ex)
                        {
                            _log.Error($"Get Audit Error:{ex.ToString()}");
                        }
                    }
                }
                return audits;
            }
            return null;
        }
        private void SaveAuditJson(List<VMAudit> vmaudits,int userId,string ipAddress)
        {
            #region Save Audit
            if (vmaudits != null && vmaudits.Count() > 0)
            {
                foreach (var vmaudit in vmaudits)
                {
                    if (vmaudit.entry != null)
                    {
                        try
                        {
                            var adt = new Audit.Audit();
                            adt.TableName = ObjectContext.GetObjectType(vmaudit.entry.Entity.GetType()).Name;
                            adt.TableName = adt.TableName.Replace("Cerebrum.Data.", "");
                            var st = vmaudit.entry.State;
                            adt.EventType = "A";

                            adt.EventDateTime = DateTime.Now;
                            adt.UserId = userId;
                            adt.IpAddress = ipAddress;
                            var keys = vmaudit.entry.Entity.GetType().GetProperties().Where(p => p.GetCustomAttributes(typeof(KeyAttribute), false).Count() > 0);
                            var tb = new TableModel();
                            if (keys != null && keys.Count() > 0)
                            {
                                tb.primaryKey = keys.FirstOrDefault().Name;
                            }

                            var vals = new List<ValueModel>();
                            foreach (var ad in vmaudit.entry.CurrentValues.PropertyNames)
                            {
                                var val = Convert.ToString(vmaudit.entry.CurrentValues[ad]);
                                if (!string.IsNullOrWhiteSpace(val))
                                {
                                    if (tb.primaryKey == ad)
                                    {
                                        tb.primaryValue = val;
                                    }
                                    if (ad.Trim().ToLower().Equals("patientrecordid"))
                                    {
                                        adt.PatientRecordId = int.Parse(val);
                                    }
                                    var av = new ValueModel
                                    {
                                        CN = ad,
                                        NV = Convert.ToString(val),
                                    };
                                    vals.Add(av);
                                }
                            }

                            tb.TableName = ObjectContext.GetObjectType(vmaudit.entry.Entity.GetType()).Name;// vmaudit.entry.Entity.ToString();
                            tb.changes = vals;
                            var json = JsonConvert.SerializeObject(tb, Formatting.Indented);
                            adt.Changes = json;

                            using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                            {
                                auditContext.Audits.Add(adt);
                                auditContext.SaveChanges();
                                scope.Complete();
                            }
                        }
                        catch (Exception ex)
                        {
                            var exc = $"{ex}";
                            //throw new Exception($"Audit Exception: {ex.Message}", ex.InnerException);
                        }
                    }
                    else if (vmaudit.Modified != null && vmaudit.Modified.Count() > 0)
                    {
                        //var savedentries = this.ChangeTracker.Entries();

                        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                        {

                            foreach (var m in vmaudit.Modified)
                            {
                                var vals = new List<ValueModel>();
                                foreach (var v in m.AuditValues)
                                {
                                    var av = new ValueModel
                                    {
                                        // TableName=m.TableName,
                                        CN = v.ColumnName,
                                        OV = v.OriginalValue,
                                        NV = v.NewValue
                                    };
                                    vals.Add(av);
                                }
                                if (vals.Count() > 0)
                                {
                                    var tb = new TableModel();
                                    tb.TableName = m.TableName;
                                    tb.primaryKey = m.primaryKey;
                                    tb.primaryValue = m.primaryValue;
                                    tb.changes = vals;
                                    var json = JsonConvert.SerializeObject(tb, Formatting.Indented);
                                    m.Changes = json;
                                    var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };

                                    auditContext.Audits.Add(au);
                                }
                            }
                            //auditContext.SaveChanges();
                            auditContext.SaveChanges();
                            scope.Complete();
                        }

                        //using (var dbt = auditContext.Database.UseTransaction(this.Database.CurrentTransaction))
                        {
                            //    Task.Run(() => auditContext.SaveChanges());
                        }
                    }
                    else if (vmaudit.Deleted != null && vmaudit.Deleted.Count() > 0)
                    {
                        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Suppress))
                        {
                            foreach (var m in vmaudit.Deleted)
                            {
                                var vals = new List<ValueModel>();
                                foreach (var v in m.AuditValues)
                                {
                                    var av = new ValueModel
                                    {
                                        //TableName=m.TableName,
                                        CN = v.ColumnName,
                                        OV = v.OriginalValue,
                                        NV = v.NewValue
                                    };
                                    vals.Add(av);
                                }
                                if (vals.Count() > 0)
                                {
                                    var tb = new TableModel();
                                    tb.TableName = m.TableName;
                                    tb.primaryKey = m.primaryKey;
                                    tb.primaryValue = m.primaryValue;
                                    tb.TableName = tb.TableName;//.Replace("Cerebrum.Data.", "");
                                    tb.changes = vals;

                                    var json = JsonConvert.SerializeObject(tb, Formatting.Indented);

                                    m.Changes = json;

                                    var au = new Audit.Audit { PatientRecordId = m.PatientRecordId, UserId = m.UserId, IpAddress = m.IpAddress, EventType = m.EventType, EventDateTime = DateTime.Now, TableName = m.TableName, Changes = m.Changes };
                                    auditContext.Audits.Add(au);
                                }

                            }
                            //auditContext.SaveChanges();

                            auditContext.SaveChanges();
                            scope.Complete();
                        }
                    }
                }
            }
            #endregion
        }
    }
}
