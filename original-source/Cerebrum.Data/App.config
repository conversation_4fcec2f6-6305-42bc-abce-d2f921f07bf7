﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
  <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 --></configSections>
  <connectionStrings>
    <add name="C3Context" providerName="System.Data.SqlClient" connectionString="Server=Cerebrum3dbtest\cerebrum3,7568;Database=DEV_Cer30;User ID=cerebrum_sa;Password=***********; MultipleActiveResultSets=true;" />
    <add name="C3AuditContext" providerName="System.Data.SqlClient" connectionString="Server=Cerebrum3dbtest\cerebrum3,7568;Database=DEV_Cer30Audit;User ID=cerebrum_sa;Password=***********; MultipleActiveResultSets=true;" />
    <!--<add name="C3Context" providerName="System.Data.SqlClient" connectionString="Server=.\SQLEXPRESS;Database=Cer30;Integrated Security=True;MultipleActiveResultSets=true;" />
    <add name="C3AuditContext" providerName="System.Data.SqlClient" connectionString="Server=.\SQLEXPRESS;Database=Cer30Audit;Integrated Security=True;MultipleActiveResultSets=true;" />-->
    <add name="C3RadContext" providerName="System.Data.SqlClient" connectionString="Server=Cerebrum3dbtest\cerebrum3,7568;Database=DEV_Cer30RAD;User ID=cerebrum_sa;Password=***********; MultipleActiveResultSets=true;" />
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <log4net debug="true">
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs\C3_log.txt" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="10MB" />
      <staticLogFileName value="true" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%-5p %d %5rms %-22.22c{1} %-18.18M - %m%n" />
      </layout>
    </appender>
    <root>
      <level value="DEBUG" />
      <appender-ref ref="RollingLogFileAppender" />
    </root>
  </log4net>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" /></startup></configuration>
