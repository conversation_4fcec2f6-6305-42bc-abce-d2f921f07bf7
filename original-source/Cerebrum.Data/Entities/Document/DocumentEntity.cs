
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Entities.Document {
  [Table("Documents")]
  public class DocumentEntity
  {
    // ================================================
    // Columns
    // ================================================

    [Column("Id")]
    public int Id { get; set; }

    [Column("Name")]
    public string Name { get; set; }

    [Column("Description")]
    public string Description { get; set; }

    [Column("MimeType")]
    public string MimeType { get; set; }

    [Column("Url")]
    public string Url { get; set; } // url where the document data can be found

    [Column("CreatedAt")]
    public DateTimeOffset CreatedAt { get; set; }

    [Column("UpdatedAt")]
    public DateTimeOffset? UpdatedAt { get; set; }

    [Column("DeletedAt")]
    public DateTimeOffset? DeletedAt { get; set; }

    [Column("TenantId")]
    public  Guid TenantId { get; set; }

    [Column("PatientRecordId")]
    public int? PatientRecordId { get; set; }

    [Column("FhirId")]
    public Guid FhirId { get; set; }

    // ================================================
    // Associations
    // ================================================

    [ForeignKey("PatientRecordId")]
    public virtual PatientRecord PatientRecord { get; set; }
  }
}