﻿using System;
using System.ComponentModel.DataAnnotations;

using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// Bill
    /// </summary>
    [TrackChanges]
    public class ApplicationSetting
    {
        [Key]
        public int id { get; set; }
        [StringLength(128)]
        public string key { get; set; }
        [StringLength(128)]
        public string value { get; set; }
        public bool isActive { get; set; }
        public DateTime dateCreated { get; set; } = DateTime.Now;
        public DateTime? dateLastModified { get; set; }
        public int? lastModifiedBy { get; set; }
    }
}