﻿using System;
using System.ComponentModel.DataAnnotations;

using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.Data
{
    /// <summary>
    /// office' urls
    /// </summary>
    ////[Table("OfficeSetting")]
    [TrackChanges]
    public class OfficeSetting
    {
        [Key]
        public int id { get; set; }
        public int officeId { get; set; }
        public OfficeSettingType settingType { get; set; }

        [StringLength(128)]
        public string key { get; set; }
        [StringLength(128)]
        public string value { get; set; }
        public bool isActive { get; set; }
        public DateTime dateCreated { get; set; } = DateTime.Now;
        public DateTime? dateLastModified { get; set; }
        public int? lastModifiedBy { get; set; }
        public virtual Office office { get; set; }
    }
}
