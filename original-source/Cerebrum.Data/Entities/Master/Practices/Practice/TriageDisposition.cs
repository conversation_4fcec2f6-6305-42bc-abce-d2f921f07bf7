﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class TriageDisposition
    {
        [Key]
        public int Id { get; set; }
        public int PracticeId { get; set; }
        [StringLength(250)]
        public string message { get; set; }
        public int displayOrder { get; set; }

    }
}
