﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// office'room
    /// </summary>
    public class OfficeRoom
    {
        public int Id { get; set; }
        [ForeignKey("OfficeRoomType")]
        public int OfficeRoomTypeId { get; set; }
        public virtual OfficeRoomType OfficeRoomType { get; set; }
        public int OfficeId { get; set; }
        public virtual Office Office { get; set; }
        public virtual List<OfficeRoomPermission> OfficeRoomPermissions { get; set; } = new List<OfficeRoomPermission>();
    }
    public class OfficeRoomPermission
    {
        public int Id { get; set; }
        public int permissionId { get; set; }
        public int OfficeRoomId { get; set; }
        public virtual OfficeRoom OfficeRoom { get; set; }
    }

    public class OfficeRoomType
    {
        public int Id { get; set; }
        [StringLength(200)]
        public string type { get; set; }
        public int PracticeId { get; set; }
        public virtual Practice Practice { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; }
    }

    public class OfficeEmail
    {
        public int Id { get; set; }
        public int OfficeId { get; set; }
        [StringLength(64)]
        public string serverUrl { get; set; }
        [StringLength(10)]
        public string serverPort { get; set; }
        [StringLength(64)]
        public string userName { get; set; }
        [StringLength(32)]
        public string password { get; set; }
        public bool enableSsl { get; set; }
        public virtual Office Office { get; set; }
        public DateTime createdDateTime { get; set; }
        public DateTime updatedDateTime { get; set; }
    }
}
