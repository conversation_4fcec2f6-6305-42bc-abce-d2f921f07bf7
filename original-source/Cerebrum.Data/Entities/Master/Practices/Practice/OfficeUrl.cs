﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// office' urls
    /// </summary>
    ////[Table("OfficeUrl")]
    [TrackChanges]
    public class OfficeUrl
    {
        [Key]
        public int Id { get; set; }
        public int officeId { get; set; }
        [StringLength(250)]
        public string url { get; set; }
        public int urlTypeId { get; set; }
        [StringLength(50)]
        public string folder { get; set; }
        public virtual Office office { get; set; }
        [ForeignKey("urlTypeId")]
        public virtual OfficeUrlType officeUrlType { get; set; }
    }
}
