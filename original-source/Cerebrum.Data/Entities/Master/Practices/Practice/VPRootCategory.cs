﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    ////[Table("VPRootCategory")]
    public class VPRootCategory
    {
        [Key]
        public int Id { get; set; }

        [StringLength(2000)]
        public string name { get; set; }
        public short order { get; set; }
        public virtual List<VPItem> VPItems { get; set; }
        public VPRootCategory()
        {
            this.VPItems = new List<VPItem>();
        }
        public int SpecialtyId { get; set; }
        public virtual Specialty Specialty { get; set; }
    }
}


