﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Triage Urgency
    /// </summary>
    [TrackChanges]
    public class TriageUrgency
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public string description { get; set; }
    }
}
