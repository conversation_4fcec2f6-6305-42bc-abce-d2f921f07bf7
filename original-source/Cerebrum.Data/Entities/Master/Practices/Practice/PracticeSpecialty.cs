﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PracticeSpecialty")]
    public class PracticeSpecialty
    {
        [Key]
        public int Id { get; set; }
        public int SpecialtyId { get; set; }
        //public virtual List<VPRootCategory> VPRootCategories { get; set; }
        public virtual List<WSRootCategory> WSRootCategories { get; set; }
        public PracticeSpecialty()
        {
            //this.VPRootCategories = new List<VPRootCategory>();
            this.WSRootCategories = new List<WSRootCategory>();
        }
        public int PracticeId { get; set; }
        public virtual Practice Practice { get; set; }
    }
}


