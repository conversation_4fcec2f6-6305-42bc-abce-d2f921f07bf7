﻿using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    ////[Table("PracticeTest")]
    [TrackChanges]
    public class PracticeTest
    {
        [Key]
        public int Id { get; set; }
        [StringLength(1000)]
        public string TestInstruction { get; set; }
        public TestDuration testDuration { get; set; } = TestDuration.T15;
        public DateTime createdDate { get; set; }
        public DateTime updatedDate { get; set; }
        public bool isActive { get; set; } = false;
        public bool attachRawData { get; set; } = false;
        public int TestId { get; set; }
        public int RawDataPages { get; set; } = 1;
        public virtual Test Test { get; set; }
        public int PracticeId { get; set; }
        public virtual Practice Practice { get; set; }
    }
}


