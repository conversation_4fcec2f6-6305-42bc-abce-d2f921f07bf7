﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class PracticeExternalDoctor
    {
        public PracticeExternalDoctor()
        {
            var currentDate = System.DateTime.Now;
            DateCreated = currentDate;
            DateLastModified = currentDate;
        }
        public int Id { get; set; }

        [Index("IX_PracticeExternalDoctor", 1, IsUnique = true)]
        public int PracticeId { get; set; }

        [ForeignKey("PracticeId")]
        public Practice Practice { get; set; }

        [Index("IX_PracticeExternalDoctor", 2, IsUnique = true)]
        public int ExternalDoctorId { get; set; }

        [ForeignKey("ExternalDoctorId")]
        public ExternalDoctor ExternalDoctor { get; set; }
        public bool IsVIP { get; set; }
        public int? PreferredDoctorId { get; set; } // preferred doctor who this external doctor wants to refer to. its externaldoctorId

        [ForeignKey("PreferredDoctorId")]
        public ExternalDoctor PreferredDoctor { get; set; }

        public bool IsAviva { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? LastModifiedByUserId { get; set; }
    }
}
