﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace Cerebrum.Data
{
    /// <summary>
    /// office' url type
    /// </summary>
    ////[Table("OfficeUrlType")]
    [TrackChanges]
    public class OfficeUrlType
    {
        [Key]
        public int Id { get; set; }
        [StringLength(50)]
        public string urlType { get; set; }
        [StringLength(250)]
        public string description { get; set; }
    }
}
