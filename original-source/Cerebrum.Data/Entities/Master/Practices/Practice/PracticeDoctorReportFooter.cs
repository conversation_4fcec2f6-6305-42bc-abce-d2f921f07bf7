﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PracticeDoctorReportFooter")]
    public class PracticeDoctorReportFooter
    {
        [Key]
        public int Id { get; set; }
        [StringLength(200)]
        public string Name { get; set; }
        public int PracticeDoctorId { get; set; }
        public byte[] Image { get; set; }
        public virtual PracticeDoctor PracticeDoctor { get; set; }
    }
}

