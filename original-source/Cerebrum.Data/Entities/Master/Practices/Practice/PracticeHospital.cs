﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class PracticeHospital
    {
        public PracticeHospital()
        {
            DateCreated = System.DateTime.Now;
            IsActive = true;
        }

        [Key]
        public int Id { get; set; }

        [Index("IX_PracticeHospital", 1, IsUnique = true)]
        public int HospitalId { get; set; }

        [ForeignKey("HospitalId")]
        public Hospital Hospital { get; set; }

        [Index("IX_PracticeHospital", 2, IsUnique = true)]
        public int PracticeId { get; set; }

        [ForeignKey("PracticeId")]
        public Practice Practice { get; set; }        
        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }

        public int? LastModifiedByUserId { get; set; }
    }
}
