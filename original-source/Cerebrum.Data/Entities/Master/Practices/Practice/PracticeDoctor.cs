﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.Data
{
    //Practice Doctor -> the group of doctors of the given practice
    ////[Table("PracticeDoctor")]
    [TrackChanges]
    public class PracticeDoctor
    {
        [Key]
        public int Id { get; set; }
        [Index("IX_ExternalDoctor", 1, IsUnique = true)]
        public int ExternalDoctorId { get; set; }
        public int mainOfficeId { get; set; }
        public bool autoActivateHRMNewPatient { get; set; } = false;
        public bool autoMarkSeenHRMReport { get; set; } = true; // auto mark seen receiving hrm report if receiver is an author
        public bool autoOpenDocument { get; set; } = true; // auto open raw data, previous report, image, referral document

        /// <summary>
        /// User name for connection from Health Canada
        /// </summary>
        [StringLength(100)]
        public string mc_un { get; set; }
        /// <summary>
        /// Password for connection from Health Canada
        /// </summary>
        [StringLength(1000)]
        public string mc_pwd { get; set; }
        /// <summary>
        /// Diagnosis VP set - will select specific data required for that specialty
        /// </summary>
        public DiagnosticVPSet diagnosisVPset { get; set; } = DiagnosticVPSet.Cardiology;
        /// <summary>
        /// Specialty:
        /// Required for claims and determines a lookup diagnostic codes and billing codes
        /// Also determines the kind of the VP 
        /// </summary>
        [StringLength(500)]
        public string specialization { get; set; } // added to the billing code
        public SpecialtyCodes specialtyCodes { get; set; }
        public virtual List<PracticeDoctorAppointmentType> doctorAppointmentType { get; set; }

        public PracticeDoctor()
        {
            this.doctorAppointmentType = new List<PracticeDoctorAppointmentType>();
            //this.Cohorts = new List<Cohort>();
        }
      
        [StringLength(100)]
        public string OLIS_IdType { get; set; }
        [StringLength(100)]
        public string OLIS_AssiningJuridictionIdCode { get; set; }
        [StringLength(50)]
        public string OLIS_AssiningJuridictionId { get; set; }
        public DateTime? OLIS_LastUpdate { get; set; }

        public int? OLISPollInterval { get; set; }
        public DateTime? OLISLastRunDate { get; set; }
        public DateTime? OLISLastSuccessRunDate { get; set; }

        [Index("IX_ExternalDoctor", 2, IsUnique = true)]
        public int PracticeId { get; set; }
        public virtual Practice Practice { get; set; }

        [StringLength(128)]
        public string ApplicationUserId { get; set; }

        public bool OLISActive { get; set; } = false;
        public OLISPreloadState OLISPreloadState { get; set; } = OLISPreloadState.Off;
        public DateTime? OLISPreloadStartDate { get; set; }
        public DateTime? OLISPreloadEndDate { get; set; }
        public virtual List<ReportPhraseByDoctor> ReportPhraseByDoctor { get; set; }
        public virtual ApplicationUser ApplicationUser { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsSendToSelf { get; set; } = false; // sent the vp or report to them selves if they are the main and referring doctor

        public int? PracticeDoctorTypeId { get; set; }
        public PracticeDoctorType PracticeDoctorType { get; set; }
        public bool? canUseCredentialForHCV { get; set; }
        public string CPRID { get; set; }
        public bool? AutomaticPrescribeIT2FAEnabled { get; set; }

    }

    [TrackChanges]
    public class CohortClass
    {
        [Key]
        public int Id { get; set; }
        [StringLength(150)]
        public string Description { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateLastModified { get; set; }
        public int PracticeId { get; set; }
        public int PracticeDoctorId { get; set; }
        public virtual List<Cohort> Cohorts { get; set; }
    }

    [TrackChanges]
    public class Cohort
    {
        [Key]
        public int Id { get; set; }
        [StringLength(150)]
        public string Description { get; set; }
        [StringLength(50)]
        public string UserId { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }

        public int practiceId { get; set; }
        public int CohortClassId { get; set; }
        public virtual CohortClass CohortClass { get; set; }

        public virtual List<PatientCohort> PatientCohorts { get; set; }

        public Cohort()
        {
            this.PatientCohorts = new List<PatientCohort>();
        }

        public int PracticeDoctorId { get; set; }
    }


    [TrackChanges]
    public class PatientCohort
    {
        [Key]
        public int Id { get; set; }
        public int PatientId { get; set; }
        public DateTime? Started { get; set; }
        public DateTime? Terminated { get; set; }
        public int DoctorId { get; set; }
        public int OfficeId { get; set; }
        [StringLength(4000)]
        public string Notes { get; set; }
        public int CohortId { get; set; }
        public virtual Cohort Cohort { get; set; }
    }

    public class ActiveHRM_User
    {
        [Key]
        public int Id { get; set; }
        [StringLength(255)]
        public string Group_Name { get; set; }
        [StringLength(255)]
        public string ClinicianName { get; set; }
        [StringLength(255)]
        public string Clinician_ID { get; set; }
        [StringLength(255)]
        public string SpecialtyID { get; set; }
        [StringLength(255)]
        public string Clinic_Location_Address { get; set; }
        [StringLength(255)]
        public string City { get; set; }
        [StringLength(255)]
        public string Postal_Code { get; set; }
        public DateTime Go_Live_Date { get; set; }
        [StringLength(255)]
        public string Lhin_num { get; set; }
        public int Total { get; set; }
    }

}
