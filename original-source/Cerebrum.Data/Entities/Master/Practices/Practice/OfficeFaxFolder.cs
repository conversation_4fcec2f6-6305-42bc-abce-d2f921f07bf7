﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("OfficeFaxFolder")]
    public class OfficeFaxFolder
    {
        [Key]
        public int Id { get; set; }
        //[Key][Column(Order = 0)]
        public int officeId { get; set; }
        //[Key][Column(Order = 1)]
        [StringLength(50)]
        public string folder { get; set; }
        [StringLength(250)]
        public string description { get; set; }
        public virtual Office office { get; set; }
    }
}
