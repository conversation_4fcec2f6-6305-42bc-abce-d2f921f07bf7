﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    ////[Table("WSRootCategory")]
    public class WSRootCategory
    {
        [Key]
        public int Id { get; set; }
        [StringLength(2000)]
        public string name { get; set; }
        public short order { get; set; }
        public virtual List<WSItem> WSItems { get; set; }
        public WSRootCategory()
        {
            this.WSItems = new List<WSItem>();
        }
        public int SpecialtyId { get; set; }
        public virtual Specialty Specialty { get; set; }
    }
}


