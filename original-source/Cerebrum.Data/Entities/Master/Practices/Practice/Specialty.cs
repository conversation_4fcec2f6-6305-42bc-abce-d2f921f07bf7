﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;

namespace Cerebrum.Data
{
    ////[Table("Specialty")]
    public class Specialty
    {
        [Key]
        public int Id { get; set; }

        [StringLength(512)]
        public string title { get; set; }
        public short code { get; set; }
        public int MasterId { get; set; }
        public virtual Master Master { get; set; }
    }
}


