﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;
using System;

namespace Cerebrum.Data
{
    /// <summary>
    /// Represents an Office in a Practice
    /// </summary>
    [Table("Office")]
    [TrackChanges]
    public class Office
    {
        [Key]
        public int Id { get; set; }
        [StringLength(200)]
        public string name { get; set; }
        [StringLength(200)]
        public string businessName { get; set; }
        [StringLength(100)]
        public string address1 { get; set; }
        [StringLength(100)]
        public string address2 { get; set; }
        [StringLength(100)]
        public string city { get; set; }
        [StringLength(20)]
        public string phone { get; set; }
        [StringLength(20)]
        public string fax { get; set; }
        [StringLength(4000)]
        public string faxUrl { get; set; }
        [StringLength(100)]
        public string state { get; set; }
        [StringLength(100)]
        public string zip { get; set; }
        [StringLength(100)]
        public string country { get; set; }
        [StringLength(4000)]
        public string url { get; set; }
        [StringLength(4)]
        public string OfficeCode { get; set; }// Unique per office. NANN (Number Alphabet Number Number)
        public UserInactivityTime AutoLogoutTime = UserInactivityTime.T15;
        /// <summary>
        /// Physical Path to the uploaded files in the Office
        /// </summary>
        /// 
        [StringLength(4000)]
        public string physicalPath { get; set; }
        [StringLength(4000)]
        public string regionalCode { get; set; }
        public virtual List<StaticIP> StaticIPs { get; set; }
        public virtual List<OfficeGroupBillingNumber> GroupBillingNumbers { get; set; }

        public virtual List<OfficeOutlook> OfficeOutlooks { get; set; }
        public virtual List<OfficeRoom> OfficeRooms { get; set; }
        //public virtual List<OfficeUrl> OfficeUrls { get; set; } = new List<OfficeUrl>();
        public virtual List<OfficeUrl> OfficeUrls { get; set; }
        [StringLength(500)]
        public string IPAddresses { get; set; }

        public Office()
        {
            this.GroupBillingNumbers = new List<OfficeGroupBillingNumber>();
            this.OfficeOutlooks = new List<OfficeOutlook>();
        }
        [StringLength(150)]
        public string externalName { get; set; }
        public int locCode { get; set; }
        [StringLength(100)]
        public string hospitalCode { get; set; }
        [StringLength(1000)]
        public string HRM_id { get; set; }
        public int province { get; set; }
        [StringLength(100)]
        public string postalCode { get; set; }
        [StringLength(1000)]
        public string imgStorageUrl { get; set; }
        [StringLength(1000)]
        public string docStorageUrl { get; set; }
        public int watingList { get; set; }
        public int status { get; set; }
        //[StringLength(50)]
        //public string MCEDTMailbox { get; set; }
        //[StringLength(50)]
        //public string MCEDTPassword { get; set; }
        //[StringLength(50)]
        //public string MCEDTId { get; set; }
        //[StringLength(10)]
        //public string BillGrNum_Consulting { get; set; }        
        //[StringLength(10)]
        //public string BillGrNum_Tech { get; set; }
        //[StringLength(10)]
        //public string BillGrNum_Prof { get; set; }
        //[StringLength(10)]
        //public string BillGrNum_IHF { get; set; }
        //public bool ConsultBilling { get; set; } = true;// 1 = for group , 0 = for personal
        [StringLength(10)]
        public string DefaultTestBillingGroup { get; set; } // xxxx = for group , 0000 = for personal
        [StringLength(10)]
        public string DefaultConsultBillingGroup { get; set; } // xxxx = for group , 0000 = for personal
        [StringLength(10)]
        public string DefaultIHFBillingGroup { get; set; } // xxxx = for group , 0000 = for personal
        [StringLength(30)]
        public string HRM_UPI { get; set; }
        public int OfficeSpeciality { get; set; }
        public bool scheduleByRoom { get; set; } = false;
        public int NumberOfDaysPswdExp { get; set; }
        public int NumberOfLoginsAllowed { get; set; }
        public bool ShowInfo { get; set; } = false;
        public int PracticeId { get; set; }
        public virtual Practice Practice { get; set; }
        public bool? IsHeadOffice { get; set; }
        public bool? IsPrescribeITenabled { get; set; }
        [StringLength(30)]
        public string CPRID { get; set; }
        public Guid? ClinicId { get; set; }
    }


    /// <summary>
    /// For mapping billing type between offices and tests
    /// </summary>
    ////[Table("BillingType_Office_Test")]
    public class BillingType_Office_Test
    {
        [Key]
        public int Id { get; set; }
        public int TestId { get; set; }
        public int OfficeId { get; set; }
        [StringLength(10)]
        public string ProfessionalBillingGroup { get; set; } // xxxx = for group , 0000 = for personal
        [StringLength(10)]
        public string TechnicalBillingGroup { get; set; } // xxxx = for group , 0000 = for personal
        [StringLength(10)]
        public string IHFBillingGroup { get; set; } // xxxx = for group
    }

    /// <summary>
    /// Represents the set of the IP addesses of specific office
    /// </summary>
    //[Table("StaticIP")]
    public class StaticIP
    {
        [Key]
        public int Id { get; set; }
        [StringLength(1024)]
        public string IPAddress { get; set; } = "**************";
        [StringLength(1000)]
        public string Note { get; set; }
        public int OfficeId { get; set; }
        public virtual Office Office { get; set; }
    }


    /// <summary>
    /// For admin user to enter Location for a user from where he/she is allowed to login
    /// </summary>
    //[Table("UserLocation")]
    [TrackChanges]
    public class UserLocation
    {
        [Key]
        public int Id { get; set; }
        public bool EveryWhere { get; set; } = false;
        [StringLength(128)]
        public string ApplicationUserId { get; set; }
        public int OfficeId { get; set; }
    }
}


