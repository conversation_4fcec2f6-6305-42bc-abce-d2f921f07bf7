﻿using System.ComponentModel.DataAnnotations;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.Data
{
    ////[Table("OfficeOutlook")]
    public class OfficeOutlook
    {
        [Key]
        public int Id { get; set; }
        public byte[] leftLogo { get; set; }
        public byte[] rightLogo { get; set; }
        public byte[] middleLogo { get; set; }

        public int fontType { get; set; }
        public int fontSize { get; set; }
        public int fontHeadSize { get; set; }
        public int fontWeight { get; set; }
        public LogoLocation logoLocation { get; set; }
        public bool showRefDoc { get; set; }
        public bool isAddressBold { get; set; }
        public ImpressionLocation impressionLocation { get; set; }
        public bool impressionBorder { get; set; }
        public bool isTable { get; set; }
        public bool isWebsite { get; set; }
        [StringLength(1000)]
        public string siteUrl { get; set; }
        public bool isSex { get; set; }
        public bool isOfficeNameVisible { get; set; }
        public RepertHeaderType headerType { get; set; }
        [StringLength(20)]
        public string HealthCardLabel { get; set; }
        [StringLength(10)]
        public string SexLabel { get; set; }
        [StringLength(25)]
        public string ReferringDoctorLabel { get; set; }
        [StringLength(25)]
        public string FamilyDoctorLabel { get; set; }
        public int OfficeId { get; set; }
        public virtual Office Office { get; set; }
    }
}


