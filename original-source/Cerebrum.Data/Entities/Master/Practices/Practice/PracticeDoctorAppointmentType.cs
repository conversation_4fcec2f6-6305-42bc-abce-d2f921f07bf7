﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PracticeDoctorAppointmentType")]
    public class PracticeDoctorAppointmentType
    {
        [Key]
        public int Id { get; set; }
        public int duration { get; set; } = 40; // set custom duration per doctor here. By default is Appointment type duration.
        public int PracticeDoctorId { get; set; }
        public virtual PracticeDoctor PracticeDoctor { get; set; }
    }
}

