﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    /// <summary>
    /// For admin user to enter Offices for a user
    /// </summary>
    //[Table("UserOffice")]
    [TrackChanges]
    public class UserOffice
    {
        [Key]
        public int Id { get; set; }
        [StringLength(128)]
        public string ApplicationUserId { get; set; }
        public int? UserId { get; set; }
        public int OfficeId { get; set; }
        // When doctor has HL7 file and doctor is not main doctor, LifeLabs does not put Doctor's Billing number and 
        // lastname and firstname are not separted , it is hard to identify
        // as serniors suggested to put this lab code to identify
        [StringLength(10)]
        public string LabOfficeCode { get; set; }
    }
}
