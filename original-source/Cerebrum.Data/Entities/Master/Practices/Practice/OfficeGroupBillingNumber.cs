﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    ////[Table("OfficeGroupBillingNumber")]
    [TrackChanges]
    public class OfficeGroupBillingNumber
    {
        [Key]
        public int Id { get; set; }
        public ServiceLocatorIndicator serviceLocatorIndicator { get; set; } = ServiceLocatorIndicator.IHF;
        public int OfficeId { get; set; }
        public virtual Office Office { get; set; }
    }
}
