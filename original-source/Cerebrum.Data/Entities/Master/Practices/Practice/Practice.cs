﻿using System;
using AwareMD.Cerebrum.Shared.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    ////[Table("Practices")]
    public class Practice
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public string PracticeName { get; set; }
        [StringLength(4000)]
        public string PracticeFolder { get; set; }
        public short PracticeNumber { get; set; }
        [StringLength(100)]
        public string OLISOrganisationName { get; set; }
        [StringLength(100)]
        public string OLISOrganisationId { get; set; }
        public int OLISPollingInterval { get; set; } = 30;
        [StringLength(100)]
        public string OLISAdminEmail { get; set; }

        public int OLISSearchFromDate { get; set; }
        [StringLength(500)]
        public string ClinicalContextApplicationID { get; set; } = "2.16.840.1.113883.3.239.23.1.101.1";
        [StringLength(500)]
        public string ClinicalContextLocalization { get; set; } = "en-CA";
        [StringLength(500)]
        public string ClinicalContextService { get; set; } = "Core";
        [StringLength(500)]
        public string STS_url { get; set; }

        public int? OceanSiteId { get; set; }

        [StringLength(60)]
        public string OceanOAuthClientID { get; set; }

        [StringLength(60)]
        public string OceanOAuthSecret { get; set; }
        public bool? BrowserNotClosedWarningEnabled { get; set; }

        public bool? IsDhdrEnabled { get; set; }

        public bool? IsEformsEnabled { get; set; }

        [StringLength(255)]
        public string OntarioHealthClientId { get; set; }
        public Guid? TenantId { get; set; }
        [StringLength(100)]
        public string Us2AiSubdomain { get; set; }
        public bool IsAppointmentPriorityEnabled { get; set; }

        public SendOutReportMethod? SendOutReportMethod { get; set; } 
        //public bool? Waive2fa { get; set; } = false;
        public virtual List<PracticeDoctor> PracticeDoctors { get; set; } 
        public virtual List<PatientRecord> PatientRecords { get; set; }
        public virtual List<Office> Offices { get; set; }
        public virtual List<PracticeSpecialty> PracticeSpecialties { get; set; }
        public virtual List<PracticeTest> PracticeTests { get; set; }
        public virtual List<PracticeAppointmentType> AppointmentTypes { get; set; }
        public virtual List<BookingConfirmationMessage> BookingConfirmationMessages { get; set; }
        public Practice()
        {
            this.Offices = new List<Office>();
            this.PracticeDoctors = new List<PracticeDoctor>();
            this.PatientRecords = new List<PatientRecord>();
            this.PracticeSpecialties = new List<PracticeSpecialty>();
            this.PracticeTests = new List<PracticeTest>();
        }
        public int MasterId { get; set; }
        public virtual Master Master { get; set; }

    }
}


