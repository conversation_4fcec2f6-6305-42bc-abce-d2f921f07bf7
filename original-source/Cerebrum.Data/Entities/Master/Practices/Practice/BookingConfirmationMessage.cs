﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class BookingConfirmationMessage
    {
        [Key]
        public int Id { get; set; }
        public int PracticeId { get; set; }
        public virtual Practice Practice { get; set; }
        [StringLength(1000)]
        public string Message { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; }
    }
}
