﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    ////[Table("WSItem")]
    public class WSItem
    {
        [Key]
        public int Id { get; set; }

        [StringLength(2000)]
        public string name { get; set; }
        //public virtual List<WSItem> WSItems { get; set; }
        //public WSItem()
        //{
        //    this.WSItems = new List<WSItem>();
        //}
        public int WSRootCategoryId { get; set; }
        public virtual WSRootCategory WSRootCategory { get; set; }
    }
}


