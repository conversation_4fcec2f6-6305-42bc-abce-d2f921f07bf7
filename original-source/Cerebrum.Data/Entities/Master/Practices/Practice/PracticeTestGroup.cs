﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class PracticeTestGroup
    {
        [Key]
        public int Id { get; set; }
        [Index("IX_PracticeTestGroup", 1, IsUnique = true)]
        public int PracticeId { get; set; }

        [Index("IX_PracticeTestGroup", 2, IsUnique = true)]
        public int GroupId { get; set; }
        public int? SSRSReportId { get; set; }
        [ForeignKey("SSRSReportId")]
        public SSRSReport SSRSReport { get; set; }
        public bool attachRawData { get; set; } = false;
        public int RawDataPages { get; set; } = 0;
        public int GenericReportId { get; set; } = 0;// e.g from dicom
        public int GenericHeaderHeight { get; set; } = 0; // for generic reports that we have else where
        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }

        [ForeignKey("PracticeId")]
        public Practice Practice { get; set; }

        [ForeignKey("GroupId")]
        public Group Group { get; set; }
    }
}
