﻿
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PracticeDoctorReportSignature")]
    public class PracticeDoctorReportSignature
    {
        [Key]
        public int Id { get; set; }
        public int PracticeDoctorId { get; set; }
        public byte[] Image { get; set; }
        public virtual PracticeDoctor PracticeDoctor { get; set; }
    }
}
