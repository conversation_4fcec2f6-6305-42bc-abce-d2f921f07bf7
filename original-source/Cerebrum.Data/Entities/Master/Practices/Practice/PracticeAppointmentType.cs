﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    ////[Table("PracticeAppointmentType")]
    public class PracticeAppointmentType
    {
        [Key]
        public int Id { get; set; }
        [ForeignKey("Practice")]
        public int PracticeId { get; set; }
        public virtual Practice Practice { get; set; }
        public int AppointmentTypeId { get; set; }
        public virtual AppointmentType AppointmentType { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
    }
}

