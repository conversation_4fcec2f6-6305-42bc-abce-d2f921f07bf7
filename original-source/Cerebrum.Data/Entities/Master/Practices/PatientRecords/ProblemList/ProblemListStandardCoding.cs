﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("ProblemListStandardCoding")]
    public class ProblemListStandardCoding
    {
        [Key]
        public int Id { get; set; }


        [StringLength(512)]
        public string standardCodingSystem { get; set; }


        [StringLength(512)]
        public string standardCode { get; set; }


        [StringLength(1000)]
        public string standardCodeDescription { get; set; }
        public int ProblemListId { get; set; }
        public virtual ProblemList ProblemList { get; set; }

    }
}
