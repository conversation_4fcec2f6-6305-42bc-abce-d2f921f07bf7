﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("ProblemListResidualInfo")]
    public class ProblemListResidualInfo
    {
        [Key]
        public int Id { get; set; }


        [StringLength(512)]
        public string name { get; set; }


        [StringLength(512)]
        public string dataType { get; set; }


        [StringLength(512)]
        public string content { get; set; }
        public int VP_CPP_Problem_ListId { get; set; }
        public virtual VP_CPP_Problem_List VP_CPP_Problem_List { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
    }
}
