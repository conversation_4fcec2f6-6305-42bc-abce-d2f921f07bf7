﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    //[Table("ProblemList")]
    [TrackChanges]

    public class ProblemList
    {
        [Key]
        public int Id { get; set; }


        [StringLength(1000)]
        public string categorySummaryLine { get; set; }

        //public virtual List<ProblemListResidualInfo> residualInfo { get; set; }
        [StringLength(1000)]
        public string problemDiagnosisDescription { get; set; }
        [StringLength(1000)]
        public string problemDescription { get; set; }
        [StringLength(255)]
        public string problemStatus { get; set; }

        public DateTime? onsetDate { get; set; }

        public LifeStages lifeStage { get; set; } = LifeStages.A;
        public bool lifeStageSpecified { get; set; }
        public DateTime? resolutionDate { get; set; }
        [StringLength(4000)]
        public string notes { get; set; }

        public ProblemList()
        {
            //this.residualInfo = new List<ProblemListResidualInfo>();
        }
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }

    }
}
