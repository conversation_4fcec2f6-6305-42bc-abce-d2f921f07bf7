﻿using Cerebrum.Data.Attributes;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    //[Table("PatientRecord")]
    [TrackChanges]
    public class PatientRecord
    {
        [Key]
        public int Id { get; set; }
        public virtual List<Demographic> Demographics { get; set; }
        public virtual List<Appointment> Appointments { get; set; }
        public virtual List<PatientMRN> PatientMRNs { get; set; }
        public virtual List<PersonalHistory> PersonalHistories { get; set; }
        public virtual List<PastHealth> PastHealths { get; set; }
        public virtual List<FamilyHistory> FamilyHistories { get; set; }
        public virtual List<ProblemList> ProblemLists { get; set; }
        public virtual List<HL7Patient> HL7Patients { get; set; }
        public virtual List<ReportReceived> ReportsReceived { get; set; }
        public virtual List<Pharmacy> Pharmacies { get; set; }

        public PatientRecord()
        {
            this.Demographics = new List<Demographic>();
            this.Appointments = new List<Appointment>();
            this.PatientMRNs = new List<PatientMRN>();
            this.PersonalHistories = new List<PersonalHistory>();
            this.PastHealths = new List<PastHealth>();
            this.FamilyHistories = new List<FamilyHistory>();
            this.ProblemLists = new List<ProblemList>();
            this.HL7Patients = new List<HL7Patient>();
            this.ReportsReceived = new List<ReportReceived>();
            this.Pharmacies = new List<Pharmacy>();
        }
        public int PracticeId { get; set; }
        public virtual Practice Practice { get; set; }
    }
}
