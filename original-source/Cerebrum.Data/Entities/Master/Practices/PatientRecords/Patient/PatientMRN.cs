﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{

    [TrackChanges]
    public class PatientMRN
    {
        [Key]
        public int Id { get; set; }
        public int HospitalCode { get; set; }
        [StringLength(20)]
        public string MedicalRecordNumber { get; set; }
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }

        public int? HospitalId { get; set; }
        public virtual Hospital Hospital { get; set; }
    }
}
