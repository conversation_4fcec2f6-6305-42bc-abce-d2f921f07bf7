﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class PatientRecordMessage
    {
        public int Id { get; set; }
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }
        public string Message { get; set; }
        public bool Seen { get; set; } = false;
        public int? UserId { get; set; } = 0;
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; }
    }
}
