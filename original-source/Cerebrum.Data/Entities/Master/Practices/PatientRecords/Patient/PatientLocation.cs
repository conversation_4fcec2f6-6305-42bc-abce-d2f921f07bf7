﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class PatientLocation
    {
        public PatientLocation()
        {
            DateCreated = System.DateTime.Now;
            IsActive = true;
        }
        [Key]
        public int Id { get; set; }

        [Index("IX_PatientLocation", 1, IsUnique = true)]
        public int PatientRecordId { get; set; }

        [ForeignKey("PatientRecordId")]
        public PatientRecord PatientRecord { get; set; }

        [Index("IX_PatientLocation", 2, IsUnique = true)]
        public int ExternalDoctorLocationId { get; set; } // external doctor address id
        [ForeignKey("ExternalDoctorLocationId")]
        public ExternalDoctorLocation ExternalDoctorDoctorLocation { get; set; }       
        
        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? LastModifiedByUserId { get; set; }
    }
}
