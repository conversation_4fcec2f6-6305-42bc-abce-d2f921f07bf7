﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    [Table("PatientAlert")]
    public class PatientAlert
    {
        [Key]
        public int Id { get; set; }

        public int PatientRecordId { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        [StringLength(250)]
        public string AlertMessage { get; set; }

        public DateTime LastModifiedDate { get; set; }

        public int LastModifiedByUserId { get; set; }
        public int CreatedByUserId { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsActive { get; set; }

        //public virtual PatientRecord PatientRecord { get; set; }
    }
}
