﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    ////[Table("AppointmentProvider")]
    [TrackChanges]
    public class AppointmentProvider
    {
        [Key]
        public int Id { get; set; }
        public int ExternalDoctorId { get; set; }
        public int AppointmentId { get; set; }
        public virtual Appointment Appointment { get; set; }
        public DateTime? DateCreated { get; set; }
        
    }
}
