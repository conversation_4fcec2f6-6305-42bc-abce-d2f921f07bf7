﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("AppointmentHealthCardValidation")]
    [TrackChanges]
    public class AppointmentHealthCardValidation
    {
        public int Id { get; set; }
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }
        public int AppointmentId { get; set; }
        public virtual Appointment Appointment { get; set; }
        [StringLength(20)]
        public string HealthCard { get; set; }
        [StringLength(50)]
        public string ResponseCode { get; set; }
        [StringLength(500)]
        public string ResponseDescription { get; set; }
        [StringLength(500)]
        public string ResponseAction { get; set; }
        [StringLength(20)]
        public string Tag { get; set; }
        public bool IsValid { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
    }
}
