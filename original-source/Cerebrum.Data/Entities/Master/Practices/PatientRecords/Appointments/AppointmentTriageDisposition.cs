﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class AppointmentTriageDisposition
    {
        [Key]
        public int Id { get; set; }
        public int AppointmentId { get; set; }
        public int TriageDispositionId { get; set; }
        public virtual TriageDisposition TriageDisposition { get; set; }
        [Display(Name ="Added By")]
        public int UserId { get; set; }
        public bool isActive { get; set; } = true;
        public DateTime createdDate { get; set; }
        public DateTime updatedDate { get; set; }
    }
}
