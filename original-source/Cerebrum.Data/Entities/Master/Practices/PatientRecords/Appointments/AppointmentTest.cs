﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;
using Newtonsoft.Json;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class AppointmentTest
    {
        public AppointmentTest()
        {
            IsActive = true;
            DateCreated = System.DateTime.Now;
            AppointmentTestStatusId = 3; // not arrived
        }
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        /// <summary>
        /// An appointment test could be selected only from the set of tests for the given practice
        /// </summary>
        public int TestId { get; set; }
        [JsonIgnore]
        public virtual Test Test { get; set; }
        public DateTime startTime { get; set; }
        public int AppointmentTestStatusId { get; set; }
        [JsonIgnore]
        public virtual AppointmentTestStatus AppointmentTestStatus { get; set; }

        public TestDuration testDuration { get; set; } = TestDuration.T15; // in minutes
        PaymentMethod billing { get; set; } = PaymentMethod.OHIP;

        public int? billStatusId { get; set; } = null;
        [JsonIgnore]
        [ForeignKey("billStatusId")]
        public virtual BillStatus billStatus { get; set; }

        [Display(Name = "Test Referral Doctor")]
        public int referralDoctorId { get; set; }
        [JsonIgnore]
        public virtual List<AppointmentTestResource> AppointmentTestResources { get; set; } = new List<AppointmentTestResource>();
        [JsonIgnore]
        public virtual List<TestReportingDoctor> TestReportDoctors { get; set; } = new List<TestReportingDoctor>();
        public int AppointmentId { get; set; }
        [JsonIgnore]
        public virtual Appointment Appointment { get; set; }

        [StringLength(50)]
        public string AccessionNumber { get; set; }

        [StringLength(4000)]
        public string PhysicianComments { get; set; }

        [StringLength(4000)]
        public string TechnicianComments { get; set; }
        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; } = DateTime.Now;
        public DateTime DateUpdated { get; set; } = DateTime.Now;

        public bool SetForReview { get; set; }
        public int ReassignDocID { get; set; }
        public DateTime? ReassignDate { get; set; }
        public bool IsImported { get; set; } = false;
        //public bool hasImages { get; set; }
        public bool IsAbnormal { get; set; } = false; // redmine #12567; cloned from svn 10616
       
    }

    /// <summary>
    /// Always appointment provider - could be several for the test but use only the last one.
    /// </summary>
    [TrackChanges]
    public class TestReportingDoctor
    {
        [Key]
        public int Id { get; set; }
        public int ExternalDoctorId { get; set; }
        public int AppointmentTestId { get; set; }
        public virtual AppointmentTest AppointmentTest { get; set; }
    }


}
