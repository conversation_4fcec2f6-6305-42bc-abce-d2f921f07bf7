﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    ////[Table("AppointmentPreconditon")]
    public class AppointmentPreconditon
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public string Type { get; set; }
        public bool Status { get; set; }
        public int AppointmentId { get; set; }
        public virtual Appointment Appointment { get; set; }
    }
}
