﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    //[Table("AppointmentTestStatus")]
    [TrackChanges]
    public class AppointmentTestStatus
    {
        [Key]
        public int Id { get; set; }
        [StringLength(50)]
        public string Color { get; set; }
        [StringLength(1000)]
        public string CSSClass { get; set; }
        [StringLength(500)]
        public string Status { get; set; }
    }
}