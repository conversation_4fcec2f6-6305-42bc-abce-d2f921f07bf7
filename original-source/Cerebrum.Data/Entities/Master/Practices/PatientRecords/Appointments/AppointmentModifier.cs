﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// Appointment Modifier
    /// </summary>
    ////[Table("AppointmntModifier")]
    [TrackChanges]
    public class AppointmentModifier
    {
        [Key]
        public int Id { get; set; }
        public int AppointmentId { get; set; }
        public virtual Appointment Appointment { get; set; }
        public int userId { get; set; }
        [StringLength(500)]
        public string reasonforChange { get; set; }
        [StringLength(4000)]
        public string changes { get; set; }
        public DateTime createDate { get; set; }
    }
}
