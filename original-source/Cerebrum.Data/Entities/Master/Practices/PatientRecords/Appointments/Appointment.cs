﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;
using System.IO;
using Newtonsoft.Json;
using AutoMapper;
using System.Reflection;
using System.Text;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.Data
{
    [Table("Appointments")]
    [TrackChanges]
    public class Appointment
    {
        public Appointment()
        {
            this.appointmentModifiers = new List<AppointmentModifier>();
            this.appointmentProviders = new List<AppointmentProvider>();
            this.appointmentTests = new List<AppointmentTest>();
            this.appointmentPreconditons = new List<AppointmentPreconditon>();
            this.appointmentTriageDisposition = new List<AppointmentTriageDisposition>();
            DateCreated = System.DateTime.Now;
        }

        [Key]
        //[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column("FhirId")]
        public Guid? FhirId { get; set; }

        [Display(Name = "Office")]
        public int OfficeId { get; set; }

        [Display(Name = "DateTime")]
        //[Column(TypeName = "date")]
        public DateTime appointmentTime { get; set; }

        [StringLength(10)]
        public string ArrivedTime { get; set; } //Time only
        [StringLength(10)]
        public string LeftTime { get; set; } //Time only
        [StringLength(500)]
        [Display(Name = "Purpose")]
        public string appointmentPurpose { get; set; }
        public AppointmentStatus appointmentStatus { get; set; }
        [StringLength(1000)]
        [Display(Name = "Note")]
        public string appointmentNotes { get; set; }
        [Display(Name = "Registrar")]
        public int appointmentRegistrar { get; set; }
        [StringLength(2000)]
        public string MWLUrl { get; set; }
        public bool MWLSentFlag { get; set; }
        public bool actionOnAbnormal { get; set; } = false;
        public bool bookingConfirmation { get; set; } = false;
        public bool cancellationList { get; set; } = false;
        public bool phoneReminder { get; set; } = false;
        public bool emailReminder { get; set; } = false;
        [StringLength(10)]
        public string roomNumber { get; set; }
        [Display(Name = "Appointment Provider")]
        public int PracticeDoctorId { get; set; }
        //[ForeignKey("PracticeDoctorId")]
        //public PracticeDoctor PracticeDoctor { get; set; }
        public int? billStatusId { get; set; }

        [StringLength(500)]
        public string OpeningStatement { get; set; }

        /// <summary>
        /// External doctor ID - Referral Doctor
        /// </summary>
        [Display(Name = "Referral Doctor")]
        public int referralDoctorId { get; set; }
        public int? ReferralDoctorAddressId { get; set; } // external doctor address id
        [ForeignKey("ReferralDoctorAddressId")]
        public virtual ExternalDoctorAddress ReferralDoctorAddress { get; set; }

        public int? ReferralDoctorPhoneNumberId { get; set; } // external doctor phone number id
        [ForeignKey("ReferralDoctorPhoneNumberId")]
        public virtual ExternalDoctorPhoneNumber ReferralDoctorPhoneNumber { get; set; }

        [Display(Name = "Type/Reason")]
        public int AppointmentTypeId { get; set; }
        [JsonIgnore]
        public virtual AppointmentType AppointmentType { get; set; }

        [Display(Name = "Confirmation")]
        public AppConfirmation appointmentConfirmation { get; set; } = AppConfirmation.notrequired;
        [Display(Name = "Payment Method")]
        public PaymentMethod appointmentPaymentMethod { get; set; } = PaymentMethod.OHIP;

        [JsonIgnore]
        public virtual List<AppointmentModifier> appointmentModifiers { get; set; }
        [JsonIgnore]
        public virtual List<AppointmentProvider> appointmentProviders { get; set; }
        [Display(Name = "Tests")]
        public virtual List<AppointmentTest> appointmentTests { get; set; }
        public virtual List<AppointmentPreconditon> appointmentPreconditons { get; set; }
        public virtual List<AppointmentTriageDisposition> appointmentTriageDisposition { get; set; }
        public int? TriageUrgencyId { get; set; }
        public virtual TriageUrgency TriageUrgency { get; set; }

        public int? TriageStatusId { get; set; }
        public virtual TriageStatus TriageStatus { get; set; }
        public bool IsImported { get; set; } = false;
        public bool IsActive { get; set; } = true;
        public short? RecordType { get; set; }
        [StringLength(4000)]
        public string ResidualData { get; set; }
        [Display(Name = "OHIP/Name")]
        public int PatientRecordId { get; set; }
        [JsonIgnore]
        public virtual PatientRecord PatientRecord { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime? LastModified { get; set; }

        public int? CancellationReasonId { get; set; }

        public string CancellationReasonNotes { get; set; }

        public bool eReferralWasBooked { get; set; }
        public int? eReferralOfficeId { get; set; }
        public int? AppointmentPriorityId { get; set; }
        [ForeignKey("CancellationReasonId")]
        public virtual CancellationReason CancellationReason { get; set; }

        [ForeignKey("AppointmentPriorityId")]
        public virtual AppointmentPriority AppointmentPriorities { get; set; }
        public string BillingNotes { get; set; }
    }

    public static class AppointmentJSON
    {
        public static string ToJson(this Appointment app)
        {
            StringBuilder sb = new StringBuilder();
            StringWriter sw = new StringWriter(sb);
            JsonTextWriter w = new JsonTextWriter(sw);

            w.WriteStartObject();

            foreach (var p in app.GetType().GetProperties())
            {
                if (!(p.Name.Equals("appointmentModifiers")
                    || p.Name.Equals("appointmentProviders")
                    || p.Name.Equals("appointmentPreconditons")
                    || p.Name.Equals("appointmentTriageDisposition")
                    || p.Name.Equals("AppointmentType")
                    || p.Name.Equals("PatientRecord")
                    || p.Name.Equals("appointmentTests")
                    ))
                    w.WriteProperty(p, app);
                else if (p.Name.Equals("appointmentTests"))
                {
                    w.WritePropertyName(p.Name);
                    w.WriteArraryProperty(p, app);
                }
            }

            w.WriteEndObject();

            return sb.ToString();
        }
        public static void WriteProperty(this JsonTextWriter w, PropertyInfo p, Appointment a)
        {
            w.WritePropertyName(p.Name);
            if (p.PropertyType == typeof(Int32))
            {
                w.WriteValue(p.GetValue(a));
            }
            else if (p.PropertyType == typeof(bool))
            {
                w.WriteValue(p.GetValue(a));
            }
            else
            {
                w.WriteValue($"{p.GetValue(a)}");
            }
        }
        public static void WriteArraryProperty(this JsonTextWriter w, PropertyInfo p, Appointment a)
        {
            w.WriteStartArray();
            var tsts = (List<AppointmentTest>)p.GetValue(a);
            foreach (var t in tsts)
            {
                foreach (var tp in t.GetType().GetProperties())
                {
                    if (!(tp.Name.Equals("AppointmentTestResources")
                        || tp.Name.Equals("AppointmentTestStatus")
                        || tp.Name.Equals("TestDuration")
                        || tp.Name.Equals("PaymentMethod")
                        || tp.Name.Equals("BillStatus")
                        || tp.Name.Equals("TestReportDoctors")
                        || tp.Name.Equals("Appointment")
                        ))
                    {
                        w.WriteProperty(tp, t);
                    }
                }
            }
            w.WriteEndArray();
        }
        public static void WriteProperty(this JsonTextWriter w, PropertyInfo p, AppointmentTest a)
        {
            w.WritePropertyName(p.Name);
            if (p.PropertyType == typeof(Int32))
            {
                w.WriteValue(p.GetValue(a));
            }
            else if (p.PropertyType == typeof(bool))
            {
                w.WriteValue(p.GetValue(a));
            }
            else
            {
                w.WriteValue($"\"{p.GetValue(a)}\"");
            }
        }
        internal static void RegisterMapper(IMapperConfigurationExpression c)
        {
            c.CreateMap<AppointmentPreconditon, JsonAppointmentPreconditon>()
                .IgnoreAllSourcePropertiesWithAnInaccessibleSetter();
            c.CreateMap<Appointment, JsonAppointment>()
            .ForMember(dest => dest.AppointmentType, option => option.Ignore())
            .ForMember(dest => dest.MWLUrl, option => option.Ignore())
            .ForMember(dest => dest.MWLSentFlag, option => option.Ignore())
            .IgnoreAllSourcePropertiesWithAnInaccessibleSetter();
        }
        public static string ToJsonVM(this Appointment app)
        {
            var a = new JsonAppointment();
            a = Mapper.Map<JsonAppointment>(app);
            var json = JsonConvert.SerializeObject(a, Formatting.None,
                               new JsonSerializerSettings()
                               {
                                   ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                                   NullValueHandling = NullValueHandling.Ignore,
                                   MaxDepth = 2
                               });
            return json;
        }
    }
    class JsonAppointment
    {
        public int Id { get; set; }
        public int OfficeId { get; set; }
        public DateTime appointmentTime { get; set; }
        public string ArrivedTime { get; set; }
        public string LeftTime { get; set; }
        public string appointmentPurpose { get; set; }
        public AppointmentStatus appointmentStatus { get; set; }
        public string appointmentNotes { get; set; }
        public int appointmentRegistrar { get; set; }
        public string MWLUrl { get; set; }
        public bool MWLSentFlag { get; set; }
        public bool actionOnAbnormal { get; set; } = false;
        public bool bookingConfirmation { get; set; } = false;
        public bool cancellationList { get; set; } = false;
        public bool phoneReminder { get; set; } = false;
        public bool emailReminder { get; set; } = false;
        [StringLength(10)]
        public string roomNumber { get; set; }
        public int PracticeDoctorId { get; set; }
        public int? billStatusId { get; set; }
        [StringLength(500)]
        public string OpeningStatement { get; set; }

        public int referralDoctorId { get; set; }
        public int AppointmentTypeId { get; set; }
        public virtual AppointmentType AppointmentType { get; set; }

        public AppConfirmation appointmentConfirmation { get; set; } = AppConfirmation.notrequired;
        public PaymentMethod appointmentPaymentMethod { get; set; } = PaymentMethod.OHIP;

        public List<AppointmentTest> appointmentTests { get; set; }
        public List<AppointmentTriageDisposition> appointmentTriageDisposition { get; set; }
        public List<JsonAppointmentPreconditon> appointmentPreconditons { get; set; }
        public int? TriageUrgencyId { get; set; }
        public bool IsActive { get; set; } = true;

        public int PatientRecordId { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime? LastModified { get; set; }
    }
    public class JsonAppointmentTest
    {
        public int Id { get; set; }
        public int TestId { get; set; }
        public DateTime startTime { get; set; }
        public int AppointmentTestStatusId { get; set; }

        public int referralDoctorId { get; set; }
        public int AppointmentId { get; set; }

        public string AccessionNumber { get; set; }

        public int ReassignDocID { get; set; }
        public DateTime? ReassignDate { get; set; }
    }
    public class JsonAppointmentPreconditon
    {
        public int Id { get; set; }
        public string Type { get; set; }
        public bool Status { get; set; }
    }
}
