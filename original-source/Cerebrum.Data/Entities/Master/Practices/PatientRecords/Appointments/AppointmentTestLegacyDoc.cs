﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class AppointmentTestLegacyDoc
    {
        [Key]
        public int Id { get; set; }
        public int AppointmentTestId { get; set; }
        public virtual AppointmentTest AppointmentTest { get; set; }
        [StringLength(512)]
        public string reportUrl { get; set; }
        [StringLength(512)]
        public string letterUrl { get; set; }
        [StringLength(512)]
        public string rawDocumentUrl { get; set; }
        
    }
}
