﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class AppointmentsAssociation 
    {
        //Original appointment Id
        [Key]
        [Column(Order = 1)]
        public int ParentAppointmentId { get; set; }
        [ForeignKey("ParentAppointmentId")]
        public Appointment ParentAppointment { get; set; }
        //Child appointment Id
        [Key]
        [Index(IsUnique = true)]
        [Column(Order = 2)]
        public int AppointmentId { get; set; }
        [ForeignKey("AppointmentId")]
        public Appointment ChildAppointment { get; set; }
        public DateTime CreatedDateTime { get; set; }
    }   
}
