﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class AppointmentC2ToC3
    {
        [Key]
        public int ID { get; set; }

        public int C2_AppointmentID { get; set; }
        public int C3_AppointmentID { get; set; }
        [StringLength(20)]
        public string datasource { get; set; }
    }

    public class AppC2ToC3Url
    {
        [Key]
        public int ID { get; set; }
        public int appointmentID { get; set; }
        public int? visitID { get; set; }
        public int? testID { get; set; }
        [StringLength(5000)]
        public string letterUrl { get; set; }
        [StringLength(5000)]
        public string reportUrl { get; set; }
        [StringLength(20)]
        public string datasource { get; set; }
    }
}
