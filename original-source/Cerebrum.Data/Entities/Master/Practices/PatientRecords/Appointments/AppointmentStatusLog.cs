﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    ////[Table("AppointmentStatusLog")]
    [TrackChanges]
    public class AppointmentStatusLog
    {
        [Key]
        public int Id { get; set; }
        [StringLength(50)]
        public string userName { get; set; }
        public DateTime changedDateTime { get; set; }
        public AppointmentStatus appointmentStatus { get; set; } = AppointmentStatus.ReadyForDoctor;
        public int AppointmentId { get; set; }
        public virtual Appointment Appointment { get; set; }
    }
}
