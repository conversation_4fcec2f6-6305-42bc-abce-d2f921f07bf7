﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class AppointmentTestResource
    {
        [Key]
        public int Id { get; set; }
        public int AppointmentTestId { get; set; }
        public AppointmentTest AppointmentTest { get; set; }
        public int? assignedToUserId { get; set; }
        public int? performedByUserId { get; set; }

        public int permissionId { get; set; }
        
        public bool isDoctorRequiredInOffice { get; set; }
        public bool isActive { get; set; } = true;
        public DateTime createdDate { get; set; } = DateTime.Now;
        public DateTime updatedDate { get; set; } = DateTime.Now;
    }
}
