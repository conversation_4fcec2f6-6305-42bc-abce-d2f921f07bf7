﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("FamilyHistoryResidualInfo")]
    public class FamilyHistoryResidualInfo
    {
        [Key]
        public int Id { get; set; }
        [StringLength(255)]
        public string name { get; set; }
        [StringLength(255)]
        public string dataType { get; set; }
        [StringLength(255)]
        public string content { get; set; }
        
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;

        public int VP_CPP_FamilyHistoryId { get; set; }
        public virtual VP_CPP_FamilyHistory VP_CPP_FamilyHistory { get; set; }
    }
}
