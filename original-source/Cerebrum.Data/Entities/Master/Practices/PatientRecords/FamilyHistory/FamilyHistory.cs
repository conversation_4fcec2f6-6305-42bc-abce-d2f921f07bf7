﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    //[Table("FamilyHistory")]
    [TrackChanges]
    public class FamilyHistory
    {
        [Key]
        public int Id { get; set; }
        [StringLength(512)]
        public string categorySummaryLine { get; set; }
        [StringLength(50)]
        public string ageAtOnset { get; set; }
        public DateTime? startDate { get; set; }
        public LifeStages lifeStage { get; set; } = LifeStages.A;
        public bool lifeStageSpecified { get; set; }
        [StringLength(512)]
        public string problemDiagnosisProcedureDescription { get; set; }
        [StringLength(512)]
        public string treatment { get; set; }
        [StringLength(100)]
        public string relationship { get; set; }
        [StringLength(500)]
        public string note { get; set; }
       
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
