﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Category
    /// </summary>
    [TrackChanges]
    public class LooseReportCategory
    {
        [Key]
        public int Id { get; set; }
        public int reportClassId { get; set; }
        [StringLength(50)]
        public string category { get; set; }
    }
}