﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    //[Table("PastHealth")]
    [TrackChanges]

    public class PastHealth
    {
        [Key]
        public int Id { get; set; }


        [StringLength(4000)]
        public string categorySummaryLine { get; set; }

        public virtual List<PastHealthResidualInfo> residualInfo { get; set; }


        [StringLength(4000)]
        public string pastHealthProblemDescriptionOrProcedures { get; set; }

        public DateTime? startDate { get; set; }

        public LifeStages lifeStage { get; set; } = LifeStages.A;
        public bool lifeStageSpecified { get; set; }
        public DateTime? resolvedDate { get; set; }
        public DateTime? procedureDate { get; set; }
        [StringLength(4000)]
        public string note { get; set; }
        [StringLength(255)]
        public string problemStatus { get; set; }
        public PastHealth()
        {
            this.residualInfo = new List<PastHealthResidualInfo>();
        }
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }
        //public int Id { get; set; }
        //public virtual PastHealthStandardCoding PastHealthStandardCoding { get; set; }

    }
}
