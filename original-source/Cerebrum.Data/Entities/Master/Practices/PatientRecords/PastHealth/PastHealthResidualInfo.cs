﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("PastHealthResidualInfo")]
    public class PastHealthResidualInfo
    {
        [Key]
        public int Id { get; set; }

        [StringLength(512)]
        public string name { get; set; }

        [StringLength(512)]
        public string dataType { get; set; }

        [StringLength(512)]
        public string content { get; set; }

        public int PastHealthId { get; set; }

        public virtual PastHealth PastHealth { get; set; }
    }
}
