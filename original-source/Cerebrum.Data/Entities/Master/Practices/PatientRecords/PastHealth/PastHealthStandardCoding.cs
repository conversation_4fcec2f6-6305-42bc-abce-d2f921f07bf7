﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("PastHealthStandardCoding")]
    public class PastHealthStandardCoding
    {
        [Key]
        public int Id { get; set; }


        [StringLength(512)]
        public string standardCodingSystem { get; set; }
        [StringLength(512)]
        public string standardCode { get; set; }
        [StringLength(512)]
        public string standardCodeDescription { get; set; }

        public int PastHealthId { get; set; }
        public virtual PastHealth PastHealth { get; set; }

    }
}
