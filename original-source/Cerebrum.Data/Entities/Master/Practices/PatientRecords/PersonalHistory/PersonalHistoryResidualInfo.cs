﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PersonalHistoryResidualInfo")]
    public class PersonalHistoryResidualInfo
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public string name { get; set; }
        [StringLength(100)]
        public string dataType { get; set; }
        [StringLength(200)]
        public string content { get; set; }
        public int PersonalHistoryId { get; set; }
        public virtual PersonalHistory PersonalHistory { get; set; }
    }
}
