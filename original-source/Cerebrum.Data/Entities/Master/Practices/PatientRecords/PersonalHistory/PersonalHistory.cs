﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    //[Table("PatientHistory")]
    [TrackChanges]

    public class PersonalHistory
    {
        [Key]
        public int Id { get; set; }


        [StringLength(1000)]
        public string categorySummaryLine { get; set; }

        public virtual List<PersonalHistoryResidualInfo> residualInfo { get; set; }

        public PersonalHistory()
        {
            this.residualInfo = new List<PersonalHistoryResidualInfo>();
        }
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
