﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    //[Table("Demographic")]
    [TrackChanges]
    public class Demographic
    {
        [Key]
        public int Id { get; set; }

        [Column("FhirId")]
        public Guid? FhirId { get; set; }

        [StringLength(100)]
        public string FederatedId { get; set; }
        public Salutation namePrefix { get; set; } = Salutation.Prof;

        [StringLength(100)]
        public string firstName { get; set; }

        [StringLength(100)]
        public string middleName { get; set; }

        [StringLength(100)]
        public string lastName { get; set; }

        [StringLength(100)]
        public string preferredName { get; set; }

        [Display(Name = "Patient Name")]
        public string FullName
        {
            get { return lastName + ", " + firstName; }
        }
        public bool useAliases { get; set; } = false;

        [StringLength(100)]
        public string aliasFirstName { get; set; }

        [StringLength(100)]
        public string aliasMiddleName { get; set; }

        [StringLength(100)]
        public string aliasLastName { get; set; }

        [Display(Name = "Patient Name")]
        public string aliasFullName
        {
            get { return aliasLastName + ", " + aliasFirstName; }
        }

        [Column(TypeName = "Date")]
        public DateTime? dateOfBirth { get; set; }

        [StringLength(20)]
        public string SIN { get; set; } = "**********";

        [StringLength(100)]
        public string chartNumber { get; set; }

        [StringLength(20)]
        public string pharmacyFaxNumber { get; set; }

        [StringLength(20)]
        public string pharmacyPhoneNumber { get; set; }

        [StringLength(500)]
        public string pictureURL { get; set; }

        [StringLength(50)]
        public string email { get; set; }

        //patient has email / permission to email
        public bool hasEmail { get; set; }
        public bool consentEmail { get; set; } = false;

        [StringLength(50)]
        public string password { get; set; } // for login into web page from home and submit data like blood presure, etc. username: healthcard #.// todo: add password encoding/decoding
        public Gender gender { get; set; }
        public Language officialSpokenLanguage { get; set; } = Language.ENG;
        public int? OfficialLanguageId { get; set; }
        public int? PreferredLanguageId { get; set; }

        [StringLength(50)]
        public string uniqueVendorIdSequence { get; set; }
        public bool preferredOfficialLanguageSpecified { get; set; } = false;

        [StringLength(100)]
        public string preferredSpokenLanguage { get; set; }

        [StringLength(4000)]
        public string notesAboutPatient { get; set; }
        public PersonStatus personStatusCode { get; set; }
        public DateTime? personStatusDate { get; set; }
        public bool personStatusDateSpecified { get; set; }

        [StringLength(100)]
        public string HospitalCode { get; set; }
        public int InsuranceCompanyId { get; set; }
        public InsuranceKind insuranceType { get; set; } = InsuranceKind.Private;

        [Display(Name = "Payment Method")]
        public PaymentMethod defaultPaymentMethod { get; set; } = PaymentMethod.OHIP;
        public Active active { get; set; } = Active.Active;
        public DateTime? StatusDate { get; set; }
        public Province Province { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string FavoritePharmacy { get; set; }

        [StringLength(100)]
        public string Phar_Address { get; set; }

        [StringLength(50)]
        public string Phar_City { get; set; }
        public Province Phar_Province { get; set; }
        public Country Phar_Country { get; set; }

        [StringLength(10)]
        public string Phar_PostalCode { get; set; }

        [StringLength(50)]
        public string Phar_Email { get; set; }
        public AddressTypes Phar_AddressType { get; set; }

        [Newtonsoft.Json.JsonIgnoreAttribute]
        public virtual List<DemographicsHealthCard> healthcards { get; set; }
        public virtual List<DemographicsAddress> addresses { get; set; }
        public virtual List<DemographicsPhoneNumber> phoneNumbers { get; set; }
        public virtual List<DemographicsNextOfKin> demographicsContacts { get; set; }
        public virtual List<DemographicsFamilyDoctor> familyDoctors { get; set; }
        public virtual List<DemographicsMainResponsiblePhysician> mainResponsiblePhysicians { get; set; }
        public virtual List<DemographicsDefaultReferralDoctor> defaultReferralDoctors { get; set; }
        public virtual List<DemographicsAssociatedDoctor> associatedlDoctors { get; set; }

        public Demographic()
        {
            healthcards = new List<DemographicsHealthCard>();
            phoneNumbers = new List<DemographicsPhoneNumber>();
            demographicsContacts = new List<DemographicsNextOfKin>();
            addresses = new List<DemographicsAddress>();
            familyDoctors = new List<DemographicsFamilyDoctor>();
            defaultReferralDoctors = new List<DemographicsDefaultReferralDoctor>();
            mainResponsiblePhysicians = new List<DemographicsMainResponsiblePhysician>();
            associatedlDoctors = new List<DemographicsAssociatedDoctor>();
        }

        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }

        public int? MergedIntoId { get; set; }

        [ForeignKey("MergedIntoId")]
        public virtual Demographic MergedInto { get; set; }

        public int? search_pid_1 { get; set; }

        [StringLength(2000)]
        public string info { get; set; }
    }
}
