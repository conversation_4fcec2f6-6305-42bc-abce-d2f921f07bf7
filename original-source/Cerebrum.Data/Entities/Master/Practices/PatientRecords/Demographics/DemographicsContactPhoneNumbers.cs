﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace Cerebrum.Data
{
    [TrackChanges]
    public class DemographicsContactPhoneNumber
    {
        [Key]
        public int Id { get; set; }
        [StringLength(50)]
        public string contactPhoneNumber { get; set; }
        [StringLength(20)]
        public string extention { get; set; }
        public virtual int DemographicsContactId { get; set; }
        public virtual DemographicsNextOfKin DemographicsContact { get; set; }
        public bool IsActive { get; set; } = false;
        public bool IsRemoved { get; set; } = false;
        public PhoneNumberType typeOfPhoneNumber { get; set; }
    }
}
