﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace Cerebrum.Data
{
    [TrackChanges]
    public class DemographicsNextOfKin
    {
        [Key]
        public int Id { get; set; }
        [StringLength(512)]
        public string contactPurpose { get; set; }
        public ContactPurpose ContactPurposeEnum { get; set; }
        [StringLength(50)]
        public string firstName { get; set; }
        [StringLength(50)]
        public string middleName { get; set; }
        [StringLength(50)]
        public string lastName { get; set; }
        [StringLength(100)]
        public string emailAddress { get; set; }
        [StringLength(500)]
        public string notes { get; set; }
        public bool IsActive { get; set; } = false;
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public bool IsRemoved { get; set; } = false;

        public virtual List<DemographicsContactPhoneNumber> contactPhoneNumbers { get; set; }
        public DemographicsNextOfKin()
        {
            this.contactPhoneNumbers = new List<DemographicsContactPhoneNumber>();
        }
        public virtual int DemographicId { get; set; }
        public virtual Demographic Demographic { get; set; }
    }
}
