﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("DemographicsHealthCard")]
    [TrackChanges]
    public class DemographicsHealthCard
    {
        [Key]
        public int Id { get; set; }
        [StringLength(20)]
        public string number { get; set; }
        [StringLength(512)]
        public string version { get; set; }
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}")]
        public DateTime? expirydate { get; set; }
        public DateTime? dateIssued { get; set; }
        public DateTime? lastValidated { get; set; }
        public PatientIdentifierType patientIdentifierType { get; set; } = PatientIdentifierType.JHN;
        public Province provinceCode { get; set; } = Province.CAON;
        public virtual int DemographicId { get; set; }
        public virtual Demographic Demographic { get; set; }
        public bool IsActive { get; set; } = false;
        public bool IsRemoved { get; set; } = false;
    }
}
