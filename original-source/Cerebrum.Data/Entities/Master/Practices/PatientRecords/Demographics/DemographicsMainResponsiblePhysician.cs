﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("DemographicsMainResponsiblePhysician")]
    [TrackChanges]
    public class DemographicsMainResponsiblePhysician
    {
        [Key]
        public int Id { get; set; }
        public int PracticeDoctorId { get; set; }
        public int ExternalDoctorId { get; set; }
        public virtual List<DemographicsEnrollment> Enrollments { get; set; }
        public DemographicsMainResponsiblePhysician()
        {
            this.Enrollments = new List<DemographicsEnrollment>();
        }
        public virtual int DemographicId { get; set; }
        public virtual Demographic Demographic { get; set; }
        public bool IsActive { get; set; } = false;
        public bool IsRemoved { get; set; } = false;
    }

    [TrackChanges]
    public class DemographicsEnrollment
    {
        [Key]
        public int Id { get; set; }
        public bool enrolled { get; set; }
        public EnrollmentStatus enrollmentStatus { get; set; }
        public bool enrollmentStatusSpecified { get; set; }
        public DateTime? enrollmentDate { get; set; }
        public bool enrollmentDateSpecified { get; set; }
        public DateTime? enrollmentTerminationDate { get; set; }
        public bool enrollmentTerminationDateSpecified { get; set; }
        public TerminationReason terminationReason { get; set; }
        public bool terminationReasonSpecified { get; set; }
        public virtual int DemographicsMRPId { get; set; }
        public virtual DemographicsMainResponsiblePhysician DemographicsMainResponsiblePhysician { get; set; }
    }
}
