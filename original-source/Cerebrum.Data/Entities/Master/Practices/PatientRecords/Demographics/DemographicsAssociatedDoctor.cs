﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("DemographicsAssociatedDoctor")]
    [TrackChanges]
    public class DemographicsAssociatedDoctor
    {
       
        [Key]
        public int Id { get; set; }
        public bool? IsCC { get; set; }
        public int ExternalDoctorId { get; set; } 

        [StringLength(512)] 
        public string authorizedBy { get; set; }
        public DateTime? authorizedDate { get; set; }
        [StringLength(512)]
        public string removedBy { get; set; }
        public DateTime? removedDate { get; set; }
        public virtual int DemographicId { get; set; }
        public virtual Demographic Demographic { get; set; }
        public bool IsActive { get; set; } = false;
        public bool IsRemoved { get; set; } = false;
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;

         
        public int? ExternalDoctorLocationId { get; set; }
        [ForeignKey("ExternalDoctorLocationId")]
        public virtual ExternalDoctorLocation ExternalDoctorLocation { get; set; }
 
    }
}
