﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{

    [TrackChanges]
    public class DemographicsDefaultReferralDoctor
    {
        [Key]
        public int Id { get; set; }
        public int ExternalDoctorId { get; set; }
        public bool enrolled { get; set; }
        public bool enrollmentStatusSpecified { get; set; }
        public DateTime? enrollmentDate { get; set; }
        public bool enrollmentDateSpecified { get; set; }
        public DateTime? enrollmentTerminationDate { get; set; }
        public bool enrollmentTerminationDateSpecified { get; set; }
        public TerminationReason terminationReason { get; set; }
        public bool terminationReasonSpecified { get; set; }
        public virtual int DemographicId { get; set; }
        public virtual Demographic Demographic { get; set; }
        public bool IsActive { get; set; } = false;
        public bool IsRemoved { get; set; } = false;
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
    }
}
