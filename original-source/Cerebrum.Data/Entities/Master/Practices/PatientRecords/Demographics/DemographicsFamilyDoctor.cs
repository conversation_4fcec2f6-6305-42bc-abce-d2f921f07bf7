﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("DemographicsFamilyDoctor")]
    [TrackChanges]
    public class DemographicsFamilyDoctor
    {
        [Key]
        public int Id { get; set; }
        public int ExternalDoctorId { get; set; }
        
        public virtual int DemographicId { get; set; }
        public virtual Demographic Demographic { get; set; }
        public bool IsActive { get; set; } = false;
        public bool IsRemoved { get; set; } = false;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }
}
