﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace Cerebrum.Data
{
    [TrackChanges]
    public class DemographicsPhoneNumber
    {
        [Key]
        public int Id { get; set; }
        [StringLength(20)]
        public string phoneNumber { get; set; }
        [StringLength(20)]
        public string extention { get; set; }
        [StringLength(20)]
        public string faxNumber { get; set; }
        [StringLength(200)]
        public string notes { get; set; }
        public PhoneNumberType typeOfPhoneNumber { get; set; }
        public virtual int DemographicId { get; set; }
        public virtual Demographic Demographic { get; set; }
        public bool IsActive { get; set; } = false;
        public bool IsRemoved { get; set; } = false;
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        [StringLength(20)]
        public string PhoneNumberSearch { get; set; }
    }
}
