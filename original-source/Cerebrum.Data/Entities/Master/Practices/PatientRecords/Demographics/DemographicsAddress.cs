﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class DemographicsAddress
    {
        [Key]
        public int Id { get; set; }
        [StringLength(200)]
        public string addressLine1 { get; set; }
        [StringLength(200)]
        public string addressLine2 { get; set; }
        [StringLength(100)]
        public string city { get; set; }
        [StringLength(10)]
        [Description("PostalCode/ZipCode")]
        public string postalCode { get; set; }
        public PostalCodeType postalCodeType { get; set;}
        [StringLength(100)]
        [Description("countrySubdivisionCode Province/Province")]
        public string province { get; set; }
        [StringLength(100)]
        public string country { get; set; }
        public AddressTypes addressType { get; set; }

        public virtual int DemographicId { get; set; }
        public virtual Demographic Demographic { get; set; }
        public bool IsActive { get; set; } = false;
        public bool IsRemoved { get; set; } = false;
    }
}
