﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    ////[Table("Master")]
    public class Master
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public string MasterName { get; set; }
        public DateTime? GenerationDate { get; set; }
        public virtual List<Practice> Practices { get; set; }
        public virtual List<ExternalDoctor> ExternalDoctors { get; set; }
        public virtual List<InsuranceCompany> InsuranceCompanies { get; set; }
        public virtual List<Specialty> Specialties { get; set; }
        public virtual List<PermissionsBase> PermissionsBases { get; set; }
        public Master()
        {
            this.Practices = new List<Practice>();
            this.ExternalDoctors = new List<ExternalDoctor>();
            this.InsuranceCompanies = new List<InsuranceCompany>();
            this.Specialties = new List<Specialty>();
            this.PermissionsBases = new List<PermissionsBase>();
        }
    }
}
