﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7MarkedSeen
    {
        [Key]
        public int Id { get; set; }
        public int? practiceDoctorId { get; set; }

        public int? UserId { get; set; }
        // For CDS Import
        [StringLength(200)]
        public string Reviewer { get; set; }
        public DateTime seenAt { get; set; }
        [StringLength(40)]
        public string IPAddress { get; set; }
        [StringLength(100)]
        public string machineName { get; set; }
        [StringLength(500)]
        public string comment { get; set; }
        public int HL7ReportId { get; set; }
        public virtual HL7Report HL7Report { get; set; }
        public int HL7ReportVersionId { get; set; } 
        public virtual HL7ReportVersion HL7ReportVersion { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
    }
}
