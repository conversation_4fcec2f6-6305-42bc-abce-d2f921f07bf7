﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7ResultNote
    {
        [Key]
        public int Id { get; set; }
        public int? setId { get; set; }
        [StringLength(4000)]
        public string comments { get; set; }
        public int? userId { get; set; }
        public int HL7ResultId { get; set; }
        public int? LabLicenseNumber { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
        public virtual HL7Result HL7Result { get; set; } 
    }
}
