﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7SensitiveResultAccessLog
    {
        public int Id { get; set; }
        public int HL7PatientId { get; set; }
        public int HL7ReportId { get; set; }
        public int HL7ReportVersionId { get; set; }
        public int HL7ResultId { get; set; }
        public int UserId { get; set; }
        [StringLength(100)]
        public string IPAddress { get; set; }
        [StringLength(200)]
        public string Reason { get; set; }
        public DateTime AccessedDateTime { get; set; } = DateTime.Now;
    }
}
