﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7TestDescription
    {
        [Key]
        public int Id { get; set; }
        [StringLength(242)]
        public string testdescription { get; set; }
        public int ordernumber { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
    }
}
