﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    public class LabUser
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public String SourceName { get; set; }
        public int PracticeDoctorId { get; set; }
        [ForeignKey("PracticeDoctorId")]
        public virtual PracticeDoctor PracticeDoctor { get; set; }
        [StringLength(50)]
        public String userID { get; set; }
        [StringLength(100)]
        public String psw { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public DateTime LastModifiedDateTime { get; set; }
    }
}
