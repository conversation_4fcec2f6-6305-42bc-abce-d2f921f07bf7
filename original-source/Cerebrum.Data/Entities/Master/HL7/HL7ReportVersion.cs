﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7ReportVersion
    {
        [Key]
        public int Id { get; set; }
       
        [Column(TypeName = "CHAR")]
        [StringLength(10)]
        public string orderControl { get; set; }
        /// <summary>
        /// Must comment Test Status
        /// </summary>
        //[Column(TypeName = "CHAR")]
        //[StringLength(4)]
        //public string testStatus { get; set; }
        public int consentBlock { get; set; }
        [Column(TypeName = "CHAR")]
        [StringLength(2)]
        public string priority { get; set; }
        public DateTime? requestedDateTime { get; set; }
        
        [StringLength(50)]
        public string physicianNo { get; set; }

        [StringLength(500)]
        public string physicianName { get; set; }
        public DateTime? resultedDateTime { get; set; }
        [StringLength(100)]
        public string status { get; set; }
        [StringLength(4000)]
        public string xmlReport { get; set; }
        [StringLength(200)]
        public string ReportLevelBlockMessage { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
        public virtual List<HL7Result> HL7Results { get; set; }
        public virtual List<HL7ReportNote> HL7ReportNotes { get; set; }

        public HL7ReportVersion()
        {
            this.HL7Results = new List<HL7Result>();
            this.HL7ReportNotes = new List<HL7ReportNote>();
        }
        public int? HL7MessageId { get; set; }
        public virtual HL7Message HL7Message { get; set; }
        public int? HL7ReportId { get; set; }
        public virtual HL7Report HL7Report { get; set; }


    }
}
