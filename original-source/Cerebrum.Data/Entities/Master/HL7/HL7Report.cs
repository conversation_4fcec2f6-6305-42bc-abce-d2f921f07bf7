﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7Report
    {
        [Key]
        public int Id { get; set; }
        
        [StringLength(50)]
        public string accessionNumber { get; set; }
        [StringLength(280)]
        public string ORC4 { get; set; }
        [Column(TypeName = "CHAR")]
        [StringLength(2)]
        public string priority { get; set; }
        public DateTime? requestedDateTime { get; set; }
        public DateTime? collectionDateTime { get; set; }
        
        [StringLength(50)]
        public string physicianNo { get; set; }
        [StringLength(100)]
        public string physicianName { get; set; }
        public DateTime? resultedDateTime { get; set; }
        [StringLength(10)]
        public string status { get; set; }
        public OLISTestRequestStatus OLISTestRequestStatus { get; set; }
        public DocumentAssignmentStatuses assignmentStatus { get; set; } = DocumentAssignmentStatuses.Unassigned;

        [StringLength(200)]
        public string testLocationId { get; set; }
        
        [StringLength(500)]
        public string testLocationDescription { get; set; }
        
        [StringLength(200)]
        public string testLocationCoding { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
        [StringLength(500)]
        public string reviewerNames { get; set; }
        public int HL7PatientId { get; set; }
        [StringLength(20)]
        public string testSortKey { get; set; }
        [StringLength(50)]
        public string OLISAccession { get; set; }
        [StringLength(100)]
        public string CerebrumAccession { get; set; }
        public bool IsOLISReport { get; set; } = false;
        public int? ReportingLaboratoryId { get; set; }
        public virtual Entities.OLIS.Laboratory ReportingLaboratory { get; set; }
        public virtual HL7Patient HL7Patient{ get; set; }
        public virtual List<HL7ReportVersion> HL7ReportVersions { get; set; }
        public virtual List<HL7ReportDoctor> HL7ReportDoctors { get; set; }
        public virtual List<HL7MarkedSeen> HL7MarkedSeens { get; set; }
    }
}
