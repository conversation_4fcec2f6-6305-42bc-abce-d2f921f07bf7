﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7ReportNote
    {
        [Key]
        public int Id { get; set; }
        public int setId { get; set; }
        [StringLength(4000)]
        public string comments { get; set; }
        public int HL7ReportVersionId { get; set; }
        public int? LabLicenseNumber { get; set; }
        public virtual HL7ReportVersion HL7ReportVersion { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
    }
}
