﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7Result
    {
        [Key]
        public int Id { get; set; }
        
        public int setId { get; set; }
        [Column(TypeName = "CHAR")]
        [StringLength(10)]
        public string valueType { get; set; }
        
        [StringLength(200)]
        public string observationSubId { get; set; }
        [StringLength(100)]
        public string LabTestRequestCode { get; set; }//OBR Code
        [StringLength(200)]
        public string testCodeIdentifier { get; set; }
        
        [StringLength(200)]
        public string codingSystem { get; set; }
        [StringLength(500)]
        public string testDescription { get; set; }
        //EMR's Proprietary Name of a laboratory test
        [StringLength(500)]
        public string testName { get; set; }
        [StringLength(200)]
        public string EmrTestName { get; set; }
        [StringLength(4000)]
        public string testResult { get; set; }
        public byte[] EmbeddedDocument { get; set; }
        public decimal testResultFloat { get; set; }
        [StringLength(100)]
        public string units { get; set; }
        public decimal? range1 { get; set; }
        public decimal? range2 { get; set; }
        
        [StringLength(500)]
        public string structureRefRange { get; set; }
        [StringLength(4000)]
        public string formattedRefRange { get; set; }
        [Column(TypeName = "CHAR")]
        [StringLength(10)]
        public string abnormalFlag { get; set; }
        [Column(TypeName = "CHAR")]
        [StringLength(2)]
        public string resultStatus { get; set; }
        
        [StringLength(200)]
        public string reportCode { get; set; }
        
        [StringLength(200)]
        public string reportFormID { get; set; }
        
        [StringLength(300)]
        public string reportGroupHead { get; set; }
        
        [StringLength(200)]
        public string reportGroupDescription { get; set; }
        [StringLength(200)]
        public string ReportURL { get; set; }

        public int? reportGroupID { get; set; }
        public int? groupSortCode { get; set; }
        public bool showResult { get; set; } = true;
        [StringLength(20)]
        public string testSortKey { get; set; }
        public int? LabLicenseNumber { get; set; }
        public DateTime? collectionDate { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
        public virtual List<HL7ResultNote> HL7ResultNotes { get; set; }
        public HL7Result()
        {
            this.HL7ResultNotes = new List<HL7ResultNote>();
        }
        public int HL7ReportVersionId { get; set; } 
        public virtual HL7ReportVersion HL7ReportVersion { get; set; }

    }
}
