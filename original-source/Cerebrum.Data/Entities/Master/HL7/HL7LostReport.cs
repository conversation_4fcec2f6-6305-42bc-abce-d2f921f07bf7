﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7LostReport
    {
        public int Id { get; set; }
        [StringLength(100)]
        public string AccessionNumber { get; set; }
        [StringLength(100)]
        public string PatientOHIP { get; set; }
        [StringLength(100)]
        public string PatientLastName { get; set; }
        [StringLength(100)]
        public string PatientFirstName { get; set; }
        [StringLength(100)]
        public string Lab { get; set; }       
        [MaxLength] 
        public byte[] HL7File { get; set; }
        public bool ReportSaved { get; set; } = false;
        public bool? IsActive { get; set; } = true;
        public DateTime CreateDateTime { get; set; } = DateTime.Now;
        public DateTime LastModifiedDateTime { get; set; } = DateTime.Now;
        public int? LastModifiedByUserId { get; set; }

        public virtual List<HL7LostReportDoctor> HL7LostReportDoctors { get; set; } = new List<HL7LostReportDoctor>();
    }

    [TrackChanges]
    public class HL7LostReportDoctor
    {
        public int Id { get; set; }
        [StringLength(100)]
        public string OHIPOrGroup { get; set; }
        [StringLength(200)]
        public string LastName { get; set; }
        [StringLength(200)]
        public string FirstName { get; set; }
        [StringLength(500)]
        public string Address { get; set; }
        public int? PracticeId { get; set; }
        public int? PracticeDoctorId { get; set; }
        public int? ExternalDoctorId { get; set; }
        public int HL7LostReportId { get; set; }
        [ForeignKey("HL7LostReportId")]
        public virtual HL7LostReport HL7LostReport { get; set; }
        public bool? ReportSaved { get; set; } = false;
        public DateTime CreateDateTime { get; set; } = DateTime.Now;
        public DateTime LastModifiedDateTime { get; set; } = DateTime.Now;
        public int? LastModifiedByUserId { get; set; }
        public bool? IsActive { get; set; } = true;
    }
}
