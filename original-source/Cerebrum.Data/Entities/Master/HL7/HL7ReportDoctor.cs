﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    /// <summary>
    /// HL7 Report Physician
    /// </summary>
    [TrackChanges]
    public class HL7ReportDoctor
    {
        [Key]
        public int Id { get; set; }
        /// <summary>
        /// From HL7 Report
        /// </summary>
        public int HL7ReportId { get; set; }
        public virtual HL7Report HL7Report { get; set; }

        /// <summary>
        /// From Practice Doctor Table
        /// </summary>
        public int? practiceDoctorId { get; set; }
        /// <summary>
        /// From External Doctor Table
        /// </summary>
        public int ExternalDoctorId { get; set; }

        public bool IsPrimary { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
    }
}
