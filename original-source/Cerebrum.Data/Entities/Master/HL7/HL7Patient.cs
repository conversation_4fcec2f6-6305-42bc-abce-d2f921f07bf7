﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    //[Table("HL7Patient")]
    [TrackChanges]
    public class HL7Patient
    {
        [Key]
        public int Id { get; set; }
        public int SetId { get; set; } = 0;

        [StringLength(20)]
        public string healthCardNo { get; set; }
        [Column(TypeName = "CHAR")]
        [StringLength(10)]
        public string versionNo { get; set; }
        [Column(TypeName = "CHAR")]
        [StringLength(2)]
        public string provinceCode { get; set; }

        [StringLength(200)] 
        public string Reference { get; set; }

        [StringLength(50)]
        public string externalID { get; set; }
        
        [StringLength(50)]
        public string internalID { get; set; }
        
        [StringLength(500)]
        public string familyName { get; set; }
        
        [StringLength(500)]
        public string firstName { get; set; }
        
        [StringLength(500)]
        public string middleName { get; set; }
        public DateTime? DOB { get; set; }
        [Column(TypeName = "CHAR")]
        [StringLength(1)]
        public string sex { get; set; }
        
        [StringLength(500)]
        public string address1 { get; set; }
        
        [StringLength(500)]
        public string address2 { get; set; }

        [StringLength(500)]
        public string city { get; set; }
        [StringLength(100)]
        public string province { get; set; }
        [StringLength(10)]
        public string postalCode { get; set; }
        [StringLength(20)]
        public string phoneNo { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
        public virtual List<HL7Report> HL7Reports { get; set; }
        public HL7Patient()
        {
            this.HL7Reports = new List<HL7Report>();
        }
        public int HL7MessageId { get; set; }
        public virtual HL7Message HL7Message { get; set; }
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
