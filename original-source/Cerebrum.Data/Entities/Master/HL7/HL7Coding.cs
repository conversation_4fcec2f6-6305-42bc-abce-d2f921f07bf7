﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7Coding
    {
        [Key]
        public int Id { get; set; }
        [StringLength(300)]
        public string labName { get; set; }
        
        [StringLength(20)]
        public string labCode { get; set; }
        
        [StringLength(20)]
        public string LOINC { get; set; }

        [StringLength(300)]
        public string description { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
        public DateTime updatedDate { get; set; } = DateTime.Now;
    }
}
