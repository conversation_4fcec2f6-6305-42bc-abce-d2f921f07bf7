﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    /// <summary>
    /// Store HL7 zip file
    /// </summary>
    [TrackChanges]

    public class HL7Lab
    {
        [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string name { get; set; }

        [StringLength(256)]
        public string webpage { get; set; }
        
    }
}
