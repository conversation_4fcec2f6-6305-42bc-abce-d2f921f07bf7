﻿using Cerebrum.Data.Attributes;
using Cerebrum.Data.Entities.OLIS;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HL7Message
    {
        [Key]
        public int Id { get; set; }
        
        [StringLength(300)]
        public string sendingApp { get; set; }
        
        [StringLength(100)]
        public string messageType { get; set; }        

        public DateTime? messageDate { get; set; }
        public Guid MSGID { get; set; }
        public byte[] rawMessage { get; set; }
        public int? OLISReceivedReportId { get; set; }
        public virtual OLISReceivedReport OLISReceivedReport { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
        public Guid? MessageAcknowledgment { get; set; }
        public virtual List<HL7Patient> HL7Patients { get; set; }
        public virtual List<HL7ReportVersion> HL7ReportVersions { get; set; }
        public HL7Message()
        {
            this.HL7Patients = new List<HL7Patient>();
            this.HL7ReportVersions = new List<HL7ReportVersion>();
        }
    }
}
