﻿using System;
using System.ComponentModel.DataAnnotations;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// ReminderSentHistory
    /// </summary>
    [TrackChanges]
    public class ReminderSentHistory
    {
        [Key]
        public int id { get; set; }
        public int officeId { get; set; }
        public int patientRecordId { get; set; }
        [StringLength(64)]
        public string appointmentId { get; set; }
        public DateTime messageSentDate { get; set; } = DateTime.Now;
        public int reminderTypeId { get; set; }
        public bool IsResendRequired { get; set; } = false;
        [StringLength(50)]
        public string ModifiedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }
}
