﻿using System.ComponentModel.DataAnnotations;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// ReminderRule
    /// </summary>
    [TrackChanges]
    public class ReminderRule
    {
        [Key]
        public int id { get; set; }
        public int officeId { get; set; }
        public int reminderTypeId { get; set; }
        [StringLength(256)]
        public string subject { get; set; }
        [StringLength(3000)]
        public string body { get; set; }
        public int scheduleMessage { get; set; }
        public int scheduleMessage2 { get; set; }
    }
}
