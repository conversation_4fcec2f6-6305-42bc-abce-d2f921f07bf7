﻿using System.ComponentModel.DataAnnotations;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// ReminderType
    /// </summary>
    [TrackChanges]
    public class ReminderType
    {
        [Key]
        public int id { get; set; }
        [StringLength(64)]
        public string name { get; set; }
        [StringLength(64)]
        public string description { get; set; }
    }
}
