﻿using System;
using System.ComponentModel.DataAnnotations;

using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    //[Table("CM_TaskMessageRecipient")]
    [TrackChanges]
    public partial class CM_TaskMessageRecipient
    {
        [Key]
        public int id { get; set; }
        [StringLength(128)]
        public string userid { get; set; }
        public bool seen { get; set; } = false;
        public DateTime? seenAt { get; set; }
        public virtual int CM_TaskMessageId { get; set; }
        public virtual CM_TaskMessage CM_TaskMessageTaskMessage { get; set; }
    }
}