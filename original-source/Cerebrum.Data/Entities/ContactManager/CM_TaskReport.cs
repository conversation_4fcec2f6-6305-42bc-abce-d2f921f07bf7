﻿using System.ComponentModel.DataAnnotations;

using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.Data
{
    //[Table("CM_TaskReport")]
    [TrackChanges]
    public class CM_TaskReport
    {
        [Key]
        public int id { get; set; }
        public int reportId { get; set; }
        public ReportType reportType { get; set; } = ReportType.Fax;
        public bool reportTypeSpecified { get; set; } = true;
        public virtual int labResultId { get; set; }
        public virtual int externalReportId { get; set; }
        public int CM_TaskDefinitionId { get; set; }
        public virtual CM_TaskDefinition CM_TaskDefinition { get; set; }
    }
}