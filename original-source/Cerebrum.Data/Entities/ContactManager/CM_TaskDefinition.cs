﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.Data
{
    //[Table("CM_TaskDefinition")]
    [TrackChanges]
    public class CM_TaskDefinition
    {
        [Key]
        public int id { get; set; }
        [StringLength(128)]
        public string userId { get; set; } //task creator user ID
        public DateTime dateCreated { get; set; }
        [StringLength(1024)]
        public string subject { get; set; }
        public DateTime? dueDate { get; set; }
        [StringLength(128)]
        public string closedBy { get; set; } //user ID task closed
        public DateTime? dateClosed { get; set; }
        public int practiceId { get; set; }
        public int? officeId { get; set; }
        public int? appointmentid { get; set; }
        public CMTaskStatus taskStatus { get; set; } = CMTaskStatus.Open;
        public CMTaskUrgency taskUrgency { get; set; } = CMTaskUrgency.Elective;
        public int? PatientRecordId { get; set; }
        public CM_TaskDefinition()
        {
            this.TaskMessages = new List<CM_TaskMessage>();
            this.TaskReports = new List<CM_TaskReport>();
        }
        public virtual PatientRecord Patient { get; set; }
        public virtual List<CM_TaskMessage> TaskMessages { get; set; }
        public virtual List<CM_TaskReport> TaskReports { get; set; }
    }
}