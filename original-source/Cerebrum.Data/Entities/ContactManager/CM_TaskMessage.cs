﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    //[Table("CM_TaskMessage")]
    [TrackChanges]
    public class CM_TaskMessage
    {
        [Key]
        public int id { get; set; }
        [StringLength(4000)]
        public string message { get; set; }
        public DateTime? messageCreatedate { get; set; }
        [StringLength(128)]
        public string messageCreator { get; set; }
        public bool? isLast { get; set; }
        public virtual List<CM_TaskMessageRecipient> TaskMessageRecipients { get; set; }
        public CM_TaskMessage()
        {
            this.TaskMessageRecipients = new List<CM_TaskMessageRecipient>();
        }
        public int CM_TaskDefinitionId { get; set; }
        public virtual CM_TaskDefinition CM_TaskDefinition { get; set; }
    }

}