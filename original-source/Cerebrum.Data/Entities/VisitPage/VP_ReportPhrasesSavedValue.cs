﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_ReportPhrasesSavedValue
    {
        [Key]
        public int Id { get; set; }

        public int UserID { get; set; }

        public int Value { get; set; }
        
        public int AppointmentId { get; set; }

        public int PatientRecordId { get; set; }

        public DateTime? DateEntered { get; set; }

        public virtual int VPReportPhraseId { get; set; }

        
        public virtual int TopLevelReportPhraseId { get; set; }
    }
}
