﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_CPP_Problem_Status
    {
        [Key]
        public int Id { get; set; }

        [StringLength(128)]
        public string Text { get; set; }
    }
}
