﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Entities.VisitPage;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class PatientBloodPressure
    {
        [Key]
        public int Id { get; set; }
        public int PatientRecordId { get; set; }
        public int AppointmentID { get; set; }
        public int Systolic { get; set; }
        public int Dystolic { get; set; }
        public virtual int VP_AppointmentTestLogId { get; set; }
    }
}
