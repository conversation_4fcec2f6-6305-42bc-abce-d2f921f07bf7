﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_ReportPhrasesSavedText
    {
        [Key]
        public int Id { get; set; }

        [StringLength(8000)]
        public string Value { get; set; } = string.Empty;

        public int AppointmentId { get; set; }

        public int PatientRecordId { get; set; }


        //foreign keys
        public virtual int VP_AppointmentTestLogId { get; set; }

        //public virtual int ReportPhraseID { get; set; }

        //[ForeignKey("VPReportPhrase")]
        public virtual int TopLevelVPReportPhraseID { get; set; }

       // public virtual VPReportPhrase ReportPhrase { get; set; }


    }
}
