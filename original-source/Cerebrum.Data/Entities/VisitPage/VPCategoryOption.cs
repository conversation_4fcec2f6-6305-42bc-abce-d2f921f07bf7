﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VPCategoryOption
    {
        [Key]
        public int Id { get; set; }
        //foriegn keys
        public virtual int VPCategoryId { get; set; }

        public virtual int VPOptionId { get; set; }
    }
}