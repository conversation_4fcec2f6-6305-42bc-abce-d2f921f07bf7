﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("VP_CPP_Problem_List")]
    [TrackChanges]
    public class VP_CPP_Problem_List
    {
        [Key]
        public int Id { get; set; }
        public int ParentId { get; set; }
        public bool IsProblemList { get; set; }
        public int PatientRecordId { get; set; }
        public int AppointmentID { get; set; }
        public bool Visible { get; set; } = true;

        [StringLength(255)]
        public string Life_Stage { get; set; }

        [StringLength(100)]
        public string Units { get; set; }
        [StringLength(20)]
        public string CodingSystem { get; set; }
        [StringLength(20)]
        public string DiagnosticCode { get; set; }
        [StringLength(255)]
        public string DiagnosticDescription { get; set; }

        [StringLength(512)]
        public string Problem_Description { get; set; }
        public int? Problem_Status { get; set; }

        [StringLength(4000)]
        public string Notes { get; set; }
        public int Position { get; set; }

        [StringLength(255)]
        public string Proc_Interv { get; set; }
        public int ProcDate_Day { get; set; }
        public int ProcDate_Month { get; set; }
        public int ProcDate_Year { get; set; }
        public DateTime? ProcDate { get; set; }

        public int Years { get; set; }

        public int Diagnosis_id { get; set; }
        public bool Record_Status { get; set; }
        [StringLength(1000)]
        //CDS Import
        public string ResidualInformation { get; set; }

        public DateTime Submit_Date { get; set; }

        public int DateOfOnset_Day { get; set; }
        public int DateOfOnset_Month { get; set; }
        public int DateOfOnset_Year { get; set; }
        public DateTime? DateOfOnset { get; set; }



        public int ResolutionDate_Day { get; set; }
        public int ResolutionDate_Month { get; set; }
        public int ResolutionDate_Year { get; set; }
        public DateTime? ResolutionDate { get; set; }

        public bool Deleted { get; set; }
        [StringLength(1024)]
        public string Reason_for_Del { get; set; }


        public DateTime? AddDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        public int AddedBy { get; set; }
        public int UpdatedBy { get; set; }
        public virtual List<ProblemListResidualInfo> ProblemListResidualInfos { get; set; } = new List<ProblemListResidualInfo>();
    }
}
