﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("VP_CPP_ImmunizationType")]
    [TrackChanges]
    public class VP_CPP_ImmunizationType
    {
        [Key]
        public int Id { get; set; }

        public int Age { get; set; }
        public int Operator { get; set; }
        public int Gender { get; set; }


        public int AgeFrom { get; set; }
        public int AgeTo { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }


        [StringLength(255)]
        public string Name { get; set; }

        [StringLength(255)]
        public string Code { get; set; }

        [StringLength(255)]
        public string Agecategory { get; set; }

        public int? Period { get; set; }
        public DateTime? Submite_date { get; set; }
        public bool IsImmunization { get; set; } = false;
    }
}
