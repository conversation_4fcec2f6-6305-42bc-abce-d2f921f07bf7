﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_Template
    {
        [Key]
        public int Id  { get; set; }
        public int? PatientRecordId { get; set; }
        [StringLength(512)]
        public string Name { get; set; }

        public int? PracticeID { get; set; }
        public int OrderSequence { get; set; } = 999;
        
        public int? DoctorID { get; set; }
        public bool IsActive { get; set; } = true;
        public virtual List<VP_Template_Detail> VP_Template_Details { get; set; } = new List<VP_Template_Detail>();
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;

    }
}