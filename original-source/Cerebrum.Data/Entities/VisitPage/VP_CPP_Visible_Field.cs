﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_CPP_Visible_Field
    {
        [Key]
        public int Id { get; set; }
        public int PracticeDoctorId { get; set; }
        public int VP_CPP_Category_Id { get; set; }
        public bool Col1Visible { get; set; }
        public bool Col2Visible { get; set; }
        public bool Col3Visible { get; set; }
        public bool Col4Visible { get; set; }
        public bool Col5Visible { get; set; }
        public bool Col6Visible { get; set; }
        public bool Col7Visible { get; set; }
        public bool Col8Visible { get; set; }
        public bool Col9Visible { get; set; }
        public bool Col10Visible { get; set; }
        public bool Col11Visible { get; set; }
        public bool Col12Visible { get; set; }
    }
}
