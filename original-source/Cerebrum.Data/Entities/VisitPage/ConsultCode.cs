﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ConsultCode
    {
        [Key]
        public int Id { get; set; }
        public int OLDID { get; set; }

        [StringLength(100)]
        public string Code { get; set; }
        public int? Spec { get; set; }

        [StringLength(1024)]
        public string Name { get; set; }
        public int? Tech { get; set; }
        public int? Pro { get; set; }
        public int Fee { get; set; }

        [StringLength(1024)]
        public string Note { get; set; }


        [StringLength(50)]
        public string SortOrder { get; set; }

    }
}
