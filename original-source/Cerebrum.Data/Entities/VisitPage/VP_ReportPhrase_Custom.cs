﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_ReportPhrase_Custom
    {
        [Key]
        public int Id { get; set; }

        [StringLength(8000)]
        public string Text { get; set; }
        public bool Visible { get; set; }
        public int Rank { get; set; }
        public bool Accumulative { get; set; }

        public int PatientRecordId { get; set; }
        public int PracticeID { get; set; }
        public int DoctorID { get; set; }
        
        //   public int UserID { get; set; }

        //foriegn keys
        public virtual int VP_ReportPhraseId { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
    }
}
