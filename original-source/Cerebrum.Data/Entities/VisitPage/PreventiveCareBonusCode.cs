﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class PreventiveCareBonusCode
    {
        [Key]
        public int Id { get; set; }

        [StringLength(512)]
        public string Code { get; set; }
        [ForeignKey("PreventiveCareBonusCategory")]
        public virtual int PreventiveCareBonusCategoryId { get; set; }

        public virtual PreventiveCareBonusCategory PreventiveCareBonusCategory { get; set; }

    }
}
