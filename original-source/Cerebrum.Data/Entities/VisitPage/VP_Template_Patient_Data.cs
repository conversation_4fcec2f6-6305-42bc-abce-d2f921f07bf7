﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_Template_Patient_Data
    {
        [Key]
        public int Id { get; set; }
        public int PatientRecordId { get; set; }

        public int LogId { get; set; }

       
        public DateTime? LogDate { get; set; }

        [StringLength(512)]
        public string Value { get; set; }
        public DateTime? DateEntered { get; set; }

        public bool From_VP_Page { get; set; }

        public virtual int VPTemplateFieldId { get; set; }

        public virtual int TemplateId { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;


    }
}
