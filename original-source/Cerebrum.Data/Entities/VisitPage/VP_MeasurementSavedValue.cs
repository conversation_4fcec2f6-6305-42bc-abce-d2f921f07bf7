﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Entities.VisitPage;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_MeasurementSavedValue
    {

        [Key]
        public int Id { get; set; }

        [StringLength(512)]
        public string Value { get; set; } = string.Empty;

        public int AppointmentId { get; set; }

        public int PatientRecordId { get; set; }

        public virtual VPUniqueMeasurement VPUniqueMeasurement { get; set; }
        
 
        //foreign keys
        public int VP_AppointmentTestLogId { get; set; }
       // public virtual VP_AppointmentTestLog VP_AppointmentTestLog { get; set; }

        public int VPUniqueMeasurementId { get; set; }
      
        public VP_MeasurementSavedValue()
        {
 
        }

    }
}
