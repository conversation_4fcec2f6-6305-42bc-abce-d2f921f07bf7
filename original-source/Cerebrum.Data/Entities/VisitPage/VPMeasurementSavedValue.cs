﻿using Cerebrum.Data.Attributes;
using Cerebrum.Data.Entities.VisitPage;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VPMeasurementSavedValue
    {
        public VPMeasurementSavedValue()
        {
            DateCreated = System.DateTime.Now;
        }
        [Key]
        public int Id { get; set; }

        [StringLength(512)]
        public string Value { get; set; }    
        public int VPUniqueMeasurementId { get; set; }

        [ForeignKey("VPUniqueMeasurementId")]
        public VPUniqueMeasurement VPUniqueMeasurement { get; set; }

        public int AppointmentTestSaveLogId { get; set; }

        [ForeignKey("AppointmentTestSaveLogId")]
        public AppointmentTestSaveLog AppointmentTestSaveLog { get; set; }

        public DateTime DateCreated { get; set; }
    }
}
