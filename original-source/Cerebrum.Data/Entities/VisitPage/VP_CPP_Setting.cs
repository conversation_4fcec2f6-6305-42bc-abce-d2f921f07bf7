﻿
using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("VP_CPP_Setting")]
    [TrackChanges]
    public class VP_CPP_Setting
    {
        [Key]
        public int ID { get; set; }
        public int DoctorID { get; set; }
        public int PatientRecordId { get; set; }

        [StringLength(1024)]
        public string Text { get; set; }
        public int Order { get; set; }
        public bool Visible { get; set; }

        public virtual int VP_CPP_Category_Id { get; set; }
    }
}
