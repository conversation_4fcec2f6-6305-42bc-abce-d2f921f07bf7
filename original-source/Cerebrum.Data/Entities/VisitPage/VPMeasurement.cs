﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VPMeasurement
    {
        [Key]
        public int Id { get; set; }

        public int OLDID { get; set; }

        public int OLD_TemplateFieldID { get; set; }

        [StringLength(512)]
        public string Name { get; set; }
        public int Order { get; set; }

        [StringLength(255)]
        public string Units { get; set; }
        [StringLength(255)]
        public string Normal { get; set; }
        public decimal? Range1 { get; set; }
        public decimal? Range2 { get; set; }
        public int Spec { get; set; }
        public int? Type { get; set; }
        public int? DrID { get; set; }
        public int Status { get; set; }

        [StringLength(255)]
        public string Options { get; set; }

        [StringLength(255)]
        public string Testcode { get; set; }
        public int? Cdid { get; set; }

        public decimal? NH { get; set; }
        public decimal? NL { get; set; }
        public decimal? TH { get; set; }
        public decimal? TL { get; set; }
        public int Frequency { get; set; }
        public bool IsCFD { get; set; }

        //foreign keys
        public virtual int VPCategoryID { get; set; }
    }
}