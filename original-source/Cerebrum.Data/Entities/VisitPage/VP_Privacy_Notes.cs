﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_Privacy_Notes
    {

        [Key]
        public int Id { get; set; }

        [StringLength(1024)]
        public string Notes { get; set; }

        public virtual int PatientRecordId { get; set; }
        public virtual int UserId { get; set; }

        public DateTime? DateEntered { get; set; }

    }
}
