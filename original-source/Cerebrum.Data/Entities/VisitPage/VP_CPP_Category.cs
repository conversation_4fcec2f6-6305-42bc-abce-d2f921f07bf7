﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_CPP_Category
    {
        [Key]
        public int Id { get; set; }

        [StringLength(512)]
        public string Text { get; set; }
        public int Group { get; set; } = 0;
    }
}
