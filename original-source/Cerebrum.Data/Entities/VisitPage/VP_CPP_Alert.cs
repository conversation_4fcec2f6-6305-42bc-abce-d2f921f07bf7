﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_CPP_Alert
    {
        [Key]
        public int Id { get; set; }
        public int ParentId { get; set; }
        public int PatientRecordId { get; set; }
        //public virtual PatientRecord PatientRecord { get; set; }
        public int UserId { get; set; }

        [StringLength(512)]
        public string Description { get; set; }

        [StringLength(4000)]
        public string Notes { get; set; }
        public DateTime? EndDate { get; set; }
        public bool Status { get; set; }
        public DateTime eSubmitDate { get; set; }
        public int DateActive_Day { get; set; }
        public int DateActive_Month { get; set; }
        public int DateActive_Year { get; set; }
        public int EndDate_Day { get; set; }
        public int EndDate_Month { get; set; }
        public int EndDate_Year { get; set; }

        public DateTime DateActive  { get; set; }


        public bool Deleted { get; set; }
        public bool Visible { get; set; } = true;
        [StringLength(1024)]
        public string ReasonForDel { get; set; }
        [StringLength(1000)]
        //CDS Import
        public string ResidualInformation { get; set; }

        public DateTime? AddDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        public int AddedBy { get; set; }
        public int UpdatedBy { get; set; }

    }
}
