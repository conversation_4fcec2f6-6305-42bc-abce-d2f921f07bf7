﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_Template_Patient_Detail
    {

        [Key]
        public int Id { get; set; }
        public int PatientRecordId { get; set; }

        [StringLength(512)]
        public string Value { get; set; }

        public decimal? NH { get; set; }
        public decimal? NL { get; set; }
        public decimal? TH { get; set; }
        public decimal? TL { get; set; }
        public int Frequency { get; set; }


        public virtual int VPTemplateField { get; set; }
        public virtual int VP_TemplateId { get; set; }
    }
}
