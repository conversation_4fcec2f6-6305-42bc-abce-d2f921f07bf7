﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VPCategory
    {
        [Key]
        public int id { get; set;}
        public int OldId { get; set; }

        [StringLength(512)]
        public string Name { get; set; }
        public int Order { get; set; }
        public int Dt { get; set; }

        [StringLength(255)]
        public string Options { get; set; }
        public int? DrID { get; set; }
        public int Status { get; set; }
    }
}
