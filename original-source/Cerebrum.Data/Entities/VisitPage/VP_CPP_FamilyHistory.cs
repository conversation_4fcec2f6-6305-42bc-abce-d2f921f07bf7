﻿using System;
using System.Collections.Generic;
using System.Linq;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [Table("VP_CPP_FamilyHistory")]
    [TrackChanges]
    public class VP_CPP_FamilyHistory
    {
        [Key]
        public int Id { get; set; }
        public int ParentId { get; set; }
        public int PatientRecordId { get; set; }
        public int StartDateDay { get; set; }
        public int StartDateMonth { get; set; }
        public int StartDateYear { get; set; }
        public int AgeOnset { get; set; }

        [StringLength(255)]
        public string Units { get; set; }

        public int? Position { get; set; }
        public int? Status { get; set; }

        [StringLength(512)]
        public string ProblemDescription { get; set; }
        [StringLength(512)]
        public string Treatment { get; set; }
        [StringLength(4000)]
        public string Notes { get; set; }

        [StringLength(255)]
        public string YearsDays { get; set; }

        [StringLength(255)]
        public string RelationShip { get; set; }

        [StringLength(255)]
        public string LifeStage { get; set; }

        [StringLength(1000)]
        //CDS Import
        public string ResidualInformation { get; set; }
        public bool Deleted { get; set; }
        public bool Visible { get; set; } = true;
        [StringLength(1024)]
        public string ReasonForDelete { get; set; }
        [StringLength(20)]
        public string CodingSystem { get; set; }
        [StringLength(20)]
        public string DiagnosticCode { get; set; }
        [StringLength(512)]
        public string DiagnosticDescription { get; set; }
        public DateTime? AddDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        public int AddedBy { get; set; }
        public int UpdatedBy { get; set; }

        public virtual List<FamilyHistoryResidualInfo> FamilyHistoryResidualInfos { get; set; } = new List<FamilyHistoryResidualInfo>();
    }
}
