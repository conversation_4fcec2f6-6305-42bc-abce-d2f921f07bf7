﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{

    public enum ImmunizationStatus
    {
        TO_BE_DONE = 1,
        REFUSED = 2,
        COMPLETED = 3,
        INELIGIBLE = 4,
        OVERDUE = 5,
        DELETED = 6,
        TRACKED = 7
    }

    [TrackChanges]
    public class VP_CPP_Immunization
    {
        [Key]
        public int Id { get; set; }
        public int PatientRecordId { get; set; }
        public int PhysicianId { get; set; }

        public bool? ContactedByPhone { get; set; }

        public DateTime? ContactedByPhoneDate { get; set; }

        [StringLength(255)]
        public string AdministeredBy { get; set; }

        [StringLength(255)]
        public string Table_Of_Doctors_Names { get; set; }

        [StringLength(255)]
        public string ImmunizationSystem { get; set; }
        public bool refuse { get; set; }
        public DateTime? ImmunizationDate { get; set; }
        public DateTime? ImmunizationRefusedDate { get; set; }

        public int? ImmunizationDay { get; set; }
        public int? ImmunizationMonth { get; set; }
        public int? ImmunizationYear { get; set; }

        [StringLength(255)]
        public string Name { get; set; }

        [StringLength(4000)]
        public string Notes { get; set; }

        [StringLength(255)]
        public string Manufacturer { get; set; }

        [StringLength(255)]
        public string LotNumber { get; set; }

        [StringLength(255)]
        public string Route { get; set; }

        [StringLength(255)]
        public string Site { get; set; }

        [StringLength(255)]
        public string Dose { get; set; }

        [StringLength(255)]
        public string DoseUnit { get; set; }

        [StringLength(512)]
        public string Instructions { get; set; }
        public DateTime? NextDate { get; set; }
        public bool NotRemind { get; set; }

        [StringLength(512)]
        public string ReasonNextDate { get; set; }

        [StringLength(255)]
        public string CodingVocabulary { get; set; }

        [StringLength(255)]
        public string ImmunizationCode { get; set; } // DIN

        [StringLength(1024)]
        public string ReasonForDel { get; set; }
        public DateTime? SubmitDate { get; set; }
        public bool isactive { get; set; }
        public int Setid { get; set; }
        public bool Colonoscopy { get; set; }
        public int? ParentID { get; set; }
        public bool IsImported { get; set; }

        [StringLength(4000)]
        public string ResidualData { get; set; }

        //foreign keys
        public virtual int VP_CPP_ImmunizationTypeId { get; set; }
        public virtual int VP_CPP_ImmunizationStatusId { get; set; }
    }
}
