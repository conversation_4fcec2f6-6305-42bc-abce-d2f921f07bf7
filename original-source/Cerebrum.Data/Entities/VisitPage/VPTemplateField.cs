﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VPTemplateField
    {
        [Key]
        public int Id { get; set; }
        public int OLDId { get; set; }
        [StringLength(512)]
        public string Name { get; set; }
        public int Order { get; set; }
        public int Grp { get; set; }
        public int Type { get; set; }

        [StringLength(255)]
        public string TestCode { get; set; }
        public decimal? NH { get; set; }
        public decimal? NL { get; set; }
        public decimal? TH { get; set; }
        public decimal? TL { get; set; }
        public int Frequency { get; set; }

        public int? OLDVPMID { get; set; }

        public virtual int? VPMeasurementId { get; set; }
    }
}