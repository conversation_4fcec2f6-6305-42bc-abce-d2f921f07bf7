﻿using System;
using System.ComponentModel.DataAnnotations;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class VPUniqueMeasurement
    {

        [Key]
        public int Id { get; set; }
        public int? VPUniqueMeasurementId { get; set; }
        public virtual VPUniqueMeasurement vpUniqueMeasurement { get; set; }
        public int? CareElementGroupId { get; set; }
        public virtual CareElementGroup careElementGroup { get; set; }
        public int? SimilarToId { get; set; }
        public int OLDID { get; set; }

        public int OLD_TemplateFieldID { get; set; }
        [StringLength(512)]
        public string Name { get; set; }
        [StringLength(512)]
        public string ShortName { get; set; }
        public int Order { get; set; }

        [StringLength(255)]
        public string Units { get; set; }
        [StringLength(255)]
        public string Normal { get; set; }
        public decimal? Range1 { get; set; }
        public decimal? Range2 { get; set; }
        public int Spec { get; set; }
        public int? Type { get; set; }
        public int? DrID { get; set; }
        public int Status { get; set; }

        [StringLength(255)]
        public string Options { get; set; }

        [StringLength(255)]
        public string Testcode { get; set; }
        public int? Cdid { get; set; }

        public decimal? NH { get; set; }
        public decimal? NL { get; set; }
        public decimal? TH { get; set; }
        public decimal? TL { get; set; }
        [StringLength(1)]
        public string Gender { get; set; }
        public int Frequency { get; set; }

        public bool IsText { get; set; }
        public AwareMD.Cerebrum.Shared.Enums.ValueType? ValueType { get; set; } = AwareMD.Cerebrum.Shared.Enums.ValueType.Text;
        [StringLength(512)]
        public string DataMigrationSchemaRoot { get; set; }
        [StringLength(512)]
        public string DataMigrationSchemaValue { get; set; }
        [StringLength(512)]
        public string DataMigrationSchemaValueOf { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
    }
}
