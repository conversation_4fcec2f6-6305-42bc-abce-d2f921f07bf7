﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class VPOptionByPatient
    {

        [Key]
        public int ID { get; set; }

        public int OptionID { get; set; }

        public int PatientRecordId { get; set; }
    }
}
