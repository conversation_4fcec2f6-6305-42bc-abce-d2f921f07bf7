﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities
{
    [TrackChanges]
    public class CareElementGroup
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public string GroupName { get; set; }
        [StringLength(100)]
        public string DataMigrationSchemaRoot { get; set; }
        public bool IsDataMigrationGroupChild { get; set; } = false;
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; }
    }
}
