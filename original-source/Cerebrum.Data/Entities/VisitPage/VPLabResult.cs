﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class VPLabResult
    {

        public VPLabResult()
        {
            DateCreated = System.DateTime.Now;
        }

        [Key]
        public int Id { get; set; }
        public int AppointmentTestSaveLogId { get; set; }

        [ForeignKey("AppointmentTestSaveLogId")]
        public AppointmentTestSaveLog AppointmentTestSaveLog { get; set; }

        public int? HL7ReportId { get; set; }

        [ForeignKey("HL7ReportId")]
        public HL7Report HL7Report { get; set; }
        public DateTime? LabResultDate { get; set; }
        public DateTime DateCreated { get; set; }
    }
}
