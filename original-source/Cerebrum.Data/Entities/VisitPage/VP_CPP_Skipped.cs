﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class VP_CPP_Skipped
    {
        [Key]
        public int ID { get; set; }
        public int UserID { get; set; }
        public int PatientRecordId { get; set; }
        public bool Skip { get; set; }
        public int CPP_Category_ID { get; set; }
    }
}
