﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class AppointmentBill
    {
        [Key]
        public int Id { get; set; }
        public int AppointmentID { get; set; }

        public int ConsultCode { get; set; }

        public int ConsultCode2 { get; set; }

        public int ConsultCode3 { get; set; }

        public int DiagnosticCode { get; set; }

        public int DiagnosticCode2 { get; set; }

        public int DiagnosticCode3 { get; set; }

        public int ImmunizationCode { get; set; }

        public int? billStatusId { get; set; } = null;
        [ForeignKey("billStatusId")]
        public virtual BillStatus billStatus { get; set; }
    }
}
