﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_CPP_ImmunizationStatus
    {
        [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string Status { get; set; }

        [StringLength(50)]
        public string Color { get; set; }
    }
}
