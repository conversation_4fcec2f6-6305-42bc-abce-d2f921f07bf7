﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class PreventiveCareBonusCategory
    {
        [Key]
        public int Id { get; set; }

        [StringLength(512)]
        public string Name { get; set; }
    }
}
