﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VPReportPhrase
    {
        [Key]
        //[DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }

        public int Index { get; set; }
        public int OldId { get; set; }

        [StringLength(1024)]
        public string Name { get; set; }

        [StringLength(8000)]
        public string Value { get; set; }
        public int Parent { get; set; }
        public int Root { get; set; }
        public int Order { get; set; }
        public int? Type { get; set; }

        [StringLength(255)]
        public string Options { get; set; }
        public int Spec { get; set; }
        public int? DrID { get; set; }
        public int Status { get; set; }

        [StringLength(255)]
        public string Grp { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
    }
}