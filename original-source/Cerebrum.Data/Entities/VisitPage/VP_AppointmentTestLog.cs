﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_AppointmentTestLog
    {
        [Key]
        public int Id { get; set; }
        public DateTime? Date { get; set; }
        public int Status { get; set; }
        public int AppointmentId { get; set; }
        public int PatientRecordId { get; set; }

        [StringLength(100)]
        public string IP { get; set; }
       
        public int UserId { get; set; }
        // 0-saved, 1- Finalized
        public int Finalized { get; set; }
        public VP_AppointmentTestLog()
        {

        }

    }
}
