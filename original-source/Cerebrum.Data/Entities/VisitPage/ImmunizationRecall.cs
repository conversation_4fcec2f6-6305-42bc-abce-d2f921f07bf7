﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ImmunizationRecall
    {
        [Key]
        public int ID { get; set; }

        public bool Active { get; set; }
        public DateTime? DateCreated { get; set; }// serviced Date
        
        public int? DateServicedDay { get; set; }        
        public int? DateServicedMonth { get; set; }        
        public int? DateServicedYear { get; set; }
        public virtual int VP_CPP_Immunization_ID { get; set; }
        public virtual int VP_CPP_ImmunizationStatusId { get; set; }
    }
}
