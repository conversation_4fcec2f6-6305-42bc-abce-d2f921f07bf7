﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;


using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ICD10
    {
        [Key]
        public int ID { get; set; }
        [StringLength(50)]
        public string Code { get; set; }

        [StringLength(1024)]
        public string Name { get; set; }

        [StringLength(1024)]
        public string Description { get; set; }
    }
}