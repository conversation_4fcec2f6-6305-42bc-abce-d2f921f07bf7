﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VPReportPhraseByDoctor
    {
        [Key]
        public int Id { get; set; }
        
        public int DrID { get; set; }

        [StringLength(8000)]
        public string Text { get; set; }

        //foreign keys
        public virtual int VPReportPhraseID { get; set; }

    }
}
