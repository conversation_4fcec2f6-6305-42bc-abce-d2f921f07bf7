﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VP_SendReport
    {
        [Key]
        public int Id { get; set; }

        [StringLength(255)]
        public string Location { get; set; }

        [StringLength(255)]
        public string URL { get; set; }
        public DateTime DateEntered { get; set; }
        public bool Sent { get; set; }
        public bool Amended { get; set; }

        public int AppointmentId { get; set; }
        public int PatientRecordId { get; set; }

        public virtual int SendTypeId { get; set; }

        [StringLength(100)]
        public string Email { get; set; }
        [StringLength(100)]
        public string Fax { get; set; }
        [StringLength(100)]
        public string DocName { get; set; }
        [StringLength(100)]
        public string MessageId { get; set; } = string.Empty;
        [StringLength(1000)]
        public string ErrorMessage { get; set; }
    }
}