﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class RecallLog
    {
        [Key]
        public int Id { get; set; }

        public DateTime? Date { get; set; }

        [StringLength(3000)]
        public string Notes { get; set; }

        public virtual int ImmunizationRecallID { get; set; }

        public virtual int PatientRecordId { get; set; }


    }
}
