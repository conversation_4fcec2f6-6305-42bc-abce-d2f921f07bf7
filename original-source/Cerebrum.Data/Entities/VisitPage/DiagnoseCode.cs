﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class DiagnoseCode
    {
        [Key]
        public int Id { get; set; }
        public int OLDID { get; set; }
        [StringLength(512)]
        public string Code { get; set; }

        [StringLength(1024)]
        public string Diagnosis { get; set; }
        public int Type { get; set; }
        public int SpecialityID { get; set; }

    }
}
