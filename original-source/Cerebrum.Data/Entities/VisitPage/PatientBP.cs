﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("PatientBP")]
    public class PatientBP // patient blood pressure
    {
        public PatientBP()
        {
            DateCreated = System.DateTime.Now;
        }

        [Key]
        public int Id { get; set; }        
        public int Systolic { get; set; }
        public int Dystolic { get; set; }       
        public int AppointmentTestSaveLogId { get; set; }
        [ForeignKey("AppointmentTestSaveLogId")]
        public AppointmentTestSaveLog AppointmentTestSaveLog { get; set; }

        public DateTime DateCreated { get; set; }
    }
}
