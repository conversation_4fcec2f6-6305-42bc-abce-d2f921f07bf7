﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data.Entities.VisitPage
{
    [TrackChanges]
    public class VP_Measurements_Patient
    {
        [Key]
        public int ID { get; set; }
        public int DocID { get; set; }
        public int PatientRecordId { get; set; }
        public int MeasurementID { get; set; }
        public bool Visible { get; set; }
        public int Rank { get; set; }
    }
}


        