﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("VP_CPP_RiskFactor")]
    [TrackChanges]
    public class VP_CPP_RiskFactor
    {
        [Key]
        public int id { get; set; }
        public int ParentId { get; set; }
        public int PatientRecordId { get; set; }

        [StringLength(512)]
        public string RiskFactor { get; set; }

        [StringLength(512)]
        public string ExposureDetails { get; set; }
        [StringLength(1000)]
        //CDS Import
        public string ResidualInformation { get; set; }
        public int OnsetAge { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        [StringLength(512)]
        public string LifeStage { get; set; }

        [StringLength(4000)]
        public string Notes { get; set; }
        public int Position { get; set; }
        public bool Status { get; set; }

        public DateTime SubmitDate { get; set; }
        public int StartDateDay { get; set; }
        public int StartDateMonth { get; set; }
        public int StartDateYear { get; set; }
        public int EndDateDay { get; set; }
        public int EndDateMonth { get; set; }
        public int EndDateYear { get; set; }

        [StringLength(100)]
        public string Unit { get; set; }

        public bool Deleted { get; set; }
        public bool Visible { get; set; } = true;
        [StringLength(1024)]
        public string ReasonForDel { get; set; }

        public DateTime? AddDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        public int AddedBy { get; set; }
        public int UpdatedBy { get; set; }
    }
}
