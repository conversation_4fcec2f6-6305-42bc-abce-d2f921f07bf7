﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Requisition Time
    /// </summary>
    [Table("RequisitionTemplate")]
    public class RequisitionTemplate
    {
        [Key]
        public int id { get; set; }
        public int practiceDoctorId { get; set; }
        public int practiceId { get; set; }
        [StringLength(50)]
        public string name { get; set; }
        public int requisitionTypeId { get; set; }
        [StringLength(2000)]
        public string requisitionItems { get; set; }
        public bool active { get; set; }

        [ForeignKey("practiceId")]
        public virtual Practice practice { get; set; }
        [ForeignKey("requisitionTypeId")]
        public virtual RequisitionType requisitionType { get; set; }
    }
}
