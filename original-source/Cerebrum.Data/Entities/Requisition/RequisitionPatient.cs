﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// Requisition Patient 
    /// </summary>
    [Table("RequisitionPatient")]
    [TrackChanges]
    public class RequisitionPatient
    {
        [Key]
        public int id { get; set; }
        public int PatientRecordId { get; set; }
        public int practiceDoctorId { get; set; }
        public DateTime requisitionTime { get; set; }
        public int nextAppointmentDoctor { get; set; }
        public int nextAppointmentOffice { get; set; }
        public int byAppointmentId { get; set; }    //doctor makes order after seeing this appointment
        [StringLength(1024)]
        public string comment { get; set; }

        [ForeignKey("PatientRecordId")]
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
