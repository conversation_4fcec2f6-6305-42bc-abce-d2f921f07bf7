﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Requisition Time
    /// </summary>
    [Table("RequisitionTime")]
    public class RequisitionTime
    {
        [Key]
        public int id { get; set; }
        [StringLength(50)]
        public string name { get; set; }
        public int? days { get; set; }
        public int orderNumber { get; set; }
    }
}
