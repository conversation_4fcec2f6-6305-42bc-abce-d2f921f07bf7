﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Requisition Item
    /// </summary>
    public class RequisitionItem
    {
        [Key]
        public int id { get; set; }
        public int requisitionItemId { get; set; }
        public int requisitionTypeId { get; set; }
        [StringLength(50)]
        public string name { get; set; }
        [StringLength(50)]
        public string shortName { get; set; }
        public int orderNumber { get; set; }

        [ForeignKey("requisitionTypeId")]
        public virtual RequisitionType requisitionType { get; set; }
    }
}
