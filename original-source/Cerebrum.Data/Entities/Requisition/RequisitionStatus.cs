﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// This class should really just be an enum, but isn't due to being legacy code.
    /// </summary>
    [Table("RequisitionStatus")]
    public class RequisitionStatus
    {
        public const string ORDERED = "Ordered";
        public const string REQUEST_SENT = "Request Sent";
        public const string ARRANGED = "Arranged";
        public const string FULFILLED = "Fulfilled";
        public const string NOT_FULFILLED = "Not Fulfilled";

        [Key]
        public int id { get; set; }

        [StringLength(32)]
        public string color { get; set; }

        [StringLength(128)]
        public string text { get; set; }

        public int SortOrder { get; set; }
    }
}
