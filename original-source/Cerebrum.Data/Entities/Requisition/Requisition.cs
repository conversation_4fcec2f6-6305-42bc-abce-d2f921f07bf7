﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// Requisition
    /// </summary>
    [Table("Requisition")]
    [TrackChanges]
    public class Requisition
    {
        [Key]
        public int id { get; set; }
        public bool isActive { get; set; } = true;
        public bool isActiveInSearch { get; set; } = true;
        public int requisitionPatientId { get; set; }
        public int requisitionTypeId { get; set; }
        public DateTime? testTime { get; set; }
        public DateTime? resultTime { get; set; }
        public int requisitionStatus { get; set; }

        [StringLength(32)]
        public string internalTestStatus { get; set; }
        public int? officeId { get; set; }

        [Column(TypeName = "nvarchar(MAX)")]
        [MaxLength]
        public string requisitionItems { get; set; }

        [StringLength(3000)]
        public string Comment { get; set; }

        [StringLength(256)]
        public string fileName { get; set; }

        [StringLength(50)]
        public string template { get; set; }

        [ForeignKey("requisitionPatientId")]
        public virtual RequisitionPatient requisitionPatient { get; set; }

        [ForeignKey("requisitionTypeId")]
        public virtual RequisitionType requisitionType { get; set; }

        [ForeignKey("requisitionStatus")]
        public virtual RequisitionStatus status { get; set; }
    }
}
