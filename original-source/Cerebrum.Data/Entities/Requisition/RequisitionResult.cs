﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.Data
{
    /// <summary>
    /// Requisition Result 
    /// </summary>
    [Table("RequisitionResult")]
    [TrackChanges]
    public class RequisitionResult
    {
        [Key]
        public int id { get; set; }
        public int requisitionId { get; set; }
        public ReportType reportType { get; set; }
        public int reportId { get; set; }
        [StringLength(128)]
        public string ip { get; set; }
        [StringLength(128)]
        public string description { get; set; }
        [StringLength(128)]
        public string userId { get; set; }

        [ForeignKey("requisitionId")]
        public virtual Requisition requisition { get; set; }
    }
}
