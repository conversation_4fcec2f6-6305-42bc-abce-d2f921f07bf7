﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class OfficeFaxSetting
    {
        public int Id { get; set; }
        public int OfficeId { get; set; }
        public string FaxAPIUrl { get; set; }
        public string Title { get; set; }
        public string Name { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Company { get; set; }
        public string Country { get; set; }
        public string Email { get; set; }
        public string FaxNumber { get; set; }
        public string HomePhone { get; set; }
        public string OfficeLocation { get; set; }
        public string OfficePhone { get; set; }
        public string StreetAddress { get; set; }
        public string ZipCode { get; set; }
        public string DocumenttName { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
    }
}
