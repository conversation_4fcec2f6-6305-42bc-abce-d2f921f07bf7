﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class Modality
    {
        [Key]
        public int Id { get; set; }
        [StringLength(10)]
        public string modalityName { get; set; }
        [StringLength(100)]
        public string description { get; set; }
        public DateTime createdDateTime { get; set; } = DateTime.Now;
    }
}
