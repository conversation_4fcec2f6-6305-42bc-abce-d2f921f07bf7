﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
   
 

    /// <summary>
    /// Per Office
    /// </summary>
    [TrackChanges]
    public class Schedule 
    {
        [Key]
        public int Id { get; set; }
        public int OfficeId { get; set; }
        public int maxNumOfDays { get; set; } = 14;
        public int timeSlotDuration { get; set; }
        public DateTime startDate { get; set; }
        public virtual List<ScheduleDay> ScheduleDays { get; set; } = new List<ScheduleDay>();
    }
    #region No Use
    
    public class ScheduleDay
    {
        [Key]
        public int Id { get; set; }
        public DateTime? date { get; set; }
        public DayOfWeek dayOfWeek { get; set; }
        [StringLength(100)]
        public string startTime { get; set; }
        [StringLength(100)]
        public string finishTime { get; set; }
        [StringLength(100)]
        public string reservedTime { get; set; }
        [StringLength(500)]
        public string comment { get; set; }
        public DateTime createdDate { get; set; }
        //public virtual List<ScheduleDayResource> ScheduleDayResources { get; set; } = new List<ScheduleDayResource>();
        public int ScheduleId { get; set; }
        public virtual Schedule Schedule { get; set; }
    }
   
    #endregion
}
namespace Cerebrum.Data
{
    
    
    public class ScheduleTimeSlot
    {
        [Key]
        public int Id { get; set; }
        public string description { get; set; }
        public string startTime { get; set; }//7:30
        public string endTime { get; set; }//7:45
        public int duration { get; set; }// duration should not exceed ScheduleUserDay's TotalTime
        public int ScheduleUserDayId { get; set; }
        public virtual ScheduleUserDay ScheduleUserDay { get; set; }
    }

}
