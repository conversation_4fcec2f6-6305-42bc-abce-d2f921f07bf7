﻿using Cerebrum.Data.Attributes;
using Cerebrum.Data.Entities.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class Test
    {
        [Key]
        public int Id { get; set; }
        public int OLDID { get; set; }
        [StringLength(40)]
        public string color { get; set; }
        [StringLength(500)]
        public string testFullName { get; set; }
        [StringLength(500)]
        public string testShortName { get; set; }
        public int order { get; set; }
        public int duration { get; set; }
        public bool IsRadiology { get; set; }
        public bool RequireDevice { get; set; }
        public int? DeviceTypeId { get; set; }
        public int? DeviceDuration { get; set; }
        [ForeignKey("DeviceTypeId")]
        public StoreInventoryType DeviceType { get; set; }
        //public Modalities modality { get; set; }
        public DateTime startTime { get; set; } //test start time for scheduler. TODO: extract time only
        public bool isActive { get; set; } = true;
        public DateTime createdDateTime { get; set; }
        public DateTime updatedDateTime { get; set; } = DateTime.Now;
        public virtual List<TestModality> Modalities { get; set; } = new List<TestModality>();
        public virtual List<TestResource> TestResources { get; set; } = new List<TestResource>();
        public virtual List<TestGroup> TestGroups { get; set; } = new List<TestGroup>();
        [StringLength(10)]
        public string HrmTypeShort { get; set; }
        [StringLength(50)]
        public string HrmTypeLong { get; set; }
        [StringLength(50)]
        public string HrmModality { get; set; }
        [StringLength(30)]
        public string LoincCode { get; set; }
    }

    [TrackChanges]
    public class TestModality
    {
        [Key]
        public int Id { get; set; }
        public int ModalityId { get; set; }
        public virtual Modality Modality { get; set; }
        public int TestId { get; set; }
        public virtual Test Test { get; set; }
        public DateTime createdDateTime { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }
}


