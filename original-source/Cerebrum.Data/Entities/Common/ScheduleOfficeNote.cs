﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ScheduleOfficeNote
    {
        public int Id { get; set; }
        public int OfficeId { get; set; }
        public DateTime ScheduleDate { get; set; }
        [StringLength(150)]
        public string Note { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTIme { get; set; } = DateTime.Now;
    }
}
