﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace Cerebrum.Data
{
    public class InsuranceCompany
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public string name { get; set; }
        [StringLength(25)]
        public string phoneNumber { get; set; }
        [StringLength(10)]
        public string extention { get; set; }
        [StringLength(25)]
        public string faxNumber { get; set; }
        [StringLength(100)]
        public string address { get; set; }
        public int MasterId { get; set; }
        public virtual Master Master { get; set; }

    }
}
