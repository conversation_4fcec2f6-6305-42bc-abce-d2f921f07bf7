﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class DoctorComment
    {
        public DoctorComment()
        {
            DateCreated = System.DateTime.Now;
            IsActive = true;
        }
        public int Id { get; set; }

        [StringLength(3000)]
        public string Comment { get; set; }

        public int PatientRecordId { get; set; }
        public int? UserId { get; set; }
        public int? PracticeDoctorId { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public bool IsActive { get; set; }
    }
}
