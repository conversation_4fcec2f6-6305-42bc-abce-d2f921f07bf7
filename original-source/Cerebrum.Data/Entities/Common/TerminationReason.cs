﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities
{
    public class EnrolmentTerminationReason
    {
        [Key]
        public int Code { get; set; }
        [StringLength(50)]
        public string Reason { get; set; }
        [StringLength(150)]
        public string Explanation { get; set; }
        [StringLength(100)]
        public string SuggestedAction { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;

    }
}
