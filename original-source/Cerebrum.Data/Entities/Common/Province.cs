﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class CountryProvince
    {
        public int Id { get; set; }
        public int? CountryProvinceId { get; set; }
        [StringLength(100)]
        public string name { get; set; }
        public virtual CountryProvince countryProvince { get; set; }
        public virtual List<CountryProvince> children { get; set; }
    }
}
