﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class ScheduleStaffNote
    {
        public int Id { get; set; }        
        [Index("IX_ScheduleStaffNote", 1, IsUnique = true)]
        public int OfficeId { get; set; }
        [Index("IX_ScheduleStaffNote", 2, IsUnique = true)]
        public int UserId { get; set; } // staff             
        [StringLength(300)]
        public string Notes { get; set; }
        [Index("IX_ScheduleStaffNote", 3, IsUnique = true)]
        public DateTime Date { get; set; } // the date that the note should show up
        [ForeignKey("OfficeRoom")]
        public int? OfficeRoomId { get; set; } // for kiosk
        public virtual OfficeRoom OfficeRoom { get; set; }
        public DateTime? DateCreated { get; set; }
        public DateTime? LastModified { get; set; }
        public int LastModifiedByUserId { get; set; }
        public bool IsActive { get; set; }
    }
}
