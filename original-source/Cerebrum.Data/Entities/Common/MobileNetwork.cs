﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// List of Mobile networks
    /// </summary>
    [TrackChanges]
    public class MobileNetwork
    {
        [Key]
        public int Id { get; set; }


        [StringLength(512)]
        public string Name { get; set; }


        [StringLength(512)]
        public string Notes { get; set; }

    }
    /// <summary>
    /// User Mobile Network - holds mobile network provider of the user
    /// </summary>
    [TrackChanges]
    public class UserMobileNetwork
    {
        [Key]
        public int Id { get; set; }


        [StringLength(128)]
        public string ApplicationUserId { get; set; }
        public int MobileNetworkId { get; set; }
    }
}
