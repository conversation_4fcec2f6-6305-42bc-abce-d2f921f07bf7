﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("LoginHistory")]
    public class LoginHistory
    {
        public LoginHistory()
        {
            DateCreated = System.DateTime.Now;
        }

        [Key]
        public int Id { get; set; }
        [StringLength(500)]
        public string UserName { get; set; }
        [StringLength(1500)]
        public string UserAgent { get; set; }
        [StringLength(100)]
        public string Browser { get; set; }
        [StringLength(100)]
        public string BrowserVersion { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }        
        [StringLength(30)]
        public string Status { get; set; }
        [StringLength(30)]
        public string ServerPort { get; set; }
        public DateTime DateCreated { get; set; }
    }
}
