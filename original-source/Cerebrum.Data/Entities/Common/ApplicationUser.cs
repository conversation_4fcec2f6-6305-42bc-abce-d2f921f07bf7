﻿using System;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ApplicationUser : IdentityUser
    {
        /// <summary>
        /// Add custom Cerebrum 3 user security claims here
        /// </summary>
        /// <param name="manager"></param>
        /// <returns></returns>
        public async Task<ClaimsIdentity> GenerateUserIdentityAsync(UserManager<ApplicationUser> manager)
        {
            // Note the authenticationType must match the one defined in CookieAuthenticationOptions.AuthenticationType
            var userIdentity = await manager.CreateIdentityAsync(this, DefaultAuthenticationTypes.ApplicationCookie);
            return userIdentity;
        }
        [Required]
        public int PracticeID { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int UserID { get; set; }

        public Salutation Salut { get; set; } = Salutation.Mr;
        public UserStatus Status { get; set; } = UserStatus.Active;
        [StringLength(50)]
        [Display(Name = "First Name")]        
        public string FirstName { get; set; }

        [StringLength(50)]
        [Display(Name = "Middle Name")]
        public string MiddleName { get; set; }

        [StringLength(50)]
        [Display(Name = "Last Name")]
        public string LastName { get; set; }

        [StringLength(150)]
        public string Address { get; set; }
        [StringLength(100)]
        public string City { get; set; }
        [StringLength(100)]
        public string Province { get; set; }
        [StringLength(10)]
        // Use a sensible display name for views:
        [Display(Name = "Postal Code")]
        public string PostalCode { get; set; }

        [StringLength(25)]
        [Display(Name = "Cell Phone")]
        public string CellPhone { get; set; }
        [Display(Name = "Country")]
        public Country UserCountry { get; set; } = Country.Canada;
        [Required]
        [Display(Name = "Cerebrum User Type")]
        public UserTypeEnum CerebrumUserType { get; set; } = UserTypeEnum.Doctor;
        [Display(Name = "User Login Persistance")]
        public LoginPersistance UserLoginPersistance { get; set; } = LoginPersistance.FourHours;
        public bool ContactManagerRecipient { get; set; }
        public bool? EveryWhere { get; set; }

        [DefaultValue(false)]
        public bool PasswordChangeRequired { get; set; } = false;
        public virtual List<AspNetUserPasswordHistorty> PasswordHistory { get; set; }
        [StringLength(15)]
        public override string PhoneNumber { get; set; }
        [StringLength(2000)]
        public override string PasswordHash{ get; set; }
        [StringLength(4000)]
        public override string SecurityStamp { get; set; }
        public int MaxFailedAccessAttemptsBeforeLockout { get; set; } = 0;
        [StringLength(500)]
        public string SecretKey { get; set; }
        public int UserContextMode { get; set; }
        [StringLength(250)]
        public string OBO { get; set; }
        [StringLength(250)]
        public string rid { get; set; }
        [StringLength(250)]
        public string NameQualifier { get; set; }
        [StringLength(500)]
        public string IdentityProvider { get; set; }
        [StringLength(500)]
        public string UserContextID { get; set; }
        public bool DisclosureAgreementStatus { get; set; }
        public bool ConnectingGTASTSPOSTDisabled { get; set; }

        [StringLength(256)]
        public string OneIdContactEmail { get; set; }

        [StringLength(256)]
        public string OneIdSubjectNameId { get; set; }

        [StringLength(20)]
        public string OtnPractitionerId { get; set; }
        public ApplicationLanguage AppLanguage { get; set; } = ApplicationLanguage.English;
        [StringLength(100)]
        public string IdpId { get; set; }
        [StringLength(256)]
        public string IdpRandomPassword { get; set; }
        /// <summary>
        /// Display Address
        /// Concatenate the address info for display in tables and such:
        /// </summary>
        public string DisplayAddress
        {
            get
            {
                string dspAddress = string.IsNullOrWhiteSpace(this.Address) ? "" : this.Address;
                string dspCity = string.IsNullOrWhiteSpace(this.City) ? "" : this.City;
                string dspProvince = string.IsNullOrWhiteSpace(this.Province) ? "" : this.Province;
                string dspPostalCode = string.IsNullOrWhiteSpace(this.PostalCode) ? "" : this.PostalCode;
                //return string.Format("{0} {1} {2} {3}", dspAddress, dspCity, dspProvince, dspPostalCode);
                return $"{dspAddress} {dspCity} {dspProvince} {dspPostalCode}";
            }
        }
    }

    //public class UserWithPermissions : ApplicationUser
    //{
    //    public string[] Permissions { get; set; }
    //}
    [TrackChanges]
    public class ApplicationRole : IdentityRole
    {
        public ApplicationRole(string name) : base(name) { }
        [StringLength(250)]
        public string Description { get; set; }
        public virtual List<RolePermission> RolePermissions { get; set; }
        public ApplicationRole() : base()
        {
            RolePermissions = new List<RolePermission>();
        }
        [Required]
        public int PracticeId { get; set; } = 0;
    }
}
