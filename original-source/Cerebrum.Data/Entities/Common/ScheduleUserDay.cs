﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class ScheduleUserDay
    {
        [Key]
        public int Id { get; set; }
        public Guid UserId { get; set; }
        public int PracticeId { get; set; }
        public int officeId { get; set; }
        public DayOfWeek dayOfWeek { get; set; }
        public string startTime { get; set; }//7:30
        public string finishTime { get; set; }//3:30
        public int TotalTime { get; set; }
        public string comment { get; set; }
        public List<ScheduleTimeSlot> ScheduleTimeSlots { get; set; } = new List<ScheduleTimeSlot>();
    }
}
