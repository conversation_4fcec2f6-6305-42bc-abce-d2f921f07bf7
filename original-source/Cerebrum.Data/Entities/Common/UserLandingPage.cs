﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class UserLandingPage
    {
        [Key]
        public int Id { get; set; }      
        
        public int LandingPageId { get; set; }

        [ForeignKey("LandingPageId")]
        public LandingPage LandingPage { get; set; }

        [Index("IX_UserLandingPage", 1, IsUnique = true)]
        public int UserId { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }
    }
}
