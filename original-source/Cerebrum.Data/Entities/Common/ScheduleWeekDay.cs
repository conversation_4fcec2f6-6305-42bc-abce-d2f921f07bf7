﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ScheduleWeekDay
    {
        [Key]
        public int Id { get; set; }
        public int officeId { get; set; }
        public DateTime? date { get; set; }
        public DayOfWeek dayOfWeek { get; set; } = DayOfWeek.Sunday;
        [StringLength(100)]
        public string startTime { get; set; }
        [StringLength(100)]
        public string finishTime { get; set; }
        [StringLength(500)]
        public string absentTime { get; set; }
        [StringLength(500)]
        public string reservedTime { get; set; }
        [StringLength(500)]
        public string comment { get; set; }
        public bool dayOff { get; set; } = false;
        public DateTime createdDate { get; set; } = DateTime.Now;
        public DateTime updatedDateTime { get; set; } = DateTime.Now;
        public int ScheduleUserId { get; set; }
        public virtual ScheduleUser ScheduleUser { get; set; }
    }
}
