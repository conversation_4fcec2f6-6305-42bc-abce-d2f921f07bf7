﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class PracticeCommRecipient
    {

        [Key]
        public int Id { get; set; }

        [Required]
        //[Index("IX_PracticeCommTypeRecipient", 1, IsUnique = true)]
        public int PracticeCommTypeId { get; set; }

        //[Index("IX_PracticeCommTypeRecipient",2, IsUnique = true)]
        [StringLength(100)]
        public string RecipientEmail { get; set; }

        [ForeignKey("PracticeCommTypeId")]
        public PracticeCommType PracticeCommType { get; set; }
    }
}
