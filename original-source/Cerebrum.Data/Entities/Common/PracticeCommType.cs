﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    //practice communication type
    public class PracticeCommType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        //[Index("IX_PracticeExternalCommType", 1, IsUnique = true)]
        public int PracticeId { get; set; }

        [Required]
        //[Index("IX_PracticeExternalCommType", 2, IsUnique = true)]
        public int ExternalCommTypeId { get; set; }        

        public int? PollingInterval { get; set; }

        [StringLength(300)]
        public string Url { get; set; }

        [StringLength(500)]
        public string Key { get; set; }

        [StringLength(500)]
        public string Password { get; set; }
        public int? Port { get; set; }

        [StringLength(500)]
        public string User { get; set; }

        [StringLength(500)]
        public string FtpFolder { get; set; }
        public bool Alert { get; set; }

        [StringLength(500)]
        public string DecryptionCertificateFile { get; set; }

        public DateTime? LastRunDate { get; set; }
        
        [StringLength(500)]
        public string ConfidentialityStatement { get; set; }

        [ForeignKey("ExternalCommTypeId")]
        public ExternalCommType ExteralCommType { get; set; }

        public List<PracticeCommRecipient> PracticeCommRecipients { get; set; }
    }
}
