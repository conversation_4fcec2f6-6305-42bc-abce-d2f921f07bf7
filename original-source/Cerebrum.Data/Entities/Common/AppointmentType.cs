﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("AppointmentType")]
    public class AppointmentType
    {
        [Key]
        public int Id { get; set; }
        public int? AppointmentTypeId { get; set; }
        [StringLength(100)]
        public string name { get; set; }
        public bool VPRequired { get; set; } = true;
        public TestDuration duration { get; set; } = TestDuration.T15;
        public virtual AppointmentType appointmentType { get; set; }
        public virtual List<AppointmentType> children { get; set; }
    }
}

