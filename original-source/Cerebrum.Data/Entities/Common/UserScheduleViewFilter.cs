﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class UserScheduleViewFilter
    {
        [Key]
        public int Id { get; set; }

        [Index("IX_UserSceduleViewFilter", 1, IsUnique = true)]
        public int OfficeId { get; set; }
        [ForeignKey("OfficeId")]
        public Office Office { get; set; }

        [Index("IX_UserSceduleViewFilter", 2, IsUnique = true)] 
        public int UserId { get; set; } // main user

        [Index("IX_UserSceduleViewFilter", 3, IsUnique = true)]
        public int UserFilterId { get; set; } // selected users that the main users wants to see

        public bool IsActive { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }
    }
}
