﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    /// <summary>
    /// Test requres multiple different resources to be able to do a test
    /// </summary>
    [TrackChanges]
    public class TestResource
    {
        [Key]
        public int Id { get; set; }
        public int permissionId { get; set; }
        public virtual Permission permission { get; set; }
        public bool nurseRequired { get; set; } = false;
        public bool doctorRequiredInOffice { get; set; } = false;
        public bool isPerformedInGroup { get; set; } = false;
        public int TestResourceTypeId { get; set; }
        public virtual TestResourceType testResourceType { get; set; }
        public bool AssignedAppointmentDoctor { get; set; } = false;
        public bool IsActive { get; set; } = true;
        //public int? RoomId { get; set; } //if not null get it from OfficeRoom table
        public int TestId { get; set; }
        public virtual Test Test { get; set; }
    }
}
