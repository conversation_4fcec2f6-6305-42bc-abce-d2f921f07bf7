﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class TestGroup
    {
        [Key]
        public int Id { get; set; }
        public int TestId { get; set; }
        public virtual Test Test { get; set; }
        public int GroupId { get; set; }
        public virtual Group Group { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
    }
   
}
