﻿using AwareMD.Cerebrum.Shared.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    public class Group
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }
        [StringLength(255)]
        public string Name { get; set; }

        [StringLength(300)]
        public string Description { get; set; }

        public int? SSRSReportId { get; set; }
        [ForeignKey("SSRSReportId")]
        public SSRSReport SSRSReport { get; set; }
        public BullsEyeSvgNumber BullsEyeSvgNumber { get; set; }
        public TestGroupLayoutType TestGroupLayoutType { get; set; }
    }

}
