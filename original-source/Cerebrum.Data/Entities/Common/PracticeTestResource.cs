﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class PracticeTestResource
    {
        public PracticeTestResource()
        {
            DateCreated = System.DateTime.Now;

        }
        [Key]
        public int Id { get; set; }        
       
        public int PracticeId { get; set; }

        [ForeignKey("PracticeId")]
        public Practice Practice { get; set; }
        public int permissionId { get; set; }
        public virtual Permission permission { get; set; }
        public bool nurseRequired { get; set; } = false;
        public bool doctorRequiredInOffice { get; set; } = false;
        public bool isPerformedInGroup { get; set; } = false;
        public int TestResourceTypeId { get; set; }
        public virtual TestResourceType testResourceType { get; set; }
        public bool AssignedAppointmentDoctor { get; set; } = false;      
        public int? OfficeRoomId { get; set; } //if not null get it from OfficeRoom table

        [ForeignKey("OfficeRoomId")]
        public OfficeRoom OfficeRoom { get; set; }
        public int TestId { get; set; }

        public bool IsActive { get; set; }
        public virtual Test Test { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }

        public int LastModifiedByUser { get; set; }

        


    }
}
