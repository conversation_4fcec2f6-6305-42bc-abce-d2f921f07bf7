﻿using Cerebrum.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    public class StoreInventory
    {
        public StoreInventory()
        {
            DateCreated = System.DateTime.Now;
        }
        [Key]
        public int id { get; set; }
        [StringLength(10)]
        public string code { get; set; } // Serial number
        public int officeId { get; set; }        
        [ForeignKey("officeId")]
        public Office Office { get; set; }
        public int inventoryTypeId { get; set; }
        [ForeignKey("inventoryTypeId")]
        public StoreInventoryType StoreInventoryType { get; set; }
        public int statusId { get; set; }
        [ForeignKey("statusId")]
        public StoreInventoryStatus Status { get; set; }        
        public int practiceId { get; set; }        
        public int userId { get; set; }
        [StringLength(1000)]
        public string notes { get; set; }
        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }

        public bool IsActive { get; set; }
    }

    public class StoreInventoryType
    {
        [Key]
        public int id { get; set; }
        [StringLength(1000)]
        public string name { get; set; }
    }

    public class StoreInventoryStatus
    {
        [Key]
        public int id { get; set; }
        [StringLength(100)]
        public string name { get; set; }
    }
}