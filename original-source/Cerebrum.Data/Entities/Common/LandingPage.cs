﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class LandingPage
    {
        [Key]
        public int Id { get; set; }

        [Index("IX_LandingPageCode", 1, IsUnique = true)]
        public int Code { get; set; }

        [StringLength(50)]
        public string Description { get; set; }

        [StringLength(50)]
        public string Area { get; set; }

        [StringLength(50)]
        public string Controller { get; set; }

        [StringLength(50)]
        public string Action { get; set; }

        [StringLength(200)]
        public string URL { get; set; }        
        public int? DisplayOrder { get; set; }
        public bool UseUrl { get; set; }
        public bool IsActive { get; set; }
    }
}
