﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    /// <summary>
    /// Resource Type Name: Doctor,Technician, Room, etc
    /// </summary>
    [TrackChanges]
    public class TestResourceType
    {
        [Key]
        public int Id { get; set; }
        [StringLength(100)]
        public string TestResourceName { get; set; }
        public virtual List<TestResource> TestResouces { get; set; } = new List<TestResource>();
    }
}
