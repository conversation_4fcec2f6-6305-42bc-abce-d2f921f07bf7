﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ScheduleUser
    {
        [Key]
        public int Id { get; set; }
        public int UserId { get; set; }
        public int PracticeId { get; set; }
        [StringLength(200)]
        public string permission { get; set; }
        public DateTime startDate { get; set; } = DateTime.Now;
        public DateTime endDate { get; set; } = DateTime.Now;
        public DateTime createdDate { get; set; } = DateTime.Now;
        public DateTime updatedDate { get; set; } = DateTime.Now;
        public virtual List<ScheduleWeekDay> ScheduleWeekDays { get; set; } = new List<ScheduleWeekDay>();
    }
}
