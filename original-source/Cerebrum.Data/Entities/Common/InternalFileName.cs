﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class InternalFileName
    {
        public int Id { get; set; }
        [StringLength(500)]
        public string OriginalFileName { get; set; }
        public int? UserId { get; set; }
        public int? PatientRecordId { get; set; }
        public DateTime DateCreated { get; set; }
        
    }
}
