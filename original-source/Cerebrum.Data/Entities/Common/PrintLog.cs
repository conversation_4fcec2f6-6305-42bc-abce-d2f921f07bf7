﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
   public class PrintLog
    {
        public int Id { get; set; }
        public DateTime EventAt { get; set; } = DateTime.Now;
        [StringLength(15)]
        public string IpAddress { get; set; }
        [StringLength(100)]
        public string userName { get; set; }
        [StringLength(1000)]
        public string Page { get; set; }
        [StringLength(150)]
        public string Message { get; set; }
        public int? PatientRecordId { get; set; }

    }
}
