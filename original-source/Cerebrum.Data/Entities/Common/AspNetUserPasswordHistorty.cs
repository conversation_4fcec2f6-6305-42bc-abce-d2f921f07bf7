﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
   public  class AspNetUserPasswordHistorty
    {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public  int  ID { get; set; }
        public int UserId { get; set; }
        [StringLength(2000)]
        public string PasswordHash { get; set; }
        public DateTime  CreatedDatetime { get; set; }
        [StringLength(15)]
        public string    IPAddress { get; set; }
        [StringLength(100)]
        public string ComputerName { get; set; }
 
       // public virtual  ApplicationUser ApplicationUser { get; set; }

    }



}
