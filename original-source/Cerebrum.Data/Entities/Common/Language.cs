﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities
{
    public class SpokenLanguage
    {
        public int Id { get; set; }
        [StringLength(10)]
        public string LanguageCode { get; set; }
        [StringLength(100)]
        public string LanguageName { get; set; }
        [StringLength(10)]
        public string CountryCode { get; set; }
        [StringLength(100)]
        public string Country { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;

    }
}
