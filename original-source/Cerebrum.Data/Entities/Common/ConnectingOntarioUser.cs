﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    public class ConnectingOntarioUser
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int UserId { get; set; }
        [StringLength(500)]
        public string SecretKey { get; set; }
        public int UserContextMode { get; set; }
        [StringLength(250)]
        public string NameId { get; set; }
        [StringLength(250)]
        public string OBO { get; set; }
        [StringLength(250)]
        public string rid { get; set; }
        [StringLength(250)]
        public string NameQualifier { get; set; }
        [StringLength(500)]
        public string IdentityProvider { get; set; }
        [StringLength(500)]
        public string UserContextID { get; set; }
        [StringLength(500)]
        public string UserEmail { get; set; }
        public bool DisclosureAgreementStatus { get; set; }
    }
}
