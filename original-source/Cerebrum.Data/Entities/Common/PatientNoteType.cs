﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class PatientNoteType
    {
        public PatientNoteType()
        {
            DateCreated = System.DateTime.Now;
        }
        [Key]
        public int Id { get; set; }

        [StringLength(200)]
        public string NoteType { get; set; }

        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? LastModifiedByUserId { get; set; }
    }
}
