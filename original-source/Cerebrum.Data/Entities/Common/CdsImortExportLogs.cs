﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    public class ImportEventLog
    {
        [Key]
        public int id { get; set; }
        public int PatientRecordId { get; set; }
        public int FamHist { get; set; }
        public int PastHealth { get; set; }
        public int ProblemList { get; set; }
        public int RiskFactor { get; set; }
        public int Allergy { get; set; }
        public int Medication { get; set; }
        public int Immunization { get; set; }
        public int Labs { get; set; }
        public int App { get; set; }
        public int MyClinicalNotes { get; set; }
        public int ReportsTxt { get; set; }
        public int ReportsImg { get; set; }
        public int CareElements { get; set; }
        public int Alerts { get; set; }
        public int PersonalHistory { get; set; }
        [StringLength(700)]
        public string fileName { get; set; }
        public bool success { get; set; }
        

        [StringLength(4000)]
        public string result { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public virtual ImportEventRecord ImportEventRecord { get; set; }
    }
    public class ImportEventRecord
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int? PracticeDoctorId { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public List<ImportEventLog> ImportEventLogs { get; set; } = new List<ImportEventLog>();
    }
    public class ExportEventLog
    {
        [Key]
        public int id { get; set; }
        public int userId { get; set; }


        [StringLength(512)]
        public string ReqId { get; set; }
        public int PatientRecordId { get; set; }
        public int practiceDoctorId { get; set; }
        public int FamHist { get; set; }
        public int PastHealth { get; set; }
        public int ProblemList { get; set; }
        public int RiskFactor { get; set; }
        public int Allergy { get; set; }
        public int Medication { get; set; }
        public int Immunization { get; set; }
        public int Labs { get; set; }
        public int App { get; set; }
        public int MyClinicalNotes { get; set; }
        public int ReportsTxt { get; set; }
        public int ReportsImg { get; set; }
        public int CareElements { get; set; }
        public int Alerts { get; set; }
        [StringLength(700)]
        public string PhGroup { get; set; }
        public bool success { get; set; }
        [StringLength(700)]
        public string version { get; set; }
    }

    public class EventLog_Transaction
    {
        [Key]
        public int id { get; set; }
        [StringLength(700)]
        public string fileName { get; set; }
        public int PatientRecordId { get; set; }

        [StringLength(512)]
        public string tableName { get; set; }
        public int destPrimKey { get; set; }

        [StringLength(512)]
        public string transName { get; set; }
        public DateTime date { get; set; }
    }
}