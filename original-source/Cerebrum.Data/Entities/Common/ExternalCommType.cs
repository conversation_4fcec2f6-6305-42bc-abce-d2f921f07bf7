﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    //communication type
    public class ExternalCommType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        //[Index("IX_ExternalCommTypeCode", 1, IsUnique = true)]
        public int Code { get; set; } // 1001 = hrm , 1002 = olis, 

        [Required]
        [StringLength(50)]
        public string Name { get; set; }

        public int? PollingInterval { get; set; }

        [StringLength(300)]
        public string Url { get; set; }

        [StringLength(500)]
        public string Key { get; set; }

        [StringLength(500)]
        public string Password { get; set; }

        public int? Port { get; set; }

        [StringLength(500)]
        public string User { get; set; }

        [StringLength(500)]
        public string SSHCertificateFile { get; set; }               

        public DateTime? LastRunDate { get; set; }

        [StringLength(500)]
        public string Recipient { get; set; }

        public List<PracticeCommType> PracticeCommTypes { get; set; }
    }
}
