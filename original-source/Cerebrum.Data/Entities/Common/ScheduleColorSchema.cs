﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace Cerebrum.Data
{
    public class ScheduleColorSchema
    {
        [Key]
        public int Id { get; set; }
        [StringLength(10)]
        public string colorHex { get; set; }
        [StringLength(100)]
        public string forPermissions { get; set; }
        [StringLength(100)]
        public string forAppointmentTypes { get; set; }
    }
}
