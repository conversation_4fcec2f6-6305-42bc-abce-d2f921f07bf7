﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class AppointmentTypeC2toC3
    {
        [Key]
        public int id { get; set; }
        [Required]
        public int AppointmentTypeID_C2 { get; set; }
        [Required]
        [StringLength(25)]
        public string AppointmentTypeName_C2 { get; set; }
        [Required]
        public int Duration_C2 { get; set; }
        [Required]
        public int AppointmentTypeID_C3 { get; set; }
        [StringLength(20)]
        public string datasource { get; set; }

    }
}
