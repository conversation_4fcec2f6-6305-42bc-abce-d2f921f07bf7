﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]

    public class PatientAuthorization
    {
        public PatientAuthorization()
        {
            DateCreated = System.DateTime.Now;            
        }
        public int Id { get; set; }
        [Index("IX_PatientUserAuth", 1, IsUnique = true)]
        public int UserId { get; set; }

        [Index("IX_PatientUserAuth", 2, IsUnique = true)]
        public int PatientRecordId { get; set; }
        [Required]
        [StringLength(1)]
        public string PermissionStatus { get; set; } // A = means allow all access except those in this list, D = means deny all access except those in this list

        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }

        public DateTime? LastModified { get; set; }
    }
}
