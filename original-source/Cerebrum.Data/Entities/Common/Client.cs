﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    public class Client
    {
        public int Id { get; set; }
        [StringLength(4000)]
        public string ClientKey { get; set; }
        [StringLength(4000)]
        public string Secret { get; set; }
        [StringLength(100)]
        public string Name { get; set; }
        public bool Active { get; set; }
    }
}
