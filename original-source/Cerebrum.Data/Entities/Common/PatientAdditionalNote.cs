﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.Common
{
    public class PatientAdditionalNote
    {
        public PatientAdditionalNote()
        {
            DateCreated = System.DateTime.Now;
        }
        public int Id { get; set; }
        public int PatientNoteTypeId { get; set; }
        [ForeignKey("PatientNoteTypeId")]
        public PatientNoteType PatientNoteType { get; set; }
        public int PatientRecordId { get; set; }
        [ForeignKey("PatientRecordId")]
        public PatientRecord PatientRecord { get; set; }
        public DateTime? NoteDate { get; set; }
        public string Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? LastModifiedByUserId { get; set; }
    }
}
