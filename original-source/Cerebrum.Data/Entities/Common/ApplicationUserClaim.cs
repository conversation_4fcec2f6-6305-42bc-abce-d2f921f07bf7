﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    public class ApplicationUserClaim:IdentityUserClaim
    {
        [StringLength(50)]
        public override string ClaimType { get; set; }
        [StringLength(200)]
        public override string ClaimValue { get; set; }

    }
}
