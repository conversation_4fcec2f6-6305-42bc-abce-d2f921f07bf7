﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.Common
{
    public class C_Query
    {
        public int Id { get; set; }
        [StringLength(500)]
        public string name { get; set; }
        public string query { get; set; }
        [StringLength(5000)]
        public string maindivdata { get; set; }
        public string ptn_ids { get; set; }
        public DateTime? date { get; set; } = DateTime.Now;
        [StringLength(2500)]
        public string message_ { get; set; }
        public string ptn_ids_checkrd { get; set; }
        public int active { get; set; } = 0;
    }
}
