﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("ZsCoreMethod")]
    public class ZsCoreMethod
    {
        public int Id { get; set; }
        [StringLength(10)]
        public string Name { get; set; }
        [StringLength(1000)]
        public string Formula { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime DateModified { get; set; }
    }
}
