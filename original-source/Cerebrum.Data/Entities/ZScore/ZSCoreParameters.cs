﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("ZSCoreParameters")]
    public class ZSCoreParameters
    {
        public int Id { get; set; }
        [ForeignKey("ZsCoreConst")]
        public int ZScoreConstantId { get; set; }
        public virtual ZsCoreConst ZsCoreConst { get; set; }
        public int ZScoreParameterNameId { get; set; }
        public virtual ZScoreParameterName ZScoreParameterName { get; set; }
        public float Value { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime DateModified { get; set; }
    }
}
