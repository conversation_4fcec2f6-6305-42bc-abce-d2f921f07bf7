﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{

    /// <summary>
    /// Table for categories like admin, doctor, etc
    /// Add new type category as needed to this table
    /// </summary>
    [Table("PermissionTypes")]
    public class PermissionType
    {
        [Key]
        public int Id { get; set; }
        [StringLength(500)]
        public string Name { get; set; }
        public virtual List<Permission> Permissions { get; set; }
        public PermissionType()
        {
            this.Permissions = new List<Permission>();
        }
        public int PermissionsBaseId { get; set; }
        public virtual PermissionsBase PermissionsBase { get; set; }
    }
}


