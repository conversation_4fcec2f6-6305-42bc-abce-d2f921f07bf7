﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Permission table contains subcategories which belongs to admin, doctor etc.
    /// </summary>
    //[Table("Permissions")]
    [TrackChanges]
    public class Permission
    {
        [Key]
        public int Id { get; set; }
        [StringLength(500)]
        public string Name { get; set; }
        public CRType crtype { get; set; } = CRType.Functionality;
        public PermissionLevel PermissionLevel { get; set; } = PermissionLevel.User;
        public int PermissionTypeId { get; set; }
        public virtual PermissionType PermissionType { get; set; }
    }

    [Table("RolePermissions")]
    [TrackChanges]
    public class RolePermission
    {
        [Key]
        public int Id { get; set; }
        public int PermissionId { get; set; }
        [StringLength(50)]
        public string PermissionName { get; set; }
        public int PermissionTypeId { get; set; }
        //Fileds for data only:
        public FilterType filterType { get; set; } = FilterType.Role;
        public CRType criticalResType { get; set; } = CRType.Functionality;
        [StringLength(100)]
        public string OwnerID { get; set; }

        public int PracticeID { get; set; }
        [StringLength(100)]
        public string RoleID { get; set; }
        //Audit data entries
        /// <summary>
        /// user added CR
        /// </summary>
        [StringLength(100)]
        public string UserID { get; set; }
        [StringLength(100)]
        public string UserName { get; set; }
        /// <summary>
        /// When CR addded
        /// </summary>
        public DateTime Date { get; set; } = DateTime.Now;
        /// <summary>
        /// CR addded from
        /// </summary>
        [StringLength(100)]
        public string IPaddress { get; set; }
        public string ApplicationRoleId { get; set; }
       // public virtual ApplicationRole ApplicationRole { get; set; }
        public bool selected { get; set; }
    }
    public enum FilterType { Practice, Role, User };
    public enum CRType { Data,Functionality};

}


