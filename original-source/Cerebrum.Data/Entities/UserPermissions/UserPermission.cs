﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Permission table contains subcategories which belongs to Application User
    /// </summary>
    public class UserPermission
    {
        public int Id { get; set; }
        [StringLength(128)]
        public string ApplicationUserId { get; set; }
        public string PermissionType { get; set; }
        public string Permission { get; set; }
        /// <summary>
        /// By default 'Apply' is true - should apply along with roles permissions to which user belongs to.
        /// If 'Apply' is false - to be excluded from the permissions set
        /// </summary>
        public bool Apply { get; set; } = true; 
    }
}


