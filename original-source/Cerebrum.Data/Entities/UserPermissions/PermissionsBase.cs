﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Root domain permissions table
    /// Add new type of permissions as needed to this table
    /// </summary>
    ////[Table("PermissionsBase")]
    public class PermissionsBase
    {
        [Key]
        public int Id { get; set; }
        [StringLength(500)]
        public string MasterName { get; set; }
        public virtual List<PermissionType> PermissionTypes { get; set; }
        public PermissionsBase()
        {
            this.PermissionTypes = new List<PermissionType>();
        }
        public int MasterId { get; set; }
        public virtual Master Master { get; set; }
    }
}

