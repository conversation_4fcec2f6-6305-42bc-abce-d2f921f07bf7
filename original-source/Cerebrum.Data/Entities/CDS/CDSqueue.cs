﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("CDSqueue")]
    [TrackChanges]
    public class CDSqueue
    {
        [Key]
        public int id { get; set; }
        public int PracticeId { get; set; }
        //public int CreatedByUserId { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; }
        public bool Processed { get; set; } = false;

        public int PracticelDoctorId { get; set; }
        public int Categories { get; set; }
        public bool NormalAbnormalFlag { get; set; } = false;
        public bool Purpose { get; set; } = false;
        public bool PersonStatusCode { get; set; } = false;
        public bool Address { get; set; } = false;
        public bool LaboratoryResultsReferenceRange { get; set; } = false;
        public bool ReportsSourceAuthorPhysician { get; set; } = false;
        public bool YNIndicator { get; set; } = false;
        [StringLength(50)]
        public string PhGroup { get; set; }
        [StringLength(20)]
        public string CerebrumVersion { get; set; }
        [StringLength(20)]
        public string Version { get; set; }
        public bool IsPerDoctor { get; set; } = false;
        public int PatientRecordId { get; set; }
        //[StringLength(50)]
        //public string GrpText { get; set; }
        public int UserId { get; set; }
        public bool IsEncrypting { get; set; } = false;
        [StringLength(256)]
        public string EncryptKey { get; set; }
        [StringLength(256)]
        public string DocText { get; set; }
        public bool CreateFinalReadMe { get; set; } = false;
        public int OfficeId { get; set; }
        public bool FilesImported { get; set; } = false;
        public DateTime? FilesImportedDateTime { get; set; }
        [StringLength(512)]
        public string CdsServerPath { get; set; }
        public int LastPatientRecordId { get; set; } = 0;
        public bool Suspended { get; set; } = false;
        public bool Enforced { get; set; } = false;


    }
}
