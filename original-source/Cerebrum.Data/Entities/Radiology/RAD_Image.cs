﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Radiology
{
    //[Table("RAD_Image")]
    public class RAD_Image
    {
        public int id { get; set; }
        public int SeriesId { get; set; }
        [StringLength(255)]
        public string ImageName { get; set; }
        [StringLength(255)]
        public string ImageUID { get; set; }
        public Nullable<int> ImageNumber { get; set; }
        [StringLength(10)]
        public string Modality { get; set; }
        public DateTime? AddDate { get; set; }
        [StringLength(5)]
        public string image_extension { get; set; }
        public Nullable<long> ImageUIDHash { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
    }
}