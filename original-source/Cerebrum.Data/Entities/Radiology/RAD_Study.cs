﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Radiology
{
    //[Table("RAD_Study")]
    [TrackChanges]
    public class RAD_Study
    {
        [Key]
        public int id { get; set; }
        [StringLength(250)]
        public string StudyUID { get; set; }
        public DateTime? StudyDate { get; set; }
        [StringLength(250)]
        public string AccessionNum { get; set; }
        [StringLength(250)]
        public string StudyDescription { get; set; }
        public Nullable<int> StudyStatus { get; set; }
        [StringLength(50)]
        public string Modality { get; set; }
        public Nullable<int> PatientId { get; set; }
        public Nullable<int> RefPhysicianId { get; set; }
        [StringLength(50)]
        public string StudyId { get; set; }
        public Nullable<int> Institution { get; set; }
        public DateTime? DateAdded { get; set; }
        public Nullable<int> ImageCount { get; set; }
        public Nullable<int> SRCount { get; set; }

        public Nullable<long> StudyUIDHash { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
    }
}
