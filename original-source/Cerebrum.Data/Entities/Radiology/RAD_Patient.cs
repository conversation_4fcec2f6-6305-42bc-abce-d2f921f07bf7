﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Radiology
{
    //[Table("RAD_Patient")]
    public class RAD_Patient
    {
        public int id { get; set; }
        [StringLength(50)]
        public string PatientLastName { get; set; }
        [StringLength(50)]
        public string PatientFirstName { get; set; }
        [StringLength(50)]
        public string PatientId { get; set; }
        public DateTime? PatientBD { get; set; }
        [StringLength(1)]
        public string PatientSex { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
    }
}