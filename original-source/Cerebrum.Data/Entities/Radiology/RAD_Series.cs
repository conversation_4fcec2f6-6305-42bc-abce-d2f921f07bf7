﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Radiology
{
    //[Table("RAD_Series")]
    public class RAD_Series
    {
        public int id { get; set; }
        [StringLength(250)]
        public string SeriesUID { get; set; }
        public int StudyId { get; set; }
        [StringLength(50)]
        public string SeriesNum { get; set; }
        public Nullable<long> SeriesUIDHash { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
    }
}
