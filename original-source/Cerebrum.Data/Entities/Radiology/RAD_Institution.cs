﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Radiology
{
    //[Table("RAD_Institution")]
    public class RAD_Institution
    {
        public int id { get; set; }
        [StringLength(50)]
        public string IName { get; set; }
        public Nullable<int> UID { get; set; }
        public int? PracticeId { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
    }
}
