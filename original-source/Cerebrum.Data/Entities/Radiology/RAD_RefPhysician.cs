﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Radiology
{
    //[Table("RAD_RefPhysician")]
    public class RAD_RefPhysician
    {
        public int id { get; set; }
        [StringLength(50)]
        public string LastName { get; set; }
        [StringLength(50)]
        public string FirstName { get; set; }
        [StringLength(50)]
        public string EMail { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
    }
}
