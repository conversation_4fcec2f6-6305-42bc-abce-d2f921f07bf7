﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class TAPPPatient
    {
        [Key]
        public int Id { get; set; }
        public int PracticeId { get; set; }
        public int PracticeDoctorId { get; set; }
        public int PatientRecordId { get; set; }
        [StringLength(1)]
        public string Gender { get; set; } = string.Empty;
        public int? Age { get; set; }
        public DateTime? LastAppointmentDate { get; set; }
        public DateTime? NextAppointmentDate { get; set; }
        public float? LDL { get; set; }
        public bool? ACE { get; set; } = false;
        public bool? ARB { get; set; } = false;
        public bool? ASA { get; set; } = false;
        public bool? BETABLOCKERS { get; set; } = false;
        public bool? STATIN { get; set; } = false;
        public bool? Ezetimibe { get; set; } = false;
        public bool? PCSK9i { get; set; } = false;
        public bool? CCB { get; set; } = false;
        public bool? SGLT2i { get; set; } = false;
        public bool? IsLastDx410 { get; set; } = false;       
        [ForeignKey("PracticeDoctorId")]
        public virtual PracticeDoctor PracticeDoctor { get; set; }
        [ForeignKey("PatientRecordId")]
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
