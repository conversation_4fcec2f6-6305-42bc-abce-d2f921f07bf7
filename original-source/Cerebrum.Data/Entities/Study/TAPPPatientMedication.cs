﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class TAPPPatientMedication
    {
        [Key]
        public int Id { get; set; }
        public int PracticeDoctorId { get; set; }
        public int PatientRecordId { get; set; }
        public bool? IsDin { get; set; }
        public bool? IsVPRootCategory { get; set; }

        [StringLength(50)]
        public string Class { get; set; } = string.Empty;
        [StringLength(300)]
        public string MedicationName { get; set; } = string.Empty;
        [ForeignKey("PracticeDoctorId")]
        public virtual PracticeDoctor PracticeDoctor { get; set; }
        [ForeignKey("PatientRecordId")]
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
