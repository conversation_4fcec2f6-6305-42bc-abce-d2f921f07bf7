﻿using System.ComponentModel.DataAnnotations;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class Study
    {
        [Key]
        public int Id { get; set; }
        [StringLength(60)]
        public string InternalName { get; set; } = string.Empty;
        [StringLength(60)]
        public string DisplayName { get; set; } = string.Empty;
        [StringLength(100)]
        public string Description { get; set; } = string.Empty;
    }
}
