﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class HFiDOCPatient
    {
        [Key]
        public int Id { get; set; }
        public int PracticeDoctorId { get; set; }
        public int PatientRecordId { get; set; }
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;
        [StringLength(100)]
        public string MiddleName { get; set; } = string.Empty;
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;
        [StringLength(1)]
        public string Gender { get; set; } = string.Empty;
        public int? Age { get; set; }
        public DateTime? LastAppointmentDate { get; set; }
        public DateTime? NextAppointmentDate { get; set; }
        [StringLength(300)]
        public string DiagnoseCodes { get; set; } = string.Empty;
        public int? HL7ReportId { get; set; }
        [StringLength(50)]
        public string PotassiumValue { get; set; } = string.Empty;
        public DateTime? PotassiumDate { get; set; }
        [StringLength(50)]
        public string CreatinineValue { get; set; } = string.Empty;
        public DateTime? CreatinineDate { get; set; }
        [StringLength(50)]
        public string eGRFValue { get; set; } = string.Empty;
        public DateTime? eGRFDate { get; set; }
        [StringLength(50)]
        public string BloodPressureValue { get; set; } = string.Empty;
        public DateTime? BloodPressureDate { get; set; }
        [StringLength(50)]
        public string HeartRateValue { get; set; } = string.Empty;
        public DateTime? HeartRateDate { get; set; }
        [StringLength(50)]
        public string EFValue { get; set; } = string.Empty;
        [ForeignKey("PracticeDoctorId")]
        public virtual PracticeDoctor PracticeDoctor { get; set; }
        [ForeignKey("PatientRecordId")]
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
