﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class DoctorStudy
    {
        [Key]
        public int Id { get; set; }
        public int PracticeDoctorId { get; set; }
        public int StudyId { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime DateSigned { get; set; } = DateTime.Now;

        [ForeignKey("PracticeDoctorId")]
        public virtual PracticeDoctor PracticeDoctor { get; set; }
        [ForeignKey("StudyId")]
        public virtual Study Study { get; set; }
    }
}
