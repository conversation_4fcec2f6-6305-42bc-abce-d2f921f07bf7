﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class PracticeRootCategoryTemplate
    {
        public PracticeRootCategoryTemplate()
        {
            var currentDate = System.DateTime.Now;
            DateCreated = currentDate;
            DateLastModified = currentDate;           
            IsActive = true;
        }
        public int Id { get; set; }

        [Index("IX_PracticeRootCategoryTemplate", 1, IsUnique = true)]
        public int TemplateId { get; set; }

        [ForeignKey("TemplateId")]
        public RootTemplate RootTemplate { get; set; }

        [Index("IX_PracticeRootCategoryTemplate", 2, IsUnique = true)]
        public int PracticeId { get; set; }
        [ForeignKey("PracticeId")]
        public Practice Practice { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? LastModifiedByUserId { get; set; }
        public bool IsActive { get; set; }
    }
}
