﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class DoctorRootCategoryTemplate
    {

        public DoctorRootCategoryTemplate()
        {
            var currentDate = System.DateTime.Now;
            DateCreated = currentDate;
            DateLastModified = currentDate;
            IsActive = true;
            IsDefault = false;
        }
        [Key]
        public int Id { get; set; }
        [Index("IX_DoctorRootCategoryTemplate", 1, IsUnique = true)]
        public int ExternalDoctorId { get; set; }
        [ForeignKey("ExternalDoctorId")]
        public ExternalDoctor ExternalDoctor { get; set; }

        [Index("IX_DoctorRootCategoryTemplate", 2, IsUnique = true)]
        public int PracRootCategoryTempId { get; set; } // practice root category template id        

        [ForeignKey("PracRootCategoryTempId")]
        public PracticeRootCategoryTemplate PracticeRootCategoryTemplate { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? CreatedByUserId { get; set; }
        public int? LastModifiedByUserId { get; set; }
        public bool IsDefault { get; set; }
        public bool IsActive { get; set; }
    }
}
