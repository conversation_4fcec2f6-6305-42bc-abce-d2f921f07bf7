﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class tempVPLog
    {
        public tempVPLog()
        {
            DateCreated = System.DateTime.Now;
        }

        [Key]
        public int Id { get; set; }
        public int VP_AppointmentTestLogId { get; set; }
        public int AppointmentTestSaveLogId { get; set; }

        [ForeignKey("AppointmentTestSaveLogId")]
        public AppointmentTestSaveLog AppointmentTestSaveLog { get; set; }

        [ForeignKey("VP_AppointmentTestLogId")]
        public VP_AppointmentTestLog VP_AppointmentTestLog { get; set; }

        public DateTime DateCreated { get; set; }
    }
}
