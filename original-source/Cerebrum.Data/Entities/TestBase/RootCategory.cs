﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class RootCategory
    {

        public RootCategory()
        {
            var currentDate = System.DateTime.Now;
            DateCreated = currentDate;
            DateLastModified = currentDate;
            IsActive = true;
        }

        [Key]        
        public int Id { get; set; }
        [Required]
        [StringLength(300)]
        [Index("IX_RootCategoryNameGroup", 1, IsUnique = true)]
        public string CategoryName { get; set; }

        [Index("IX_RootCategoryNameGroup", 2, IsUnique = true)]
        public int GroupId { get; set; }
        [ForeignKey("GroupId")]
        public Group Group { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? CreatedByUserId { get; set; }
        public int? LastModifiedByUserId { get; set; }
        public bool IsActive { get; set; }
    }
}
