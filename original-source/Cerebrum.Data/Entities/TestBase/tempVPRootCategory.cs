﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class tempVPRootCategory
    {
        public tempVPRootCategory()
        {
            DateCreated = System.DateTime.Now;
        }
        [Key]
        public int Id { get; set; }
        public int VPReportPhraseId { get; set; }
        public int RootCategoryId { get; set; }

        public string CategoryNameNew { get; set; }
        public string CategoryNameOld { get; set; }
        

        [ForeignKey("RootCategoryId")]
        public RootCategory RootCategory { get; set; }

        [ForeignKey("VPReportPhraseId")]
        public VPReportPhrase VPReportPhrase { get; set; }

        public DateTime DateCreated { get; set; }
    }
}
