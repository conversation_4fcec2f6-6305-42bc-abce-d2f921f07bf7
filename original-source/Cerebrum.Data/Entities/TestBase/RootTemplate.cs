﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class RootTemplate
    {
        public RootTemplate()
        {
            DateCreated = System.DateTime.Now;
        }
        public int Id { get; set; }
        [StringLength(50)]
        public string TemplateName { get; set; }

        public int GroupId { get; set; }

        [ForeignKey("GroupId")]
        public Group Group { get; set; }
        public bool IsSystem { get; set; }// system template
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? CreatedByUserId { get; set; }
        public bool IsActive { get; set; }
    }
}
