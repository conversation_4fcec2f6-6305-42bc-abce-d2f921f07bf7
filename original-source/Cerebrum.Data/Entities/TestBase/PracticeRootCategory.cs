﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class PracticeRootCategory
    {
        public PracticeRootCategory()
        {
            var currentDate = System.DateTime.Now;
            DateCreated = currentDate;
            DateLastModified = currentDate;           
            IsActive = true;
        }

        [Key]
        public int Id { get; set; }
        
        [Index("IX_PracticeRootCategory", 1, IsUnique = true)]
        public int RootCategoryId { get; set; }

        [ForeignKey("RootCategoryId")]
        public RootCategory RootCategory { get; set; }

        [Index("IX_PracticeRootCategory", 2, IsUnique = true)]
        public int PracRootCategoryTempId { get; set; }
        [ForeignKey("PracRootCategoryTempId")]
        public PracticeRootCategoryTemplate PracticeRootCategoryTemplate { get; set; }       
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? CreatedByUserId { get; set; }
        public int? LastModifiedByUserId { get; set; }
        public bool IsActive { get; set; }
    }
}
