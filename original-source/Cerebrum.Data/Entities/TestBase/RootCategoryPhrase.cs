﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class RootCategoryPhrase
    {
        public RootCategoryPhrase()
        {
            var currentDate = System.DateTime.Now;
            DateCreated = currentDate;
            DateLastModified = currentDate;
            IsActive = true;
            IsSubCategory = false;
        }
        [Key]
        public int Id { get; set; }
       
        public int RootCategoryId { get; set; }
        [ForeignKey("RootCategoryId")]
        public RootCategory RootCategory { get; set; }
        [Required]
        [StringLength(300)]        
        public string PhraseName { get; set; }
        [StringLength(8000)]
        public string PhraseValue { get; set; }
        public int? ParentId { get; set; } // sub categories
        [ForeignKey("ParentId")]
        public RootCategoryPhrase RootPhrase { get; set; }
        public bool IsSubCategory { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? CreatedByUserId { get; set; }
        public int? LastModifiedByUserId { get; set; }
        public bool IsActive { get; set; }
    }
}
