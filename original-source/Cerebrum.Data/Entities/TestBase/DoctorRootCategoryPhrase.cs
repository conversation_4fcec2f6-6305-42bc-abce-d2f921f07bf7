﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class DoctorRootCategoryPhrase
    {
        public DoctorRootCategoryPhrase()
        {
            var currentDate = System.DateTime.Now;
            DateCreated = currentDate;
            DateLastModified = currentDate;
            IsActive = true;
        }
        [Key]
        public int Id { get; set; }

        [Index("IX_DoctorRootCategoryPhrase", 1, IsUnique = true)]
        public int PracRootCategoryTempId { get; set; }

        [ForeignKey("PracRootCategoryTempId")]
        public PracticeRootCategoryTemplate PracticeRootCategoryTemplate { get; set; }

        [Index("IX_DoctorRootCategoryPhrase", 2, IsUnique = true)]
        public int RootCategoryPhraseId { get; set; }
        [ForeignKey("RootCategoryPhraseId")]
        public RootCategoryPhrase RootCategoryPhrase { get; set; }

        [Index("IX_DoctorRootCategoryPhrase", 3, IsUnique = true)]
        public int ExternalDoctorId { get; set; }

        [ForeignKey("ExternalDoctorId")]
        public ExternalDoctor ExternalDoctor { get; set; }       

        [Required]
        [StringLength(300)]
        public string PhraseName { get; set; } // custom name 

        [StringLength(8000)]
        public string PhraseValue { get; set; } // custom value 
        public int? DisplayOrder { get; set; } // order of which to display            
        public bool IsVisible { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
        public int? CreatedByUserId { get; set; }
        public int? LastModifiedByUserId { get; set; }
        public bool IsActive { get; set; }
    }
}
