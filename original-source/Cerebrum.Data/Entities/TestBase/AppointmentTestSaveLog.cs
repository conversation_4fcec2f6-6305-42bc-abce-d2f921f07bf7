﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class AppointmentTestSaveLog
    {

        public AppointmentTestSaveLog()
        {
            var currentDate = System.DateTime.Now;
            LogDate = currentDate;
            DateCreated = currentDate;
            IsActive = true;            
        }
        [Key]
        public int Id { get; set; }
        public DateTime LogDate { get; set; }                        
        public int AppointmentTestId { get; set; }        
        [StringLength(100)]
        public string IpAddress { get; set; }
        public int? SavedByUserId { get; set; }
        public int? PracRootCategoryTempId { get; set; } // practice root category template id        

        [ForeignKey("PracRootCategoryTempId")]
        public PracticeRootCategoryTemplate PracticeRootCategoryTemplate { get; set; }
        public VPWSSaveType SaveType { get; set; }
        public bool IsActive { get; set; }        
        public DateTime DateCreated { get; set; }        
        public DateTime? DateLastModified { get; set; }
        [ForeignKey("AppointmentTestId")]
        public AppointmentTest AppointmentTest { get; set; }

        

    }
}
