﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class RootCategorySavedValue
    {
        public RootCategorySavedValue()
        {
            DateCreated = System.DateTime.Now;
        }

        [Key]
        public int Id { get; set; }

        [StringLength(8000)]
        public string SavedValue { get; set; }        
        
        public int AppointmentTestSaveLogId { get; set; }

        [ForeignKey("AppointmentTestSaveLogId")]
        public AppointmentTestSaveLog AppointmentTestSaveLog { get; set; }

        public int RootCategoryId { get; set; }
        [ForeignKey("RootCategoryId")]
        public RootCategory RootCategory { get; set; }
        public DateTime DateCreated { get; set; }        
        
    }
}
