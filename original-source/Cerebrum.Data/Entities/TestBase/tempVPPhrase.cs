﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class tempVPPhrase
    {
        public tempVPPhrase()
        {
            DateCreated = System.DateTime.Now;
        }

        [Key]
        public int Id { get; set; }
        public int VPReportPhraseId { get; set; }

        
        public int RootCategoryPhraseId { get; set; }

        [ForeignKey("RootCategoryPhraseId")]
        public RootCategoryPhrase RootCategoryPhrase { get; set; }

        [Foreign<PERSON>ey("VPReportPhraseId")]
        public VPReportPhrase VPReportPhrase { get; set; }

        public DateTime DateCreated { get; set; }
    }
}
