﻿using Cerebrum.VirtualVisit.Seedwork;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class SSRSReport: ISSRSReport
    {
        public SSRSReport()
        {
            DateCreated = System.DateTime.Now;
        }
        public int Id { get; set; }
        [Required]
        [StringLength(100)]
        public string ReportName { get; set; } // friendly report name for front end users
        [Required]
        [StringLength(100)]
        public string ReportSSRSName { get; set; } // the actual report name in ssrs
        [Required]
        [StringLength(200)]
        public string ReportDescription { get; set; }
        [Required]
        [StringLength(500)]
        public string ReportPath { get; set; }

        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }

    }
}
