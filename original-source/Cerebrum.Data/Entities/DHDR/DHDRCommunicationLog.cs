﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static AwareMD.DHDR.Dto.Enums;

namespace Cerebrum.Data
{
    [Table("DHDRCommunicationLogs")]
    [TrackChanges]
    public class DHDRCommunicationLog
    {
        [Key]
        public int Id { get; set; }

        public int PracticeId { get; set; }

        public int OfficeId { get; set; }

        public int UserId { get; set; }

        public int? PatientRecordId { get; set; }

        [StringLength(10)]
        public string HealthCardNumber { get; set; }

        public TransactionType? TransactionType { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        [StringLength(128)]
        public string TransactionId { get; set; }

        public bool? ConsentViewOverride { get; set; } = false;

        public TransactionStatus? TransactionStatus { get; set; }

        public DateTime CreatedDateTime { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string InteractingService { get; set; }

        [StringLength(500)]
        public string URL { get; set; }

        [StringLength(100)]
        public string EHRIdentifier { get; set; }

        [StringLength(100)]
        public string ReturnCode { get; set; }

        [StringLength(500)]
        public string ReturnMessage { get; set; }

        [StringLength(100)]
        public string MessageHeaderId { get; set; }

        [Column(TypeName = "varchar(MAX)")]
        [MaxLength]
        public string MedicationDispenseId { get; set; }

        //[StringLength(150)]
        //public string UserFullName { get; set; }

    }
}
