﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("KioskIpAddresses")]
    public class KioskIpAddress
    {
        public int Id { get; set; }

        [Required]
        [Index("IX_KioskIpAddress", 1, IsUnique = true)]
        public int OfficeId { get; set; }

        [ForeignKey("OfficeId")]
        public Office Office { get; set; }
        
        [Required]
        [StringLength(100)]
        [Index("IX_KioskIpAddress", 2, IsUnique = true)]
        public string IpAddress { get; set; }

        [StringLength(500)]
        public string WelcomeMessage { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime? LastModified { get; set; }
        public int? LastModifiedByUser { get; set; }
    }
}
