﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class KioskMessage
    {
        [Key]
        public int Id { get; set; }

        [Index("IX_KioskMessageCode", 1, IsUnique = true)]
        public int Code { get; set; }
        [StringLength(500)]
        public string InternalMessage { get; set; }
        [StringLength(500)]
        public string ExternalMessage { get; set; } // friendly message for patients
        public DateTime DateCreated { get; set; }
        public DateTime? LastModified { get; set; }
        public int? LastModifiedByUser { get; set; }
    }
}
