﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("KioskCheckins")]
    public class KioskCheckin
    {
        [Key]
        public int Id { get; set; }

        public int KioskSetId { get; set; }

        [ForeignKey("KioskSetId")]
        public KioskSet KioskSet { get; set; }

        public int? AppointmentId { get; set; }

        [ForeignKey("AppointmentId")]
        public Appointment Appointment { get; set; }

        [StringLength(100)]
        public string HealthCard { get; set; }

        [StringLength(100)]
        public string IpAddress { get; set; }
        public int? KioskMessageId { get; set; }
        [ForeignKey("KioskMessageId")]
        public KioskMessage KioskMessage { get; set; }
        public DateTime? DateCheckedIn { get; set; }
        public DateTime DateCreated { get; set; }
    }
}
