﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("EconsultPatientAssociationLogs")]
    [TrackChanges]
    public class EconsultPatientAssociationLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int PatientId { get; set; } 

        [Required]
        [StringLength(20)]
        public string CaseId { get; set; }

        public int? DisassociationReasonId { get; set; } 

        [StringLength(50)]
        public string DisassociationNote { get; set; }

        public int UserId { get; set; }

        public DateTime DateCreated { get; set; }

        public DBSaveType SaveType { get; set; } 

        [ForeignKey("DisassociationReasonId")]
        public virtual EconsultPatientDisassociationReason PatientDisassociationReason { get; set; }

        [ForeignKey("PatientId")]
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
