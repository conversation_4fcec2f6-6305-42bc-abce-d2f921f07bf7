﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("EconsultPatientAssociations")]
    [TrackChanges]
    public class EconsultPatientAssociation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        [Index("UX_CaseId_EconsultPatientAssociations", IsUnique = true)]
        public string CaseId { get; set; }

        [Required]
        public int PatientId { get; set; } 

        [Required]
        public bool EmrInitialRequest { get; set; } 

        [Required]
        public DateTime DateCreated { get; set; } 

        [ForeignKey("PatientId")]
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
