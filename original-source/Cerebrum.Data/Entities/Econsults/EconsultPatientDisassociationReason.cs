﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("EconsultPatientDisassociationReasons")]
    [TrackChanges]
    public class EconsultPatientDisassociationReason
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string DisassociationReason { get; set; }

        public DateTime DateCreated { get; set; }
    }
}
