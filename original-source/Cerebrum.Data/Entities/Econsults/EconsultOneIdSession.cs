﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Entities.Econsults
{

    [Table("EconsultOneIdSessions")]
    [TrackChanges]
    public class EconsultOneIdSession
    {
        [Key]
        public int Id { get; set; }
        [Required]
        public int UserId { get; set; }

        [MaxLength(100)]
        public string MessageId { get; set; }
        public string Token1 { get; set; }

        [MaxLength(100)]
        public string IpAddress { get; set; }
        public int Status { get; set; }
        public DateTime? DateCreated { get; set; }
        public DateTime? DateUpdated { get; set; }
        public DateTime? OneIdTokenExpirationDate { get; set; }

    }
}

