﻿using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("EconsultUAO")]
    [TrackChanges]
    public class EconsultUAO
    {
        [Key]
        public short Id { get; set; }

        [Required]
        [StringLength(40)]
        public string UAOName { get; set; }

        [Required]
        [StringLength(60)]
        public string UAO { get; set; }

        public bool IsActive { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime? DateUpdated { get; set; }

        public short UAOType { get; set; }

        [Required]
        [StringLength(200)]
        public string LegalName { get; set; }

        [Required]
        [StringLength(100)]
        public string Address1 { get; set; }

        [StringLength(100)]
        public string Address2 { get; set; }

        [Required]
        [StringLength(20)]
        public string Phone { get; set; }

        [Required]
        [StringLength(100)]
        public string City { get; set; }

        [Required]
        public Province Province { get; set; } = Province.CAON;

        [Required]
        [StringLength(100)]
        public string PostalCode { get; set; }

        [Required]
        public int Country { get; set; } = 0;

        public int? PracticeId { get; set; }

        [ForeignKey("UAOType")]
        public virtual EconsultUAOType EconsultUAOTypes { get; set; }
    }
}
