﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("EconsultUserUAO")]
    [TrackChanges]
    public  class EconsultUserUAO
    {
        [Key]
        public short Id { get; set; }
        public int UserId { get; set; }
        public short EconsultUAOId { get; set; }
        public bool IsActive { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime? DateUpdated { get; set; }

        [ForeignKey("EconsultUAOId")]
        public virtual EconsultUAO EconsultUAOs { get; set; }

    }
}
