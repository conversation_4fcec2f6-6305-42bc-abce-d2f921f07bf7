﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("EconsultAttachedFiles")]
    [TrackChanges]
    public class EconsultAttachedFile
    {
        [Key]
        public int Id { get; set; }
        public int ConsultId { get; set; }        // FK to Econsult table

        [StringLength(500)]
        public string AttachedFileName { get; set; }

        [StringLength(100)]
        public string Description { get; set; }
        public DateTime DateCreated { get; set; }

        [StringLength(50)]
        public string AttachedFileExtension { get; set; }
        public bool IsClinicServerFile { get; set; } = false;

        [Required]
        public int OfficeId { get; set; } = 0;

        [ForeignKey("ConsultId")]
        public virtual Econsult Econsults { get; set; }
    }
}
