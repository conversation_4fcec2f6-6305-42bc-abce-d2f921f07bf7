﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("EconsultMetadata")] 
    [TrackChanges]
    public class EconsultMetadata
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        [Index("UX_CaseId_EconsultMetadata", IsUnique = true)]
        public string CaseId { get; set; }                      // Otn consultId

        public DateTime DateSaved { get; set; }                // from EMR
        public DateTime DateLastUpdated { get; set; }          // from OTN consult
        public DateTime DateSubmitted { get; set; }            // from OTN initial request

        [Required]
        [StringLength(100)]
        public string RequesterName { get; set; }

        [Required]
        [StringLength(100)]
        public string RecipientName { get; set; }

        [StringLength(20)]
        public string RequesterPractitionerId { get; set; }

        [StringLength(200)]
        public string IssuerName { get; set; }
        [StringLength(200)]
        public string IssuerGroup { get; set; }

        public int CoverageId { get; set; }                     // enum BillingCoverageType: OHIP, NON OHIP

        [StringLength(1000)]
        public string Title { get; set; }                       // Subject


        [StringLength(4000)]
        public string Description { get; set; }                 // subject  + first note

        public bool EmrInitialRequest { get; set; }

        public int UseId { get; set; }                          // it's who last updated record

        [StringLength(200)]
        public string RespondentSpecialty { get; set; }
        [StringLength(200)]
        public string RespondentSubSpecialty { get; set; }
        [StringLength(200)]
        public string RespondentGroupOrOrganizationName { get; set; } // like 'Cardiology Specialty Group', 'Managed Specialty Group -

        [StringLength(100)]
        public string PatientFamily { get; set; }

        [StringLength(100)]
        public string PatientGiven { get; set; }

        [StringLength(20)]
        public string PatientGender { get; set; }

        public DateTime PatientBirthDate { get; set; }

        [StringLength(20)]
        public string Patient_HCN { get; set; }

        [StringLength(6)]
        public string Patient_HCN_VC { get; set; }

        [StringLength(200)]
        public string AuthorName { get; set; }                  // who created initial request

        public bool PatientNeedsToBeSeen { get; set; }          // in notes, get latest in case sorting/filtering

        [StringLength(50)]
        public string ConsultState { get; set; }

        public bool SavedCompletedFlag { get; set; }

        [StringLength(50)]
        public string ConsultFlag { get; set; }

        public int PracticeId { get; set; }

        [ForeignKey("PracticeId")]
        public virtual Practice Practices { get; set; }
    }
}
