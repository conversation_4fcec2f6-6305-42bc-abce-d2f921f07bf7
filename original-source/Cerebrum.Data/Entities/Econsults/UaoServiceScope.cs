﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("UaoServiceScope")]
    [TrackChanges]
    public class UaoServiceScope
    {
        [Key]
        public int Id { get; set; }

        public int OntarioHealthServiceId { get; set; }

        [Required]
        [StringLength(50)]
        public string ScopeProfileName { get; set; }

        [Required]
        [StringLength(50)]
        public string Scope { get; set; }

        [Required]
        [StringLength(250)]
        public string Profile { get; set; }

        public bool IsActive { get; set; }

        [ForeignKey("OntarioHealthServiceId")]
        public virtual OntarioHealthService OntarioHealthServices { get; set; }
    }
}
