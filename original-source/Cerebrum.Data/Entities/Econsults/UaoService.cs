﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("UaoServices")]
    [TrackChanges]
    public class UaoService
    {
        [Key]
        [Column(Order = 0)]
        public int OntarioHealthServiceId { get; set; }
        [Key]
        [Column(Order = 1)]
        public short EconsultUaoId { get; set; }

        [ForeignKey("EconsultUaoId")]
        public virtual EconsultUAO EconsultUAOs { get; set; }

        [ForeignKey("OntarioHealthServiceId")]
        public virtual OntarioHealthService OntarioHealthServices { get; set; }
    }
}
