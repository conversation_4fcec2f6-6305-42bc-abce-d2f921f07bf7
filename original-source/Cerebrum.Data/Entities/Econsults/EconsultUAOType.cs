﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("EconsultUAOTypes")]
    [TrackChanges]
    public class EconsultUAOType
    {
        [Key]
        public short Id { get; set; }

        [Required]
        [StringLength(50)]
        public string UAOTypeName { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateUpdated { get; set; }

    }
}
