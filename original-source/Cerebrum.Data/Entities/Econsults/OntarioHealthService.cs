﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("OntarioHealthServices")]
    [TrackChanges]
    public class OntarioHealthService
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Key { get; set; }

        [Required]
        [StringLength(150)]
        public string ServiceName { get; set; }

        [Required]
        [StringLength(50)]
        public string ScopeName { get; set; }

        public DateTime DateCreated { get; set; }

    }
}
