﻿using System;
using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("EconsultMetadataNotes")]
    [TrackChanges]
    public class EconsultMetadataNote
    {
        [Key]
        public int Id { get; set; }

        public int EconsultMetadataId { get; set; }             // FK EconsultMetadata - EconsultMetadata

        [Required]
        [StringLength(20)]
        public string OtnNoteId { get; set; }                   

        [StringLength(4000)]
        public string Note { get; set; }

        public DateTime Date { get; set; }

        [Required]
        [StringLength(200)]
        public string SenderName { get; set; }              // name of doctor 

        [Required]
        [StringLength(50)]
        public string State { get; set; }                   // ConsultState

        [StringLength(50)]
        public string DeclineReason { get; set; }

        public bool PatientNeedsToBeSeen { get; set; }
        public bool PatientNeedsToBeSeenAvailable { get; set; }
        [Required]
        [StringLength(200)]
        public string AuthorName { get; set; }              // the same as Sender<PERSON>ame if not a delegate. if user a delegate - delegate name 


        [ForeignKey("EconsultMetadataId")]
        public virtual EconsultMetadata EconsultMetadata { get; set; }

    }
}
