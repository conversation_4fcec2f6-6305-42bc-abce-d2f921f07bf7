﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace Cerebrum.Data.Entities.Econsults
{

    [Table("EconsultSupportedFileTypes")]
    [TrackChanges]
    public class EconsultSupportedFileType
    {
        [Key]
        public int Id { get; set; }

        [MaxLength(50)]
        public string FileExtension { get; set; }

        [MaxLength(100)]
        public string Description { get; set; }
    }
}
