﻿using System;
using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("EconsultMetadataNoteFiles")]
    [TrackChanges]
    public class EconsultMetadataNoteFile
    {
        [Key]
        public int Id { get; set; }

        public int EconsultMetadataNoteId { get; set; }         // FK EconsultMetadataNote

        [StringLength(100)]
        public string Title { get; set; }                       // Description

        [Required]
        [StringLength(200)]
        public string FileName { get; set; }

        [Required]
        [StringLength(20)]
        public string FileType { get; set; }                    // file Extension 

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; }                    // full file path in folder

        //[Required]
        [StringLength(100)]
        public string OtnContentType { get; set; }

        [Required]
        [StringLength(20)]
        public string OtnId { get; set; }

        [Required]
        public int OfficeId { get; set; }

        public bool IsSaved { get; set; } = false;

        [ForeignKey("EconsultMetadataNoteId")]
        public virtual EconsultMetadataNote EconsultMetadataNote { get; set; }
    }
}
