﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.Econsults
{
    [Table("Econsults")]
    [TrackChanges]
    public class Econsult
    {
        [Key]
        public int Id { get; set; }
        [Required]
        [StringLength(20)]
        public string RequesterPractitionerId { get; set; }    // otn PractitionerId         
        [Required]
        public int PatientId { get; set; }

        [StringLength(20)]
        public string CaseId { get; set; }              // CaseId from eService, update when get from eService

        [StringLength(1000)]
        public string SubjectLine { get; set; }

        [StringLength(4000)]
        public string Notes { get; set; }

        public int CoverageType { get; set; }           // enum BillingCoverageType: OHIP, NON OHIP

        [StringLength(200)]
        public string InsurerName { get; set; } = "N/A";

        [StringLength(20)]
        public string InsuranceGroupNumber { get; set; } = "N/A";

        [Required]
        public int DraftStatus { get; set; } = 1;           // Draft - 1 , Submitted - 2

        [Required]
        public int TypeOfDeliveryModel { get; set; } = 0;   // DeliveryModel enum, -- 2 options: BaseManagedSpecialty = 1, SpecificProviderOrGroup = 2

        public int SpecificProviderOrGroup { get; set; } = 0; // Specialist - 1, Organization - 2

        [StringLength(100)]
        public string ManagedTypeCode { get; set; }

        [StringLength(100)]
        public string ManagedSpecialtyCode { get; set; }

        [StringLength(20)]
        public string RecipientOrganizationId { get; set; }

        [StringLength(20)]
        public string RecipientServiceId { get; set; }

        [StringLength(20)]
        public string RecipientPractitionerId { get; set; }

        public int AuthorUserId { get; set; }           // aspnetusers UserId


        public DateTime DateCreated { get; set; }
        public DateTime DateLastModified { get; set; }
        public DateTime? DateSubmitted { get; set; }


      
        // virtual ???
        //public virtual ConsultDetail Details { get; set; }
        //public virtual List<eConsultDetail> Details { get; set; }
    }
}
