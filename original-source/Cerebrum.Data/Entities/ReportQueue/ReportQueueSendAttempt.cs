﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("ReportQueueSendAttempts")]
    [TrackChanges]
    public class ReportQueueSendAttempt
    {
        [Key]
        public int Id { get; set; } 
        public int ReportQueueId { get; set; }
        public DateTime DateCreated { get; set; }

        [ForeignKey("ReportQueueId")]
        public virtual ReportQueue ReportQueue { get; set; }
    }
}
