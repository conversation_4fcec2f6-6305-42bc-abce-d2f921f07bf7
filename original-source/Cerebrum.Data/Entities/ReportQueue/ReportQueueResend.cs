﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("ReportQueueResends")]
    [TrackChanges]
    public class ReportQueueResend
    {
        [Key]
        public int Id { get; set; }
        public int ReportQueueId { get; set; }

        [StringLength(1600)]
        public string Doctor { get; set; }

        [ForeignKey("ReportQueueId")]
        public virtual ReportQueue ReportQueue { get; set; }
    }
}
