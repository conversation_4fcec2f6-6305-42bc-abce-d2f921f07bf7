﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("ReportQueues")]
    [TrackChanges]
    public class ReportQueue
    {
        [Key]
        public int Id { get; set; }
        public int AppointmentTestId { get; set; }
        public int AppointmentTestLogId { get; set; }
        public bool IsDoctor { get; set; }
        public int ReportType { get; set; }
        public int LetterType { get; set; }
        public int UserId { get; set; }

        [StringLength(500)]
        public string UserFullName { get; set; }

        [StringLength(100)]
        public string IpAddress { get; set; }
        public bool IsAmended { get; set; }
        public bool ChangeStatus { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateLastModified { get; set; }
        public int SendStatusId { get; set; } // FK table ReportQueueSendStatus: In Queue, Sent, Not Sent, Suspended
        public bool IsVP { get; set; }
        public int NumberOfAttempts { get; set; }
        public int? BullsEyeId { get; set; }
        public bool IsResend { get; set; } = false;
        public SendOutReportPriority SendOutReportPriority { get; set; } = SendOutReportPriority.High;
        public SendOutReportMethod SendOutReportMethod { get; set; } = SendOutReportMethod.Queue;

        [ForeignKey("SendStatusId")]
        public virtual ReportQueueSendStatus ReportQueueSendStatus { get; set; }

    }
}
