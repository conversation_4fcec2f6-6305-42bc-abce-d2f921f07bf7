﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("ReportQueueSendStatuses")]
    [TrackChanges]
    public class ReportQueueSendStatus
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        [Index("IX_Name", IsUnique = true)]
        public string StatusName { get; set; } // InQueue, Sent, NotSent, Suspended

        [Required]
        [StringLength(100)]
        public string StatusText { get; set; } // In Queue, Sent, Not Sent, Suspended

        [StringLength(200)]
        public string StatusDescription { get; set; }
        public DateTime DateCreated { get; set; }

    }
}
