﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data.Radiology
{
    [TrackChanges]
    public class PdfValueIndex
    {
        [Key]
        public Guid? pvid { get; set; }
        public Guid? pid { get; set; }
        public int? practiceID { get; set; }
        public int? testID { get; set; }
        public int? appointmentID { get; set; }
        public String data { get; set; }
        public DateTime? ts { get; set; }
    }
}
