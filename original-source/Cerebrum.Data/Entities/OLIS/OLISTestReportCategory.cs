﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    /// <summary>
    /// OLIS Test Report category
    /// </summary>
    [TrackChanges]
    public class OLISTestReportCategory
    {
        [Key]
        public int Id { get; set; }
        [StringLength(250)]
        public string categoryName { get; set; }
        public DateTime createdOn { get; set; }
        public DateTime updatedOn { get; set; }
        public bool isActive { get; set; }
    }
}