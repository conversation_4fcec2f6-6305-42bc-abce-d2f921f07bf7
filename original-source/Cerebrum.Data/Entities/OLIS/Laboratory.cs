﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum.Data.Entities.OLIS
{
    public class Laboratory
    {
        public int Id { get; set; }
        public int? LaboratoryId { get; set; }
        [StringLength(4)]
        public string LicenceNumber { get; set; }
        public LabType LabType { get; set; }
        [StringLength(256)]
        public string FacilityName { get; set; }
        [StringLength(100)]
        public string Address1 { get; set; }
        [StringLength(100)]
        public string Address2 { get; set; }
        [StringLength(50)]
        public string City { get; set; }
        [StringLength(2)]
        public string Province { get; set; }
        [StringLength(7)]
        public string PostalCode { get; set; }
        [StringLength(100)]
        public string OID { get; set; }
        [StringLength(100)]
        public string FullID { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; }
        public bool IsActive { get; set; }
        public virtual Laboratory laboratory { get; set; }
        public virtual List<Laboratory> children { get; set; }
    }
}
