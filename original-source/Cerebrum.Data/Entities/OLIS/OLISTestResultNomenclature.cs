﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    /// <summary>
    /// OLIS Test Result Nomenclature
    /// </summary>
    [TrackChanges]
    public class OLISTestResultNomenclature
    {
        [Key]
        public int Id { get; set; }
        [StringLength(10)]
        public string LOINCCode { get; set; }
        [StringLength(500)]
        public string LOINCComponent { get; set; }
        [StringLength(50)]
        public string LOINCProperty { get; set; }
        [StringLength(100)]
        public string Units { get; set; }
        [StringLength(100)]
        public string LOINCTime { get; set; }
        [StringLength(100)]
        public string LOINCSystem { get; set; }
        [StringLength(100)]
        public string LOINCScale { get; set; }
        [StringLength(100)]
        public string LOINCMethod { get; set; }
        [StringLength(150)]
        public string LOINCShortName { get; set; }
        [StringLength(500)]
        public string LOINCFullySpecifiedName { get; set; }
        [StringLength(500)]
        public string resultAlternateName1 { get; set; }
        [StringLength(500)]
        public string resultAlternateName2 { get; set; }
        [StringLength(500)]
        public string resultAlternateName3 { get; set; }
        [StringLength(1000)]
        public string  LOINCAnswerList{ get; set; }
        public DateTime? effectiveDate { get; set; }
        public DateTime? expiredDate { get; set; }
        [StringLength(15)]
        public string sortKey { get; set; }
        public int OLISTestResultCategoryId { get; set; }
        public virtual OLISTestResultCategory OLISTestResultCategory { get; set; }
        public DateTime? createdOn { get; set; } = DateTime.Now;
        public DateTime? updatedOn { get; set; } = DateTime.Now;
        public bool isActive { get; set; }


    }
}