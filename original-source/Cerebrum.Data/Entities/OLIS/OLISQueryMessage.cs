﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class OLISQueryMessage
    {
        [Key]
        public int Id { get; set; }
        [StringLength(4)]
        public string QueryId { get; set; }
        [StringLength(200)]
        public string QueryTrigger { get; set; }
        [StringLength(150)]
        public string QueryName { get; set; }
        [StringLength(200)]
        public string StoreProcedureName { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
