﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.OLIS
{
    [TrackChanges]
    public class OLISQueryParameterError
    {
        public int Id { get; set; }
        public int OLISBusinessLogicErrorCodeId { get; set; } 
        public virtual OLISBusinessLogicErrorCode OLISBusinessLogicErrorCodes { get; set; }
        [StringLength(10)]
        public string ParameterName { get; set; }
        [StringLength(200)]
        public string ErrorMessage { get; set; }
        [StringLength(200)]
        public string DisplayMessage { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; }
      
    }
}
