﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.OLIS
{
    [TrackChanges]
    public class OLISReceivedReportPatient
    {
        public int Id { get; set; }
        public int OLISReceivedReportId { get; set; }
        public virtual OLISReceivedReport OLISReceivedReport { get; set; }
        public int? PatientRecordId { get; set; }
        [StringLength(12)]
        public string HealthCardNumber { get; set; }
        [StringLength(260)]
        public string OLISAccessionNumber { get; set; }
        [StringLength(260)]
        public string CerebrumAccessionNumber { get; set; }
        public DateTime? ReportDateTime { get; set; }
        public int PID_SetId { get; set; }
        [StringLength(100)]
        public string PatientName { get; set; }
        public OLISPatientReportStatus ReportStatus { get; set; }
        //[StringLength(2000)]
       // public string RequestedTests { get; set; }
        [StringLength(500)]
        public string RejectReason { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
        public virtual List<OLISReceivedReportDetail> OLISReceivedReportDetails { get; set; } = new List<OLISReceivedReportDetail>();
    }
    [TrackChanges]
    public class OLISReceivedReportDetail
    {
        public int Id { get; set; }
        public int OLISReceivedReportPatientId { get; set; }
        public virtual OLISReceivedReportPatient OLISReceivedReportPatient { get; set; }
        [StringLength(100)]
        public string AccessionNumber { get; set; }
        public DateTime? CollectionDate { get; set; }
        public DateTime? LastUpdatedInOLIS { get; set; }
        [StringLength(15)]
        public string TestRequestCode { get; set; }
        public OLISTestRequestStatus TestRequestStatus { get; set; }
        public OLISPatientReportStatus ReportStatus { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
        public virtual List<OLISReportResult> OLISReportResults { get; set; } = new List<OLISReportResult>();
    }
    [TrackChanges]
    public class OLISReportDoctor
    {
        public int Id { get; set; }
        public int OLISReceivedReportDetailId { get; set; }
        public virtual OLISReceivedReportDetail OLISReceivedReportDetail { get; set; }
        [StringLength(15)]
        public string RequestingHic { get; set; }
        public OLISReportDoctorType doctorType { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
    }
    [TrackChanges]
    public class OLISReportResult
    {
        public int Id { get; set; }
        public int OLISReceivedReportDetailId { get; set; }
        public int? SetId { get; set; }
        public virtual OLISReceivedReportDetail OLISReceivedReportDetail { get; set; }
        [StringLength(10)]
        public string LOINC { get; set; }
        [StringLength(250)]
        public string TestName { get; set; }
        public OLISPatientReportStatus ReportStatus { get; set; }
        public OLISResultStatus ResultStatus { get; set; }
        public int? UserId { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime UpdatedDateTime { get; set; } = DateTime.Now;
    }
}
