﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    ///  OLIS Test Request Nomenclature
    ///  Practitioner order test 
    ///  OLIS HL7 OBR Segment
    ///  TRNNNNN-N LOINC Code
    /// </summary>
    [TrackChanges]
    public class OLISTestRequestNomenclature
    {
        [Key]
        public int Id { get; set; }
        [StringLength(15)]
        public string OLISTestRequestCode { get; set; }
        [StringLength(500)]
        public string testRequestName { get; set; }
        [StringLength(500)]
        public string requestAlternateName1 { get; set; }
        [StringLength(500)]
        public string requestAlternateName2 { get; set; }
        [StringLength(500)]
        public string requestAlternateName3 { get; set; }
        [StringLength(500)]
        public string comment { get; set; }
        [StringLength(15)]
        public string sortKey { get; set; }

        public int OLISTestRequestCategoryId { get; set; }
        public virtual OLISTestRequestCategory OLISTestRequestCategory { get; set; }
        public int? OLISTestRequestSubCategoryId { get; set; }
        public virtual OLISTestRequestSubCategory OLISTestRequestSubCategory { get; set; }
        public int? OLISTestReportCategoryId { get; set; }
        public virtual OLISTestReportCategory OLISTestReportCategory { get; set; }
        public DateTime createdOn { get; set; }
        public DateTime updatedOn { get; set; }
        public bool isActive { get; set; }

    }
}
