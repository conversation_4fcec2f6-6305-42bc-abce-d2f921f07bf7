﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.OLIS
{
    [TrackChanges]
    public class OLISBusinessLogicErrorCode
    {
        public int Id { get; set; }
        public int ErrorCode { get; set; }
        [StringLength(150)]
        public string Description { get; set; }
        [StringLength(50)]
        public string ErrorPointsTo { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; } 
        public virtual List<OLISQueryParameterError> OLISQueryParameterErrors { get; set; }
    }
}
