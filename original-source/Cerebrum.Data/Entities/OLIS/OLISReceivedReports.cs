﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.OLIS
{
    [TrackChanges]
    public class OLISReceivedReport
    {
        public int Id { get; set; }
        public int OLISCommunicationLogId { get; set; }
        public virtual OLISCommunicationLog OLISCommunicationLog { get; set; }
        public byte[] jsonReports { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public virtual List<OLISReceivedReportPatient> Patients { get; set; } = new List<OLISReceivedReportPatient>();
        public virtual List<OLISReceivedResponseFile> Files { get; set; } = new List<OLISReceivedResponseFile>();
        
    }

   
}
