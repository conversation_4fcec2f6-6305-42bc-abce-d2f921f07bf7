﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.OLIS
{
    [TrackChanges]
    public class OLISMicrorganism
    {
        public int Id { get; set; }
        [StringLength(20)]
        public string Code { get; set; }
        [StringLength(20)]
        public string MicroorganismType { get; set; }
        [StringLength(20)]
        public string TaxonomicLevel { get; set; }
        [StringLength(200)]
        public string MicroorganismName { get; set; }
        [StringLength(200)]
        public string AlternativeName1 { get; set; }
        [StringLength(200)]
        public string AlternativeName2 { get; set; }
        [StringLength(200)]
        public string ShortName { get; set; }
        [StringLength(200)]
        public string Source { get; set; }
        [StringLength(10)]
        public string Reportable { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public bool IsActive { get; set; } = true;

    }
}
