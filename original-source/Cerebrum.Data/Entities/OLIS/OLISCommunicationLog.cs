﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.Entities.OLIS
{
    [TrackChanges]
    public class OLISCommunicationLog
    {
        public int Id { get; set; }
        public int PracticeId { get; set; }
        public int? OfficeId { get; set; }
        public int userId { get; set; }
        [StringLength(3)]
        public string QueryType { get; set; }
        public int? PatientRecordId { get; set; }

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        //public DateTime? OBRFromDate { get; set; }
       //public DateTime? OBRToDate { get; set; }
        [StringLength(10)]
        public string RequestingHIC { get; set; }
        [StringLength(128)]
        public string OLISTranscationID { get; set; }
        public EMRToOLISQueryType EMRQueryType { get; set; }

        public bool consentViewOverride { get; set; }
        [StringLength(500)]
        public string ConsentComment { get; set; }

        public Guid MSH10ClientTransactionID { get; set; }
        //public Int64 QueryUniqueId { get; set; }
        [StringLength(10)]
        public string MessageType { get; set; }// Request or Response
        public bool IsSuccessStatusCode { get; set; }


        [StringLength(4000)]
        public string Message { get; set; }

        public DateTime createdDateTime { get; set; } = DateTime.Now;

        public int? OLISCommunicationLogId { get; set; }
        [StringLength(4000)]
        public string Error { get; set; }
        public bool ContinuousPointer { get; set; } = false;
        public int ContinuousPointerCount { get; set; } = 0;

        public virtual OLISCommunicationLog olisCommunicationLog { get; set; }
        public virtual List<OLISCommunicationLog> olisCommunicationResponses { get; set; } = new List<OLISCommunicationLog>();
        //public virtual List<OLISCommunicationError> Errors { get; set; } = new List<OLISCommunicationError>();

    }
    //[TrackChanges]
    //public class OLISCommunicationError
    //{
    //    public int Id { get; set; }
    //    [StringLength(10)]
    //    public string Number { get; set; }
    //    [StringLength(100)]
    //    public string Severity { get; set; }
    //    [StringLength(200)]
    //    public string Message { get; set; }

    //    public int OLISCommunicationLogId { get; set; }
    //    public virtual OLISCommunicationLog Request { get; set; }

    //    public DateTime createdDateTime { get; set; } = DateTime.Now;
    //}
}
