﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// OLIS Test Request category
    /// </summary>
    [TrackChanges]
    public class OLISTestRequestCategory
    {
        [Key]
        public int Id { get; set; }
        [StringLength(250)]
        public string categoryName { get; set; }
        public DateTime createdOn { get; set; }
        public DateTime updatedOn { get; set; }
        public bool isActive { get; set; }
    }
}