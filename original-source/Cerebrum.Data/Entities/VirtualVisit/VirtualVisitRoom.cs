﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    //[Table("VirtualVisitRoom")]
    [TrackChanges]
    public class VirtualVisitRoom: VirtualVisit.Seedwork.DAL.IVirtualVisitRoomDAL
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// To record the Twilio SID of the virtual visit room
        /// </summary>
        public string RoomSid { get; set; }

        private string _RoomSidCollection = null;
        /// <summary>
        /// To collect the Twilio SIDs when users are attending same appointment multiple times, 
        /// so that multiple room SIDs have been used.
        /// </summary>
        [MaxLength(1000)]
        public string RoomSidCollection 
        { 
            get 
            { 
                return _RoomSidCollection?.Substring(0, (_RoomSidCollection.Length >= 1000 ? 1000: _RoomSidCollection.Length)); 
            }
            set { _RoomSidCollection = value; } 
        }
        public bool HasScreenSharing { get; set; } = true;

        [Required]
        public string RoomName { get; set; }

        [Required]
        public string ValidationCode { get; set; }

        [Required]
        public DateTime StartDateTime { get; set; }

        [Required]
        [Range(minimum:1,maximum:1440)]
        public int DurationInMinute { get; set; }

        [Required]
        public int PracticeId { get; set; }

        [ForeignKey("Appointment")]
        public int? AppointmentId { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? RoomOpenedDateTime { get; set; }
        public DateTime? RoomCompletedDateTime { get; set; }

        public virtual Appointment Appointment { get; set; }

        [MaxLength(20)]
        public string InvitedPatientPhone { get; set; }
        [MaxLength(256)]
        public string InvitedPatientEmail { get; set; }
        //public ICollection<VirtualVisitInvitation> Invitations { get; set; }

        //public ICollection<VirtualVisitInviteGuest> InvitedGuests { get; set; }
    }
}
