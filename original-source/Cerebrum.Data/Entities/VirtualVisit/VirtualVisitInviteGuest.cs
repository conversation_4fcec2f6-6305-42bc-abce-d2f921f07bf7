﻿using Cerebrum.Data.Attributes;
using Cerebrum.VirtualVisit.Seedwork.DAL;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VirtualVisitInviteGuest: IVirtualVisitInviteGuestDAL
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        public Guid VirtualVisitRoomId { get; set; }

        public string GuestName { get; set; }
        public string GuestEmail { get; set; }
        public string GuestPhone { get; set; }

        [Required]
        public string ValidationCode { get; set; }
        public DateTime CreatedDateTime { get; set; } = DateTime.Now;
        public DateTime? JoinedDateTime { get; set; }
        public DateTime? EndDateTime { get; set; }
    }
}
