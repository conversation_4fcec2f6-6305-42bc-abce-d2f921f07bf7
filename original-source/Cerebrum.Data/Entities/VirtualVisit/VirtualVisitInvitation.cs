﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class VirtualVisitInvitation
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [ForeignKey("Room")]
        public Guid VirtualVisitRoomId { get; set; }
        public string PatientName { get; set; }
        public string PatientEmail { get; set; }
        public string PatientPhone { get; set; }

        [Required]
        public string ValidationCode { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public virtual VirtualVisitRoom Room { get; set; }
    }
}
