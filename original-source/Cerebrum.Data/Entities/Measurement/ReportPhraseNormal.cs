﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ReportPhraseNormal
    {
        [Key]
        public int ID { get; set; }

        public virtual int ReportPhraseID { get; set; }

        public virtual int NormalReportPhraseID { get; set; }

        public virtual int TestID { get; set; }
    }
}