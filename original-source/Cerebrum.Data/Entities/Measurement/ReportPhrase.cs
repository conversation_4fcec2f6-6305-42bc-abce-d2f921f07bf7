﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ReportPhrase
    {
        //[DatabaseGenerated(DatabaseGeneratedOption.None)]

        [Key]
        public int Id { get; set; }
        public int Index { get; set; }
        public int OldId { get; set; }

        [StringLength(1024)]
        public string name { get; set; }

        [StringLength(8000)]
        public string value { get; set; }

        public int? parent { get; set; }
        public int? root { get; set; }
        public int? ordernumber { get; set; }

        public int? TestID { get; set; }
        public int? OLDTESTID { get; set; }
        public int? GroupID { get; set; }
        public int? type { get; set; }
        public int? field { get; set; }

        public int status { get; set; }
        [StringLength(128)]
        public string grp { get; set; }
        public int? dr { get; set; }

        //public int? NormalReportPhraseID { get; set; }

        public List<ReportPhraseSavedText> ReportPhraseSavedText { get; set; }

        public List<ReportPhraseSavedValue> ReportPhraseSavedValue { get; set; }

        public List<ReportPhraseByDoctor> ReportPhraseByDoctor { get; set; }

        public List<ReportPhraseByPractice> ReportPhraseByPractice { get; set; }
    }
}