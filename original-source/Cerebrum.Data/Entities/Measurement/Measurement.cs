﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class Measurement
    {
        [Key]
        //[DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }

        public int OLDID { get; set; }

        [StringLength(512)]
        public string name { get; set; }

        public int order { get; set; }

        [StringLength(50)]
        public string units { get; set; }
 
        public int categoryCode { get; set; }

        [StringLength(255)]
        public string measurementCode { get; set; }

        public int status { get; set; }

        [StringLength(20)]
        public string mask { get; set; }

       
        public DateTime? dateAdded { get; set; }

        public bool? isCompulsory { get; set; }

        public bool? visibleOnWorkSheet { get; set; }

        public bool? calculateBSA { get; set; }

        public int? ZScoreMethod1 { get; set; }
        public int? ZScoreMethod2 { get; set; }
        public int? ZScoreMethod3 { get; set; }
        public MeasurementCategory Category { get; set; }

        public List<MeasurementSavedValue> MeasurementSavedValue { get; set; }

        //public MeasurementSavedValue MeasurementSavedValue { get; set; }

        public List<MeasurementMapping> MeasurementMappings { get; set; }

        public List<MeasurementByPractice> MeasurementByPractice { get; set; }

        //foreign keys
        public virtual int? MeasurementCategoryID { get; set; }

        public Measurement()
        {
            MeasurementSavedValue = new List<MeasurementSavedValue>();
        }


    }
}