﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace Cerebrum.Data
{
    public class TestGroupDetail
    {
        [Key]
        public int Id { get; set; }

        public bool Visible { get; set; }

        //foreign keys 
        public virtual int TestID { get; set; }
        public virtual int TestGroupId { get; set; }
        public virtual int? ReportPhraseId { get; set; }
        public virtual int? MeasurementCategoryId { get; set; }

    }
}
