﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data.Entities.Measurement
{
    [TrackChanges]
    public class CategoryByGroup
    {
        [Key]
        public int ID { get; set; }

        public int CatgeoryID { get; set; }

        public int GroupID { get; set; }

        public bool Visible { get; set; }
    }
}
