﻿using System;
using System.Linq;
using System.Web;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class MeasurementSavedValue
    {
        [Key]
        public int Id { get; set; }

        [StringLength(512)]
        public string Value { get; set; } = string.Empty;

        //actual or special value
        public bool isCalculated { get; set; }

        //for convieniance
        public int AppointmentID { get; set; }

        public int TestID { get; set; }

        public Measurement  Measurement  { get; set; }
        public MeasurementOperator MeasurementOperator { get; set; }

        //public MeasurementRanges MeasurementRange { get; set; }

        //public MeasurementRanges? MeasurementBSARange { get; set; }

        //foreign keys
        public virtual int AppointmentTestLogID { get; set; }

        public virtual int MeasurementId { get; set; }

        public virtual int MeasurementOperatorId { get; set; }

        public MeasurementSavedValue()
        {

            MeasurementOperatorId = 1; //for NONE
        }


    }
}