﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    public class ReportPhraseMeasurmentCategoryScroll
    {
        [Key]
        public int ID { get; set; }

        public virtual int ReportPhraseID { get; set; }

        public virtual int MeasurementCategoryID { get; set; }
    }
}
