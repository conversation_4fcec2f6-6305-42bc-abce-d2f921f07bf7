﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [TrackChanges]

    public class ReportPhraseSavedValue
    {
        [Key]
        public int Id { get; set; }

        public int Value { get; set; }

        public DateTime DateEntered { get; set; }

        //foreign keys
        //public int AppointmentTestID { get; set; }

        public int AppointmentID { get; set; }

        public int TestID { get; set; }

        //foreign keys
        //public virtual int AppointmentTestLogID { get; set; }
        public int UserID { get; set; }

        public virtual int ReportPhraseID { get; set; }
        
        public virtual int TopLevelReportPhraseID { get; set; }
    }
}