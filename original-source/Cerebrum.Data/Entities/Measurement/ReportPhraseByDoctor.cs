﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    public class ReportPhraseByDoctor
    {

        [Key]
        public int Id { get; set; }

        public int DrID { get; set; }



        [StringLength(8000)]
        public string Text { get; set; }

        //foreign keys
        public virtual int ReportPhraseID { get; set; }


    }
}