﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class PatientProstaticValve
    {
        [Key]
        public int Id { get; set; }
        public int? PatientRecordId { get; set; }
        public int ProstaticValveId { get; set; }
        public decimal? Size { get; set; }
        public int? Addedby { get; set; }
        public int? StatusChangeBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public DateTime? EndDate { get; set; }
        [StringLength(1024)]
        public string InActComment { get; set; }
        public int? InActreason { get; set; }
        public bool IsActive { get; set; }
    }
}