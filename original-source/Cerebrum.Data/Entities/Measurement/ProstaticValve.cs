﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ProstaticValve
    {
        [Key]
        public int Id { get; set; }
        [StringLength(512)]
        public string Name { get; set; }
        [StringLength(512)]
        public string Type { get; set; }
        public int? Typeid { get; set; }


        [StringLength(512)]
        public string Gentype { get; set; }
    }
}
