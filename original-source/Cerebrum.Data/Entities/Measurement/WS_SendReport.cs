﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    public class WS_SendReport
    {
        [Key]
        public int Id { get; set; }

        public bool Active { get; set; } = true;

        [StringLength(512)]
        public string Location { get; set; }
        public DateTime DateEntered { get; set; }
        public bool Sent { get; set; }
        public bool Amended { get; set; }

        public int AppointmentId { get; set; }
        public int TestId { get; set; }

        public virtual int SendTypeId { get; set; }
        [StringLength(512)]
        public string URL { get; set; }

        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(100)]
        public string Fax { get; set; }

        [StringLength(100)]
        public string DocName { get; set; }
        [StringLength(100)]
        public string MessageId { get; set; } = string.Empty;

        [StringLength(1000)]
        public string ErrorMessage { get; set; }
    }
}
