﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ReportPhraseSavedText
    {
        [Key]
        public int Id { get; set; }


        [StringLength(8000)]
        public string Value { get; set; } = string.Empty;

        public int AppointmentID { get; set; }

        public int TestID { get; set; }


        //foreign keys
        public virtual int AppointmentTestLogID { get; set; }

        //public virtual int ReportPhraseID { get; set; }

        [ForeignKey("ReportPhrase")]
        public virtual int TopLevelReportPhraseID { get; set; }

        public virtual ReportPhrase ReportPhrase { get; set; }



    }
}