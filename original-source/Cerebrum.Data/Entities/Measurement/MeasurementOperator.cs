﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class MeasurementOperator
    {
        [Key]
        public int Id { get; set; }

        [StringLength(25)]
        public string name { get; set; }

        //foriegn keys
        public virtual List<MeasurementSavedValue> MeasurementSavedValue  { get;set;}

    }
     
}