﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class AppointmentTestLog
    {
        [Key]
        public int Id { get; set; }
        public DateTime? Date { get; set; }
        public int Status { get; set; }
        public int AppointmentID { get; set; }
        public int TestID { get; set; }
        [StringLength(255)]
        public string IP { get; set; }
        public List<MeasurementSavedValue> MeasurementSavedValue { get; set; }
        public List<ReportPhraseSavedText> ReportPhraseSavedText { get; set; }
       // public List<ReportPhraseSavedValue> ReportPhraseSavedValue { get; set; }
        public AppointmentTestLog()
        {
            this.MeasurementSavedValue = new List<MeasurementSavedValue>();
            this.ReportPhraseSavedText = new List<ReportPhraseSavedText>();
           // this.ReportPhraseSavedValue = new List<ReportPhraseSavedValue> ();
        }

        public int UserID { get; set; }

        //foriegn keys
        //public virtual int UserID { get; set; } 
        //links to upper tables
        //public virtual int AppointmentTestID { get; set; }
    }
}
