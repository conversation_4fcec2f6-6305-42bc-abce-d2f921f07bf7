﻿
using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class MeasurementMapping
    {
       [Key]
       // [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }

        [StringLength(512)]
        public string measureName { get; set; }

        [StringLength(100)]
        public string measureCode { get; set; }

        public int status { get; set; }

        public DateTime? dateAdded { get; set; }

        [StringLength(100)]
        public string categoryCode { get; set; }


        //foreign keys
        public virtual int? MeasurementID { get; set; }

        public virtual Measurement Measurement { get; set; }
    }
}