﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class MeasurementRangeType
    {
        [Key]
        public int Id { get; set; }

        [StringLength(255)]
        public string name { get; set; }

        [StringLength(25)]
        public string color { get; set; }

        public   List<MeasurementRange> MeasuremenRange { get; set; }

        public   List<MeasurementBSARange> MeasurementBSARange { get; set; }
    }
}