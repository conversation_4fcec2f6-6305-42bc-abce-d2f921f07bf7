﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class MeasurementRangeText
    {
        [Key]
        public int ID { get; set; }

        [StringLength(512)]
        public string Text { get; set; }

        //foriegn keys
        public virtual int MeasurementId { get; set; }
        
        //public virtual int ReportPhraseID { get; set; }

        public virtual int MeasurementRangeId { get; set; }
    }
}

