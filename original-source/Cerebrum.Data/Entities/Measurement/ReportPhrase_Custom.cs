﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ReportPhrase_Custom
    {
        [Key]
        public int Id { get; set; }

        [StringLength(8000)]
        public string Text { get; set; }
        public bool Visible { get; set; }
        public int Rank { get; set; }
        public int TestID { get; set; }

        public int PatientRecordId { get; set; }
        public int PracticeID { get; set; }
        public int UserID { get; set; }

        //foriegn keys
        public virtual int ReportPhraseId { get; set; }
    }
}
