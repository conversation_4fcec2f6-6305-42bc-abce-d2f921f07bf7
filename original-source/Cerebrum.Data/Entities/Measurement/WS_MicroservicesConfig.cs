﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;

namespace Cerebrum.Data
{
    [Table("WS_MicroservicesConfig")]
    /// <summary>
    /// Configurations for WS microservices, see DevOps 1135, 1695 & 1739.
    /// </summary>
    public class WS_MicroservicesConfig
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int PracticeId { get; set; }

        [Required]
        [DefaultValue(false)]
        public bool IsConsumeApi { get; set; } = false;

        [Required]
        [DefaultValue(5)]
        public int CacheRefreshMinute { get; set; } = 5;
    }
}
