﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ReportPhraseByPractice
    {

        [Key]
        public int Id { get; set; }

        [StringLength(8000)]
        public string Text { get; set; }

        public int order { get; set; }

        //foreign keys
        public virtual int PracticeID { get; set; }
        public virtual int ReportPhraseID { get; set; }

        [DefaultValue(true)]
        public bool Visible { get; set; } = true;
    }
}