﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class MeasurementCategory
    {
        [Key]
        //[DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }

        public int OLDID { get; set; }

        public int par { get; set; }

        [StringLength(512)]
        public string name { get; set; }

        public int order { get; set; }

        [StringLength(50)]
        public string categoryCode { get; set; }

        public int status { get; set; }


        public DateTime? dateAdded { get; set; }

        //foreign keys
        public virtual List<Measurement> measurements { get; set; }

        public virtual List<MeasurementCategoryTest> MeasurementCategoryTest { get; set; }
        public virtual List<TestGroupDetail> TestGroupDetails { get; set; }

        public MeasurementCategory()
        {

            measurements = new List<Measurement>();

            MeasurementCategoryTest = new List<MeasurementCategoryTest>();
            TestGroupDetails = new List<TestGroupDetail>();
        }
    }
}