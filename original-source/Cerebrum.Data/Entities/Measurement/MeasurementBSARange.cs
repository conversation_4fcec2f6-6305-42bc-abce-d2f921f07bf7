﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class MeasurementBSARange
    {

        [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string categoryCode { get; set; }

        [StringLength(50)]
        public string measurementCode { get; set; }

        [StringLength(1)]
        public string gender { get; set; }

        public double? IndexedRange1 { get; set; }

        public double? IndexedRange2 { get; set; }

        [StringLength(50)]
        public string units { get; set; }


        //foreign keys
        public virtual int MeasurementRangeTypeId { get; set; }

        public virtual int ProstaticValveID { get; set; }

        public   MeasurementRangeType MeasurementRangeType { get; set; }
    }
}