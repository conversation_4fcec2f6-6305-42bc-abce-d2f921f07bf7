﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class MeasurementByPractice
    {
        [Key]
        public int Id { get; set; }

        public bool Visible { get; set; }

        public int PracticeID { get; set; }
        public int MeasurementID { get; set; }
    }
}