﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// HRM Clinic
    /// </summary>
    [TrackChanges]
    public class HRMLog
    {
        [Key]
        public int id { get; set; }
        public int practiceId { get; set; }
        [StringLength(16)]
        public string messageType { get; set; }
        [StringLength(512)]
        public string message { get; set; }
        [StringLength(64)]
        public string user { get; set; }
        [StringLength(16)]
        public string pollingType { get; set; }
        [StringLength(64)]
        public string recipient { get; set; }
        public DateTime dateCreated { get; set; } = DateTime.Now;
    }
}