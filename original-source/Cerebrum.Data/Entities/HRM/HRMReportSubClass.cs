﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// HRM Report SubClass
    /// </summary>
    [TrackChanges]
    public class HRMReportSubClass
    {
        [Key]
        public int id { get; set; }
        public int hrmReportClassId { get; set; } = 0;
        public int facilityId { get; set; }
        [StringLength(128)]
        public string subClassName { get; set; } = string.Empty;
        [StringLength(128)]
        public string accompanyingSubClassName { get; set; } = string.Empty;
        public DateTime dateCreated { get; set; } = DateTime.Now;
    }
}