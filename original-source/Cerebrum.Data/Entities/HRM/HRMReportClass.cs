﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// HRM Report Class
    /// </summary>
    [TrackChanges]
    public class HRMReportClass
    {
        [Key]
        public int id { get; set; }
        [StringLength(128)]
        public string name { get; set; }
        public DateTime dateCreated { get; set; } = DateTime.Now;
    }
}