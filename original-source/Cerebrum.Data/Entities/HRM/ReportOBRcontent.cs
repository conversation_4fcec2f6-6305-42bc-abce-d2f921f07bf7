﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// HRM OBR Content
    /// </summary>
    [TrackChanges]
    public class ReportOBRContent
    {
        [Key]
        public int id { get; set; }
        public int reportId { get; set; }
        public int accompanyingSubClassId { get; set; }  //[HrmReportSubClass] ID
        [StringLength(1024)]
        public string accompanyingMnemonic { get; set; }
        [StringLength(1024)]
        public string accompanyingDescription { get; set; }
        public DateTime? observationDate { get; set; }
        public DateTime dateCreated { get; set; } = DateTime.Now;
    }
}