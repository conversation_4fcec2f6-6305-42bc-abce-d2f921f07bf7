﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// HRM Report Subclass Map
    /// </summary>
    [TrackChanges]
    public class ReportSubclassMap
    {
        [Key]
        public int id { get; set; }
        public int practiceId { get; set; }
        [StringLength(128)]
        public string originalSubClassIds { get; set; }
        public int looseReportCategoryId { get; set; }
        public bool status { get; set; }
        public DateTime dateCreated { get; set; } = DateTime.Now;
    }
}