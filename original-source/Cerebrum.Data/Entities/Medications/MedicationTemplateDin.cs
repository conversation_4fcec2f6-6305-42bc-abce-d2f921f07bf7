﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("MedicationTemplateDins")]
    [TrackChanges]
    public class MedicationTemplateDin
    {
        [Key, ForeignKey("MedicationTemplate")]
        public int MedicationTemplateId { get; set; }

        public MedicationTemplate MedicationTemplate { get; set; }

        public int MedicationId { get; set; }

        [ForeignKey("MedicationId")]
        public Medication Medication { get; set; }
    }
}