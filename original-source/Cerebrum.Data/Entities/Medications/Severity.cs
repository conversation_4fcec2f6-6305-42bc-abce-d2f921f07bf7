﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("Severity")]
    [TrackChanges]
    public class Severity
    {
        [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string Description { get; set; }

        [StringLength(50)]
        //[Index("IX_SeverityCode", 1, IsUnique = true)]
        public string Code { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }
    }
}