﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("MedicationTemplates")]
    [TrackChanges]
    public class MedicationTemplate
    {
        [Key]
        public int Id { get; set; }
        public int TemplateClassId { get; set; }
        [ForeignKey("TemplateClassId")]
        public MedicationTemplateClass TemplateClass { get; set; }

        //[StringLength(500)]
        //public string MedicationName { get; set; }

        [StringLength(50)]
        public string Din { get; set; }        

        public int UserId { get; set; }
        [StringLength(50)]
        public string Name { get; set; }
        [StringLength(50)]
        public string Dose { get; set; }
        [StringLength(50)]
        public string SIG { get; set; }
        [StringLength(50)]
        public string Route { get; set; }
        public int? Repeats { get; set; }
        
        public int? Mitte { get; set; }
        public int? MitteUnitId { get; set; }

        [ForeignKey("MitteUnitId")]
        public MedicationFrequencyUnit MitteUnit { get; set; }
        public int? TreatmentTypeId { get; set; }
        public int? ComplianceId { get; set; }
        [StringLength(50)]
        public string Strength { get; set; }
        [StringLength(512)]
        public string Instructions { get; set; }
        [StringLength(50)]
        public string LU { get; set; }
        public bool CanShare { get; set; }
        public bool IsActive { get; set; }
        public bool IsDin { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime LastModified { get; set; }

        public int LastModifiedByUserId { get; set; }


        public int? MedicationNoDinId { get; set; }
        //[ForeignKey("MedicationNoDinId")]
        //public MedicationNoDin MedicationNoDin { get; set; }
        //public MedicationTemplateDin MedicationDin { get; set; }
        //public MedicationTemplateNoDin MedicationNoDin { get; set; }

    }
}