﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("PatientMedicationSets")]
    [TrackChanges]
    public class PatientMedicationSet
    {       
        [Key]
        public int Id { get; set; }
        public DateTime DateCreated { get; set; }
    }
}