﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("MedicationTemplateClass")]
    [TrackChanges]
    public class MedicationTemplateClass
    {
        [Key]
        public int Id { get; set; }

        [StringLength(100)]
        public string ClassName { get; set; }

        public int PracticeId { get; set; }

        [ForeignKey("PracticeId")]
        public Practice Practice { get; set; }

        public int DoctorSpecializationCode { get; set; }

        public bool IsActive { get; set; }

        public List<MedicationTemplate> MedicationTemplates { get; set; }

    }
}