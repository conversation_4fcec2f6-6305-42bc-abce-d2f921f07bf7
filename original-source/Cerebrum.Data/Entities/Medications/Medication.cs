﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("QRYM_DRUG")]
    public class Medication
    {
        [Key]
        [Column("DRUG_CODE", TypeName = "numeric")]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }

        [Column("PRODUCT_CATEGORIZATION")]
        [StringLength(80)]
        public string Category { get; set; }

        [Column("CLASS")]
        [StringLength(50)]
        public string Class { get; set; } // if Human or Veterinary

        [Column("DRUG_IDENTIFICATION_NUMBER")]
        [StringLength(50)]
        public string DIN { get; set; }

        [Column("BRAND_NAME")]
        [StringLength(200)]
        public string Name { get; set; }

        [Column("DESCRIPTOR")]
        [StringLength(150)]
        public string Descriptor { get; set; }

        [Column("PEDIATRIC_FLAG")]
        [StringLength(50)]
        public string PediatricFlag { get; set; }

        [Column("ACCESSION_NUMBER")]
        [StringLength(50)]
        public string AccessionNumber { get; set; }

        [Column("NUMBER_OF_AIS")]
        [StringLength(50)]
        public string NumberOfAIS { get; set; }

        [Column("LAST_UPDATE_DATE")]
        public DateTime? LastUpdateDate { get; set; }

        [Column("AI_GROUP_NO")]
        [StringLength(50)]
        public string AIGroupNo { get; set; }

        [Column("isActive")]
        public bool IsActive { get; set; }

        public List<MedicationClass> MedicationClasses { get; set; }
        public List<MedicationForm> MedicationForms { get; set; }
        public List<MedicationRoute> MedicationRoutes { get; set; }
        public List<MedicationIngredient> MedicationIngredients { get; set; }
    }
}