﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("PatientAllergyIngredientDetails")]
    public class PatientAllergyIngredientDetail
    {
        [Key]
        public int Id { get; set; }
        public int AllergyIngredientId { get; set; }
        [ForeignKey("AllergyIngredientId")]
        public PatientAllergyIngredient PatientAllergyIngredient { get; set; }        
        public int MedicationId { get; set; }
        [StringLength(200)]
        public string MedicationName { get; set; }
        public int LastModifiedByUser { get; set; }
        public DateTime LastModified { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }
    }
}