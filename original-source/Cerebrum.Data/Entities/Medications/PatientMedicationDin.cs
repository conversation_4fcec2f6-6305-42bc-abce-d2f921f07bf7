﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("PatientMedicationDins")]
    public class PatientMedicationDin
    {
        [Key, ForeignKey("PatientMedication")]
        public int PatientMedicationId { get; set; }

        public PatientMedication PatientMedication { get; set; }

        public int MedicationId { get; set; }

        [ForeignKey("MedicationId")]
        public Medication Medication { get; set; }
        
    }
}