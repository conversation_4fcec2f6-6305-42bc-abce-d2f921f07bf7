﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table(" PatientAllergyNoDins")]
    public class PatientAllergyNoDin
    {
        [Key, ForeignKey("PatientAllergy")]
        public int PatientAllergyId { get; set; }

        public PatientAllergy PatientAllergy { get; set; }

        public int MedicationNoDinId { get; set; }

        [ForeignKey("MedicationNoDinId")]
        public MedicationNoDin MedicationNoDin { get; set; }
    }
}