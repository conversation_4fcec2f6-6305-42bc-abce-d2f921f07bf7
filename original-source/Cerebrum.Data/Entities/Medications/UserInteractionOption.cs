﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("UserInteractionOptions")]
    [TrackChanges]
    public class UserInteractionOption
    {
        [Key]
        public int Id { get; set; }

        public int SettingTypeId { get; set; }

        [ForeignKey("SettingTypeId")]
        public UserInteractionSettingType SettingType { get; set; }
        [StringLength(50)]
        public string Code { get; set; }


        [StringLength(50)]
        public string Description { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }
    }
}
