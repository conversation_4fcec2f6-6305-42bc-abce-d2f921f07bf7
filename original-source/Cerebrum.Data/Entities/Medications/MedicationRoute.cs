﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("QRYM_ROUTE")]
    public class MedicationRoute
    {
        [Key]
        public int Id { get; set; }

        [Column("DRUG_CODE", TypeName = "numeric")]
        public int MedicationId { get; set; }

        [Column("ROUTE_OF_ADMINISTRATION_CODE")]
        public int RouteOfAdministrationCode { get; set; }

        [Column("ROUTE_OF_ADMINISTRATION")]
        [StringLength(50)]
        public string RouteOfAdministration { get; set; }

        [Column("isActive")]
        public bool IsActive { get; set; }

        [ForeignKey("MedicationId")]
        public Medication Medication { get; set; }
        
    }
}