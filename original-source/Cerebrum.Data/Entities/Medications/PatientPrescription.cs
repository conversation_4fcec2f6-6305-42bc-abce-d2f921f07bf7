﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PatientPrescriptions")]
    [TrackChanges]
    public class PatientPrescription
    { 
        [Key]
        public int Id { get; set; }

        public int PrescriptionSetId { get; set; }

        [ForeignKey("PrescriptionSetId")]
        public PatientPrescriptionSet PrescriptionSet { get; set; }

        public int PatientMedicationId { get; set; }
        [ForeignKey("PatientMedicationId")]
        public PatientMedication PatientMedication { get; set; }
        [StringLength(500)]
        public string Description { get; set; }
        [StringLength(50)]
        public string MedicationForm { get; set; }
        [StringLength(50)]
        public string Strength { get; set; }
        [StringLength(50)]
        public string Mitte { get; set; }
        public int? Quantity { get; set; }
        public int? QuantityUnitTypeId { get; set; }

        [ForeignKey("QuantityUnitTypeId")] // strenght unit
        public MedicationStrengthUnit MedicationQuantityUnit { get; set; }

        public int? Repeats { get; set; } // number of refills

        [StringLength(50)]
        public string Frequency { get; set; }
        public int? Duration { get; set; }
        public int? RefillDuration { get; set; }
        public int? RefillQuantity { get; set; }

        public int? RefillQuantityUnitTypeId { get; set; }

        [ForeignKey("RefillQuantityUnitTypeId")] // strength unit
        public MedicationStrengthUnit MedicationRefillQuantityUnit { get; set; }

        [StringLength(50)]
        public string DispenseInterval { get; set; }
        [StringLength(50)]
        public string LU { get; set; }
        public bool? SubstitutionNotAllowed { get; set; } 
        public bool? LongTerm { get; set; }        
        public bool NonAuthoritive { get; set; }
        [StringLength(50)]
        public string Protocol { get; set; }
        
        public int PrescribedByUser { get; set; }
        [StringLength(50)]
        public string PrescriberCPSO { get; set; }
        [StringLength(50)]
        public string PrescriberOHIP { get; set; }
        [StringLength(50)]
        public string PrescriberFirstName { get; set; }
        [StringLength(50)]
        public string PrescriberLastName { get; set; }
        public int? PrescriptionStatusId { get; set; }
        [ForeignKey("PrescriptionStatusId")]
        public PrescriptionStatus PrecriptionStatus { get; set; }       

        [StringLength(50)]
        public string ProblemCode { get; set; }               
        public bool? Compliance { get; set; }
       
        public DateTime DateWritten { get; set; }

        public int? DateWrittenDay { get; set; }

        public int? DateWrittenMonth { get; set; }

        public int? DateWrittenYear { get; set; }

        public DateTime DateStarted { get; set; }

        public int? DateStartedDay { get; set; }

        public int? DateStartedMonth { get; set; }

        public int? DateStartedYear { get; set; }

        public DateTime? DateLastRefill { get; set; }
        public DateTime? DateExpired { get; set; }

        public bool? PastMedication { get; set; }

        [StringLength(1000)]
        public string PrescriptionIdImported { get; set; }

        [StringLength(1000)]
        public string PriorPrescriptionId { get; set; }        

        [StringLength(50)]
        public string PharmacyIdentifier { get; set; } // Targeted Dispensing Facility - service location Identifier
        [StringLength(50)]
        public string PharmacyName { get; set; } // Targeted Dispensing Facility - service location name
        [StringLength(50)]
        public string PharmacyAddress { get; set; } // Targeted Dispensing Facility - service location address


        [StringLength(1000)]
        public string Instructions { get; set; }
        [StringLength(1000)]
        public string Notes { get; set; }

        //[DataType(DataType.Date)]
        public DateTime? DateToBePickup { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? LastModified { get; set; }
        public int LastModifiedByUser { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }

        public bool IsQuickPrescribe { get; set; }

        public bool IsActive { get; set; }

        public bool IsImported { get; set; }
        public List<PatientPrescriptionPrint> PatientPrescriptionPrinted { get; set; }


    }
}