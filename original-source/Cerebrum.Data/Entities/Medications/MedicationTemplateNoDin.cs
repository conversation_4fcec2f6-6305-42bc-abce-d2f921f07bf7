﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("MedicationTemplateNoDins")]
    public class MedicationTemplateNoDin
    {
        [Key, ForeignKey("MedicationTemplate")]
        public int MedicationTemplateId { get; set; }

        public MedicationTemplate MedicationTemplate { get; set; }

        public int MedicationNoDinId { get; set; }

        [ForeignKey("MedicationNoDinId")]
        public MedicationNoDin MedicationNoDin { get; set; }
    }
}