﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PatientPrescriptionsPrinted")]
    [TrackChanges]
    public class PatientPrescriptionPrint
    {
        public PatientPrescriptionPrint()
        {
            IsFax = false;
        }
        [Key]
        public int Id { get; set; }
        public int PrescriptionSetId { get; set; }
        public int UserId { get; set; }

        [ForeignKey("PrescriptionSetId")]
        public PatientPrescriptionSet PrescriptionSet_ { get; set; }
        [StringLength(50)]
        public string IPAddress { get; set; }
        public bool IsFax { get; set; }
        [StringLength(300)]
        public string Notes { get; set; }
        public DateTime DatePrinted { get; set; }      

    }
}