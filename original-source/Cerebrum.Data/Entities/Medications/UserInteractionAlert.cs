﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class UserInteractionAlert
    {
        [Key]
        public int Id { get; set; }

        [Index("IX_UserInteraction", 1, IsUnique = true)]
        public int UserId { get; set; }

        [Index("IX_UserInteraction", 2, IsUnique = true)]
        public int PatientId { get; set; }

        [Required]
        [StringLength(50)]
        [Index("IX_UserInteraction", 3, IsUnique = true)]
        public string MedicationDin { get; set; }

        [Required]
        [StringLength(50)]
        [Index("IX_UserInteraction", 4, IsUnique = true)]
        public string InteractingDin { get; set; }

        public bool Managed { get; set; }

        public bool ShowOnNextlogin { get; set; }

        public DateTime DateManaged { get; set; }

        public DateTime? DateUnmanaged { get; set; }
    }
}
