﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("PatientMedicationNoDins")]
    public class PatientMedicationNoDin
    {
        [Key, ForeignKey("PatientMedication")]
        public int PatientMedicationId { get; set; }

        public PatientMedication PatientMedication { get; set; }

        public int MedicationNoDinId { get; set; }

        [ForeignKey("MedicationNoDinId")]
        public MedicationNoDin MedicationNoDin { get; set; }
    }
}