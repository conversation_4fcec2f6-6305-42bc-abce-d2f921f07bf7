﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("UserInteractionSettingTypes")]
    [TrackChanges]
    public class UserInteractionSettingType
    {
        [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string Description { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }
    }
}