﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("AllergyStatuses")]
    [TrackChanges]
    public class AllergyStatus
    {
        [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string Description { get; set; }

        [StringLength(50)]
        //[Index("IX_AllergyStatusCode", 1, IsUnique = true)]
        public string Code { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }
    }
}