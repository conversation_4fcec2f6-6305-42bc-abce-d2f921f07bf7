﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("MedicationDefaults")]
    public class MedicationDefault
    {
        [Key]
        public int Id { get; set; }


        [StringLength(512)]
        public string Strength { get; set; }


        [StringLength(512)]
        public string SIG { get; set; }


        [StringLength(512)]
        public string Route { get; set; }
        
        public int Mitte { get; set; }

        public int Repeats { get; set; }

        public int? TreatmentTypeId { get; set; }
        [ForeignKey("TreatmentTypeId")]
        public TreatmentType TreatmentType { get; set; }
    }
}