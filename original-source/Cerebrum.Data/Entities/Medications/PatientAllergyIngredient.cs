﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("PatientAllergyIngredients")]
    [TrackChanges]
    public class PatientAllergyIngredient
    {
        public PatientAllergyIngredient()
        {
            //Details = new List<PatientAllergyIngredientDetail>();
        }

        [Key]
        public int Id { get; set; }

        [Index("IX_AllergyIngredient", 1, IsUnique = true)]
        public int PatientRecordId { get; set; }
        [Index("IX_AllergyIngredient", 2, IsUnique = true)]
        public int IngredientCode { get; set; }

        [StringLength(250)]
        public string Ingredient { get; set; }              
        public bool IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime DateCreated { get; set; }

        public DateTime? LastModfied { get; set; }

        public int? LastModifiedByUser { get; set; }
        [StringLength(50)]
        public string IpAddress { get; set; }

        public int? PatientAllergyId { get; set; }

        //[ForeignKey("PatientAllergyId")]
        //public PatientAllergy PatientAllergy { get; set; }

        //public List<PatientAllergyIngredientDetail> Details { get; set; }
    }
}