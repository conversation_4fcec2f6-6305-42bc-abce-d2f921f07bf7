﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("PrescriptionDefaults")]
    [TrackChanges]
    public class PrescriptionDefault
    {
        public int Id { get; set; }

        public int? PrescriptionStatusId { get; set; }
        [ForeignKey("PrescriptionStatusId")]
        public PrescriptionStatus PrecriptionStatus { get; set; }
        //public int? TreatmentTypeId { get; set; }
        //[ForeignKey("TreatmentTypeId")]
        //public TreatmentType TreatmentType { get; set; }        

        public int? ComplianceId { get; set; }

        [ForeignKey("ComplianceId")]
        public Compliance Compliance { get; set; }
    }
}