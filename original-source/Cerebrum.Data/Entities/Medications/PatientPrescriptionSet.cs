﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table("PatientPrescriptionSets")]
    [TrackChanges]
    public class PatientPrescriptionSet
    {
        [Key]
        public int Id { get; set; }        
        public DateTime DateCreated { get; set; }
        //public DateTime? LastModified { get; set; }
        //public int LastModifiedByUser { get; set; }
        //[StringLength(50)]
        //public string IpAddress { get; set; }

        //public bool IsActive { get; set; }
    }
}