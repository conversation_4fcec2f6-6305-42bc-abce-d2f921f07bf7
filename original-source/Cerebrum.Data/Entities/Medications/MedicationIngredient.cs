﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("QRYM_ingred")]
    public class MedicationIngredient
    {
        [Key]
        public int Id { get; set; }

        [Column("DRUG_CODE", TypeName = "numeric")]
        public int MedicationId { get; set; }

        [Column("ACTIVE_INGREDIENT_CODE")]
        public int IngredientCode { get; set; }

        [Column("INGREDIENT")]
        [StringLength(240)]
        public string Ingredient { get; set; }

        [Column("INGREDIENT_SUPPLIED_IND")]
        [StringLength(50)]
        public string IngredientSuppliedInd { get; set; }

        [Column("STRENGTH")]
        [StringLength(50)]
        public string Strenghth { get; set; }

        [Column("STRENGTH_UNIT")]
        [StringLength(50)]
        public string StrengthUnit { get; set; }

        [Column("STRENGTH_TYPE")]
        [StringLength(50)]
        public string StrengthType { get; set; }

        [Column("DOSAGE_VALUE")]
        [StringLength(50)]
        public string DosageValue { get; set; }

        [Column("BASE")]
        [StringLength(50)]
        public string Base { get; set; }

        [Column("DOSAGE_UNIT")]
        [StringLength(50)]
        public string DosageUnit { get; set; }

        [Column("NOTES")]
        [StringLength(2000)]
        public string Notes { get; set; }

        [Column("isActive")]
        public bool IsActive { get; set; }

        [ForeignKey("MedicationId")]
        public Medication Medication { get; set; }
    }
}