﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("PracticeInteractionSettings")]
    [TrackChanges]
    public class PracticeInteractionSetting
    {
        [Key]
        public int Id { get; set; }

        [Index("IX_PracticeInteractionSetting", 1, IsUnique = true)]
        public int PracticeId { get; set; }

        [Index("IX_PracticeInteractionSetting", 2, IsUnique = true)]
        public int SettingTypeId { get; set; }

        [ForeignKey("SettingTypeId")]
        public UserInteractionSettingType SettingType { get; set; }

        [Required]
        [StringLength(50)]
        [Index("IX_PracticeInteractionSetting", 3, IsUnique = true)]
        public string Code { get; set; }

        public bool IsVisible { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }
    }
}
