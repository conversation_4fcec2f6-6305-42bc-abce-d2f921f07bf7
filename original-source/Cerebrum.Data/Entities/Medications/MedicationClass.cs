﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace Cerebrum.Data
{
    [Table("QRYM_THERAPEUTIC_CLASS")]
    public class MedicationClass
    {
        [Key]
        public int Id { get; set; }

        [Column("DRUG_CODE", TypeName = "numeric")]
        public int MedicationId { get; set; }

        [Column("TC_ATC_NUMBER")]
        [StringLength(50)]
        public string TCATCNumber { get; set; }

        [Column("TC_ATC")]
        [StringLength(120)]
        public string TCATC { get; set; }

        [Column("TC_AHFS_NUMBER")]
        [StringLength(50)]
        public string ClassNumber { get; set; }

        [Column("TC_AHFS")]
        [StringLength(80)]
        public string Class { get; set; }

        [Column("isActive")]
        public bool IsActive { get; set; }

        [ForeignKey("MedicationId")]
        public Medication Medication { get; set; }
    }
}