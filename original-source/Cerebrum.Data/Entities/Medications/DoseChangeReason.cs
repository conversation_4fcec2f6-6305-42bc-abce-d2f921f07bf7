﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("DoseChangeReasons")]
    public class DoseChangeReason
    {
        [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string Description { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }
    }
}