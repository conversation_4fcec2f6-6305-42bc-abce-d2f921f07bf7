﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("MedicationStrengthUnits")]
    [TrackChanges]
    public class MedicationStrengthUnit
    {
        [Key]
        public int Id { get; set; }

        [Index("IX_Code", 1, IsUnique = true)]
        [StringLength(50)]
        public string Code { get; set; }

        [StringLength(50)]
        public string Description { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }
    }
    
}
