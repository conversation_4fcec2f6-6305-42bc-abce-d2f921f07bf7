﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("UserPatMedCPPVisibility")]
    [TrackChanges]
    public class UserPatMedCPPVisibility
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Index("IX_UserPatientMedicationCPP", 1, IsUnique = true)]
        public int UserId { get; set; }

        [Index("IX_UserPatientMedicationCPP", 2, IsUnique = true)]
        public int PatientMedicationId { get; set; }

        //[Required]
        //[ForeignKey("PatientMedicationId")]
        //public PatientMedication PatientMedication { get; set; }       

        public bool IsVisible { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }
    }
}
