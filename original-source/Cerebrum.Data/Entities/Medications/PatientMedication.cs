﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PatientMedications")]
    [TrackChanges]
    public class PatientMedication
    {
        public PatientMedication()
        {
            IsImported = false;
        }
        [Key]
        public int Id { get; set; }
        public int PatientRecordId { get; set; }
        [ForeignKey("PatientRecordId")]
        public PatientRecord PatientRecord { get; set; }
        public int MedicationSetId { get; set; }
        [ForeignKey("MedicationSetId")]
        public PatientMedicationSet MedicationSet { get; set; }

        [StringLength(500)]
        public string MedicationName { get; set; }
        [StringLength(50)]
        public string DIN { get; set; } // Drug Code   
        [StringLength(50)]
        public string Dose { get; set; } // Dosage  
        [StringLength(50)]
        public string Strength { get; set; }

        [StringLength(100)]
        public string Form { get; set; }

        [StringLength(1500)]
        public string Classes { get; set; }

        [StringLength(1500)]
        public string Ingredients { get; set; }
        [StringLength(50)]
        public string SIG { get; set; }
        [StringLength(100)]
        public string Route { get; set; }
        //[StringLength(50)]
        //public string Mitte { get; set; }
        public int? Mitte { get; set; }
        public int? MitteUnitId { get; set; }

        [ForeignKey("MitteUnitId")]
        public MedicationFrequencyUnit MitteUnit { get; set; }

        public int Repeats { get; set; } // number of refills  
        public int? DoseChangeReasonId { get; set; }
        [ForeignKey("DoseChangeReasonId")]
        public DoseChangeReason DoseChangeReason { get; set; }
        [StringLength(1000)]
        public string DoseChangeComment { get; set; }

        public int? DiscontinueReasonId { get; set; }
        [ForeignKey("DiscontinueReasonId")]
        public DiscontinueReason DiscontinueReason { get; set; }


        [StringLength(1000)]
        public string DiscontinueComment { get; set; }

        [StringLength(50)]
        public string LU { get; set; }

        [StringLength(1000)]
        public string Instructions { get; set; }

        public bool IsDin { get; set; }

        //not needed anymore
        //public int? MedicationId { get; set; }
        //[ForeignKey("MedicationId")]
        //public Medication Medication { get; set; }
        public int? MedicationNoDinId { get; set; }
        [ForeignKey("MedicationNoDinId")]
        public MedicationNoDin MedicationNoDin { get; set; }

        public int? TreatmentTypeId { get; set; }
        [ForeignKey("TreatmentTypeId")]
        public TreatmentType TreatmentType { get; set; }

        public int? LastPrescribedByUser { get; set; }
        [StringLength(100)]
        public string LastPrescribedByName { get; set; }

        [StringLength(50)]
        public string OutsideProviderFirstName { get; set; }

        [StringLength(50)]
        public string OutsideProviderLastName { get; set; }
        public int? DiscontinuedByUserId { get; set; }
        [StringLength(50)]
        public string DiscontinuedByFirstName { get; set; }

        [StringLength(50)]
        public string DiscontinuedByLastName { get; set; }

        public bool IsActive { get; set; }

        //public bool IsActiveMed { get; set; } // if this is a active drug from the medication database
        public DateTime? MedicationLastUpdated { get; set; }

        public DateTime? DateLastPrescribed { get; set; }

        [StringLength(50)]
        public string DateLastPrescribedPartial { get; set; } // for display only

        [DataType(DataType.Date)]
        public DateTime DateStarted { get; set; } // for vp purpose

        public int? DateStartedDay { get; set; }

        public int? DateStartedMonth { get; set; }

        public int? DateStartedYear { get; set; }
        public DateTime? DateExpired { get; set; }

        [DataType(DataType.Date)]
        public DateTime? DateDiscontinued { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? LastModified { get; set; }
        public int LastModifiedByUser { get; set; }

        [StringLength(50)]
        public string IpAddress { get; set; }

        public bool IsImported { get; set; }

        [StringLength(4000)]
        public string ResidualData { get; set; }

        //public PatientMedicationDin MedicationDin { get; set; }
        //public PatientMedicationNoDin MedicationNoDin { get; set; }

        public List<PatientPrescription> PatientPrescriptions { get; set; }
    }
}