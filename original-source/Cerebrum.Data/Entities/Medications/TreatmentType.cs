﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("TreatmentTypes")]
    [TrackChanges]
    public class TreatmentType
    {
        [Key]
        public int Id { get; set; }

        [StringLength(50)]
        public string Description { get; set; }

        [StringLength(50)]
        //[Index("IX_TreatmentCode", 1, IsUnique = true)]
        public string Code { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }
    }
}