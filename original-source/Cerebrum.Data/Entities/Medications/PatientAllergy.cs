﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PatientAllergies")]
    [TrackChanges]
    public class PatientAllergy
    {
        public PatientAllergy()
        {
            PatientAllergyIngredients = new List<PatientAllergyIngredient>();
            IsImported = false;
        }

        [Key]
        public int Id { get; set; }

        public int PatientRecordId { get; set; }

        [StringLength(500)]
        public string MedicationName { get; set; }

        public int? AgeStarted { get; set; }

        public int? LifeStageId { get; set; }

        [ForeignKey("LifeStageId")]
        public LifeStage LifeStage { get; set; }

        public int ReactionTypeId { get; set; }

        [ForeignKey("ReactionTypeId")]
        public virtual ReactionType ReactionType { get; set; }

        public int SeverityId { get; set; }

        [ForeignKey("SeverityId")]
        public virtual Severity Severity { get; set; }

        public int? AllergyStatusId { get; set; }

        [ForeignKey("AllergyStatusId")]
        public virtual AllergyStatus AllergyStatus { get; set; }

        [StringLength(200)]
        public string Description { get; set; }
        [StringLength(200)]
        public string Diagnosis { get; set; }

        [StringLength(200)]
        public string Procedure { get; set; }
        [StringLength(4000)]
        public string Notes { get; set; }

        public bool IsDin { get; set; }

        [StringLength(50)]
        public string Din { get; set; }
        //public int? MedicationId { get; set; }
        //[ForeignKey("MedicationId")]
        //public Medication Medication { get; set; }
        public int? MedicationNoDinId { get; set; }
        [ForeignKey("MedicationNoDinId")]
        public MedicationNoDin MedicationNoDin { get; set; }

        public DateTime DateStarted { get; set; } // for vp purpose

        public int? DateStartedDay { get; set; }

        public int? DateStartedMonth { get; set; }

        public int? DateStartedYear { get; set; }

        public DateTime? DateRecorded { get; set; } 

        public int? DateRecordedDay { get; set; }

        public int? DateRecordedMonth { get; set; }

        public int? DateRecordedYear { get; set; }

        public DateTime? DateResolved { get; set; }
        
        public DateTime? DateProcedure { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime LastModfied { get; set; }

        public int LastModifiedByUser { get; set; }

        public bool IsActive { get; set; }

        public bool IsImported { get; set; }

        [StringLength(4000)]
        public string ResidualData { get; set; }

        [StringLength(50)]
        public string IpAddress { get; set; }

        //public virtual PatientAllergyDin MedicationDin { get; set; }
        //public virtual PatientAllergyNoDin MedicationNoDin { get; set; }

        public virtual List<PatientAllergyIngredient> PatientAllergyIngredients { get; set; }

    }
}