﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("PrescriptionStatus")]
    [TrackChanges]

    public class PrescriptionStatus
    {
        [Key]
        public int id { get; set; }

        [StringLength(50)]
        public string Description { get; set; }

        [StringLength(50)]
        //[Index("IX_StatusCode", 1, IsUnique = true)]
        public string Code { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }

    }
}