﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    [Table("UserPatMedSort")]
    [TrackChanges]
    public class UserPatMedSort // user patient medication sort
    {
        public UserPatMedSort()
        {

        }

        [Key]
        public int Id { get; set; }

        [Required]
        [Index("IX_UserPatientMedicationSort", 1, IsUnique = true)]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Index("IX_UserPatientMedicationSort", 2, IsUnique = true)]
        public string SortKey { get; set; }

        [StringLength(1)]
        public string SortOrder { get; set; } // A = Ascending, d = Descending

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }
    }
}
