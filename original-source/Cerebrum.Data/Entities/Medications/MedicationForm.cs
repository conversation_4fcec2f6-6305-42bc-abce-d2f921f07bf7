﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("QRYM_FORM")]
    public class MedicationForm
    {
        [Key]
        public int Id { get; set; }

        [Column("DRUG_CODE", TypeName = "numeric")]
        public int MedicationId { get; set; }

        [Column("PHARM_FORM_CODE", TypeName = "numeric")]
        public int FormCode { get; set; }

        [StringLength(50)]
        [Column("PHARMACEUTICAL_FORM")]
        public string PharmaceuticalForm { get; set; }

        [Column("isActive")]
        public bool IsActive { get; set; }

        [ForeignKey("MedicationId")]
        public Medication Medication { get; set; }
    }
}