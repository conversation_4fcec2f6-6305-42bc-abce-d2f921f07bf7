﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace Cerebrum.Data
{
    [Table(" PatientAllergyDins")]
    public class PatientAllergyDin
    {
        [Key, ForeignKey("PatientAllergy")]
        public int PatientAllergyId { get; set; }

        public PatientAllergy PatientAllergy { get; set; }

        public int MedicationId { get; set; }

        [ForeignKey("MedicationId")]
        public Medication Medication { get; set; }
        
    }
}