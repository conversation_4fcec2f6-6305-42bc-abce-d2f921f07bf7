﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [Table("AppointmentPriority")]
    [TrackChanges]
    public class AppointmentPriority
    {
        [Key]
        public int Id { get; set; }
        public int PracticeId { get; set; }
        [StringLength(20)]
        public string PriorityName { get; set; }
        public int Rank { get; set; }
        public bool IsActive { get; set; }

        [ForeignKey("PracticeId")]
        public virtual Practice Practices { get; set; }
    }
}
