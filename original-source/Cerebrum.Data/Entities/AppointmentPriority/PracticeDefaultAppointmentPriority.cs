﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [Table("PracticeDefaultAppointmentPriority")]
    [TrackChanges]
    public class PracticeDefaultAppointmentPriority
    {
        [Key]
        public int Id { get; set; }
        public int PracticeId { get; set; }
        public int AppointmentPriorityId { get; set; }

        [ForeignKey("PracticeId")]
        public virtual Practice Practices { get; set; }

        [ForeignKey("AppointmentPriorityId")]
        public virtual AppointmentPriority AppointmentPriorities { get; set; }
    }
}
