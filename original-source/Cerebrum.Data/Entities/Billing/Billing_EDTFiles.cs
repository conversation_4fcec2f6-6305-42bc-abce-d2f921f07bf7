﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Billing Sequence Number
    /// </summary>

    [TrackChanges]
    public class Billing_EDTFile
    {
        [Key]
        public int id { get; set; }
        public int officeId { get; set; }
        [StringLength(20)]
        public string edtGroupId { get; set; }
        public int practiceDoctorId { get; set; }
        [StringLength(20)]
        public string billingNumber { get; set; }
        public int specialty { get; set; }
        public int sequenceNumber { get; set; }
        [StringLength(16)]
        public string edtBatchId { get; set; }
        [StringLength(256)]
        public string claimFileName { get; set; }
        [StringLength(256)]
        public string confirmedFileName { get; set; }
        public int Error { get; set; }

        public int? billStatusId { get; set; } = null;
        [ForeignKey("billStatusId")]
        public virtual BillStatus billStatus { get; set; }

        public DateTime DateCreated { get; set; } = DateTime.Now;
        public DateTime? DateSubmitted { get; set; }
        public DateTime? DateSent { get; set; }
        public DateTime? DateReceived { get; set; }
        public DateTime? DateDownloaded { get; set; }
    }
}
