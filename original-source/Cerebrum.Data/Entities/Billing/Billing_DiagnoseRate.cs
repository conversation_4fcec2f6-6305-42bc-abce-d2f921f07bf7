﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// Diagnose Rate
    /// </summary>
    [TrackChanges]
    public class Billing_DiagnoseRate
    {
        [Key]
        public int id { get; set; }
        public int diagnoseCode { get; set; }
        [StringLength(255)]
        public string diagnose { get; set; }
        public int type { get; set; }
    }
}