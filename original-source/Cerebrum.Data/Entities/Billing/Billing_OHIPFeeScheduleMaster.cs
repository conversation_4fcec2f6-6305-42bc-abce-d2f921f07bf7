﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// OHIP Fee Schedule Master
    /// </summary>
    [TrackChanges]
    public class Billing_OHIPFeeScheduleMaster
    {
        [Key]
        public int id { get; set; }
        [StringLength(8)]
        public string scheduleCode { get; set; }
        public DateTime effectiveDate { get; set; }
        public DateTime terminationDate { get; set; }
        public int providerFee { get; set; }
        public int assistantFee { get; set; }
        public int specialistFee { get; set; }
        public int anaesthetistFee { get; set; }
        public int nonAnaesthetistFee { get; set; }
    }
}