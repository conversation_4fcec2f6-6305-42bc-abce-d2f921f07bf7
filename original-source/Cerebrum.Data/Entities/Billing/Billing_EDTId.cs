﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// Billing EDT IDS      in C2: table "mcedtid"
    /// </summary>
    [TrackChanges]
    public class Billing_EDTId  
    {
        [Key]
        public int id { get; set; }
        [StringLength(20)]
        public string edtId { get; set; }
        [StringLength(20)]
        public string billingGroup { get; set; }    //edt group id for group billing;  billing number for personal billing
        [StringLength(20)]
        public string billingNumber { get; set; }    //edt group id for group billing;  billing number for personal billing
    }
}