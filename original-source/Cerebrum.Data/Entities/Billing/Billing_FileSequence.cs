﻿using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// Billing Sequence Number
    /// </summary>
    [TrackChanges]
    public class Billing_FileSequence
    {
        [Key]
        public int id { get; set; }
        public int officeId { get; set; }
        [StringLength(20)]
        public string edtGroupId { get; set; }
        [StringLength(20)]
        public string billingNumber { get; set; }
        public int specialty { get; set; }
        public int sequenceNumber { get; set; }        
    }
}