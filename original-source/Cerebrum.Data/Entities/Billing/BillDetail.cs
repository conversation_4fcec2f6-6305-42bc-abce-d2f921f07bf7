﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// Bill
    /// </summary>
    [TrackChanges]
    public class BillDetail
    {
        [Key]
        public int id { get; set; }
        public int billId { get; set; }
        public int edtFileId { get; set; }

        [StringLength(1)]
        public string MOHCODE { get; set; }
        public DateTime date { get; set; }
        public int sequenceNumber { get; set; }
        public int officeId { get; set; }
        [StringLength(20)]
        public string edtGroupId { get; set; }
        public int practiceDoctorId { get; set; }
        [StringLength(20)]
        public string billingNumber { get; set; }
        public int specialty { get; set; }

        public int appointmentId { get; set; }
        public int hdAdmissionId { get; set; }     //hospital billing
        public int hdAdmissionActionId { get; set; }     //hospital billing
        public int hdServiceId { get; set; }     //hospital billing
        public int hdServiceCodeId { get; set; }     //hospital billing
        public int PatientRecordId { get; set; }
        public int referralDoctorId { get; set; }
        [StringLength(20)]
        public string claimNumber { get; set; } //appointmentId
        [StringLength(20)]
        public string healthCardNumber { get; set; }
        [StringLength(20)]
        public string healthCardVersion { get; set; }
        [StringLength(50)]
        public string firstName { get; set; }
        [StringLength(50)]
        public string lastName { get; set; }
        public Gender gender { get; set; }
        [StringLength(20)]
        public string province { get; set; }
        public DateTime dateOfBirth { get; set; }
        [StringLength(20)]
        public string referralBillingNumber { get; set; }
        [StringLength(6)]
        public string serviceLocationIndicator { get; set; }    //sli
        public PaymentMethod payment { get; set; }
        [StringLength(128)]
        public string payor { get; set; }
        public int payorId { get; set; } = 0;
        [StringLength(4)]
        public string hospitalCode { get; set; } = string.Empty;     //hospital billing
        [StringLength(8)]
        public string dateAdmitted { get; set; } = string.Empty;     //hospital billing
        [StringLength(1)]
        public string manualReview { get; set; } = string.Empty;     //hospital billing
        public int appointmentTestId { get; set; }
        public int appointmentBillId { get; set; }
        [StringLength(500)]
        public string service { get; set; }
        [StringLength(20)]
        public string serviceCode { get; set; }
        public int fee { get; set; }
        public Int16 numberOfServices { get; set; }
        public DateTime serviceDate { get; set; }
        [StringLength(20)]
        public string diagnoseCode { get; set; }
        public int? billingTypeId { get; set; } = null;
        public int? billStatusId { get; set; } = null;
        public int? ohipAmountPaid { get; set; }
        public DateTime? ohipPayDate { get; set; }
        [StringLength(8)]
        public string ohipExplanatoryCode { get; set; }
        [StringLength(24)]
        public string errorCode { get; set; }
        [StringLength(11)]
        public string MOHId { get; set; }   //claim number from OHIP      
        public DateTime? reconciledDate { get; set; }       
        public int cohortId { get; set; }

        [ForeignKey("billId")]
        public virtual Bill bill { get; set; }
        [ForeignKey("billStatusId")]
        public virtual BillStatus billStatus { get; set; }
    }
}
