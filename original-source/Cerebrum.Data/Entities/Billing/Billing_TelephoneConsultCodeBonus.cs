﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// Consult Rate
    /// </summary>
    [TrackChanges]
    public class Billing_TelephoneConsultCodeBonus
    {
        [Key]
        public int id { get; set; }
        public int telephoneConsultCodeId { get; set; }
        [StringLength(20)]
        public string bonusCode { get; set; }
        public int numberOfService { get; set; }
        [ForeignKey("telephoneConsultCodeId")]
        public virtual Billing_TelephoneConsultCode telephoneConsultCode { get; set; }
    }
}