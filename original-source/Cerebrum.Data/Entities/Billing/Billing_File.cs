﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// Billing Sequence Number
    /// </summary>

    [TrackChanges]
    public class Billing_File
    {
        [Key]
        public int id { get; set; }
        public int officeId { get; set; }
        [StringLength(20)]
        public string edtGroupId { get; set; }
        public int practiceDoctorId { get; set; }
        [StringLength(20)]
        public string billingNumber { get; set; }
        [StringLength(20)]
        public string UniqueId { get; set; }
        [StringLength(256)]
        public string fileName { get; set; } = string.Empty;
        public BillingFileType fileType { get; set; } = BillingFileType.Other;
        [StringLength(256)]
        public string Error { get; set; } = string.Empty;
        public DateTime DateEntered { get; set; } = DateTime.Now;
    }
}
