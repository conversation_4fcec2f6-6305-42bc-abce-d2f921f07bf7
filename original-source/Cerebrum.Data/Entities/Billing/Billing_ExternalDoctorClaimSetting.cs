﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Billing_ExternalDoctorClaimSettings
    /// </summary>
    [Table("Billing_ExternalDoctorClaimSettings")]
    [TrackChanges]  
    public class Billing_ExternalDoctorClaimSetting
    {
        [Key]
        public int Id { get; set; }
        public int ExternalDoctorId { get; set; }
        public bool isDownloadEnabled { get; set; } = true;
        public bool isUploadEnabled { get; set; } = true;
    }
}