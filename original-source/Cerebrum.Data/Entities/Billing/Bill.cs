﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Bill
    /// </summary>
    [TrackChanges]
    public class Bill
    {
        [Key]
        public int id { get; set; }
        public int practiceId { get; set; } = 0;
        public int officeId { get; set; } = 0;
        public int appointmentId { get; set; } = 0;     //appointment id that billed from invoice
        public int admissionActionId { get; set; } = 0;     //hospital billing
        public DateTime date { get; set; }
      
        public bool locked { get; set; }
    }
}