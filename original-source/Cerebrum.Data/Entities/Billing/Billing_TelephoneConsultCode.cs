﻿using System;
using System.ComponentModel.DataAnnotations;

using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    /// <summary>
    /// Consult Rate
    /// </summary>
    [TrackChanges]
    public class Billing_TelephoneConsultCode
    {
        [Key]
        public int id { get; set; }
        [StringLength(10)]
        public string telephoneConsultCode { get; set; }
        [StringLength(10)]
        public string consultCode { get; set; }
        public int numberOfService { get; set; }
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public int minimumAge { get; set; }
        public int maximumAge { get; set; }
    }
}