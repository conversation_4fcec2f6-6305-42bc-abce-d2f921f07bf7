﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// Consult Rate
    /// </summary>
    [TrackChanges]
    public class Billing_PreventiveCareBonusRate
    {
        [Key]
        public int id { get; set; }
        public int preventiveCareBonusCategoryId { get; set; }
        public int achievedComplianceRate { get; set; }
        public int fee { get; set; }
        [StringLength(10)]
        public string claimCode { get; set; }
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public DateTime createdDate { get; set; } = DateTime.Now;
    }
}