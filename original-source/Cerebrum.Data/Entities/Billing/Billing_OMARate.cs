﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// Test Rate
    /// </summary>
    [TrackChanges]
    public class Billing_OMARate
    {
        [Key]
        public int id { get; set; }
        public double rate { get; set; }
        public bool active { get; set; } = true;
        public DateTime startDate { get; set; } = DateTime.Now;
        public DateTime endDate { get; set; } = DateTime.Now;
        public DateTime createdDate { get; set; } = DateTime.Now;        
    }
}