﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// Bill Status
    /// </summary>
    [TrackChanges]
    public class BillStatus
    {
        [Key]
        public int id { get; set; }
        [StringLength(50)]
        public string name { get; set; }
        [StringLength(50)]
        public string color { get; set; }
        public int orderNumber { get; set; }
    }
}