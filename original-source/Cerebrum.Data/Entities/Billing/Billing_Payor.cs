﻿using Cerebrum.Data.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Bill Payor
    /// </summary>
    [TrackChanges]
    public class Billing_Payor
    {
        [Key]
        public int id { get; set; }
        [StringLength(250)]
        public string name { get; set; }
        [StringLength(250)]
        public string address { get; set; }
    }
}