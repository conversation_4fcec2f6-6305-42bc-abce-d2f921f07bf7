﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Billing Group
    /// </summary>
    [TrackChanges]
    public class Billing_Group
    {
        [Key]
        public int id { get; set; }
        [StringLength(20)]
        public string edtGroupId { get; set; }
        public int officeId { get; set; }
        [StringLength(6)]
        public string serviceLocationIndicator { get; set; }    //sli
        [StringLength(1)]
        public string MOHCODE { get; set; }
        [StringLength(50)]
        public string MCEDTMailbox { get; set; }
        [StringLength(50)]
        public string MCEDTPassword { get; set; }
        public bool IHFbilling { get; set; } = false;
        public bool isDownloadEnabled { get; set; } = true;
        public bool isUploadEnabled { get; set; } = true;
        [StringLength(20)]
        public string MasterNumber { get; set; } = string.Empty;
    }
}