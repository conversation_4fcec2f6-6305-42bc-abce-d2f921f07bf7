﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class HDService
    {

        public HDService()
        {
            HDServiceCodes = new List<HDServiceCode>();
        }
        [Key]
        public int Id { get; set; }

        public int AdmissionActionId { get; set; }

        [ForeignKey("AdmissionActionId")]
        public HDAdmissionAction AdmissionAction { get; set; }

        public int? BundleId { get; set; }

        [ForeignKey("BundleId")]
        public HospitalBundle HospitalBundle { get; set; }

        public int BillStatusId { get; set; }

        [ForeignKey("BillStatusId")]
        public BillStatus BillStatus { get; set; }

        public int? BonusCodeId { get; set; }

        [ForeignKey("BonusCodeId")]
        public Billing_BonusCode BonusCode { get; set; }

        public DateTime DateServiced { get; set; }
        public PaymentMethod PaymentMethod { get; set; }

        public int PracticeDoctorId { get; set; }        

        public bool IsActive { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }
        public int? LastModifiedBy { get; set; }

        public List<HDServiceCode> HDServiceCodes { get; set; }


    }
}
