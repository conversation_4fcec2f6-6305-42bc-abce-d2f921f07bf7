﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class HDServiceCode
    {
        [Key]
        public int Id { get; set; }

        public int HDServiceId { get; set; } // hospital daysheet service

        public HDService HDService { get; set; }

        //public int? HospitalBundleId { get; set; }

        //[ForeignKey("HospitalBundleId")]
        //public HospitalBundle HospitalBundle { get; set; }

        [StringLength(50)]
        public string Paid { get; set; }

        [StringLength(200)]
        public string Code { get; set; }

        [StringLength(1000)]
        public string Notes { get; set; }

        public int Fee { get; set; }
        
        public int NumberOfServices { get; set; }

        public int BillStatusId { get; set; }

        [ForeignKey("BillStatusId")]
        public BillStatus BillStatus { get; set; }

        //public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }

        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }
    }
}
