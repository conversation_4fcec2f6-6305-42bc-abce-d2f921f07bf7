﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class HDAdmissionAction
    {
        public HDAdmissionAction()
        {
            Services = new List<HDService>();
        }
        [Key]
        public int Id { get; set; }

        public int AdmissionId { get; set; }

        [ForeignKey("AdmissionId")]
        public HDAdmission Admission { get; set; }

        public int BillStatusId { get; set; }

        [ForeignKey("BillStatusId")]
        public BillStatus BillStatus { get; set; }

        public int? BonusCodeId { get; set; }

        [ForeignKey("BonusCodeId")]
        public Billing_BonusCode BonusCode { get; set; }

        public DateTime DateServiced { get; set; }        

        public bool IsActive { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }
        public int? LastModifiedBy { get; set; }

        public List<HDService> Services { get; set; }
    }
}
