﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class HospitalBundle
    {
        public HospitalBundle()
        {
            ServiceCodes = new List<HospitalServiceCode>();
        }

        [Key]
        public int Id { get; set; }

        public int? HospitalBundleTypeId { get; set; }

        [ForeignKey("HospitalBundleTypeId")]
        public HospitalBundleType HospitalBundleType { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        [StringLength(200)]
        public string ExternalName { get; set; }       

        public int? DisplayOrder { get; set; }

        public bool IsActive { get; set; }

        public List<HospitalServiceCode> ServiceCodes { get; set; }
    }
}
