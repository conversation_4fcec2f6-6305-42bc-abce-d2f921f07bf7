﻿using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class HDAdmission
    {
        public HDAdmission()
        {
            AdmissionActions = new List<HDAdmissionAction>();
        }
        [Key]
        public int Id { get; set; }

        public int PatientRecordId { get; set; }

        [ForeignKey("PatientRecordId")]
        public PatientRecord PatientRecord { get; set; }

        public int AdmissionTypeId { get; set; }

        [ForeignKey("AdmissionTypeId")]
        public HDAdmissionType AdmissionType { get; set; }


        public int DiagnosisCodeId { get; set; }

        [ForeignKey("DiagnosisCodeId")]
        public DiagnoseCode DiagnoseCode { get; set; }

        public int BillStatusId { get; set; }

        [ForeignKey("BillStatusId")]
        public BillStatus BillStatus { get; set; }

        public DateTime DateAdmitted { get; set; }
        public DateTime DateDischarge { get; set; }

        public int? ReferralDoctorId { get; set; } //external doctor id

        public int PracticeDoctorId { get; set; }

        public int HospitalId { get; set; }

        [ForeignKey("HospitalId")]
        public Hospital Hospital { get; set; }

        public int StatusId { get; set; }

        public bool IsActive { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }
        public int? LastModifiedBy { get; set; }

        public List<HDAdmissionAction> AdmissionActions { get; set; }
       
    }
}
