﻿using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [Table("UserTimesheet")]
    [TrackChanges]
    public class UserTimesheet
    {
        [Key]
        public int Id { get; set; }
        public int? UserTimesheetId { get; set; }
        public string IPAddress { get; set; }

        public int UserId { get; set; }
        
        public DateTime? ScheduledStartTime { get; set; }
        public DateTime? ScheduledFinishTime { get; set; }
        public DateTime PunchDateTime { get; set; }
        public int punchedByUserId { get; set; }
        public PunchTimeCard PunchTimeCard { get; set; }
        [StringLength(500)]
        public string Notes { get; set; }
        public bool Approved { get; set; } = false;
        public int? ApprovedByUserId { get; set; }
        public DateTime CreatedDateTime { get; set; }
        public DateTime UpdatedDateTime { get; set; }
        public bool IsActive { get; set; } = true;
        [ForeignKey("UserTimesheetId")]
        public virtual UserTimesheet userTimesheet { get; set; }
        public virtual List<UserTimesheet> records { get; set; }
        public UserTimesheet Clone()
        {
            return (UserTimesheet)this.MemberwiseClone();
        }
    }
}
