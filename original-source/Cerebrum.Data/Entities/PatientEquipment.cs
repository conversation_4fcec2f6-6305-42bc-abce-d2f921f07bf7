﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class PatientEquipment
    {
        public PatientEquipment()
        {
            DateCreated = System.DateTime.Now;
        }
        [Key]
        public int Id { get; set; }

        public int PatientRecordId { get; set; }
        [ForeignKey("PatientRecordId")]
        public PatientRecord PatientRecord { get; set; }
        public int AppointmentTestId { get; set; }
        [ForeignKey("AppointmentTestId")]
        public AppointmentTest AppointmentTest { get; set; }
        public int InventoryId { get; set; }                
        [ForeignKey("InventoryId")]
        public StoreInventory InventoryItem { get; set; }

        public int AssignedByUserId { get; set; }
        public int? ReturnedByUserId { get; set; } // user who set the device as return or who took the device back from patient

        [StringLength(500)]
        public string Notes { get; set; }
        public DateTime DateStarted { get; set; }

        public DateTime? DateExpectedReturn { get; set; }

        public DateTime? DateReturned { get; set; }

        public DateTime DateCreated { get; set; }

        public DateTime? DateLastModified { get; set; }

        public bool IsActive { get; set; }

    }
}
