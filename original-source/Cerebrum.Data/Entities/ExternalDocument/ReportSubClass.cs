﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    [TrackChanges]
    /// <summary>
    /// Report sub_Classes
    /// </summary>
    public class ReportSubClass
    {
        [Key]
        public int Id { get; set; }
        [StringLength(128)]
        public string name { get; set; }
        public int ReportClassId { get; set; }
        public virtual ReportClass ReportClass { get; set; }
    }
}
