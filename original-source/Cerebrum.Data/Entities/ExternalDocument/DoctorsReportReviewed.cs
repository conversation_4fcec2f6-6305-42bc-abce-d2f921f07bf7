﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class DoctorsReportReviewed
    {
        [Key]
        public int Id { get; set; }
        public int practiceDoctorId { get; set; } //internal doctor Id
        public DocumentAssignmentStatuses assignmentStatus { get; set; } = DocumentAssignmentStatuses.Unassigned;
        public Nullable<DateTime> dateTimeReportReviewed { get; set; }
        [StringLength(250)]
        public string hrmXmlFileName { get; set; }
        [StringLength(1024)]
        public string ReportReviewedNotes { get; set; }
        public int ReportReceivedId { get; set; }
        public virtual ReportReceived ReportReceived { get; set; }
    }
}
