﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    /// <summary>
    /// Report Received details
    /// </summary>
    [TrackChanges]

    public class ReportReceived
    {
        [Key]
        public int Id { get; set; }
        public ReportType reportType { get; set; } = ReportType.Fax;
        public DateTime? testDate { get; set; }
        public int? fileSize { get; set; }
        [StringLength(250)]
        public string fileType { get; set; }
        private string _fileName;
        [StringLength(250)]
        public string fileName { get { return _fileName; } set { _fileName = value.ToLower(); } }
        [StringLength(20)]
        public string userIp { get; set; }
        public int officeId { get; set; }
        public int? looseReportCategoryId { get; set; }
        public bool? abnormal { get; set; }
        private string _url;
        [StringLength(512)]
        public string url { get { return _url; } set { _url = value.ToLower(); } }
        public bool markSeen { get; set; }
        public DateTime? seenDateTime { get; set; }
        [StringLength(128)]
        public string userId { get; set; } // seen by this user

        public ReportMedia? media { get; set; }
        public bool? mediaSpecified { get; set; }
        public ReportFormat? reportFormat { get; set; }
        [StringLength(64)]
        public string fileExtensionAndVersion { get; set; }
        public DateTime? eventDateTime { get; set; }
        public DateTime? receivedDateTime { get; set; }
        [StringLength(128)]
        public string sourceAuthorPhysician { get; set; }
        [StringLength(128)]
        public string sourceFacility { get; set; }
        [StringLength(128)]
        public string sendingFacilityId { get; set; }//?
        [StringLength(512)]
        public string sendingFacilityReport { get; set; }
        //public ReportsReceivedOBRContent[] oBRContent { get; set; }
        [StringLength(64)]
        public string hRMResultStatus { get; set; }
        [StringLength(256)]
        public string messageUniqueID { get; set; }
        [StringLength(1024)]
        public string description { get; set; }
        public int? reportClassId { get; set; }
        //public virtual ReportClass reportClass { get; set; }
        public int? reportSubClassId { get; set; }
        //public virtual ReportSubClass reportSubClass { get; set; }
        public int? appointmentId { get; set; }

        public virtual Appointment appointment { get; set; }
        public int? testId { get; set; } // Tests  Under Master 
        public DocumentAssignmentStatuses assignmentStatus { get; set; } = DocumentAssignmentStatuses.Unassigned;
        [DefaultValue(true)]
        public bool? status { get; set; } = true;

        [StringLength(1024)]
        public string acompCPSO { get; set; }
        [StringLength(1024)]
        public string AccompanyingSubClass { get; set; }
        [StringLength(1024)]
        public string AccompanyingMnemonic { get; set; }
        [StringLength(1024)]
        public string AccompanyingDescription { get; set; }
        public DateTime? ObservationDateTime { get; set; }
        public DateTime? creationDate { get; set; }
        public DateTime? SentDateTime { get; set; }
        [StringLength(32)]
        public string hrmVersion { get; set; }
        [StringLength(64)]
        public string contentCheckSum { get; set; }
        [StringLength(400)]
        public string ResidualInfo { get; set; }
        public virtual List<DoctorsReportReviewed> DoctorsReportReviewed { get; set; }
        public int PatientRecordId { get; set; }
        public virtual PatientRecord PatientRecord { get; set; }
    }
}
