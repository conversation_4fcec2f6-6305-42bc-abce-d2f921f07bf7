﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    [TrackChanges]
    /// <summary>
    /// Report Accompanying Subclass
    /// </summary>
    public class ReportAccompanyingSubclass
    {
        [Key]
        public int Id { get; set; }
        public int ReportClassId { get; set; }
        [StringLength(128)]
        public string name { get; set; }
    }
}
