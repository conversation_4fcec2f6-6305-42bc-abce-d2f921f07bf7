﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    /// <summary>
    /// Report Received details
    /// </summary>
    [Table("ReportReceivedAppointments")]
    [TrackChanges]

    public class ReportReceivedAppointment
    {
        [Key]
        public int Id { get; set; }
        public int ReportReceivedId { get; set; }
        public int AppointmentId { get; set; }

        [ForeignKey("AppointmentId")]
        public virtual Appointment Appointment { get; set; }

        [ForeignKey("ReportReceivedId")]
        public virtual ReportReceived ReportReceived { get; set; }
    }
}
