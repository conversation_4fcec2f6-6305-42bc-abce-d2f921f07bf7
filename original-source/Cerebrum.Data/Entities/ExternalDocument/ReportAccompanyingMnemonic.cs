﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    [TrackChanges]
    /// <summary>
    /// Report Accompanying Mnemonic
    /// </summary>
    public class ReportAccompanyingMnemonic
    {
        [Key]
        public int Id { get; set; }
        public int AccompanyingSubclassId { get; set; }
        [StringLength(128)]
        public string name { get; set; }
        [StringLength(256)]
        public string description { get; set; }
    }
}
