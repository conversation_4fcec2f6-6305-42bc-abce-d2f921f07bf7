﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    [TrackChanges]
    /// <summary>
    /// Sending Facility
    /// </summary>
    public class ReportSendingFacility
    {
        [Key]
        public int Id { get; set; }
        [StringLength(32)]
        public string facilityId { get; set; }
        [StringLength(128)]
        public string facilityName { get; set; }
    }
}
