﻿using Cerebrum.Data.Attributes;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Cerebrum.Data
{
    [TrackChanges]
    /// <summary>
    /// Report Classes
    /// </summary>
    public class ReportClass
    {
        [Key]
        public int Id { get; set; }
        [StringLength(128)]
        public string name { get; set; }
        public int cds_classId { get; set; }
        public virtual List<ReportSubClass> ReportSubClass { get; set; }
        public ReportClass()
        {
            this.ReportSubClass = new List<ReportSubClass>();
        }
    }
}
