﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data
{
    public class ExternalDoctorLocation
    {
        public ExternalDoctorLocation()
        {
            DateCreated = System.DateTime.Now;
            IsActive = true;
        }
        [Key]
        public int Id { get; set; }

        [Index("IX_ExternalDoctorLocation", 1, IsUnique = true)]
        public int ExternalDoctorAddressId { get; set; } // external doctor address id
        [ForeignKey("ExternalDoctorAddressId")]
        public ExternalDoctorAddress ExternalDoctorDoctorAddress { get; set; }

        [Index("IX_ExternalDoctorLocation", 2, IsUnique = true)]
        public int ExternalDoctorPhoneNumberId { get; set; } // external doctor phone number id
        [ForeignKey("ExternalDoctorPhoneNumberId")]
        public ExternalDoctorPhoneNumber ExternalDoctorPhoneNumber { get; set; }
        public bool IsActive { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime? DateLastModified { get; set; }

        public int? LastModifiedByUserId { get; set; }
    }
}
