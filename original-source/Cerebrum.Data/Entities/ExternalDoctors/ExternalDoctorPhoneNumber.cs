﻿using Cerebrum.Data.Attributes;
using AwareMD.Cerebrum.Shared.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ExternalDoctorPhoneNumber
    {
        public ExternalDoctorPhoneNumber()
        {
            IsActive = true;
        }
        [Key]
        public int Id { get; set; }
        [StringLength(20)]
        public string phoneNumber { get; set; }
        [StringLength(20)]
        public string extention { get; set; }
        [StringLength(20)]
        public string faxNumber { get; set; }
        public PhoneNumberType typeOfPhoneNumber { get; set; }

        public virtual int ExternalDoctorId { get; set; }
        public virtual ExternalDoctor ExternalDoctor { get; set; }

        public bool IsActive { get; set; }
    }
}
