﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    ////[Table("ExternalDoctor")]
    /// <summary>
    ///  External Doctor table
    ///  External Doctor - a doctor in Canada
    ///  Family doctor from the list of External Doctors -> Demographics
    ///  Reference doctor from the list External Doctors -> to Appointment
    /// </summary>
    [TrackChanges]
    public class ExternalDoctor
    {
        [Key]
        public int Id { get; set; }
        [StringLength(10)]
        public string Initials { get; set; }
        public TeamMember Team { get; set; } = TeamMember.Cardiology;
        [StringLength(512)]
        public string BlueCross { get; set; }

        [StringLength(512)]
        public string RegionalCode { get; set; }
        [StringLength(1000)]
        public string LetterHead { get; set; }
        [StringLength(150)]
        public string Degrees { get; set; }
        public int SpecialtyId { get; set; }


        [StringLength(255)]
        public string Cell { get; set; }
        [StringLength(50)]
        public string MCEDTMailbox { get; set; }
        [StringLength(50)]
        public string MCEDTPassword { get; set; }
        [StringLength(50)]
        public string MCEDTId { get; set; }
        public HospitalBilling HospBilling { get; set; } = HospitalBilling.Inactive;
        public DiagnosticVPSet DiagnosticSet { get; set; } = DiagnosticVPSet.Cardiology;
        public Template LetterTemplate { get; set; } = Template.Regular;
        public Template ReportTemplate { get; set; } = Template.Regular;
        [StringLength(255)]
        public string OHIPPhysicianId { get; set; }
        [StringLength(50)]
        public string firstName { get; set; }
        [StringLength(50)]
        public string middleName { get; set; }
        [StringLength(50)]
        public string lastName { get; set; }
        [Display(Name = "Physician Name")]
        public string FullName
        {
            get { return "Dr. " + lastName + ", " + firstName; }
        }
        //public string TrimFullName
        //{
        //    get { return 
        //            (string.IsNullOrWhiteSpace(lastName) && string.IsNullOrWhiteSpace(firstName) )
        //            ?"": lastName.Trim() + "," + firstName.Trim();
        //    }
        //}
        [StringLength(10)]
        public string CPSO { get; set; }
        [StringLength(255)]
        public string HRMId { get; set; }
        [StringLength(250)]
        public string description { get; set; }
        [StringLength(256)]
        public string comment { get; set; }
        [StringLength(50)]
        public string emailAddress { get; set; }
        public bool locked { get; set; } = false;
        public bool active { get; set; } = true;
        public bool fax { get; set; } = true;
        public bool mail { get; set; }
        public bool email { get; set; }
        public bool byphone { get; set; }
        public bool HRM { get; set; } = false;
        [StringLength(255)]
        public string password { get; set; } // for external doctor terminal
        [StringLength(10)]
        public string PhysicianType { get; set; }
        [StringLength(30)]
        public string HrmMnemonic { get; set; }
        public virtual List<ExternalDoctorPhoneNumber> phoneNumbers { get; set; }
        public virtual List<ExternalDoctorAddress> externalDoctorAddresses { get; set; }
        public ExternalDoctor()
        {
            this.phoneNumbers = new List<ExternalDoctorPhoneNumber>();
            this.externalDoctorAddresses = new List<ExternalDoctorAddress>();
        }

        public int MasterId { get; set; }
        public virtual Master Master { get; set; }
    }
}
