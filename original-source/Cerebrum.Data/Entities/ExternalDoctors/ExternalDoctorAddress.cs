﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Attributes;

namespace Cerebrum.Data
{
    [TrackChanges]
    public class ExternalDoctorAddress
    {
        public ExternalDoctorAddress()
        {
            IsActive = true;
        }
        [Key]
        public int Id { get; set; }
        [StringLength(250)]
        public string addressName { get; set; } // location name for this address
        [StringLength(150)]
        public string addressLine1 { get; set; }
        [StringLength(150)]
        public string addressLine2 { get; set; }
        [StringLength(100)]
        public string faxNumber { get; set; }
        [StringLength(100)]
        public string city { get; set; }
        [StringLength(10)]
        [Description("PostalCode/ZipCode")]
        public string postalCode { get; set; }

        [StringLength(100)]
        [Description("countrySubdivisionCode Province/Province")]
        public string province { get; set; }
        [StringLength(100)]
        public string country { get; set; }
        public AddressTypes addressType { get; set; }

        public virtual int ExternalDoctorId { get; set; }
        public virtual ExternalDoctor ExternalDoctor { get; set; }
        public bool IsActive { get; set; }
    }
}
