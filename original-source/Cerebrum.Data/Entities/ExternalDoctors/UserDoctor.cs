﻿using Cerebrum.Data.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace Cerebrum.Data
{
    [TrackChanges]
    public class UserDoctor
    {
        [Key]
        public int Id { get; set; }


        [StringLength(128)]
        public string ApplicationUserId { get; set; }
        public int ExternalDoctorId { get; set; }
    }

    [TrackChanges]
    public class UserBillingDoctor
    {
        [Key]
        public int Id { get; set; }

        [StringLength(128)]
        public string ApplicationUserId { get; set; }
        public int ExternalDoctorId { get; set; }
    }
}
