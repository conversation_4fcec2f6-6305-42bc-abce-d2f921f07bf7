﻿using System;
using System.Collections.Generic;
using System.Linq;
using Cerebrum.Data;
using Cerebrum3.Infrastructure;

namespace Cerebrum3.DataAccess
{
    public class AppointmentPriorityRepository : GenericRepository<CerebrumContext, AppointmentPriority>, IAppointmentPriorityRepository
    {
        public List<AppointmentPriority> GetPracticePriorityList(int practiceId)
        {
            return this.FindBy(x => x.PracticeId == practiceId).ToList();
        }
        public AppointmentPriority GetPracticePriorityByName(int practiceId, string priorityName)
        {
            return this.FindBy(x => x.PracticeId == practiceId && x.PriorityName.Equals(priorityName, StringComparison.CurrentCultureIgnoreCase)).FirstOrDefault();
        }
    }
}
