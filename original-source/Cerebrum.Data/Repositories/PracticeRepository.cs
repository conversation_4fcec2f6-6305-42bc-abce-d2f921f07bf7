﻿using System.Linq;
using Cerebrum.Data;
using Cerebrum3.Infrastructure;

namespace Cerebrum3.DataAccess
{
    //TODO: This class should not be here, it should be in the Cerebrum.Infrastructure project, but at this moment it is not possible to place it there, pending relocation
    public class PracticeRepository : GenericRepository<CerebrumContext, Practice>, IPracticeRepository
    {
        public Practice GetSingle(int practiceId) {
            var query = context.Practices.FirstOrDefault(x => x.Id == practiceId);
            return query;
        }
    }
}

