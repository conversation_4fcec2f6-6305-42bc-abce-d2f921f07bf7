﻿using System.Data.Entity.Migrations;
using System.Linq;
using Cerebrum.Data.Infrastructure;
using Cerebrum3.Infrastructure;

namespace Cerebrum.Data.Repositories
{
    //TODO: this class is duplicate in cerebrum30, but it's not possible to move the original class to here by coupling level that exists
    public class UserRepository : GenericRepository<CerebrumContext, ApplicationUser>, IUserRepository
    {
        public ApplicationUser GetUserById(int userId)
        {
            return context.Users.FirstOrDefault(u => u.UserID == userId);
        }

        public void UpdateUser(ApplicationUser user)
        {
            context.Users.AddOrUpdate(user);
            context.SaveChanges();
        }
    }
}
