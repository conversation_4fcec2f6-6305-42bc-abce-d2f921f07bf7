﻿using System;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using Cerebrum.Data;

namespace Cerebrum3.Infrastructure
{
    //TODO: This class should not be here, it should be in the Cerebrum.Infrastructure project, but at this moment it is not possible to place it there, pending relocation
    public abstract class GenericRepository<C, T> : IGenericRepository<T> where T : class where C : CerebrumContext, new()
    {

        private C _entities = new C();
        protected T _entity = null;

        protected C context
        {
            get { return _entities; }
            set { _entities = value; }
        }

        public virtual IQueryable<T> GetAll()
        {
            IQueryable<T> query = _entities.Set<T>();
            //var test = query.ToList();
            return query;
        }
        public IQueryable<T> Find(Expression<Func<T, bool>> predicate)
        {
            IQueryable<T> query = _entities.Set<T>().Where(predicate);
            return query;
        }
        public IQueryable<T> FindBy(Expression<Func<T, bool>> predicate)
        {

            var type = _entities.Set<T>().GetType();


            if (ValidateRequest(predicate))
            {
                return _entities.Set<T>().Where(predicate);
            }
            else
            {
                HttpContext.Current.Server.ClearError();
                //HttpContext.Current.Response.Headers.Clear();
                HttpContext.Current.Response.Redirect("/Home/Access", false);
                return null;
                //throw new Exception("Access rights violation", new Exception("The current user has no permission to access the data requested."));
            }
        }
        public T GetSingleById(int id)
        {
            return _entities.Set<T>().Find(id);
        }
        public virtual void Add(T entity)
        {
            _entity = entity;
            _entities.Set<T>().Add(entity);
        }

        public virtual void Delete(T entity)
        {
            _entity = entity;
            _entities.Set<T>().Remove(entity);
        }

        public virtual void Edit(T entity)
        {
            _entity = entity;
            _entities.Entry(entity).State = EntityState.Modified;
        }
        public virtual void Save()
        {
            ////"System.Data.Entity.DbSet`1[Cerebrum30.DBClasses.Medications.Medication]"  myString.Split('/').Last();
            //DataAccessInterface dataAccess = new Cerebrum30.API.DataAccess.MedicationsDataAccess((HttpContext.Current.Request.IsAuthenticated) ? HttpContext.Current.User : null,
            //                                                            HttpContext.Current.Request.RawUrl,
            //                                                            HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] ?? HttpContext.Current.Request.UserHostAddress,
            //                                                            (_entities.Set<T>().GetType().ToString().Split('.').Last()).Replace("]", ""));
            //if (dataAccess.CanSave())
            //{
            //    _entities.SaveChanges((HttpContext.Current.Request.IsAuthenticated) ? HttpContext.Current.User.Identity.Name : "Anonymous");
            //}
            //else
            //{
            //    //TODO: Show a screen with a message - can't save data.
            //}
            _entities.SaveChanges();
        }
        public virtual void Save(int userId,string ipaddress)
        {
            _entities.SaveChanges(userId,ipaddress);
        }
        private bool disposed = false;
        protected virtual void Dispose(bool disposing)
        {
            if (!this.disposed)
                if (disposing)
                    _entities.Dispose();

            this.disposed = true;
        }
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        public DbSet<T> GetEntity()
        {
            return _entities.Set<T>();
        }

        public bool ValidateRequest(Expression<Func<T, bool>> predicate)
        {
            //string methodName = MethodBase.GetCurrentMethod().Name;
            ////string methodName = method.Name;
            ////string className = method.ReflectedType.Name;
            //var predicParams = predicate.Parameters;
            //var TypeName = predicate.Parameters[0].Type.Name; //"PatientRecord" = table name with PracticeId - get all pat. records for practice with id = value (4);
            //var NodeType = predicate.Parameters[0].NodeType; //Parameter
            //var pr = predicate.Parameters[0]; // "pr" - parameter
            //var prBody = predicate.Body;
            ////var testSelect = Context.(predicate.Parameters[0].Type.Name).
            //MemberExpression right = (MemberExpression)((BinaryExpression)predicate.Body).Right;
            //MemberExpression left = (MemberExpression)((BinaryExpression)predicate.Body).Left;
            //var rightTypeName = right.Type.Name; //"Int32"
            //var rightMemberName = right.Member.Name; //"PracticeId"
            //var leftTypeName = left.Type.Name; //"Int32"
            //var leftMemberName = left.Member.Name; // "PracticeId"
            //var record = -1;
            //try
            //{
            //    record = (int)Expression.Lambda(right).Compile().DynamicInvoke();
            //}
            //catch
            //{
            //    record = (int)Expression.Lambda(left).Compile().DynamicInvoke();
            //}

            //string URLAccessed = HttpContext.Current.Request.RawUrl;
            //string IPAddress = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] ?? HttpContext.Current.Request.UserHostAddress;
            //IPrincipal User = (HttpContext.Current.Request.IsAuthenticated) ? HttpContext.Current.User : null;

            //// for now not all rules are implemented, so we assume that if we do not check the access then access should be granted
            //bool valid = true;
            //switch (TypeName)
            //{
            //    case "QRYM_DRUG":
            //    case "QRYM_FORM":
            //    case "QRYM_ingred":
            //    case "QRYM_ROUTE":
            //    case "QRYM_THERAPEUTIC_CLASS":
            //    case "PatientMedication":
            //        dataAccess = new MedicationsDataAccess(User, URLAccessed, IPAddress, TypeName, record, rightMemberName, leftMemberName);
            //        valid = dataAccess.CheckCredentials();
            //        break;
            //    case "PatientRecord":
            //        dataAccess = new PatientRecordDataAccess(User, URLAccessed, IPAddress, TypeName, record, rightMemberName, leftMemberName);
            //        valid = dataAccess.CheckCredentials();
            //        break;
            //    default:
            //        break;
            //}
            // //return valid ? true : false;

            return true;
        }

       
    }
}