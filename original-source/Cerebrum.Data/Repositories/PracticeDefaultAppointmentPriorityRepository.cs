﻿using System.Collections.Generic;
using System.Linq;
using Cerebrum.Data;
using Cerebrum3.Infrastructure;

namespace Cerebrum3.DataAccess
{
    public class PracticeDefaultAppointmentPriorityRepository : GenericRepository<CerebrumContext, PracticeDefaultAppointmentPriority>, IPracticeDefaultAppointmentPriorityRepository
    {
        public PracticeDefaultAppointmentPriority GetPracticePriorityDefault(int practiceId)
        {
            return this.FindBy(x => x.PracticeId == practiceId).FirstOrDefault();
        }
    }
}
