﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.InterfaceLog
{
   
    public class InterfaceLogContext : DbContext
    {        
        public InterfaceLogContext() : base("name=C3InterfaceContext")
        {
            Database.SetInitializer<InterfaceLogContext>(null);
            base.Configuration.ProxyCreationEnabled = false;            
        }
        
        public static InterfaceLogContext Create()
        {
            return new InterfaceLogContext();
        }
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Properties<System.DateTime>().Configure(c => c.HasColumnType("datetime2"));            
        }
       
        public DbSet<InterfaceLog> InterfaceLogs { get; set; }
        public DbSet<PracticeCommTypeLog> PracticeCommTypeLogs { get; set; }


        public IEnumerable<T> GetData<T>(string storedProcedure, List<SqlParameter> parameters = null) where T : class
        {
            List<T> list = new List<T>();
            using (System.Data.Common.DbCommand cmd = Database.Connection.CreateCommand())
            {
                cmd.CommandText = storedProcedure;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                //cmd.CommandTimeout = sqlCommandTimeOut;
                try
                {
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    cmd.Connection.Open();
                    list = ((IObjectContextAdapter)this).ObjectContext.Translate<T>(cmd.ExecuteReader()).ToList();
                }
                finally
                {
                    if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                        cmd.Connection.Close();
                }

            }
            return list;
        }
    }
        
    public class C3InterfaceLogInitializer : DropCreateDatabaseIfModelChanges<InterfaceLogContext> 
    {       
        protected override void Seed(InterfaceLogContext context)
        {
            base.Seed(context);
        }
    }
}
