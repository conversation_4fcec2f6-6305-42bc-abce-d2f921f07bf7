﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.Data.InterfaceLog
{
    public class PracticeCommTypeLog
    {
        public int Id { get; set; }

        public DateTime LastRunDate { get; set; }

        public int UserId { get; set; }

        [StringLength(50)]
        public string IpAddress { get; set; }
    }
}
