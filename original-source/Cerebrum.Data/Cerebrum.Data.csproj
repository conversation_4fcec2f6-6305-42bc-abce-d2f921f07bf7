﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{EFD7CFF2-C670-499D-8123-A6692F4EF798}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Cerebrum.Data</RootNamespace>
    <AssemblyName>Cerebrum.Data</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Uat|AnyCPU'">
    <OutputPath>bin\Uat\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Staging|AnyCPU'">
    <OutputPath>bin\Staging\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugCli|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugCli\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugSharedDb|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugSharedDb\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugCliSharedDb|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugCliSharedDb\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=6.1.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.6.1.1\lib\net45\AutoMapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="AwareMD.Cerebrum.Shared, Version=1.1.7.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AwareMD.Cerebrum.Shared.1.1.7\lib\net461\AwareMD.Cerebrum.Shared.dll</HintPath>
    </Reference>
    <Reference Include="AwareMD.DHDR.Dto, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AwareMD.DHDR.Dto.1.0.25\lib\netstandard2.0\AwareMD.DHDR.Dto.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.1\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.1\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.1\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=2.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.2.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=2.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.2.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=2.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.2.1.0\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=2.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.2.1.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Attributes\AuditChangesAttribute.cs" />
    <Compile Include="Audit\Audit.cs" />
    <Compile Include="Audit\AuditPage.cs" />
    <Compile Include="Audit\CerebrumAuditContext.cs" />
    <Compile Include="Audit\DemographicSync.cs" />
    <Compile Include="Audit\Log.cs" />
    <Compile Include="Audit\PatientRecordActivity.cs" />
    <Compile Include="Audit\PerformanceLog.cs" />
    <Compile Include="Audit\UserSync.cs" />
    <Compile Include="Audit\VirtualVisitLogs.cs" />
    <Compile Include="Audit\VMAudit.cs" />
    <Compile Include="DataAccess\CerebrumContext.cs" />
    <Compile Include="DataAccess\CerebrumFormBuilderContext.cs" />
    <Compile Include="DataAccess\CerebrumRADContext.cs" />
    <Compile Include="Entities\AppointmentPriority\AppointmentPriority.cs" />
    <Compile Include="Entities\AppointmentPriority\PracticeDefaultAppointmentPriority.cs" />
    <Compile Include="Entities\Billing\Billing_File.cs" />
    <Compile Include="Entities\Billing\Billing_ExternalDoctorClaimSetting.cs" />
    <Compile Include="Entities\Billing\Billing_TelephoneConsultCode.cs" />
    <Compile Include="Entities\Billing\Billing_TelephoneConsultCodeBonus.cs" />
    <Compile Include="Entities\Billing\Billing_OMARate.cs" />
    <Compile Include="Entities\CDS\CDSqueue.cs" />
    <Compile Include="Entities\Common\CancellationReason.cs" />
    <Compile Include="Entities\Dashboard\ExpiredUserSession.cs" />
    <Compile Include="Entities\DHDR\DHDRCommunicationLog.cs" />
    <Compile Include="Entities\Econsults\Econsult.cs" />
    <Compile Include="Entities\Econsults\EconsultAttachedFile.cs" />
    <Compile Include="Entities\Econsults\EconsultMetadata.cs" />
    <Compile Include="Entities\Econsults\EconsultMetadataNote.cs" />
    <Compile Include="Entities\Econsults\EconsultMetadataNoteFile.cs" />
    <Compile Include="Entities\Econsults\EconsultOneIdSession.cs" />
    <Compile Include="Entities\Econsults\EconsultPatientAssociation.cs" />
    <Compile Include="Entities\Econsults\EconsultPatientAssociationLog.cs" />
    <Compile Include="Entities\Econsults\EconsultPatientDisassociationReason.cs" />
    <Compile Include="Entities\Econsults\EconsultSupportedFileType.cs" />
    <Compile Include="Entities\Econsults\EconsultUAO.cs" />
    <Compile Include="Entities\Econsults\EconsultUAOType.cs" />
    <Compile Include="Entities\Econsults\EconsultUserUAO.cs" />
    <Compile Include="Entities\Econsults\OntarioHealthService.cs" />
    <Compile Include="Entities\Econsults\UaoService.cs" />
    <Compile Include="Entities\Econsults\UaoServiceScope.cs" />
    <Compile Include="Entities\ExternalDocument\ReportReceivedAppointment.cs" />
    <Compile Include="Entities\FormBuilder\PdfValueIndex.cs" />
    <Compile Include="Entities\Master\HL7\HL7LostReport.cs" />
    <Compile Include="Entities\Master\HL7\LabUser.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentsAssociation.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Patient\PatientAlert.cs" />
    <Compile Include="Entities\Master\Practices\Practice\OfficeOutlook.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeDoctorReportFooter.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeDoctorReportSignature.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeDoctorType.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeExternalDoctor.cs" />
    <Compile Include="Entities\Measurement\WS_MicroservicesConfig.cs" />
    <Compile Include="Entities\ReportQueue\ReportQueue.cs" />
    <Compile Include="Entities\ReportQueue\ReportQueueResend.cs" />
    <Compile Include="Entities\ReportQueue\ReportQueueSendStatus.cs" />
    <Compile Include="Entities\ReportQueue\ReportQueueSendAttempt.cs" />
    <Compile Include="Entities\Study\DoctorStudy.cs" />
    <Compile Include="Entities\Study\TAPPPatient.cs" />
    <Compile Include="Entities\Study\HFiDOCPatient.cs" />
    <Compile Include="Entities\Study\TAPPPatientMedication.cs" />
    <Compile Include="Entities\Study\HFiDOCPatientMedication.cs" />
    <Compile Include="Entities\Study\Study.cs" />
    <Compile Include="Entities\TestBase\DoctorRootCategory.cs" />
    <Compile Include="Entities\TestBase\DoctorRootCategoryPhrase.cs" />
    <Compile Include="Entities\TestBase\DoctorRootCategoryTemplate.cs" />
    <Compile Include="Entities\TestBase\PracticeRootCategory.cs" />
    <Compile Include="Entities\TestBase\PracticeRootCategoryTemplate.cs" />
    <Compile Include="Entities\TestBase\RootCategoryPhraseUsed.cs" />
    <Compile Include="Entities\TestBase\RootCategoryTemplate.cs" />
    <Compile Include="Entities\TestBase\RootCategoryPhrase.cs" />
    <Compile Include="Entities\TestBase\PracticeRootCategoryPhrase.cs" />
    <Compile Include="Entities\TestBase\RootTemplate.cs" />
    <Compile Include="Entities\VirtualVisit\VirtualVisitInviteGuest.cs" />
    <Compile Include="Entities\VirtualVisit\VirtualVisitRoom.cs" />
    <Compile Include="Entities\VisitPage\PatientBP.cs" />
    <Compile Include="Entities\TestBase\tempVPLog.cs" />
    <Compile Include="Entities\TestBase\tempVPPhrase.cs" />
    <Compile Include="Entities\TestBase\tempVPRootCategory.cs" />
    <Compile Include="Entities\VisitPage\VPLabResult.cs" />
    <Compile Include="Entities\VisitPage\VPMeasurementSavedValue.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentHealthCardValidation.cs" />
    <Compile Include="ErrorExtensions\EntityErrorJson.cs" />
    <Compile Include="ErrorExtensions\FormattedDbEntityValidationException.cs" />
    <Compile Include="DataAccess\MyDbConfiguration.cs" />
    <Compile Include="DataAccess\QueryLog.cs" />
    <Compile Include="Entities\ApplicationSetting\ApplicationSetting.cs" />
    <Compile Include="Entities\Billing\Bill.cs" />
    <Compile Include="Entities\Billing\BillDetail.cs" />
    <Compile Include="Entities\Billing\Billing_BonusCode.cs" />
    <Compile Include="Entities\Billing\Billing_PreventiveCareBonusRate.cs" />
    <Compile Include="Entities\Billing\Billing_ConsultRate.cs" />
    <Compile Include="Entities\Billing\Billing_DiagnoseRate.cs" />
    <Compile Include="Entities\Billing\Billing_EDTError.cs" />
    <Compile Include="Entities\Billing\Billing_EDTErrorCode.cs" />
    <Compile Include="Entities\Billing\Billing_EDTFiles.cs" />
    <Compile Include="Entities\Billing\Billing_EDTId.cs" />
    <Compile Include="Entities\Billing\Billing_FileSequence.cs" />
    <Compile Include="Entities\Billing\Billing_Group.cs" />
    <Compile Include="Entities\Billing\Billing_OHIPFeeScheduleMaster.cs" />
    <Compile Include="Entities\Billing\Billing_Payor.cs" />
    <Compile Include="Entities\Billing\Billing_TestRate.cs" />
    <Compile Include="Entities\Billing\Billing_Type.cs" />
    <Compile Include="Entities\Billing\BillStatus.cs" />
    <Compile Include="Entities\Common\ApplicationUser.cs" />
    <Compile Include="Entities\Common\AppointmentType.cs" />
    <Compile Include="Entities\Common\AppointmentTypeC2toC3.cs" />
    <Compile Include="Entities\Common\CdsImortExportLogs.cs" />
    <Compile Include="Entities\Common\ApplicationUserClaim.cs" />
    <Compile Include="Entities\Common\PatientAdditionalNote.cs" />
    <Compile Include="Entities\Common\PatientNoteType.cs" />
    <Compile Include="Entities\Common\PracticeTestResource.cs" />
    <Compile Include="Entities\Common\TestResourceType.cs" />
    <Compile Include="Entities\Common\TestResource.cs" />
    <Compile Include="Entities\Common\ScheduleUser.cs" />
    <Compile Include="Entities\Common\ScheduleUserDay.cs" />
    <Compile Include="Entities\Common\ScheduleWeekDay.cs" />
    <Compile Include="Entities\Common\Client.cs" />
    <Compile Include="Entities\Common\C_Query.cs" />
    <Compile Include="Entities\Common\DoctorComment.cs" />
    <Compile Include="Entities\Common\ExternalCommType.cs" />
    <Compile Include="Entities\Common\Hospital.cs" />
    <Compile Include="Entities\Common\InsuranceCompany.cs" />
    <Compile Include="Entities\Common\InternalFileName.cs" />
    <Compile Include="Entities\Common\inventory.cs" />
    <Compile Include="Entities\Common\LandingPage.cs" />
    <Compile Include="Entities\Common\Language.cs" />
    <Compile Include="Entities\Common\LoginHistory.cs" />
    <Compile Include="Entities\Common\MobileNetwork.cs" />
    <Compile Include="Entities\Common\Modality.cs" />
    <Compile Include="Entities\Common\OhipTimeLimitedFeeCode.cs" />
    <Compile Include="Entities\Common\AspNetUserPasswordHistorty.cs" />
    <Compile Include="Entities\Common\PatientAuthorization.cs" />
    <Compile Include="Entities\Common\Pharmacy.cs" />
    <Compile Include="Entities\Common\PracticeCommRecipient.cs" />
    <Compile Include="Entities\Common\PracticeCommType.cs" />
    <Compile Include="Entities\Common\ScheduleStaffNote.cs" />
    <Compile Include="Entities\Common\UserScheduleViewFilter.cs" />
    <Compile Include="Entities\ExternalDoctors\ExternalDoctorLocation.cs" />
    <Compile Include="Entities\HRM\HRMReportClass.cs" />
    <Compile Include="Entities\HRM\ReportOBRContent.cs" />
    <Compile Include="Entities\HRM\HRMReportSubClass.cs" />
    <Compile Include="Entities\HRM\ReportSubclassMap.cs" />
    <Compile Include="Entities\Kiosk\KioskCheckin.cs" />
    <Compile Include="Entities\Kiosk\KioskIpAddress.cs" />
    <Compile Include="Entities\Kiosk\KioskMessage.cs" />
    <Compile Include="Entities\Kiosk\KioskSet.cs" />
    <Compile Include="Entities\Master\HL7\HL7SensitiveResultAccessLog.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Patient\PatientLocation.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeHospital.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeTestGroup.cs" />
    <Compile Include="Entities\OLIS\OLISBusinessLogicErrorCodes.cs" />
    <Compile Include="Entities\OLIS\OLISMicrorganism.cs" />
    <Compile Include="Entities\OLIS\OLISQueryParameterError.cs" />
    <Compile Include="Entities\OLIS\OLISReceivedResponseFile.cs" />
    <Compile Include="Entities\PatientEquipment.cs" />
    <Compile Include="Entities\Reminder\ReminderSentHistory.cs" />
    <Compile Include="Entities\Reminder\ReminderType.cs" />
    <Compile Include="Entities\Reminder\ReminderRule.cs" />
    <Compile Include="Entities\Reports\SSRSReport.cs" />
    <Compile Include="Entities\Timesheet\UserTimesheet.cs" />
    <Compile Include="Entities\TestBase\AppointmentTestSaveLog.cs" />
    <Compile Include="Entities\TestBase\RootCategorySavedValue.cs" />
    <Compile Include="Entities\ZScore\ZsCoreConst.cs" />
    <Compile Include="Entities\ZScore\ZsCoreMethod.cs" />
    <Compile Include="Entities\ZScore\ZScoreParameterName.cs" />
    <Compile Include="Entities\ZScore\ZSCoreParameters.cs" />
    <Compile Include="Entities\TestBase\RootCategory.cs" />
    <Compile Include="Extensions\TransactionUtils.cs" />
    <Compile Include="Infrastructure\IAppointmentPriorityRepository.cs" />
    <Compile Include="Infrastructure\IPracticeDefaultAppointmentPriorityRepository.cs" />
    <Compile Include="Infrastructure\IPracticeRepository.cs" />
    <Compile Include="Infrastructure\IUserRepository.cs" />
    <Compile Include="InterfaceLog\PracticeCommTypeLog.cs" />
    <Compile Include="Entities\Common\PrintLog.cs" />
    <Compile Include="Entities\Common\Province.cs" />
    <Compile Include="Entities\Common\RequestInfo.cs" />
    <Compile Include="Entities\Common\Schedule.cs" />
    <Compile Include="Entities\Common\ScheduleColorSchema.cs" />
    <Compile Include="Entities\Common\ScheduleOfficeNote.cs" />
    <Compile Include="Entities\Common\TerminationReason.cs" />
    <Compile Include="Entities\Common\Test.cs" />
    <Compile Include="Entities\Common\CUser.cs" />
    <Compile Include="Entities\Common\UserLandingPage.cs" />
    <Compile Include="Entities\ContactManager\CM_TaskDefinition.cs" />
    <Compile Include="Entities\ContactManager\CM_TaskMessage.cs" />
    <Compile Include="Entities\ContactManager\CM_TaskMessageRecipient.cs" />
    <Compile Include="Entities\ContactManager\CM_TaskReport.cs" />
    <Compile Include="Entities\ExternalDoctors\ExternalDoctor.cs" />
    <Compile Include="Entities\ExternalDoctors\ExternalDoctorAddress.cs" />
    <Compile Include="Entities\ExternalDoctors\ExternalDoctorPhoneNumber.cs" />
    <Compile Include="Entities\ExternalDoctors\UserDoctor.cs" />
    <Compile Include="Entities\ExternalDocument\DoctorsReportReviewed.cs" />
    <Compile Include="Entities\ExternalDocument\ReportSendingFacility.cs" />
    <Compile Include="Entities\ExternalDocument\ReportAccompanyingSubclass.cs" />
    <Compile Include="Entities\ExternalDocument\ReportAccompanyingMnemonic.cs" />
    <Compile Include="Entities\ExternalDocument\ReportSubClass.cs" />
    <Compile Include="Entities\ExternalDocument\ReportClass.cs" />
    <Compile Include="Entities\ExternalDocument\ReportReceived.cs" />
    <Compile Include="Entities\Fax\OfficeFaxSetting.cs" />
    <Compile Include="Entities\HosiptalDaysheet\HDAdmission.cs" />
    <Compile Include="Entities\HosiptalDaysheet\HDAdmissionAction.cs" />
    <Compile Include="Entities\HosiptalDaysheet\HDAdmissionType.cs" />
    <Compile Include="Entities\HosiptalDaysheet\HDService.cs" />
    <Compile Include="Entities\HosiptalDaysheet\HDServiceCode.cs" />
    <Compile Include="Entities\HosiptalDaysheet\HospitalBundle.cs" />
    <Compile Include="Entities\HosiptalDaysheet\HospitalBundleType.cs" />
    <Compile Include="Entities\HosiptalDaysheet\HospitalServiceCode.cs" />
    <Compile Include="Entities\HRM\HRMLog.cs" />
    <Compile Include="Entities\Master\HL7\HL7Coding.cs" />
    <Compile Include="Entities\Master\HL7\HL7Lab.cs" />
    <Compile Include="Entities\Master\HL7\HL7MarkedSeen.cs" />
    <Compile Include="Entities\Master\HL7\HL7Message.cs" />
    <Compile Include="Entities\Master\HL7\HL7Patient.cs" />
    <Compile Include="Entities\Master\HL7\HL7Report.cs" />
    <Compile Include="Entities\Master\HL7\HL7ReportDoctor.cs" />
    <Compile Include="Entities\Master\HL7\HL7ReportNote.cs" />
    <Compile Include="Entities\Master\HL7\HL7ReportVersion.cs" />
    <Compile Include="Entities\Master\HL7\HL7Result.cs" />
    <Compile Include="Entities\Master\HL7\HL7ResultNote.cs" />
    <Compile Include="Entities\Master\HL7\HL7TestDescription.cs" />
    <Compile Include="Entities\Master\Master.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\Appointment.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentModifier.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentPreconditon.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentProvider.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentStatusLog.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentTest.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentTestLegacyDoc.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentTestResource.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentTestStatus.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentTriageDisposition.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Appointments\AppointmentC2ToC3.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Patient\PatientPharmacy.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Patient\PatientRecordMessage.cs" />
    <Compile Include="Entities\Master\Practices\Practice\BookingConfirmationMessage.cs" />
    <Compile Include="Entities\Master\Practices\Practice\OfficeSetting.cs" />
    <Compile Include="Entities\Master\Practices\Practice\TriageDisposition.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\Demographic.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsAddress.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsAssociatedDoctor.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsContactPhoneNumbers.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsDefaultReferralDoctor.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsFamilyDoctor.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsHealthCard.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsMainResponsiblePhysician.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsNextOfKin.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Demographics\DemographicsPhoneNumber.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\ExternalDocument\LooseReportCategory.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\FamilyHistory\FamilyHistory.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\FamilyHistory\FamilyHistoryResidualInfo.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\FamilyHistory\FamilyHistoryStandardCoding.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\PastHealth\PastHealth.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\PastHealth\PastHealthResidualInfo.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\PastHealth\PastHealthStandardCoding.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Patient\PatientMRN.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\Patient\PatientRecord.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\PersonalHistory\PersonalHistory.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\PersonalHistory\PersonalHistoryResidualInfo.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\ProblemList\ProblemList.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\ProblemList\ProblemListResidualInfo.cs" />
    <Compile Include="Entities\Master\Practices\PatientRecords\ProblemList\ProblemListStandardCoding.cs" />
    <Compile Include="Entities\Master\Practices\Practice\Office.cs" />
    <Compile Include="Entities\Master\Practices\Practice\OfficeFaxFolder.cs" />
    <Compile Include="Entities\Master\Practices\Practice\OfficeGroupBillingNumber.cs" />
    <Compile Include="Entities\Master\Practices\Practice\OfficeRoom.cs" />
    <Compile Include="Entities\Master\Practices\Practice\OfficeUrl.cs" />
    <Compile Include="Entities\Master\Practices\Practice\OfficeUrlType.cs" />
    <Compile Include="Entities\Master\Practices\Practice\Practice.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeAppointmentType.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeDoctor.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeDoctorAppointmentType.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeSpecialty.cs" />
    <Compile Include="Entities\Master\Practices\Practice\PracticeTest.cs" />
    <Compile Include="Entities\Master\Practices\Practice\Specialty.cs" />
    <Compile Include="Entities\Master\Practices\Practice\TriageStatus.cs" />
    <Compile Include="Entities\Master\Practices\Practice\TriageUrgency.cs" />
    <Compile Include="Entities\Master\Practices\Practice\UserOffice.cs" />
    <Compile Include="Entities\Master\Practices\Practice\WSItem.cs" />
    <Compile Include="Entities\Master\Practices\Practice\WSRootCategory.cs" />
    <Compile Include="Entities\Measurement\AppointmentTestLog.cs" />
    <Compile Include="Entities\Measurement\BullEye.cs" />
    <Compile Include="Entities\Measurement\CategoryByGroup.cs" />
    <Compile Include="Entities\Common\Group.cs" />
    <Compile Include="Entities\Measurement\Measurement.cs" />
    <Compile Include="Entities\Measurement\MeasurementBSARange.cs" />
    <Compile Include="Entities\Measurement\MeasurementByPractice.cs" />
    <Compile Include="Entities\Measurement\MeasurementCategory.cs" />
    <Compile Include="Entities\Measurement\MeasurementCategoryTest.cs" />
    <Compile Include="Entities\Measurement\MeasurementMapping.cs" />
    <Compile Include="Entities\Measurement\MeasurementOperator.cs" />
    <Compile Include="Entities\Measurement\MeasurementRange.cs" />
    <Compile Include="Entities\Measurement\MeasurementRangeText.cs" />
    <Compile Include="Entities\Measurement\MeasurementRangeType.cs" />
    <Compile Include="Entities\Measurement\MeasurementSavedValue.cs" />
    <Compile Include="Entities\Measurement\PatientProstaticValve.cs" />
    <Compile Include="Entities\Measurement\ProstaticValve.cs" />
    <Compile Include="Entities\Measurement\ReportPhrase.cs" />
    <Compile Include="Entities\Measurement\ReportPhraseByDoctor.cs" />
    <Compile Include="Entities\Measurement\ReportPhraseByPractice.cs" />
    <Compile Include="Entities\Measurement\ReportPhraseMeasurmentCategory.cs" />
    <Compile Include="Entities\Measurement\ReportPhraseMeasurmentCategoryScroll.cs" />
    <Compile Include="Entities\Measurement\ReportPhraseNormal.cs" />
    <Compile Include="Entities\Measurement\ReportPhraseSavedText.cs" />
    <Compile Include="Entities\Measurement\ReportPhraseSavedValue.cs" />
    <Compile Include="Entities\Measurement\ReportPhrase_Custom.cs" />
    <Compile Include="Entities\Measurement\SendType.cs" />
    <Compile Include="Entities\Common\TestGroup.cs" />
    <Compile Include="Entities\Measurement\TestGroupDetail.cs" />
    <Compile Include="Entities\Measurement\WS_SendReport.cs" />
    <Compile Include="Entities\Medications\AllergyStatus.cs" />
    <Compile Include="Entities\Medications\Compliance.cs" />
    <Compile Include="Entities\Medications\DiscontinueReason.cs" />
    <Compile Include="Entities\Medications\DoseChangeReason.cs" />
    <Compile Include="Entities\Medications\LifeStage.cs" />
    <Compile Include="Entities\Medications\Medication.cs" />
    <Compile Include="Entities\Medications\MedicationClass.cs" />
    <Compile Include="Entities\Medications\MedicationDBUpdate.cs" />
    <Compile Include="Entities\Medications\MedicationDefault.cs" />
    <Compile Include="Entities\Medications\MedicationForm.cs" />
    <Compile Include="Entities\Medications\MedicationFrequencyUnit.cs" />
    <Compile Include="Entities\Medications\MedicationIngredient.cs" />
    <Compile Include="Entities\Medications\MedicationNoDin.cs" />
    <Compile Include="Entities\Medications\MedicationStrengthUnit.cs" />
    <Compile Include="Entities\Medications\MedicationRoute.cs" />
    <Compile Include="Entities\Medications\MedicationTemplate.cs" />
    <Compile Include="Entities\Medications\MedicationTemplateClass.cs" />
    <Compile Include="Entities\Medications\PatientAllergy.cs" />
    <Compile Include="Entities\Medications\PatientAllergyIngredient.cs" />
    <Compile Include="Entities\Medications\PatientMedication.cs" />
    <Compile Include="Entities\Medications\PatientMedicationSet.cs" />
    <Compile Include="Entities\Medications\UserPatMedCPPVisibility.cs" />
    <Compile Include="Entities\Medications\UserPatMedSort.cs" />
    <Compile Include="Entities\Medications\PatientPrescription.cs" />
    <Compile Include="Entities\Medications\PatientPrescriptionPrint.cs" />
    <Compile Include="Entities\Medications\PatientPrescriptionSet.cs" />
    <Compile Include="Entities\Medications\PracticeInteractionSetting.cs" />
    <Compile Include="Entities\Medications\PrescriptionDefault.cs" />
    <Compile Include="Entities\Medications\PrescriptionStatus.cs" />
    <Compile Include="Entities\Medications\ReactionType.cs" />
    <Compile Include="Entities\Medications\Severity.cs" />
    <Compile Include="Entities\Medications\SigCode.cs" />
    <Compile Include="Entities\Medications\TreatmentType.cs" />
    <Compile Include="Entities\Medications\UserInteractionAlert.cs" />
    <Compile Include="Entities\Medications\UserInteractionOption.cs" />
    <Compile Include="Entities\Medications\UserInteractionSettingType.cs" />
    <Compile Include="Entities\Medications\UserInteractionSetting.cs" />
    <Compile Include="Entities\OLIS\Laboratory.cs" />
    <Compile Include="Entities\OLIS\OLISCommunicationLog.cs" />
    <Compile Include="Entities\OLIS\OLISQueryMessage.cs" />
    <Compile Include="Entities\OLIS\OLISReceivedReportPatient.cs" />
    <Compile Include="Entities\OLIS\OLISReceivedReports.cs" />
    <Compile Include="Entities\OLIS\OLISTestReportCategory.cs" />
    <Compile Include="Entities\OLIS\OLISTestRequestCategory.cs" />
    <Compile Include="Entities\OLIS\OLISTestRequestNomenclature.cs" />
    <Compile Include="Entities\OLIS\OLISTestRequestSubCategory.cs" />
    <Compile Include="Entities\OLIS\OLISTestResultCategory.cs" />
    <Compile Include="Entities\OLIS\OLISTestResultNomenclature.cs" />
    <Compile Include="Entities\VisitPage\CareElementGroup.cs" />
    <Compile Include="Entities\VisitPage\DiagnoseCodeLookup.cs" />
    <Compile Include="Entities\VisitPage\PatientBloodPressure.cs" />
    <Compile Include="Entities\VisitPage\PreventiveCareBonusCategory.cs" />
    <Compile Include="Entities\VisitPage\PreventiveCareBonusCode.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Problem_Status.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Visible_Field.cs" />
    <Compile Include="InterfaceLog\InterfaceLogContext.cs" />
    <Compile Include="InterfaceLog\InterfaceLog.cs" />
    <Compile Include="Migrations\ConfigurationInterfaceLog.cs" />
    <Compile Include="Migrations\ConfigurationRAD.cs" />
    <Compile Include="Entities\Radiology\RAD_Image.cs" />
    <Compile Include="Entities\Radiology\RAD_Institution.cs" />
    <Compile Include="Entities\Radiology\RAD_Patient.cs" />
    <Compile Include="Entities\Radiology\RAD_RefPhysician.cs" />
    <Compile Include="Entities\Radiology\RAD_Series.cs" />
    <Compile Include="Entities\Radiology\RAD_Study.cs" />
    <Compile Include="Entities\Reports\ExternalReport.cs" />
    <Compile Include="Entities\Reports\LabResult.cs" />
    <Compile Include="Entities\Requisition\Reasons.cs" />
    <Compile Include="Entities\Requisition\Requisition.cs" />
    <Compile Include="Entities\Requisition\RequisitionItem.cs" />
    <Compile Include="Entities\Requisition\RequisitionPatient.cs" />
    <Compile Include="Entities\Requisition\RequisitionResult.cs" />
    <Compile Include="Entities\Requisition\RequisitionStatus.cs" />
    <Compile Include="Entities\Requisition\RequisitionTemplate.cs" />
    <Compile Include="Entities\Requisition\RequisitionTime.cs" />
    <Compile Include="Entities\Requisition\RequisitionType.cs" />
    <Compile Include="Entities\Requisition\Services.cs" />
    <Compile Include="Entities\UserPermissions\Permission.cs" />
    <Compile Include="Entities\UserPermissions\PermissionsBase.cs" />
    <Compile Include="Entities\UserPermissions\PermissionType.cs" />
    <Compile Include="Entities\UserPermissions\UserPermission.cs" />
    <Compile Include="Entities\VisitPage\AppointmentBill.cs" />
    <Compile Include="Entities\VisitPage\ConsultCode.cs" />
    <Compile Include="Entities\VisitPage\DiagnoseCode.cs" />
    <Compile Include="Entities\VisitPage\ICD10.cs" />
    <Compile Include="Entities\VisitPage\ImmunizationRecall.cs" />
    <Compile Include="Entities\VisitPage\RecallLog.cs" />
    <Compile Include="Entities\VisitPage\VPCategory.cs" />
    <Compile Include="Entities\VisitPage\VPCategoryOption.cs" />
    <Compile Include="Entities\VisitPage\VPMeasurement.cs" />
    <Compile Include="Entities\VisitPage\VPMeasurementOption.cs" />
    <Compile Include="Entities\VisitPage\VPOption.cs" />
    <Compile Include="Entities\VisitPage\VPOptionByPatient.cs" />
    <Compile Include="Entities\VisitPage\VPReportPhrase.cs" />
    <Compile Include="Entities\VisitPage\VPReportPhraseByDoctor.cs" />
    <Compile Include="Entities\VisitPage\VPReportPhraseOption.cs" />
    <Compile Include="Entities\VisitPage\VPTemplateField.cs" />
    <Compile Include="Entities\VisitPage\VPUniqueMeasurement.cs" />
    <Compile Include="Entities\VisitPage\VP_AppointmentTestLog.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Alert.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Category.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_FamilyHistory.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Immunization.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_ImmunizationStatus.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_ImmunizationType.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Item.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Problem_List.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_RiskFactor.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Setting.cs" />
    <Compile Include="Entities\VisitPage\VP_CPP_Skipped.cs" />
    <Compile Include="Entities\VisitPage\VP_MeasurementSavedValue.cs" />
    <Compile Include="Entities\VisitPage\VP_Measurements_Patient.cs" />
    <Compile Include="Entities\VisitPage\VP_Privacy_Notes.cs" />
    <Compile Include="Entities\VisitPage\VP_ReportPhrasesSavedText.cs" />
    <Compile Include="Entities\VisitPage\VP_ReportPhrasesSavedValue.cs" />
    <Compile Include="Entities\VisitPage\VP_ReportPhrases_Skipped.cs" />
    <Compile Include="Entities\VisitPage\VP_ReportPhrase_Custom.cs" />
    <Compile Include="Entities\VisitPage\VP_SendReport.cs" />
    <Compile Include="Entities\VisitPage\VP_Template.cs" />
    <Compile Include="Entities\VisitPage\VP_Template_Detail.cs" />
    <Compile Include="Entities\VisitPage\VP_Template_Patient_Data.cs" />
    <Compile Include="Entities\VisitPage\VP_Template_Patient_Detail.cs" />
    <Compile Include="Entities\WebBooking\WebBookingDoctor.cs" />
    <Compile Include="Extensions\Extentions.cs" />
    <Compile Include="Migrations\ConfigurationAudit.cs" />
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Repositories\AppointmentPriorityRepository.cs" />
    <Compile Include="Repositories\GenericRepository.cs" />
    <Compile Include="Repositories\PracticeDefaultAppointmentPriorityRepository.cs" />
    <Compile Include="Repositories\PracticeRepository.cs" />
    <Compile Include="Repositories\RequisitionPatientRepository.cs" />
    <Compile Include="Repositories\RequisitionRepository.cs" />
    <Compile Include="Repositories\RequisitionStatusRepository.cs" />
    <Compile Include="Repositories\UserRepository.cs" />
    <Compile Include="Startup.cs" />
    <Compile Include="Entities\Document\DocumentEntity.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Cerebrum.Infrastructure\Cerebrum.Infrastructure.csproj">
      <Project>{b3e9ad82-59f9-4fe3-b3db-b8c1a8a29af5}</Project>
      <Name>Cerebrum.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\Cerebrum.VirtualVisit.Seedwork\Cerebrum.VirtualVisit.Seedwork.csproj">
      <Project>{7ae11209-2fdb-4dd2-b295-f3c89fd30b7d}</Project>
      <Name>Cerebrum.VirtualVisit.Seedwork</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>