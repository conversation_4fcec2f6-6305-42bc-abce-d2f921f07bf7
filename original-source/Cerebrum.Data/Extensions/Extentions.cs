﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Infrastructure;
using System.Data.SqlClient;
using System.Linq;

namespace Cerebrum.Data
{
    public static class Extentions
    {
        public static IEnumerable<T> GetData_SP<T>(this CerebrumContext c, string storedProcedure, List<SqlParameter> parameters = null, int sqlCommandTimeOut = 0) where T : class
        {
            List<T> list = new List<T>();
            using (SqlConnection conn = new SqlConnection(c.Database.Connection.ConnectionString))
            using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
            {
                {
                    cmd.CommandText = storedProcedure;
                    cmd.CommandType = System.Data.CommandType.StoredProcedure;
                    //cmd.CommandTimeout = sqlCommandTimeOut;
                    if (sqlCommandTimeOut > 0)
                        cmd.CommandTimeout = sqlCommandTimeOut;

                    try
                    {
                        if (parameters != null)
                        {
                            cmd.Parameters.Clear();
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.Add(param);
                            }
                        }
                        //if (conn.State != System.Data.ConnectionState.Open)
                            conn.Open();
                        list = ((IObjectContextAdapter)c).ObjectContext.Translate<T>(cmd.ExecuteReader()).ToList();
                    }
                    catch (Exception x)
                    {
                        string parameter = string.Join(",", parameters.Select(s => Convert.ToString(s.Value)));
                        string excStr = $"storedProcedure:{storedProcedure} Parameters:{parameter} {x.ToString()}";

                        throw new Exception(excStr);
                    }
                    finally
                    {
                        if (cmd.Connection != null && cmd.Connection.State != System.Data.ConnectionState.Closed)
                            cmd.Connection.Close();
                    }

                }
                return list;
            }
        }
        public static List<SqlParameter> ToSQLParameters(this Object obj)
        {
            List<SqlParameter> parms = new List<SqlParameter>();
            Type t = obj.GetType();
            foreach (var prop in t.GetProperties())
            {
                var val = prop.GetValue(obj, null);
                if (val != null)
                {
                    parms.Add(new SqlParameter(prop.Name, val));
                }
            }
            return parms;
        }
        public static System.Data.DataTable ToTableIntegers(this List<int> ids)
        {
            var table = new System.Data.DataTable();
            table.Columns.Add("IntegerValue", typeof(int));
            if (ids!=null && ids.Any())
            {
                

                foreach (var id in ids)
                {
                    table.Rows.Add(id);
                }

                return table;
            }
            return table;
        }
        public static TEntity ShallowCopyEntity<TEntity>(TEntity source) where TEntity : class, new()
        {

            // Get properties from EF that are read/write and not marked witht he NotMappedAttribute
            var sourceProperties = typeof(TEntity)
                                    .GetProperties()
                                    .Where(p => p.CanRead && p.CanWrite &&
                                                p.GetCustomAttributes(typeof(NotMappedAttribute), true).Length == 0);
            var newObj = new TEntity();

            foreach (var property in sourceProperties)
            {

                // Copy value
                property.SetValue(newObj, property.GetValue(source, null), null);

            }

            return newObj;

        }
    }
}
