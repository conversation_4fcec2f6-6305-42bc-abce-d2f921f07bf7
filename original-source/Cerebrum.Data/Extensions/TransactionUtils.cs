﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace Cerebrum.Data
{
    public class TransactionUtils
    {
        public static TransactionScope CreateTransactionScope(IsolationLevel isolationLev)
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = isolationLev;
            transactionOptions.Timeout = TransactionManager.MaximumTimeout;
            return new TransactionScope(TransactionScopeOption.Required, transactionOptions);
        }
        public static TransactionScope CreateTransactionScope()
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = IsolationLevel.ReadCommitted;
            transactionOptions.Timeout = TransactionManager.MaximumTimeout;
            return new TransactionScope(TransactionScopeOption.Required, transactionOptions);
        }
        public static TransactionScope CreateTransactionScopeAsync()
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = IsolationLevel.ReadCommitted;
            transactionOptions.Timeout = TransactionManager.MaximumTimeout;
            return new TransactionScope(TransactionScopeOption.Required, transactionOptions, TransactionScopeAsyncFlowOption.Enabled);
        }
        public static TransactionScope CreateTransactionScopeRequiredNewAsync()
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = IsolationLevel.ReadCommitted;
            transactionOptions.Timeout = TransactionManager.MaximumTimeout;
            return new TransactionScope(TransactionScopeOption.RequiresNew, transactionOptions, TransactionScopeAsyncFlowOption.Enabled);
        }
        public static TransactionScope CreateTransactionScopeSupress()
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = IsolationLevel.ReadCommitted;
            transactionOptions.Timeout = TransactionManager.MaximumTimeout;
            return new TransactionScope(TransactionScopeOption.Suppress, transactionOptions);
        }
        public static TransactionScope CreateTransactionScopeSupress(TimeSpan timeout)
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = IsolationLevel.ReadCommitted;
            transactionOptions.Timeout = timeout;
            return new TransactionScope(TransactionScopeOption.Suppress, transactionOptions);
        }

        public static TransactionScope CreateTransactionScopeSupressAsync()
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = IsolationLevel.ReadCommitted;
            transactionOptions.Timeout = TransactionManager.MaximumTimeout;
            
            return new TransactionScope(TransactionScopeOption.Suppress, transactionOptions,TransactionScopeAsyncFlowOption.Enabled);
        }
        public static TransactionScope CreateTransactionScopeRequiresNew(IsolationLevel isolationLev, TimeSpan timeout)
        {
            var transactionOptions = new TransactionOptions();
            transactionOptions.IsolationLevel = isolationLev;
            transactionOptions.Timeout = timeout;

            return new TransactionScope(TransactionScopeOption.RequiresNew, transactionOptions);
        }
    }
}
