﻿using AwareMD.Cerebrum.Shared;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL.Requisition;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using Cerebrum.ViewModels.ContactManagerNew;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Cerebrum.ContactManager
{
    public interface IContactManagerService
    {
        Task<ContactManagerNewTaskViewModel> InitializeNewTask(ContactManagerNewTaskRequest newTaskRequest);
        string CheckMessagePermission(int practiceId, string taskMessageId, string patientRecordId);
        string AddNewTask(ContactManagerTaskData newTask, int practiceId, string userGuid, int modifyingUserId, string modifyingFromIpAddress);
        bool UpdateTask(ContactManagerTaskData newTask, int practiceId, int modifyingUserId, string modifyingFromIpAddress);

    }
    public class ContactManagerService : IContactManagerService
    {
        private readonly CerebrumContext _context;
        readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        #region CONSTANTS
        private const string ORDER_ASC = "asc";
        private const string ORDER_DESC = "desc";
        private const string DATE_FORMAT_DUE = "MM/dd/yyyy";
        private const string DATE_FORMAT_MESSAGE = "MM/dd/yyyy HH:mm:ss";
        private const string FAX = "fax";
        private const string HL7 = "hl7";
        private const string HRM = "hrm";
        private const string EXTERNALDOCUMENTFAX = "fax";
        private const string PERMISSIONVIEWCOMMONMESSAGES = "ViewCommonMessages";
        private const string PERMISSIONADDREQUISITION = "AddRequisition";
        private const string TYPEAPPOINTMENT = "appointment";
        private const string TYPEREQUISITION = "requisition";

        //requisition status
        private const string STATUSORDERED = "ordered";
        private const string STATUSARRANGED = "arranged";

        private const string HttpContext = "MS_HttpContext";
        private const string RemoteEndpointMessage = "System.ServiceModel.Channels.RemoteEndpointMessageProperty";

        private const string DATEFORMAT = "MM/dd/yyyy";
        private const string DATEFORMAT3 = "yyyyMMdd";
        #endregion
        private readonly IRequisitionBLL _requisitionBLL;
        private readonly IContactManagerEmailService _contactManagerEmailService;

        public ContactManagerService(CerebrumContext context, IRequisitionBLL requisitionBLL, IContactManagerEmailService contactManagerEmailService)
        {
            _context = context;
            _requisitionBLL = requisitionBLL;
            _contactManagerEmailService = contactManagerEmailService;
        }

        public async Task<ContactManagerNewTaskViewModel> InitializeNewTask(ContactManagerNewTaskRequest newTaskRequest)
        {
            ContactManagerNewTaskViewModel result = new ContactManagerNewTaskViewModel();
            result.patientRecordId = newTaskRequest.patientRecordId;

            result.taskMessage.taskMessageId = newTaskRequest.taskMessageId;
            result.taskMessage.patientRecordId = newTaskRequest.patientRecordId;
            result.taskMessage.reportId = newTaskRequest.reportId;
            result.taskMessage.reportType = newTaskRequest.reportType;
            result.taskMessage.recipients = string.Empty;

            result.offices = await GetPracticeOfficesAsync(newTaskRequest.practiceId);
            result.urgencies = GetTaskUrgencies();
            result.userTypes = GetUserTypes();
            result.users = GetUsers(newTaskRequest.practiceId, true);

            if (!string.IsNullOrEmpty(newTaskRequest.taskMessageId))
            {
                var requisitions = await GetRequistions(newTaskRequest.practiceId);
                if (requisitions.Count > 0)
                    result.requisitions = requisitions;
            }

            bool isClosed = false;
            var tasksDb = GetTask(newTaskRequest, out isClosed);
            result.isClosed = isClosed;

            if (tasksDb != null)
            {
                result.taskMessage.taskId = tasksDb.taskId;
                result.taskMessage.taskMessageId = tasksDb.taskMessageId;
                result.taskMessage.dueDate = tasksDb.dueDate;
                result.taskMessage.urgency = tasksDb.urgency;
                result.taskMessage.subject = tasksDb.subject;
                result.taskMessage.office = tasksDb.office;
                result.taskMessage.patientRecordId = tasksDb.patientRecordId;
                result.taskMessage.message = GetTaskMessage(result.taskMessage.taskId, result.taskMessage.taskMessageId);
                result.taskMessage.recipients = GetTaskReceipients(result.taskMessage.taskMessageId, newTaskRequest.applicationUserId, tasksDb.messageCreator);
            }
            else
            {
                result.taskMessage.recipients = GetPracticeDoctorIds(newTaskRequest.practiceId, newTaskRequest.applicationUserId);
            }

            var patient = GetPatientInfo(result.taskMessage.patientRecordId);
            if (patient != null)
            {
                result.patientName = patient.patientName;
                result.demographicId = patient.demographicId;
                result.patientRecordId = patient.patientRecordId;
            }

            if (!string.IsNullOrEmpty(result.patientRecordId))
            {
                var doctorId = GetPracticeDoctorId(newTaskRequest.practiceId, newTaskRequest.applicationUserId);
                if (doctorId != null)
                {
                    result.criticalResources.Add(PERMISSIONADDREQUISITION);
                    result.practiceDoctorId = doctorId.ToString();
                }
            }

            ContactManagerNewTaskAttachmentResponse attachments = await GetNewTaskAttachment(result.patientRecordId, newTaskRequest.taskMessageId);
            if (string.IsNullOrEmpty(attachments.errorMessage))
                result.attachments = attachments.attachments;
            else
                result.errorMessage = attachments.errorMessage;

            return result;
        }

        public string CheckMessagePermission(int practiceId, string taskMessageId, string patientRecordId)
        {
            if (string.IsNullOrEmpty(taskMessageId) && string.IsNullOrEmpty(patientRecordId))   //new message
                return string.Empty;

            int practiceIdForDoctorPatient = -1;
            if (!string.IsNullOrEmpty(taskMessageId))
            {
                int taskMessageIdInt = GetInt(taskMessageId, -1);
                practiceIdForDoctorPatient = (from a in _context.CM_TaskMessages
                                              join b in _context.Users on a.messageCreator equals b.Id
                                              where a.id == taskMessageIdInt
                                              select b.PracticeID).FirstOrDefault();
            }
            if (!string.IsNullOrEmpty(patientRecordId))
            {
                int patientRecordIdInt = GetInt(patientRecordId, -1);
                practiceIdForDoctorPatient = _context.PatientRecords.Where(a => a.Id == patientRecordIdInt).Select(a => a.PracticeId).FirstOrDefault();
            }

            if (practiceIdForDoctorPatient != practiceId)
                return "No permission to access message";

            return string.Empty;
        }

        public string AddNewTask(ContactManagerTaskData newTask, int practiceId, string userGuid, int modifyingUserId, string modifyingFromIpAddress)
        {
            if (string.IsNullOrEmpty(newTask.recipients) && newTask.markDone == 0)
                return "New message doesn't have recipient! ";

            int taskDefinitionId = -1;
            List<string> recipients = null;

            try
            {
                taskDefinitionId = AddContactManagerTaskDefinition(newTask, practiceId, userGuid, modifyingUserId, modifyingFromIpAddress);

                if (!string.IsNullOrEmpty(newTask.taskMessageId))
                {
                    int taskMessageId = Convert.ToInt32(newTask.taskMessageId);
                    string message = MarkTaskMessageSeen(userGuid, 0, taskMessageId, modifyingUserId, modifyingFromIpAddress);
                    if (!string.IsNullOrWhiteSpace(message))
                    {
                        return message;
                    }
                }

                if (!string.IsNullOrWhiteSpace(newTask.message))
                {
                    AddContactManagerTaskMessage(taskDefinitionId, newTask, userGuid, modifyingUserId, modifyingFromIpAddress);

                    if (!string.IsNullOrWhiteSpace(newTask.recipients))
                    {
                        recipients = newTask.recipients.Split(',').Distinct().ToList();
                    }
                }

                if (!string.IsNullOrEmpty(newTask.reportId) && !string.IsNullOrEmpty(newTask.reportType))
                {
                    AddContactManagerTaskReport(taskDefinitionId, newTask, modifyingUserId, modifyingFromIpAddress);
                }

                if (taskDefinitionId > 0 && newTask.markDone == 1)
                {
                    var message = MarkTaskDone(userGuid, taskDefinitionId, modifyingUserId, modifyingFromIpAddress);
                    if (!string.IsNullOrWhiteSpace(message))
                    {
                        return message;
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex.ExceptionDetails());
                return "There was a problem. Please Contact Administrator! ";
            }

            if (!newTask.sendEmail)
            {
                return string.Empty;
            }

            return SendEmail(practiceId, taskDefinitionId, newTask.subject, recipients);
        }
        private string SendEmail(int practiceId, int taskId, string subject, List<string> recipients)
        {
            try
            {
                string emailTo = "";
                string emailBody = "";

                ViewModels.OfficeEmail.Email officeEmail = _contactManagerEmailService.GetOfficeEmail(practiceId, 0);

                emailBody = GetTaskMessage(taskId);

                string result = string.Empty;
                for (int i = 0; i < recipients.Count; i++)
                {
                    if (!string.IsNullOrEmpty(recipients[i]))
                    {
                        string recipient = recipients[i];
                        ApplicationUser user = _context.Users.Where(t => t.Id == recipient).FirstOrDefault();
                        if (user != null && !string.IsNullOrEmpty(user.Email))
                        {
                            emailTo = user.Email;

                            if (!UtilityHelper.SendContactManagerMail(emailTo, emailBody, subject, officeEmail))
                            {
                                _log.Debug($"Email error: From {officeEmail.userName}; To: {emailTo}; Subject: {subject}");
                                result = "Task / message was added/mark done successfully but there was a problem with sending email. Please Contact Administrator! ";
                            }
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _log.Error(ex.ExceptionDetails());
                return "There was a problem. Please Contact Administrator! ";
            }
        }
        public bool UpdateTask(ContactManagerTaskData newTask, int practiceId, int modifyingUserId, string modifyingFromIpAddress)
        {
            int taskId = Convert.ToInt32(newTask.taskId);
            CM_TaskDefinition ts = _context.CM_TaskDefinitions.Where(a => a.id == taskId && a.practiceId == practiceId).FirstOrDefault();
            if (ts != null)
            {
                DateTime dueDate;
                if (!DateTime.TryParseExact(newTask.dueDate, DATE_FORMAT_DUE, CultureInfo.InvariantCulture, DateTimeStyles.None, out dueDate))
                    dueDate = DateTime.Now;
                ts.dueDate = dueDate;
                ts.taskUrgency = (CMTaskUrgency)(Convert.ToInt32(newTask.urgency));
                _context.Entry(ts).State = EntityState.Modified;
                int saved = _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);
                return saved > 0;
            }

            return false;
        }

        public string MarkTaskDone(string userGuid, int taskDefinitionId, int modifyingUserId, string modifyingFromIpAddress)
        {
            CM_TaskDefinition task = _context.CM_TaskDefinitions
                .Select(t => t).Where(t => t.id == taskDefinitionId).FirstOrDefault();

            if (task == null)
            {
                return "Cannot find the task";
            }

            task.closedBy = userGuid;
            task.dateClosed = DateTime.Now;
            task.taskStatus = CMTaskStatus.Closed;
            _context.Entry(task).State = EntityState.Modified;
            _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);

            string message = MarkTaskMessageSeen(userGuid, taskDefinitionId, 0, modifyingUserId, modifyingFromIpAddress);

            return message;
        }
        public string MarkTaskMessageSeen(string userId, int taskDefinitionId, int taskMessageId, int modifyingUserId, string modifyingFromIpAddress)
        {
            List<int> taskMessageIds = new List<int>();
            if (taskDefinitionId == 0)
            {
                var taskMessage = _context.CM_TaskMessages.Where(a => a.id == taskMessageId).FirstOrDefault();
                if (taskMessage == null)
                {
                    return "Cannot find the message";
                }
                taskMessageIds = _context.CM_TaskMessages.Where(a => a.CM_TaskDefinitionId == taskMessage.CM_TaskDefinitionId && a.id <= taskMessage.id).Select(b => b.id).ToList();
            }
            else
            {
                taskMessageIds = _context.CM_TaskMessages.Where(a => a.CM_TaskDefinitionId == taskDefinitionId).Select(b => b.id).ToList();
            }

            var recipients = _context.CM_TaskMessageRecipients.Where(a => a.userid == userId && a.seen == false && taskMessageIds.Contains(a.CM_TaskMessageId)).ToList();
            recipients.ForEach(a =>
            {
                a.seen = true;
                a.seenAt = DateTime.Now;
            });

            _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);

            return string.Empty;
        }
        private ContactManagerPatientInfoDto GetPatientInfo(string cmPatientRecordId)
        {
            if (!string.IsNullOrEmpty(cmPatientRecordId))
            {
                int patientRecordId = Convert.ToInt32(cmPatientRecordId);
                return _context.Demographics.Where(t => t.PatientRecordId == patientRecordId).Select(p => new ContactManagerPatientInfoDto
                {
                    patientName = (p.lastName == null ? string.Empty : p.lastName.ToUpper() + ", ") + (p.firstName ?? string.Empty),
                    demographicId = p.Id.ToString(),
                    patientRecordId = p.PatientRecordId.ToString()
                }).FirstOrDefault();
            }
            return null;
        }

        private ContactManagerTaskData GetTask(ContactManagerNewTaskRequest request, out bool isClosed)
        {
            isClosed = false;
            var taskDb = GetTaskFromDb(request);
            if (taskDb != null)
            {
                isClosed = taskDb.isClosed;
                var task = new ContactManagerTaskData
                {
                    taskId = taskDb.taskId,
                    taskMessageId = taskDb.taskMessageId.ToString(),
                    messageCreator = taskDb.messageCreator,
                    dueDate = taskDb.dueDate == null ? string.Empty : taskDb.dueDate.Value.ToString(DATE_FORMAT_DUE, CultureInfo.InvariantCulture),
                    urgency = ((int)taskDb.taskUrgency).ToString(),
                    subject = taskDb.subject,
                    office = taskDb.officeId == null ? string.Empty : taskDb.officeId.ToString(),
                    patientRecordId = taskDb.patientRecordId == null ? string.Empty : taskDb.patientRecordId.ToString()
                };
                return task;
            }

            return null;
        }

        private string GetTaskMessage(int taskId)
        {
            var taskMessages = (from tm in _context.CM_TaskMessages
                                join u in _context.Users on tm.messageCreator equals u.Id
                                where tm.CM_TaskDefinitionId == taskId
                                orderby tm.messageCreatedate descending
                                select new ContactManagerTaskMessage
                                {
                                    message = tm.message,
                                    messageCreateDate = tm.messageCreatedate,
                                    creator = (u.LastName == null ? string.Empty : u.LastName.ToUpper() + ", ") + (u.FirstName ?? string.Empty)
                                }).ToList();

            return taskMessages != null ? FormatTaskMessage(taskMessages) : string.Empty;
        }

        private string GetTaskMessage(int taskId, string taskMessageId)
        {
            int taskMsgId = Convert.ToInt32(taskMessageId);
            var taskMessages = (from tm in _context.CM_TaskMessages
                                join u in _context.Users on tm.messageCreator equals u.Id
                                where tm.CM_TaskDefinitionId == taskId && tm.id <= taskMsgId
                                orderby tm.messageCreatedate descending
                                select new ContactManagerTaskMessage
                                {
                                    message = tm.message,
                                    messageCreateDate = tm.messageCreatedate,
                                    creator = (u.LastName == null ? string.Empty : u.LastName.ToUpper() + ", ") + (u.FirstName ?? string.Empty)
                                }).ToList();

            return taskMessages != null ? FormatTaskMessage(taskMessages) : string.Empty;
        }

        private string FormatTaskMessage(List<ContactManagerTaskMessage> taskMessages)
        {
            string message = string.Empty;
            foreach (var tm in taskMessages)
            {
                if (!string.IsNullOrEmpty(message))
                {
                    message += Environment.NewLine;
                }
                message += tm.messageCreateDate.Value.ToString(DATE_FORMAT_MESSAGE, CultureInfo.InvariantCulture) + " " + tm.creator + ": " + tm.message;
            }
            return message;
        }
        private string GetTaskReceipients(string taskMessageId, string applicationUserId, string messageCreator)
        {
            int taskMsgId = Convert.ToInt32(taskMessageId);
            var taskRecipients = (from tr in _context.CM_TaskMessageRecipients.Where(t => t.CM_TaskMessageId == taskMsgId && t.userid != applicationUserId && t.userid != messageCreator)
                                  select new
                                  {
                                      tr.userid
                                  }).ToList();
            string lastMessageRecipients = string.Empty;
            if (messageCreator != applicationUserId)
                lastMessageRecipients = messageCreator;
            foreach (var tr in taskRecipients)
            {
                if (!string.IsNullOrEmpty(lastMessageRecipients))
                    lastMessageRecipients += ",";
                lastMessageRecipients += tr.userid;
            }
            return lastMessageRecipients;
        }

        private ContactManagerTaskDto GetTaskFromDb(ContactManagerNewTaskRequest request)
        {
            if (!string.IsNullOrEmpty(request.taskMessageId) || (!string.IsNullOrEmpty(request.patientRecordId) && !string.IsNullOrEmpty(request.reportId) && !string.IsNullOrEmpty(request.reportType)))
            {
                if (!string.IsNullOrEmpty(request.taskMessageId))
                {
                    int taskId = Convert.ToInt32(request.taskMessageId);
                    return (from ts in _context.CM_TaskDefinitions
                            join tm in _context.CM_TaskMessages on ts.id equals tm.CM_TaskDefinitionId
                            where ts.practiceId == request.practiceId && tm.id == taskId
                            select new ContactManagerTaskDto
                            {
                                isClosed = (ts.taskStatus == CMTaskStatus.Closed ? true : false),
                                taskId = ts.id,
                                taskMessageId = tm.id,
                                messageCreator = tm.messageCreator,
                                dueDate = ts.dueDate,
                                taskUrgency = ts.taskUrgency,
                                subject = ts.subject,
                                officeId = ts.officeId,
                                patientRecordId = ts.PatientRecordId
                            }).FirstOrDefault();
                }
                else
                {
                    int patientRecordId = Convert.ToInt32(request.patientRecordId);
                    int reportId = Convert.ToInt32(request.reportId);
                    ReportType reportType = this.ParseReportType(request.reportType);

                    return (from ts in _context.CM_TaskDefinitions
                            join tm in _context.CM_TaskMessages on ts.id equals tm.CM_TaskDefinitionId
                            join tr in _context.CM_TaskReports on ts.id equals tr.CM_TaskDefinitionId
                            where ts.PatientRecordId == patientRecordId && tr.reportId == reportId && tr.reportType == reportType
                            orderby tm.id descending
                            select new ContactManagerTaskDto
                            {
                                isClosed = (ts.taskStatus == CMTaskStatus.Closed ? true : false),
                                taskId = ts.id,
                                taskMessageId = tm.id,
                                messageCreator = tm.messageCreator,
                                dueDate = ts.dueDate,
                                taskUrgency = ts.taskUrgency,
                                subject = ts.subject,
                                officeId = ts.officeId,
                                patientRecordId = ts.PatientRecordId
                            }).FirstOrDefault();
                }
            }
            return null;
        }

        private void AddContactManagerTaskReport(int taskDefinitionId, ContactManagerTaskData newTask, int modifyingUserId, string modifyingFromIpAddress)
        {
            CM_TaskReport tr = new CM_TaskReport();
            tr.CM_TaskDefinitionId = taskDefinitionId;
            tr.reportId = Int32.Parse(newTask.reportId);
            tr.reportType = (newTask.reportType.ToLower() == FAX ? ReportType.Fax : ReportType.HL7);
            tr.reportTypeSpecified = false;
            tr.labResultId = newTask.reportType.ToLower() == FAX ? 0 : tr.reportId;
            tr.externalReportId = newTask.reportType.ToLower() == FAX ? tr.reportId : 0;
            _context.Entry(tr).State = EntityState.Added;
            _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);
        }

        private void AddContactManagerTaskMessage(int taskDefinitionId, ContactManagerTaskData newTask, string userGuid, int modifyingUserId, string modifyingFromIpAddress)
        {
            CM_TaskMessage tm = new CM_TaskMessage();
            tm.CM_TaskDefinitionId = taskDefinitionId;
            tm.message = newTask.message ?? string.Empty;
            tm.messageCreatedate = DateTime.Now;
            tm.messageCreator = userGuid;
            tm.isLast = true;
            _context.Entry(tm).State = EntityState.Added;
            _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);

            if (!string.IsNullOrWhiteSpace(newTask.recipients))
            {
                var recipients = newTask.recipients.Split(',').Distinct().ToList();
                for (int i = 0; i < recipients.Count; i++)
                {
                    if (!string.IsNullOrEmpty(recipients[i]))
                    {
                        CM_TaskMessageRecipient tmr = new CM_TaskMessageRecipient();
                        tmr.CM_TaskMessageId = tm.id;
                        tmr.userid = recipients[i];
                        tmr.seen = false;
                        _context.Entry(tmr).State = EntityState.Added;
                    }
                }
                _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);
            }
        }

        private int AddContactManagerTaskDefinition(ContactManagerTaskData newTask, int practiceId, string userGuid, int modifyingUserId, string modifyingFromIpAddress)
        {
            int taskDefinitionId = -1;
            CM_TaskDefinition ts;
            if (string.IsNullOrEmpty(newTask.taskMessageId))
            {
                DateTime dueDate;
                if (!DateTime.TryParseExact(newTask.dueDate, DATE_FORMAT_DUE, CultureInfo.InvariantCulture, DateTimeStyles.None, out dueDate))
                    dueDate = DateTime.Now;

                ts = new CM_TaskDefinition();
                ts.userId = userGuid;
                ts.dateCreated = DateTime.Now;
                ts.subject = newTask.subject;
                ts.dueDate = dueDate;
                if (!string.IsNullOrEmpty(newTask.office))
                {
                    ts.officeId = Convert.ToInt32(newTask.office);
                }
                ts.practiceId = practiceId;
                ts.taskUrgency = (CMTaskUrgency)(Convert.ToInt32(newTask.urgency));
                if (!string.IsNullOrEmpty(newTask.patientRecordId))
                {
                    ts.PatientRecordId = Convert.ToInt32(newTask.patientRecordId);
                }

                _context.Entry(ts).State = EntityState.Added;
                _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);
                taskDefinitionId = ts.id;
            }
            else
            {
                taskDefinitionId = Convert.ToInt32(newTask.taskId);
                ts = _context.CM_TaskDefinitions.Where(a => a.id == taskDefinitionId && a.practiceId == practiceId).FirstOrDefault();
                if (ts != null)
                {
                    ts.subject = newTask.subject;
                    ts.taskUrgency = (CMTaskUrgency)(Convert.ToInt32(newTask.urgency));
                    _context.Entry(ts).State = EntityState.Modified;
                    _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);
                }
            }

            //update "isLast" field
            var taskMessages = _context.CM_TaskMessages.Where(t => t.CM_TaskDefinitionId == taskDefinitionId).ToList();
            taskMessages.ForEach(t => t.isLast = false);
            _context.SaveChanges(modifyingUserId, modifyingFromIpAddress);

            return taskDefinitionId;
        }

        private int? GetPracticeDoctorId(int practiceId, string applicationUserId)
        {
            return _context.PracticeDoctors.FirstOrDefault(d => d.ApplicationUserId == applicationUserId)?.Id;
        }

        private string GetPracticeDoctorIds(int practiceId, string applicationUserId)
        {
            var assistantsDb = (from pd in _context.PracticeDoctors
                                join ud in _context.UserDoctors on pd.ExternalDoctorId equals ud.ExternalDoctorId
                                where pd.ApplicationUserId == applicationUserId && pd.PracticeId == practiceId
                                select new { ud.ApplicationUserId }).ToList();
            string assistants = string.Empty;
            foreach (var assistant in assistantsDb)
            {
                if (!string.IsNullOrEmpty(assistants))
                {
                    assistants += ",";
                }
                assistants += assistant.ApplicationUserId;
            }
            return assistants;
        }

        private List<ContactManagerTextValueViewModel> GetTaskUrgencies()
        {
            List<ContactManagerTextValueViewModel> urgencies = new List<ContactManagerTextValueViewModel>();
            foreach (CMTaskUrgency urgency in Enum.GetValues(typeof(CMTaskUrgency)))
            {
                urgencies.Add(new ContactManagerTextValueViewModel { text = urgency.GetEnumDescription(), value = ((int)urgency).ToString() });
            }
            return urgencies;
        }

        private List<ContactManagerUser> GetUsers(int practiceId, bool contactManagerRecipientFilter = false)
        {
            var allUserOffices = (from usr in _context.Users
                                  join uof in _context.UserOffices on usr.Id equals uof.ApplicationUserId
                                  where
                                  usr.PracticeID == practiceId && usr.Status == UserStatus.Active
                                  && (!contactManagerRecipientFilter || usr.ContactManagerRecipient)
                                  select new { uof.ApplicationUserId, obj = new ContactManagerUser { id = usr.Id, name = (usr.LastName == null ? string.Empty : usr.LastName.ToUpper() + ", ") + (usr.FirstName ?? string.Empty), officeId = uof.OfficeId.ToString(), userType = (int)usr.CerebrumUserType } }
                       ).ToList()
                ;
            List<ApplicationUser> usersDb = _context.Users.Where(t => t.PracticeID == practiceId && t.Status == UserStatus.Active && (!contactManagerRecipientFilter || t.ContactManagerRecipient)).OrderBy(t => t.FirstName).ThenBy(t => t.LastName).ToList();
            List<ContactManagerUser> users = new List<ContactManagerUser>();

            foreach (var user in usersDb)
            {
                users.Add(new ContactManagerUser { id = user.Id, name = (user.LastName == null ? string.Empty : user.LastName.ToUpper() + ", ") + (user.FirstName ?? string.Empty), officeId = string.Empty, userType = (int)user.CerebrumUserType });
                var officeIds = allUserOffices.Where(t => t.ApplicationUserId == user.Id);
                users.AddRange(officeIds.Select(x => x.obj));
            }

            return users.OrderBy(a => a.name).ToList();
        }

        private async Task<ContactManagerNewTaskAttachmentResponse> GetNewTaskAttachment(string patientRecordId, string taskMessageId)
        {
            ContactManagerNewTaskAttachmentResponse response = new ContactManagerNewTaskAttachmentResponse();
            List<ContactManagerNewTaskAttachment> attachments = new List<ContactManagerNewTaskAttachment>();

            if (!string.IsNullOrEmpty(taskMessageId))
            {
                int nTaskMessageId = int.Parse(taskMessageId);
                var taskReports = await (from td in _context.CM_TaskDefinitions
                                         join tm in _context.CM_TaskMessages on td.id equals tm.CM_TaskDefinitionId
                                         join tr in _context.CM_TaskReports on td.id equals tr.CM_TaskDefinitionId
                                         where tm.id == nTaskMessageId
                                         select new
                                         {
                                             tr.reportId,
                                             tr.reportType
                                         }).ToListAsync();
                if (taskReports.Count > 0)
                {
                    string reportName;
                    string reportType;
                    string reportUrl;
                    foreach (var taskReport in taskReports)
                    {
                        reportType = HL7;
                        reportName = string.Empty;
                        reportUrl = string.Empty;
                        if (taskReport.reportType == ReportType.Fax || taskReport.reportType == ReportType.HRM)
                        {
                            reportType = taskReport.reportType == ReportType.Fax ? FAX : HRM;
                            var fax = await (from r in _context.ReportsReceived
                                             where r.Id == taskReport.reportId
                                             select new { r.fileName, r.url, r.officeId }).FirstOrDefaultAsync();
                            if (fax != null)
                            {
                                reportName = fax.fileName;
                                reportUrl = UtilityHelper.GetDocumentUrl(fax.url, fax.officeId);
                            }
                        }
                        attachments.Add(new ContactManagerNewTaskAttachment { reportId = taskReport.reportId, reportType = reportType, reportName = reportName, reportUrl = reportUrl });
                    }
                }
            }

            if (!string.IsNullOrEmpty(patientRecordId))
            {
                string errorMessage = string.Empty;

                var requisitionList = _requisitionBLL.GetRequisitionList(int.Parse(patientRecordId), 0, out errorMessage);
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    response.errorMessage = errorMessage;
                    return response;
                }

                int requisitionId;
                int requisitionStatusArranged = _requisitionBLL.GetRequisitionStatusByName(STATUSARRANGED)?.id ?? -1;
                int requisitionStatusOrdered = _requisitionBLL.GetRequisitionStatusByName(STATUSORDERED)?.id ?? -1;
                string requisitionName;
                foreach (var requisition in requisitionList)
                {
                    if (requisition.requisitionPatientId == 0)
                        continue;

                    foreach (var testOrdered in requisition.testOrdered)
                    {
                        if (testOrdered.source.ToLower() == TYPEREQUISITION && (testOrdered.status == requisitionStatusArranged || testOrdered.status == requisitionStatusOrdered))
                        {
                            switch (testOrdered.type.ToLower())
                            {
                                case "internal":
                                case "external":
                                    requisitionName = testOrdered.type + ":";
                                    foreach (var requisitionItem in testOrdered.requisitionItem)
                                    {
                                        requisitionName += " " + requisitionItem.testName;
                                    }
                                    requisitionId = testOrdered.id;
                                    attachments.Add(new ContactManagerNewTaskAttachment { reportId = requisitionId, reportType = "requisition", reportName = testOrdered.type, reportUrl = string.Empty });
                                    break;
                                default:
                                    attachments.Add(new ContactManagerNewTaskAttachment { reportId = testOrdered.id, reportType = "requisition", reportName = testOrdered.type, reportUrl = string.Empty });
                                    break;
                            }
                        }
                    }
                }
            }

            response.attachments = attachments;
            return response;
        }

        private async Task<List<TextValueListViewModel>> GetRequistions(int practiceId)
        {
            List<TextValueListViewModel> requisitions = await (from r in _context.RequisitionType
                                                               orderby r.orderNumber
                                                               select new TextValueListViewModel
                                                               {
                                                                   text = r.name,
                                                                   value = r.id.ToString(),
                                                                   templates = (from t in _context.RequisitionTemplate
                                                                                where t.practiceId == practiceId && t.requisitionTypeId == r.id
                                                                                orderby t.name
                                                                                select new TextValueViewModel { text = t.name, value = t.id.ToString() }).ToList()
                                                               }).ToListAsync();
            return requisitions;
        }

        private List<ContactManagerTextValueViewModel> GetUserTypes()
        {
            List<ContactManagerTextValueViewModel> userTypes = new List<ContactManagerTextValueViewModel>();
            foreach (UserTypeEnum userType in Enum.GetValues(typeof(UserTypeEnum)))
            {
                userTypes.Add(new ContactManagerTextValueViewModel { text = userType.GetEnumDescription(), value = ((int)userType).ToString() });
            }
            return userTypes.OrderBy(a => a.text).ToList();
        }

        private async Task<List<ContactManagerTextValueViewModel>> GetPracticeOfficesAsync(int practiceId)
        {
            var offices = (from t in _context.Offices.Where(t => t.PracticeId == practiceId && t.status == 0)
                           orderby t.name
                           select new ContactManagerTextValueViewModel { text = t.name, value = t.Id.ToString() });
            return await offices.ToListAsync();
        }


        private int GetInt(string data, int defaultInt)
        {
            if (string.IsNullOrWhiteSpace(data))
                return defaultInt;

            int result = defaultInt;
            if (int.TryParse(data, out result))
                return result;

            return defaultInt;
        }

        private ReportType ParseReportType(string reportTypeStr)
        {
            var success = ReportType.TryParse(reportTypeStr, true, out ReportType parsedReportType);

            if (!success)
            {
                parsedReportType = ReportType.Fax;
            }
            return parsedReportType;
        }
    }
}
