﻿using AwareMD.Cerebrum.Shared;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL.User;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using Cerebrum.ViewModels.ContactManager;
using Cerebrum.ViewModels.ContactManagerNew;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Cerebrum.ContactManager
{
    public class ContactManagerBLL : IDisposable, ILogGeneric
    {
        #region CONSTANTS
        private const string ORDER_ASC = "asc";
        private const string ORDER_DESC = "desc";
        private const string DATE_FORMAT_DUE = "MM/dd/yyyy";
        private const string DATE_FORMAT_MESSAGE = "MM/dd/yyyy HH:mm:ss";
        private const string FAX = "fax";
        private const string HL7 = "hl7";
        private const string HRM = "hrm";
        private const string EXTERNALDOCUMENTFAX = "fax";
        private const string PERMISSIONVIEWCOMMONMESSAGES = "ViewCommonMessages";
        private const string PERMISSIONADDREQUISITION = "AddRequisition";
        private const string TYPEAPPOINTMENT = "appointment";
        private const string TYPEREQUISITION = "requisition";

        //requisition status
        private const string STATUSORDERED = "ordered";
        private const string STATUSARRANGED = "arranged";

        private const string HttpContext = "MS_HttpContext";
        private const string RemoteEndpointMessage = "System.ServiceModel.Channels.RemoteEndpointMessageProperty";

        private const string DATEFORMAT = "MM/dd/yyyy";
        private const string DATEFORMAT3 = "yyyyMMdd";
        #endregion

        #region Variables

        //private class taskData
        //{
        //    public int id;
        //    public CMTaskStatus taskStatus;
        //    public DateTime? dueDate;
        //    public CMTaskUrgency taskUrgency;
        //    public string subject;
        //    public int messageId;
        //    public string message;
        //    public string userName;
        //    public string patientName;
        //    public string patientRecordId;
        //    public DateTime? markSeenDate = null;
        //}

        private int practiceId = 0;
        private int userId = 0;
        private string applicationUserId = string.Empty;
        private string ipAddress = string.Empty;
        private CerebrumContext context;
        private readonly IContactManagerEmailService _contactManagerEmailService;
        #endregion

        public ContactManagerBLL(int practiceId, int userId, string applicationUserId, string ipAddress)
        {
            context = new CerebrumContext();
            this.practiceId = practiceId;
            this.userId = userId;
            this.applicationUserId = applicationUserId;
            this.ipAddress = ipAddress;
            _contactManagerEmailService = new ContactManagerEmailService(context);
        }

        /// <summary>add new task to database</summary>
        /// <param name="newTask">new task data</param>
        /// <returns>return empty data if there is no error; return error message if there is error</returns>    
        public async Task<string> AddNewTask(ContactManagerTaskData newTask, ViewModels.OfficeEmail.Email officeEmail) // string SMTPTelus, string SMTPpw_Telus, string SMTPun_Telus)
        {
            if (string.IsNullOrEmpty(newTask.recipients))
                return "New message doesn't have recipient! ";

            int taskId = -1;
            List<string> recipients = null;

            //using (TransactionScope scope = new TransactionScope())
            {
                if (string.IsNullOrEmpty(newTask.taskMessageId))
                {
                    DateTime dueDate;
                    if (!DateTime.TryParseExact(newTask.dueDate, DATE_FORMAT_DUE, CultureInfo.InvariantCulture, DateTimeStyles.None, out dueDate))
                        dueDate = DateTime.Now;

                    CM_TaskDefinition ts = new CM_TaskDefinition();
                    ts.userId = applicationUserId;
                    ts.dateCreated = DateTime.Now;
                    ts.subject = newTask.subject;
                    ts.dueDate = dueDate;
                    if (!string.IsNullOrEmpty(newTask.office))
                        ts.officeId = Convert.ToInt32(newTask.office);
                    ts.practiceId = practiceId;
                    ts.taskUrgency = (CMTaskUrgency)(Convert.ToInt32(newTask.urgency));
                    if (!string.IsNullOrEmpty(newTask.patientRecordId))
                    {
                        ts.PatientRecordId = Convert.ToInt32(newTask.patientRecordId);
                    }

                    context.Entry(ts).State = EntityState.Added;
                    context.SaveChanges(userId, ipAddress);
                    taskId = ts.id;
                }
                else
                {
                    int taskMessageId = Convert.ToInt32(newTask.taskMessageId);
                    string message = MarkTaskMessageSeen(applicationUserId, 0, taskMessageId);
                    taskId = Convert.ToInt32(newTask.taskId);
                }

                //update "isLast" field
                var taskMessages = await context.CM_TaskMessages.Where(t => t.CM_TaskDefinitionId == taskId).ToListAsync();
                taskMessages.ForEach(t => t.isLast = false);
                context.SaveChanges(userId, ipAddress);

                if (!string.IsNullOrWhiteSpace(newTask.message))
                {
                    CM_TaskMessage tm = new CM_TaskMessage();
                    tm.CM_TaskDefinitionId = taskId;
                    tm.message = newTask.message ?? string.Empty;
                    tm.messageCreatedate = DateTime.Now;
                    tm.messageCreator = applicationUserId;
                    tm.isLast = true;
                    context.Entry(tm).State = EntityState.Added;
                    context.SaveChanges(userId, ipAddress);

                    recipients = newTask.recipients.Split(',').Distinct().ToList();
                    for (int i = 0; i < recipients.Count; i++)
                    {
                        if (!string.IsNullOrEmpty(recipients[i]))
                        {
                            CM_TaskMessageRecipient tmr = new CM_TaskMessageRecipient();
                            tmr.CM_TaskMessageId = tm.id;
                            tmr.userid = recipients[i];
                            tmr.seen = false;
                            context.Entry(tmr).State = EntityState.Added;
                        }
                    }
                    context.SaveChanges(userId, ipAddress);
                }

                if (!string.IsNullOrEmpty(newTask.reportId) && !string.IsNullOrEmpty(newTask.reportType))
                {
                    CM_TaskReport tr = new CM_TaskReport();
                    tr.CM_TaskDefinitionId = taskId;
                    tr.reportId = Int32.Parse(newTask.reportId);
                    tr.reportType = this.ParseReportType(newTask.reportType);
                    tr.reportTypeSpecified = false;
                    tr.labResultId = newTask.reportType.ToLower() == FAX ? 0 : tr.reportId;
                    tr.externalReportId = newTask.reportType.ToLower() == FAX ? tr.reportId : 0;
                    context.Entry(tr).State = EntityState.Added;
                    context.SaveChanges(userId, ipAddress);
                }

                //scope.Complete();
            }

            if (!newTask.sendEmail)
                return string.Empty;

            try
            {
                string emailTo = "";
                string emailBody = "";
                string subject = newTask.subject;

                var taskMessages = await (from mes in context.CM_TaskMessages
                                          join u in context.Users on mes.messageCreator equals u.Id
                                          where mes.CM_TaskDefinitionId == taskId
                                          orderby mes.messageCreatedate descending
                                          select new
                                          {
                                              mes.message,
                                              mes.messageCreatedate,
                                              creator = (u.FirstName == null ? string.Empty : u.FirstName) + " " + (u.LastName == null ? string.Empty : u.LastName)
                                          }).ToListAsync();
                foreach (var message in taskMessages)
                {
                    if (!string.IsNullOrEmpty(emailBody))
                    {
                        emailBody += Environment.NewLine;
                    }
                    emailBody += message.messageCreatedate.Value.ToString(DATE_FORMAT_MESSAGE, CultureInfo.InvariantCulture) + " " + message.creator + ": " + message.message;
                }

                string result = string.Empty;
                for (int i = 0; i < recipients.Count; i++)
                {
                    if (!string.IsNullOrEmpty(recipients[i]))
                    {
                        string recipient = recipients[i];
                        ApplicationUser user = await context.Users.Where(t => t.Id == recipient).FirstOrDefaultAsync();
                        if (user != null && !string.IsNullOrEmpty(user.Email))
                        {
                            emailTo = user.Email;
                            if (!UtilityHelper.SendContactManagerMail(emailTo, emailBody, subject, officeEmail))
                            {
                                this.LogDebugFormat("Email error: From {0}; To: {1}; Subject: {2}", officeEmail.userName, emailTo, subject);
                                result = "New task / message was added successfully but there was a problem with sending email. Please Contact Administrator! ";
                            }
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message + " ### AddNewTask Send Email";

                this.LogDebugFormat("Exception:{0}", Msg);

                return "There was a problem. Please Contact Administrator! ";
            }
        }

        public async Task<string> GetHrmMessage(string host, ViewModels.Measurements.VMValidateHrmReport vm)
        {
            string message = string.Empty;
            var info = await (from a in context.Appointments
                              join b in context.AppointmentTests on a.Id equals b.AppointmentId
                              join c in context.Tests on b.TestId equals c.Id
                              where b.Id == vm.AppointmentTestId
                              select new { a.appointmentTime, a.OfficeId, c.testFullName }).FirstOrDefaultAsync();
            if (info != null)
            {
                message = $"Appointment time: {String.Format("{0:f}", info.appointmentTime)}{Environment.NewLine}Test: {info.testFullName}{Environment.NewLine}{Environment.NewLine}{vm.ErrorMessage}{Environment.NewLine}{Environment.NewLine}Worksheet: {host}/Measurements/Measurement?AppointmentID={vm.AppointmentId}&TestID={vm.TestId}&AppointmentTestID={vm.AppointmentTestId}&officeId={info.OfficeId}&date={String.Format("{0:d}", info.appointmentTime)}";
            }

            return message;
        }

        public VMContactMessage SendMessage(VMContactRequest request)
        {
            var contactMessage = new VMContactMessage();
            var userBLL = new Cerebrum.BLL.User.UserBLL();

            if (request.PatientId > 0)
            {
                var patientBLL = new Cerebrum.BLL.Patient.PatientBLL(context);
                var patientInfo = patientBLL.GetPatientInfo(request.PatientId); //todo
                contactMessage.PatientId = patientInfo.PatientId;
                contactMessage.PatientFullName = patientInfo.FullName;
            }

            List<VMRecipient> recipients = new List<VMRecipient>();
            var practiceUsers = userBLL.GetPracticeUsers(practiceId); //todo
            foreach (var user in practiceUsers)
            {
                var recipient = new VMRecipient();
                recipient.UserId = user.UserId;
                recipient.Id = user.Id;
                recipient.UserFullName = (user.LastName == null ? string.Empty : user.LastName.ToUpper() + ", ") + (user.FirstName ?? string.Empty);

                if (request.Recipient > 0 && request.Recipient == user.UserId)
                {
                    recipient.IsSelected = true;
                }
                else
                {
                    recipient.IsSelected = false;
                }

                recipients.Add(recipient);
            }
            contactMessage.Recipients = recipients.OrderBy(a => a.UserFullName).ToList();

            return contactMessage;
        }

        public async Task<string> SendMessage(VMContactMessage contactMessage)
        {
            string mssg = string.Empty;

            var users = new List<string>();
            foreach (var item in contactMessage.Recipients)
            {
                if (item.IsSelected) users.Add(item.Id);
            }

            var guid = string.Join(",", users);

            ViewModels.OfficeEmail.Email officeEmail = new ViewModels.OfficeEmail.Email();
            mssg = await AddNewTask(new ContactManagerTaskData()
            {
                patientRecordId = contactMessage.PatientId.ToString(),
                subject = contactMessage.Subject,
                recipients = guid,
                urgency = contactMessage.UrgencyId.ToString(),
                dueDate = contactMessage.DueDate.ToString("MM/dd/yyyy"),
                message = contactMessage.Message,
                office = contactMessage.OfficeId.ToString()

            }, officeEmail);

            return mssg;
        }

        public async Task<ContactManagerNewTaskViewModel> RecallTask(string practiceDoctorId, string patientRecordIds)
        {
            ContactManagerNewTaskViewModel result = new ContactManagerNewTaskViewModel();

            //get list of offices
            List<ContactManagerTextValueViewModel> offices = await (from t in context.Offices.Where(t => t.PracticeId == practiceId)
                                                                    select new ContactManagerTextValueViewModel { text = t.name, value = t.Id.ToString() })
                                                              .OrderBy(t => t.text)
                                                              .ToListAsync();
            result.offices = offices;

            //get list of urgencies
            List<ContactManagerTextValueViewModel> urgencies = new List<ContactManagerTextValueViewModel>();
            foreach (CMTaskUrgency urgency in Enum.GetValues(typeof(CMTaskUrgency)))
            {
                urgencies.Add(new ContactManagerTextValueViewModel { text = urgency.GetEnumDescription(), value = ((int)urgency).ToString() });
            }
            result.urgencies = urgencies;

            //get list of user types
            List<ContactManagerTextValueViewModel> userTypes = new List<ContactManagerTextValueViewModel>();
            foreach (UserTypeEnum userType in Enum.GetValues(typeof(UserTypeEnum)))
            {
                userTypes.Add(new ContactManagerTextValueViewModel { text = userType.GetEnumDescription(), value = ((int)userType).ToString() });
            }
            result.userTypes = userTypes;

            //commented out 2020-12-03
            //get list of users
            //List<Cerebrum.Data.ApplicationUser> usersDb = await context.Users.Where(t => t.PracticeID == practiceId && t.Status == UserStatus.Active).OrderBy(t => t.FirstName).ThenBy(t => t.LastName).ToListAsync();
            //List<ContactManagerUser> users = new List<ContactManagerUser>();
            //foreach (var user in usersDb)
            //{
            //    users.Add(new ContactManagerUser { id = user.Id, name = (user.LastName == null ? string.Empty : user.LastName.ToUpper() + ", ") + (user.FirstName ?? string.Empty), officeId = string.Empty, userType = (int)user.CerebrumUserType });
            //    var officeIds = await context.UserOffices.Where(t => t.ApplicationUserId == user.Id).ToListAsync();
            //    foreach (var office in officeIds)
            //    {
            //        users.Add(new ContactManagerUser { id = user.Id, name = (user.LastName == null ? string.Empty : user.LastName.ToUpper() + ", ") + (user.FirstName ?? string.Empty), officeId = office.OfficeId.ToString(), userType = (int)user.CerebrumUserType });
            //    }
            //}
            List<ContactManagerUser> users = ContactManagerUserBLL.GetList<ContactManagerUser>(context, practiceId);
            result.users = users;
            result.practiceDoctorId = practiceDoctorId;
            result.patientRecordId = patientRecordIds;

            var assistantsDb = await (from pd in context.PracticeDoctors
                                      join ud in context.UserDoctors on pd.ExternalDoctorId equals ud.ExternalDoctorId
                                      where pd.ApplicationUserId == applicationUserId && pd.PracticeId == practiceId
                                      select new { ud.ApplicationUserId }).ToListAsync();
            string assistants = string.Empty;
            foreach (var assistant in assistantsDb)
            {
                if (!string.IsNullOrEmpty(assistants))
                {
                    assistants += ",";
                }
                assistants += assistant.ApplicationUserId;
            }
            result.taskMessage.recipients = assistants;

            return result;
        }

        public async Task SendRecallTaskMessage(RecallTaskMessageRequest request, string SMTPTelus, string SMTPpw_Telus, string SMTPun_Telus)
        {
            DateTime dueDate;
            if (!DateTime.TryParseExact(request.dueDate, DATE_FORMAT_DUE, CultureInfo.InvariantCulture, DateTimeStyles.None, out dueDate))
                dueDate = DateTime.Now;

            var patientRecordIds = request.patientRecordIds.Split(',').Distinct().ToList();
            var recipients = request.recipients.Split(',').Distinct().ToList();

            foreach (var patientRecordId in patientRecordIds)
            {
                CM_TaskDefinition ts = new CM_TaskDefinition();
                ts.userId = applicationUserId;
                ts.dateCreated = DateTime.Now;
                ts.subject = request.subject;
                ts.dueDate = dueDate;
                if (!string.IsNullOrEmpty(request.office))
                    ts.officeId = Convert.ToInt32(request.office);
                ts.practiceId = practiceId;
                ts.taskUrgency = (CMTaskUrgency)(Convert.ToInt32(request.urgency));
                ts.PatientRecordId = Convert.ToInt32(patientRecordId);
                context.CM_TaskDefinitions.Add(ts);
                //context.Entry(ts).State = EntityState.Added;

                CM_TaskMessage tm = new CM_TaskMessage();
                tm.CM_TaskDefinitionId = ts.id;
                tm.message = request.message ?? string.Empty;
                tm.messageCreatedate = DateTime.Now;
                tm.messageCreator = applicationUserId;
                tm.isLast = false;
                context.CM_TaskMessages.Add(tm);
                //context.Entry(tm).State = EntityState.Added;

                foreach (var recipient in recipients)
                {
                    if (!string.IsNullOrEmpty(recipient))
                    {
                        CM_TaskMessageRecipient tmr = new CM_TaskMessageRecipient();
                        tmr.CM_TaskMessageId = tm.id;
                        tmr.userid = recipient;
                        tmr.seen = false;
                        //context.Entry(tmr).State = EntityState.Added;
                        context.CM_TaskMessageRecipients.Add(tmr);
                    }
                }

                context.SaveChanges(userId, ipAddress);
            }

            if (!request.sendEmail)
                return;

            string emailFrom = string.Empty;
            string emailBody = request.message;
            string subject = request.subject;
            string creator = string.Empty;

            ApplicationUser user = await context.Users.Where(t => t.Id == applicationUserId).FirstOrDefaultAsync();
            if (user == null || string.IsNullOrEmpty(user.Email))
                emailFrom = ConfigurationManager.AppSettings["SMTP_From"];
            else
            {
                emailFrom = user.Email;
                creator = (user.LastName == null ? string.Empty : user.LastName) + ", " + (user.FirstName == null ? string.Empty : user.FirstName);
            }

            emailBody = DateTime.Now.ToString(DATE_FORMAT_MESSAGE, CultureInfo.InvariantCulture) + " " + creator + ": " + Environment.NewLine + request.message;
            var patients = await context.Demographics.Where(a => patientRecordIds.Contains(a.PatientRecordId.ToString())).Select(b => new { name = (b.lastName == null ? string.Empty : b.lastName) + ", " + (b.firstName == null ? string.Empty : b.firstName) }).ToListAsync();
            var emailTos = await context.Users.Where(a => recipients.Contains(a.Id)).Select(b => new { b.Email }).ToListAsync();
            ViewModels.OfficeEmail.Email officeEmail = _contactManagerEmailService.GetOfficeEmail(practiceId, 0);

            foreach (var patient in patients)
            {
                string emailSubject = subject;
                if (patients.Count() > 1)
                    emailSubject = string.Format($"Patient ({patient.name}): {subject}");

                foreach (var emailTo in emailTos)
                {
                    if (!string.IsNullOrEmpty(emailTo.Email))
                    {
                        try
                        {
                            //if (!UtilityHelper.SendMail(emailFrom, emailTo.Email, emailBody, emailSubject, SMTPTelus, SMTPpw_Telus, SMTPun_Telus))
                            if (!UtilityHelper.SendContactManagerMail(emailTo.Email, emailBody, emailSubject, officeEmail))
                                this.LogDebugFormat("Email error: Patient {0}; From {1}; To: {2}", patient.name, officeEmail.emailFrom, emailTo.Email);
                        }
                        catch
                        {
                            this.LogDebugFormat("Email error: Patient {0}; From {1}; To: {2}", patient.name, officeEmail.emailFrom, emailTo.Email);
                            //LogException(new StackTrace().GetFrame(0).GetMethod().Name, e, "SendRecallTaskMessage");
                        }
                    }
                }
            }
        }

        public ContactManagerTaskListViewModel LoadTasks(ContactManagerTaskListRequest taskListRequest, List<string> permissions)
        {
            ContactManagerTaskListViewModel result = new ContactManagerTaskListViewModel();

            //get list of statuses and urgencies
            if (taskListRequest.readRequest == 2)
            {
                List<ContactManagerTextValueViewModel> statuses = new List<ContactManagerTextValueViewModel>();
                foreach (CMTaskStatus status in Enum.GetValues(typeof(CMTaskStatus)))
                {
                    statuses.Add(new ContactManagerTextValueViewModel { text = status.ToString(), value = ((int)status).ToString() });
                }
                result.statuses = statuses;

                List<ContactManagerTextValueViewModel> urgencies = new List<ContactManagerTextValueViewModel>();
                foreach (CMTaskUrgency urgency in Enum.GetValues(typeof(CMTaskUrgency)))
                {
                    urgencies.Add(new ContactManagerTextValueViewModel { text = urgency.GetEnumDescription(), value = ((int)urgency).ToString() });
                }
                result.urgencies = urgencies;
            }

            string taskSource = taskListRequest.taskSource.Trim().ToLower();
            if (string.IsNullOrWhiteSpace(taskSource))
                taskSource = "inbox";
            if (taskListRequest.patientRecordId > 0)
                taskSource = "patient";

            if (taskSource == "commonfolder" && !permissions.Contains(PERMISSIONVIEWCOMMONMESSAGES))   //only admin can view commond folder
            {
                result.errorMessage = "You don't have permission to view commond folder. Please Contact Administrator! ";
                return result;
            }

            string filterStatus = GetFilter("status", taskListRequest.filters);
            if (taskSource == "inbox")
            {
                result.statuses = result.statuses.Where(a => a.text == "Open").ToList();
                filterStatus = ((int)CMTaskStatus.Open).ToString();
            }

            List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("taskSource", taskSource),
                    new SqlParameter("userId", applicationUserId),
                    new SqlParameter("practiceId", practiceId),
                    new SqlParameter("patientRecordId", taskListRequest.patientRecordId),
                    new SqlParameter("rowStart", taskListRequest.rowStart),
                    new SqlParameter("rowCount", taskListRequest.rowCount),
                    new SqlParameter("readRequest", taskListRequest.readRequest),
                    new SqlParameter("sortByColumn", taskListRequest.sortByColumn),
                    new SqlParameter("sortByOrder", taskListRequest.sortByOrder),
                    new SqlParameter("filterStatus", filterStatus),
                    new SqlParameter("filterUrgency", GetFilter("urgency", taskListRequest.filters)),
                    new SqlParameter("filterDue", GetDateFilter("due", taskListRequest.filters)),
                    new SqlParameter("filterSubject", GetFilter("subject", taskListRequest.filters)),
                    new SqlParameter("filterMessage", GetFilter("message", taskListRequest.filters)),
                    new SqlParameter("filterCreator", GetFilter("creator", taskListRequest.filters)),
                    new SqlParameter("filterPatient", GetFilter("patient", taskListRequest.filters)),
                    new SqlParameter("filterSeenat", GetDateFilter("seenat", taskListRequest.filters)),
                    new SqlParameter("filterMessageDate", GetDateFilter("messagedate", taskListRequest.filters))
                };
            SqlParameter totalRow = new SqlParameter("totalRow", System.Data.SqlDbType.Int);
            totalRow.Direction = System.Data.ParameterDirection.ReturnValue;
            parameters.Add(totalRow);
            var tasks = context.GetData<ContactManagerTaskViewModel>("dbo.GetContactManagerTaskList", parameters).ToList();
            if (taskListRequest.readRequest == 1 || taskListRequest.readRequest == 2)
                result.totalRow = (int)totalRow.Value;

            result.tasks = tasks;

            string patientName = string.Empty;
            if (taskListRequest.patientRecordId > 0)
            {
                var patient = context.Demographics.Where(a => a.PatientRecordId == taskListRequest.patientRecordId).FirstOrDefault();
                if (patient != null)
                    patientName = (patient.lastName ?? string.Empty) + (string.IsNullOrWhiteSpace(patient.firstName) ? string.Empty : ", " + patient.firstName);
            }
            result.patientName = patientName;

            return result;
        }

        private string MarkTaskMessageSeen(string userGuid, int taskDefinitionId, int taskMessageId)
        {
            List<int> taskMessageIds = new List<int>();
            if (taskDefinitionId == 0)
            {
                var taskMessage = context.CM_TaskMessages.Where(a => a.id == taskMessageId).FirstOrDefault();
                if (taskMessage == null)
                {
                    return "Cannot find the message";
                }
                taskMessageIds = context.CM_TaskMessages.Where(a => a.CM_TaskDefinitionId == taskMessage.CM_TaskDefinitionId && a.id <= taskMessage.id).Select(b => b.id).ToList();
            }
            else
            {
                taskMessageIds = context.CM_TaskMessages.Where(a => a.CM_TaskDefinitionId == taskDefinitionId).Select(b => b.id).ToList();
            }

            var recipients = context.CM_TaskMessageRecipients.Where(a => a.userid == userGuid && a.seen == false && taskMessageIds.Contains(a.CM_TaskMessageId)).ToList();
            recipients.ForEach(a =>
            {
                a.seen = true;
                a.seenAt = DateTime.Now;
            });

            context.SaveChanges(userId, ipAddress);

            return string.Empty;
        }

        private string GetFilter(string filterName, List<TextValueViewModel> filters)
        {
            if (filters == null)
                return string.Empty;

            var filter = filters.Where(a => a.value.ToLower() == filterName.ToLower()).FirstOrDefault();
            if (filter == null)
                return string.Empty;

            return filter.text;
        }

        private string GetDateFilter(string filterName, List<TextValueViewModel> filters)
        {
            string filter = GetFilter(filterName, filters);
            if (string.IsNullOrWhiteSpace(filter))
                return string.Empty;

            return DateTime.ParseExact(filter, DATEFORMAT, CultureInfo.InvariantCulture).ToString(DATEFORMAT3, CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Parse a report type string to a ReportType enum
        /// </summary>
        /// <param name="reportTypeStr"> The report type string to parse. </param>
        /// <returns> the parsed report type string or FAX if there is no match to maintain legacy functionality </returns>
        private ReportType ParseReportType(string reportTypeStr)
        {
            var success = ReportType.TryParse(reportTypeStr, true, out ReportType parsedReportType);

            if (!success)
            {
                parsedReportType = ReportType.Fax;
            }
            return parsedReportType;
        }

        public void Dispose()
        {
            context.Dispose();
        }
    }
}
