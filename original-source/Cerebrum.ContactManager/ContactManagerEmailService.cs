﻿using Cerebrum.Data;
using Cerebrum.ViewModels.OfficeEmail;
using System.Configuration;
using System.Linq;

namespace Cerebrum.ContactManager
{
    public interface IContactManagerEmailService
    {
        Email GetOfficeEmail(int practiceId, int officeId);
    }
    public class ContactManagerEmailService : IContactManagerEmailService
    {
        private readonly CerebrumContext _context;
        public ContactManagerEmailService(CerebrumContext context)
        {
            _context = context;
        }
        public Email GetOfficeEmail(int practiceId, int officeId)
        {
            ViewModels.OfficeEmail.Email officeEmail = new ViewModels.OfficeEmail.Email();

            ////use for pace temporarily
            if (practiceId == 1)
            {
                int port = 1025;
                string emailPort = ConfigurationManager.AppSettings["SMTP_Telus_Port_PACE"];
                if (!string.IsNullOrWhiteSpace(emailPort))
                {
                    if (!int.TryParse(emailPort, out port))
                        port = 1025;
                }

                bool sslEnable = false;
                string sslEnableConfig = ConfigurationManager.AppSettings["SMTPssl_Telus_PACE"];
                if (!string.IsNullOrWhiteSpace(sslEnableConfig) && sslEnableConfig.ToLower().Contains("on"))
                    sslEnable = true;

                officeEmail.serverUrl = ConfigurationManager.AppSettings["SMTPTelus_PACE"];
                officeEmail.serverPort = port.ToString();
                officeEmail.userName = ConfigurationManager.AppSettings["SMTPun_Telus_PACE"];
                officeEmail.password = ConfigurationManager.AppSettings["SMTPpw_Telus_PACE"];
                officeEmail.emailFrom = ConfigurationManager.AppSettings["SMTP_From_PACE"];
                officeEmail.enableSsl = sslEnable;
                return officeEmail;
            }

            bool foundOfficeEmail = false;
            if (officeId != 0)
            {
                var email = _context.OfficeEmails.Where(a => a.OfficeId == officeId).FirstOrDefault();
                if (email != null)
                {
                    officeEmail.serverUrl = email.serverUrl;
                    officeEmail.serverPort = email.serverPort;
                    officeEmail.userName = email.userName;
                    officeEmail.password = email.password;
                    officeEmail.enableSsl = email.enableSsl;

                    foundOfficeEmail = true;
                }
            }

            if (!foundOfficeEmail)
            {
                int port = 2525;
                string emailPort = ConfigurationManager.AppSettings["SMTP_Telus_Port"];
                if (!string.IsNullOrWhiteSpace(emailPort))
                {
                    if (!int.TryParse(emailPort, out port))
                        port = 2525;
                }

                bool sslEnable = false;
                string sslEnableConfig = ConfigurationManager.AppSettings["SMTPssl_Telus"];
                if (!string.IsNullOrWhiteSpace(sslEnableConfig) && sslEnableConfig.ToLower().Contains("on"))
                    sslEnable = true;

                officeEmail.serverUrl = ConfigurationManager.AppSettings["SMTPTelus"];
                officeEmail.serverPort = port.ToString();
                officeEmail.userName = ConfigurationManager.AppSettings["SMTPun_Telus"];
                officeEmail.password = ConfigurationManager.AppSettings["SMTPpw_Telus"];
                officeEmail.enableSsl = sslEnable;
            }

            officeEmail.emailFrom = officeEmail.userName;

            return officeEmail;
        }
    }
}
