﻿using Cerebrum.IdentityModelBLL.Dto;
using IdentityModel.Client;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Cerebrum.IdentityModelBLL.Services
{
    public class ClientCredentialService
    {
        private readonly string identityServerTokenEndPoint = ConfigurationManager.AppSettings["IdentityServerTokenEndPoint"];

        public async Task<string> TokenRequestAsync(HttpClient client, ClientTokenRequest clientTokenRequest)
        {
            var apiClientCredentials = new ClientCredentialsTokenRequest
            {
                Address = $"{clientTokenRequest.BaseUrl}{identityServerTokenEndPoint}",
                ClientId = clientTokenRequest.ClientId,
                ClientSecret = clientTokenRequest.ClientSecret,
                Scope = clientTokenRequest.Scope
            };
            var tokenResponse = await client.RequestClientCredentialsTokenAsync(apiClientCredentials);
            
            return tokenResponse.AccessToken;
        }
        public string TokenRequest(HttpClient client, ClientTokenRequest clientTokenRequest)
        {
            var apiClientCredentials = new ClientCredentialsTokenRequest
            {
                Address = $"{clientTokenRequest.BaseUrl}{identityServerTokenEndPoint}",
                ClientId = clientTokenRequest.ClientId,
                ClientSecret = clientTokenRequest.ClientSecret,
                Scope = clientTokenRequest.Scope
            };
            var tokenResponse = client.RequestClientCredentialsTokenAsync(apiClientCredentials).Result;

            return tokenResponse.AccessToken;
        }
    }
}
